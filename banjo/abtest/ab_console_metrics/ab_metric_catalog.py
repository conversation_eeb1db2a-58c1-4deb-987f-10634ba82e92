from __future__ import division

import copy
import json
import logging
import os
import os.path
import pandas as pd
import re
import six
import warnings

from collections import defaultdict, OrderedDict
from datetime import timedelta
from functools import partial
from typing import List, Optional


from banjo.abtest.ab_console_metrics.core_metrics import (
    CORE_METRICS_GROUP_IDS, CORE_METRICS_COMPASS_GROUP_IDS
)
from banjo.abtest.metric import (
    get_ab_quantile_metric,
    abtest_metrics_sql_helper,
    define_metric_list,
    BaseMetricTable,
    MetricType,
    MetricDirection,
    MetricTable
)
from banjo.abtest.metric_constants import (
    QUEST_METRIC_SOURCE, CONSOLE_METRIC_SOURCE
)
from banjo.abtest.mapping_table_config import HOURLY_USERMAP_ROLLOUT_DATE
from banjo.utils import gbq


DEFAULT_BQ_BILLING_PROJECT = 'sc-bq-gcs-billingonly'  # the project in which all snap employees can submit queries

METRIC_JSON_FILENAME = 'metrics.json'
QUANTILE_METRIC_JSON_FILENAME = 'quantile_metrics.json'

logger = logging.getLogger(__name__)


class ABMetricConfig(object):
    def __init__(
            self, user_col, bin_cols, cont_cols, ratio_cols, quantile_cols, name, section, description,
            metric_desired_directions=None):
        """
        name: quest table_name
        section: metric_group
        """
        self.user_col = user_col if user_col else {}
        self.bin_cols = bin_cols if bin_cols else {}
        self.cont_cols = cont_cols if cont_cols else {}
        self.ratio_cols = ratio_cols if ratio_cols else {}
        self.quantile_cols = quantile_cols if quantile_cols else {}
        self.name = name
        self.section = section
        self.description = description
        self.metric_desired_directions = metric_desired_directions if metric_desired_directions else {}

    def __repr__(self):
        return ("ABMetricConfig("
                "user_col={user_col},"
                "bin_cols={bin_cols},"
                "cont_cols={cont_cols},"
                "ratio_cols={ratio_cols},"
                "quantile_cols={quantile_cols},"
                "name='{name}',"
                "section='{section}',"
                "description='{description}')"
                ).format(**self.to_dict())

    def to_dict(self):
        return dict(
            user_col=self.user_col,
            bin_cols=self.bin_cols,
            cont_cols=self.cont_cols,
            ratio_cols=self.ratio_cols,
            quantile_cols=self.quantile_cols,
            name=self.name,
            section=self.section,
            description=self.description,
            metric_desired_directions=self.metric_desired_directions,
        )

    def to_metric_table(self,
                        hourly_granularity: bool = False,
                        include_only_these_columns: Optional[List[str]] = None
                        ) -> MetricTable:
        def filter_columns(cols: dict) -> dict:
            if not include_only_these_columns:
                return cols
            return {col: definition for col, definition in cols.items()
                    if col in include_only_these_columns}

        metric_table = self._get_abtest_console_metric_table(
            metric_table_name=self.name,
            user_col=self.user_col,
            cont_cols=filter_columns(self.cont_cols),
            bin_cols=filter_columns(self.bin_cols),
            ratio_cols=filter_columns(self.ratio_cols),
            quantile_cols=filter_columns(self.quantile_cols),
            metric_desired_directions=self.metric_desired_directions,
            hourly_granularity=hourly_granularity,
        )
        return metric_table

    @property
    def column_dist_mapping(self):
        mapping = {}
        for col in self.bin_cols:
            mapping[col] = "bin"
        for col in self.cont_cols:
            mapping[col] = "cont"
        for col in self.ratio_cols:
            mapping[col] = "ratio"
        for col in self.quantile_cols:
            mapping[col] = "quantile"
        return mapping

    def _get_abtest_console_metric_table(self,
                                         metric_table_name,
                                         user_col='ghost_user_id',
                                         cont_cols=None,
                                         bin_cols=None,
                                         ratio_cols=None,
                                         quantile_cols=None,
                                         metric_desired_directions=None,
                                         hourly_granularity=False):

        col_names = []
        col_defs = []
        ratio_defs = []

        for cols in (cont_cols, bin_cols, quantile_cols):
            col_defs.extend([v + ' AS ' + k for k, v in six.iteritems(cols)])
            col_names.append(list(cols.keys()))

        for k, v in six.iteritems(ratio_cols):
            numerator, denominator = v.split('/')
            ratio_defs.append({
                'col': k,
                'numerator': numerator,
                'denominator': denominator
            })

        if hourly_granularity:
            sql_callable = partial(
                abtest_metrics_sql_helper,
                "sc-portal.quest.{metric_table_name}_".format(
                    metric_table_name=metric_table_name
                ),
                columns=(
                    [user_col + ' AS ghost_user_id'] +
                    col_defs +
                    ['TIMESTAMP_MILLIS(event_date_hour_last_millis) AS metric_ts']
                ),
                select_star=True,
                group_by=None if quantile_cols else "ghost_user_id, metric_ts",
                bq_dialect='standard',
                dates=None
            )
        else:
            sql_callable = partial(
                abtest_metrics_sql_helper,
                "sc-portal.quest.{metric_table_name}_".format(
                    metric_table_name=metric_table_name
                ),
                columns=([user_col + ' AS ghost_user_id'] + col_defs),
                select_star=True,
                group_by=None if quantile_cols else "ghost_user_id",
                bq_dialect='standard',
                dates=None
            )

        metrics = define_metric_list(
            cont_cols=col_names[0],
            bin_cols=col_names[1],
            ratio_cols=ratio_defs,
            quantile_cols=quantile_cols,
            metric_desired_directions=metric_desired_directions,
        )
        return MetricTable(metrics=metrics, name=metric_table_name, quantile_metrics=bool(quantile_cols),
                           bq_dialect='standard', hourly_granularity=hourly_granularity,
                           sql_callable=sql_callable)


# TODO(dmao): Remove ABMetricCatalog, class is deprecated
class ABMetricCatalog(object):
    """Metrics config for legacy A/B console metrics in sc-portal:abtest_metric_extraction_v2
    Usage:
        ab_metric_catalog = (
            ABMetricCatalog('20170101', '20170116').get_catalog()
        )
        # Alternatively
        catalog = ABMetricCatalog('20170101', '20170116')
        catalog.search_metric_tables('user', exact_match=False)
        catalog.search_metric_tables('user_story_snap_post',
                                     exact_match=True)
    """
    def __init__(self, start_date, end_date):
        warnings.warn(DeprecationWarning("The old YaML based A/B metrics have been deprecated."))
        self.metrics = self.from_json()
        self.start_date = start_date
        self.end_date = end_date

    @staticmethod
    def from_json(file_path=None):
        if file_path is None:
            cur_path = os.path.dirname(os.path.abspath(__file__))
            file_path = os.path.join(cur_path, METRIC_JSON_FILENAME)
        with open(file_path) as f:
            metrics = json.load(f)

        return [ABMetricConfig(**metric) for metric in metrics]

    def get_catalog(self):
        def catalog(): return None
        for metric in self.metrics:
            metric_table = metric.to_metric_table(
            )
            setattr(catalog, metric.name.lower(), metric_table)

        return catalog

    def get_catalog_dict(self):
        catalog = {}
        for metric in self.metrics:
            metric_table = metric.to_metric_table(
            )
            catalog[metric.name] = metric_table
        return catalog

    def search_metric_tables(self, term, exact_match=True):
        metric_tables = []
        for metric in self.metrics:
            term = term.lower()
            metric_name = metric.name.lower()
            if ((not exact_match and term in metric_name)
                    or term == metric_name):

                metric_table = metric.to_metric_table(
                )
                metric_tables.append(metric_table)

        return metric_tables


# TODO(dmao): Remove ABQuantileMetricCatalog, class is deprecated
class ABQuantileMetricCatalog(object):
    """Quantile metrics config using sc-analytics:report_quest_performance

    Many of the metrics are superseded by blizzard event based metrics.
    """
    def __init__(self, start_date, end_date):
        warnings.warn(DeprecationWarning("The old YaML based A/B metrics have been deprecated."))
        self.metrics = self.from_json()
        self.start_date = start_date
        self.end_date = end_date

    @staticmethod
    def from_json(file_path=None):
        if file_path is None:
            cur_path = os.path.dirname(os.path.abspath(__file__))
            file_path = os.path.join(cur_path, QUANTILE_METRIC_JSON_FILENAME)
        with open(file_path) as f:
            metrics = json.load(f)

        return metrics

    def get_catalog(self):
        def catalog(): return None
        for metric in self.metrics:
            metric_table = get_ab_quantile_metric(
                metric, self.start_date, self.end_date
            )
            setattr(catalog, metric.get('quantile_col').lower(), metric_table)

        return catalog


class ABQuestMetricCatalog(object):
    """Metrics config for Quest V2 based A/B console metrics in sc-portal:quest
    """
    quest_project = 'sc-portal'
    quest_dataset = 'quest'
    quest_table = 'metadata'  # the table storing metric metadata

    # These are exceptional tables without hourly timestamp.
    # Todo: remove this list and the logic at the end of 2022
    quest_daily_aggregation_tables = [
        "ad_ad_request_user",
        "friending_friends_attributed_active_days",
        "notif_email_campaign_user",
        "notif_normal_app_open",
        "notif_notif_always_on_delivery",
        "notif_notif_campaign_attributed_install",
        "notif_notif_sms_campaign_user",
        "notif_notif_tentpole",
        "search_search_posttype_session_final",
        "snap_memories_backup_14_day_incomplete",
        "snap_memories_backup_2_day_incomplete",
        "snap_memories_backup_30_day_incomplete",
        "snap_memories_backup_3_day_incomplete",
        "snap_memories_backup_7_day_incomplete",
        "snap_memories_backup_90_day_incomplete",
        "snap_memories_backup_same_day_incomplete",
        "snap_memories_capture_session_save_status",
        "user_dnu_contact_sync_user_phone_verified",
        "user_customer_report_user",
        "user_user_app_open_with_bitmoji",
        "user_user_contact_sync_user_phone_verified"
    ]    
    
    def __init__(self,
                 start_date,
                 end_date,
                 aa_start_date=None,
                 aa_end_date=None,
                 cap_metrics=True,
                 hourly_granularity=True,
                 bq_project=None,
                 bq_priority="BATCH"
                 ):
        self.bq_project = bq_project or DEFAULT_BQ_BILLING_PROJECT
        self.bq_priority = bq_priority
        self.start_date = start_date
        self.end_date = end_date
        self.aa_start_date = aa_start_date
        self.aa_end_date = aa_end_date
        self.metric_configs: List[ABMetricConfig] = []
        self.metric_tables = {}
        self.metadata = None  # Quest metrics metadata
        self.cap_metrics = cap_metrics
        self.hourly_granularity = hourly_granularity
        self.metric_name_mapping = {}
        self.daily_winsorization_values = defaultdict(dict)
        self._quest_daily_aggregation_tables = []  # todo(xhe): turn this into a property
        self.initialize_metrics_from_metadata()

    def initialize_metrics_from_metadata(self):
        if not self.end_date >= self.start_date:
            raise ValueError("The end_date must not be earlier than the start_date.")

        metrics_identifies = ["major_metric", "minor_metric"]  # these together uniquely identifies a metric

        available_metrics = self.bq_read_available_metrics(self.start_date, self.end_date)

        def _populate_winsorization_limit(df_metrics):
            for _, row in df_metrics.iterrows():
                if row.metric_type in [MetricType.METRIC_TYPE_SUM, MetricType.METRIC_TYPE_ACTIVE_DAY]:
                    for idx, ds in enumerate(row.ds):
                        self.daily_winsorization_values[
                            row.metric_name
                        ][ds] = float(row.cap_values_above[idx])

        _populate_winsorization_limit(available_metrics)

        if self.aa_start_date and self.aa_end_date:
            aa_available_metrics = self.bq_read_available_metrics(self.aa_start_date, self.aa_end_date)
            available_metrics = available_metrics.merge(
                aa_available_metrics.loc[:, metrics_identifies],
                on=metrics_identifies,
                how="inner",
            )
            _populate_winsorization_limit(aa_available_metrics)

        last_day_metadata = self.bq_read_metadata(self.end_date)

        self.metadata = last_day_metadata.merge(
            available_metrics.loc[:, metrics_identifies],
            on=metrics_identifies,
            how="inner",
        )

        self.metadata["major_minor"] = self.metadata["major_metric"] + "____" + self.metadata["minor_metric"]
        self.metadata["major_minor"] = self.metadata["major_minor"].str.replace("_[Pp][59]0", "", regex=True)
        self.metric_name_mapping = self.get_metadata_mapping(
            from_column="metric_name",
            to_column="dash_name",
            sort_key_by=lambda x: x,
        )

        # Todo: refactor to MetricTable directly. Remove ABMetricConfig
        self.metric_configs = self._transform_quest_metadata_to_metric_config(bq_dialect='standard')

        self._quest_daily_aggregation_tables = self._get_quest_daily_aggregation_tables()
        for metric_config in self.metric_configs:
            self.metric_tables[metric_config.name] = self.convert_ab_metric_config_to_metric_table(metric_config)

    def convert_ab_metric_config_to_metric_table(
            self,
            metric_config: ABMetricConfig,
            include_only_these_columns: Optional[List[str]] = None) -> BaseMetricTable:
        metric_table = metric_config.to_metric_table(
            hourly_granularity=self.hourly_granularity if (
                    metric_config.name not in self._quest_daily_aggregation_tables) else False,
            include_only_these_columns=include_only_these_columns,
        )
        for m in metric_table.metrics:
            if m.col in self.metric_name_mapping:
                m.name = self.metric_name_mapping[m.col]
            # add winsorization_limits
            m.winsorization_limits = self.daily_winsorization_values.get(m.col, None)
        return metric_table

    def get_catalog(self):
        def catalog(): return None
        for name, metric_table in self.metric_tables.items():
            setattr(catalog, name.lower(), metric_table)

        return catalog

    def get_catalog_dict(self):
        return self.metric_tables

    def get_filtered_metric_tables(
            self,
            metric_groups=None,
            metric_tables=None,
            metric_ab_majors=None,
            metric_ab_major_minors=None,
            dash_group_ids=None,
            compass_group_ids=None,
            include_core_metrics=False
    ):
        """Get a list of banjo.abtest.MetricTable including the metric_groups and metric_tables

        Most of the filtering conditions map to fields in sc-portal.quest.metadata_<date> tables.

        Parameters
        ----------
        metric_groups : `list` or `set` of str
            `list` or `set` of Quest metric groups (nb: not A/B dashboard metric groups) to include
            maps to the metadata bigquery table's metric_group field
            Examples: Story Our, Send Message, Snap
        metric_tables : `list` or `set` of str
            `list` or `set` of Quest Section + Quest jobs to include (these are also the metrics' bigquery table names)
            maps to <quest_section>_<quest_job> in the metadata bigquery table
            Examples: chat_chat_send_active_day_user, chat_chat_view_user
        metric_ab_majors : `list` or `set` of str
            `list` or `set` of Quest AB MAJOR to include (https://ab.sc-corp.net/v2/metrics)
        metric_ab_major_minors : `list` or `set` of str
            `list` or `set` of Quest AB MAJOR and AB MINOR to include (https://ab.sc-corp.net/v2/metrics)
            in the format of ["{ab_major}____{ab_minor}", ...]
        dash_group_ids: `list` or `set` of str
            Deprecated in favor of compass_group_ids. The sequence IDs of the A/B dashboard groups.
        compass_group_ids: `list` or `set` of str
            The compass group ids
            maps to the metadata bigquery table's compass_group_ids field
            Examples: ab/crash, ab/friend_feed_performance
        include_core_metrics : bool
            Whether to include all core engagement and performance metrics, defined in CORE_METRICS_COMPASS_GROUP_IDS

        Returns
        -------
        filtered_eligible_metric_tables : list of banjo.abtest.metric.MetricTable
        """

        metric_groups = metric_groups or set()
        metric_tables = metric_tables or set()
        metric_ab_majors = metric_ab_majors or set()
        metric_ab_major_minors = metric_ab_major_minors or set()
        dash_group_ids = dash_group_ids or set()
        compass_group_ids = compass_group_ids or set()
        metric_to_dash_group_id = self.get_metadata_mapping(from_column="metric_name", to_column="dash_group_id")
        metric_to_metric_group = self.get_metadata_mapping(from_column="metric_name", to_column="metric_group")
        metric_to_ab_major = self.get_metadata_mapping(from_column="metric_name", to_column="major_metric")
        metric_to_ab_major_minor = self.get_metadata_mapping(from_column="metric_name", to_column="major_minor")
        metric_to_compass_group_id = self.get_metric_to_compass_group_ids_mapping()

        def _is_core(metric_name):
            if metric_to_dash_group_id or metric_to_compass_group_id:
                return metric_to_dash_group_id.get(metric_name) in CORE_METRICS_GROUP_IDS or len(
                    metric_to_compass_group_id.get(metric_name).intersection(set(CORE_METRICS_COMPASS_GROUP_IDS))) > 0
            else:
                return False

        eligible_metric_tables = []

        for metric_config in self.metric_configs:  # metric_config: ABMetricConfig
            eligible_metrics = []
            for metric_name, metric_dist in metric_config.column_dist_mapping.items():
                if (metric_to_metric_group.get(metric_name) in metric_groups
                        or metric_config.name in metric_tables
                        or metric_to_ab_major.get(metric_name) in metric_ab_majors
                        or metric_to_ab_major_minor.get(metric_name) in metric_ab_major_minors
                        or metric_to_dash_group_id.get(metric_name) in dash_group_ids
                        or len(
                            metric_to_compass_group_id.get(metric_name).intersection(set(compass_group_ids))) > 0
                        or (include_core_metrics and _is_core(metric_name))):
                    eligible_metrics.append(metric_name)
                    # add numerator and denominator of ratio
                    if metric_dist == "ratio":
                        # we store "numerator/denominator" as the value of the ratio_cols dictionary
                        # we need to make sure we keep select both the numerator and the denominator
                        # in addition to the name of the ratio metric itself
                        eligible_metrics.extend(metric_config.ratio_cols[metric_name].split("/"))

            if eligible_metrics:  # some metrics in this metric_config meets one of the filtering condition
                metric_table = self.convert_ab_metric_config_to_metric_table(
                    metric_config, include_only_these_columns=eligible_metrics
                )
                eligible_metric_tables.append(metric_table)

        # use metric_table_name to check the quest table existence.
        # This check is for metrics deprecated in quest, but still exists in metadata
        filtered_eligible_metric_tables = []
        for eligible_metric_table in eligible_metric_tables:
            # todo: this may be slower than reading the dataset metadata in a single query for all tables
            # SELECT
            #   table_name_prefix,
            #   COUNT(*) AS available_days
            # FROM (
            #   SELECT
            #     SUBSTR(table_name, 0, CHAR_LENGTH(table_name) - 9) AS table_name_prefix,
            #     SUBSTR(table_name, -8) AS ts
            #   FROM
            #     `sc-portal.quest.INFORMATION_SCHEMA.TABLE_OPTIONS`
            #   WHERE
            #     SUBSTR(table_name, -8) BETWEEN "20231025" AND "20231101"
            # )
            # GROUP BY 1
            # ORDER BY 1
            metric_table_name = eligible_metric_table.name
            table_exists = gbq.table_exists(
                project_id=ABQuestMetricCatalog.quest_project,
                dataset=ABQuestMetricCatalog.quest_dataset,
                table='{}_{}'.format(metric_table_name, self.start_date)
            ) and gbq.table_exists(
                project_id=ABQuestMetricCatalog.quest_project,
                dataset=ABQuestMetricCatalog.quest_dataset,
                table='{}_{}'.format(metric_table_name, self.end_date)
            )
            if not table_exists:
                logger.error(
                    f"Missing Quest table for {metric_table_name}.\n"
                    "This is either due to that the metrics have been migrated to a different table during the "
                    "analysis period, or that the metrics have been removed.\n"
                    "Please ask in the #phx channel if you have questions. Thanks!"
                )
                continue
            else:
                filtered_eligible_metric_tables.append(eligible_metric_table)

        return filtered_eligible_metric_tables

    def get_filtered_metric_tables_deprecated(
            self,
            metric_groups=None,
            metric_tables=None,
            metric_ab_majors=None,
            metric_ab_major_minors=None,
            dash_group_ids=None,
            compass_group_ids=None,
            include_core_metrics=False
    ):
        """Get a list of banjo.abtest.MetricTable including the metric_groups and metric_tables
        
        Most of the filtering conditions map to fields in sc-portal.quest.metadata_<date> tables.
        
        Parameters
        ----------
        metric_groups : list of str
            list of Quest metric groups (nb: not A/B dashboard metric groups) to include
            maps to the metadata bigquery table's metric_group field
            Examples: Story Our, Send Message, Snap
        metric_tables : list of str
            list of Quest Section + Quest jobs to include (these are also the metric's bigquery table names)
            maps to <quest_section>_<quest_job> in the metadata bigquery table            
        metric_ab_majors : list of str
            list of Quest AB MAJOR to include (https://ab.sc-corp.net/v2/metrics)
        metric_ab_major_minors : list of str
            list of Quest AB MAJOR and AB MINOR to include (https://ab.sc-corp.net/v2/metrics)
            in the format of ["{ab_major}____{ab_minor}", ...]
        dash_group_ids: list of str
            Deprecated in favor of compass_group_ids. The sequence IDs of the A/B dashboard groups.
        compass_group_ids: list of str
            The compass group ids
            maps to the metadata bigquery table's compass_group_ids field
            Examples: ab/crash, ab/friend_feed_performance
        include_core_metrics : bool
            Whether to include all core engagement and performance metrics

        Returns
        -------
        filtered_eligible_metric_tables : list of banjo.abtest.metric.MetricTable
        """

        if metric_groups is None:
            metric_groups = []
        if metric_tables is None:
            metric_tables = []
        if metric_ab_majors is None:
            metric_ab_majors = []
        if metric_ab_major_minors is None:
            metric_ab_major_minors = []
        if dash_group_ids is None:
            dash_group_ids = []
        if compass_group_ids is None:
            compass_group_ids = []
        metric_to_dash_group_id = self.get_metadata_mapping(from_column="metric_name", to_column="dash_group_id")
        metric_to_metric_group = self.get_metadata_mapping(from_column="metric_name", to_column="metric_group")
        metric_to_ab_major = self.get_metadata_mapping(from_column="metric_name", to_column="major_metric")
        metric_to_ab_major_minor = self.get_metadata_mapping(from_column="metric_name", to_column="major_minor")
        metric_to_compass_group_id = self.get_metric_to_compass_group_ids_mapping()

        def _is_core(metric_name, table_name):
            if metric_to_dash_group_id or metric_to_compass_group_id:
                return metric_to_dash_group_id.get(metric_name) in CORE_METRICS_GROUP_IDS or len(
                    metric_to_compass_group_id.get(metric_name).intersection(set(CORE_METRICS_COMPASS_GROUP_IDS))) > 0
            else:
                return False

        eligible_metric_tables = []

        for metric_table_name, metric_table in self.metric_tables.items():
            eligible_metrics = {}
            for metric_object in metric_table.metrics:
                if (metric_to_metric_group.get(metric_object.col) in metric_groups
                        or metric_table_name in metric_tables
                        or metric_to_ab_major.get(metric_object.col) in metric_ab_majors
                        or metric_to_ab_major_minor.get(metric_object.col) in metric_ab_major_minors
                        or metric_to_dash_group_id.get(metric_object.col) in dash_group_ids
                        or len(metric_to_compass_group_id.get(metric_object.col).intersection(set(compass_group_ids))) > 0
                        or (include_core_metrics and _is_core(metric_object.col, metric_table_name))):
                    eligible_metrics[metric_object.col] = metric_object
                    # add numerator and denominator of ratio
                    if metric_object.dist == "ratio":
                        eligible_metrics[metric_object.numerator_metric.col] = metric_object.numerator_metric
                        eligible_metrics[metric_object.denominator_metric.col] = metric_object.denominator_metric

            if eligible_metrics:
                metric_table_copy = copy.deepcopy(metric_table)
                # todo(xhe): using copy and directly assigning .metrics can have unintended consequences.
                # the MetricTable.__init__ could have mechanism to prevent instances from initialized using bad
                # values, this skips it.
                metric_table_copy.metrics = list(eligible_metrics.values())
                eligible_metric_tables.append(metric_table_copy)

        # use metric_table_name to check the quest table existence.
        # This check is for metrics deprecated in quest, but still exists in metadata
        filtered_eligible_metric_tables = []
        for eligible_metric_table in eligible_metric_tables:
            metric_table_name = eligible_metric_table.name
            table_exists = gbq.table_exists(
                project_id=ABQuestMetricCatalog.quest_project,
                dataset=ABQuestMetricCatalog.quest_dataset,
                table='{}_{}'.format(metric_table_name, self.start_date)
            ) and gbq.table_exists(
                project_id=ABQuestMetricCatalog.quest_project,
                dataset=ABQuestMetricCatalog.quest_dataset,
                table='{}_{}'.format(metric_table_name, self.end_date)
            )
            if not table_exists:
                logger.error(
                    "Missing Quest table for {}. \nThis is either due to that the metrics have been migrated"
                    " to a different table during the analysis period, or that the metrics have been removed.\n "
                    "Please ask in the #quest-ab channel if you have questions. Thanks!".format(
                        metric_table_name
                    )
                )
                continue
            else:
                filtered_eligible_metric_tables.append(eligible_metric_table)

        return filtered_eligible_metric_tables

    def get_metadata_mapping(self, from_column, to_column, sort_key_by=None):
        """Define a mapping between column key and column value from the metadata data frame
        """
        if from_column not in self.metadata.columns or to_column not in self.metadata.columns:
            return {}
        # keep non empty values
        df = self.metadata.loc[:, [from_column, to_column]].dropna()
        # remove empty strings
        df = df.loc[(df[from_column] != "") & (df[to_column] != ""), :]
        mapping = pd.Series(
            df[to_column].values,
            index=df[from_column]
        ).to_dict()

        if sort_key_by:
            mapping = OrderedDict(sorted(mapping.items(), key=lambda t: sort_key_by(t[0])))
        return mapping

    def get_metric_to_compass_group_ids_mapping(self):
        metric_to_compass_group_ids = defaultdict(set)
        for _, row in self.metadata.iterrows():
            metric_to_compass_group_ids[row.metric_name].update(row.compass_group_ids)

        return metric_to_compass_group_ids

    def bq_read_metadata(self, metadata_date):
        table_exists = gbq.table_exists(
            project_id=ABQuestMetricCatalog.quest_project,
            dataset=ABQuestMetricCatalog.quest_dataset,
            table=f"{ABQuestMetricCatalog.quest_table}_{metadata_date}"
        )
        if not table_exists:
            raise ValueError("Metadata table for {} does not exit".format(metadata_date))
        metadata = gbq.read_gbq(
            "SELECT * FROM `{project_id}.{dataset}.{table}`".format(
                project_id=ABQuestMetricCatalog.quest_project,
                dataset=ABQuestMetricCatalog.quest_dataset,
                table=f"{ABQuestMetricCatalog.quest_table}_{metadata_date}"
            ),
            project_id=self.bq_project,
            priority=self.bq_priority,
            dialect='standard',
            use_bqstorage_api=True,
            bq_retry=gbq.BATCH_PRIORITY_RETRY,
        )
        metadata["metric_name"] = metadata.apply(ABQuestMetricCatalog._get_quest_metric_name, axis=1)
        if "dash_name" in metadata:
            metadata["dash_name"] = metadata.dash_name.str.replace(r' (p|P)(5|9)0$', '', regex=True)
        return metadata

    def bq_read_available_metrics(self, start_date, end_date):
        # Keep only metrics that exist during the entire period, assuming at least one of them exists during the
        # entire period (and so the condition max_days = days works).
        # Also only keep metrics that did not switch one Quest job to another. These are output to different
        # bigquery tables. We currently define metrics based on tables as the units. Supporting these metrics requires
        # more complex mapping.

        available_metrics_query = f"""
        SELECT
          *
        FROM (
          SELECT
            major_metric,
            minor_metric,
            MAX(metric_type) AS metric_type,
            ARRAY_AGG(CONCAT("20", _TABLE_SUFFIX) ORDER BY _TABLE_SUFFIX) AS ds,
            ARRAY_AGG(cap_values_above ORDER BY _TABLE_SUFFIX) AS cap_values_above,
            COUNT(*) AS days,
            MIN(CONCAT("20", _TABLE_SUFFIX)) AS min_date,
            MAX(CONCAT("20", _TABLE_SUFFIX)) AS max_date,
            MAX(COUNT(*)) OVER () AS max_days,
            MIN(CONCAT(quest_section, "____", quest_job)) AS min_quest_table,
            MAX(CONCAT(quest_section, "____", quest_job)) AS max_quest_table,
          FROM
            `{ABQuestMetricCatalog.quest_project}.{ABQuestMetricCatalog.quest_dataset}.{ABQuestMetricCatalog.quest_table}_20*`
          WHERE 
            CONCAT("20", _TABLE_SUFFIX) BETWEEN "{start_date}" AND "{end_date}"
          GROUP BY
            1,
            2
          ORDER BY
            1,
            2
        )
        -- WHERE
          -- max_days = days
          --AND min_quest_table = max_quest_table
        """
        available_metrics = gbq.read_gbq(
            available_metrics_query,
            project_id=self.bq_project,
            priority=self.bq_priority,
            dialect='standard',
            use_bqstorage_api=True,
            bq_retry=gbq.BATCH_PRIORITY_RETRY,
        )
        if (
                available_metrics.empty
                or
                not pd.to_datetime(end_date) - pd.to_datetime(start_date) != timedelta(days=len(available_metrics) - 1)
        ):
            raise ValueError(f"Some metadata tables do not exit in this range {start_date} - {end_date}")

        invalid_metrics = available_metrics.loc[
            (available_metrics.max_days > available_metrics.days)
            |
            (available_metrics.min_quest_table != available_metrics.max_quest_table),
            :
        ]
        if not invalid_metrics.empty:
            logger.info("These metrics are dropped before they were newly added or switched between different"
                        " quest jobs. We support only metrics available in the entire analysis period.")
            for index, row in invalid_metrics.iterrows():
                logger.info(f"Metric dropped: {row.major_metric}____{row.minor_metric}")

        available_metrics = available_metrics.drop(invalid_metrics.index)

        available_metrics["metric_name"] = available_metrics.apply(ABQuestMetricCatalog._get_quest_metric_name, axis=1)
        return available_metrics

    @staticmethod
    def _get_quest_metric_name(row):
        # Add _ prefix as some major_metric now starts with numbers and make them not qualified BQ field names
        long_metric_name = '_' + row.major_metric.lower() + '_' + re.sub(r'^total_', '', row.minor_metric)
        # for quantile metrics
        return re.sub(r'_p(5|9)0$', '', long_metric_name)

    def _get_earliest_available_quest_bq_tables(self):
        """Get earliest date of each quest table
        table_name: quest table {quest_section}_{quest_job}

        This method is not used as we already filter the metadata to what's available on the start_date
        """
        available_bq_table_sql = """
            SELECT SUBSTR(table_name, 0, CHAR_LENGTH(table_name) - 9) as table_name,
            MIN(SUBSTR(table_name, -8)) AS ts
            FROM
            `{quest_project}.{quest_dataset}.INFORMATION_SCHEMA.TABLE_OPTIONS`
            GROUP BY table_name
        """.format(
            quest_project=ABQuestMetricCatalog.quest_project,
            quest_dataset=ABQuestMetricCatalog.quest_dataset
        )
        available_table_results = gbq.read_gbq(
            available_bq_table_sql,
            project_id=self.bq_project,
            priority=self.bq_priority,
            dialect='standard',
            use_bqstorage_api=True,
        )
        available_tables = {}
        for _, row in available_table_results.iterrows():
            available_tables[row[0]] = row[1]
        return available_tables

    def _transform_quest_metadata_to_metric_config(self, bq_dialect='standard'):
        ab_metric_configs = []

        cont_dict = defaultdict(dict)
        bin_dict = defaultdict(dict)
        quantile_dict = defaultdict(dict)
        ratio_dict = defaultdict(dict)
        metric_directions = defaultdict(dict)
        quest_table_dict = {}
        
        for _, row in self.metadata.iterrows():
            if (None in [row.quest_section, row.quest_job, row.quest_measure,
                         row.major_metric, row.minor_metric, row.metric_type]
                and row.metric_type != MetricType.METRIC_TYPE_RATIO):
                continue

            if row.metric_type != MetricType.METRIC_TYPE_RATIO:
                quest_section, quest_job = row.quest_section, row.quest_job
                quest_name = row.quest_measure
            else:
                quest_section, quest_job, quest_name = row.numerator.split('____')

            quest_table_name = '{}_{}'.format(quest_section, quest_job)
            metric_name = row.metric_name
            quest_table_dict[quest_table_name] = row.metric_group
            if row.metric_type in [MetricType.METRIC_TYPE_SUM, MetricType.METRIC_TYPE_ACTIVE_DAY]:
                if self.cap_metrics and 'cap_values_above' in row and row.cap_values_above is not None:
                    # applying the cap is unnecessary as we will apply the limit when doing the user day aggregation
                    # in Report.get_join_metrics_query
                    bq_cast_func_template = "FLOAT({quest_name})" if bq_dialect == 'legacy' else (
                        "CAST({quest_name} AS FLOAT64)")
                    cont_dict[quest_table_name][metric_name] = (
                        'IF(SUM({bq_cast_func}) > {cap:.8e}, {cap:.8e}, SUM({bq_cast_func}))'.format(
                            bq_cast_func=bq_cast_func_template.format(quest_name=quest_name),
                            # caps defined as float with default max float, convert to integer
                            cap=min(float(row.cap_values_above), float(six.MAXSIZE))
                        )
                    )
                else:
                    cont_dict[quest_table_name][metric_name] = 'SUM({quest_name})'.format(quest_name=quest_name)

            elif row.metric_type == MetricType.METRIC_TYPE_UNIQUE:
                bin_dict[quest_table_name][metric_name] = 'IF(MAX({quest_name}) > 0, 1, 0)'.format(
                    quest_name=quest_name)
            elif row.metric_type == MetricType.METRIC_TYPE_QUANTILE:
                quantile_dict[quest_table_name][metric_name] = 'IF({quest_name} >= 0, {quest_name}, NULL)'.format(
                    quest_name=quest_name
                )
            elif row.metric_type == MetricType.METRIC_TYPE_RATIO:
                denom_quest_section, denom_quest_job, denom_quest_name = row.denominator.split('____')
                if quest_section != denom_quest_section or quest_job != denom_quest_job:
                    logging.warning(f"The ratio metric {metric_name} is not supported because its numerator"
                                    f" and denominator are defined in different quest_job and stored "
                                    f"in different bigquery tables.")
                    continue
                try:
                    ratio_expression = '{numerator}/{denominator}'.format(
                        numerator=self._find_metadata_row(
                            quest_section=quest_section,
                            quest_job=quest_job,
                            quest_measure=quest_name,
                            sub_metric_type=row.numerator_metric_type,
                        ).metric_name,
                        denominator=self._find_metadata_row(
                            quest_section=denom_quest_section,
                            quest_job=denom_quest_job,
                            quest_measure=denom_quest_name,
                            sub_metric_type=row.denominator_metric_type,
                        ).metric_name
                    )
                    ratio_dict[quest_table_name][metric_name] = ratio_expression
                except ValueError as e:
                    logging.warning("Error processing ratio metric {}: {}".format(metric_name, e))
                    continue

            if row.good_direction.lower() == 'down':
                metric_directions[quest_table_name][metric_name] = MetricDirection.NEGATIVE
                metric_directions[quest_table_name]["{}_active_days".format(metric_name)] = MetricDirection.NEGATIVE
                metric_directions[quest_table_name][quest_name] = MetricDirection.NEGATIVE

        for quest_table_name in quest_table_dict.keys():
            bin_cols = bin_dict.get(quest_table_name, {})
            cont_cols = cont_dict.get(quest_table_name, {})
            quantile_cols = quantile_dict.get(quest_table_name, {})
            ratio_cols = ratio_dict.get(quest_table_name, {})

            # todo (xhe): we should split into two MetricTables when there are both quantile
            # and non-quantile metrics. As a temp fix, we drop the quantile metrics
            # As of 5/10/2022 there is only one such metric:
            # LENS_APPLY_DELAY_LATENCY____lens_explorer_apply_delay_latency
            # which is defined at the user level

            if quantile_cols and (bin_cols or cont_cols or ratio_cols):
                logger.warning(f"Quantiles metrics of metric table {quest_table_name} are dropped "
                               f"because the table includes non-quantile metrics: {list(quantile_cols.keys())}")
                quantile_cols = {}

            ab_metric_configs.append(
                ABMetricConfig(
                    user_col='ghost_user_id',
                    bin_cols=bin_cols,
                    cont_cols=cont_cols,
                    quantile_cols=quantile_cols,
                    ratio_cols=ratio_cols,
                    name=quest_table_name,
                    section=quest_table_dict[quest_table_name],
                    description=quest_table_dict[quest_table_name],
                    metric_desired_directions=metric_directions.get(quest_table_name, {}),
                )
            )

        return ab_metric_configs

    def _find_metadata_row(self, **kwargs):
        query = " and ".join("{} == {}".format(field, repr(value)) for field, value in kwargs.items())
        rows = self.metadata.query(query, inplace=False)
        if rows.empty:
            raise ValueError("No matches found for {}".format(dict(kwargs)))
        if len(rows) > 1:
            raise ValueError("Multiple matches found for {}".format(dict(kwargs)))
        return rows.iloc[0, :]

    def _get_quest_daily_aggregation_tables(self):
        if self.metadata is None or "is_hourly" not in self.metadata:
            return ABQuestMetricCatalog.quest_daily_aggregation_tables
        else:
            # treat missing values the same as True
            self.metadata.is_hourly.fillna(True, inplace=True)
            daily_metrics_metadata = self.metadata.loc[~self.metadata.is_hourly, :]
            return (daily_metrics_metadata.quest_section + "_" + daily_metrics_metadata.quest_job).unique().tolist()
