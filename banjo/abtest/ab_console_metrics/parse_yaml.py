# TODO(dmao): Remove prase_yaml, model is deprecated
import yaml
import re
import json
import os
from datetime import datetime
from banjo.abtest.ab_console_metrics.ab_metric_catalog import (
    ABMetricConfig, METRIC_JSON_FILENAME, QUANTILE_METRIC_JSON_FILENAME
)

IS_VALID = '0is_valid'
NAME = '1name'
GROUP = '2group'
DESCRIPTION = '3description'
USER_ID_FIELD = '5user_id_field'
INNER = '8inner_select'
OUTER = '9outer_select'
METRIC_TYPE = 'metric_type'


def strip_outermost_func(stmt):
    m = re.match(r"^\w*\((?P<value>.+)\)(?P<extra>.*)$", stmt)
    if m is not None:
        res = m.group('value')
        if m.group('extra'):
            res += m.group('extra')
        return res
    else:
        return stmt


def process_metric_config(config):
    if IS_VALID in config and not config[IS_VALID]:
        return None

    if (OUTER not in config
            or '5user_id_field' not in config
            or 'deprecate' in config.get(GROUP, '').lower()):
        print('--------------')
        print('Metric skipped')
        print(json.dumps(config, indent=2))
        return None

    metric_name = config.get(NAME, '')
    user_col = 'ghost_user_id'
    bin_cols, cont_cols, ratio_cols = {}, {}, {}
    not_parsed = []
    for column in config[OUTER]:
        if "expr" in column and "field_name" in column:
            expr = strip_outermost_func(column["expr"])

            # Some like to use COUNT(*) for user count
            if expr == "*" and "per_user_metric" in column:
                expr = column["per_user_metric"]
            colname = re.sub(r'^total_', '', column["field_name"])
            if metric_name:
                colname = '_'.join([metric_name.lower(), colname])
            if expr:
                if "metric_type" in column:
                    metric_type = column["metric_type"]
                    if metric_type == "user_count":
                        bin_cols[colname] = expr
                        continue
                    elif metric_type == "user_engagement":
                        cont_cols[colname] = expr
                        continue
                    elif metric_type == "ratio":
                        if 'sub_value' in column and 'total_value' in column:
                            numerator = re.sub(r'^total_', '', column["sub_value"])
                            denominator = re.sub(r'^total_', '', column["total_value"])
                            if metric_name:
                                numerator = '_'.join([metric_name.lower(), numerator])
                                denominator = '_'.join([metric_name.lower(), denominator])
                            ratio_cols[colname] = numerator + '/' + denominator

        if column.get("metric_type", '') not in ['percentile']:
            not_parsed.append(column)

    if not_parsed:
        print('---------------')
        print(config.get(GROUP, ''))
        print(config.get(NAME, ''))
        print('columns skipped')
        print(json.dumps(not_parsed, indent=2))

    if cont_cols or bin_cols:
        ab_metric = ABMetricConfig(
            user_col=user_col,
            bin_cols=bin_cols,
            cont_cols=cont_cols,
            ratio_cols=ratio_cols,
            name=metric_name,
            section=config.get(GROUP, ''),
            description=config.get(DESCRIPTION, '')
        )
    else:
        ab_metric = None
    return ab_metric


def process_quantile_metric_config(config):
    if IS_VALID in config and not config[IS_VALID]:
        return None

    if (OUTER not in config
            or '5user_id_field' not in config
            or 'deprecate' in config.get(GROUP, '').lower()):
        print('--------------')
        print('Metric skipped')
        print(json.dumps(config, indent=2))
        return None

    metric_name = config.get(NAME, '')
    user_col = 'ghost_user_id'
    exprs = {}
    for column in config[INNER]['select_fields']:
        if 'expr' in column and 'field_name' in column:
            exprs[column['field_name']] = column['expr']

    quantile_cols = {}
    for column in config[OUTER]:
        if "metric_field" in column and "metric_type" in column:
            expr = exprs.get(column['metric_field'])
            colname = re.sub(r'^total_', '', column["metric_field"])
            if metric_name:
                colname = '_'.join([metric_name.lower(), colname])
            if expr:
                if column["metric_type"] == 'percentile':
                    quantile_cols[colname] = expr
                    continue

    quantile_metrics = []
    if quantile_cols:
        for k in quantile_cols.keys():
            quantile_metrics.append({
                'user_col': user_col,
                'quantile_col': k,
                'quantile_expr' : quantile_cols.get(k),
                'name': metric_name,
                'section': config.get(GROUP, ''),
                'description': config.get(DESCRIPTION, '')
            })
    else:
        quantile_metrics = None
    return quantile_metrics


try:
    cur_path = os.path.dirname(os.path.abspath(__file__))
except NameError:
    cur_path = os.path.expanduser(
        '~/src/pyanalytics/banjo/abtest/ab_console_metrics')

YAML_DIR = os.path.join(cur_path, "yaml")
YAML_FILES = [os.path.join(YAML_DIR, file)
              for file in os.listdir(YAML_DIR) if file.endswith(".yaml")
              ]

ab_metrics = []
ab_quantile_metrics = []
for fp in YAML_FILES:
    with open(fp) as f:
        metric_config = yaml.load(f)
        metric = process_metric_config(metric_config)
        quantile_metric = process_quantile_metric_config(metric_config)
        if metric:
            ab_metrics.append(metric)
        if quantile_metric:
            ab_quantile_metrics.extend(quantile_metric)


with open(os.path.join(cur_path, 'UPDATED'), 'w') as f:
    f.write(datetime.now().isoformat())
    f.write(" {} metrics parsed".format(len(ab_metrics)))
    f.write("\n")

with open(os.path.join(cur_path, METRIC_JSON_FILENAME), 'w') as f:
    json.dump([metric.to_dict() for metric in ab_metrics], f,
              indent=2)

with open(os.path.join(cur_path, QUANTILE_METRIC_JSON_FILENAME), 'w') as f:
    json.dump(ab_quantile_metrics, f, indent=2)
