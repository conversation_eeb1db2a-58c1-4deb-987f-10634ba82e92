"""
Generate report for cohort breakdowns
"""
from __future__ import division, print_function

import itertools
import re
import warnings
from datetime import timed<PERSON><PERSON>
from typing import List

import pandas as pd
import sqlparse

from banjo.utils.gbq import (BigQueryRuntimeError, read_gbq, submit_sync_query,
                             table_exists)
from banjo.utils.helpers import hash_string

from .cohort_report_dimensions import (SQL_DF_FOR_YOU_LEU_ENGAGEMENT_STATUS,
                                       SQL_GDPR_COUNTRIES, SQL_GROWTH_REGION,
                                       SQL_USER_AND_FRIENDS_GROWTH_REGION,
                                       SQL_PUBLIC_PROFILE_FOLLOWER_COUNT_CAT,
                                       SQL_DEVICE_CLUSTER_BUCKET,
                                       Dimension)
from .data import *
from .mapping_table_config import ABTEST_PROJECT, MAPPING_TABLE_CONFIG
from .report import CustomReport


class CohortReport(CustomReport):
    """Provide standard breakdowns using [sc-analytics:report_search.user_cohorts_]"""

    cohort_table_path_stdsql = "sc-analytics.report_search.user_cohorts_{ds}"

    # when adding a new field, make sure to specify the first available date of the field
    # these are fields available in the sc-analytics.report_search.user_cohorts table

    cohort_dimensions = [
        Dimension("country_x_reported_age_bucket", "20221023"),
        Dimension("reported_age_bucket", None),
        Dimension("inferred_age_bucket", None),
        Dimension("gender", None),
        # 'is_test_user',
        Dimension("l_90_country", None),
        Dimension("locale", None, sql="SUBSTR(locale, 0, 2)"),
        # 'is_engaged_in_last_1_day',
        # 'is_engaged_in_last_30_days',
        # 'bidirectional_friend_count',
        # 'unidirectional_friend_count',
        # 'follower_count',
        # 'story_privacy',
        Dimension("os_type", None),  # rename this to os?
        Dimension("app_L7", None),
        Dimension("communication_L7", None),
        Dimension("publisher_L7", None),
        Dimension("story_L7", None),
        Dimension("is_phone_verified", None),
        Dimension("is_contact_sync_enabled", None),
        Dimension("is_email_verified", None),
        Dimension("relationship_count_acquaintance_plus_v3_status", "20231215"),
        Dimension("relationship_count_close_plus_v3_status", "20231215"),
        Dimension("relationship_count_strong_v3_status", "20231215"),
        Dimension("days_since_last_active", None),
        Dimension("is_s11_country", "20221001"),
        Dimension("is_spammer_l30", "20211103"),
        # 'creation_date_pst',
        Dimension("is_popular_user", None),
        Dimension("is_story_public_user", None),
        Dimension("days_since_creation", None),
        Dimension("app_engagement_status", None),
        Dimension("communication_engagement_status", None),
        Dimension("snap_create_engagement_status", None),
        Dimension("df_non_friend_story_engagement_status", None),
        Dimension("df_for_you_story_engagement_status", "20200903"),
        Dimension(
            "df_for_you_leu_engagement_status", sql=SQL_DF_FOR_YOU_LEU_ENGAGEMENT_STATUS
        ),
        Dimension("friend_story_engagement_status", None),
        Dimension("friend_story_engagement_status_v2", "20220331"),
        Dimension("public_user_story_engagement_status", None),
        Dimension("lens_engagement_status", None),
        Dimension("publisher_engagement_status", None),
        Dimension("publisher_subscription_status", None),
        Dimension("total_subscription_status", "********"),
        Dimension("bidirectional_friend_status", None),
        Dimension("unidirectional_friend_status", None),
        Dimension("df_show_viewer", "********"),
        Dimension("df_show_engagement_status", "********"),
        Dimension("spotlight_story_engagement_status", "********"),
        Dimension("spotlight_df_views_l28", "********"),
        Dimension("spotlight_status_l28_t20", "********"),
        # Dimension('multiple_country', None),
        # 'multiple_os',
        # 'multiple_accounts',
        # 'is_3pa_likely',
        Dimension("device_cluster", "********"),
        Dimension("device_maker", "********"),
        Dimension("device_model", "********"),
        Dimension("device_name", None),
        Dimension("country_bucket", "********"),
        Dimension("growth_region", sql=SQL_GROWTH_REGION),
        Dimension(
            "user_and_friends_growth_region", sql=SQL_USER_AND_FRIENDS_GROWTH_REGION
        ),
        Dimension(
            "os_version",  # todo: rename this to major_os_version
            "********",
            sql=r"CONCAT(cohort.os_type, ' ', "
            r"REGEXP_EXTRACT(cohort.os_version, r'^([^\.]+)\.?'))",
        ),
        Dimension("user_persona_v2", "********"),
        Dimension(
            "user_agg_bandwidth",
            "********",
            sql="IFNULL(cohort.user_agg_bandwidth, 'Not Available')",
        ),
        Dimension("has_bitmoji", None, sql=r"IF(cohort.has_bitmoji, 'true', 'false')"),
        Dimension("story_post_L7", "20200128"),
        Dimension("friend_story_post_L7", "20200128"),
        Dimension(
            "network_quality",
            "20200628",
            sql="IFNULL(cohort.network_quality, 'Not Available')",
        ),
        Dimension("aspect_ratio", "20200721"),
        Dimension("screen_size_bucket", "20200812"),
        Dimension("has_cameos", "20200826"),
        Dimension("cameos_engagement_status", "20200826"),
        Dimension("display_area_aspect_ratio", "20201013"),
        Dimension("chat_ct_engagement_status", "20200605"),
        Dimension("daily_active_friend_status", "20201029"),
        Dimension("daily_post_active_friend_status", "20201029"),
        Dimension("daily_communication_active_friend_status", "20201029"),
        Dimension("daily_snap_create_active_friend_status", "20201029"),
        Dimension("weekly_active_friend_status", "20201029"),
        Dimension("weekly_post_active_friend_status", "20201029"),
        Dimension("weekly_communication_active_friend_status", "20201029"),
        Dimension("weekly_snap_create_active_friend_status", "20201029"),
        Dimension("app_open_power_friend_status", "20201029"),
        Dimension("post_power_friend_status", "20201029"),
        Dimension("communication_power_friend_status", "20201029"),
        Dimension("snap_create_power_friend_status", "20201029"),
        Dimension("device_free_space_mb", "20210928"),
        Dimension("camera_user_segmentation", "20220727"),
        Dimension("fs_poster_segment", "20230106"),
        Dimension("creator_tier", "20230314"),
        Dimension("is_mau", None),
        Dimension("public_profile_tier", "20230328"),
        Dimension("with_snapchat_plus", "20230101"),
        Dimension("gdpr_country", None, sql=SQL_GDPR_COUNTRIES),
        Dimension("calling_engagement_status", "20220721"),
        Dimension("camera_user_engagement_v2", "20231018"),
        Dimension("days_since_myai_adoption","20230820"),
        Dimension("has_myai_bio","20230820"),
        Dimension("myai_engagement_status","20230820"),
        Dimension("map_persona_v1","20220801"),
        Dimension("l7_network_quality_v2", "20240121"),
        Dimension("l28_network_quality_v2", "20240121"),
        Dimension("darkmode_usage"),
        Dimension("resurrected_user_inactive_days", "20240425"),
        Dimension("snap_create_L7", "20220522"),
        Dimension("spotlight_user_segment", "20240825"),
        Dimension("non_friend_story_user_segment", "20240825"),
        Dimension("non_friend_content_user_segment", "20240825"),
        Dimension("plus_plan_type", "20241022"),
        Dimension("camera_creator_tier", None, sql=SQL_PUBLIC_PROFILE_FOLLOWER_COUNT_CAT),
        Dimension("device_cluster_bucket", None, sql=SQL_DEVICE_CLUSTER_BUCKET),
        Dimension("lens_engagement_status_v2", "20250515"),
        Dimension("chat_tab_L7","20250715"),
        Dimension("chat_tab_engagement_status","20250715"),
    ]
    cohort_fields = cohort_dimensions  # alias to support legacy use cases

    # when adding a new field, make sure to specify the first available date of the field
    # these are fields available in the a/b team's mapping table in usermap_cumulative
    study_user_dimensions = [
        Dimension("os", None, source_alias="study"),
        Dimension("app_version", None, source_alias="study"),
        Dimension("device_name", None, source_alias="study"),
        Dimension("device_cluster", None, source_alias="study"),
        Dimension("initial_timestamp", None, source_alias="study"),
        Dimension("country", None, source_alias="study"),
        Dimension("language", None, source_alias="study"),
        Dimension("age_group", None, source_alias="study"),
        Dimension("user_start_month", None, source_alias="study"),
        Dimension("user_start_date", None, source_alias="study"),
        Dimension("bidirectional_friend_status", None, source_alias="study"),
        Dimension("app_l7", None, source_alias="study"),
        Dimension("has_bitmoji", None, source_alias="study"),
        Dimension("device_maker", None, source_alias="study"),
        Dimension("major_os_version", None, source_alias="study"),
        Dimension("network_quality", None, source_alias="study"),
        Dimension("user_persona_v3", None, source_alias="study"),
        Dimension("is_spammer_l30", None, source_alias="study"),
        Dimension("with_snapchat_plus", None, source_alias="study"),
        Dimension("has_cameos", None, source_alias="study"),
        Dimension("aspect_ratio", None, source_alias="study"),
        Dimension("country_bucket", None, source_alias="study"),
        Dimension("business_report_region", None, source_alias="study"),
        Dimension("inclusive_region", None, source_alias="study"),
        Dimension("device_cluster_bucket", None, source_alias="study"),
        Dimension("time_in_study_days", None, source_alias="study"),
        Dimension("country_w_age_group", None, source_alias="study"),
        Dimension("study_new_user_type", None, source_alias="study"),
    ]

    def __init__(
        self,
        cohort_definition_date=None,
        materializing_mapping_table=True,
        overwrite_mapping_table=False,
        bq_dialect="standard",
        **kwargs
    ):
        # TODO: must be a better way to initialize this
        custom_mapping_sql = ""
        super(CohortReport, self).__init__(
            custom_mapping_sql=custom_mapping_sql,
            materializing_mapping_table=False,
            bq_dialect=bq_dialect,
            **kwargs
        )

        if self.mapping_table_ver == "v2":
            raise ValueError("The support for v2 metric tables have been discontinued.")

        if self.bq_dialect == "legacy":
            raise ValueError(
                "Legacy sql is no longer supported. Please use standard sql."
            )

        if not cohort_definition_date:
            cohort_definition_date = (
                pd.to_datetime(self.study_start_date) + timedelta(days=-1)
            ).strftime("%Y%m%d")
            self.logger.warning(
                "Cohort definition date not provided, defaulting to "
                "one day before study start date %s" % cohort_definition_date
            )
        self.cohort_definition_date = cohort_definition_date

        self.available_cohort_dimensions = [
            dimension
            for dimension in CohortReport.cohort_dimensions
            if dimension.start_date is None
            or dimension.start_date <= self.cohort_definition_date
        ]
        self.available_study_user_dimensions = [
            dimension
            for dimension in CohortReport.study_user_dimensions
            if dimension.start_date is None
            or dimension.start_date <= self.cohort_definition_date
        ]

        self.remove_invalid_user_group_by()
        self.user_group_vars = list(
            set(list(itertools.chain.from_iterable(self.user_group_by_list)))
        )
        self.ab_mapping_last_day_table_name = None

        self.init_mapping_stdsql()
        if materializing_mapping_table:
            self.materialize_mapping_table(overwrite=overwrite_mapping_table)

    # StandardSQL version of init_mapping_sql()
    def init_mapping_stdsql(self):
        # checks date range and default to max end date
        self.validate_analysis_date_range()

        ab_mapping_table_processed_stdsql = self.get_ab_mapping_table_processed_stdsql()

        custom_mapping_table_stdsql = """
            SELECT
              study.ghost_user_id AS ghost_user_id,
              study.exp_id AS exp_id,
              study.ts AS ts,
              study.exposure_ts AS exposure_ts
              {ab_user_fields}
              {cohort_fields}
            FROM
              `{cohort_table}` AS cohort
            RIGHT JOIN
              ({ab_mapping_table_processed}) AS study
            ON
              cohort.ghost_user_id = study.ghost_user_id
        """.format(
            ab_user_fields="\n".join(
                [
                    ", {sql} AS {field}".format(
                        field=dimension.name, sql=dimension.sql
                    )
                    for dimension in self.selected_study_user_dimensions
                ]
            ),
            cohort_fields="\n".join(
                [
                    ", {sql} AS {field}".format(
                        field=dimension.name, sql=dimension.sql
                    )
                    for dimension in self.cohort_only_dimensions
                ]
            ),
            ab_mapping_table_processed=ab_mapping_table_processed_stdsql,
            cohort_table=CohortReport.cohort_table_path_stdsql.format(
                ds=self.cohort_definition_date
            ),
        )

        self._custom_mapping_sql = sqlparse.format(
            custom_mapping_table_stdsql, reindent=True
        )
        # todo(xhe): remove these two lines after rewriting the sql to the new format
        self._old_mapping_format = True
        self.wrap_old_mapping_sql()
        self._mapping_table_hash = "user_cohort_breakdown_{}_{}".format(
            self.cohort_definition_date,
            hash_string(self.get_mapping_sql(dialect="standard")),
        )

    def get_ab_mapping_table_processed_stdsql(self):
        mapping_table_config = MAPPING_TABLE_CONFIG[self.mapping_table_ver]
        usermap_bq_tables_stdsql = self._get_usermap_stdsql()


        # new studies
        ab_mapping_table_processed_stdsql = """
            SELECT
              raw.ghost_user_id AS ghost_user_id,
              raw.exp_id AS exp_id,
              PARSE_TIMESTAMP('%Y%m%d', raw.ts) AS ts,
              mask.exposure_ts AS exposure_ts
              {ab_study_fields}
            FROM
              ({usermap_bq_tables}) AS raw
            LEFT JOIN
              (SELECT
                ghost_user_id, 
                exp_id, 
                MIN(TIMESTAMP_MILLIS(CAST(initial_timestamp AS INT64))) AS exposure_ts
                {ab_fields}
              FROM
                `{ab_project}.{ab_mapping_dataset}.{ab_mapping_last_day_table_name}`
              GROUP BY ghost_user_id, exp_id
              ) AS mask
            ON raw.ghost_user_id = mask.ghost_user_id AND raw.exp_id = mask.exp_id 
        """.format(
            ab_study_fields="\n".join(
                [
                    ", mask.{field} AS {field}".format(field=dim.name)
                    for dim in self.selected_study_user_dimensions
                ]
            ),
            ab_fields="\n".join(
                [
                    ", MAX({field}) AS {field}".format(field=dim.name)
                    for dim in self.selected_study_user_dimensions
                ]
            ),
            usermap_bq_tables=usermap_bq_tables_stdsql,
            ab_project=ABTEST_PROJECT,
            ab_mapping_dataset=mapping_table_config["dataset"],
            ab_mapping_last_day_table_name=self.ab_mapping_last_day_table_name,
        )

        return ab_mapping_table_processed_stdsql

    def _get_usermap_stdsql(self):
        mapping_table_config = MAPPING_TABLE_CONFIG[self.mapping_table_ver]
        downsample_to = self.downsample_to
        # bigquery uses the latest table schema when select with _TABLE_SUFFIX
        usermap_bq_tables = """
            (SELECT
              SUBSTR(_TABLE_SUFFIX, -8) AS ts, ghost_user_id, exp_id, {extra} {ab_fields}
            FROM `{ab_project}.{ab_mapping_dataset}.{study_name}__*`
            WHERE SAFE_CAST(SUBSTR(_TABLE_SUFFIX, 0, 8) AS INT64) > 0
              AND SUBSTR(_TABLE_SUFFIX, -8) BETWEEN '{start_date}' AND '{end_date}')
        """
        if downsample_to:
            return f"""
            SELECT
              ts,
              ghost_user_id,
              exp_id,
              {{ab_fields}}
            FROM ((
                {usermap_bq_tables}
                )
              INNER JOIN (
                SELECT
                  exp_id AS exp_id_joined,
                  LEAST({downsample_to}, MIN(COUNT(ghost_user_id)) OVER())/COUNT(ghost_user_id) AS sampling_ratio
                FROM
                  ({usermap_bq_tables})
                GROUP BY
                  exp_id)
              ON
                exp_id = exp_id_joined )
            WHERE
              uniform_number < sampling_ratio
            """.format(
                ab_fields=", ".join(
                    [
                        "{field}".format(field=dim.name)
                        for dim in self.selected_study_user_dimensions
                    ]
                ),
                ab_project=ABTEST_PROJECT,
                ab_mapping_dataset=mapping_table_config["dataset"],
                study_name=self.study_name,
                start_date=self.analysis_start_date,
                end_date=self.study_end_date,
                extra="""(CAST(FARM_FINGERPRINT(CONCAT(ghost_user_id)) AS FLOAT64)
                - CAST(-9223372036854775808 AS FLOAT64))
                 / (CAST(9223372036854775807 AS FLOAT64)
                 - CAST(-9223372036854775808 AS FLOAT64)) AS uniform_number,"""
            )
        else:
            return usermap_bq_tables.format(
                ab_fields=", ".join(
                    [
                        "{field}".format(field=dim.name)
                        for dim in self.selected_study_user_dimensions
                    ]
                ),
                ab_project=ABTEST_PROJECT,
                ab_mapping_dataset=mapping_table_config["dataset"],
                study_name=self.study_name,
                start_date=self.analysis_start_date,
                end_date=self.study_end_date,
                extra=""
            )


    def validate_analysis_date_range(self):
        """Validate the A/B user mapping tables exist for the specified analysis date range"""
        mapping_table_config = MAPPING_TABLE_CONFIG[self.mapping_table_ver]

        max_available_end_date_sql = """
            SELECT
              table_name, SUBSTR(table_name, -8) as ts
            FROM
              `{ab_project}.{ab_mapping_dataset}.INFORMATION_SCHEMA.TABLE_OPTIONS`
            WHERE table_name LIKE "{study_name}__%"
              AND SUBSTR(table_name, -8) >= '{start_date}'
              AND SUBSTR(table_name, -8) <= '{end_date}'
            ORDER BY table_name DESC
        """.format(
            ab_project=ABTEST_PROJECT,
            ab_mapping_dataset=mapping_table_config["dataset"],
            study_name=self.study_name,
            start_date=self.analysis_start_date,
            end_date=self.study_end_date,
        )

        max_end_date_result = read_gbq(
            max_available_end_date_sql,
            client=self.bq_client,
            dialect="standard",
            priority=self.bq_priority,
        )
        if max_end_date_result.empty:
            raise ValueError(
                "No mapping table found in date range {} - {}. "
                "Check if provided analysis start date"
                " is past the analysis pipeline end "
                "date.".format(
                    self.analysis_start_date or self.study_start_date,
                    self.study_end_date,
                )
            )
        else:
            max_available_end_date = max_end_date_result["ts"][0]
            if self.study_end_date > max_available_end_date:
                self.logger.warning(
                    "Study End Date is not valid, defaulting to maximum available date {}".format(
                        max_available_end_date
                    )
                )
                self.study_end_date = max_available_end_date
        self.ab_mapping_last_day_table_name = max_end_date_result["table_name"][0]

    @property
    def available_breakdown_fields(self) -> List[str]:
        fields =[
            dim.name
            for dim in set(self.available_cohort_dimensions).union(set(self.available_study_user_dimensions))
        ]
        return fields

    @property
    def selected_cohort_dimensions(self) -> List[Dimension]:
        return [
            dim
            for dim in self.available_cohort_dimensions
            if dim.name in self.user_group_vars
        ]

    @property
    def selected_study_user_dimensions(self) -> List[Dimension]:
        """Dimensions from the a/b mapping table selected for breakdown analysis"""
        return [
            dim
            for dim in self.available_study_user_dimensions
            if dim.name in self.user_group_vars
        ]

    def remove_invalid_user_group_by(self) -> None:
        # We used to allow users to prefix a dimension with study_ to indicate the user wants to use the dimension
        # from the a/b mapping tables. We now default to use a/b mapping when the dimension name is in the mapping
        # dimension list automatically. Doing this prefix removal of user input to support the old use case.
        # We could remove this step of using re.replace once all notebook and husky use case of
        # study_device_cluster etc is deprecated
        user_group_by_list_with_study_prefix_removed =[
            [re.sub(r"^study_", "", dimension) for dimension in user_group_by]
            # remove the re.replace once all notebook and husky use case of study_device_cluster etc is deprecated
            for user_group_by in self.user_group_by_list
        ]

        self.user_group_by_list = [
            user_group_by
            for user_group_by in user_group_by_list_with_study_prefix_removed
            if all(
                dimension in self.available_breakdown_fields
                for dimension in user_group_by
            )
        ]

    @property
    def cohort_only_dimensions(self) -> List[Dimension]:
        return list(set(self.selected_cohort_dimensions) - set(self.selected_study_user_dimensions))

    @property
    def study_only_dimensions(self) -> List[Dimension]:
        return list(set(self.selected_study_user_dimensions) - set(self.selected_cohort_dimensions))

    @property
    def cohort_study_mutual_dimensions(self) -> List[Dimension]:
        return list(set(self.selected_study_user_dimensions).intersection(set(self.selected_cohort_dimensions)))


class LensReport(CohortReport):
    def __init__(
        self,
        creative_lens_ids=None,
        any_lens_users=True,
        bq_dialect="standard",
        **kwargs
    ):
        self.creative_lens_ids = creative_lens_ids
        self.any_lens_users = any_lens_users

        super(LensReport, self).__init__(bq_dialect=bq_dialect, **kwargs)

    def get_ab_mapping_table_processed_stdsql(self):
        ab_mapping_table_processed_stdsql = super(
            LensReport, self
        ).get_ab_mapping_table_processed_stdsql()
        if self.creative_lens_ids is not None or self.any_lens_users:
            creativemap_bq_tables_stdsql = self._get_creativemap_stdsql()
            ab_mapping_table_processed_stdsql = """
                        SELECT raw_init.ts as ts, 
                            raw_init.ghost_user_id as ghost_user_id, 
                            raw_init.exp_id as exp_id,
                            raw_init.exposure_ts as exposure_ts
                            {ab_study_fields}
                        FROM ({ab_mapping_table}) AS raw_init
                        JOIN {creativemap_bq_tables} AS lens
                        ON raw_init.ts = lens.ts
                        AND raw_init.ghost_user_id = lens.ghost_user_id
                        """.format(
                ab_study_fields="\n".join(
                    [
                        ", raw_init.{field} AS {field}".format(field=dim.name)
                        for dim in self.selected_study_user_dimensions
                    ]
                ),
                ab_mapping_table=ab_mapping_table_processed_stdsql,
                creativemap_bq_tables=creativemap_bq_tables_stdsql,
            )

        return ab_mapping_table_processed_stdsql

    def _get_creativemap_stdsql(self):
        if self.creative_lens_ids is not None:
            creativemap_bq_tables = """
            (SELECT PARSE_TIMESTAMP('%Y%m%d', SUBSTR(_TABLE_SUFFIX, -8)) as ts, ghost_user_id
            FROM `sc-analytics.report_lens.lens_root_*`
            WHERE SAFE_CAST(SUBSTR(_TABLE_SUFFIX, 0, 8) AS INT64) > 0
                AND SUBSTR(_TABLE_SUFFIX, -8) BETWEEN '{start_date}' AND '{end_date}'
                AND filter_geolens_id in ({creative_lens_ids})
                AND (lens_swipe_count > 0 OR lens_spin_count > 0)
            GROUP BY ts, ghost_user_id)
            """.format(
                start_date=self.analysis_start_date,
                end_date=self.study_end_date,
                creative_lens_ids=self.creative_lens_ids,
            )
        else:
            creativemap_bq_tables = """
            (SELECT PARSE_TIMESTAMP('%Y%m%d', SUBSTR(_TABLE_SUFFIX, -8)) as ts, ghost_user_id
            FROM `sc-analytics.report_lens.lens_root_*`
            WHERE SAFE_CAST(SUBSTR(_TABLE_SUFFIX, 0, 8) AS INT64) > 0
                AND SUBSTR(_TABLE_SUFFIX, -8) BETWEEN '{start_date}' AND '{end_date}'
                AND (lens_swipe_count > 0 OR lens_spin_count > 0)
                AND REGEXP_CONTAINS(filter_geolens_id, r"^\d*$")
            GROUP BY ts, ghost_user_id)
            """.format(
                start_date=self.analysis_start_date, end_date=self.study_end_date
            )

        return creativemap_bq_tables


class NewUserCohortReport(CustomReport):
    """Provide breakdowns by whether it's new user, whether the user
    just signed up within N days.
    """

    pass


class SpotlightCohortReport(CohortReport):
    """Provide breakdowns by spotlight growth account/user status"""

    def __init__(self, bq_dialect="standard", spotlight_breakdowns=None, **kwargs):
        self.spotlight_breakdowns = spotlight_breakdowns or []
        super(SpotlightCohortReport, self).__init__(bq_dialect=bq_dialect, **kwargs)
        self.available_cohort_dimensions = [
            dimension
            for dimension in CohortReport.cohort_dimensions
            + [
                Dimension("spotlight_5_plus_story_session_7d_status", "********"),
                Dimension(
                    "spotlight_5_plus_story_session_7d_status_detailed", "********"
                ),
                Dimension("spotlight_story_view_7d_status", "********"),
                Dimension("spotlight_story_view_7d_status_detailed", "********"),
            ]
            if dimension.start_date is None
            or dimension.start_date <= self.cohort_definition_date
        ]

    def init_mapping_stdsql(self):
        # checks date range and default to max end date
        self.validate_analysis_date_range()

        ab_mapping_table_processed_stdsql = self.get_ab_mapping_table_processed_stdsql()

        custom_mapping_table_stdsql = """
            SELECT
              study.ghost_user_id AS ghost_user_id,
              study.exp_id AS exp_id,
              study.ts AS ts,
              study.exposure_ts AS exposure_ts
              {ab_user_fields}
              {cohort_fields}
              {spotlight_breakdown_fields}
            FROM
              ({ab_mapping_table_processed}) AS study
            LEFT JOIN
              (SELECT 
                *
              FROM
              `{cohort_table}`) AS cohort
            ON
              study.ghost_user_id = cohort.ghost_user_id
            LEFT JOIN
                (SELECT
                ghost_user_id,
                spotlight_5_plus_story_session_7d_status,
                spotlight_5_plus_story_session_7d_status_detailed,
                spotlight_story_view_7d_status,
                spotlight_story_view_7d_status_detailed,
                CASE
                    WHEN country IN ('US', 'SA', 'IN') THEN country
                    WHEN country IN ('AU', 'CA', 'FR', 'DE', 'NE', 'NO', 'SE', 'AE', 'GB') THEN 'REST_OF_S11'
                    WHEN country IN ('BH', 'DZ', 'EG', 'IL', 'IQ', 'JO', 'KW', 'LB', 'LY', 'MA', 'OM', 'PS', 'QA', 'TN') THEN 'REST_OF_MENA'
                ELSE 'ROW'
                END AS region,
                FROM
                `sc-analytics.report_spotlight.spotlight_feed_metrics_user_status_{ds}`)  spotlight_cohort
            ON
              study.ghost_user_id = spotlight_cohort.ghost_user_id
        """.format(
            ab_user_fields="\n".join(
                [
                    ", {sql} AS {field}".format(
                        field=dimension.name, sql=dimension.sql
                    )
                    for dimension in self.selected_study_user_dimensions
                ]
            ),
            cohort_fields="\n".join(
                [
                    ", {sql} AS {field}".format(
                        field=dimension.name, sql=dimension.sql
                    )
                    for dimension in self.cohort_only_dimensions
                ]
            ),
            ab_mapping_table_processed=ab_mapping_table_processed_stdsql,
            cohort_table=CohortReport.cohort_table_path_stdsql.format(
                ds=self.cohort_definition_date
            ),
            ds=self.cohort_definition_date,
            spotlight_breakdown_fields="," + ",".join(self.spotlight_breakdowns),
        )

        self._custom_mapping_sql = sqlparse.format(
            custom_mapping_table_stdsql, reindent=True
        )
        self.user_group_vars += self.spotlight_breakdowns
        if len(self.user_group_by_list) > 0 and len(self.user_group_by_list[0]) > 0:
            self.user_group_by_list += [
                [breakdown] for breakdown in self.spotlight_breakdowns
            ]
        else:
            self.user_group_by_list = [
                [breakdown] for breakdown in self.spotlight_breakdowns
            ]
        self.has_user_breakdowns = True

        # todo(xhe): remove these two lines after rewriting the sql to the new format
        self._old_mapping_format = True
        self.wrap_old_mapping_sql()
        self._mapping_table_hash = "user_cohort_breakdown_{}_{}".format(
            self.cohort_definition_date,
            hash_string(self.get_mapping_sql(dialect="standard")),
        )
