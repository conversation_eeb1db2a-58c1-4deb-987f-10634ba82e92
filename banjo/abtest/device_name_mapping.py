"""SQL string for mapping device model number to device commercial name

This is taken from the official AB pipeline. It does not include the latest models released since 2017 H2
"""

device_name_mapping = """
CASE
    WHEN {source_column} IN ('iPhone9,2',
                             'iPhone9,4') THEN 'iPhone7Plus'
    WHEN {source_column} IN ('iPhone9,1',
                             'iPhone9,3') THEN 'iPhone7'
    WHEN {source_column} = 'iPhone8,4' THEN 'iPhoneSE'
    WHEN {source_column} = 'iPhone8,2' THEN 'iPhone6sPlus'
    WHEN {source_column} = 'iPhone8,1' THEN 'iPhone6s'
    WHEN {source_column} = 'iPhone7,1' THEN 'iPhone6Plus'
    WHEN {source_column} = 'iPhone7,2' THEN 'iPhone6'
    WHEN {source_column} IN ('iPhone6,1',
                             'iPhone6,2') THEN 'iPhone5s'
    WHEN {source_column} IN ('iPhone5,3',
                             'iPhone5,4') THEN 'iPhone5c'
    WHEN {source_column} IN ('iPhone5,1',
                             'iPhone5,2') THEN 'iPhone5'
    WHEN {source_column} = 'iPhone4,1' THEN 'iPhone4s'
    WHEN {source_column} IN ('iPhone3,1',
                             'iPhone3,2',
                             'iPhone3,3') THEN 'iPhone4'
    WHEN {source_column} CONTAINS 'SM-G935' THEN 'S7Edge'
    WHEN {source_column} CONTAINS 'SM-G930' THEN 'S7'
    WHEN {source_column} CONTAINS 'SM-G928' THEN 'S6EdgePlus'
    WHEN {source_column} CONTAINS 'SM-G925' THEN 'S6Edge'
    WHEN {source_column} CONTAINS 'SM-G890'
      OR {source_column} CONTAINS 'SM-G920' THEN 'S6'
    WHEN {source_column} CONTAINS 'SM-G900'
      OR {source_column} CONTAINS 'SM-G901' THEN 'S5'
    WHEN {source_column} CONTAINS 'GT-I9500'
      OR {source_column} CONTAINS 'GT-I9505'
      OR {source_column} CONTAINS 'GT-I9506'
      OR {source_column} CONTAINS 'SGH-I337'
      OR {source_column} CONTAINS 'SCH-I545'
      OR {source_column} CONTAINS 'GT-I9515'
      OR {source_column} CONTAINS 'SGH-M919' THEN 'S4'
    WHEN {source_column} CONTAINS 'GT-I9192'
      OR {source_column} CONTAINS 'GT-I9190'
      OR {source_column} CONTAINS 'GT-I9195' THEN 'S4Mini'
    WHEN {source_column} CONTAINS 'GT-I9300'
      OR {source_column} CONTAINS 'GT-I9305'
      OR {source_column} CONTAINS 'SGH-T999'
      OR {source_column} CONTAINS 'SGH-I747' THEN 'S3'
    WHEN {source_column} CONTAINS 'SM-N920' THEN 'Note5'
    WHEN {source_column} CONTAINS 'SM-N910' THEN 'Note4'
    WHEN {source_column} CONTAINS 'SM-N900' THEN 'Note3'
    WHEN {source_column} CONTAINS 'SM-G530'
      OR {source_column} CONTAINS 'SM-G531' THEN 'GrandPrime'
    WHEN {source_column} CONTAINS 'SM-G360'
      OR {source_column} CONTAINS 'SM-G361' THEN 'CorePrime'
    WHEN {source_column} CONTAINS 'SM-J500' THEN 'J5'
    WHEN {source_column} CONTAINS 'SM-A500' THEN 'A5'
    ELSE 'OTHER'
END
"""


def get_device_name_mapping_sql(source_column):
    return device_name_mapping.format(source_column=source_column)
