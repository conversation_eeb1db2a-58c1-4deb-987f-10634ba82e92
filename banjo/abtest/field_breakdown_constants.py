# Always-on fields allowlist for all Blizzard events
always_on_fields_allowlist = {
    "os_type": "os_type",
    "device_connectivity": "device_connectivity",
    # "app_travel_mode": "app_travel_mode",
}

# Event specific fields allowlist
event_specific_fields_allowlist = {
    # "DIRECT_SNAP_SEND": {"os_type": "CASE WHEN os_type='iOS' THEN 'iOS' ELSE 'non-iOS' END"},
}

# Metric -> SQL overwrite allowlist
metric_sql_definition_allowlist = {
    # "DIRECT_SNAP_SEND____total_sent_snap": {
    #     "blizzard_events": "DIRECT_SNAP_SEND", 
    #     "query": """
    #             SELECT
    #             1 AS direct_snap_send
    #             FROM
    #             `sc-analytics.prod_analytics_snap.daily_events_yyyyMMdd`
    #             WHERE
    #             event_name = 'DIRECT_SNAP_SEND'
    #             AND recipient_count > 0
    #             """,
    #     "operation": "SUM", # RATIO, <PERSON>UM, <PERSON><PERSON><PERSON><PERSON>, QUAN<PERSON><PERSON>
    #     "display_name": "Snap Send",
    #     "calculation_metric_name": "direct_snap_send", # same as the "AS direct_snap_send" part in the query
    #     "good_direction": "UP"
    # },
    "CHAT_CHAT_VIEW____notif_tentpole_chat_view": {
        "blizzard_events": "CHAT_CHAT_VIEW", 
        "query": """
                SELECT
                1 AS notif_tentpole_chat_view
                FROM
                `sc-analytics.prod_analytics_chat.daily_events_yyyyMMdd`
                WHERE
                event_name = 'CHAT_CHAT_VIEW'
                AND teamsnap_id <> ''
                """,
        "operation": "SUM", 
        "display_name": "Team Snapchat Chat View",
        "calculation_metric_name": "notif_tentpole_chat_view",
        "good_direction": "UP"
    },
    "SNAP_SEND_OR_POST_BITMOJI_STICKER_IN_SNAP____snap_send_or_post_sticker_in_snap": {
        "blizzard_events": "DIRECT_SNAP_PREVIEW", 
        "query": """
                SELECT
                    IF(snap_send_or_post > 0
                    AND sticker_count > 0,
                    sticker_count,
                    0) AS snap_send_or_post_sticker_in_snap
                FROM
                    (SELECT
                        IF((with_snap_send
                        OR with_story_post
                        AND direct_snap_preview > 0),
                        direct_snap_preview,
                        0) AS snap_send_or_post,
                        sticker_count AS sticker_count
                    FROM
                        (SELECT
                            sticker_count AS sticker_count,
                            1 AS direct_snap_preview,
                            with_story_post AS with_story_post,
                            with_snap_send AS with_snap_send
                        FROM
                            `sc-analytics.prod_analytics_snap.daily_events_yyyyMMdd`
                        WHERE
                            event_name = 'DIRECT_SNAP_PREVIEW' )
                    WHERE
                        (
                            with_snap_send
                            OR with_story_post
                            AND direct_snap_preview > 0
                        )
                    )
                WHERE
                    snap_send_or_post > 0
                    AND sticker_count > 0
                """,
        "operation": "SUM", 
        "display_name": "Sticker Count in Snap Send or Post",
        "calculation_metric_name": "snap_send_or_post_sticker_in_snap",
        "good_direction": "UP"
    },
    "SNAP_SEND_OR_POST_BITMOJI_STICKER_IN_SNAP____snap_with_sticker_send_or_post": {
        "blizzard_events": "DIRECT_SNAP_PREVIEW", 
        "query": """
                SELECT
                    1 AS snap_with_sticker_send_or_post
                FROM
                    (SELECT
                        IF(snap_send_or_post > 0
                        AND sticker_count > 0,
                        sticker_count,
                        0) AS snap_send_or_post_sticker_in_snap
                    FROM
                        (SELECT
                            IF((with_snap_send
                            OR with_story_post
                            AND direct_snap_preview > 0),
                            direct_snap_preview,
                            0) AS snap_send_or_post,
                            sticker_count AS sticker_count
                        FROM
                            (SELECT
                                sticker_count AS sticker_count,
                                1 AS direct_snap_preview,
                                with_story_post AS with_story_post,
                                with_snap_send AS with_snap_send
                            FROM
                                `sc-analytics.prod_analytics_snap.daily_events_yyyyMMdd`
                            WHERE
                                event_name = 'DIRECT_SNAP_PREVIEW' )
                        WHERE
                            (
                                with_snap_send
                                OR with_story_post
                                AND direct_snap_preview > 0
                            )
                        )
                    WHERE
                        snap_send_or_post > 0
                        AND sticker_count > 0 )
                WHERE
                    snap_send_or_post_sticker_in_snap > 0
                """,
        "operation": "SUM", 
        "display_name": "Snap With Sticker Send Or Post",
        "calculation_metric_name": "snap_with_sticker_send_or_post",
        "good_direction": "UP"
    },
}