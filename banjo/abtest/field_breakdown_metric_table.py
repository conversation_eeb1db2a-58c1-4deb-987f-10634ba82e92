"""MetricTable class for metric field level breakdowns"""
from collections import Iterable
from functools import partial
import numpy as np
import pandas as pd

from .analysis import compare_metric_table, compare_quantile_metrics, multiple_treatment_ids
from .data import aggregate_metric_table_by_day, aggregate_quantile
from .metric import BaseMetricTable, Calculator
from banjo.utils import helpers
from .stat_funcs import calc_chisq_dist
from IPython.core.display import display, HTML


class FieldBreakdownMetricTable(BaseMetricTable):
    def __init__(self,
                 sql,
                 metrics,
                 name=None,
                 inner_join_with_mapping=False,
                 unit_id='ghost_user_id',
                 join_key='ghost_user_id',
                 cumulative=True,
                 daily=True,
                 quantile_metrics=False,
                 metric_section=None,
                 bq_dialect='legacy',
                 breakdowns=None,
                 sql_callable=None,
                 aa=False,
                 breakdown_top_n=None,
                 breakdown_threshold=None
                 # todo(xhe): maybe add breakdown filters
                 ):
        """ Metric Table class for metrics with metric field level breakdowns
        For example, snap send by device_connectivity, snap send by media_type

        Parameters
        ----------
        sql : str
        The query string that defines the metric table.
        metrics : list of banjo.abtest.metric.Metric
        The list of metrics defined by the sql
        name : str
        The unique name of the metric_table
        inner_join_with_mapping : bool
        Whether the mapping table should be inner-joined with the metric_table. On its way of deprecation.
        unit_id : str
        join_key : str
        cumulative : bool
        Whether cumulative results should be computed
        daily : bool
        Whether daily results should be computed
        quantile_metrics : bool
        Whether the metric table include dist="quantile" metrics
        metric_section : str
        Meta data for the metric table
        bq_dialect : str
        "standard" or "legacy"
        breakdowns : list of lists
        Each list of the breakdowns list should include the SQL field names of the breakdown. The SQL field must be
        of the type STRING. For example `[["device_connectivity", "media_type"], ["media_type"]]`
        sql_callable : function
        breakdown_top_n : dict
            breakdown_column : str
            What column candidates are based on (e.g. creator_ghost_user_id, story_id, creator_id)
            top_n : int
            The number of candidates to keep to speed up calculation and limit the output table size
            metric: str
            What metric to order top n candidates on
            order_by_exp_id : str (optional)
            If supplied, limits the metric aggregation and top n candidates to that single exp_id
        This dictionary instructs how many top breakdown levels are kept in the results.
        breakdown_threshold : dict
            breakdown_column : str
            What column candidates are based on (e.g. story_id, tile_id)
            metric: str
            What metric to order top n candidates on
            control : str (optional)
            control id
            treatment : str (optional) (one or more of control or treatment must be supplied)
            treatment_id
            control_threshold : int (optional) default 100
            minimum metric value of control
            treatment_threshold : int (optional) default 100
            minimum metric value of treatment
        This dictionary instructs what are the minimum metric values are kept in the results per experiment id.
        e.g. {"breakdown_column":"creator_ghost_user_id", "top_n":10000, "metric":"view_count", "order_by_exp_id":"1"}.
        """
        if not isinstance(breakdowns, Iterable) or not all(isinstance(breakdown, Iterable) for breakdown in breakdowns):
            raise ValueError("breakdowns should be a list of lists")
        self.breakdowns = breakdowns
        self.unique_breakdown_fields = list(set(helpers.merge_list_of_lists(self.breakdowns)))
        self.breakdown_top_n = breakdown_top_n
        self.breakdown_threshold = breakdown_threshold
        super(FieldBreakdownMetricTable, self).__init__(
            sql=sql,
            metrics=metrics,
            name=name,
            inner_join_with_mapping=inner_join_with_mapping,
            unit_id=unit_id,
            join_key=join_key,
            cumulative=cumulative,
            daily=daily,
            quantile_metrics=quantile_metrics,
            metric_section=metric_section,
            bq_dialect=bq_dialect,
            sql_callable=sql_callable,
            aa=aa,
        )

    @property
    def cols(self):
        cols = super(FieldBreakdownMetricTable, self).cols
        return cols + self.unique_breakdown_fields

    def get_calculators(
            self,
            report,
            table_path,
            date_col,
            user_col,
            user_group_cols,
            cumulative,
            cumulative_trend,
            **kwargs,
    ):
        calculators = []
        # [[]]: for the overall metric results
        for breakdown in [[]] + list(self.breakdowns):
            if not user_group_cols:
                user_group_cols = []
            else:
                user_group_cols = list(user_group_cols)

            analysis_func = partial(
                FieldBreakdownMetricTable.compare_metrics_with_field_breakdown,
                self.quantile_metrics
            )

            if self.quantile_metrics:
                quantile_metrics = self.get_metric_objects_by_dist('quantile')
                for quantile_metric in quantile_metrics:
                    agg_query = aggregate_quantile(
                        table_path,
                        bq_project="",
                        date_col=date_col,
                        user_col=user_col,
                        metric=quantile_metric,
                        quantiles=report.quantiles,
                        user_group_cols=user_group_cols,
                        metric_group_cols=breakdown,
                        cumulative=cumulative,
                        verbose=False,
                        dry_run=True,
                        **kwargs,
                    )
                    calculators.append(
                        Calculator(
                            agg_query,
                            # todo: drop field breakdown columns ?
                            analysis_func=analysis_func,
                            func_args=dict(
                                quantile_vars=[quantile_metric.col + '_' + quantile for quantile in report.quantiles],
                                user_group_cols=user_group_cols,
                                breakdown=breakdown,
                                breakdown_top_n=self.breakdown_top_n,
                                breakdown_threshold=self.breakdown_threshold,
                            ),
                            metric_table=self,
                            bq_dialect="standard",
                        )
                    )

            else:
                # non-quantile metric
                agg_query = aggregate_metric_table_by_day(
                    table_path,
                    bq_project="",
                    date_col=date_col,
                    user_col=user_col,
                    metric_table=self,
                    user_group_cols=user_group_cols,
                    metric_group_cols=breakdown,
                    cumulative=cumulative,
                    verbose=False,
                    dry_run=True,
                    cumulative_trend=cumulative_trend,
                    **kwargs,
                )
                calculators.append(
                    Calculator(
                        agg_query,
                        # todo: drop field breakdown columns ?
                        analysis_func=analysis_func,
                        func_args=dict(
                            metric_table=self,
                            user_group_cols=user_group_cols,
                            breakdown=breakdown,
                            breakdown_top_n=self.breakdown_top_n,
                            breakdown_threshold=self.breakdown_threshold,
                        ),
                        metric_table=self,
                        bq_dialect="standard",
                    )
                )

        return calculators

    @staticmethod
    def detect_field_hte_cont_bin(results):
        results = results.copy()
        chisq_dist_threshold = 0.50  # todo (bonnie): to adjust threshold if needed.
        # Indicate if the data is used in HTE test
        hte_tested = (
                (results["avg_control"] + results["avg_treatment"] > 0) &
                (results["avg_control"] > 0) &
                (-results["diff"].isna()) & (-results["p_value"].isna())
        )
        results["metric_breakdown_hte_tested"] = hte_tested
        # Calculate chisq_dist
        chisq_dist = calc_chisq_dist(results["avg_control"].values, results["avg_treatment"].values)
        results.loc[hte_tested, "chisq_dist"] = chisq_dist
        # Label metric_breakdown_hte_outcome
        # todo: add HTE decision back once we are satisfied with the criteria.
        results.loc[hte_tested, "metric_breakdown_hte_outcome"
        ] = np.where(chisq_dist >= chisq_dist_threshold, " ", " ")

        # Calculate abs diff %, distribution %, and their ratio.
        total_diff = results[results['period'] == "AB"]["avg_treatment"].sum() - results[results['period'] == "AB"][
            "avg_control"].sum()
        results.loc[hte_tested, "metric_breakdown_abs_diff_pct"
        ] = np.where(total_diff != 0,
                     (results.loc[hte_tested, "avg_treatment"] -
                      results.loc[hte_tested, "avg_control"]) / total_diff,
                     np.nan
                     )
        results.loc[hte_tested, "metric_breakdown_dist_pct"
        ] = results.loc[hte_tested, "avg_control"] / results[results['period'] == "AB"]["avg_control"].sum()

        results.loc[hte_tested, "metric_breakdown_abs_diff_to_dist_ratio"
        ] = (results.loc[hte_tested, "metric_breakdown_abs_diff_pct"] /
             results.loc[hte_tested, "metric_breakdown_dist_pct"]
             )
        return results

    @staticmethod
    def detect_field_hte_quantile(results):
        results = results.copy()
        chisq_dist_threshold = 0.50  # todo (bonnie): to adjust threshold if needed.
        p_value_threshold = 0.01
        # Indicate if the data is used in HTE test
        hte_tested = (
                (results["volume_control"] + results["volume_treatment"] > 0) &
                (results["volume_control"] > 0) &
                (-results["diff"].isna()) & (-results["p_value"].isna())
        )
        results["metric_breakdown_hte_tested"] = hte_tested
        # Define HTE based on event counts (chisq_dist)
        chisq_dist = calc_chisq_dist(results["avg_volume_control"].values, results["avg_volume_treatment"].values)
        results.loc[hte_tested, "chisq_dist"] = chisq_dist
        event_count_hte = chisq_dist >= chisq_dist_threshold

        # Define HTE based on quantile stats
        total_group = len(results)
        pos_sig_group = sum((results['pct_diff'] > 0) & (abs(results['p_value']) < p_value_threshold))
        neg_sig_group = sum((results['pct_diff'] < 0) & (abs(results['p_value']) < p_value_threshold))
        sig_group = pos_sig_group + neg_sig_group
        non_sig_group = total_group - pos_sig_group - neg_sig_group
        quantile_hte = ((sig_group * non_sig_group != 0) | (pos_sig_group * neg_sig_group != 0))

        # Label metric_breakdown_hte_outcome
        if quantile_hte and event_count_hte:
            results.loc[hte_tested, "metric_breakdown_hte_outcome"] = "HTE on quantile & event count"
        elif quantile_hte:
            results.loc[hte_tested, "metric_breakdown_hte_outcome"] = "HTE on quantile"
        elif event_count_hte:
            results.loc[hte_tested, "metric_breakdown_hte_outcome"] = "HTE on event count"
        else:
            results.loc[hte_tested, "metric_breakdown_hte_outcome"] = "No HTE"

        # Calculate abs diff %, distribution %, and their ratio.
        total_diff = results[results['period'] == "AB"]["avg_volume_treatment"].sum() - results[results['period'] == "AB"][
            "avg_volume_control"].sum()
        results.loc[hte_tested, "metric_breakdown_abs_diff_pct"
        ] = np.where(total_diff != 0,
                     (results.loc[hte_tested, "avg_volume_treatment"] -
                      results.loc[hte_tested, "avg_volume_control"]) / total_diff,
                     np.nan
                     )
        results.loc[hte_tested, "metric_breakdown_dist_pct"
        ] = results.loc[hte_tested, "avg_volume_control"] / results[results['period'] == "AB"]["avg_volume_control"].sum()

        results.loc[hte_tested, "metric_breakdown_abs_diff_to_dist_ratio"
        ] = (results.loc[hte_tested, "metric_breakdown_abs_diff_pct"] /
             results.loc[hte_tested, "metric_breakdown_dist_pct"]
             )
        return results

    @staticmethod
    def breakdown_top_n_filter(df, breakdown_top_n, breakdown_threshold=None):
        if breakdown_threshold is None:
            metric = f"{breakdown_top_n['metric']}_sum"
            breakdown_col = breakdown_top_n["breakdown_column"]
            top_n = breakdown_top_n["top_n"]
        else:
            metric = f"{breakdown_threshold['metric']}_sum"
            breakdown_col = breakdown_threshold["breakdown_column"]
            top_n = 1000
        if "order_by_exp_id" in breakdown_top_n:
            top_n_candidates_df = df[df['exp_id'] == breakdown_top_n['order_by_exp_id']][
                [breakdown_col, metric]].groupby(breakdown_col).agg({metric: "sum"}).reset_index()
        else:
            top_n_candidates_df = df[[breakdown_col, metric]].groupby(breakdown_col).agg(
                {metric: "sum"}).reset_index()
        top_n_candidates_df = top_n_candidates_df.sort_values(
            by=metric,
            ascending=False).head(top_n)
        return df.merge(top_n_candidates_df[[breakdown_col]].drop_duplicates(), on=breakdown_col)

    @staticmethod
    def breakdown_threshold_filter(df, breakdown_threshold):
        metric = f"{breakdown_threshold['metric']}_sum"
        breakdown_col = breakdown_threshold["breakdown_column"]
        control = breakdown_threshold.get("control")
        threshold_candidate_list = set(df[breakdown_col].tolist())
        threshold_list = []
        if control:
            control_threshold = breakdown_threshold.get("control_threshold")
            if not control_threshold:
                pass
            else:
                threshold_list += list(threshold_candidate_list.intersection(set(
                    df[(df['exp_id'].astype(int) == int(control)) & (df[metric] > control_threshold)][
                        breakdown_col])))
        treatment = breakdown_threshold.get("treatment")
        if treatment:
            treatment_threshold = breakdown_threshold.get("treatment_threshold")
            if not treatment_threshold:
                pass
            else:
                threshold_list += list(threshold_candidate_list.intersection(set(df[(df['exp_id'].astype(
                    int).isin([int(i) for i in treatment])) & (df[metric] > treatment_threshold)][breakdown_col])))
        if len(threshold_list) == 0:
            display(HTML(f"<h5>Control / Treatment Threshold Not Provided or No value Passes the Threshold.  Taking the Top 1000 {breakdown_col} instead</h5>"))
            return FieldBreakdownMetricTable.breakdown_top_n_filter(
                df=df,
                breakdown_top_n={},
                breakdown_threshold=breakdown_threshold
                )
        else:
            return df[df[breakdown_col].isin(threshold_list)]

    @staticmethod
    def compare_metrics_with_field_breakdown(quantile_metrics, *args, **kwargs):
        # We filter to the top top_n entities by breakdown ordered by metric when breakdown_top_n is added as a field in FieldBreakdownMetricTable
        agg_df = args[0]
        args = args[1:]
        breakdown_top_n = kwargs.pop("breakdown_top_n")
        breakdown_threshold = kwargs.pop("breakdown_threshold")
        if kwargs["breakdown"]:
            if breakdown_top_n:
                agg_df = FieldBreakdownMetricTable.breakdown_top_n_filter(agg_df, breakdown_top_n)
            if breakdown_threshold:
                agg_df = FieldBreakdownMetricTable.breakdown_threshold_filter(agg_df, breakdown_threshold)
        user_group_cols = kwargs.pop("user_group_cols")
        breakdown = kwargs.pop("breakdown")
        kwargs["group_vars"] = user_group_cols + breakdown
        if quantile_metrics:
            results = multiple_treatment_ids(compare_quantile_metrics)(agg_df, *args, **kwargs)
        else:
            results = multiple_treatment_ids(compare_metric_table)(agg_df, *args, **kwargs)
        if results is None or results.empty:
            return results

        if breakdown:
            groups = sorted(breakdown)
            results["metric_breakdown_dimensions"] = "|".join(groups)
            for group in groups:
                results[group] = results[group].astype(str)
            results["metric_breakdown_values"] = results[groups[0]].str.cat(
                results[groups[1:]],
                sep='|',
                na_rep=""
            )
            if not user_group_cols:
                # Apply HTE detection
                if quantile_metrics:
                    results_grouped = results.groupby(["metric", "quantile", "treatment_id"])
                else:
                    results_grouped = results.groupby(["metric", "treatment_id"])
                df_list = []
                for metric, results_subset in results_grouped:
                    if quantile_metrics:
                        df = FieldBreakdownMetricTable.detect_field_hte_quantile(results_subset)
                        df_list.append(df)
                    elif (results_subset["dist"].iloc[0] == "cont") or (results_subset["dist"].iloc[0] == "bin"):
                        df = FieldBreakdownMetricTable.detect_field_hte_cont_bin(results_subset)
                        df_list.append(df)
                    else:
                        # todo: add ratio metrics later
                        df_list.append(results_subset)
                if df_list:
                    results = pd.concat(df_list).reset_index(None, drop=True)
        else:
            results["metric_breakdown_dimensions"] = "Overall"
            results["metric_breakdown_values"] = "Overall"

        # note(xhe): assign proper orders to the metric_breakdown_values as a Categorical variable will be difficult
        # unlike in user level breakdowns where we keep different breakdowns in different tables
        # here we put different field breakdowns in the same pandas data frame.


        return results
