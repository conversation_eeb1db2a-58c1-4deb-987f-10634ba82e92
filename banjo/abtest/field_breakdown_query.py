import os
import datetime
from functools import lru_cache
import pandas as pd
import json
import sqlparse
import re

import banjo
from banjo import utils
from banjo.abtest import field_breakdown_metric_table
from banjo.abtest import field_breakdown_constants as fbc
from functools import partial


def generate_original_event(metric_name, date):
    # format of metric_name is ab_major____ab_minor (all lower case)

    # check if the metric_name format is expected
    if "____" in metric_name:
        ab_major = metric_name.replace("____", " ").split()[0]
        ab_minor = metric_name.replace("____", " ").split()[1]
    else:
        print(
            "Sorry, this metric has a wrong naming format. Expecting ab_major____ab_minor.",
        )
        return ""

    # check if the metric is included in the overwrite allowlist in field_breakdown_constants.py
    if metric_name in fbc.metric_sql_definition_allowlist:
        return fbc.metric_sql_definition_allowlist[metric_name]["blizzard_events"]

    # metric definition query
    metric_definition_sql = """
    SELECT
    *
    FROM
    `sc-analytics.report_search.ab_metrics_catalog_20231001` 
    -- Using fixed date as there's a change in Quest SQL definition on 10/08/2023. 
    -- We need to refactor this module to work with the new definition.
    WHERE 
    LOWER(major) = LOWER("{ab_major}")
    AND LOWER(minor) = LOWER("{ab_minor}")
    """

    # query the metric definition
    metric_definition = utils.gbq.read_gbq(
        metric_definition_sql.format(
            date=date,
            ab_major=ab_major, ab_minor=ab_minor),
        project_id="sc-bq-gcs-billingonly",
        dialect="standard",
    )

    # check if there's any matching metric definition
    if metric_definition.shape[0] == 0:
        print(
            "Sorry, can't find a matching metric in `sc-analytics.report_search.ab_metrics_catalog_20231001`.".format(
                date=date)
        )
        return ""

    # check if there's any matching Blizzard event
    if metric_definition.loc[0, "blizzard_events"] == "" or metric_definition.loc[0, "blizzard_events"] is None:
        print(
            "Sorry, can't find a matching Blizzard event in `sc-analytics.report_search.ab_metrics_catalog_20231001`.".format(
                date=date)
        )
        return ""

    # metric_definition_json_dic = json.loads(metric_definition.json[0])
    metric_definition_blizzard_event = list(metric_definition.blizzard_events)

    return metric_definition_blizzard_event[0].split(",")[0]


def generate_breakdown_field_dictionary(
        event_name,
        date,
        null_value_percent_thresh=0.5,  # setting null value threshold to include the field
        unique_value_thresh=5,  # setting unique value threshold to bucket or use raw field
):
    breakdown_field_dict = {}

    # 0. Add most recent app versions breakdown 
    # App Version: Fetching the most recent app versions
    app_version_sql = """
        SELECT 
            app_version_minor 
        FROM
            (SELECT app_version_minor, MAX(days_since_launch) AS days_since_launch FROM `sc-analytics.report_search.app_version_history_*`
            WHERE 
                _TABLE_SUFFIX BETWEEN "{date_minus_one}" AND "{date}"
                AND app_build = "prod"
            GROUP BY app_version_minor 
            )
        ORDER BY days_since_launch
        LIMIT 10
    """
    app_version_recent_list = utils.gbq.read_gbq(
        app_version_sql.format(date=date,
                               date_minus_one=str(
                                   datetime.date(int(date[0:4]), int(date[4:6]), int(date[6:8])) - datetime.timedelta(
                                       days=1)).replace("-", "")),
        project_id="sc-bq-gcs-billingonly",
        dialect="standard",
    )

    breakdown_field_dict.update(
        {"app_version":
            (
                "CASE WHEN REGEXP_EXTRACT(app_version, r'(.*?\..*?\.[\d]*?)\.') IN ({app_version_recent_list}) "
                "THEN REGEXP_EXTRACT(app_version, r'(.*?\..*?\.[\d]*?)\.') "
                "ELSE 'Older Versions' END"
            ).format(
                app_version_recent_list=', '.join(
                    '"{0}"'.format(v) for v in app_version_recent_list.app_version_minor)
            )
        }
    )

    # 1. Fields that are always useful (support bucketing), from field_breakdown_constants.py

    breakdown_field_dict.update(fbc.always_on_fields_allowlist)

    # 2. Fields to run for specific events, and pre-defined additional dimensions based on the existing fields (e.g. pre-defined cutoff values for fields)

    breakdown_field_dict.update(fbc.event_specific_fields_allowlist.get(event_name, {}))

    # 3. select fields with less than x% of null values

    # Query from Max's table to get all available fields and metadata of the fields
    metadata_sql = """
    SELECT
    *
    FROM
    `sc-analytics.report_search.blizzard_field_metadata_{date_minus_one}`
    WHERE 
    event_name = "{event_name}"
    """

    event_field_metadata = utils.gbq.read_gbq(
        metadata_sql.format(
            date_minus_one=str(
                datetime.date(int(date[0:4]), int(date[4:6]), int(date[6:8])) - datetime.timedelta(days=1)).replace("-",
                                                                                                                    ""),
            event_name=event_name
        ),
        project_id="sc-bq-gcs-billingonly",
        dialect="standard",
    )

    # If there's no matching event in the metadata table, return empty list and raise an exception
    if event_field_metadata.shape[0] == 0:
        print(
            "Sorry, this metric doesn't have any event metadata ({event_name}) in `sc-analytics.report_search.blizzard_field_metadata_{date_minus_one}`.".format(
                event_name=event_name,
                date_minus_one=str(
                    datetime.date(int(date[0:4]), int(date[4:6]), int(date[6:8])) - datetime.timedelta(days=1)).replace(
                    "-", ""),
            )
        )
        return {}

    fields_above_null_value_thresh = event_field_metadata.loc[
        event_field_metadata.loc[:, "not_null_count"]
        / event_field_metadata.loc[:, "event_count"]
        > null_value_percent_thresh,
        ["field", "field_type", "unique_value_count"],
    ]

    # remove "_id" like fields. To do: are there other non-id fields that look like _id?
    fields_above_null_value_thresh_excl_id = fields_above_null_value_thresh.loc[
                                             ["_id" not in field for field in fields_above_null_value_thresh.field], :
                                             ]

    # remove all fields with only 1 unique value
    fields_above_null_value_thresh_excl_id_or_single_value = (
        fields_above_null_value_thresh_excl_id.loc[
        fields_above_null_value_thresh_excl_id.unique_value_count > 1, :
        ]
    )

    # separate out string fields and numeric fields
    string_fields_above_null_value_thresh_excl_id = (
        fields_above_null_value_thresh_excl_id_or_single_value.loc[
        (
                fields_above_null_value_thresh_excl_id_or_single_value.field_type
                == "STRING"
        )
        | (
                fields_above_null_value_thresh_excl_id_or_single_value.field_type
                == "BOOLEAN"
        ),
        :,
        ]
    )
    numeric_fields_above_null_value_thresh_excl_id = (
        fields_above_null_value_thresh_excl_id_or_single_value.loc[
        (
                fields_above_null_value_thresh_excl_id_or_single_value.field_type
                == "FLOAT"
        )
        | (
                fields_above_null_value_thresh_excl_id_or_single_value.field_type
                == "INTEGER"
        ),
        :,
        ]
    )

    # Check if there are actually any fields for breakdown. If not, then raise an exception
    if (
            len(string_fields_above_null_value_thresh_excl_id.field)
            + len(numeric_fields_above_null_value_thresh_excl_id.field)
            == 0
    ):
        print(
            "There are no fields available for this Blizzard event to breakdown. Contact @xhe, @ytang to add support for this event."
        )
        return {}

    # 4. Numerical Fields

    # Check if there are numeric fields: 
    if len(numeric_fields_above_null_value_thresh_excl_id.field) != 0:

        # query all numerical fields' distribution
        distribution_sql = """
        SELECT
        field,
        quantiles
        FROM
        `sc-analytics.report_search.blizzard_field_distribution_{date_minus_one}`
        WHERE 
        event_name = "{event_name}"
        AND field IN ({field_list})
        AND is_latest_app_version
        AND app_build=""
        AND os_type="iOS"
        """

        numeric_field_distribution = utils.gbq.read_gbq(
            distribution_sql.format(
                date_minus_one=str(
                    datetime.date(int(date[0:4]), int(date[4:6]), int(date[6:8])) - datetime.timedelta(days=1)).replace(
                    "-", ""),
                event_name=event_name,
                field_list=", ".join(
                    '"{0}"'.format(field)
                    for field in numeric_fields_above_null_value_thresh_excl_id.field
                ),
            ),
            project_id="sc-bq-gcs-billingonly",
            dialect="standard",
        )

        # Check for each field and insert into the final fields dictionary
        for numeric_field, unique_value_count in zip(
                numeric_fields_above_null_value_thresh_excl_id.field,
                numeric_fields_above_null_value_thresh_excl_id.unique_value_count,
        ):

            # If number of unique values > Y1, bucket into P0, P10, P25, P50, P75, P90, P100. Using iOS distribution for now

            if unique_value_count > unique_value_thresh:

                # check if there is actually a quantile distribution for this field in the table returned; if not, skip this field;
                if numeric_field not in numeric_field_distribution.field.tolist():
                    continue

                # check if the length of the quantile distribution is shorter than 20; if so, skip this field.
                if (
                        len(
                            numeric_field_distribution.loc[
                                numeric_field_distribution.field == numeric_field, "quantiles"
                            ].tolist()[0]
                        )
                        == 0
                ):
                    continue

                distribution = numeric_field_distribution.loc[
                    numeric_field_distribution.field == numeric_field, "quantiles"
                ].tolist()[0]

                # mapping the percentiles of desire to the index of the returned array
                percentiles_dict = dict(
                    zip(
                        ["P0", "P25", "P50", "P75", "P100"],
                        distribution[[0, 5, 10, 15, 20]],
                    )
                )

                bucket_definition = """
                                    CASE WHEN {numeric_field} < {P25} THEN '01. <{P25}' 
                                         WHEN {numeric_field} >= {P25} AND {numeric_field} < {P50} THEN '02. >={P25} AND <{P50}' 
                                         WHEN {numeric_field} >= {P50} AND {numeric_field} < {P75} THEN '03. >={P50} AND <{P75}' 
                                         WHEN {numeric_field} >= {P75} THEN '04. >={P75}' 
                                         WHEN {numeric_field} IS NULL THEN '05. NULL'
                                    END
                                    """.format(
                    numeric_field=numeric_field,
                    P0=round(percentiles_dict["P0"], 4),
                    P25=round(percentiles_dict["P25"], 4),
                    P50=round(percentiles_dict["P50"], 4),
                    P75=round(percentiles_dict["P75"], 4),
                    P100=round(percentiles_dict["P100"], 4),
                )
                # create the pair and insert into the final dictionary; renamed as field_name + _bucket
                breakdown_field_dict.update({numeric_field + "_bucket": bucket_definition})

            # Else if number of unique value < Y1, run for all breakdowns
            else:

                breakdown_field_dict.update(
                    {numeric_field: "IFNULL(CAST({} AS STRING), '<NULL>')".format(numeric_field)}
                )

    # 5. Categorical Fields

    # Check if there are categorical fields: 
    if len(string_fields_above_null_value_thresh_excl_id.field) != 0:

        # query all string fields' distribution
        distribution_sql = """
        SELECT
        field,
        t.value,
        t.count
        FROM
        `sc-analytics.report_search.blizzard_field_distribution_{date_minus_one}`, UNNEST(top_count) AS t
        WHERE 
        event_name = "{event_name}"
        AND field IN ({field_list})
        AND is_latest_app_version
        AND app_build=""
        AND os_type="iOS"
        ORDER BY field, t.count desc
        """

        string_field_distribution = utils.gbq.read_gbq(
            distribution_sql.format(
                date_minus_one=str(
                    datetime.date(int(date[0:4]), int(date[4:6]), int(date[6:8])) - datetime.timedelta(days=1)).replace(
                    "-", ""),
                event_name=event_name,
                field_list=", ".join(
                    '"{0}"'.format(field)
                    for field in string_fields_above_null_value_thresh_excl_id.field
                ),
            ),
            project_id="sc-bq-gcs-billingonly",
            dialect="standard",
        )

        # Check for each field and insert into the final fields dictionary
        for string_field, unique_value_count in zip(
                string_fields_above_null_value_thresh_excl_id.field,
                string_fields_above_null_value_thresh_excl_id.unique_value_count,
        ):

            # If number of unique values > Y1, choose the only top Y1 values and bucket all the rest. Using iOS distribution for now

            if unique_value_count > unique_value_thresh:

                top_string_values_to_keep = string_field_distribution.loc[
                                                string_field_distribution.field == string_field, "value"
                                            ].tolist()[0:unique_value_thresh]

                # check if the length of the top values are actually larger than the unique value threshold; if not, skip this field.
                if len(top_string_values_to_keep) < unique_value_thresh:
                    continue

                string_bucket_definition = """
                          CASE 
                            WHEN CAST({string_field} AS STRING) IN ({top_value_list}) THEN CAST({string_field} AS STRING)
                            WHEN {string_field} IS NULL THEN "<NULL>"
                            ELSE "OTHER"
                          END""".format(
                    string_field=string_field,
                    top_value_list=", ".join(
                        '"{0}"'.format(value) for value in top_string_values_to_keep
                    ),
                )
                # create the pair and insert into the final dictionary; renamed as field_name + _bucket
                breakdown_field_dict.update(
                    {string_field + "_bucket": string_bucket_definition}
                )

            # Else if number of unique value < Y1, run for all breakdowns
            else:

                breakdown_field_dict.update({string_field: "IFNULL(CAST({} AS STRING), '<NULL>')".format(string_field)})

    # 6. Multi-dimensional breakdowns - TBD

    # allowlist in the config file
    # Step by step
    # Run the one-dimension breakdown first
    # Select the top few dimensions that have “HTE”, and run multi-dimensional

    # 7. rename the fields to make sure there's no field name crash between Quest definition and additional fields

    breakdown_field_dict_renamed = {(name + "_"): definition for name, definition in breakdown_field_dict.items()}

    return breakdown_field_dict_renamed


def generate_original_query(metric_name, date):
    # check if the metric is included in the overwrite allowlist in field_breakdown_constants.py
    if metric_name in fbc.metric_sql_definition_allowlist:
        return sqlparse.format(
            fbc.metric_sql_definition_allowlist[metric_name]["query"], reindent=True, keyword_case="upper"
        )

    # format of metric_name is ab_major____ab_minor (all lower case)

    metric_definition_sql = """
    SELECT
    *
    FROM
    `sc-analytics.report_search.ab_metrics_catalog_20231001`
    WHERE 
    LOWER(major) = LOWER("{ab_major}")
    AND LOWER(minor) = LOWER("{ab_minor}")
    """

    ab_major = metric_name.replace("____", " ").split()[0]
    ab_minor = metric_name.replace("____", " ").split()[1]

    metric_definition = utils.gbq.read_gbq(
        metric_definition_sql.format(date=date, ab_major=ab_major, ab_minor=ab_minor),
        project_id="sc-bq-gcs-billingonly",
        dialect="standard",
    )

    # check if there's any matching SQL definition
    if metric_definition.loc[0, "query"] == "":
        print(
            "Sorry, can't find a matching SQL definition in `sc-analytics.report_search.ab_metrics_catalog_20231001`.".format(
                date=date)
        )
        return ""

    # metric_definition_json_dic = json.loads(metric_definition.json[0])
    metric_definition_blizzard_event = list(metric_definition.blizzard_events)
    metric_definition_blizzard_query_raw = metric_definition.loc[0, "query"]

    # formatting the original query to ensure consistency of keyword case
    metric_definition_blizzard_query = sqlparse.format(
        metric_definition_blizzard_query_raw, reindent=True, keyword_case="upper"
    )

    return metric_definition_blizzard_query


@lru_cache(maxsize=128)
def fetch_metric_definition(metric_name, metric_date):
    # format of metric_name is ab_major____ab_minor (all lower case)

    # check if the metric is included in the overwrite allowlist in field_breakdown_constants.py
    if metric_name in fbc.metric_sql_definition_allowlist:
        return pd.DataFrame(data=fbc.metric_sql_definition_allowlist[metric_name], index=[0])

    no_metadata_message = (
        "There's no metadata matching this metric '{metric_name}' in "
        "`sc-analytics.report_search.ab_metrics_catalog_20231001`."
    ).format(date=metric_date, metric_name=metric_name)
    if metric_date <= "20201007":
        # Metadata prior to 10/07 do not have all the necessary fields
        print(no_metadata_message)
        return None
    # otherwise check the definition from the catalog table
    metric_definition_sql = """
    SELECT
    *
    FROM
    `sc-analytics.report_search.ab_metrics_catalog_20231001`
    WHERE 
    LOWER(major) = LOWER("{ab_major}")
    AND LOWER(minor) = LOWER("{ab_minor}")
    """

    ab_major = metric_name.replace("____", " ").split()[0]
    ab_minor = metric_name.replace("____", " ").split()[1]

    metric_definition = utils.gbq.read_gbq(
        metric_definition_sql.format(date=metric_date, ab_major=ab_major, ab_minor=ab_minor),
        project_id="sc-bq-gcs-billingonly",
        dialect="standard",
    )

    if metric_definition is None or metric_definition.empty:
        print(no_metadata_message)
        return None
    elif len(metric_definition.loc[0, "query"]) == 0 or "operation" not in metric_definition.columns:
        print(
            "There's no query definition or A/B operation for this metric '{metric_name}' in "
            "`sc-analytics.report_search.ab_metrics_catalog_20231001`.".format(date=metric_date, metric_name=metric_name)
        )
        return None
    else:
        return metric_definition


def replace_from_last_occurrence(string, old, new, occurrence):
    list_item = string.rsplit(old, occurrence)
    return new.join(list_item)


def generate_field_breakdown_metric_table(
        metric_name, field_dict, start_date, end_date
):
    # format of metric_name is ab_major____ab_minor (all lower case)
    metric_definition = fetch_metric_definition(metric_name, end_date)

    # a few quality checks before moving forward
    if metric_definition is None:
        return None

    original_query = sqlparse.format(
        metric_definition.loc[0, "query"], reindent=True, keyword_case="upper"
    )
    original_operation = metric_definition.loc[0, "operation"]
    good_direction = metric_definition.loc[0, "good_direction"]

    # check if the metric is included in the overwrite allowlist in field_breakdown_constants.py
    if metric_name in fbc.metric_sql_definition_allowlist:
        calculation_metric_name = metric_definition["calculation_metric_name"][0]
        major_minor_metric_name = "{}_{}".format(
            metric_name.replace("____", " ").split()[0].lower(),
            re.sub(r"^total_", "", metric_name.replace("____", " ").split()[1]),
        )
    else:
        metric_definition_json_dic = json.loads(metric_definition.json[0])
        calculation_metric_name = metric_definition_json_dic["relationshipAttributes"][
            "ParentMetric"
        ][0][
            "displayText"
        ]  # not display name
        major_minor_metric_name = "{}_{}".format(
            metric_definition.major[0].lower(),
            re.sub(r"^total_", "", metric_definition.minor[0]),
        )

    # metric_definition_blizzard_event = list(metric_definition.blizzard_events)

    # Perform the AB operation to the original definition
    # validation below shows that ~100% of the metrics have a SELECT xxxxxxx AS calculation_metric_name before the
    # first FROM, and this is what needs to be replaced by the operation
    # https://docs.google.com/spreadsheets/d/1OabB6JehTb80Y5REKFrWFrklv9xT2ahviR9jOAUub_o/edit#gid=2145445933

    # for SUM/COUNT operations:
    if original_operation.lower() in ["sum", "count"]:
        # if the old format (query starting with "SELECT 1 AS {metric_name}")
        if "1" in original_query[0:10]:
            original_query_with_operation = (
                original_query.replace("SELECT", "SELECT {operation}(", 1)
                .format(operation=original_operation)
                .replace(
                    "AS {metric_name}".format(metric_name=calculation_metric_name),
                    ") AS {metric_name}",
                )
                .format(metric_name=major_minor_metric_name)
            )  # only replace the first occurrence of SELECT *** AS metric name
        # if the new format (query starting with "SELECT {metric_name}")
        else:
            original_query_with_operation = (
                original_query.replace("SELECT {metric_name} AS {metric_name}".format(
                    metric_name=calculation_metric_name),
                    "SELECT {metric_name}".format(
                        metric_name=calculation_metric_name))
                .replace("SELECT {metric_name}".format(metric_name=calculation_metric_name),
                         "SELECT {operation}({metric_name}) AS {major_minor_metric_name},".format(
                             operation=original_operation,
                             metric_name=calculation_metric_name,
                             major_minor_metric_name=major_minor_metric_name)
                         , 1)

            )  # only replace the first occurrence of SELECT *** AS metric name

    ############### Unfinished: how to take UU/activeDay metrics? ###############
    # for QUANTILE operations:
    if original_operation.lower() == "unique":
        original_query_with_operation = original_query
    ##########################################################################

    ############### Unfinished: how to take quantile metrics? ###############
    # for QUANTILE operations:
    if original_operation.lower() == "quantile":
        original_query_with_operation = original_query
    ##########################################################################

    # Remove ghost_user_id from SELECT part if there is any, to avoid collision with the ghost_user_id to be added
    original_query_with_operation_remove_user_id = original_query_with_operation.replace(
        "ghost_user_id,", ""
    )

    # TO DO: how to handle metrics with the joining of two events?
    original_query_with_operation_ts_field_interim = replace_from_last_occurrence(
        original_query_with_operation_remove_user_id,
        "SELECT",
        """
                                            SELECT
                                            TIMESTAMP(PARSE_DATE('%Y%m%d', _TABLE_SUFFIX)) AS ts,
                                            ghost_user_id,
                                            """
        + ",\n    ".join(
            [
                m + " AS " + n
                for m, n in zip(list(field_dict.values()), list(field_dict.keys()))
            ]
        )
        + ",\n",
        1,
    )  # only replace the last occurrence

    original_query_with_operation_ts_field = original_query_with_operation_ts_field_interim.replace(
        "SELECT",
        """
                                    SELECT
                                    ts,
                                    ghost_user_id,
                                    """
        + ",\n    ".join([n for n in list(field_dict.keys())])
        + ",\n",
        original_query_with_operation_ts_field_interim.count("SELECT") - 1,
    )  # replace the otehr occurrences with field name only

    # Handling the GROUP BY clause
    if "GROUP BY".lower() not in original_query_with_operation_ts_field.lower():
        # TO DO: what if GROUP BY clause are not supposed to be put at the end?
        original_query_with_operation_ts_field_groupby = (
                original_query_with_operation_ts_field
                + "\nGROUP BY\n    ts,\n    ghost_user_id,\n    "
                + ",\n    ".join(field_dict.keys())
        )
    else:
        original_query_with_operation_ts_field_groupby = (
            original_query_with_operation_ts_field.replace(
                "GROUP BY",
                "GROUP BY\n    ts,\n    ghost_user_id,\n    "
                + ",\n    ".join(field_dict.keys())
                + ",\n",
            )
        )

        # Handling the WHERE clause

    def final_sql(original_sql, start, end):
        if "yyyyMMdd` WHERE".lower() not in " ".join(original_sql.lower().split()):
            sql_final = sqlparse.format(
                original_sql.replace("yyyyMMdd`", "*`\nWHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}' \n"),
                reindent=True, keyword_case="upper")
        # if there is already a WHERE statement, then add the date range to the WHERE statement
        else:
            sql_final = sqlparse.format(" ".join(original_sql.split()).replace("yyyyMMdd` WHERE",
                                                                               "*` WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}' AND"),
                                        reindent=True, keyword_case="upper")
        return sql_final.format(start=start, end=end)

        # Generate the metricTable

    # decide the distribution of the metric
    if original_operation.lower() in ["sum", "count"]:
        metric_distribution_type = "cont"
    elif original_operation.lower() == "quantile":
        metric_distribution_type = "quantile"
    else:
        metric_distribution_type = "cont"

    if (metric_definition.loc[0, "display_name"] != "") & (metric_definition.loc[0, "display_name"] is not None):
        metric_display_name = metric_definition.loc[0, "display_name"]
    else:
        metric_display_name = metric_name

    metric_table_with_field_breakdown = field_breakdown_metric_table.FieldBreakdownMetricTable(
        sql=None,
        metrics=[
            banjo.abtest.metric.Metric(
                col=major_minor_metric_name,
                name=metric_display_name,
                dist=metric_distribution_type,
                desired_direction=(
                    banjo.abtest.metric.NEGATIVE if good_direction.lower() == "down" else banjo.abtest.metric.POSITIVE),
            )
        ],
        name=major_minor_metric_name,
        breakdowns=[[field] for field in field_dict.keys()],
        bq_dialect="standard",
        sql_callable=partial(final_sql, original_query_with_operation_ts_field_groupby),  # this query is already grouping by ts
    )

    return metric_table_with_field_breakdown


def split_dict_equally(input_dict, max_size=10):
    "Splits dict by keys. Returns a list of dictionaries."
    # prep with empty dicts
    chunks = ((len(input_dict.values()) - 1) // max_size) + 1
    return_list = [dict() for idx in range(chunks)]
    idx = 0
    for k, v in input_dict.items():
        return_list[idx][k] = v
        if idx < chunks - 1:  # indexes start at 0
            idx += 1
        else:
            idx = 0
    return return_list


def generate_field_breakdown_metric_table_list(
        metric_name, field_dict, start_date, end_date
):
    # format of metric_name is ab_major____ab_minor (all lower case)
    metric_definition = fetch_metric_definition(metric_name, end_date)
    if metric_definition is None:
        return []
    operation = metric_definition.loc[0, "operation"]

    # currently only supports sum/count metrics. Raise an exception when a quantile metric is selected
    if operation.lower() not in ["sum", "count"]:
        print(
            "Sorry, the current Field Breakdown analysis only support SUM or COUNT metrics. ",
            "Please contact @xhe or @ytang if you think this analysis can be helpful for any other metrics.",
        )
        return []

    metric_table_with_field_breakdown_list = []

    field_dict_list = split_dict_equally(input_dict=field_dict, max_size=10)

    for split_field_dict in field_dict_list:

        metric_table = generate_field_breakdown_metric_table(
            metric_name, split_field_dict, start_date, end_date
        )

        if metric_table is not None:
            metric_table_sql = metric_table.sql if metric_table.sql_callable is None else metric_table.sql_callable(start_date, end_date)
            try:
                if utils.gbq.check_query_data_access(str(metric_table_sql), dialect="standard"):
                    metric_table_with_field_breakdown_list.append(metric_table)
            except:
                print("This modified SQL is not executable. Skipping the metric: ", metric_name)
                print("Below is the modified SQL query: \n", metric_table_sql)
                return []

    return metric_table_with_field_breakdown_list
