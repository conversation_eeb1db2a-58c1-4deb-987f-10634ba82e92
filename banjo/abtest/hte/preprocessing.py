from sklearn.preprocessing import TargetEncoder
from sklearn.preprocessing import OrdinalEncoder
from banjo.descriptive_stats.field import Catego<PERSON><PERSON><PERSON>
from typing import List
import numpy as np
import pandas as pd


# TODO: this could be a exhaustive list of ordinal features, or improve to be an user input field in HUSKY
# for now, we just use a predefined list of ordinal features from default selected features in current conversion lift investigation report
ORDINAL_FEATURE_PREDEFIND_LIST = [
    "age_group",
    "days_since_creation",
    "bidirectional_friend_status",
    "friend_story_engagement_status_v2",
    'spotlight_story_engagement_status',
    'lens_engagement_status',
    'app_engagement_status',
    'friend_story_engagement_status',
    'df_non_friend_story_engagement_status'
]


def feature_encoding(df: pd.DataFrame,
                    X_columns: List[str],
                    y_column: str,
                    control_id: str,
                    treatment_id: str,
                    exp_column: str = 'exp_id'):
    """ perform ordinal and target encoding on df

    :param df:
    :param X_columns:
    :param y_column:
    :param control_id:
    :param treatment_id:
    :param exp_column:
    :return: X, y, t, nominal_encoder, target_encoder
    """
    df = df[df[exp_column].isin([control_id, treatment_id])].copy()
    y = df[y_column].fillna(0)

    # fill na in numerical columns with 0
    numerical_X = df[X_columns].select_dtypes(exclude='object').fillna(0)
    if len(numerical_X.columns) > 0:
        numerical_X_values = numerical_X.values
    else:
        numerical_X_values = None

    # take out categorical features
    categorical_X = df[X_columns].select_dtypes(include='object').astype(str).fillna('Unknown')

    # split cateogrical features into ordinal and nominal
    ordinal_features = [col for col in X_columns if col in ORDINAL_FEATURE_PREDEFIND_LIST]

    nominal_features = list(set(categorical_X.columns) - set(ordinal_features))

    ordinal_X = categorical_X[ordinal_features]
    nominal_X = categorical_X[nominal_features]

    # ordinal features - sort categories by order, then use ordinal encoding
    if len(ordinal_X.columns) > 0:
        ordinal_enc_alphabetical_sorted = OrdinalEncoder()
        ordinal_enc_alphabetical_sorted.fit(ordinal_X)

        # take out all categories and sort the categories using infer_categorical_level_order() 
        unsorted_ordinal_categories = ordinal_enc_alphabetical_sorted.categories_
        sorted_ordinal_categories = [CategoricalField.infer_categorical_level_order(category) for category in unsorted_ordinal_categories]

        ordinal_encoder = OrdinalEncoder(categories=sorted_ordinal_categories)
        ordinal_encoder.fit(ordinal_X)
        transformed_ordinal_X = ordinal_encoder.transform(ordinal_X)
    else:
        ordinal_encoder = OrdinalEncoder()
        transformed_ordinal_X = None

    # nominal features - use target encoding
    if len(nominal_X.columns) > 0:
        target_encoder = TargetEncoder(target_type='continuous', smooth=0)
        target_encoder.fit(nominal_X, y)
        transformed_nominal_X = target_encoder.transform(nominal_X)
    else:
        target_encoder = TargetEncoder()
        transformed_nominal_X = None

    # add numerical features back
    # only concatenate the components if they are not None
    components = [transformed_ordinal_X, transformed_nominal_X, numerical_X_values]
    X = np.concatenate([component for component in components if component is not None], axis=1)
    
    # for mapping feature names in tree diagram
    ordinal_encoder.numerical_feature_names_ = numerical_X.columns.tolist()
    target_encoder.numerical_feature_names_ = numerical_X.columns.tolist()

    
    t = (df[exp_column] == treatment_id).astype(int)
    return X, y.values, t.values, ordinal_encoder, target_encoder


# keep the original function for now to avoid breaking the existing downstream code
def target_encoding(df: pd.DataFrame,
                    X_columns: List[str],
                    y_column: str,
                    control_id: str,
                    treatment_id: str,
                    exp_column: str = 'exp_id'):
    """ perform target encoding on df
    :param df:
    :param X_columns:
    :param y_column:
    :param control_id:
    :param treatment_id:
    :param exp_column:
    :return: X, y, t, encoder
    """
    df = df[df[exp_column].isin([control_id, treatment_id])].copy()
    encoder = TargetEncoder(target_type='continuous', smooth=0)
    categorical_X = df[X_columns].select_dtypes(include='object').astype(str).fillna('Unknown')
    # fill na in numerical columns with 0
    numerical_X = df[X_columns].select_dtypes(exclude='object').fillna(0)

    # target encoding
    y = df[y_column].fillna(0)
    encoder.fit(categorical_X, y)
    transformed_categorical_X = encoder.transform(categorical_X)

    # add numerical features back
    X = np.concatenate([transformed_categorical_X, numerical_X.values], axis=1)
    encoder.numerical_feature_names_ = numerical_X.columns.tolist()

    t = (df[exp_column] == treatment_id).astype(int)
    return X, y.values, t.values, encoder

