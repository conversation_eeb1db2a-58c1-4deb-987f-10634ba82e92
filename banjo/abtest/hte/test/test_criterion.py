from banjo.abtest.hte.tree.criterion import TStatCriterion, TStatWithTreatmentCriterion

import numpy as np
from scipy.stats import ttest_ind
import unittest

class TestCriterion(unittest.TestCase):
    def test_criterion(self):
        y = np.random.rand(100)
        sample_indices = np.arange(0, 100)
        c = TStatCriterion(y=y, sample_indices=sample_indices)
        t_stat_sq = c.update(50)
        # compare with scipy
        result = ttest_ind(y[sample_indices[:50]], y[sample_indices[50:]], equal_var=False)
        self.assertAlmostEqual(t_stat_sq, result.statistic ** 2, msg=f"{t_stat_sq}, {result.statistic ** 2}")

        t_stat_sq = c.update(70)
        result = ttest_ind(y[sample_indices[:70]], y[sample_indices[70:]], equal_var=False)
        self.assertAlmostEqual(t_stat_sq, result.statistic ** 2, msg=f"{t_stat_sq}, {result.statistic ** 2}")

    def test_t_stat_with_treatment_criterion(self):
        y = np.random.rand(100)
        t = np.random.randint(0, 2, size=100)
        sample_indices = np.arange(0, 100)
        c = TStatWithTreatmentCriterion(y=y, is_treated=t, sample_indices=sample_indices)

        for index in [1, 50, 99]:
            c.update(index)
            self.assertEqual(c.sufficient_stats['left']['control']['n'], np.sum(t[sample_indices[:index]] == 0))
            self.assertAlmostEqual(
                c.sufficient_stats['left']['control']['sum'],
                np.sum(y[sample_indices[:index]][t[sample_indices[:index]] == 0])
            )
            self.assertAlmostEqual(
                c.sufficient_stats['right']['treatment']['sum'],
                np.sum(y[sample_indices[index:]][t[sample_indices[index:]] == 1])
            )
            self.assertAlmostEqual(
                c.sufficient_stats['right']['control']['sum'],
                np.sum(y[sample_indices[index:]][t[sample_indices[index:]] == 0])
            )






if __name__ == "__main__":
    unittest.main()