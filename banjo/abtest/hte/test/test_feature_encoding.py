from banjo.abtest.hte.preprocessing import feature_encoding

import numpy as np
import pandas as pd
import unittest


class TestFeatureEncoding(unittest.TestCase):
    def test_feature_encoding_1(self):
        df = pd.DataFrame (
            [['dog', 100, '4-8M', 0.13, '1'],
             ['dog', 101,  '1-2M', 0.15, '2'],
             ['cat', 102, '>16M', 0.16, '1'],
             ['dog', 103, '0-1M',0.20, '3']
            ],
            columns=['feature1', 'feature2', 'days_since_creation', 'metric_1', 'exp_id']
        )
        X, y, t, ordinal_encoder, target_encoder = feature_encoding(df,
                                        X_columns=['feature1', 'feature2', 'days_since_creation'],
                                        y_column='metric_1',
                                        control_id='1',
                                        treatment_id='2'
                                        )
        self.assertTrue(np.allclose(X, np.array([[1, 0.14, 100],
                                                 [0, 0.14, 101],
                                                 [2, 0.16, 102]
                                                ])
                                    ))
        self.assertTrue(np.allclose(y, np.array([0.13, 0.15, 0.16])))
        self.assertTrue(np.allclose(t, np.array([0, 1, 0])))
        self.assertEqual(target_encoder.numerical_feature_names_, ['feature2'])
        self.assertEqual(target_encoder.feature_names_in_, ['feature1'])
        self.assertEqual(ordinal_encoder.feature_names_in_, ['days_since_creation'])


if __name__ == "__main__":
    unittest.main()