from banjo.abtest.hte.tree.splitter import Splitter
from banjo.abtest.hte.tree.criterion import TStatCriterion

import numpy as np
import unittest

class TestSplitter(unittest.TestCase):
    def test_splitter(self):
        x = np.array([1, 1, 1, 2, 2, 2])
        y = np.array([0.3, 0.2, 1.3, 2.4, 3.5, 8.6])
        sample_indices = np.array([0, 5, 2, 4, 3, 1])
        splitter = Splitter(TStatCriterion)
        criterion_value, threshold, left_indices, right_indices = splitter.split(x, y, sample_indices)
        self.assertEqual(sorted(left_indices), [0, 1, 2])
        self.assertEqual(sorted(right_indices), [3, 4, 5])
        self.assertEqual(threshold, 2)

    def test_splitter_unique(self):
        x = np.array([1, 1, 1])
        y = np.array([0.1, 0.2, 0.5])
        sample_indices = np.array([0, 1, 2])
        splitter = Splitter(TStatCriterion)
        criterion_value, threshold, left_indices, right_indices = splitter.split(x, y, sample_indices)
        self.assertEqual(criterion_value, float('-inf'))

    def test_splitter_min_leaf(self):
        x = np.array([1, 1, 1, 2, 2, 2])
        y = np.array([0.1, 0.2, 0.3, 0.5, 0.7, 0.8])
        sample_indices = np.array([0, 1, 2, 3, 4, 5])
        splitter = Splitter(TStatCriterion, min_samples_leaf=4)
        criterion_value, threshold, left_indices, right_indices = splitter.split(x, y, sample_indices)
        self.assertEqual(criterion_value, float('-inf'))

        splitter = Splitter(TStatCriterion, min_samples_leaf=3)
        criterion_value, threshold, left_indices, right_indices = splitter.split(x, y, sample_indices)
        self.assertEqual(threshold, 2)



if __name__ == "__main__":
    unittest.main()