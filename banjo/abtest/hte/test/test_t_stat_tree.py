from banjo.abtest.hte.tree import SquaredTStatisticTree, SquaredTStatisticABTree

import numpy as np
import unittest


class TestTStatTree(unittest.TestCase):
    def test_t_stat_tree_1(self):
        X = np.array([
            [1, 1],
            [1, 2],
            [1, 3]
        ])
        y = np.array([0.1, 0.2, 0.3])
        sts_regressor = SquaredTStatisticTree(max_depth=2)
        sts_regressor.fit(X, y)

        # tree should only have one root node because TStat tree requires at least 4 data points to split a node
        self.assertEqual(sts_regressor.tree_.children_left, np.array([-1]))
        self.assertEqual(sts_regressor.tree_.children_right, np.array([-1]))
        self.assertEqual(sts_regressor.tree_.feature_spaces.tolist(), [[[float("-inf"), float("inf")] for _ in range(2)]])

    def test_t_stat_tree_2(self):
        X = np.array([
            [1, 1],
            [1, 2],
            [1, 3],
            [1, 4]
        ])
        y = np.array([1, 2, 3, 4])
        sts_regressor = SquaredTStatisticTree(max_depth=2)
        sts_regressor.fit(X, y)

        # tree should have 3 nodes, the root node splits the data by X[:, 1] < 3,
        # y=[1, 2] and y=[3, 4]: t-stat should be sqrt(8)
        self.assertEqual(sts_regressor.tree_.children_left.tolist(), [1, -1, -1])
        self.assertEqual(sts_regressor.tree_.children_right.tolist(), [2, -1, -1])
        self.assertEqual(sts_regressor.tree_.feature.tolist(), [1, -2, -2])
        self.assertEqual(sts_regressor.tree_.threshold.tolist(), [3, -2, -2])
        self.assertEqual(sts_regressor.tree_.value.tolist(), [2.5, 1.5, 3.5])
        self.assertTrue(np.allclose(sts_regressor.tree_.criterion_value, [8, -2, -2]))

    def test_t_stat_tree_3(self):
        X = np.array([
            [1, 1],
            [1, 2],
            [1, 3],
            [2, 4]
        ])
        y = np.array([1, 2, 3, 4])
        sts_regressor = SquaredTStatisticTree(max_depth=2)
        sts_regressor.fit(X, y)

        # tree should have 3 nodes, the root node splits the data by X[:, 1] < 3
        self.assertEqual(sts_regressor.tree_.children_left.tolist(), [1, -1, -1])
        self.assertEqual(sts_regressor.tree_.children_right.tolist(), [2, -1, -1])
        self.assertEqual(sts_regressor.tree_.feature.tolist(), [1, -2, -2])
        self.assertEqual(sts_regressor.tree_.threshold.tolist(), [3, -2, -2])


class TestTStatABTree(unittest.TestCase):
    def test_t_stat_ab_tree_1(self):
        X = np.array([
            [1, 1],
            [1, 2],
            [1, 3]
        ])
        y = np.array([0.1, 0.2, 0.3])
        t = np.array([0, 0, 1])
        sts_regressor = SquaredTStatisticABTree(max_depth=2)
        sts_regressor.fit(X, y, t)

        # tree should only have one root node because TStat tree requires at least 4 data points to split a node
        self.assertEqual(sts_regressor.tree_.children_left, np.array([-1]))
        self.assertEqual(sts_regressor.tree_.children_right, np.array([-1]))

    def test_t_stat_ab_tree_2(self):
        X = np.array([
            [1, 1],
            [1, 2],
            [1, 3],
            [2, 4]
        ])
        y = np.array([0.1, 0.2, 0.3, 0.4])
        t = np.array([0, 0, 1, 1])
        sts_regressor = SquaredTStatisticABTree(max_depth=2)
        sts_regressor.fit(X, y, t)

        # tree should only have one root node because there is no way to split a node
        # s.t. left and right both have control and treatment
        self.assertEqual(sts_regressor.tree_.children_left, np.array([-1]))
        self.assertEqual(sts_regressor.tree_.children_right, np.array([-1]))

    def test_t_stat_ab_tree_3(self):
        X = np.array([
            [1, 1],
            [1, 2],
            [2, 3],
            [1, 4],
            [2, 5],
            [1, 6],
            [2, 7],
            [2, 8]
        ])
        y = np.array([0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8])
        t = np.array([0, 1, 0, 1, 0, 1, 0, 1])
        sts_regressor = SquaredTStatisticABTree(max_depth=2)
        sts_regressor.fit(X, y, t)

        # tree should only have one root node because there is no way to split a node
        # s.t. left and right both have control and treatment
        self.assertEqual(sts_regressor.tree_.feature.tolist(), [1, -2, -2])
        self.assertEqual(sts_regressor.tree_.threshold.tolist(), [5, -2, -2])
        self.assertEqual(sts_regressor.tree_.children_left.tolist(), [1, -1, -1])
        self.assertEqual(sts_regressor.tree_.children_right.tolist(), [2, -1, -1])

        sts_regressor = SquaredTStatisticABTree(max_depth=2, min_samples_leaf=5)
        sts_regressor.fit(X, y, t)
        self.assertEqual(sts_regressor.tree_.feature.tolist(), [-2])


if __name__ == "__main__":
    unittest.main()
