# Squared T Statistic Tree

[doc](https://docs.google.com/document/d/1hAatct5V-xjfe0s0h-JOksb43WmYOGlTqfLyjDPbxTs/edit#heading=h.pp2rdou18g42)

Example usage:
```python
from banjo.abtest.hte.tree import SquaredTStatisticTree, SquaredTStatisticABTree

tree = SquaredTStatisticTree(max_depth=2, min_samples_leaf=100)
tree.fit(X, y)

tree = SquaredTStatisticABTree(max_depth=2, min_samples_leaf=100)
tree.fit(X, y, t)
```

More examples can be found in [colab notebook](https://colab.research.google.com/drive/1nRzKbVDctWo2A8vcc2Xo9QVKNzgi_scz#scrollTo=16c32719).