from abc import ABC, abstractmethod
import numpy as np
from banjo.abtest.stat_funcs import calc_diff_population_means_large_independent_samples

class CriterionBase(ABC):
    """Base class for Criterion. Shouldn't use on its own.

    All subclasses inherited from this class should follow the rule: larger criterion means better
    """

    def __init__(self,
                 y: np.array,
                 sample_indices: np.array):
        """
        criterion to split a decision tree node with values y[sample_indices]
        left child has indices: sample_indices[0: self.pos]
        right child has indices: sample_indices[self.pos: -1]

        :param y:
        :param sample_indices:
        """
        self.y = y
        self.sample_indices = sample_indices
        self.N = len(sample_indices)
        self.pos = 0

    def reset(self):
        self.pos = 0

    @abstractmethod
    def update(self, new_pos):
        """
        move sample_indices[self.pos: new_pos] from right child to left child
        and update self.pos to new_pos

        :param new_pos:
        :return:
        """
        raise NotImplementedError("Subclass must implement this method.")

    @staticmethod
    def _calc_sufficient_stats(y):
        return {'n': len(y), 'sum': np.sum(y), 'sumsq': np.sum(np.square(y))}

    @staticmethod
    def _calc_variance_from_sufficient_stats(n, s, ssq):
        mean = 1.0 * s / n
        return (ssq - mean ** 2 * n) / (n - 1)


class TStatCriterion(CriterionBase):
    """Criterion based on T-stat between left child node and right child node
    https://arxiv.org/abs/1504.01132

    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.reset()

    def reset(self):
        super().reset()
        # sufficient stats: n, sum, sum square
        self.sufficient_stats = {
            'left': self._calc_sufficient_stats([]),
            'right': self._calc_sufficient_stats(self.y[self.sample_indices])
        }

    def compute_t_stat_from_sufficient_stats(self):
        left_n = self.sufficient_stats['left']['n']
        right_n = self.sufficient_stats['right']['n']
        if left_n == 0 or right_n == 0:
            return np.nan
        if left_n == 1 or right_n == 1:
            return float('-inf')

        left_mean = self.sufficient_stats['left']['sum'] / left_n
        right_mean = self.sufficient_stats['right']['sum'] / right_n

        # assume unequal variances
        left_variance = (self.sufficient_stats['left']['sumsq'] - left_mean ** 2 * left_n) / (left_n - 1)
        right_variance = (self.sufficient_stats['right']['sumsq'] - right_mean ** 2 * right_n) / (right_n - 1)

        t_stat_sq = (left_mean - right_mean) ** 2 / (left_variance / left_n + right_variance / right_n)
        return t_stat_sq

    def update(self, new_pos):
        """
        data slice self.sample_indices[self.pos:new_pos] is shifted from right node to left node
        :param new_pos:
        :return:
        """
        if new_pos <= self.pos:
            raise ValueError(f"new position: {new_pos} cannot be less than current position: {self.pos}")

        delta_n = new_pos - self.pos
        delta_sum = np.sum(self.y[self.sample_indices[self.pos:new_pos]])
        delta_sumsq = np.sum(np.square(self.y[self.sample_indices[self.pos:new_pos]]))

        self.sufficient_stats['left']['n'] += delta_n
        self.sufficient_stats['right']['n'] -= delta_n
        self.sufficient_stats['left']['sum'] += delta_sum
        self.sufficient_stats['right']['sum'] -= delta_sum
        self.sufficient_stats['left']['sumsq'] += delta_sumsq
        self.sufficient_stats['right']['sumsq'] -= delta_sumsq

        self.pos = new_pos

        return self.compute_t_stat_from_sufficient_stats()


class TStatWithTreatmentCriterion(CriterionBase):
    """Criterion based on T-stat between treatment effect on the left child node and right child node

    delta_mean = (left_t_mean - left_c_mean) - (right_t_mean - right_c_mean),
    delta_se = np.sqrt(left_c_variance / left_c_n + left_t_variance / left_t_n +
                       right_c_variance / right_c_n + right_t_variance / left_t_n
    )

    Squared t-stat = (delta_mean / delta_se) ** 2

    If we pool variance:
    pooled_var = (left_c_variance * (left_c_n - 1) +
                left_t_variance * (left_t_n - 1) +
                right_c_variance * (right_c_n - 1) +
                right_t_variance * (right_t_n - 1)) / (N - 4)
    then:
    delta_se =  pooled_var * (1 / left_c_n + 1 / left_t_n + 1 / right_c_n + 1 / right_t_n)

    """

    def __init__(self, y, is_treated, sample_indices, pool_var=False):
        super().__init__(y, sample_indices)
        self.is_treated = is_treated
        self.pool_var = pool_var
        self.reset()

    def _calc_sufficient_stats_treatment(self, y, is_treated):
        control = self._calc_sufficient_stats(y[is_treated == 0])
        treatment = self._calc_sufficient_stats(y[is_treated == 1])
        return {'control': control, 'treatment': treatment}

    def reset(self):
        super().reset()
        # sufficient stats: n, sum, sum square
        self.sufficient_stats = {
            'left': self._calc_sufficient_stats_treatment(y=np.array([]), is_treated=np.array([])),
            'right': self._calc_sufficient_stats_treatment(y=self.y[self.sample_indices],
                                                           is_treated=self.is_treated[self.sample_indices]),
        }

    def compute_t_stat_from_sufficient_stats(self):
        left_c_n = self.sufficient_stats['left']['control']['n']
        left_t_n = self.sufficient_stats['left']['treatment']['n']
        right_c_n = self.sufficient_stats['right']['control']['n']
        right_t_n = self.sufficient_stats['right']['treatment']['n']
        if left_c_n == 0 or left_t_n == 0 or right_c_n == 0 or right_t_n == 0:
            return np.nan
        if left_c_n == 1 or left_t_n == 1 or right_c_n == 1 or right_t_n == 1:
            return float("-inf")

        left_c_mean = self.sufficient_stats['left']['control']['sum'] / left_c_n
        left_t_mean = self.sufficient_stats['left']['treatment']['sum'] / left_t_n
        right_c_mean = self.sufficient_stats['right']['control']['sum'] / right_c_n
        right_t_mean = self.sufficient_stats['right']['treatment']['sum'] / right_t_n

        # assume unequal variances
        left_c_variance = (self.sufficient_stats['left']['control']['sumsq'] - left_c_mean ** 2 * left_c_n) \
                          / (left_c_n - 1)
        left_t_variance = (self.sufficient_stats['left']['treatment']['sumsq'] - left_t_mean ** 2 * left_t_n) \
                          / (left_t_n - 1)
        right_c_variance = (self.sufficient_stats['right']['control']['sumsq'] - right_c_mean ** 2 * right_c_n) \
                           / (right_c_n - 1)
        right_t_variance = (self.sufficient_stats['right']['treatment']['sumsq'] - right_t_mean ** 2 * right_t_n) \
                           / (right_t_n - 1)

        delta_mean = (left_t_mean - left_c_mean) - (right_t_mean - right_c_mean)
        if self.pool_var:
            pooled_variance = (left_c_variance * (left_c_n - 1) +
                               left_t_variance * (left_t_n - 1) +
                               right_c_variance * (right_c_n - 1) +
                               right_t_variance * (right_t_n - 1)) / (left_c_n + left_t_n + right_c_n + right_t_n - 4)
            delta_variance = pooled_variance * (1 / left_c_n + 1 / left_t_n + 1 / right_c_n + 1 / right_t_n)
        else:
            delta_variance = (left_c_variance / left_c_n + left_t_variance / left_t_n +
                              right_c_variance / right_c_n + right_t_variance / left_t_n
                              )

        t_stat_sq = delta_mean ** 2 / delta_variance

        return t_stat_sq

    def update(self, new_pos):
        """
        data slice self.sample_indices[self.pos:new_pos] is shifted from right node to left node
        :param new_pos:
        :return:
        """
        if new_pos <= self.pos:
            raise ValueError(f"new position: {new_pos} cannot be less than current position: {self.pos}")

        delta_sufficient_stats = self._calc_sufficient_stats_treatment(
            self.y[self.sample_indices[self.pos:new_pos]],
            self.is_treated[self.sample_indices[self.pos:new_pos]]
        )

        for group in ['control', 'treatment']:
            for attribute in ['n', 'sum', 'sumsq']:
                self.sufficient_stats['left'][group][attribute] += delta_sufficient_stats[group][attribute]
                self.sufficient_stats['right'][group][attribute] -= delta_sufficient_stats[group][attribute]

        self.pos = new_pos

        return self.compute_t_stat_from_sufficient_stats()


class PooledTStatWithTreatmentCriterion(TStatWithTreatmentCriterion):
    def __init__(self, y, is_treated, sample_indices):
        super().__init__(y, is_treated, sample_indices, pool_var=True)

class RelativeChangeTStatWithTreatmentCriterion(TStatWithTreatmentCriterion):
    def __init__(self, y, is_treated, sample_indices):
        super().__init__(y, is_treated, sample_indices)

    def compute_t_stat_from_sufficient_stats(self):
        left_c_n = self.sufficient_stats['left']['control']['n']
        left_t_n = self.sufficient_stats['left']['treatment']['n']
        right_c_n = self.sufficient_stats['right']['control']['n']
        right_t_n = self.sufficient_stats['right']['treatment']['n']
        if left_c_n == 0 or left_t_n == 0 or right_c_n == 0 or right_t_n == 0:
            return np.nan
        if left_c_n == 1 or left_t_n == 1 or right_c_n == 1 or right_t_n == 1:
            return float("-inf")

        left_stats = calc_diff_population_means_large_independent_samples(
            c_n=left_c_n, t_n=left_t_n,
            c_sum=self.sufficient_stats['left']['control']['sum'],
            t_sum=self.sufficient_stats['left']['treatment']['sum'],
            c_sums=self.sufficient_stats['left']['control']['sumsq'],
            t_sums=self.sufficient_stats['left']['treatment']['sumsq']
        )
        right_stats = calc_diff_population_means_large_independent_samples(
            c_n=right_c_n, t_n=right_t_n,
            c_sum=self.sufficient_stats['right']['control']['sum'],
            t_sum=self.sufficient_stats['right']['treatment']['sum'],
            c_sums=self.sufficient_stats['right']['control']['sumsq'],
            t_sums=self.sufficient_stats['right']['treatment']['sumsq']
        )
        left_error = (left_stats['DIFFERENCE_ERROR_MARGIN'][1] - left_stats['DIFFERENCE_ERROR_MARGIN'][0]) / 2 / 1.96
        right_error = (right_stats['DIFFERENCE_ERROR_MARGIN'][1] - right_stats['DIFFERENCE_ERROR_MARGIN'][0]) / 2 / 1.96
        t_stat_sq = (left_stats['DIFFERENCE'] - right_stats['DIFFERENCE']) ** 2 / (left_error ** 2 + right_error ** 2)
        return t_stat_sq