import numpy as np

MAX_UNIQUE_SPLITS = 50 # aka max_bin, maximum number of unique splits for a single feature in a tree

class Splitter(object):
    def __init__(self, criterion, min_samples_split=None, min_samples_leaf=None):
        self.criterion = criterion
        if min_samples_split is None:
            self.min_samples_split = 4
        else:
            self.min_samples_split = max(min_samples_split, 4)

        if min_samples_leaf is None:
            self.min_samples_leaf = 1
        else:
            self.min_samples_leaf = max(min_samples_leaf, 1)

    def split(self, x, y, sample_indices, **kwargs):
        """
        find the best split threshold in x to maximize self.criterion
        :param x:
        :param y:
        :param sample_indices:
        :return: criterion_value, threshold, left_indices, right_indices
        """
        _, counts = np.unique(x[sample_indices], return_counts=True)
        # cannot split if there is only one unique value in feature x[sample_indices]
        if len(counts) == 1:
            return float("-inf"), None, None, None
        # reorder sample_indices according to x[i]
        reordered_indices = np.array(sorted(sample_indices, key=lambda i: x[i]))
        position = np.array([x for x in np.cumsum(counts)
                             if self.min_samples_leaf <= x <= len(sample_indices) - self.min_samples_leaf
                             ])
        # cannot find split such that there are at least min_samples_leaf
        # training samples in each of the left and right branches
        if len(position) == 0:
            return float("-inf"), None, None, None
        if len(position) > MAX_UNIQUE_SPLITS:
            position = position[sorted(np.random.choice(np.arange(len(position)), size=MAX_UNIQUE_SPLITS, replace=False))]

        if 'is_treated' in kwargs.keys():
            is_treated = kwargs.pop('is_treated')
            criterion = self.criterion(y, is_treated, reordered_indices)
        else:
            criterion = self.criterion(y, reordered_indices)

        max_criterion_value = float("-inf")
        split_pos = None
        for pos in position:
            criterion_value = criterion.update(pos)
            if criterion_value > max_criterion_value:
                max_criterion_value = criterion_value
                split_pos = pos
        return max_criterion_value, x[reordered_indices[split_pos]], reordered_indices[:split_pos], reordered_indices[split_pos:]


