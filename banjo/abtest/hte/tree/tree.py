from banjo.abtest.hte.tree.criterion import (
    TStatCriterion, TStatWithTreatmentCriterion,
    PooledTStatWithTreatmentCriterion,
    RelativeChangeTStatWithTreatmentCriterion
)
from banjo.abtest.hte.tree.splitter import Splitter
from banjo.abtest.hte.tree.tree_node import Tree

from abc import ABC, abstractmethod
import numpy as np
import copy


class DecisionTreeBase(ABC):
    @abstractmethod
    def __init__(self,
                 criterion,
                 max_depth=None,
                 min_samples_split=None,
                 min_samples_leaf=None,
                 ):
        self.criterion = criterion
        self.max_depth = max_depth
        self.min_samples_split = min_samples_split
        self.min_samples_leaf = min_samples_leaf

    def _fit(self, X, y, **kwargs):
        # check input
        if self.max_depth is None:
            max_depth = 10
        else:
            max_depth = self.max_depth

        splitter = Splitter(self.criterion,
                            min_samples_split=self.min_samples_split,
                            min_samples_leaf=self.min_samples_leaf)
        self.tree_ = Tree(max_depth=max_depth)
        self.tree_.build(X, y, splitter=splitter, **kwargs)

        return self
    
    def _evaluate(self, X_eval, y_eval, **kwargs):
        tree_eval = copy.deepcopy(self)
        tree_eval.tree_.evaluate(X_eval, y_eval, **kwargs)
        return tree_eval


class SquaredTStatisticTree(DecisionTreeBase):
    def __init__(self,
                 max_depth=None,
                 min_samples_split=None,
                 min_samples_leaf=None,
                 ):
        super().__init__(
            criterion=TStatCriterion,
            max_depth=max_depth,
            min_samples_split=min_samples_split,
            min_samples_leaf=min_samples_leaf
        )

    def fit(self, X, y):
        super()._fit(X, y)
        return self
    
    def evaluate(self, X_eval, y_eval):
        tree_eval = super()._evaluate(X_eval, y_eval)
        return tree_eval


class SquaredTStatisticABTree(DecisionTreeBase):
    def __init__(self,
                 max_depth=None,
                 min_samples_split=None,
                 min_samples_leaf=None,
                 pooled_var=False,
                 use_ratio=False
                 ):
        if pooled_var:
            criterion = PooledTStatWithTreatmentCriterion
        elif use_ratio:
            criterion = RelativeChangeTStatWithTreatmentCriterion
        else:
            criterion = TStatWithTreatmentCriterion
        super().__init__(
            criterion=criterion,
            max_depth=max_depth,
            min_samples_split=min_samples_split,
            min_samples_leaf=min_samples_leaf
        )

    def fit(self, X, y, is_treated):
        super()._fit(X, y, is_treated=is_treated)
        return self
    
    def evaluate(self, X_eval, y_eval, is_treated):
        tree_eval = super()._evaluate(X_eval, y_eval, is_treated=is_treated)
        return tree_eval
