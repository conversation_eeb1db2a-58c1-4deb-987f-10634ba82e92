from banjo.abtest.stat_funcs import calc_diff_population_means_large_independent_samples

import copy
import numpy as np


class Tree(object):
    """
    Tree, represented in numpy.array
    """
    def __init__(self, max_depth):
        self.max_depth = max_depth
        self._reset()

    def build(self, X, y, splitter, is_treated=None):
        """
        build tree in a DFS way

        :param X:
        :param y:
        :param splitter:
        :param is_treated: treatment indicator
        :return:
        """
        self.has_treatment = (is_treated is not None)
        self._reset()
        self._grow(X, y, is_treated=is_treated,
                   sample_indices=np.arange(len(y)),
                   level=1,
                   splitter=splitter,
                   feature_space=self.get_root_feature_space(X)
                   )
        self._convert_to_numpy()

    def get_root_feature_space(self, X):
        return [[float("-inf"), float("inf")] for _ in range(X.shape[1])]


    def _grow(self, X, y, is_treated, sample_indices, level, splitter, feature_space):
        
        current_node_index = len(self.children_right)        
        # check stop conditions
        at_depth = level >= self.max_depth
        min_sample = len(sample_indices) < splitter.min_samples_split
        one_cls = np.unique(y[sample_indices]).shape[0] == 1
        # write node attributes
        if is_treated is None:
            # y_mean
            self._value.append(self._calc_node_value(y[sample_indices]))
            # number of samples in current node
            self.n_node_samples.append(len(sample_indices))
        else:
            self._value.append(self._calc_node_value(y[sample_indices], is_treated[sample_indices]))
            # append control count and treatment count
            self.n_node_samples.append((np.sum(is_treated[sample_indices] == 0), np.sum(is_treated[sample_indices] == 1)))
        self.feature_spaces.append(feature_space)

        # split node
        if at_depth or min_sample or one_cls:   # stop, we are in a leaf node
            self.children_left.append(-1)
            self.children_right.append(-1)
            self.feature.append(-2)
            self.threshold.append(-2)
            self.criterion_value.append(-2)
        else:                                   # we need to split the data
            n_features = X.shape[1]
            best_split_criterion = float("-inf")
            best_split_feature = None
            best_split_threshold = None
            best_left_indices = None
            best_right_indices = None

            # iterate through all features
            for i in range(n_features):
                kwargs = {}
                if self.has_treatment:
                    kwargs['is_treated'] = is_treated
                criterion_value, threshold, left_indices, right_indices = splitter.split(X[:, i], y, sample_indices, **kwargs)
                if criterion_value > best_split_criterion:
                    best_split_criterion = criterion_value
                    best_split_threshold = threshold
                    best_split_feature = i
                    best_left_indices = left_indices
                    best_right_indices = right_indices

            if best_split_feature is not None:
                # successfully find the best feature and threshold to split
                self.feature.append(best_split_feature)
                self.threshold.append(best_split_threshold)
                self.children_left.append(None)     # placeholder
                self.children_right.append(None)    # placeholder
                self.criterion_value.append(best_split_criterion)
                # DFS
                left_feature_space = copy.deepcopy(feature_space)
                left_feature_space[best_split_feature][1] = best_split_threshold
                self.children_left[current_node_index] = self._grow(X, y, is_treated,
                                                                    best_left_indices,
                                                                    level + 1,
                                                                    splitter,
                                                                    left_feature_space
                                                                    )
                right_feature_space = copy.deepcopy(feature_space)
                right_feature_space[best_split_feature][0] = best_split_threshold
                self.children_right[current_node_index] = self._grow(X, y, is_treated,
                                                                     best_right_indices,
                                                                     level + 1,
                                                                     splitter,
                                                                     right_feature_space
                                                                     )
            else:
                # cannot find a feature to split the data. Stop and make it a leaf
                self.children_left.append(-1)
                self.children_right.append(-1)
                self.feature.append(-2)
                self.threshold.append(-2)
                self.criterion_value.append(-2)

        return current_node_index

    def evaluate(self, X_eval, y_eval, is_treated=None, node_index=0):
        """
        Implement honest tree from Recursive Partitioning for Heterogeneous Causal Effects
        paper link: https://arxiv.org/abs/1504.01132
        
        evaluate the tree on test data 

        :param X_eval:
        :param y_eval:
        :param is_treated: t_eval if it is an AB Tree
        :return: 
        """
        current_node_index = node_index

        if is_treated is None:
            self._value[current_node_index] = self._calc_node_value(y_eval)
            self.n_node_samples[current_node_index] = len(y_eval)

        else:
            self._value[current_node_index] = self._calc_node_value(y_eval, is_treated)       
            self.n_node_samples[current_node_index] = (np.sum(is_treated == 0), 
                                                       np.sum(is_treated == 1))
        
        # check if current node is a leaf node as we stored feature as -2 when can't find feature to split, stop
        if self.feature[current_node_index] != -2:
        
            if (self.children_left[current_node_index] != -1):
                left_filter = X_eval[:, self.feature[current_node_index]] < self.threshold[current_node_index]
                self.evaluate(X_eval[left_filter], 
                              y_eval[left_filter], 
                              is_treated=is_treated[left_filter], 
                              node_index=self.children_left[current_node_index]
                )
                
            if (self.children_right[current_node_index] != -1):
                right_filter = X_eval[:, self.feature[current_node_index]] >= self.threshold[current_node_index]
                self.evaluate(X_eval[right_filter], 
                              y_eval[right_filter], 
                              is_treated=is_treated[right_filter], 
                              node_index=self.children_right[current_node_index])   
        return current_node_index

    
    def _calc_node_value(self, y, is_treated=None):
        if is_treated is None:
            return np.mean(y)
        else:
            # calculate stats based on y and t in the node
            statistics = calc_diff_population_means_large_independent_samples(
                c_n=np.sum(is_treated == 0),
                c_sum=np.sum(y[is_treated == 0]),
                c_sums=np.sum(np.square(y[is_treated == 0])),
                t_n=np.sum(is_treated == 1),
                t_sum=np.sum(y[is_treated == 1]),
                t_sums=np.sum(np.square(y[is_treated == 1]))
            )
            return (statistics['DIFFERENCE'], np.mean(y[is_treated == 0]), np.mean(y[is_treated == 1]), statistics['P_VALUE'])


    def _reset(self):
        self.children_left = []
        self.children_right = []
        self.feature = []
        self.threshold = []
        self._value = []
        self.criterion_value = []
        self.n_node_samples = []
        self.feature_spaces = []

    def _convert_to_numpy(self):
        self.children_left = np.array(self.children_left)
        self.children_right = np.array(self.children_right)
        self.feature = np.array(self.feature)
        self.threshold = np.array(self.threshold)
        self._value = np.array(self._value)
        self.criterion_value = np.array(self.criterion_value)
        self.n_node_samples = np.array(self.n_node_samples)
        self.feature_spaces = np.array(self.feature_spaces)

    @property
    def value(self):
        """add this attribute to be compatible with sklearn
        """
        return self._value

    @property
    def n_outputs(self):
        """add this attribute to be compatible with sklearn
        """
        return 2

    @property
    def n_classes(self):
        """add this attribute to be compatible with sklearn
        """
        return [1]