# Individual Treatment Effect Estimator
This module estimates ITE from AB data. Design doc [here](https://docs.google.com/document/d/1ZJ397Mu3XBGIX9iY1I39-ISw139_yR4EmjQZiWSspFc/edit#heading=h.a12g2ajn856). 
The model can also be used for non-AB data in a general causal inference analysis.
## Installation

1. Create a virtual env and install `banjo` from this [guide](https://github.sc-corp.net/Snapchat/pyanalytics).
2. In `pyanalytics` directory, use ```pip install -e ".[ITE]"``` to install required packages for ITE.

## Data Preparation
Prepare an input config yaml file in `config` directory, it needs to have the following fields:
```yaml
study_name: 
study_start_date: 
study_end_date: 
control_id: 
treatment_id: 
metric: 
user_breakdown_list:
  -
```
Inputs in `user_breakdown_list` will be used as features to learn ITE.

To submit data preparation job, use command
```bash
./banjo/abtest/ite/bash_scripts/data_preparation_job.sh -c=your_config_name
```
Then your training data should be ready in bigquery `vellum-dev.ITE_data` dataset.

## Training Job
We currently support S-learner, TARNet, and dragonnet.
### Local Train
<!---
Set gcloud local python path to the current virtual env python path.
```bash
gcloud config set ml_engine/local_python $(which python)
```
```bash
gcloud ai-platform local train \
--module-name=banjo.abtest.ite.toy_job.task \
--package-path=banjo \
--job-dir=./banjo/abtest/ite/toy_job/local-training-output
```
--->
```bash
python -m banjo.abtest.ite.<learner_module>.task \
 --config-name=creator_ab_1 \
 --job-dir=banjo/abtest/ite/<learner_module>/local-training-output
```
replace `<learner_module>` with your choice of `s_learner` or `dragonnet`.

#### Trouble Shoot
If you encounter `Empty update [Op:IO>BigQueryReadSession]` error, config gRPC environment [here](https://github.com/googleapis/google-cloud-cpp/blob/main/google/cloud/bigtable/examples/README.md#configure-environment).

### Submit Cloud Training Job
We use a service account to submit training jobs to ai-platform. 
Please request a lease for `iam.serviceAccountUser`role [here](https://lease.sc-corp.net/v2/request_access/gcp_resources/service_account?parentResource=vellum-dev&resource=<EMAIL>&permanentAccess=false&roles=[roles/iam.serviceAccountUser]).
```bash
./banjo/abtest/ite/bash_scripts/submit_training_job.sh \
-n=s_learner \
--config_name=creator_ab_1
```