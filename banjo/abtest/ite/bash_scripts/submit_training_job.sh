#!/bin/bash

for i in "$@"
do
case $i in
    -n=*|--job_name=*)
    RAW_JOB_NAME="${i#*=}"
    ;;
    -m=*|--machine_type=*)
    MASTER_MACHINE_TYPE="${i#*=}"
    ;;
    -c=*|--config_name=*)
    CONFIG_NAME="${i#*=}"
    ;;
esac
done

# confirm that the required APIs are enabled to use AI Platform
#gcloud services enable ml.googleapis.com
#gcloud services enable compute.googleapis.com

PROJECT_ID="vellum-dev"
JOB_NAME="${RAW_JOB_NAME}_$(date +%Y%m%d_%H%M%S)"
MODEL_BUCKET_NAME="dsa-staging/ITE/${JOB_NAME}_job_dir"

if [[ ${MASTER_MACHINE_TYPE} = '' ]]
then
MASTER_MACHINE_TYPE="n1-standard-8"
fi


echo "Submitting job ${JOB_NAME} to gcloud AI platform..."
echo

CMD="gcloud --project=${PROJECT_ID} ai-platform jobs submit training $JOB_NAME \
  --job-dir gs://$MODEL_BUCKET_NAME \
  --package-path banjo \
  --module-name banjo.abtest.ite.${RAW_JOB_NAME}.task \
  --region us-central1 \
  --runtime-version=2.11 \
  --python-version=3.7 \
  --scale-tier CUSTOM \
  --master-machine-type=$MASTER_MACHINE_TYPE \
  --service-account=<EMAIL>
  --stream-logs \
  --
  --config-name=$CONFIG_NAME"

echo ${CMD}
echo "View job in the Cloud Console at:
https://console.cloud.google.com/mlengine/jobs/${JOB_NAME}?project=${PROJECT_ID}"
eval $CMD