from banjo.abtest.ite.constant import ITE_PROJECT, ITE_DATASET, TREATMENT_COLUMN_NAME, BQ_CLIENT_PROJECT
from banjo.abtest.ite.common.utils import load_yaml, get_training_table_name
from banjo.utils.gbq import (
    read_gbq,
)

from google.cloud import bigquery
from tensorflow.python.framework import dtypes
from tensorflow_io.bigquery import BigQueryClient


class DataLoader(object):
    """Load data from bigquery into tensorflow

    """

    def __init__(self, config_name):
        self.client = bigquery.Client(BQ_CLIENT_PROJECT)
        self.config = load_yaml(config_name)
        self.ITE_table_name = get_training_table_name(self.config['study_name'],
                                                      self.config['study_start_date'],
                                                      self.config['study_end_date'],
                                                      self.config['control_id'],
                                                      self.config['treatment_id'],
                                                      self.config['metric'],
                                                      )

    def read_bigquery(self):
        # get schema of input data
        sql = f"""
            SELECT 
                column_name,
                data_type
            FROM `{ITE_PROJECT}.{ITE_DATASET}.INFORMATION_SCHEMA.COLUMNS`
            WHERE table_name = "{self.ITE_table_name}"
        """
        schema_df = read_gbq(sql, client=self.client, dialect='standard')
        non_feature_columns = ['ghost_user_id', TREATMENT_COLUMN_NAME, self.config['metric']]
        categorical_features = []
        numerical_features = []
        for feature, data_type in zip(schema_df['column_name'], schema_df['data_type']):
            if feature in non_feature_columns:
                continue

            if data_type == "STRING":
                categorical_features.append(feature)
            else:
                numerical_features.append(feature)

        # get vocabulary of categorical features
        vocab_clause = [f'ARRAY_AGG(distinct {feature} IGNORE NULLS) AS {feature}_vocab'
                        for feature in categorical_features
                        ]
        vocab_sql = f"""
            SELECT
                {', '.join(vocab_clause)}
            FROM `{ITE_PROJECT}.{ITE_DATASET}.{self.ITE_table_name}`
        """
        vocab_df = read_gbq(vocab_sql, client=self.client, dialect='standard')

        # categorical features needs to have at least two unique values
        categorical_features = list(filter(
            lambda x: len(vocab_df[x + "_vocab"].iloc[0]) > 1, categorical_features
        ))
        # read bigquery into tensorflow dataset
        selected_fields = non_feature_columns + categorical_features + numerical_features
        output_types = (
            [dtypes.string, dtypes.int64, dtypes.double]
            + [dtypes.string] * len(categorical_features)
            + [dtypes.double] * len(numerical_features)
        )
        tensorflow_io_bigquery_client = BigQueryClient()
        read_session = tensorflow_io_bigquery_client.read_session(
            "projects/" + ITE_PROJECT,
            ITE_PROJECT, self.ITE_table_name, ITE_DATASET,
            selected_fields,
            output_types,
            requested_streams=2
        )

        dataset = read_session.parallel_read_rows()
        return dataset, vocab_df, categorical_features, numerical_features, self.config['metric']


if __name__ == "__main__":
    dl = DataLoader("creator_ab_1")
    dataset, vocab_df, categorical_features, numerical_features, non_feature_columns = dl.read_bigquery()
    print(dataset)
    import pandas as pd
    df = pd.DataFrame(
        {
            'ghost_user_id': list(x.decode() for x in dataset.map(lambda x: x['ghost_user_id']).as_numpy_iterator())
        }
    )
    print(df.head())
    #print(list(dataset.map(lambda x: x['ghost_user_id']).as_numpy_iterator()))
