import os
import yaml


def load_yaml(file_name: str):
    cur_path = os.path.dirname(os.path.abspath(__file__))
    yaml_dir = os.path.join(cur_path, "../config")
    full_config_name = os.path.join(yaml_dir, f'{file_name}.yaml')
    with open(full_config_name, "r") as stream:
        data = yaml.safe_load(stream)
    return data


def get_training_table_name(
        study_name: str,
        study_start_date: str,
        study_end_date: str,
        control_id: str,
        treatment_id: str,
        metric: str
):
    return "{}_{}_{}_{}_{}_{}".format(
        study_name,
        control_id,
        treatment_id,
        metric,
        study_start_date,
        study_end_date,
    )

def get_prediction_table_name(
        model_name: str,
        study_name: str,
        study_start_date: str,
        study_end_date: str,
        control_id: str,
        treatment_id: str,
        metric: str
):
    return "{}_{}_{}_{}_{}_{}_{}".format(
        model_name,
        study_name,
        control_id,
        treatment_id,
        metric,
        study_start_date,
        study_end_date,
    )