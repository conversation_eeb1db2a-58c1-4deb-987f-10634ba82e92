from banjo import abtest
from banjo.abtest.cohort_report import <PERSON>hortReport
from banjo.abtest.ite.constant import ITE_PROJECT, ITE_DATASET, TREATMENT_COLUMN_NAME, BQ_CLIENT_PROJECT
from banjo.abtest.ite.common.job import JobBase
from banjo.abtest.ite.common.utils import load_yaml, get_training_table_name

from banjo.utils.gbq import (
    submit_sync_query,
)

import argparse
import logging
logging.basicConfig(level=logging.INFO)

from google.cloud import bigquery


class DataPrepJob(JobBase):
    """Data preparation job for ITE
    This class uses banjo.abtest and bigquery to prepare training data
    """
    def __init__(self, config_name: str):
        super().__init__()
        self.read_config(config_name)

        ab_console_metrics_catalog = abtest.ABQuestMetricCatalog(self.config['study_start_date'],
                                                                 self.config['study_end_date'],
                                                                 aa_start_date=None,
                                                                 aa_end_date=None)
        ab_console_metric_tables = ab_console_metrics_catalog.get_filtered_metric_tables(
            metric_ab_major_minors=[self.config['metric']],
            include_core_metrics=False,
        )
        self.major_minor_to_metric_name_mapping = ab_console_metrics_catalog.get_metadata_mapping(
            from_column="major_minor", to_column="metric_name"
        )
        self.client = bigquery.Client(BQ_CLIENT_PROJECT)
        self.report = CohortReport(
            study_name=self.config['study_name'],
            study_start_date=self.config['study_start_date'],
            study_end_date=self.config['study_end_date'],
            metric_tables=ab_console_metric_tables,
            overwrite_mapping_table=True,
            control_id=self.config['control_id'],
            treatment_ids=[self.config['treatment_id']],
            bq_project='sc-bq-gcs-billingonly',
            bq_client=self.client,
            dest_dataset='temp_abtest',
            user_group_bys=self.config['user_breakdown_list'],
            cohort_definition_date=None,
            materializing_mapping_table=True,
            exp_id_to_name=None,
            excel_output=True,
            bq_priority="BATCH",
            quantiles=['50', '90'],
            aa_start_date=None,
            aa_end_date=None
        )
        self.logger.info(f"user mapping table: {self.report.get_mapping_sql('standard')}")

    def read_config(self, config_name):
        self.config = load_yaml(config_name)
        self.logger.info(self.config)

    def agg(self, metric_table_name, destination_table):
        """
        Aggregate data from user-day level into user level
        :param metric_table_name: metric table name from cohort report
        :param destination_table:
        :return:
        """
        user_breakdown_rollup = "\n".join(
            [f"MAX({dimension}) AS {dimension}," for dimension in self.config['user_breakdown_list']]
        )
        # convert metric name into MAJOR____minor format
        metric_name = self.major_minor_to_metric_name_mapping[self.config['metric']]
        sql = f"""
            SELECT 
                ghost_user_id,
                IF(exp_id = '{self.config['treatment_id']}', 1, 0) AS {TREATMENT_COLUMN_NAME},
                {user_breakdown_rollup}
                IFNULL(SUM({metric_name}), 0) AS {self.config['metric']}
            FROM `{metric_table_name}`
            GROUP BY 1, 2
        """
        submit_sync_query(
            sql,
            client=self.client,
            dialect='standard',
            dest_project_id=ITE_PROJECT,
            dest_dataset_id=ITE_DATASET,
            dest_table_name=destination_table,
            write_disposition='WRITE_TRUNCATE'
        )

    def execute(self):
        """
        Execute data preparation job.
        :return:
        """
        self.report.run_joins(overwrite=False)
        metric_table_name = self.report.get_joined_table_name(self.report.metric_tables[0], full=True)

        ITE_data_table = get_training_table_name(self.config['study_name'],
                                                 self.config['study_start_date'],
                                                 self.config['study_end_date'],
                                                 self.config['control_id'],
                                                 self.config['treatment_id'],
                                                 self.config['metric'],
                                                 )
        self.agg(metric_table_name, ITE_data_table)
        self.logger.info(f"write training data to to {ITE_data_table}")


def main(arg):
    data_prep_job = DataPrepJob(arg.config_name)
    data_prep_job.execute()

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--config-name',
        type=str,
        required=True,
        help='config yaml file name')
    args = parser.parse_args()
    main(args)