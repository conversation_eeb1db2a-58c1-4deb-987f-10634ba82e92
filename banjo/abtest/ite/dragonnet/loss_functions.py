import tensorflow as tf

def regression_loss(concat_true, concat_pred):
    """
    mse of prediction of y0 and y1 in dragonnet.
    :param concat_true: tensor with dimension of (BATCH_SIZE, 2)
    :param concat_pred: tensor with dimension of (BATCH_SIZE, 4)
    :return:
    """
    y_true = concat_true[:, 0]
    t_true = concat_true[:, 1]

    y0_pred = concat_pred[:, 0]
    y1_pred = concat_pred[:, 1]
    t_pred = concat_pred[:, 2]
    epsilon = concat_pred[:, 3]

    loss0 = tf.reduce_sum((1. - t_true) * tf.square(y_true - y0_pred))
    loss1 = tf.reduce_sum(t_true * tf.square(y_true - y1_pred))
    return (loss0 + loss1)

def binary_classification_loss(concat_true, concat_pred):
    """
    cross entropy loss of treatment prediction
    :param concat_true: tensor with dimension of (BATCH_SIZE, 2)
    :param concat_pred: tensor with dimension of (BATCH_SIZE, 4)
    :return:
    """
    t_true = concat_true[:, 1]
    t_pred = concat_pred[:, 2]
    t_pred = (t_pred + 0.001) / 1.002 # clip probability to [0.001, 0.999]
    loss = tf.reduce_sum(tf.keras.losses.BinaryCrossentropy()(t_true, t_pred))

    return loss