import numpy as np
import tensorflow as tf
from tensorflow.keras.layers import (
    Layer, Input, Den<PERSON>, Concatenate, BatchNormalization, Dropout, Reshape, Embedding, StringLookup
)
from tensorflow.keras import Model, regularizers
from typing import List



class EpsilonLayer(Layer):
    """
    Coefficient in targeted regularization
    """
    def __init__(self):
        super(Epsilon<PERSON>ayer, self).__init__()

    def build(self, input_shape):
        # Create a trainable weight variable for this layer.
        self.epsilon = self.add_weight(name='epsilon',
                                       shape=[1, 1],
                                       initializer='RandomNormal',
                                       #  initializer='ones',
                                       trainable=True)
        super(EpsilonLayer, self).build(input_shape)  # Be sure to call this at the end

    def call(self, inputs, **kwargs):
        # import ipdb; ipdb.set_trace()
        return self.epsilon * tf.ones_like(inputs)[:, 0:1]


def make_dragonnet(
        vocab_df,
        categorical_features: List[str],
        numerical_features: List[str],
        reg_l2: float
) -> Model:
    """
    Neural net predictive model. The dragon has three heads.
    :param vocab_df:
    :param categorical_features:
    :param numerical_features:
    :param reg_l2:
    :return:
    """
    inputs = []
    embedding_outputs = []
    # embedding layers for categorical features
    for feature in categorical_features:
        # label encoder for categorical features, NULL will be encoded as 0
        string_lookup_layer = StringLookup(
            vocabulary=vocab_df[f'{feature}_vocab'].iloc[0], num_oov_indices=1)
        vocab_size = len(vocab_df[f'{feature}_vocab'].iloc[0])
        # rule based embedding dimension = vocab_size / 2, but not larger than 50
        embedding_dim = min(int(np.ceil(vocab_size / 2)), 50)
        embedding = Embedding(input_dim=vocab_size + 1, output_dim=embedding_dim)

        keras_input = tf.keras.Input(shape=(1,), dtype=tf.string, name=feature)
        inputs.append(keras_input)
        embedding_outputs.append(
            Reshape(target_shape=(embedding_dim,))(
                embedding(string_lookup_layer(keras_input))
            )
        )

    for feature in numerical_features:
        keras_input = Input(shape=(1,), dtype=tf.double, name=feature)
        inputs.append(keras_input)
        embedding_outputs.append(keras_input)

    # concat embedding with numerical features and treatment column
    x = Concatenate(axis=-1)(embedding_outputs)

    # representation
    x = Dense(units=200, activation='elu', kernel_initializer='RandomNormal')(x)
    x = Dense(units=200, activation='elu', kernel_initializer='RandomNormal')(x)
    x = Dense(units=200, activation='elu', kernel_initializer='RandomNormal')(x)

    t_predictions = Dense(units=1, activation='sigmoid')(x)

    # HYPOTHESIS
    y0_hidden = Dense(units=100, activation='elu', kernel_regularizer=regularizers.l2(reg_l2))(x)
    y1_hidden = Dense(units=100, activation='elu', kernel_regularizer=regularizers.l2(reg_l2))(x)

    # second layer
    y0_hidden = Dense(units=100, activation='elu', kernel_regularizer=regularizers.l2(reg_l2))(y0_hidden)
    y1_hidden = Dense(units=100, activation='elu', kernel_regularizer=regularizers.l2(reg_l2))(y1_hidden)

    # third
    y0_predictions = Dense(units=1, activation=None, kernel_regularizer=regularizers.l2(reg_l2), name='y0_predictions')(
        y0_hidden)
    y1_predictions = Dense(units=1, activation=None, kernel_regularizer=regularizers.l2(reg_l2), name='y1_predictions')(
        y1_hidden)

    dl = EpsilonLayer()
    epsilons = dl(t_predictions, name='epsilon')
    # logging.info(epsilons)
    concat_pred = Concatenate(1)([y0_predictions, y1_predictions, t_predictions, epsilons])
    model = Model(inputs=inputs, outputs=concat_pred)

    return model
