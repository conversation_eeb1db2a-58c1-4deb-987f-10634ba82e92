from banjo.abtest.ite.constant import ITE_PROJECT, ITE_DATASET, TREATMENT_COLUMN_NAME
from banjo.abtest.ite.common.data_loader import DataLoader
from banjo.abtest.ite.common.utils import get_prediction_table_name
from banjo.abtest.ite.dragonnet.model import make_dragonnet
from banjo.abtest.ite.dragonnet.loss_functions import regression_loss
from banjo.utils.gbq import to_gbq

import argparse
import os
import pandas as pd
import tensorflow as tf
from tensorflow.keras.optimizers import Adam, SGD

def main(args):
    dl = DataLoader(args.config_name)
    dataset, vocab_df, categorical_features, numerical_features, outcome_metric_name = dl.read_bigquery()

    def transform_row(row_dict):
        """
        Transform raw dataset into format that can be consumed by dragonnet
        :param row_dict:
        :return: (X, (y, t))
        """
        trimmed_dict = {column: tf.expand_dims(tensor, axis=0)
                        for (column, tensor) in row_dict.items()
                        if column in categorical_features + numerical_features
                        }
        # target: tuple of outcome metric and treatment
        outcome = tf.concat(
            [tf.expand_dims(row_dict[outcome_metric_name], axis=0),
             tf.expand_dims(tf.cast(row_dict[TREATMENT_COLUMN_NAME], tf.double), axis=0)
             ],
            axis=0
        )
        return (trimmed_dict, outcome)
    transformed_ds = dataset.map(transform_row)

    model = make_dragonnet(vocab_df, categorical_features, numerical_features, reg_l2=0.01)
    # TODO: add metrics
    model.compile(optimizer=Adam(learning_rate=0.001), loss=regression_loss)
    model.fit(transformed_ds.batch(32), epochs=3)

    # make prediction of (y0, y1, t, epsilons)
    predictions = model.predict(transformed_ds)
    ghost_user_ids = list(x.decode() for x in dataset.map(lambda x: x['ghost_user_id']).as_numpy_iterator())
    output_df = pd.DataFrame(
        predictions,
        columns=['y0_pred', 'y1_pred', 't_pred', 'eps']
    )
    output_df['ghost_user_id'] = ghost_user_ids
    prediction_table_name = get_prediction_table_name(
        "ITE_Dragonnet_prediction",
        dl.config['study_name'],
        dl.config['study_start_date'],
        dl.config['study_end_date'],
        dl.config['control_id'],
        dl.config['treatment_id'],
        dl.config['metric'],
    )
    to_gbq(output_df, project_id=ITE_PROJECT, dest_dataset_id=ITE_DATASET, dest_table_name=prediction_table_name)
    print(f'prediction exported to: {ITE_PROJECT}.{ITE_DATASET}.{prediction_table_name}')

    export_path = os.path.join(args.job_dir, 'keras_model')
    model.save(export_path)
    print('Model exported to: {}'.format(export_path))


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--config-name',
        type=str,
        required=True,
        help='config yaml file name')
    parser.add_argument(
        '--job-dir',
        type=str,
        required=True,
        help='local or GCS location for writing checkpoints and exporting '
             'models')
    args = parser.parse_args()
    main(args)