from banjo.abtest.ite.constant import TREATMENT_COLUMN_NAME
import numpy as np
import tensorflow as tf
from tensorflow.keras.layers import (
    Layer, Input, Dense, Concatenate, BatchNormalization, Dropout, Reshape, Embedding, StringLookup
)
from tensorflow.keras import Model
from typing import List


def make_single_prediction_model(
        vocab_df,
        categorical_features: List[str],
        numerical_features: List[str]
):
    """S-learner model
    A NN model that takes both features and treatment indicator to predict outcome.

    :param vocab_df: vocabularies for each categorical feature
    :param categorical_features: a list of categorical features
    :param numerical_features: a list of numerical features
    :return: tf.keras.Model
    """
    inputs = []
    embedding_outputs = []
    # embedding layers for categorical features
    for feature in categorical_features:
        # label encoder for categorical features, NULL will be encoded as 0
        string_lookup_layer = StringLookup(
            vocabulary=vocab_df[f'{feature}_vocab'].iloc[0], num_oov_indices=1)
        vocab_size = len(vocab_df[f'{feature}_vocab'].iloc[0])
        # rule based embedding dimension = vocab_size / 2, but not larger than 50
        embedding_dim = min(int(np.ceil(vocab_size / 2)), 50)
        embedding = Embedding(input_dim=vocab_size + 1, output_dim=embedding_dim)

        keras_input = tf.keras.Input(shape=(1,), dtype=tf.string, name=feature)
        inputs.append(keras_input)
        embedding_outputs.append(
            Reshape(target_shape=(embedding_dim,))(
                embedding(string_lookup_layer(keras_input))
            )
        )

    for feature in numerical_features:
        keras_input = Input(shape=(1,), dtype=tf.double, name=feature)
        inputs.append(keras_input)
        embedding_outputs.append(keras_input)

    # treatment column
    keras_input = Input(shape=(1,), dtype=tf.double, name=TREATMENT_COLUMN_NAME)
    inputs.append(keras_input)
    embedding_outputs.append(keras_input)

    # concat embedding with numerical features and treatment column
    z = Concatenate(axis=-1)(embedding_outputs)
    # FC layers
    z = Dense(32, activation='relu')(z)
    z = Dense(16, activation='relu')(z)
    z = Dense(1)(z)

    model = Model(inputs=inputs, outputs=z)
    return model
