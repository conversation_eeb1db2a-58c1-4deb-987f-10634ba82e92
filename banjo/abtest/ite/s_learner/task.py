from banjo.abtest.ite.constant import ITE_PROJECT, ITE_DATASET, TREATMENT_COLUMN_NAME
from banjo.abtest.ite.common.data_loader import DataLoader
from banjo.abtest.ite.common.utils import get_prediction_table_name
from banjo.abtest.ite.s_learner.model import make_single_prediction_model
from banjo.utils.gbq import to_gbq

import argparse
import os
import pandas as pd
import tensorflow as tf
from tensorflow.keras.optimizers import Adam, SGD

def main(args):
    dl = DataLoader(args.config_name)
    dataset, vocab_df, categorical_features, numerical_features, outcome_metric_name = dl.read_bigquery()

    def transform_row(row_dict):
        """
        expand dim of input features
        :param row_dict:
        :return: tuple of (X, y)
        """
        trimmed_dict = {column: tf.expand_dims(tensor, axis=0)
                        for (column, tensor) in row_dict.items()
                        if column in categorical_features + numerical_features + [TREATMENT_COLUMN_NAME]
                        }
        outcome = row_dict[outcome_metric_name]
        return (trimmed_dict, outcome)
    transformed_ds = dataset.map(transform_row)

    model = make_single_prediction_model(vocab_df, categorical_features, numerical_features)
    model.compile(optimizer=Adam(learning_rate=0.001), loss='mse')
    model.fit(transformed_ds.batch(32), epochs=20)

    # make counterfactual dataset
    def counterfactual_row(row_dict):
        """
        expand dim of input features, and flip treatment assignment
        :param row_dict:
        :return: X_counterfactual
        """
        trimmed_dict = {column: tf.expand_dims(tensor, axis=0)
                        for (column, tensor) in row_dict.items()
                        if column in categorical_features + numerical_features
                        }
        # make control -> treatment and treatment -> control
        trimmed_dict[TREATMENT_COLUMN_NAME] = tf.expand_dims(1 - row_dict[TREATMENT_COLUMN_NAME], axis=0)
        #outcome = row_dict[outcome_metric_name]
        return trimmed_dict
    counterfactual_ds = dataset.map(counterfactual_row)

    # make predictions
    factual_pred = model.predict(transformed_ds)
    counterfactual_pred = model.predict(counterfactual_ds)
    ghost_user_ids = list(x.decode() for x in dataset.map(lambda x: x['ghost_user_id']).as_numpy_iterator())
    output_df = pd.DataFrame(
        {
            'ghost_user_id': ghost_user_ids,
            'factual_prediction': factual_pred.flatten(),
            'counterfactual_prediction': counterfactual_pred.flatten()
        }
    )
    prediction_table_name = get_prediction_table_name(
        "ITE_S_learner_prediction",
        dl.config['study_name'],
        dl.config['study_start_date'],
        dl.config['study_end_date'],
        dl.config['control_id'],
        dl.config['treatment_id'],
        dl.config['metric'],
    )
    to_gbq(output_df, project_id=ITE_PROJECT, dest_dataset_id=ITE_DATASET, dest_table_name=prediction_table_name)
    print(f'prediction exported to: {ITE_PROJECT}.{ITE_DATASET}.{prediction_table_name}')

    export_path = os.path.join(args.job_dir, 'keras_model')
    model.save(export_path)
    print('Model exported to: {}'.format(export_path))



if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--config-name',
        type=str,
        required=True,
        help='config yaml file name')
    parser.add_argument(
        '--job-dir',
        type=str,
        required=True,
        help='local or GCS location for writing checkpoints and exporting '
             'models')
    args = parser.parse_args()
    main(args)