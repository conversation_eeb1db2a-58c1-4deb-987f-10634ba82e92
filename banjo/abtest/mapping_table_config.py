"""Configs for where to find mapping tables"""

ABTEST_PROJECT = 'sc-portal'
MAPPING_TABLE_CONFIG = {
    "v1": {
        "dataset": "abtest_study_default_map_cumulative",
        "table_prefix": "{study_name}__{start_date}",
    },
    "v2": {
        "dataset": "notebook",
        "table_prefix": "{study_name}__{start_date}",
    },
    "v3": {
        "dataset": "usermap_cumulative",
        "table_prefix": "{study_name}__{start_date}",
    },
    "allocation": {
        "dataset": "allocation_usermap_cumulative",
        "table_prefix": "{study_name}__{start_date}",
    }
}


# usermap_v3 with hourly timestamp. 2020 q3
HOURLY_USERMAP_ROLLOUT_DATE = '20200928'
HOURLY_USERMAP_TEST_STDUY = 'DUM_IOS'
