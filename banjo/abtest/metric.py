"""Metric and MetricTable classes"""

from __future__ import division, print_function, unicode_literals
import copy
import logging
import itertools
import re
import pandas as pd
from datetime import datetime
from typing import List, Optional, Union, Dict

from banjo.abtest.metric_constants import DATE_FORMAT_NO_DASH, DATE_FORMAT_W_DASH
import banjo.utils.helpers
from sclexicon.generated import metric_pb2, table_pb2
import sclexicon.utils.tables
from .data import aggregate_metric_table_by_day, aggregate_metric_table_cuped_cumulative, aggregate_quantile
from .analysis import compare_metric_table, compare_quantile_metrics, multiple_treatment_ids

AB_PERFORMANCE_DATASET = 'sc-portal:abtest_metric_extraction.'
POSITIVE = "<positive>"
NEGATIVE = "<negative>"
NEUTRAL = "<neutral>"


class MetricType(object):
    METRIC_TYPE_SUM = 'sum'
    METRIC_TYPE_ACTIVE_DAY = 'active_day'
    METRIC_TYPE_UNIQUE = 'unique'
    METRIC_TYPE_RATIO = 'ratio'
    METRIC_TYPE_QUANTILE = 'quantile'


class MetricDirection(object):
    POSITIVE = "<positive>"
    NEGATIVE = "<negative>"
    NEUTRAL = "<neutral>"


class Metric(object):
    """Define a single metric

    An instance of this class maps a column or columns in a table to a metric in the analysis.
    """
    distribution_map = [
        ('cont', metric_pb2.Metric.DISTRIBUTION_CONTINUOUS),
        ('bin', metric_pb2.Metric.DISTRIBUTION_BINARY),
        ('ratio', metric_pb2.Metric.DISTRIBUTION_RATIO),
        ('quantile', metric_pb2.Metric.DISTRIBUTION_QUANTILE),
    ]
    direction_map = [
        (POSITIVE, metric_pb2.Metric.DIRECTION_POSITIVE),
        (NEGATIVE, metric_pb2.Metric.DIRECTION_NEGATIVE),
        (NEUTRAL, metric_pb2.Metric.DIRECTION_NEUTRAL),
    ]

    def __init__(self,
                 col: str,
                 name: Optional[str] = None,
                 dist: Optional[str] = 'cont',
                 daily: Optional[bool] = True,
                 cumulative: Optional[bool] = True,
                 always_use_cumulative_count: Optional[bool] = False,
                 use_metric_sample_size: Optional[bool] = False,
                 numerator: Optional[str] = None,
                 denominator: Optional[str] = None,
                 numerator_metric: Optional["Metric"] = None,
                 denominator_metric: Optional["Metric"] = None,
                 desired_direction: Optional[str] = POSITIVE,
                 interpolate_density: Optional[bool] = False,
                 lower_bound: Optional[Union[int, float]] = None,
                 upper_bound: Optional[Union[int, float]] = None,
                 winsorization_limits: Optional[Dict[str, Union[int, float]]] = None,
                 description: Optional[str] = None,
                 stat_fmtr: Optional[str] = None,
                 ):
        """

        Parameters
        ----------
        col : str
            The SQL field name of the metric in BaseMetricTable.sql. This field is also used as the unique metric
            identifier including ratio metrics. We require that all `col` values to be unique for a single A/B Report
        name : str
            Human readable name of the metric.
        dist : str
            "cont", "bin", "quantile", "ratio" are valid values
        daily : bool
            Whether to include this metric in the daily results
        cumulative : bool
            Whether to include this metric in the cumulative results
        always_use_cumulative_count : bool
            Deprecated. Historically A/B console used daily exposure count as the sample size. A/B console has switched to
        always using the cumulative exposure count for daily results.
            use_metric_sample_size: bool
            Not recommended to use.
        numerator : str
            The sql field name of the numerator metric. We prefer using numerator_metric instead
        denominator : str
            The sql field name of the denominator metric. We prefer using denominator_metric instead
        numerator_metric : Metric
            The numerator metric
        denominator_metric : Metric
            The denominator metric
        desired_direction : str
            metric.POSITIVE, metric.NEUTRAL, metric.NEGATIVE
        interpolate_density : bool
            Whether to use the new Quantile metric density estimation formula. If True, please make sure that the
            metric's unique value count is < 10^6. Please considering rounding to reduce the number of unique values.
            https://docs.google.com/document/d/1e7NLutq_IIeXpdK6zZgGE2yA6pSXCZxf89Tpeke_jcU/edit
        lower_bound : float, optional
            To reduce the cardinality of quantile metrics, the lower bound where the metric should be truncated
        upper_bound : float, optional
            To reduce the cardinality of quantile metrics, the upper bound where the metric should be truncated
        winsorization_limits : dict, optional
            For dist="cont" metrics, an optional winsorization limit to be applied at daily-user level
            http://go.sc-corp.net/metric-capping
        description : str
            Longer human readable description
        stat_fmtr : str
            The format string for the metric statistics. This is used in the table visualizations.
            Example: "{pct_diff:,.2f}%, Δ:{diff:,.3} ({avg_control:,.4}→{avg_treatment:,.4}, {p_value:.4})"
            When this is provided, the setting on the Report object's configuration will be ignored.
        """

        self.col = col
        if name is None:
            self.name = col.replace('_', ' ').title()
        else:
            self.name = name
        self.dist = dist
        self.always_use_cumulative_count = always_use_cumulative_count
        self.daily = daily
        self.cumulative = cumulative
        self.use_metric_sample_size = use_metric_sample_size
        self.numerator = numerator
        self.denominator = denominator
        self.numerator_metric = numerator_metric
        self.denominator_metric = denominator_metric
        self.desired_direction = desired_direction
        self.interpolate_density = interpolate_density
        if lower_bound is None and interpolate_density:
            self.lower_bound = 0
        else:
            self.lower_bound = lower_bound
        if upper_bound is None and interpolate_density:
            self.upper_bound = 10**8
        else:
            self.upper_bound = upper_bound

        self.winsorization_limits = winsorization_limits
        self.description = description
        self.stat_fmtr = stat_fmtr

    def __str__(self):
        return 'Metric: {col}({name})-{dist}'.format(
            col=self.col,
            name=self.name,
            dist=self.dist,
        )

    def __repr__(self):
        return ("Metric(col='{col}', name='{name}', dist='{dist}', "
                "daily={daily}, cumulative={cumulative}, "
                "use_metric_sample_size="
                "{use_metric_sample_size}, "
                "numerator='{numerator}', "
                "denominator='{denominator}', "
                "desired_direction='{desired_direction}')").format(
            col=self.col,
            name=self.name,
            dist=self.dist,
            daily=self.daily,
            cumulative=self.cumulative,
            use_metric_sample_size=self.use_metric_sample_size,
            numerator=self.numerator,
            denominator=self.denominator,
            desired_direction=self.desired_direction,
        )

    def get_aa_ab_sum(self):
        return ["IFNULL(SUM(IF(period='AA', {col}, 0)), 0) AS {col}__aa,"
                "IFNULL(SUM(IF(period='AB', {col}, 0)), 0) AS {col}__ab".format(col=self.col)
                ]

    def get_cuped_slope_col(self):
        return ["IFNULL(SAFE_DIVIDE(COVAR_SAMP({col}__aa,{col}__ab), VAR_SAMP({col}__aa)), 0) AS {col}__slope".format(col=self.col)]

    def get_metric_cuped_and_ab_value_col(self):
        return ["{col}__ab - {col}__aa * {col}__slope AS {col}, {col}__ab as {col}__ab ".format(col=self.col)]

    def get_sql_select_rollup(self, over=None):
        """The select statements for this metric when do roll-ups at different levels

        This is for backwards compatibility. We should subclass the Metric class instead for different types.
        """
        if not over:
            over = ""
        if self.dist == "ratio":
            return []
        elif self.dist == "cont":
            return ["SUM({col}) {over} AS {col}".format(col=self.col, over=over)]
        elif self.dist == "bin":
            return ["MAX({col}) {over} AS {col}".format(col=self.col, over=over)]
        return []

    def sql_select_sufficient_stat_fields(self, cuped=False):
        statements = self.get_sql_select_sufficient_stat(cuped=cuped)
        return [_extract_sql_field_name_from_select(statement) for statement in statements]

    def get_sql_select_sufficient_stat(self, cuped=False):
        sufficient_stats = []
        if self.dist == "cont":
            if cuped:
                sufficient_stats = [
                    "IFNULL(SUM({col}), 0) AS {col}_sum".format(col=self.col),
                    "IFNULL(SUM(POW({col}, 2)), 0) AS {col}_sum_square".format(col=self.col),
                    "IFNULL(SUM({col}__ab), 0) AS {col}_sum_original".format(col=self.col),
                ]
            else:
                sufficient_stats = [
                    "IFNULL(SUM({col}), 0) AS {col}_sum".format(col=self.col),
                    "IFNULL(SUM(POW({col}, 2)), 0) AS {col}_sum_square".format(col=self.col),
                ]
        elif self.dist == "bin":
            sufficient_stats = ["SUM(IF({col} > 0 , 1, 0)) AS {col}_bin".format(col=self.col)]
        elif self.dist == "ratio":
            sufficient_stats = self._get_sql_select_sufficient_stat_for_ratio_metric()
        return self._add_metric_sample_size(sufficient_stats)

    def _add_metric_sample_size(self, sufficient_stats):
        if self.use_metric_sample_size:
            sufficient_stats.append("COUNT({col_for_sample_size}) AS {col}_sample_size".format(
                    col_for_sample_size=self.denominator_metric.col if self.dist == "ratio" else self.col,
                    col=self.col,
                )
            )
        return sufficient_stats

    def _get_sql_select_sufficient_stat_for_ratio_metric(self):
        if not self.numerator_metric or not self.denominator_metric:
            raise ValueError("numerator_metric and denominator_metric must be present")

        numerator = self.numerator_metric
        denominator = self.denominator_metric

        numerator_expression = (
            numerator.col if numerator.dist == "cont" else "IF({} > 0 , 1, 0)".format(numerator.col)
        )
        denominator_expression = (
            denominator.col if denominator.dist == "cont" else "IF({} > 0 , 1, 0)".format(denominator.col)
        )

        return [
            "IFNULL(SUM({}), 0) AS {}_num".format(numerator_expression, self.col),
            "IFNULL(SUM(POW({}, 2)), 0) AS {}_num_sqr".format(numerator_expression, self.col),
            "IFNULL(SUM({}), 0) AS {}_den".format(denominator_expression, self.col),
            "IFNULL(SUM(POW({}, 2)), 0) AS {}_den_sqr".format(denominator_expression, self.col),
            "IFNULL(SUM({} * {}), 0) AS {}_cov".format(numerator_expression, denominator_expression, self.col)
        ]

    def get_sql_select_winsorization(self, ts_column):
        """The select statements for this metric when do the daily-user level winsorization
        """
        if self.dist == "cont" and self.winsorization_limits is not None:
            when_clause = (
                'WHEN {ts_column} = TIMESTAMP("{ts_value} 00:00:00") THEN '
                'IF(SUM({col}) > {cap:.24e}, {cap:.24e}, SUM({col}))'
            )
            when_clauses = [
                when_clause.format(
                    ts_column=ts_column,
                    ts_value=datetime.strptime(dt, DATE_FORMAT_NO_DASH).strftime(DATE_FORMAT_W_DASH),
                    cap=cap,
                    col=self.col,
                )
                for dt, cap in self.winsorization_limits.items()
            ]
            return ["CASE\n {when_clauses}\n END AS {col}".format(when_clauses="\n".join(when_clauses), col=self.col)]
        return self.get_sql_select_rollup()

    @classmethod
    def from_lexicon(cls, lexicon_metric):
        """Factory method using a lexicon object"""
        if lexicon_metric is None:
            return None
        return cls(
            col=lexicon_metric.name,
            name=lexicon_metric.display_name or None,
            dist=dict(reversed(pair) for pair in cls.distribution_map).get(lexicon_metric.distribution),
            numerator_metric=(
                cls.from_lexicon(lexicon_metric.numerator) if lexicon_metric.HasField("numerator") else None
            ),
            denominator_metric=(
                cls.from_lexicon(lexicon_metric.denominator) if lexicon_metric.HasField("denominator") else None
            ),
            desired_direction=dict(reversed(pair) for pair in cls.direction_map).get(lexicon_metric.desired_direction),
            description=lexicon_metric.description
        )

    def to_lexicon(self):
        """Return a lexicon object"""
        return metric_pb2.Metric(
            name=self.col,
            display_name=self.name,
            distribution=dict(self.distribution_map).get(self.dist),
            numerator=self.numerator_metric.to_lexicon() if self.numerator_metric else None,
            denominator=self.denominator_metric.to_lexicon() if self.denominator_metric else None,
            desired_direction=dict(self.direction_map).get(self.desired_direction),
            description=self.description,

        )


def get_abtest_console_metric_table(*args, **kwargs):
    raise AttributeError("This method has been deprecated")


def get_ab_quantile_metric(metric, start_date, end_date):

    query = """
    SELECT
      TIMESTAMP('{end}') AS ts,
      {user_col} AS ghost_user_id,
      {value_col} AS {metric_name}
    FROM
      TABLE_DATE_RANGE_STRICT(
        [{table}],
        TIMESTAMP('{start}'),
        TIMESTAMP('{end}')
      )
    """.format(
        value_col=metric.get('quantile_expr'),
        metric_name=metric.get('quantile_col'),
        table=AB_PERFORMANCE_DATASET + metric.get('name') + '__',
        user_col=metric.get('user_col'),
        start=pd.to_datetime(start_date),
        end=pd.to_datetime(end_date)
    )

    quest_metric = Metric(
        metric.get('quantile_col'),
        dist="quantile",
    )
    quest_metric_table = MetricTable(
        sql=query,
        metrics=[quest_metric]
    )
    return quest_metric_table


def define_metric_list(
        cont_cols,
        bin_cols,
        ratio_cols,
        quantile_cols,
        metric_desired_directions,
        ):
    """Helper function"""
    cont_cols = [] if cont_cols is None else cont_cols
    bin_cols = [] if bin_cols is None else bin_cols
    ratio_cols = [] if ratio_cols is None else ratio_cols
    metrics = []
    metrics.extend(
        [Metric(col, dist='cont', desired_direction=metric_desired_directions.get(col, POSITIVE)) for col in cont_cols]
    )
    metrics.extend(
        [Metric(col, dist='bin', desired_direction=metric_desired_directions.get(col, POSITIVE)) for col in bin_cols]
    )
    metrics.extend(
        [Metric(
            col['col'],
            dist='ratio',
            numerator=col['numerator'],
            denominator=col['denominator'],
            desired_direction=metric_desired_directions.get(col['col'], POSITIVE),
        ) for col in ratio_cols]
    )
    metrics.extend(
        [Metric(
            col,
            dist='quantile',
            desired_direction=metric_desired_directions.get(col, POSITIVE),
            interpolate_density=True
        ) for col in quantile_cols]
    )
    return metrics


# For backwards compatibility, all args are keyword arguments. And new args need to be added after sql and metrics.
class BaseMetricTable(object):
    def __init__(self,
                 sql: Optional[str] = None,
                 metrics: Optional[List[Metric]] = None,
                 name: Optional[str] = None,
                 inner_join_with_mapping: Optional[bool] = False,
                 unit_id: Optional[str] = 'ghost_user_id',
                 join_key: Optional[str] = 'ghost_user_id',
                 cumulative: Optional[bool] = True,
                 daily: Optional[bool] = True,
                 quantile_metrics: Optional[bool] = False,
                 metric_section: Optional[str] = None,
                 bq_dialect: Optional[str] = 'legacy',
                 hourly_granularity: Optional[bool] = False,
                 sql_callable: callable = None,
                 aa: Optional[bool] = False,
                 cuped: Optional[bool] = False,
                 including_mapping_info: Optional[bool] = False,
                 ):
        """

        Parameters
        ----------
        sql : str
            sql_callable is the recommended way to specify the SQL logic for the metric definitions.

            The sql string should define these columns
              ts (timestamp): at UTC midnight indicating date of the row. (A value of '2017-01-01 00:00:00 UTC'
                means the row is for 2017/01/01 PST.) Consider this as using the timestamp value to indicate the (PST)
                date, and so only the date part is relevant.
              [optional] metric_ts (timestamp): not to confused with ts above, which indicate the PST date of row.
                metric_ts indicates the hour of the row, and perhaps confusingly in UTC. So
                metric_ts = '2017-01-01 00:00:00 UTC' means the row is for the hour of 00:00 UTC or 16:00 PST of the
                previous day. You should supply this when hourly_granularity is set to True.
                When you include metric_ts, you still need to include the `ts` column.

              ghost_user_id (str): or unit_id if your study is not run at the ghost_user_id level
              a list of metrics (numeric): the metric values

              optionally, the SQL may also include metric level dimensions (see FieldLevelBreakdownMetricTable)

            The resulted data must be one row per ts, ghost_user_id (and optionally the metric level dimensions).

        metrics : list of banjo.abtest.metric.Metric
        name : str
            Unique name of this MetricTable
        inner_join_with_mapping : bool
            If True, the mapping table will be inner joined with this table, thereby keeping only users with
            metrics in the comparison/as the denominator. This is not recommended.
        unit_id : str
            The id field identifying a unit of the study.
        join_key : str
            May be useful if the SQL is to be joined with the mapping sql using a field that's not the unit_id
            This is rarely used.
        cumulative : bool
            Set to False if you want to skip the cumulative results of this MetricTable.

            Most often we set this to True.
        daily : bool
            Set to False if you want to skip the daily calculation of the results, i.e. the daily trend.
            Setting to False saves some cost and time.
        quantile_metrics : bool
            Whether the MetricTable include quantile metrics

            Note: a metric table either includes only quantile metrics (True), or only non-quantile metrics (False)
        metric_section : str
            Section of the metric table, a piece of metadata
        bq_dialect : str
            "standard" or "legacy"
        sql_callable : callable
            Callable[[str, str], str]
            Returns a SQL string (see `sql` above for details) for the input start_date and end_date.

            start_date and end_date are %Y%m%d formatted strings, e.g. "20220619"
        aa : bool
            Whether retro A/A results should be computed for this MetricTable. This is only supported when
            sql_callable is supplied.
        cuped : bool
            Whether cuped  results should be computed for this MetricTable. We only support cont metrics.
        including_mapping_info : bool
            Sometimes the metric table already has the mapping information. In those cases, you might want to skip
            the potentially expensive join operation, by setting this to True. If you do that, you should make sure
            that `sql` defines a table in the schema that we expect:
            - ghost_user_id (str): or your specified join_key
            - exp_id (str): should match Report object's control_id and treatment_ids
            - exp_ds (timestamp):
                indicate the date of the metric value, e.g. TIMESTAMP('2017-01-01 00:00:00')
                if you aggregate over the entire period, you could simply set this value to the analysis end date
            - period (str) optional, only needed if you specified compute AA or CUPED options
            - metric_1, metric_2, ... (numeric) the metrics

        """
        self.sql = sql
        if not isinstance(metrics, list):
            raise TypeError('metrics must be a list of Metric objects')
        self.metrics = metrics
        self._name = name
        self.inner_join_with_mapping = inner_join_with_mapping
        self.unit_id = unit_id
        self.join_key = join_key
        self.cumulative = cumulative
        self.daily = daily
        self.quantile_metrics = quantile_metrics
        self.metric_section = metric_section
        self.hourly_granularity = hourly_granularity
        self.aa = False

        if bq_dialect == "legacy" and sql is None:
            raise ValueError("Define sql if you use legacy bq dialect, sql_callable only support standard bq")

        if sql is None and sql_callable is None:
            raise ValueError("Define sql or sql_callable")

        if cuped or aa:
            self.aa = True
        if self.aa and sql_callable is None:
            raise ValueError("sql_callable must be provided, or set aa back to be False")
        self.sql_callable = sql_callable
        self.cuped = cuped

        if bq_dialect not in ['standard', 'legacy']:
            raise ValueError('bq_dialect must be standard or legacy')

        self.bq_dialect = bq_dialect
        self.aggregation_results = None
        self.analysis_results = None
        self.materialized_table_name = "metric_table_{}_{}".format(self.name[:50], self.hash_string)
        self.is_materialized = False

        # Connect ratio metrics with denominator and numerator objects
        # This won't be needed if all ratio metrics are defined with denominator and numerator objects
        ratio_metrics = [metric for metric in self.metrics if metric.dist == "ratio"]
        for metric in ratio_metrics:
            if not metric.numerator_metric and not metric.denominator_metric:
                metric.numerator_metric = next(m for m in self.metrics if m.col == metric.numerator)
                metric.denominator_metric = next(m for m in self.metrics if m.col == metric.denominator)
        # todo(xhe): add more checks here. For example we should ensure that the numerators and denominators of ratios
        # are included as well

        self.including_mapping_info = including_mapping_info
        if self.including_mapping_info and not self.sql:
            raise ValueError("If including_mapping_info is set to True, sql must be provided")
        # todo: add more checks here. For example we should ensure that the sql has the expected schema

    def __str__(self):
        return 'MetricTable: metrics:{cols}'.format(
            cols=self.cols
        )

    def __repr__(self):
        return ("MetricTable(sql='''{sql}''',\n"
                "metrics={metrics},\n"
                "name='{name}',\n"
                "inner_join_with_mapping={inner_join_with_mapping},\n"
                "unit_id='{unit_id}',\n"
                "join_key='{join_key}',\n"
                "cumulative={cumulative},\n"
                "daily={daily},\n"
                ")").format(
            sql=self.sql,
            metrics=self.metrics,
            name=self.name,
            inner_join_with_mapping=self.inner_join_with_mapping,
            unit_id=self.unit_id,
            join_key=self.join_key,
            cumulative=self.cumulative,
            daily=self.daily,
        )

    @property
    def name(self):
        if self._name is not None:
            return self._name
        else:
            return '_'.join(sorted(self.cols))

    @property
    def hash_string(self):
        # todo(xhe): we shouldn't need the cols in the hash
        # sql_callable("20200101", "20200101"): use fix value to return sql for detecting function change
        return banjo.utils.helpers.hash_string(
            str(self.sql) + "|".join(sorted(self.cols)) + "|" + str(self.aa) + "|" +
            str(self.sql_callable("20200101", "20200101") if self.sql_callable else self.sql_callable)
        )

    @property
    def cols(self):
        return [m.col for m in self.metrics if m.dist != 'ratio']

    def get_metrics_by_dist(self, dist):
        return [m.col for m in self.metrics if m.dist == dist]

    def get_metric_objects_by_dist(self, dist):
        return [m for m in self.metrics if m.dist == dist]

    def get_ratio_metrics(self):
        # Deprecated
        return [{'ratio': m.col,
                 'numerator': m.numerator,
                 'denominator': m.denominator}
                for m in self.metrics if m.dist == 'ratio']

    @property
    def daily_metrics(self):
        return [m.col for m in self.metrics if m.daily]

    @property
    def cumulative_metrics(self):
        return [m.col for m in self.metrics if m.cumulative]

    @property
    def always_use_cumulative_count_metrics(self):
        return [m.col for m in self.metrics if m.always_use_cumulative_count]

    def get_metrics_by_type(self, type_):
        if type_ == 'cumulative':
            return self.cumulative_metrics
        elif type_ == 'daily':
            return self.daily_metrics
        elif type_ == 'always_use_cumulative_count':
            return self.always_use_cumulative_count_metrics

        return []

    @property
    def name_mapping(self):
        return {m.col: m.name for m in self.metrics}

    @property
    def requires_winsorization(self):
        for metric in self.metrics:
            if metric.winsorization_limits is not None:
                return True
        return False

    def get_winsorization_clauses(self, ts_column):
        """the SQL winsorization logic"""
        clauses = []
        for metric in self.metrics:
            clauses.extend(metric.get_sql_select_winsorization(ts_column=ts_column))
        return clauses

    @classmethod
    def from_lexicon(cls, lexicon_table, start_date, end_date, only_available_metrics=True):
        table_path = "{project}:{dataset}.{table}_".format(
            project=lexicon_table.table_path.project,
            dataset=lexicon_table.table_path.dataset,
            table=lexicon_table.table_path.table_name_prefix,
        )
        unit_id = lexicon_table.banjo_config.unit_id
        if not unit_id:
            unit_id = None
        join_key = lexicon_table.banjo_config.join_key or "ghost_user_id"

        if only_available_metrics:
            lexicon_metrics = sclexicon.utils.tables.get_lexicon_available_metrics_by_dates(
                lexicon_table, start_date, end_date)
        else:
            lexicon_metrics = lexicon_table.metrics

        metrics = [Metric.from_lexicon(lexicon_metric) for lexicon_metric in lexicon_metrics]
        # Fields in the select clause for _metrics_
        if lexicon_table.type == table_pb2.BigQuerySourceTable.TABLE_TYPE_BLIZZARD_RAW_EVENT:
            selects = list(itertools.chain.from_iterable([metric.get_sql_select_rollup() for metric in metrics]))
        else:
            selects = [
                "{} AS {}".format(
                    lexicon_metric.banjo_config.sql_clause or lexicon_metric.name,
                    lexicon_metric.name
                ) for lexicon_metric in lexicon_metrics
                if lexicon_metric.distribution != metric_pb2.Metric.DISTRIBUTION_RATIO
            ]

        if lexicon_table.banjo_config.ds_field_sql_clause:
            ts = lexicon_table.banjo_config.ds_field_sql_clause
            source_table = (
                "TABLE_DATE_RANGE_STRICT([{table_path}], "
                "TIMESTAMP('{start_date'), "
                "TIMESTAMP('{end_date}')) "
            ).format(
                table_path=table_path,
                start_date=start_date,
                end_date=end_date,
            )
            # todo: this doesn't handle the cases when join_key and unit_id are different
            # we do not have such cases now
            sql = (
                "SELECT {unit_id} {ts} AS ts, {selects} "
                "FROM {source} "
                "GROUP BY {unit_id} ts"
            ).format(
                unit_id="{},".format(unit_id) if unit_id else "",
                ts=ts,
                selects=",\n".join(selects),
                source=source_table,
            )
        else:
            sql = abtest_metrics_sql_helper(
                table_path,
                start_date=start_date,
                end_date=end_date,
                columns=["ghost_user_id"] + selects,
                select_star=True,
                group_by=unit_id,
            )

        return cls(
            sql,
            metrics=metrics,
            name="{}_{}".format(lexicon_table.name, banjo.utils.helpers.hash_string(sql)),
            inner_join_with_mapping=lexicon_table.banjo_config.inner_join_with_mapping,
            unit_id=unit_id,
            join_key=join_key,
            quantile_metrics=lexicon_table.banjo_config.quantile_metrics,
            metric_section=lexicon_table.name,
        )

    def to_lexicon(self):
        # todo(xhe): implement this and save all existing MetricTable defined in this repo to the lexicon format
        pass

    def get_calculators(
            self,
            report,
            table_path,
            date_col,
            user_col,
            user_group_cols,
            cumulative,
            cumulative_trend,
            **kwargs,
        ):
        """ banjo.abtest.report.Report calls this function to get the Calculators

        The report object provides all the argument values. If any input values are not supported, the method
        should return an empty list

        Parameters
        ----------
        report : banjo.abtest.report.Report
            the caller
        table_path: str
            the full path of the joined table
        date_col: str
            the name for the date field
        user_col: str
            the name for the user (experiment id) field
        user_group_cols: list of str
            any user level group by (i.e. breakdown) fields
        cumulative : bool
            whether to return the query for the cumulative results
        cumulative_trend : bool
            whether to return the query that computes the cumulative trend
        kwargs :

        Returns
        -------
        calculators : list of Calculator
        """
        raise NotImplementedError


class MetricTable(BaseMetricTable):
    """MetricTable class supporting count, unique user, ratio and quantile metrics
    Using methods developed in 2017-2018
    """
    def __init__(self,
                 sql=None,
                 metrics=None,
                 name=None,
                 inner_join_with_mapping=False,
                 unit_id='ghost_user_id',
                 join_key='ghost_user_id',
                 cumulative=True,
                 daily=True,
                 quantile_metrics=False,
                 metric_section=None,
                 bq_dialect='legacy',
                 hourly_granularity=False,
                 sql_callable=None,
                 aa=False,
                 cuped=False,
                 ):
        super(MetricTable, self).__init__(
            sql=sql,
            metrics=metrics,
            name=name,
            inner_join_with_mapping=inner_join_with_mapping,
            unit_id=unit_id,
            join_key=join_key,
            cumulative=cumulative,
            daily=daily,
            quantile_metrics=quantile_metrics,
            metric_section=metric_section,
            bq_dialect=bq_dialect,
            hourly_granularity=hourly_granularity,
            sql_callable=sql_callable,
            aa=aa,
            cuped=cuped
        )

    # todo(xhe): rather than returning a list, we should just save them as an attribute of the MetrcTable instance
    def get_calculators(
            self,
            report,
            table_path,
            date_col,
            user_col,
            user_group_cols,
            cumulative,
            cumulative_trend,
            **kwargs,
    ):
        calculators = []
        if self.quantile_metrics:
            quantile_metrics = self.get_metric_objects_by_dist('quantile')
            for quantile_metric in quantile_metrics:
                agg_query = aggregate_quantile(
                    table_path,
                    bq_project="",
                    date_col=date_col,
                    user_col=user_col,
                    metric=quantile_metric,
                    quantiles=report.quantiles,
                    user_group_cols=user_group_cols,
                    cumulative=cumulative,
                    verbose=False,
                    dry_run=True,
                    eps=0.1,
                    **kwargs,
                )
                calculators.append(
                    Calculator(
                        agg_query,
                        analysis_func=multiple_treatment_ids(compare_quantile_metrics),
                        func_args=dict(
                            quantile_vars=[quantile_metric.col + '_' + quantile for quantile in report.quantiles],
                            group_vars=user_group_cols,
                        ),
                        metric_table=self,
                        bq_dialect="standard",
                    )
                )

        else:
            agg_query = aggregate_metric_table_by_day(
                table_path,
                bq_project="",
                date_col=date_col,
                user_col=user_col,
                metric_table=self,
                user_group_cols=user_group_cols,
                cumulative=cumulative,
                verbose=False,
                dry_run=True,
                cumulative_trend=cumulative_trend,
                **kwargs,
            )
            calculators.append(
                Calculator(
                    agg_query,
                    analysis_func=multiple_treatment_ids(compare_metric_table),
                    func_args=dict(
                        metric_table=self,
                        group_vars=user_group_cols,
                    ),
                    metric_table=self,
                    bq_dialect="standard",
                )
            )
            if self.cuped and cumulative:  # only support cumulative for CUPED
                metric_table_cuped = copy.deepcopy(self)
                metric_table_cuped.metrics = metric_table_cuped.get_metric_objects_by_dist('cont')

                cuped_agg_query = aggregate_metric_table_cuped_cumulative(
                    table_path,
                    date_col=date_col,
                    user_col=user_col,
                    metric_table=metric_table_cuped,
                    user_group_cols=user_group_cols,
                    **kwargs,

                )

                calculators.append(
                    Calculator(
                        cuped_agg_query,
                        analysis_func=multiple_treatment_ids(compare_metric_table),
                        func_args=dict(
                            metric_table=self,
                            group_vars=user_group_cols,
                        ),
                        metric_table=self,
                        bq_dialect="standard"
                    )
                )

        return calculators


class Calculator(object):
    def __init__(self, query, analysis_func, func_args, metric_table, query_results=None, bq_dialect="legacy"):
        """
        The class works in between Report and MetricTable.
        It holds the query to be run for the analysis (cumulative or not, with or without
        user breakdown), the query results, and the logic of turning the query results into analysis results.

        Parameters
        ----------
        query : str
        The query used to populate result_df
        analysis_func : callable
        The function to be called that returns the analysis result for a given control_id, and a list of treatment_ids
        analysis_func(self.query_results, control_id, treatment_ids, **self.func_args)
        func_args : dict
        keyword arguments to be passed to analysis_func. This could be the metric name, the bootstrap iteractions
        for example.
        metric_table: BaseMetricTable
        query_results : pandas.DataFrame
        The results of the query
        bq_dialect : str
        "standard" or "legacy"
        """
        self.query = query
        self.bq_dialect = bq_dialect
        self.analysis_func = analysis_func
        self.func_args = func_args
        self.metric_table = metric_table
        self.query_results = query_results

    def get_analysis_results(self, control_id, treatment_ids):
        if self.query_results is not None:
            return self.analysis_func(self.query_results, control_id, treatment_ids, **self.func_args)
        else:
            raise AttributeError("query_results has not been populated.")

    @property
    def stats(self):
        """For backwards compatibility"""
        return self.query_results


def abtest_metrics_sql_helper(table_ds,
                              start_date=None,
                              end_date=None,
                              dates=None,
                              columns=None,
                              select_star=True,
                              group_by=None,
                              bq_dialect='legacy'):
    """Construct the SQL statement that adds ts columns to a ds
    partitioned table.
    This is useful for tables without any datetime column.
    The SQL statement can be used in the FROM statement of a SQL query that
    join with A/B test mapping tables on ts.
    Parameters
    ----------
    table_ds : str or unicode
        Name of the date partitioned table, e.g
        sc-portal:abtest_metric_extraction_v2.MY_STORY_SNAP_POST____
    dates : list of str or list of datetime.datetime
        The dates with which to expand the table
    start_date : str or datetime.datetime
    end_date : str or datetime.datetime
    columns : list of str or list of unicode
        The list of columns to keep
    select_star : bool
        If True, wrap the FROM statement with SELECT * FROM so that the
        final string is a complete SQL query
    group_by: str or unicode
        group by x (user_id or something else) to sum up the metric value
        if x == user_id, then it makes sure each user has one entry for one metric
    Returns
    -------
    sql : str
        The SQL statement
    Examples
    --------
    >>> sql = abtest_metrics_sql_helper(
    ...     "sc-portal:abtest_metric_extraction_v2.MY_STORY_SNAP_POST____",
    ...     start_date='2016-01-02', end_date='2016-01-03',
    ...     columns=['ghost_user_id', 'metric1', 'another_metric'],
    ...     select_star=True
    ... )
    >>> print(sql)
    SELECT * FROM
        (
          SELECT
            TIMESTAMP("2016-01-02 00:00:00") AS ts,
            ghost_user_id, metric1, another_metric
          FROM
            [sc-portal:abtest_metric_extraction_v2.MY_STORY_SNAP_POST____20160102]
        ),
        (
          SELECT
            TIMESTAMP("2016-01-03 00:00:00") AS ts,
            ghost_user_id, metric1, another_metric
          FROM
            [sc-portal:abtest_metric_extraction_v2.MY_STORY_SNAP_POST____20160103]
        )
    """

    if bq_dialect == 'legacy':
        single_day_sql = """
          (
            SELECT
              TIMESTAMP("{ds} 00:00:00") AS ts,
              {columns}
            FROM
              [{table_ds}{ds_nosep}]
            {group_by}
          )
          """
    else:
        single_day_sql = """
          (
            SELECT
              TIMESTAMP("{ds} 00:00:00") AS ts,
              {columns}
            FROM
              {table_ds}{ds_nosep}
            {group_by}
          )"""

    if columns is None:
        cols = '*'
    else:
        cols = ', '.join(columns)

    if dates:
        table_dates = [d.strftime(DATE_FORMAT_W_DASH) for d in pd.to_datetime(dates)]
    elif start_date is not None and end_date is not None:
        table_dates = pd.date_range(start_date, end_date).strftime(DATE_FORMAT_W_DASH)
    else:
        raise ValueError('Either provide a list of dates as a list or both'
                         ' start and end dates')

    tables = [
        single_day_sql.format(
            ds=d,
            ds_nosep=d.replace('-', ''),
            columns=cols,
            table_ds=table_ds,
            group_by='' if group_by is None else 'GROUP BY ' + group_by
        )
        for d in table_dates
    ]

    if bq_dialect == 'legacy':
        sql = ','.join(tables)
    else:
        sql = '\nUNION ALL\n'.join(tables)

    if select_star:
        sql = "SELECT * FROM {subquery}".format(subquery=sql)
    return sql


def _extract_sql_field_name_from_select(clause):
    """ Extract the field name defined in the SQL clause using regular express

    Parameters
    ----------
    clause : str
        The sql clause in SELECT

    Returns
    -------
    str
        The sql field

    >>> print(_extract_sql_field_name_from_select("SUM(a > 0, 1, 0) AS unique_users"))
    unique_users

    >>> print(_extract_sql_field_name_from_select("COUNT(*) events "))
    events

    >>> print(_extract_sql_field_name_from_select(" event_time_hour"))
    event_time_hour

    >>> print(_extract_sql_field_name_from_select("CASE WHEN a > 0 THEN true \\n WHEN a = 0 THEN false\\n END cat"))
    cat
    """
    match = re.search(r' AS (?P<name>\w+)\s*$', clause, flags=re.IGNORECASE)
    if match is None:
        match = re.search(r' (?P<name>\w+)\s*$', clause, flags=re.IGNORECASE)
    if match is None:
        match = re.search(r'^\s*(?P<name>\w+)\s*$', clause, flags=re.IGNORECASE)
    return match.group('name')
