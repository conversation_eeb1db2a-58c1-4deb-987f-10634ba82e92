"""parsing the metric config stored in bigquery table


banjo.teams_ab_metrics snapshots most banjo.teams a/b metrics in a bigquery table.
This module provide utils that reads from that table and returns banjo.abtest.metric.MetricTable instances
"""
import logging
import pandas as pd

import banjo.abtest.metric
import banjo.utils

from functools import lru_cache
from typing import List, Optional


def get_metric_table_from_banjo_config(
        metric_name: str,
        display_name: str,
        metric_distribution: str,
        sql_template: str,
        bq_dialect: str,
        start_date: str,
        end_date: str,
        ) -> Optional[banjo.abtest.metric.MetricTable]:
    """turn data (one row) in vellum-sc.banjo_abtest_metrics.banjo_abtest_metrics_20
    into metric tables


    Parameters
    ----------
    metric_name
    display_name
    metric_distribution
    sql_template
    bq_dialect
    start_date
    end_date

    Returns
    -------

    """
    def sql_func(s, e):
        return sql_template.format(start_date=s, end_date=e)

    # dry-run SQL against bigquery?
    # todo(xhe): handle ratio later

    if metric_distribution == "ratio":
        logging.warning(f"ratio metric {metric_name} skipped as ratios are not supported")
        return

    if bq_dialect == "legacy":
        # legacy does not support callable
        sql = sql_func(start_date, end_date)
        sql_callable = None
    else:
        sql = None
        sql_callable = sql_func

    return banjo.abtest.metric.MetricTable(
        sql=sql,
        sql_callable=sql_callable,
        metrics=[
            banjo.abtest.metric.Metric(
                metric_name,
                name=display_name,
                dist=metric_distribution
            )
        ],
        bq_dialect="legacy" if bq_dialect == "legacy" else "standard",
        quantile_metrics=metric_distribution == "quantile"
    )


@lru_cache(maxsize=2)
def read_config_from_bq() -> pd.DataFrame:
    return banjo.utils.gbq.read_gbq(
        """
        SELECT
          module_name,
          object_name,
          metric_table_name,
          metric_name,
          MAX(display_name) AS display_name,
          MAX(metric_distribution) AS metric_distribution,
          MAX(sql_template) AS sql_template,
          MAX(bq_dialect) AS bq_dialect
        FROM
          `vellum-sc.banjo_abtest_metrics.banjo_abtest_metrics_20*`
        WHERE
          _TABLE_SUFFIX = (
          SELECT
            SUBSTR(MAX(table_id), -6) AS latest_date_str
          FROM
            `vellum-sc.banjo_abtest_metrics.__TABLES_SUMMARY__`
          WHERE
            REGEXP_CONTAINS(table_id, '^banjo_abtest_metrics_[0-9]{8}$') )
          AND metric_distribution <> "ratio"
          GROUP BY 
            module_name, object_name, metric_table_name, metric_name
        """,
        project_id="sc-bq-gcs-billingonly",
        dialect="standard",
    )


def get_filtered_metric_tables_from_bigquery_metric_config(
        metric_config_ids: List[str],
        start_date,
        end_date,
) -> List[banjo.abtest.metric.MetricTable]:
    """The function most likely used in the notebook

    Given a list of config identifiers, this returns a list of MetricTable

    config id examples (see logic below in the function body)
    ```
    [
        "banjo.teams.product.lens_metrics__lens_sponsored_active_day_metrics__lens_sponsored_active_day_metrics___sponsored_lens_long_swipe_3_day",
        "banjo.teams.product.sticker_camera__chat_sticker_funnel_metrics_old__chat_sticker_funnel_metrics_old__chat_sticker_backend_search_from_birthday_pill_uu",
    ]
    ```

    """
    banjo_metrics_repo_raw = read_config_from_bq()
    banjo_metrics_repo_raw["metric_config_id"] = (
            banjo_metrics_repo_raw["module_name"]
            + "__"
            + banjo_metrics_repo_raw["object_name"]
            + "__"
            + banjo_metrics_repo_raw["metric_table_name"]
            + "__"
            + banjo_metrics_repo_raw["metric_name"]
    )

    filtered_config = banjo_metrics_repo_raw.loc[
      banjo_metrics_repo_raw.metric_config_id.isin(metric_config_ids), :
    ]

    if filtered_config is None or filtered_config.empty:
        return []

    metric_tables = [
        get_metric_table_from_banjo_config(
            start_date=start_date,
            end_date=end_date,
            metric_name=config.metric_name,
            display_name=config.display_name,
            metric_distribution=config.metric_distribution,
            sql_template=config.sql_template,
            bq_dialect=config.bq_dialect,
        )
        for _, config in filtered_config.iterrows()
    ]

    return [mt for mt in metric_tables if mt is not None]
