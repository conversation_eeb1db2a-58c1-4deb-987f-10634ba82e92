# -*- coding: utf-8 -*-

"""Plot the result of a A/B testing
"""
from __future__ import division, unicode_literals, print_function
from builtins import str

from datetime import timedelta
import logging
import matplotlib.dates as mdates
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import re
import seaborn as sns
import six
import plotly.graph_objects as go
from plotly.subplots import make_subplots

from banjo.utils.printer import IPythonReportPrinter, HTMLFileReportPrinter
import banjo.abtest.metric
from .analysis import hte_types


__all__ = ['trendplot', 'forestplot', 'deltatrendplot', 'tabulate', 'pivot_table',
           'get_formatted_pvalue', 'timetrendplot', 'infer_dt_tick_gap', 'stylized_by_p', 'highlight_values_with_color',
           'user_hte_plot', 'field_hte_plot', 'field_hte_plot_quantile_stats', 'plot_tstat_ab_tree',
           'hte_tree_table_view'
           ]

MARKERS = ['^', 'v', 'D', 's', '*', 'o'] * 10

HUMAN_NAMES = {'avg_treatment': 'Average, treatment',
               'avg_control': 'Average, control'}

P_VALUE_CUTPOINTS = [-np.inf, 1e-4, 1e-3, 0.01, 0.05, 0.1, 1]
P_VALUE_LABELS = ['< 0.0001', '< 0.001', '< 0.01',
                  '< 0.05', '< 0.1', '≥ 0.1']

COLOR_MAP = {
    "red": {
        "< 0.0001": "#B10101",
        "< 0.001": "#D01212",
        "< 0.01": "#F56666",
        "< 0.05": "#F89999",
    },
    "green": {
        "< 0.0001": "#006400",
        "< 0.001": "#1D831D",
        "< 0.01": "#66B366",
        "< 0.05": "#99CC99",
    },
    "gray": {
        "< 0.0001": "#333333",
        "< 0.001": "#555555",
        "< 0.01": "#777777",
        "< 0.05": "#999999",
    },
}

ALPHA_LEVEL = 0.05

BH_NONSIG = "bh_nonsig"
BH_SIG = "bh_sig"
BH_TABLE_FOOTNOTE = (
    "BH procedure has been applied to control the FDR. "
    "The cell highlight is removed for cells with small p-values "
    "but are not discoveries/significant after the BH procedure. "
)
# todo: print out the FDR in the footnote and hyperlink to user guide.


logger = logging.getLogger(__name__)


def _trendplot(x, y, **kwargs):
    ax = plt.gca()
    plt.plot_date(to_pydt(x), y, linestyle='-', **kwargs)


def trendplot(data, date, measures,
              metric, xlabel='Date', ylabel='Value',
              title='A/B testing',
              size=3, aspect=2,
              facet_col=None,
              y_inc_0=True,
              **kwargs
              ):
    """
    Parameters
    ----------
    data
    date
    measures
    metric
    xlabel
    ylabel
    title
    size
    aspect
    facet_col
    y_inc_0 : bool
    Returns
    -------
    """
    df = data.assign(__ts=pd.to_datetime(data[date]))
    dt_tick_gap = infer_dt_tick_gap(df.__ts)
    cols = ['__ts', metric]
    id_cols = [metric, '__ts']
    if facet_col is not None:
        cols.append(facet_col)
        id_cols.append(facet_col)
    cols.extend(measures)
    df_melt = pd.melt(
        df[cols],
        id_vars=id_cols,
        var_name='variable',
        value_name='value'
    )

    df_melt['variable'] = df_melt['variable'].replace(HUMAN_NAMES)

    g = sns.FacetGrid(
        df_melt,
        row=metric,
        col=facet_col,
        height=size,
        aspect=aspect,
        hue_kws={"marker": MARKERS},
        hue='variable',
        sharey=False,
        xlim=(to_pydt(df_melt.__ts).min() - dt_tick_gap,
              to_pydt(df_melt.__ts).max() + dt_tick_gap,
              ),
        **kwargs
    )
    _ = g.map(_trendplot, '__ts', 'value')
    _ = g.add_legend(title='')
    _ = g.set_axis_labels(xlabel, ylabel)
    _ = g.fig.subplots_adjust(bottom=-0.2,
                              hspace=_get_margin_between_facets(g.fig, 0.7))
    _ = g.fig.suptitle(title, y=_get_title_y_pos(g.fig), fontsize=18)
    x_ticks = thin_x_ticks(sorted(np.unique(to_pydt(df_melt.__ts))))
    g.axes.flat[0].set_xticks(x_ticks)
    for ax in g.axes.flat:
        dt_format = '%m/%d' if dt_tick_gap >= timedelta(days=1) else '%m/%d %H:%M'
        ax.xaxis.set_major_formatter(mdates.DateFormatter(dt_format))
        _ = plt.setp(ax.get_xticklabels(), visible=True)
        if y_inc_0:
            # make sure the y axis starts from 0
            ylim = ax.get_ylim()
            new_ylim = [min([0, ylim[0]]),
                        max([0, ylim[1]])
                        ]
            ax.set_ylim(new_ylim)
    plt.tight_layout()
    plt.show()
    return g


def timetrendplot(
        data, date,
        xlabel='Date', ylabel='Value',
        title='Time Trend',
        y_inc_0=True,
        **kwargs):
    df = data.assign(__ts=pd.to_datetime(data[date]))
    dt_tick_gap = infer_dt_tick_gap(df.__ts)
    g = sns.FacetGrid(
        df,
        hue_kws={"marker": MARKERS},
        xlim=(to_pydt(df.__ts).min() - dt_tick_gap,
              to_pydt(df.__ts).max() + dt_tick_gap,
              ),
        **kwargs
    )
    _ = g.map(_trendplot, '__ts', 'value')
    _ = g.add_legend(title='')
    _ = g.set_axis_labels(xlabel, ylabel)
    _ = g.fig.subplots_adjust(bottom=-0.2,
                              hspace=_get_margin_between_facets(g.fig, 0.7))
    _ = g.fig.suptitle(title, y=_get_title_y_pos(g.fig), fontsize=18)
    x_ticks = thin_x_ticks(sorted(np.unique(to_pydt(df.__ts))))
    g.axes.flat[0].set_xticks(x_ticks)
    for ax in g.axes.flat:
        _ = plt.setp(ax.get_xticklabels(), visible=True)
        if y_inc_0:
            # make sure the y axis starts from 0
            ylim = ax.get_ylim()
            new_ylim = [min([0, ylim[0]]),
                        max([0, ylim[1]])
                        ]
            ax.set_ylim(new_ylim)
    plt.tight_layout()
    plt.show()
    return g


def _deltatrendplot(x, y, low, high, connected, ref=0.0, **kwargs):
    ax = plt.gca()
    ls = '-' if connected else ''
    ax.axhline(ref, linestyle='-', color='firebrick', linewidth=0.75, alpha=0.25)
    ax.vlines(to_pydt(x), low, high, alpha=0.5, zorder=5, color=kwargs.get('color'))
    ax.plot_date(to_pydt(x), y, linestyle=ls, **kwargs)


def deltatrendplot(data, date, point, low, high,
                   metric, connected=True, xlabel='Date',
                   ylabel=None,
                   title='A/B testing',
                   size=3, aspect=2,
                   facet_col=None,
                   facet_hue=None,
                   **kwargs
                   ):
    """
    Parameters
    ----------
    data
    date
    point
    low
    high
    metric
    connected
    xlabel
    ylabel
    title
    size
    aspect
    facet_col
    facet_hue
    Returns
    -------
    """
    if ylabel is None:
        ylabel = 'Difference'
        if 'pct' in point:
            ylabel += ' (%)'

    df = data.assign(__ts=pd.to_datetime(data[date]))
    dt_tick_gap = infer_dt_tick_gap(df.__ts)
    ts_min = to_pydt(df.__ts).min()
    ts_max = to_pydt(df.__ts).max()
    x_ticks = thin_x_ticks(sorted(np.unique(to_pydt(df.__ts))))

    # If facet_hue is enabled, we should jitter the date a bit to avoid overlapping.
    # the amount of shift should be based on the overall range of the data ts_max - ts_min
    # and is deterministic based on the facet_hue value. We should order facet_hue values
    # and center the median (i.e. no shift) and shift the rest based on the order to
    # the left and to the right
    if facet_hue is not None:
        unique_values = df[facet_hue].dropna().sort_values().unique()
        shift = 0.01 * (ts_max - ts_min)
        shift_map = {value: shift * (i - len(unique_values) // 2) for i, value in enumerate(unique_values)}
        df['__ts'] = df['__ts'] + pd.to_timedelta(df[facet_hue].map(shift_map), unit='D')

    g = sns.FacetGrid(
        df,
        row=metric,
        col=facet_col,
        hue=facet_hue,
        height=size,
        aspect=aspect,
        sharey=False,
        xlim=(ts_min - dt_tick_gap,
              ts_max + dt_tick_gap,
              ),
        legend_out=False,
        **kwargs
    )
    line_color = 'k' if facet_hue is None else None
    _ = g.map(_deltatrendplot, '__ts', point, low, high, connected=connected, zorder=10,
              linewidth=2.0, markersize=8, color=line_color)
    _ = g.set_axis_labels(xlabel, ylabel)
    _ = g.fig.subplots_adjust(bottom=-0.2, hspace=_get_margin_between_facets(g.fig, 0.7))
    _ = g.fig.suptitle(title, y=_get_title_y_pos(g.fig), fontsize=18)

    g.axes.flat[0].set_xticks(x_ticks)
    for ax in g.axes.flat:
        dt_format = '%m/%d' if dt_tick_gap >= timedelta(days=1) else '%m/%d %H:%M'
        ax.xaxis.set_major_formatter(mdates.DateFormatter(dt_format))

        ylim = ax.get_ylim()
        # Center Y-axis at 0
        ref = 0
        new_ylim = [
            min([ref / 2 - ylim[1], ylim[0]]),
            max([ref / 2 - ylim[0], ylim[1]])
        ]
        ax.set_ylim(new_ylim)

        _ = plt.setp(ax.get_xticklabels(), visible=True)
        if facet_hue is not None:
            # remove duplicated legends
            handles, labels = ax.get_legend_handles_labels()
            handle_list, label_list = [], []
            for handle, label in zip(handles, labels):
                if label not in label_list:
                    handle_list.append(handle)
                    label_list.append(label)
            ax.legend(handle_list, label_list)
    plt.tight_layout()
    plt.show()
    return g


def _forest_plot(x, y, ref=None, **kwargs):
    plt.scatter(x, y, **kwargs)
    if ref is not None:
        plt.axvline(ref, linestyle='-', color='firebrick', zorder=1,
                    linewidth=0.75, alpha=0.75)
        ax = plt.gca()
        xlim = ax.get_xlim()
        new_xlim = [min([ref / 2 - xlim[1], xlim[0]]),
                    max([ref / 2 - xlim[0], xlim[1]])
                    ]
        ax.set_xlim(new_xlim)


def forestplot(df,
               y='metric',
               point='pct_diff',
               low='pct_diff_lo',
               high='pct_diff_hi',
               ref_value=0,
               # pvalue=None,
               xlabel='Difference (%)',
               ylabel='Metric',
               title='A/B testing',
               sort_by_value=False,
               size=3,
               aspect=2,
               facet_col=None,
               facet_row=None,
               facet_col_wrap=None,
               **kwargs
               ):
    if facet_col_wrap == 1:
        # seems to be a bug in seaborn
        facet_col_wrap = None
    if sort_by_value:
        sort_by = [point]
        if facet_col is not None:
            sort_by = [facet_col] + sort_by
        if facet_row is not None:
            sort_by = [facet_row] + sort_by
        df = df.sort_values(sort_by)
    ys = list(reversed(df[y].unique()))
    df = df.assign(_ypos=df[y].replace(ys, np.arange(len(ys))))
    hue = None
    point_color = 'k'
    #     if pvalue is not None:
    #         df['_pval'] = pd.cut(df[pvalue],
    #                              P_VALUE_CUTPOINTS, labels=P_VALUE_LABELS)
    #         hue='_pval'
    #         point_color=None

    g = sns.FacetGrid(data=df,
                      col=facet_col, row=facet_row, col_wrap=facet_col_wrap,
                      palette="Blues",
                      hue=hue,
                      height=size,
                      aspect=aspect,
                      **kwargs)
    _ = g.map(plt.hlines, '_ypos', low, high, color='gray', zorder=5)
    _ = g.map(_forest_plot, point, '_ypos', ref=ref_value,
              color=point_color,
              marker='o', s=60, edgecolor='k', linewidth=0.8, zorder=10)

    _ = [ax.set_xlabel(xlabel) for ax in g.axes.flat]
    _ = g.axes.flat[0].set_ylim((-1, df['_ypos'].max() + 1))
    _ = g.axes.flat[0].set_yticks(np.arange(df['_ypos'].max() + 1))
    _ = g.axes.flat[0].set_yticklabels(ys)
    _ = g.fig.subplots_adjust(bottom=-0.2,
                              hspace=_get_margin_between_facets(g.fig, 0.7))
    _ = g.fig.suptitle(title, y=_get_title_y_pos(g.fig), fontsize=18)
    #     if pvalue is not None:
    #         _ = g.add_legend(title='P-value')
    for ax in g.axes.flat:
        _ = ax.set_ylabel(ylabel)
        _ = plt.setp(ax.get_xticklabels(), visible=True)
    # seaborn 0.8.1 with matplotlib 0.2.1 displays y labels for non-first columns
    # hide it
    if len(g.axes.shape) > 1:
        for column in g.axes[:, 1:]:
            for ax in column:
                _ = plt.setp(ax.get_yticklabels(), visible=False)
    plt.show()
    return g


def highlight_values_with_color(values, color):
    def func(column):
        return ['background: {};'.format(color) if x in values else '' for x in column]
    return func


def user_hte_plot(df,
                 metric='metric',
                 plot_width=20,
                 plot_length=8,
                 capsize=4,
                 name_length_limit=25,
                 num_group_limit=15,
                 interaction_p_value_threshold=0.001,
                 p_value_threshold=0.01,
                 ):
    """ Use this function to plot conditional treatment effect in each subgroup.
    It takes a data frame at breakdown value level for 1 metric, 1 treatment_id and 1 breakdown dimension.
    The input data frame must have columns: 'breakdown_dimensions', 'breakdown_values', 'metric', 'treatment_id',
    'pct_diff', 'pct_diff_hi', 'pct_diff_lo', 'p_value', 'interaction_p_value', 'count_control', 'count_treatment'.
    Parameters
    ----------
    name_length_limit: if too long name, show only the first x letters
    num_group_limit: if too many groups, show only the top x groups based on group size
    interaction_p_value_threshold: threshold to determine whether there is sig HTE
    p_value_threshold: threshold to categorize types of HTE if detected
    show_hte_type: if True, show types of HTE (if detected) in plot title
    ----------
    """

    df = df.copy()

    df['group_size'] = df['count_treatment'] + df['count_control']
    df['label_in_plot'] = df["breakdown_values"].astype(str)

    if len(df) > num_group_limit:
        df = df[df['group_size'].rank(method='first', ascending=False) <= num_group_limit].copy()
        print(
            "Too many groups, show only the top {} groups based on group sample size.".format(num_group_limit)
        )

    if len(df) > 5:  # further adjust name_length_limit if num groups > x to avoid overlapping labels
        name_length_limit = max(22 - len(df), 6)
    df.loc[df['label_in_plot'].str.len() > name_length_limit, 'label_in_plot'] = \
        df["breakdown_values"].str[:name_length_limit - 2].astype(str) + '..'

    # if after truncating, some groups have the same labels, add 2, 3, 4...suffix
    df["row_num"] = df.reset_index(None, drop=True).index
    df["dup_label_rank"] = df.groupby('label_in_plot')['row_num'].rank(method='first', ascending=True).astype(int)
    df.loc[df['dup_label_rank'] > 1, 'label_in_plot'] = \
        df['label_in_plot'].astype(str) + df['dup_label_rank'].astype(str)

    # if truncation happens, print out original names
    if any(df['label_in_plot'] != df["breakdown_values"]):
        print("In order to fit in the plot, some group names are truncated:")
        for label_in_plot in df[df['label_in_plot'] != df["breakdown_values"]]['label_in_plot']:
            print("{}: {}".format(label_in_plot,
                                  df[df['label_in_plot'] == label_in_plot]["breakdown_values"].values[0])
                  )
    breakdown_dimensions = df["breakdown_dimensions"].iloc[0]

    # sort table before plotting
    df = df.sort_values("breakdown_values").reset_index(None, drop=True)

    # now start plotting, first define plot title
    interaction_p_value = df['interaction_p_value'].iloc[0]
    hte_type = df['hte_type'].iloc[0]
    if abs(interaction_p_value) >= interaction_p_value_threshold:
        plot_title = 'No Significant HTE'
    else:
        plot_title = hte_types[hte_type]

    plot_title = plot_title + ' (interaction_p_value={:.4f})'.format(interaction_p_value)

    metric = df[metric].iloc[0]
    treatment_id = df["treatment_id"].iloc[0]
    plot_subtitle = "Metric = {}; Treatment = {}".format(metric, treatment_id)

    # print barplot
    fig, (ax0, ax1) = plt.subplots(2, 1, gridspec_kw={'height_ratios': [3, 1]}, figsize=(plot_width, plot_length))
    plt.subplots_adjust(hspace=0)
    ax0.errorbar(df['label_in_plot'], df['pct_diff'],
                 yerr=(df['pct_diff_hi'] - df['pct_diff_lo']) / 2,
                 alpha=1,
                 capsize=capsize,
                 marker='o',
                 ms=15,
                 ls='none'
    )
    ax0.set_ylabel('Difference (%)', size=16)
    ax0.set_title(plot_title, size=16)
    ax0.figure.suptitle(plot_subtitle, size=16, y=0.98)
    ax0.yaxis.grid(True)
    scale_lim = max(max(abs(df['pct_diff_hi'].replace([np.nan, np.inf, -np.inf], 0))),
                    max(abs(df['pct_diff_lo'].replace([np.nan, np.inf, -np.inf], 0)))) * 1.1
    ax0.set_ylim(-scale_lim, scale_lim)  # to put y = 0 in the middle of the y axis
    ax0.hlines(0, -100, 100, 'k')

    rects = ax1.bar(df['label_in_plot'], df['group_size'], label=df['group_size'])
    ax1.set_ylabel('User Count', size=16)
    ax1.set_xlabel(breakdown_dimensions, size=16)
    ax0.set_xlim(ax1.get_xlim())
    scale_lim = max(df['group_size']) * 1.15
    ax1.set_ylim(0, scale_lim)
    plt.show()

def field_hte_plot(df,
                   plot_width=1000,
                   plot_length=500,
                   title_size=16,
                   metric_name="Metric",
                   avg_control="avg_control",
                   avg_treatment="avg_treatment",
                   pct_diff="pct_diff",
                   pct_diff_lo="pct_diff_lo",
                   pct_diff_hi="pct_diff_hi",
                   ):
    """ Use this function to plot the distribution of event counts (e.g., story post) in each metric field breakdown
    (e.g., media type). It takes a data frame at metric_breakdown_values level for 1 metric, 1 treatment_id and 1
    metric_breakdown_dimensions.
    The input data frame must have columns: 'metric_breakdown_hte_tested', 'metric_breakdown_dimensions',
    'metric_breakdown_values', 'metric', 'avg_treatment', 'avg_control', 'treatment_id', 'control_id',
    'pct_diff', 'pct_diff_lo, 'pct_diff_hi'.
    """
    # keep only 'metric_breakdown_hte_tested'
    df_plot = df[df["metric_breakdown_hte_tested"]]

    if df_plot.empty:
        return

    # adjust plot length based on num of groups
    plot_length += max(0, (len(df_plot) - 4) * 20)

    # customize toolbar
    config = {'displaylogo': False,
              'displayModeBar': True,
              'modeBarButtonsToRemove': ['lasso2d', 'select2d', 'pan2d', 'zoom2d']
              }

    # prepare data for plot 1: pct_diff with confidence interval
    fields = df_plot["metric_breakdown_values"].tolist()
    mean = df_plot[pct_diff].tolist()
    error = ((df_plot[pct_diff_hi] - df_plot[pct_diff_lo])/2).tolist()

    # prepare data for plot 2: event count distribution
    d1 = (df_plot[avg_control] / df_plot[avg_control].sum() * 100).to_list()
    d2 = (df_plot[avg_treatment] / df_plot[avg_treatment].sum() * 100).to_list()
    # calculate pct diff
    d3 = [(x2 / x1 - 1) * 100 for x1, x2 in zip(d1, d2)]

    # prepare titles, labels, legend
    treatment_id = df_plot["treatment_id"].iloc[0]
    control_id = df_plot["control_id"].iloc[0]
    field_dimension = df_plot['metric_breakdown_dimensions'].iloc[0]
    hte_outcome = df_plot["metric_breakdown_hte_outcome"].iloc[0]
    chisq_dist = df_plot["chisq_dist"].iloc[0]
    subplot1_title = "Treatment Effect <br>{} vs {}".format(control_id, treatment_id)
    if len(subplot1_title) > 65:
        subplot1_title = "Treatment Effect <br>{} vs <br>{}".format(control_id, treatment_id)
    subplot2_title = "Event Counts Distribution"
    # todo: add HTE decision back once we are satisfied with the criteria.
    plot_title = "{} by {} (chisq_dist={:.4f})".format(metric_name, field_dimension, chisq_dist)
    if hte_outcome in hte_types.keys():
        title_color = "red"
    else:
        title_color = "black"
    # adjust title font size if title is long
    if len(plot_title) > 110:
        title_size -= min(4, (len(plot_title) - 110)/10)

    # set up plots structure. we are going to have 2 subplots.
    fig = go.Figure()
    fig = make_subplots(rows=1,
                        cols=2,
                        start_cell="bottom-left",
                        subplot_titles=(subplot1_title, subplot2_title),
                        )

    # draw plot 1
    fig.add_trace(go.Scatter(
        y=fields,
        x=mean,
        mode='markers',
        showlegend=False,
        error_x=dict(type='data', array=error)),
        row=1,
        col=1,
    )
    # further update plot 1
    # reverse y axis order
    fig.update_yaxes(autorange="reversed", row=1, col=1)
    # put 0 in the middle
    scale_lim = max(max(abs(df_plot['pct_diff_hi'])), max(abs(df_plot['pct_diff_lo']))) * 1.1
    fig.update_xaxes(title_text='Difference (%)', range=[-scale_lim, scale_lim], row=1, col=1)

    # draw plot 2
    fig.add_trace(go.Bar(
        y=fields,
        x=d1,
        name=control_id,
        marker_color='#F8766D',
        orientation='h'),
        row=1,
        col=2,
    )
    fig.add_trace(go.Bar(
        y=fields,
        x=d2,
        name=treatment_id,
        marker_color='#00BFC4',
        orientation='h',
        text=d3),
        row=1,
        col=2,
    )
    # further update plot 2
    # add pct_diff label to treatment bar
    fig.update_traces(selector=dict(name=treatment_id),
                      texttemplate='diff: ' + '%{text:,.2f}' + '%',
                      textposition='auto',
                      row=1,
                      col=2)
    # reverse y axis order and put label to right side
    fig.update_yaxes(autorange="reversed", side="right", row=1, col=2)
    # set x axis range
    fig.update_xaxes(title_text="%", range=[0, 100], row=1, col=2)

    # adjust overall plot structure
    fig.update_layout(title=dict(text=plot_title, x=0.5, y=0.93, xref='paper',
                                 font=dict(color=title_color, size=title_size)),
                      yaxis_type='category',
                      width=plot_width,
                      height=plot_length,
                      legend=dict(orientation="v", yanchor="bottom", y=-0.4, xanchor="left", x=0.5),
                      margin=dict(t=150),
                      )

    fig.show(config=config)
    return fig


def field_hte_plot_quantile_stats(df,
                                  plot_width=1000,
                                  plot_length=400,
                                  title_size=16,
                                  metric_name="Metric",
                                  ):
    """ Use this function to plot quantile stats in each metric field breakdown.
    It takes a data frame at metric_breakdown_values level for 1 metric, 1 treatment_id and 1
    metric_breakdown_dimensions.
    The input data frame must have columns: 'metric_breakdown_hte_tested', 'metric_breakdown_dimensions',
    'metric_breakdown_values', 'metric', 'avg_volume_treatment', 'avg_volume_control', 'treatment_id', 'control_id',
    'pct_diff', 'pct_diff_lo, 'pct_diff_hi'.
    """
    # keep only 'metric_breakdown_hte_tested'
    df_plot = df[df["metric_breakdown_hte_tested"]]

    if df_plot.empty:
        return

    # adjust plot length based on num of groups
    plot_length += max(0, (len(df_plot) - 4) * 20)

    # customize toolbar
    config = {'displaylogo': False,
              'displayModeBar': True,
              'modeBarButtonsToRemove': ['lasso2d', 'select2d', 'pan2d', 'zoom2d']
              }
    # prepare data for plot
    fields = df_plot["metric_breakdown_values"].tolist()
    mean = df_plot["pct_diff"].tolist()
    error = ((df_plot["pct_diff_hi"] - df_plot["pct_diff_lo"]) / 2).tolist()

    # prepare titles, labels, legend
    treatment_id = df_plot["treatment_id"].iloc[0]
    control_id = df_plot["control_id"].iloc[0]
    field_dimension = df_plot['metric_breakdown_dimensions'].iloc[0]
    hte_outcome = df_plot["metric_breakdown_hte_outcome"].iloc[0]
    chisq_dist = df_plot["chisq_dist"].iloc[0]
    plot_title = "{} by {} ({} chisq_dist={:.4f})".format(metric_name, field_dimension, hte_outcome, chisq_dist)
    if hte_outcome in hte_types.keys():
        title_color = "red"
    else:
        title_color = "black"
    # adjust title font size if title is long
    if len(plot_title) > 110:
        title_size -= min(4, (len(plot_title) - 110)/10)
    # draw plot
    fig = go.Figure()
    fig.add_trace(go.Scatter(
        y=fields,
        x=mean,
        mode='markers',
        showlegend=False,
        error_x=dict(type='data', array=error)),
    )
    # reverse y axis order
    fig.update_yaxes(autorange="reversed")
    # put 0 in the middle
    scale_lim = max(max(abs(df_plot['pct_diff_hi'])), max(abs(df_plot['pct_diff_lo']))) * 1.1
    fig.update_xaxes(title_text='Difference (%)', range=[-scale_lim, scale_lim])
    # adjust overall plot structure
    fig.update_layout(title=dict(text=plot_title, x=0.5, xref='paper', font=dict(color=title_color, size=title_size)),
                      yaxis_type='category',
                      width=plot_width,
                      height=plot_length,
                      legend=dict(orientation="h", yanchor="bottom", y=-0.35, xanchor="left", x=0.7),
                      )

    fig.show(config=config)
    return fig

def get_formatted_pvalue(pval):
    """ Translate the numerical p-values into strings, e.g. < 0.1, < 0.0001
    Parameters
    ----------
    pval : pandas.Series or int or list
        A pandas series or a list of numeric values in the range of 0-1
    Returns
    -------
    """
    series = pd.Series(pval).astype(float)
    formatted = pd.cut(series, P_VALUE_CUTPOINTS, labels=P_VALUE_LABELS)
    if six.PY2:
        formatted = formatted.astype(unicode)
    else:
        formatted = formatted.astype(str)
    if isinstance(pval, pd.Series):
        return formatted
    else:
        lst = list(pd.cut(series, P_VALUE_CUTPOINTS, labels=P_VALUE_LABELS))
        if isinstance(pval, list):
            return lst
        else:
            return lst[0]


def _get_series_for_styles(df: pd.DataFrame, pvalue_formatted: str, use_bh_significance: bool) -> pd.Series:
    """Take a `banjo.abtest.report.Report` result table and return a pd.Series of strings that can be used
    to determine cell css styles"""
    s = (
            df['diff'].map('{:.2f} '.format)
            + df[pvalue_formatted].astype(str)
            + df['desired_direction'].astype(str)
    )
    if use_bh_significance:
        s += df['bh_significant'].map({True: BH_SIG, False: BH_NONSIG})
    return s


def pivot_table(
        data,
        rows=None,
        cols=None,
        output=None,
        printer=IPythonReportPrinter(),
        bg_color_for_significance=True,
        report=None,
        highlight_sspm=False,
        **kwargs
        ):

    if rows is None:
        rows = []
    if cols is None:
        cols = []
    fields_needed = rows + cols + ['diff', 'desired_direction']
    use_bh_significance = False if not report else report.configurations.get('use_bh_significance', False)
    if use_bh_significance:
        fields_needed.append("bh_significance")
    df = tabulate(
        data=data,
        format_pvalue=True,
        output="full_data_frame",
        report=report,
        **kwargs
    )

    stat = 'Statistic'
    for_styles = 'for_styles'

    error_msg = 'Provide enough rows and cols so there is one value per cell in the pivot table.'
    pivot_tables = {
        col: pd.pivot_table(
            df.assign(Statistic=df[col]),
            values="Statistic",
            index=rows,
            columns=cols,
            aggfunc=lambda x: x if len(x) <= 1 else error_msg,
            fill_value='',
        )
        for col in [stat, for_styles]
    }

    assert(pivot_tables[stat].shape == pivot_tables[for_styles].shape)

    # Use another table to stylize the table we need
    styles = pivot_tables[for_styles].applymap(
        lambda x: stylized_by_p(x, bg=bg_color_for_significance)
    )
    styled_table = pivot_tables[stat].style.apply(
        lambda _: styles,
        axis=None
    )
    if use_bh_significance:
        add_bh_footnote_to_table(styled_table)

    if highlight_sspm:
        stylize_sspm(styled_table)

    if output == 'table':
        printer.print_text(styled_table.to_html(), 'p')
    elif output == "data_frame":
        return pivot_tables[stat]
    return styled_table

def stylize_sspm(styled_table):
    sspm_stat_sig_style = {}
    for column in styled_table.columns:
        column_str = "".join(column)
        result = re.findall("p=(\d+\.\d+)", column_str)
        if len(result) > 0 and float(result[0]) < 0.001:  # sspm p value
            sspm_stat_sig_style[column] = [{'selector': 'th', 'props': [('color', 'red')]}]
    styled_table.set_table_styles(sspm_stat_sig_style)

def add_bh_footnote_to_table(styler):
    styler.set_table_styles(
        [
            {
                'selector': '',
                'props': [
                    ('caption-side', 'bottom')
                ]
            },
            {
                'selector': 'caption',
                'props': [
                    ('text-align', 'left')
                ]
            }

        ]
    )
    styler.caption = BH_TABLE_FOOTNOTE


def tabulate(data,
             date='exp_ds',
             metric='metric',
             pvalue='p_value',
             stat_fmtr='{pct_diff:.2f} ({pct_diff_lo:.2f}, {pct_diff_hi:.2f})',
             other_cols=None,
             format_pvalue=False,
             output=None,
             printer=IPythonReportPrinter(),
             bg_color_for_significance=True,
             report=None,
             ):
    """ Create a formatted table from the data frame with results
    Parameters
    ----------
    data: pandas.DataFrame
        A/B test results
    date: str or unicode
        name of date column
    metric: str or unicode
        name of the column with metric name
    pvalue: str or unicode
        name of the p value column
    stat_fmtr: str or unicode
        a valid Python string formatter
        Examples:
            * "{pct_diff:,.2f}% (Δ:{diff:,.3}, p:{p_value_formatted})"
            * "{pct_diff:,.2f}% ({avg_control:,.2}→{avg_treatment:,.2})"
    use_bh_significance: bool
        Whether to use the bh_significant column to highlight cells
    other_cols: array_like, optional
        other columns to include in the final table/data frame
    format_pvalue: boolean
        whether to include the raw p-values or formatted p-values as a column
    output: {'table', 'data_frame'}, optional
        controls return value type
    printer: ReportPrinter
    bg_color_for_significance: boolean
        whether to use cell bg or fg color to indicate significance
    report: banjo.abtest.report.Report
        the report object to provide the information in the configuration.
    Returns
    -------
    res : pandas.DataFrame
        Reshaped data frame
    """
    if not isinstance(data, pd.DataFrame):
        raise ValueError("Input data must be a pandas.DataFrame")
    if other_cols is None:
        other_cols = []
    df = data.copy()
    pvalue_formatted = "{}_formatted".format(pvalue)
    df[pvalue_formatted] = get_formatted_pvalue(df[pvalue])
    use_bh_significance = False if not report else report.configurations.get('use_bh_significance', False)

    try:
        # Create a new column called stat which is a formatted string using stat_fmtr as the template
        # and the values of that row as the keyword arguments
        # if a row's metric_sql_name is found in report.metric_stat_fmtr_mapping, use that instead of the
        # stat_fmtr keyword argument of the function call
        df['stat'] = df.apply(
            lambda row: stat_fmtr.format(**row) if row['metric_sql_name'] not in report.metric_stat_fmtr_mapping
            else report.metric_stat_fmtr_mapping[row['metric_sql_name']].format(**row),
            axis=1
        )

    except (KeyError, IndexError) as e:
        logger.error(e)
        raise ValueError(
            "stat_fmtr needs to be a valid Python string formatter with keyword placeholders. "
            "Valid keywords are {}".format(list(df.columns))
        )

    if format_pvalue:
        pvalue = pvalue_formatted
    # Style (e.g. red/green font) for pandas.Data.Frame.style based on p-value and direction of change.
    df["for_styles"] = _get_series_for_styles(
        df,
        pvalue_formatted,
        use_bh_significance=use_bh_significance,
    )

    styles = df["for_styles"].apply(stylized_by_p, bg=bg_color_for_significance).values

    display = [date, metric, 'stat', pvalue]
    display_column_names = ['Date', 'Metric', 'Statistic', 'P Value']
    display.extend(other_cols)
    res = df[display]

    res.columns = display_column_names + other_cols
    res = res.reset_index(None, drop=True)

    res_style = res.style.apply(
        lambda col: styles,
        axis=0,
        subset=pd.IndexSlice[:, ['Statistic']]
    )

    if use_bh_significance:
        add_bh_footnote_to_table(res_style)

    if output == 'table':
        printer.print_text(res_style.to_html(), 'p')
    elif output == 'data_frame':
        return res
    elif output == "full_data_frame":
        column_names = df.columns.tolist()
        # we want to rename the most important columns to standardized names
        # date, metric, stat, p value
        # But we also want to preserve all the original columns since
        # we don't expect the users to know about the renaming. Their
        # downstream operation may still refer to the original column names, e.g. metric.
        df = pd.concat([df[display], df], axis=1)
        df.columns = display_column_names + column_names
        return df

    return res_style


def _get_title_y_pos(fig, desired_inch_above=0.2):
    fig_height = fig.get_size_inches()[1]
    return 1 + desired_inch_above/fig_height


def _get_margin_between_facets(fig, desired_inch_between=0.7):
    fig_height = (
        fig.axes[0].get_window_extent()
                   .transformed(fig.dpi_scale_trans.inverted())
                   .height
    )
    return desired_inch_between/fig_height


def to_pydt(series):
    """Convert pandas datetime64 Series to an numpy array in Python datetime type"""
    try:
        is_datetime64 = pd.core.dtypes.common.is_datetime64_ns_dtype(series)
    except AttributeError:
        # < pandas 0.20
        is_datetime64 = pd.api.types.is_datetime64_ns_dtype(series)
    if is_datetime64:
        if type(series) == pd.Series:
            return series.dt.to_pydatetime()
        else:
            # A scalar
            return series.to_pydatetime()
    else:
        return series


def infer_dt_tick_gap(series):
    """Infer the appropriate gap for axis ticks when plotting a datetime series"""
    default_value = timedelta(hours=1)
    if series is None or len(series) == 0 or (isinstance(series, pd.Series) and series.empty):
        return default_value
    arr = np.unique(to_pydt(series))
    if len(arr) == 1:
        return default_value
    else:
        return min(np.diff(sorted(np.unique(arr))))


def thin_x_ticks(ticks):
    while len(ticks) > 7:
        ticks = [tick for ind, tick in enumerate(ticks)
                 if ind % 2 == 0 or ind == len(ticks) - 1]
    return ticks


def stylized_by_p(text, bg=True):
    if bg:
        css = 'color:white;font-weight:bold;background-color:{color};'
    else:
        css = 'color:{color};font-weight:bold;'

    if text.count(banjo.abtest.metric.NEUTRAL):
        color = "gray"
    elif text.count('-') > 0:
        if text.count(banjo.abtest.metric.NEGATIVE) > 0:
            color = 'green'
        else:
            color = 'red'
    else:
        if text.count(banjo.abtest.metric.NEGATIVE) > 0:
            color = 'red'
        else:
            color = 'green'

    rendered_css = ""
    for p_threshold in COLOR_MAP["red"]:
        if text.count(p_threshold) == 1:
            rendered_css = css.format(
                color=COLOR_MAP[color][p_threshold]
            )
            break
    if BH_NONSIG in text:
        rendered_css = ""

    return rendered_css

def get_split_rule_text(feature_idx, threshold, feature_space, target_encoder, ordinal_encoder = None, text_limit=30):
    """ A helper function for plotting Squared T Stat Tree

    :param feature_idx:
    :param threshold:
    :param feature_space:
    :param encoder:
    :return:
    """
    if feature_idx == -2:
        # leaf node
        return ""
    
    # Get feature names and categories from encoders
    ordinal_feature_names = getattr(ordinal_encoder, 'feature_names_in_', [])
    target_feature_names = getattr(target_encoder, 'feature_names_in_', [])
    numerical_feature_names = getattr(target_encoder, 'numerical_feature_names_', [])

    # Convert to lists if necessary
    ordinal_feature_names = ordinal_feature_names.tolist() if hasattr(ordinal_feature_names, 'tolist') else ordinal_feature_names
    target_feature_names = target_feature_names.tolist() if hasattr(target_feature_names, 'tolist') else target_feature_names

    # Calculate the number of ordinal features
    num_ordinal_features = len(ordinal_feature_names)
    
    # Total number of categorical features
    n_cat_features = num_ordinal_features + len(target_feature_names)

    # split text based on different encoders
    if feature_idx < n_cat_features:
        if feature_idx < num_ordinal_features:
            encoder = ordinal_encoder
            feature_name = encoder.feature_names_in_[feature_idx]
            categories = encoder.categories_[feature_idx]
            encodings = list(range(len(categories)))
        else:
            encoder = target_encoder
            feature_name = encoder.feature_names_in_[feature_idx - num_ordinal_features]
            categories = encoder.categories_[feature_idx - num_ordinal_features]
            encodings = encoder.encodings_[feature_idx - num_ordinal_features]
        
        # categorical feature
        lower_bound, upper_bound = feature_space[feature_idx]
        left_set = [str(categories[i]) for i in range(len(categories)) if lower_bound <= encodings[i] < threshold]
        left_set = ", ".join(left_set)
        if len(left_set) > text_limit:
            left_set = left_set[:text_limit-3] + "..."
        right_set = [str(categories[i]) for i in range(len(categories)) if threshold <= encodings[i] < upper_bound]
        right_set = ", ".join(right_set)
        if len(right_set) > text_limit:
            right_set = right_set[:text_limit-3] + "..."
        text = f"left: {feature_name} in {{{left_set}}}\nright: {feature_name} in {{{right_set}}}"
    else:
        # numerical feature
        feature_name = numerical_feature_names[feature_idx - n_cat_features]
        text = f"left: {feature_name} < {threshold}\nright: {feature_name} >= {threshold}"
    return "\n\n"+text


def plot_tstat_ab_tree(tree, target_encoder, ordinal_encoder = None, ax=None, fontsize=14):
    from sklearn.tree._export import _MPLTreeExporter
    if ax is None:
        fig, ax = plt.subplots(figsize=(24, 8))

    if ordinal_encoder is None:
        feature_names = target_encoder.feature_names_in_.tolist() + target_encoder.numerical_feature_names_
    else:
        # try to get the feature names from the encoder, else return empty list
        ordinal_feature_names = getattr(ordinal_encoder, 'feature_names_in_', [])
        target_feature_names = getattr(target_encoder, 'feature_names_in_', [])
        numerical_feature_names = getattr(target_encoder, 'numerical_feature_names_', [])

        # Convert to lists if they are not None
        ordinal_feature_names = ordinal_feature_names.tolist() if hasattr(ordinal_feature_names, 'tolist') else ordinal_feature_names
        target_feature_names = target_feature_names.tolist() if hasattr(target_feature_names, 'tolist') else target_feature_names

        # Concatenate non-empty lists
        feature_names = ordinal_feature_names + target_feature_names + numerical_feature_names
        
    exporter = _MPLTreeExporter(max_depth=None,
                                feature_names=feature_names,
                                class_names=None,
                                label="all",
                                filled=False,
                                impurity=False,
                                node_ids=False,
                                proportion=False,
                                rounded=False,
                                precision=3,
                                fontsize=fontsize,
                                )
    texts = exporter.export(tree, ax=ax)
    stat_fmtr = "{pct_diff:,.2f}% ({avg_control:,.3}→{avg_treatment:,.3}, p={p_value:.2})"

    for i in range(len(texts)):
        val = tree.tree_.value[i]
        text = texts[i]
        feature_space = tree.tree_.feature_spaces[i]
        text.set_text(
            "control size = {}\ntreatment size = {}\n".format(tree.tree_.n_node_samples[i][0],
                                                              tree.tree_.n_node_samples[i][1]) +
            stat_fmtr.format(pct_diff=val[0], avg_control=val[1], avg_treatment=val[2], p_value=val[3]) +
            get_split_rule_text(tree.tree_.feature[i], tree.tree_.threshold[i], feature_space, target_encoder, ordinal_encoder)
        )
        p_value = val[3]
        color_group = 'green' if val[0] > 0 else 'red'
        text_color = 'w' if p_value < 0.001 else 'k'
        if p_value < 0.0001:
            color = COLOR_MAP[color_group]['< 0.0001']
        elif p_value < 0.001:
            color = COLOR_MAP[color_group]['< 0.001']
        elif p_value < 0.01:
            color = COLOR_MAP[color_group]['< 0.01']
        elif p_value < 0.05:
            color = COLOR_MAP[color_group]['< 0.05']
        else:
            color = 'white'
        text.set_bbox({'edgecolor': 'k', 'facecolor': 'none'})
        text.set_backgroundcolor(color)
        text.set_color(text_color)
    plt.show()
    plt.close()

def hte_tree_table_view(tree, target_encoder, ordinal_encoder = None, printer=IPythonReportPrinter()):
    n_tree_nodes = len(tree.tree_.feature)
    if n_tree_nodes == 0:
        return None
    user_cohorts = [[] for _ in range(n_tree_nodes)]

    # preorder traversal
    for i in range(n_tree_nodes):
        feature_space = tree.tree_.feature_spaces[i]
        text = get_split_rule_text(tree.tree_.feature[i], tree.tree_.threshold[i], feature_space, target_encoder, ordinal_encoder, text_limit=100)
        if text == "":
            continue
        result = re.search(r"\s\sleft: (.+)\sright: (.+)", text)
        left_text, right_text = result.groups()

        left_index = tree.tree_.children_left[i]
        user_cohorts[left_index] = user_cohorts[i] + [left_text]
        right_index = tree.tree_.children_right[i]
        user_cohorts[right_index] = user_cohorts[i] + [right_text]

    user_cohorts = [" & ".join(x) for x in user_cohorts]
    user_cohorts[0] = "Overall"
    stat_fmtr = "{pct_diff:,.2f}% ({avg_control:,.3}→{avg_treatment:,.3}, p={p_value:.4f})"
    df = pd.DataFrame({
        'Treatment Effect': [stat_fmtr.format(pct_diff=val[0], avg_control=val[1], avg_treatment=val[2], p_value=val[3])
                             for val in tree.tree_.value],
        'Control Size': [s[0] for s in tree.tree_.n_node_samples],
        'Treatment Size': [s[1] for s in tree.tree_.n_node_samples],
        'P value': [val[3] for val in tree.tree_.value]
    }, index=user_cohorts)
    df.index.rename("User Cohort", inplace=True)
    df['for_style'] = df['Treatment Effect'] + get_formatted_pvalue(df['P value'])
    styles = df['for_style'].map(
        lambda x: stylized_by_p(x, bg=True)
    )
    selected_columns = ['Treatment Effect', 'Control Size', 'Treatment Size']
    styler = df[selected_columns].style.set_table_styles([
        {'selector': 'th', 'props': [('text-align', 'left')]},
        {'selector': 'td', 'props': [('text-align', 'left')]}
    ]).apply(
        lambda _: styles,
        subset=['Treatment Effect']
    )
    original_max_colwidth = pd.get_option("display.max_colwidth")
    pd.set_option("display.max_colwidth", 1000)
    printer.print_text(styler.to_html(), 'p')
    pd.set_option("display.max_colwidth", original_max_colwidth)
    return
