"""Quest metrics"""

from __future__ import division, print_function
from .metric import Metric, MetricTable
import pandas as pd

QUEST_CORE_METRICS = {
    'time_spent': {
        'table': 'quest_raw_session_time_sec_',
        'value_col': 'SUM(value)',
        'name': 'Time Spent',
        'dist': 'cont',
    },
    'active_days': {
        'table': 'quest_raw_dau_',
        'value_col': 'MAX(IF(value > 0, 1, 0))',
        'name': 'Active Days',
        'dist': 'cont',
        'daily': True,
        # Daily only makes sense if joining with cumulative mapping tables
    },
    'direct_snap_create': {
        'table': 'quest_raw_direct_snap_create_',
        'value_col': 'SUM(value)',
        'name': 'Direct Snap Create',
        'dist': 'cont',
    },
    'direct_snap_send': {
        'table': 'quest_raw_direct_snap_send_',
        'value_col': 'SUM(value)',
        'name': 'Direct Snap Send',
        'dist': 'cont',
    },
    'direct_snap_view': {
        'table': 'quest_raw_direct_snap_view_',
        'value_col': 'SUM(value)',
        'name': 'Direct Snap View',
        'dist': 'cont',
    },
    'story_snap_view': {
        'table': 'quest_raw_story_snap_view_',
        'value_col': 'SUM(value)',
        'name': 'Story Snap View',
        'dist': 'cont',
    },
    'story_snap_post': {
        'table': 'quest_raw_story_snap_post_',
        'value_col': 'SUM(value)',
        'name': 'Story Snap Post',
        'dist': 'cont',
    },
    'chat_send': {
        'table': 'quest_raw_chat_send_',
        'value_col': 'SUM(value)',
        'name': 'Chat Send',
        'dist': 'cont',
    },
    'chat_view': {
        'table': 'quest_raw_chat_view_',
        'value_col': 'SUM(value)',
        'name': 'Chat View',
        'dist': 'cont',
    },
}
TIER_ONE = ['active_days', 'time_spent', 'direct_snap_create', ]

ALL_QUEST_METRICS = list(QUEST_CORE_METRICS.keys())

QUEST_DATASET = 'sc-analytics:report_quest.'


def get_quest_metric_table(metric_name,
                           start_date,
                           end_date,
                           ):
    if metric_name not in QUEST_CORE_METRICS:
        raise ValueError('Metric {} is not supported.'.format(metric_name))

    config = QUEST_CORE_METRICS[metric_name]

    query = """
    SELECT
      TIMESTAMP(ds + ' 00:00:00') AS ts,
      id AS ghost_user_id,
      {value_col} AS {metric_name}
    FROM
      TABLE_DATE_RANGE_STRICT(
        [{table}],
        TIMESTAMP('{start}'),
        TIMESTAMP('{end}')
      )
    GROUP EACH BY
      1, 2
    """.format(
        value_col=config['value_col'],
        metric_name=metric_name,
        table=QUEST_DATASET + config['table'],
        start=pd.to_datetime(start_date),
        end=pd.to_datetime(end_date)
    )

    quest_metric = Metric(
        metric_name,
        config['name'],
        config['dist'],
        daily=config.get('daily', True),
        cumulative=config.get('cumulative', True),
        always_use_cumulative_count=config.get(
            'always_use_cumulative_count', False
        )
    )
    quest_metric_table = MetricTable(
        sql=query,
        metrics=[quest_metric]
    )
    return quest_metric_table
