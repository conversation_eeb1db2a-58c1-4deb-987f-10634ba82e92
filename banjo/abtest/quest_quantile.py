"""Quest metrics"""

from __future__ import division, print_function
from .metric import Metric, MetricTable, NEGATIVE
import pandas as pd

QUEST_QUANTILE_METRICS = {
    'camera_switch': {
        'table': 'camera_switch_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Camera Switch',
        'dist': 'quantile'
    },
    'front_camera_fps_lens': {
        'table': 'camera_switch_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Front Camera FPS Lens',
        'dist': 'quantile',
        'where_clause': 'where facing_direction == "front" and lens_bool = 1'
    },
    'front_camera_fps_no_lens': {
        'table': 'camera_switch_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Front Camera FPS No Lens',
        'dist': 'quantile',
        'where_clause': 'where facing_direction == "front" and lens_bool = 0'
    },
    'rear_camera_fps_lens': {
        'table': 'camera_switch_events_raw_',
        'value_col': 'Rear Camera FPS Lens',
        'name': 'Camera Switch',
        'dist': 'quantile',
        'where_clause': 'where facing_direction == "rear" and lens_bool = 1'
    },
    'rear_camera_fps_no_lens': {
        'table': 'camera_switch_events_raw_',
        'value_col': 'Rear Camera FPS No Lens',
        'name': 'Rear Camera FPS No Lens',
        'dist': 'quantile',
        'where_clause': 'where facing_direction == "rear" and lens_bool = 0'
    },
    'story_post_delay': {
        'table': 'story_post_delay_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Story Post Delay',
        'dist': 'quantile',
    },
    'camera_delay': {
        'table': 'camera_delay_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Camera Delay',
        'dist': 'quantile'
    },
    'camera_creation_delay': {
        'table': 'camera_creation_delay_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Camera Creation Delay',
        'dist': 'quantile'
    },
    'camera_creation_delay_ios_image': {
        'table': 'camera_creation_delay_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Camera Creation Delay IOS Image',
        'dist': 'quantile',
        'where_clause': 'where type = "image" and platform = "iOS"'
    },
    'camera_creation_delay_android_image': {
        'table': 'camera_creation_delay_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Camera Creation Delay Android Image',
        'dist': 'quantile',
        'where_clause': 'where type = "image" and platform = "Android"'
    },
    'camera_creation_delay_ios_video': {
        'table': 'camera_creation_delay_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Camera Creation Delay IOS Video',
        'dist': 'quantile',
        'where_clause': 'where type = "video" and platform = "iOS"'
    },
    'camera_creation_delay_android_video': {
        'table': 'camera_creation_delay_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Camera Creation Delay Android Video',
        'dist': 'quantile',
        'where_clause': 'where type = "video" and platform = "Android"'
    },
    'camera_transcoding': {
        'table': 'camera_transcoding_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Camera Transcoding',
        'dist': 'quantile'
    },
    'recording_delay': {
        'table': 'recording_delay_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Recording Delay',
        'dist': 'quantile'
    },
    'ghost_to_snappable_android_cold': {
        'table': 'ghost_to_snappable_med_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Ghost To Snappable Android Cold',
        'dist': 'quantile',
        'where_clause': 'where metric = "ghost_to_snappable_android_cold"'
    },
    'ghost_to_snappable_android_warm': {
        'table': 'ghost_to_snappable_med_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Ghost To Snappable Android Warm',
        'dist': 'quantile',
        'where_clause': 'where metric = "ghost_to_snappable_android_warm"'
    },
    'ghost_to_snappable_ios_cold': {
        'table': 'ghost_to_snappable_med_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Ghost To Snappable iOS Cold',
        'dist': 'quantile',
        'where_clause': 'where metric = "ghost_to_snappable_ios_cold"'
    },
    'ghost_to_snappable_ios_headless': {
        'table': 'ghost_to_snappable_med_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Ghost To Snappable iOS Headless',
        'dist': 'quantile',
        'where_clause': 'where metric = "ghost_to_snappable_ios_headless"'
    },
    'ghost_to_snappable_ios_warm': {
        'table': 'ghost_to_snappable_med_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Ghost To Snappable iOS Warm',
        'dist': 'quantile',
        'where_clause': 'where metric contains "ghost_to_snappable_ios_warm"'
    },
    'ghost_to_snappable_ios_warm_resume_camera': {
        'table': 'ghost_to_snappable_med_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Ghost To Snappable iOS Warm Resume Camera',
        'dist': 'quantile',
        'where_clause': 'where metric = "ghost_to_snappable_ios_warm_resume_camera"'
    },
    'ghost_to_snappable_ios_warm_start_camera_from_main': {
        'table': 'ghost_to_snappable_med_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Ghost To Snappable iOS Warm Start Camera From Main',
        'dist': 'quantile',
        'where_clause': 'where metric = "ghost_to_snappable_ios_warm_start_camera_from_main"'
    },
    'ghost_to_snappable_ios_warm_start_camera_from_other': {
        'table': 'ghost_to_snappable_med_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Ghost To Snappable iOS Warm Start Camera From Other',
        'dist': 'quantile',
        'where_clause': 'where metric = "ghost_to_snappable_ios_warm_start_camera_from_other"'
    },
    'ghost_to_snappable_ios_warm_undefined': {
        'table': 'ghost_to_snappable_med_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Ghost To Snappable iOS Warm Undefined',
        'dist': 'quantile',
        'where_clause': 'where metric = "ghost_to_snappable_ios_warm_undefined"'
    },
    'snap_to_preview': {
        'table': 'snap_to_preview_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Snap To Preview',
        'dist': 'quantile'
    },
    'pull_to_refresh': {
        'table': 'pull_to_refresh_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Pull To Refresh',
        'dist': 'quantile',
    },
    'ready_to_capture': {
        'table': 'ready_to_capture_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Ready To Capture',
        'dist': 'quantile',
    },
    'warm_start_time': {
        'table': 'startup_time_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Warm Start Time',
        'dist': 'quantile',
        'where_clause': 'where from_background > 0'
    },
    'cold_start_time': {
        'table': 'startup_time_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Cold Start Time',
        'dist': 'quantile',
        'where_clause': 'where from_background <= 0'
    },
    'swipe_latency': {
        'table': 'swipe_latency_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Swipe Latency',
        'dist': 'quantile',
    },
    'user_load_time': {
        'table': 'other_perf_events_subset_',
        'value_col': 'INTEGER(JSON_EXTRACT_SCALAR(metrics, \'$.timers.time[0]\'))',
        'name': 'User Load Time',
        'dist': 'quantile',
        'where_clause': 'where eventName = "USER_LOAD_TIME"'
    },
    'snap_sent_delay': {
        'table': 'snap_sent_delay_events_raw_',
        'value_col': 'metric_latency',
        'name': 'Snap Sent Delay',
        'dist': 'quantile',
    },
}

QUEST_PERFORMANCE_DATASET = 'sc-analytics:report_quest_performance.'


def get_quest_quantile_metric(metric_name,
                              start_date,
                              end_date,
                              ):
    if metric_name not in QUEST_QUANTILE_METRICS:
        raise ValueError('Metric {} is not supported.'.format(metric_name))

    config = QUEST_QUANTILE_METRICS[metric_name]

    query = """
    SELECT
      TIMESTAMP('{end}') AS ts,
      ghost_user_id AS ghost_user_id,
      {value_col} AS {metric_name}
    FROM
      TABLE_DATE_RANGE_STRICT(
        [{table}],
        TIMESTAMP('{start}'),
        TIMESTAMP('{end}')
      )
    {where_clause}

    """.format(
        value_col=config['value_col'],
        metric_name=metric_name,
        table=QUEST_PERFORMANCE_DATASET + config['table'],
        start=pd.to_datetime(start_date),
        end=pd.to_datetime(end_date),
        where_clause=config.get('where_clause', '')
    )

    quest_metric = Metric(
        metric_name,
        config['name'],
        config['dist'],
        daily=config.get('daily', True),
        cumulative=config.get('cumulative', True),
        always_use_cumulative_count=config.get(
            'always_use_cumulative_count', False
        ),
        desired_direction=NEGATIVE,
    )
    quest_metric_table = MetricTable(
        sql=query,
        metrics=[quest_metric],
        quantile_metrics=True,
    )
    return quest_metric_table
