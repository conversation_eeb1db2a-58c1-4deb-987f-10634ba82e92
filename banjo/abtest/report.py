"""
Generate report
"""
from __future__ import division, print_function, unicode_literals

import copy
import datetime
import getpass
import itertools
import locale
import logging
import pandas as pd
import re
import six
import socket
import sqlparse
import tempfile
import warnings

from typing import List, Union, Optional, Dict, Literal
from functools import reduce

from google.cloud import bigquery
from scipy.stats import norm
from statsmodels.stats.proportion import proportions_ztest

from .data import *
from .data import (
    ABTEST_PROJECT
)
from .plots import *
from .analysis import *

from banjo.abtest.hte import preprocessing, tree
from sklearn.model_selection import train_test_split
from banjo.abtest.utils import get_sql_selects
from banjo.utils.gbq import (
    submit_sync_query,
    wait_for_job_then_update_table_desc,
    table_exists,
    BigQueryRuntimeError,
)
from banjo.utils.helpers import hash_string
import banjo.utils.add_log_handler as add_log_handler
import banjo.utils.pandas.dataframe
# todo: remove the .metric and .quest imports here after removing the `from report imports ...` in all notebooks
from .metric import (
    Metric, MetricTable, get_abtest_console_metric_table
)
from .quest import get_quest_metric_table
from .stat_funcs import anova_sample_mean, chisq_test_sample_size, calculate_bh_significance
from .mapping_table_config import MAPPING_TABLE_CONFIG
from banjo.utils.printer import IPythonReportPrinter
from banjo.utils import gbq, gcs
import banjo.abtest.utils

CUMULATIVE = 'cumulative'
DAILY = 'daily'

ALPHA_LEVEL = 0.05
BIGQUERY_CONCURRENCY_LIMIT = 45

HOSTNAME = socket.gethostname() or "unknown_hostname"
USER = getpass.getuser() or "unknown_user"

"""
SQL template for joining the metric table with the mapping table.

These are the timestamp requirements

Mapping side:

* exposure_ds: exposure date expressed as TIMESTAMP("2020-01-01 00:00:00)
* exposure_ts: exposure hour expressed in UTC AS TIMESTAMP("2020-01-01 00:08:00)

Metrics side:

* ts: exposure date expressed as TIMESTAMP("2020-01-01 00:00:00)
* metric_ts: exposure hour expressed in UTC AS TIMESTAMP("2020-01-01 00:08:00)

"""
JOIN_EXPOSURE_SQL = """
-- Submitted by module {module} for MetricTable {metric_table_name} on host {host} by user {user}
SELECT
  study_table.{mapping_user_col} AS {mapping_user_col},
  study_table.exp_id AS exp_id,
  IFNULL(metric_table.ts, study_table.exposure_ds) AS exp_ds,
  IF(metric_table.{metric_user_col} is NULL, 0, 1) AS __has_metrics,
  {period_clause}
  {study_cols}
  {metric_cols}
FROM
  {metric_table_sql} AS metric_table
{join_how} JOIN
    {study_table} AS study_table

ON
  study_table.{mapping_user_col} = metric_table.{metric_user_col}
  {join_conditions}
{where_conditions}
"""

# Get Experiment Metadata
study_metadata_sql = """
    SELECT
      name,
      exp_id,
      study_version,
      traffic,
      analysis_start_date AS study_start_date
    FROM
      `sc-analytics.report_search.ab_console_study_config`
    WHERE
      study_name = '{study_name}'
      AND exp_id IN ('{exp_ids}')
"""


def visualize_sample_sizes(count_treatment, count_control, sspm_pval):
    if sspm_pval is None:
        return "Treatment={:,.0f}<br>\nControl={:,.0f}<br>\nSSPM N/A".format(
            count_treatment,
            count_control
        )
    elif 0 <= sspm_pval <= 0.001:
        return "Treatment={:,.0f}<br>\nControl={:,.0f}<br>\nSSPM Detected (p={:.4f})".format(
            count_treatment,
            count_control,
            sspm_pval
        )
    else:
        return "Treatment={:,.0f}<br>\nControl={:,.0f}<br>\nNo SSPM (p={:.4f})".format(
            count_treatment,
            count_control,
            sspm_pval
        )


class Report(object):
    def __init__(self,
                 study_name,
                 study_start_date,
                 study_end_date,
                 metric_tables,
                 control_id,
                 treatment_ids,
                 analysis_start_date=None,
                 quantiles=None,
                 exp_id_to_name=None,
                 exp_id_to_traffic=None,
                 user_group_bys=None,
                 mapping_user_col='ghost_user_id',
                 bq_project=None,
                 bq_client=None,
                 dest_dataset='temp_abtest',
                 abtest_project=ABTEST_PROJECT,
                 cumulative_mapping_dataset=None,
                 mapping_table_ver='v3',
                 ab_printer=IPythonReportPrinter(),
                 control_bq_submit_rate=False,
                 bq_priority='INTERACTIVE',
                 bq_retry=None,
                 configurations=None,
                 report_key=None,
                 excel_output=False,
                 aa_start_date=None,
                 aa_end_date=None,
                 additional_metric_direction_mapping=None,
                 downsample_to=None
                 ):
        """

        Parameters
        ----------
        study_name : str
        study_start_date : str
            The start date of the A/B study version
        study_end_date : str
        metric_tables : list of MetricTable
        control_id : str
        treatment_ids : list of str
        analysis_start_date : str
            The start date of the A/B analysis date range; defaults to study_start_date
        quantiles : list of str, optional
        user_group_bys : list of str or list of lists of str
            user_group_bys can be None (default)
            a string for a single variable,
              e.g. `age`. The report will include a single breakdown by this dimension
            a list of strings
              e.g. `["age", "gender", "os|device_cluster"]`. The report will include one breakdown analysis for
              each string in the list. `|` separator can be used to construct simultaneous breakdowns.
            a list of lists of strings
              e.g. `[["age"], ["gender"], ["os", "device_cluster"]]`. The report will include on breakdown analysis
              for each list in the list of lists.
        mapping_user_col : str
        bq_project : str
            The BigQuery project where all the temporary tables are stored under its `dest_dataset`
            If bq_client is not supplied, this is also the BigQuery project with which to submit all BigQuery jobs
        bq_client: google.cloud.bigquery.Client
            The bigquery.Client object used to submit all queries.
            If bq_project is not supplied, the project associated with this client is used to store all temporary
            tables.
        dest_dataset : str
            Temporary dataset for the intermmediate joined tables between mapping and metrics
        abtest_project : str
            Project where the mapping tables are saved.
        cumulative_mapping_dataset : str
            Dataset where cumulative mapping tables are saved. If None, we will use the default location.
        mapping_table_ver : str
            Which mapping table versions to use, of which there are currently two:
            "v3" and "allocation"
        ab_printer : Printer
             Use IPython: ab_printer=IPythonReportPrinter()
             Use File output: ab_printer=HTMLFileReportPrinter(your_html_file_name) f
        control_bq_submit_rate : bool
            Whether to respect (default False) bigquery concurrency limit
        bq_priority : str
            'INTERACTIVE' or 'BATCH'
            Controls the priority of all bigquery query jobs submitted by the instance
        bq_retry:google.api_core.retry.Retry
            a Retry object for the bigquery error retry logic; use default if None is specified
        report_key : str
            an alphanumeric Unique ID for the report, used for uploading results to GCS
        excel_output : boolean or str
            a Excel file system path where all tables will be saved or True to output an excel file to a temp file
            or False to disable output to Excel
        aa_start_date : str
        aa_end_date : str
        downsample_to : int
            Specifies the number of ghost_user_id entries to include for each exp_id group on AB Mapping.
            If any exp_id group contains fewer entries than the specified value of downsample_to,
            all exp_id groups will be adjusted to match the number of entries in the exp_id group with the fewest entries.
            If downsample_to is not selected or set to None, no downsampling will be performed.
            Additionally, the number of samples in Husky will also depend on the number of users present in the metric table, which may further limit the number of entries available for each exp_id group.
        """
        self.logger = logging.getLogger(__name__)
        self.study_name = study_name
        self.study_start_date = study_start_date
        self.study_end_date = study_end_date
        self.metric_tables = metric_tables
        self.control_id = control_id
        if isinstance(treatment_ids, six.string_types):
            treatment_ids = [treatment_ids]
        self.treatment_ids = treatment_ids
        if analysis_start_date is None:
            self.analysis_start_date = study_start_date
        else:
            self.analysis_start_date = analysis_start_date

        self.quantiles = quantiles
        if not configurations:
            self.configurations = {}
        else:
            self.configurations = configurations
        # Set default configurations
        self.configurations['stat_fmtr'] = self.configurations.get(
            'stat_fmtr',
            '{pct_diff:.2f} ({pct_diff_lo:.2f}, {pct_diff_hi:.2f})'
        )

        if not bq_client and not bq_project:
            raise ValueError("You must specify either bq_project or bq_client to run the queries.")
        self.bq_client = (bq_client if bq_client else bigquery.Client(bq_project))
        self.bq_project = (bq_project if bq_project else bq_client.project)

        try:
            self.study_metadata = gbq.read_gbq(
                study_metadata_sql.format(study_name=study_name, exp_ids="', '".join([control_id] + treatment_ids)),
                client=self.bq_client,
                use_bqstorage_api=self.configurations.get("use_bqstorage_api", True),
                dialect="standard",
                priority=bq_priority,
            )
        except BigQueryRuntimeError:
            # todo: we could output the source to gcs and read from there to solve the access issue
            self.study_version = ''  # to build hyper link to AB Console under DF_UI_AB_STUDY_TEMPLATE.ipynb
            self.study_metadata = pd.DataFrame()  # empty data frame
            self.logger.warning("Not able to read study metadata")

        if not self.study_metadata.empty:
            self.study_version = self.study_metadata.study_version.iloc[0]
            metadata_study_start_date = self.study_metadata.study_start_date.iloc[0].strftime("%Y%m%d")
            if metadata_study_start_date != self.study_start_date:
                self.logger.warning(
                    "Overwrite study_start_date to {} from metadata. "
                    "analysis_start_date still uses the supplied value of {}".format(
                        metadata_study_start_date,
                        self.analysis_start_date
                    )
                )
                self.study_start_date = metadata_study_start_date

        if self.analysis_start_date < self.study_start_date or self.analysis_start_date > self.study_end_date:
            self.logger.warning("analysis_start_date must be between study_start_date and study_end_date")

        if exp_id_to_name:
            self.exp_id_to_name = exp_id_to_name
        elif not self.study_metadata.empty:
            self.exp_id_to_name = dict(zip(self.study_metadata.exp_id, self.study_metadata.name))
        else:
            self.exp_id_to_name = {}

        if exp_id_to_traffic:
            self.exp_id_to_traffic = exp_id_to_traffic
        elif not self.study_metadata.empty:
            self.exp_id_to_traffic = dict(zip(self.study_metadata.exp_id, self.study_metadata.traffic))
        else:
            self.exp_id_to_traffic = {}

        # convert user_group_bys to list of lists
        if not user_group_bys:
            user_group_bys = [[]]
        elif isinstance(user_group_bys, six.string_types):
            user_group_bys = [user_group_bys.split("|")]
        elif isinstance(user_group_bys[0], six.string_types):  # input is a list of strings
            user_group_bys = [group_by.split("|") for group_by in user_group_bys]
        self.user_group_by_list: List[List[str]] = user_group_bys
        # unique values of group_by_list
        self.user_group_vars = list(set(list(itertools.chain.from_iterable(self.user_group_by_list))))
        self.has_user_breakdowns = bool(self.user_group_vars)

        self.mapping_user_col = mapping_user_col

        self.dest_dataset = dest_dataset
        self.jobs = {}
        self._sufficient_stats = {}
        self.results = {}  # The final results
        self.sample_sizes = {}  # The sample sizes, with the same keys as self.results
        self.abtest_project = abtest_project
        self.cumulative_mapping_dataset = cumulative_mapping_dataset

        self.mapping_table_ver = mapping_table_ver
        self.ab_printer = ab_printer
        self.control_bq_submit_rate = control_bq_submit_rate
        self.bq_priority = bq_priority
        self.bq_retry = bq_retry

        self.report_key = report_key
        self._report_with_cuped = False

        if aa_start_date and aa_end_date and aa_start_date > aa_end_date:
            self.logger.warning("aa_start_date should be smaller than aa_end_date")

        self.aa_start_date = aa_start_date
        self.aa_end_date = aa_end_date

        if report_key is not None and not report_key.isalnum():
            raise ValueError("report_key must be alphanumeric")

        if isinstance(excel_output, str):
            self.excel_output_path = excel_output
        elif excel_output:
            tmp_xls = tempfile.NamedTemporaryFile(delete=False, suffix=".xlsx")
            tmp_xls.close()
            self.excel_output_path = tmp_xls.name
        else:
            self.excel_output_path = None

        self._excel_sheet_seq_no = 0
        self.anchor_list = []
        self._calculate_aa = False
        for metric_table in metric_tables:
            if not any(metric.dist == "cont" for metric in metric_table.metrics):
                metric_table.cuped = False
            if metric_table.aa or metric_table.cuped:
                self._calculate_aa = True
            if metric_table.cuped:
                self._report_with_cuped = True
        self.additional_metric_direction_mapping=additional_metric_direction_mapping
        self.downsample_to=downsample_to

    def __str__(self):
        return ("Report: {name} - {start}~{end}: Metrics: {metrics}, "
                "Control ID: {control_id}, "
                "Treatment IDs: {treatment_ids}").format(
            name=self.study_name,
            metrics=self.metric_tables,
            start=self.analysis_start_date,
            end=self.study_end_date,
            control_id=self.control_id,
            treatment_ids=self.treatment_ids,
        )

    def check_sspm(self, control_user_count, treatment_user_count, treatment_id):
        if not self.exp_id_to_traffic:
            return None
        control_proportion = self.exp_id_to_traffic.get(self.control_id, 0)
        treatment_proportion = self.exp_id_to_traffic.get(treatment_id, 0)
        if control_proportion > 0 and treatment_proportion > 0:
            expected_proportion = treatment_proportion / sum([treatment_proportion, control_proportion])
        else:
            return None
        total_user_count = sum([control_user_count, treatment_user_count])
        _, p_value = proportions_ztest(treatment_user_count, total_user_count, expected_proportion)
        return p_value

    # This function will be only used in app engine service
    # Add file handler to logger(xxx.banjo), users can track their own log in file
    def set_banjo_file_handler(self, log_file):
        banjo_logger_str = self.logger.name.rsplit(".abtest", 1)[0]
        banjo_logger = logging.getLogger(banjo_logger_str)
        add_log_handler.add_file_handler(banjo_logger, log_file, logging.INFO)

    def get_mapping_sql(self, dialect):
        """ The method that returns the mapping sql in the given bigquery dialect"""
        if dialect == "legacy":
            raise ValueError("The Report class only supports metrics in standard dialect")

        mapping_table_versions = ["v3", "allocation"]
        if self.mapping_table_ver not in mapping_table_versions:
            raise ValueError(
                "Specify a mapping_table_ver from one of these values {}".format(mapping_table_versions)
            )
        mapping_config = copy.deepcopy(MAPPING_TABLE_CONFIG[self.mapping_table_ver])
        if self.cumulative_mapping_dataset:
            mapping_config["dataset"] = self.cumulative_mapping_dataset

        # Process columns to select
        study_cols = self.user_group_vars
        study_cols_always_include = {
            "PARSE_TIMESTAMP('%Y%m%d', SUBSTR(_TABLE_SUFFIX, -8)) as ts", "ghost_user_id", 'exp_id'
        }
        if not study_cols:
            study_cols = set()

        mapping_dataset = mapping_config["dataset"]
        mapping_sql = """
                (
                  SELECT {cols}
                  FROM `{project}.{dataset}.{study_name}__*`
                  WHERE SAFE_CAST(SUBSTR(_TABLE_SUFFIX, 0, 8) AS INT64) > 0
                     AND SUBSTR(_TABLE_SUFFIX, -8) BETWEEN '{start_date}' AND '{end_date}'
                )
                """.format(
            cols=', '.join(study_cols_always_include | set(study_cols)),
            project=self.abtest_project,
            dataset=mapping_dataset,
            study_name=self.study_name,
            start_date=self.analysis_start_date,
            end_date=self.study_end_date,
        )
        return mapping_sql

    def _get_aa_time_range(self):
        if self.aa_start_date and self.aa_end_date:
            return self.aa_start_date, self.aa_end_date
        start_date = datetime.datetime.strptime(self.study_start_date, '%Y%m%d')
        aa_start = (start_date - datetime.timedelta(days=7)).strftime('%Y%m%d')
        aa_end = (start_date - datetime.timedelta(days=1)).strftime('%Y%m%d')
        return aa_start, aa_end

    def get_join_metrics_query(
            self,
            metric_table_sql: str,  # todo(xhe): deprecate this field once we deprecate join_metrics_with_mapping
            metric_cols: List[str],
            metric_user_col: str,
            mapping_user_col: str,
            metric_date_col: str,
            metric_table_object: MetricTable,
            join_how: Optional[str] = 'LEFT',
            study_cols: Optional[List[str]] = None,
            mapping_table_type: Optional[str] = 'custom',
            exp_ids: Optional[List[str]] = None,
            custom_mapping_sql: Optional[str] = None,
            verbose: Optional[bool] = False):
        """Return the query that joins the mapping table with metrics

        Various formats of the Metric.

        1. The Metric has multiple single day metrics and a date column.
            This format is common and easy to get

        2. The Metric has multiple cumulative day metrics and a date column.
        3. The Metric has one day of single day metrics.
        4. The Metric has one day of cumulative day metrics.

        Parameters
        ----------
        metric_table_sql : str
            Full BQ table name or SQL statement representing
            the table.
        metric_cols : list of str
            columns from the metric table that you want to select
        metric_user_col : str
            user id column in the metric table, used to join with the
            mapping table.
        mapping_user_col : str, optional
            The column name in the mapping table for the user column
        metric_date_col : str or unicode, optional
            Date column from the metric table, used to join with the mapping
            table when you have multiple days of metric values.
        metric_table_object : banjo.abtest.metric.MetricTable, optional
            Optional metric_table object for future refactoring of this function
        join_how : str or unicode
            Whether the mapping table should be INNER/RIGHT joined with the metric
            table.  If INNER, it will remove users who are in the mapping table
            but not the metrics table; this will impact the user counts.  With
            the LEFT option, users who do not appear in the metrics table are
            effectively imputed to have zero (0) for their metrics values.  If
            the zero-imputation does not make sense for your metrics, use INNER.
            An example to user INNER is when using performance metrics for which
            lower values are better.
        study_cols : list of str, optional
        mapping_table_type: str
            deprecated, "custom" is the only option
            custom: see custom_mapping_sql
        exp_ids : list of str, optional
        custom_mapping_sql : str
            The sql that defines the mapping table logic. In the simplest case,
            this is, for example,
            [sc-bq-gcs-billingonly:work_datascience.some_table]
        verbose : bool

        Returns
        -------
        query : str
            The query
        """
        # if no join is needed for this metric_table
        if metric_table_object.including_mapping_info:
            return metric_table_object.sql

        # Check input values
        if not isinstance(metric_cols, list):
            raise ValueError("metric_cols must be specified as a list.")
        if study_cols is not None and not isinstance(study_cols, list):
            raise ValueError("study_cols must be specified as a list.")
        if join_how not in ['INNER', 'RIGHT']:
            raise ValueError("Only 'INNER' and 'RIGHT' are supported for join_how")
        # Process columns to select
        metric_columns = ','.join(
            ["metric_table.{c} AS {c}".format(c=c) for c in metric_cols])
        study_cols = study_cols if study_cols is not None else []
        if study_cols:
            study_columns = '\n'.join(
                ["study_table.{c} AS {c},".format(c=c) for c in study_cols])
        else:
            study_columns = ""

        if mapping_table_type == 'custom':
            if not custom_mapping_sql:
                raise ValueError('You must provide valid SQL code that defines'
                                 ' the mapping table.')
        else:
            raise ValueError('Only valid options for mapping table type is "custom"')

        if exp_ids is None:
            where_conditions = ''
        else:
            # Filter by exp_id
            where_conditions = (
                    'WHERE '
                    + ' OR '.join(['study_table.exp_id = ' + "'{}'".format(e) for e in exp_ids])
            )

        join_conditions = (
            'AND study_table.exposure_ds <= metric_table.{date_col}'.format(date_col=metric_date_col)
        )
        period_clause = '"AB" AS period,'

        # do logic based on metric_table_object if supplied
        if metric_table_object is not None:
            if metric_table_object.sql is None:
                metric_table_sql = "({})".format(metric_table_object.sql_callable(self.analysis_start_date,
                                                                                  self.study_end_date))
            elif metric_table_object.is_materialized:
                metric_table_sql = metric_table_object.sql
            else:
                # wrap raw sql as a subquery
                metric_table_sql = "({})".format(metric_table_object.sql)

            if metric_table_object.aa or metric_table_object.cuped:
                aa_start, aa_end = self._get_aa_time_range()
                aa_metric_table_sql = metric_table_object.sql_callable(aa_start, aa_end)
                metric_table_sql = (
                    '((SELECT * FROM {ab_metric_table_sql}) UNION ALL (SELECT * FROM ({aa_metric_table_sql})))'
                ).format(ab_metric_table_sql=metric_table_sql, aa_metric_table_sql=aa_metric_table_sql)

                if metric_table_object.hourly_granularity:
                    period_clause = (
                        '\nIF(study_table.exposure_ts <= metric_table.metric_ts OR metric_table.metric_ts IS NULL, '
                        '"AB", "AA") AS period,'
                    )
                    join_conditions = (
                        '\nAND (study_table.exposure_ts <= metric_table.metric_ts OR metric_table.metric_ts < '
                        'TIMESTAMP_ADD(PARSE_TIMESTAMP("%Y%m%d", "{study_start_date}"), INTERVAL 8 HOUR))'.format(
                            study_start_date=self.study_start_date)
                    )
                else:
                    period_clause = (
                        '\nIF(study_table.exposure_ds <= metric_table.{date_col} OR metric_table.{date_col} IS NULL,'
                        '"AB", "AA") AS period,'
                    ).format(date_col=metric_date_col)
                    join_conditions = (
                        '\nAND (study_table.exposure_ds <= metric_table.{date_col} OR metric_table.{date_col} < '
                        'PARSE_TIMESTAMP("%Y%m%d", "{study_start_date}"))'.format(
                            date_col=metric_date_col, study_start_date=self.study_start_date)
                    )

            elif metric_table_object.hourly_granularity:
                # quest table can have null in event_date_hour_last_millis
                join_conditions += (
                    '\nAND (metric_table.metric_ts IS NULL OR study_table.exposure_ts <= metric_table.metric_ts)'
                )
        else:
            # wrap raw sql as a subquery
            metric_table_sql = "({})".format(metric_table_sql)

        query = JOIN_EXPOSURE_SQL.format(
            study_table=custom_mapping_sql,
            metric_table_sql=metric_table_sql,
            metric_user_col=metric_user_col,
            mapping_user_col=mapping_user_col,
            period_clause=period_clause,
            study_cols=study_columns,
            metric_cols=metric_columns,
            join_conditions=join_conditions,
            where_conditions=where_conditions,
            join_how=join_how,
            module=__name__,
            host=HOSTNAME,
            user=USER,
            metric_table_name=metric_table_object.name if metric_table_object is not None else "unknown_metric_table",
        )
        if metric_table_object is not None and not metric_table_object.quantile_metrics and (
                metric_table_object.hourly_granularity or metric_table_object.requires_winsorization):
            # pre-agg on daily and cap
            query = """
                SELECT
                  {mapping_user_col},
                  period,
                  exp_id,
                  exp_ds,
                  {study_cols}
                  {metric_cols}
                FROM
                  ({subquery})
                GROUP BY 1, 2, 3, 4
            """.format(
                mapping_user_col=mapping_user_col,
                study_cols=get_sql_selects([
                    "ANY_VALUE({col}) AS {col}".format(col=col) for col in study_cols
                ]),
                metric_cols=get_sql_selects(metric_table_object.get_winsorization_clauses(ts_column="exp_ds")),
                subquery=query
            )
        # likely that in join we need to aggregate across dates for cuped
        # if metric_table_object is not None and not metric_table_object.quantile_metrics and metric_table.cuped
        # aggregate to user, exp_id level and then add a step that computes the CUPED values
        # when doing cuped modeling aa values null needs to be set to 0

        query = sqlparse.format(query, reindent=True)
        if verbose:
            print("Generated BQL: ")
            print(query)

        return query

    def get_join_query_for_metric_table(self, metric_table):
        """Get the join query for the given MetricTable"""
        if metric_table.inner_join_with_mapping:
            join_how = "INNER"
        else:
            join_how = "RIGHT"

        # Todo?: would only pass metric_table_object be enough?
        query = self.get_join_metrics_query(
            metric_table_sql=metric_table.sql,
            metric_table_object=metric_table,
            metric_cols=metric_table.cols,
            metric_user_col=metric_table.join_key,
            join_how=join_how,
            mapping_user_col=self.mapping_user_col,
            exp_ids=[self.control_id] + self.treatment_ids,
            metric_date_col='ts',
            study_cols=self.user_group_vars,
            mapping_table_type="custom",
            custom_mapping_sql=self.get_mapping_sql(dialect=metric_table.bq_dialect)
        )
        return query

    def get_join_query_for_metric_table_with_downsampling(
            self,
            metric_table,
            downsample_percent: Optional[float] = None,
            downsample_user_col: Optional[str] = None,
            verbose: Optional[bool] = False,
        ):
        """
        Like get_join_query_for_metric_table, but applies deterministic downsampling to the mapping table.
        """
        if metric_table.inner_join_with_mapping:
            join_how = "INNER"
        else:
            join_how = "RIGHT"

        # Determine which user column to use for sampling
        sample_user_col = downsample_user_col or self.mapping_user_col

        # Build the mapping SQL, with downsampling if requested
        custom_mapping_sql = self.get_mapping_sql(dialect=metric_table.bq_dialect)
        sampled_mapping_sql = custom_mapping_sql
        if downsample_percent is not None and custom_mapping_sql:
            if not (0 < downsample_percent <= 100):
                raise ValueError("downsample_percent must be between 0 and 100.")
            sample_mod = int(downsample_percent)
            # Only wrap custom_mapping_sql in parentheses if it is not already a subquery
            stripped_mapping_sql = custom_mapping_sql.strip()
            if stripped_mapping_sql.startswith('('):
                inner = stripped_mapping_sql
            else:
                inner = stripped_mapping_sql
            # Remove redundant parentheses for table references
            if inner.startswith('`') and inner.endswith('`'):
                sampled_mapping_sql = (
                    f"(SELECT * FROM {inner} AS mapping_sampled "
                    f"WHERE MOD(ABS(FARM_FINGERPRINT(CAST({sample_user_col} AS STRING))), 100) < {sample_mod})"
                )
            else:
                sampled_mapping_sql = (
                    f"(SELECT * FROM ({inner}) AS mapping_sampled "
                    f"WHERE MOD(ABS(FARM_FINGERPRINT(CAST({sample_user_col} AS STRING))), 100) < {sample_mod})"
                )
            logging.info(f"Downsampled mapping SQL:\n{sampled_mapping_sql}")

        query = self.get_join_metrics_query(
            metric_table_sql=metric_table.sql,
            metric_table_object=metric_table,
            metric_cols=metric_table.cols,
            metric_user_col=metric_table.join_key,
            join_how=join_how,
            mapping_user_col=self.mapping_user_col,
            exp_ids=[self.control_id] + self.treatment_ids,
            metric_date_col='ts',
            study_cols=self.user_group_vars,
            mapping_table_type="custom",
            custom_mapping_sql=sampled_mapping_sql,
            verbose=verbose,
        )
        return query


    def materialize_legacy_dialect_metric_tables(self):
        """Because the joins are done only in standard; we need to materialize the legacy dialect tables

        This method is needed until all metric tables are migrated to standard
        """

        def _set_metric_table_to_materialized(mt):
            mt.bq_dialect = "standard"
            mt.is_materialized = True
            mt.sql = "`{}.{}.{}`".format(
                self.bq_project, self.dest_dataset, mt.materialized_table_name
            )

        queries = []
        destinations = []
        for metric_table in self.metric_tables:
            if metric_table.bq_dialect == "legacy" and not metric_table.is_materialized:
                if table_exists(
                        client=self.bq_client,
                        table_reference=bigquery.TableReference.from_string(
                            f"{self.bq_project}.{self.dest_dataset}.{metric_table.materialized_table_name}"
                        )
                ):
                    self.logger.info('Table {} already exists, skipping.'.format(metric_table.materialized_table_name))
                    _set_metric_table_to_materialized(metric_table)
                    continue
                queries.append(metric_table.sql)
                destinations.append("{}.{}.{}".format(
                    self.bq_project, self.dest_dataset, metric_table.materialized_table_name
                ))
        # todo(xhe): set retention here after adding support to the function
        _ = gbq.batch_submit_queries(
            queries,
            destinations,
            dialect="legacy",
            wait_for_completion=True,
            client=self.bq_client,
            write_disposition="WRITE_TRUNCATE",
            maximum_billing_tier=3,
            priority=self.bq_priority,
            bq_retry=self.bq_retry,
        )
        for metric_table in self.metric_tables:
            if metric_table.bq_dialect == "legacy" and not metric_table.is_materialized:
                _set_metric_table_to_materialized(metric_table)

    def check_joins_queries(self, overwrite=False):
            self.materialize_legacy_dialect_metric_tables()
            queries = []
            dialects = []
            test_queries = []  # <-- Collect results here
            for metric_table in self.metric_tables:
                table_name = self.get_joined_table_name(metric_table)
                if (not overwrite
                        and table_exists(
                                client=self.bq_client,
                                table_reference=bigquery.TableReference.from_string(
                                    f"{self.bq_project}.{self.dest_dataset}.{table_name}"
                                )
                        )
                ):
                    self.logger.info('Table {} already exists, skipping. '
                                    'use overwrite=True if want to recheck the query '
                                    'to overwrite it'.format(table_name))
                    continue
                queries.append(self.get_join_query_for_metric_table(metric_table))
                dialects.append(metric_table.bq_dialect)
            for query, dialect in zip(queries, dialects):
                test_query = gbq.check_query(
                    query,
                    client=self.bq_client,
                    dialect=dialect,
                )
                self.logger.info('Checked Query for metric table: {}. \nNo Errors Reported. \nTotal Bytes to be processed: {}.'.format(table_name, test_query.total_bytes_processed))
                test_queries.append(test_query)  # <-- Append each result
            return test_queries  # <-- Return the list

    def run_joins(self,
                  overwrite=False,
                  ):
        self.materialize_legacy_dialect_metric_tables()
        queries = []
        destinations = []
        dialects = []
        for metric_table in self.metric_tables:

            table_name = self.get_joined_table_name(metric_table)

            if (not overwrite
                    and table_exists(
                            client=self.bq_client,
                            table_reference=bigquery.TableReference.from_string(
                                f"{self.bq_project}.{self.dest_dataset}.{table_name}"
                            )
                    )
            ):
                self.logger.info('Table {} already exists, skipping. '
                                 'use overwrite=True if want to rerun the query '
                                 'to overwrite it'.format(table_name))
                continue
            queries.append(self.get_join_query_for_metric_table(metric_table))
            destinations.append("{}.{}.{}".format(self.bq_project, self.dest_dataset, table_name))
            dialects.append(metric_table.bq_dialect)

        jobs = gbq.batch_submit_queries(
            queries,
            destinations,
            dialects,
            wait_for_completion=True,
            client=self.bq_client,
            write_disposition="WRITE_TRUNCATE",
            maximum_billing_tier=3,
            priority=self.bq_priority,
            bq_retry=self.bq_retry,
        )

        self.jobs = jobs

    def run_joins_with_sampling(self, sampling_percent, overwrite=False):
        """
        Like run_joins, but applies deterministic user-based downsampling to the mapping table in each query.
        """
        self.materialize_legacy_dialect_metric_tables()
        queries = []
        destinations = []
        dialects = []
        for metric_table in self.metric_tables:
            table_name = self.get_joined_table_name(metric_table)
            if (not overwrite
                    and table_exists(
                            client=self.bq_client,
                            table_reference=bigquery.TableReference.from_string(
                                f"{self.bq_project}.{self.dest_dataset}.{table_name}"
                            )
                    )
            ):
                self.logger.info('Table {} already exists, skipping. '
                                'use overwrite=True if want to rerun the query '
                                'to overwrite it'.format(table_name))
                continue
            query = self.get_join_query_for_metric_table_with_downsampling(
                metric_table=metric_table,
                downsample_percent=sampling_percent
            )
            queries.append(query)
            destinations.append("{}.{}.{}".format(self.bq_project, self.dest_dataset, table_name))
            dialects.append(metric_table.bq_dialect)

        jobs = gbq.batch_submit_queries(
            queries,
            destinations,
            dialects,
            wait_for_completion=True,
            client=self.bq_client,
            write_disposition="WRITE_TRUNCATE",
            maximum_billing_tier=3,
            priority=self.bq_priority,
            bq_retry=self.bq_retry,
        )

        self.jobs = jobs

    def get_metrics_by_type(self, type_, excluded_types=None):
        excluded_metrics = []
        if excluded_types:
            for excluded_type in excluded_types:
                excluded_metrics.extend(self.get_metrics_by_type(excluded_type))

        metrics_list = [mt.get_metrics_by_type(type_)
                        for mt in self.metric_tables]
        metrics = list(itertools.chain.from_iterable(metrics_list))
        return [m for m in metrics if m not in excluded_metrics]

    def agg(self,
            kind,
            user_group_bys=None,
            group_filters=None,
            cumulative=None,
            cumulative_trend=False,
            **kwargs,
            ):
        """Aggregate the user level table to (experiment ID, metric) level sufficient stats"""
        calculators = []
        user_group_bys = user_group_bys or []
        results_key = self.get_results_key(kind, user_group_bys)
        self._sufficient_stats[results_key] = calculators
        if cumulative is None:
            cumulative = kind == CUMULATIVE

        for metric_table in self.metric_tables:
            if (cumulative and not metric_table.cumulative) or (not cumulative and not metric_table.daily):
                continue

            full_table_name = self.get_joined_table_name(metric_table, full=True)

            calculators.extend(
                metric_table.get_calculators(
                    self,
                    full_table_name,
                    date_col='exp_ds',
                    user_col=self.mapping_user_col,
                    user_group_cols=user_group_bys,
                    cumulative=cumulative,
                    cumulative_trend=cumulative_trend,
                    **kwargs,
                )
            )

        agg_results = gbq.batch_read_gbq(
            queries=[calc.query for calc in calculators],
            client=self.bq_client,
            project_id=self.bq_project,
            priority=self.bq_priority,
            parallel=self.bq_priority == "BATCH",
            bq_retry=self.bq_retry,
            use_bqstorage_api=self.configurations.get("use_bqstorage_api", True),
            dialect=[calc.bq_dialect for calc in calculators],
        )

        for calculator, agg_df in zip(calculators, agg_results):
            if "period" not in agg_df.columns:
                agg_df["period"] = "AB"
            if group_filters is not None:
                for k, v in six.iteritems(group_filters):
                    agg_df = agg_df.loc[agg_df[k].isin(v), :].copy()
            if not calculator.metric_table.inner_join_with_mapping:
                # Set user counts from the mapping table
                if "user_count" in agg_df.columns:
                    agg_df.drop(columns=["user_count"], inplace=True)

                if self._calculate_aa:
                    join_and_group_by_keys = ["exp_id", "exp_ds", "period"] + user_group_bys
                else:
                    join_and_group_by_keys = ["exp_id", "exp_ds"] + user_group_bys
                agg_df = agg_df.merge(
                    self.sample_sizes[results_key],
                    how="left",
                    on=join_and_group_by_keys,
                    validate="many_to_one",
                )
                # keep only top subgroups to avoid computing (and displaying) subsets that are too small to be
                # useful

                if user_group_bys:
                    largest_subgroups_to_keep_per_breakdown = self.configurations.get(
                        "largest_subgroups_to_keep_per_breakdown", 500)
                    # todo(xhe): we could compute this outside of this function
                    # to speed things up
                    top_subgroups = (
                        self.sample_sizes[results_key]
                        .loc[:, user_group_bys + ["user_count"]]
                        .groupby(user_group_bys, dropna=False, sort=False)
                        .max()  # use max as we might be aggregating across days, this is aproximate anyways
                        .reset_index()
                        .sort_values(["user_count"], ascending=False)
                        .head(
                            largest_subgroups_to_keep_per_breakdown
                        )
                        .drop(columns=["user_count"])
                    )
                    # do join only if necessary
                    if len(top_subgroups.index) >= largest_subgroups_to_keep_per_breakdown:
                        agg_df = agg_df.merge(
                            top_subgroups,
                            how="inner",
                            on=user_group_bys,
                            validate="many_to_one",
                        )
            if user_group_bys:
                for group_by in user_group_bys:
                    agg_df[group_by] = agg_df[group_by].astype(str)
            # remove timezone
            if pd.api.types.is_datetime64tz_dtype(agg_df["exp_ds"]):
                agg_df["exp_ds"] = agg_df["exp_ds"].dt.tz_convert(None)
            calculator.query_results = agg_df

    def compute_test_results_from_sufficient_stats(
            self,
            kind,
            user_group_bys=None,
    ):
        """Perform statistical tests using the sufficient statistics"""
        results_key = self.get_results_key(kind, user_group_bys)
        calculators = self._sufficient_stats[results_key]

        all_results = []
        for calculator in calculators:
            results = calculator.get_analysis_results(
                control_id=self.control_id,
                treatment_ids=self.treatment_ids,
            )

            if results is None:
                continue

            results['sspm_pval'] = results.apply(
                lambda x: self.check_sspm(control_user_count=x.count_control,
                                          treatment_user_count=x.count_treatment,
                                          treatment_id=x.treatment_id),
                axis=1)

            all_results.append(results)

        if all_results:
            df_all_results = pd.concat(all_results, sort=True).reset_index(None, drop=True)
            df_all_results = banjo.utils.pandas.dataframe.approx_drop_duplicates(df_all_results)
            df_all_results["result_kind"] = kind
            if user_group_bys:
                groups = sorted(user_group_bys)
                df_all_results["breakdown_dimensions"] = "|".join(groups)
                df_all_results["breakdown_values"] = df_all_results[groups[0]].str.cat(
                    df_all_results[groups[1:]],
                    sep='|',
                    na_rep=""
                )
                # Reorder breakdown values
                df_all_results["breakdown_values"] = df_all_results["breakdown_values"].astype(
                    pd.api.types.CategoricalDtype(
                        Report.infer_breakdown_order(df_all_results["breakdown_values"].unique()),
                        ordered=True
                    )
                )
                for user_group_by in user_group_bys:
                    df_all_results[user_group_by] = df_all_results[user_group_by].astype(
                        pd.api.types.CategoricalDtype(
                            Report.infer_breakdown_order(df_all_results[user_group_by].unique()),
                            ordered=True
                        )
                    )
            else:
                df_all_results["breakdown_dimensions"] = "Overall"
                df_all_results["breakdown_values"] = "Overall"
                df_all_results["breakdown_values"] = df_all_results["breakdown_values"].astype('category')

            if kind == CUMULATIVE:
                use_bh_significance = self.configurations.get("use_bh_significance", False)
                if use_bh_significance and 'p_value' in df_all_results.columns:
                    fdr = self.configurations.get("fdr", 0.05)
                    df_all_results['fdr'] = fdr
                    for treatment_id in df_all_results['treatment_id'].unique().tolist():
                        bh_q_values, bh_significant = calculate_bh_significance(
                            df_all_results.loc[df_all_results.treatment_id == treatment_id, 'p_value'].tolist(), fdr)
                        df_all_results.loc[
                            df_all_results.treatment_id == treatment_id, 'bh_significant'] = bh_significant
                        df_all_results.loc[df_all_results.treatment_id == treatment_id, 'bh_q_value'] = bh_q_values
            self.results[results_key] = df_all_results

    def compute_hte_results(self,
                            kind,
                            user_group_bys,
                            interaction_p_value_threshold=0.001):
        # use analysis.apply_hte_test to perform HTE test
        results_key = self.get_results_key(kind, user_group_bys)

        interaction_p_value_threshold = self.configurations.get(
            'interaction_p_value_threshold', interaction_p_value_threshold
        )

        if results_key in self.results:
            results_df = self.results[results_key]

            if "quantile" not in results_df.columns:
                results_df["quantile"] = "NA"
            results_df.loc[results_df["quantile"].isna(), "quantile"] = "NA"

            hte_group_by_dims = ["exp_ds", "control_id", "treatment_id", "metric", "quantile"]
            self.results[results_key] = results_df.groupby(hte_group_by_dims) \
                .apply(apply_hte_test,
                       interaction_p_value_threshold=interaction_p_value_threshold
                       ).reset_index(None, drop=True)

    def aggregate(self,
                  kind,
                  user_group_bys=None,
                  group_filters=None,
                  extra_cont_stats=None,
                  cumulative_trend=True):
        warnings.warn(DeprecationWarning("Use the self.agg method instead: pd.concat(self.agg())"))
        all_results = []
        for metric_table in self.metric_tables:
            full_table_name = self.get_joined_table_name(metric_table, full=True)
            cont_cols = metric_table.get_metrics_by_dist('cont')
            bin_cols = metric_table.get_metrics_by_dist('bin')

            agg = aggregate_metrics_by_day(
                full_table_name,
                self.bq_project,
                date_col='exp_ds',
                user_col=self.mapping_user_col,
                cont_cols=cont_cols,
                bin_cols=bin_cols,
                user_group_cols=user_group_bys,
                cumulative=kind == CUMULATIVE,
                verbose=False,
                extra_cont_stats=extra_cont_stats,
                cumulative_trend=cumulative_trend,
                bq_client=self.bq_client,
                bq_retry=self.bq_retry,
            )

            if group_filters is not None:
                for k, v in six.iteritems(group_filters):
                    agg = agg.loc[agg[k].isin(v), :]

            all_results.append(agg)

        return pd.concat(all_results)

    def calculate(self,
                  kind,
                  user_group_bys=None,
                  group_filters=None,
                  cumulative=None,
                  cumulative_trend=False,
                  ):
        """
        Parameters
        ----------
        kind
        user_group_bys
        group_filters
        cumulative
        cumulative_trend

        Returns
        -------

        """
        self.agg(kind=kind, user_group_bys=user_group_bys, group_filters=group_filters,
                 cumulative=cumulative, cumulative_trend=cumulative_trend)
        self.compute_test_results_from_sufficient_stats(kind=kind, user_group_bys=user_group_bys)

        if self.configurations.get('interaction_p_value_threshold') is not None:
            interaction_p_value_threshold = self.configurations.get('interaction_p_value_threshold')
        if user_group_bys:
            self.compute_hte_results(kind=kind, user_group_bys=user_group_bys)

    def _update_report_sample_size(self, sample_sizes_query_results, sample_size_group_bys):
        if self._calculate_aa:
            for idx, user_group_bys in enumerate(sample_size_group_bys):
                last_day_sample_size = self._get_last_day(
                    sample_sizes_query_results[idx], replace_ds_with_range=False
                )

                ab_cumulative_sample_size = last_day_sample_size.copy()
                ab_cumulative_sample_size["period"] = "AB"
                aa_cumulative_sample_size = last_day_sample_size.copy()
                aa_cumulative_sample_size["period"] = "AA"
                cuped_cumulative_sample_size = last_day_sample_size.copy()
                cuped_cumulative_sample_size["period"] = "CUPED"
                self.sample_sizes[self.get_results_key(CUMULATIVE, user_group_bys)] = pd.concat(
                    [ab_cumulative_sample_size, aa_cumulative_sample_size, cuped_cumulative_sample_size]).reset_index()

                ab_daily_sample_size = sample_sizes_query_results[idx].copy()
                ab_daily_sample_size["period"] = "AB"
                # cuped_daily_sample_size = sample_sizes_query_results[idx].copy()
                # cuped_daily_sample_size["period"] = "CUPED"
                aa_daily_sample_size = aa_cumulative_sample_size.copy()
                aa_daily_sample_size.drop(columns=["exp_ds"], inplace=True)
                aa_start, aa_end = self._get_aa_time_range()
                aa_daily_sample_size = pd.concat([
                    aa_daily_sample_size.assign(exp_ds=date)
                    for date in pd.date_range(aa_start, aa_end, tz="UTC")
                ])
                self.sample_sizes[self.get_results_key(DAILY, user_group_bys)] = pd.concat(
                    [ab_daily_sample_size, aa_daily_sample_size]).reset_index()
            return

        for idx, user_group_bys in enumerate(sample_size_group_bys):
            self.sample_sizes[self.get_results_key(CUMULATIVE, user_group_bys)] = self._get_last_day(
                sample_sizes_query_results[idx], replace_ds_with_range=False
            )
            self.sample_sizes[self.get_results_key(DAILY, user_group_bys)] = sample_sizes_query_results[idx]

    def count_sample_sizes(self):
        """Calculate the daily sample size for overall, breakdown

        The results are used in statistical tests
        """
        mapping_sql = self.get_mapping_sql("standard")
        sample_size_query = """
        SELECT
          exp_ds, exp_id {{user_group_cols}}, count(*) AS user_count
        FROM {mapping_sql},
        UNNEST([{timestamps}]) AS exp_ds
        WHERE exposure_ds <= exp_ds
        -- filter by exp_id
        GROUP BY exp_ds, exp_id {{user_group_cols}}
        ORDER BY exp_ds, exp_id
        """.format(
            mapping_sql=mapping_sql,
            timestamps=get_timestamp_sql(
                pd.date_range(self.analysis_start_date, self.study_end_date)
            ),
        )
        queries = []
        sample_size_group_bys = [[]] + self.user_group_by_list
        for user_group_bys in sample_size_group_bys:
            queries.append(
                sample_size_query.format(
                    user_group_cols=",{}".format(
                        ", ".join(user_group_bys)
                    ) if user_group_bys else ""
                )
            )
        # note to self: need to make sure adding sample sizes do not change the size of the data frame
        # aa, ab, cuped could be treated as another user group by in aggregation and comoparisono
        sample_sizes_query_results = gbq.batch_read_gbq(
            queries=queries,
            client=self.bq_client,
            project_id=self.bq_project,
            priority=self.bq_priority,
            parallel=self.bq_priority == "BATCH",
            bq_retry=self.bq_retry,
            use_bqstorage_api=self.configurations.get("use_bqstorage_api", True),
            dialect="standard",
        )
        self._update_report_sample_size(sample_sizes_query_results, sample_size_group_bys)
        return sample_sizes_query_results

    def visualize(self,
                  result_table: pd.DataFrame,
                  group_vars: Optional[List] = None,
                  group_filters: Optional[Dict] = None,
                  facet_bys: Optional[List] = None,
                  format_pvalue: bool = True,
                  last_day_only: bool = False,
                  display_period: Optional[List] = None,
                  show_forest: bool = False,
                  show_trend: bool = False,
                  show_table: bool = False,
                  display_items: Optional[List] = None,
                  summary_metrics_type: Optional[Literal["cumulative", "daily"]] = None,
                  table_format: str = 'table',
                  stat_fmtr: Optional[str] = None,
                  extra_table_cols: Optional[List] = None,
                  should_pivot_table: Optional[bool] = None,
                  pivot_table_rows: Optional[List] = None,
                  pivot_table_cols: Optional[List] = None,
                  title: Optional[str] = None,
                  highlight_sspm: bool = False) -> Optional[Dict]:
        if result_table.empty:
            return

        if title is None:
            title = (self.study_name
                     + ': ' + self.analysis_start_date
                     + ' to ' + self.study_end_date)

        if display_items:
            if "table" in display_items:
                show_table = True
            if "forest" in display_items:
                show_forest = True
            if "trend" in display_items:
                show_trend = True

        if extra_table_cols is None:
            extra_table_cols = []
        if group_vars is None:
            group_vars = []
        if facet_bys is None:
            facet_bys = list(group_vars)

        if group_filters is not None:
            for k, v in six.iteritems(group_filters):
                result_table = (
                    result_table.loc[result_table[k].isin(v), :]
                    .copy()
                )

        # remove facets not existing in the data frame
        facet_bys = [facet for facet in facet_bys if
                     facet in result_table.columns]

        # add facets to extra_table_cols so that they are displayed:
        for facet in facet_bys:
            if facet not in extra_table_cols and facet not in group_vars:
                extra_table_cols.append(facet)

        if self._calculate_aa:
            aa_start, aa_end = self._get_aa_time_range()

            def _format_date(date_str):
                return pd.to_datetime(date_str).strftime("%m/%d")

            result_table['period'] = result_table['period'].replace(
                {
                    'AA': 'AA ({}-{})'.format(_format_date(aa_start), _format_date(aa_end)),
                    'AB': 'AB ({}-{})'.format(_format_date(self.analysis_start_date),
                                              _format_date(self.study_end_date)),
                    'CUPED': 'CUPED ({}-{})'.format(_format_date(self.analysis_start_date),
                                                    _format_date(self.study_end_date)),
                }
            )

        # Translate to human readable names
        result_table['metric_sql_name'] = result_table['metric']
        result_table['metric'] = result_table['metric'].replace(
            self.metric_name_mapping
        )
        result_table['desired_direction'] = result_table['metric_sql_name'].replace(
            self.metric_direction_mapping
        )

        if isinstance(result_table['metric_sql_name'].dtype, pd.api.types.CategoricalDtype):
            metrics_order = result_table['metric_sql_name'].dtype.categories.tolist()
            result_table['metric'] = result_table['metric'].astype(
                pd.api.types.CategoricalDtype(
                    [
                        self.metric_name_mapping.get(_metric, _metric)
                        for _metric in metrics_order
                    ],
                    ordered=True
                )
            )
        # Check facet_bys validity
        if facet_bys:
            max_per_facet = (result_table.groupby(facet_bys + ['exp_ds', 'metric'])
                             ['exp_ds'].count().max()
                             )
            if max_per_facet > 1 and (show_forest or show_trend):
                raise ValueError(
                    'The data frame has a maximum of {} rows per facet, metric, and date. This might '
                    'cause results to be rendered incorrectly. Try these:'
                    '\n1. Use `group_filters` to keep only one row per group, metric, and date. '
                    '\n2. Remove `facet_bys` argument to use the default setting.'
                    '\n3. This may also be caused by having the same sql field names in multiple '
                    'metric tables. SQL field names must be unique across metric tables.'
                    '\n facets given: {}'.format(max_per_facet, facet_bys)
                )
            if len(facet_bys) > 2:
                if show_forest or show_trend:
                    show_forest = False
                    show_trend = False
                    self.logger.warning(
                        'Charts are only shown when two or fewer facets are specified. '
                        'We have %d: %s' % (len(facet_bys), ', '.join(facet_bys)))

        if last_day_only:
            table_df = self._get_last_day(result_table)
        else:
            table_df = result_table

        if summary_metrics_type is not None:
            table_df = table_df.loc[
                       table_df.metric_sql_name.isin(
                           self.get_metrics_by_type(summary_metrics_type)),
                       :
                       ]
        if display_period is not None:
            table_df = table_df[table_df['period'].apply(lambda x: any(x.startswith(period) for period in display_period))]
        graphs = {}
        if show_table:
            if not stat_fmtr:
                stat_fmtr = self.configurations.get('stat_fmtr')
            if should_pivot_table is None:
                should_pivot_table = self.configurations.get('pivot_table', False)
            if should_pivot_table:
                if pivot_table_rows is None and pivot_table_cols is None:
                    pivot_table_rows = ['Date', 'Metric'] + group_vars[:-1]
                    if self._calculate_aa:
                        pivot_table_rows.append('period')

                    pivot_table_cols = ['control_id', 'treatment_id'] + group_vars[-1:]
                    if self._results_have_quantile_metrics(table_df):
                        table_df.loc[:, 'quantile'].fillna("NA", inplace=True)  # OK as we always work with a copy
                        pivot_table_rows.append('quantile')
                    # move Date to the last of the pivot rows if this is a daily result table
                    if summary_metrics_type == DAILY:
                        logging.info("move date to the last pivot row position")
                        pivot_table_rows.append(pivot_table_rows.pop(0))

                # Add sample size if the results in the entire column have the same sample sizes
                count_fields = ["count_treatment", "count_control"]
                table_df_group_by_cols = table_df.loc[:, count_fields + pivot_table_cols].groupby(pivot_table_cols)
                if all(table_df_group_by_cols.nunique()[field].le(1).all() for field in count_fields):
                    table_df["Sample Sizes"] = table_df.apply(
                        lambda x: visualize_sample_sizes(x.count_treatment, x.count_control, x.get("sspm_pval")),
                        axis=1
                    )
                    pivot_table_cols.append("Sample Sizes")
                else:
                    self.logger.info("Sample sizes not displayed for pivot table {} by {}".format(
                        pivot_table_rows, pivot_table_cols,
                    ))

                graphs['table'] = pivot_table(
                    table_df,
                    output=table_format,
                    stat_fmtr=stat_fmtr,
                    rows=pivot_table_rows,
                    cols=pivot_table_cols,
                    printer=self.ab_printer,
                    bg_color_for_significance=self.configurations.get(
                        'bg_color_for_significance', True
                    ),
                    report=self,
                    highlight_sspm=highlight_sspm
                )
                freeze_panes = (
                    len(pivot_table_cols) + 1,
                    len(pivot_table_rows)
                )
            else:
                graphs['table'] = tabulate(
                    table_df,
                    format_pvalue=format_pvalue,
                    output=table_format,
                    stat_fmtr=stat_fmtr,
                    other_cols=group_vars + extra_table_cols,
                    printer=self.ab_printer,
                    bg_color_for_significance=self.configurations.get(
                        'bg_color_for_significance', True
                    ),
                    report=self,
                )
                freeze_panes = (1, 0)

            sheet_name = "{}_{}".format(
                self._excel_sheet_seq_no,
                ".".join(group_vars) if group_vars else "Overall",
            )
            self.write_excel(graphs['table'], sheet_name=sheet_name, freeze_panes=freeze_panes)

        facet_col = None
        facet_row = None
        facet_col_wrap = None
        if facet_bys:
            facet_col = facet_bys[0]
            if len(facet_bys) > 1:
                facet_row = facet_bys[1]
            else:
                facet_col_wrap = 2

        if show_forest:
            # determine size/aspect ratio of the plot by the number of metrics
            if summary_metrics_type is not None:
                num_metrics = len(self.get_metrics_by_type(summary_metrics_type))
            else:
                num_metrics = len(table_df.metric.unique())
            forest_size = max(2.5, num_metrics // 3)
            forest_aspect = 2 / (num_metrics // 20 + 1)

            graphs['forest'] = forestplot(
                table_df,
                y='metric',
                point='pct_diff',
                low='pct_diff_lo',
                high='pct_diff_hi',
                sort_by_value=False,
                size=forest_size,
                aspect=forest_aspect,
                title=title,
                sharex=False,
                facet_row=facet_row,
                facet_col=facet_col,
                facet_col_wrap=facet_col_wrap,
            )

        if show_trend:
            facet_hue = None
            if facet_bys and len(facet_bys) > 1:
                facet_hue = facet_bys[1]

            graphs['trend'] = deltatrendplot(
                result_table,
                date='exp_ds',
                point='pct_diff',
                low='pct_diff_lo',
                high='pct_diff_hi',
                metric='metric',
                aspect=4 if facet_bys else 6,
                size=2,
                title=title,
                facet_col=facet_col,
                facet_hue=facet_hue,
            )

        return graphs if graphs else None

    def visualize_hte_breakdowns(self,
                                 result_table,
                                 user_level=True,
                                 metric_level=False,
                                 interaction_p_value_threshold=0.001,
                                 stat_fmtr=None,
                                 hide_insignificant_hte_plots=False
                                 ):

        if result_table.empty:
            return
        if user_level and metric_level:
            raise ValueError("We don't support summarizing user_level and metric_level together. "
                             "Please call this function twice to output both.")
        if user_level:
            dimensions_field_name = "breakdown_dimensions"
            values_field_name = "breakdown_values"
            hte_tested = "hte_tested"
            non_msg = "Not enough qualified groups for HTE test"
            stats = "interaction_p_value"
        else:  # metric_level
            dimensions_field_name = "metric_breakdown_dimensions"
            values_field_name = "metric_breakdown_values"
            hte_tested = "metric_breakdown_hte_tested"
            non_msg = "Less than 2 groups with > 0 event counts or ratio metrics, which we don't currently support"
            stats = "chisq_dist"

        # remove groups not used in hte_tested
        result_table = result_table[
            (result_table[hte_tested]) & (-result_table[hte_tested].isna())
            ].copy()

        if len(result_table) <= 1:
            print(non_msg)
            return

        # Format 'period' col for display results from retro AA
        if self._calculate_aa:
            aa_start, aa_end = self._get_aa_time_range()

            def _format_date(date_str):
                return pd.to_datetime(date_str).strftime("%m/%d")

            result_table['period'] = result_table['period'].replace(
                {
                    'AA': 'AA ({}-{})'.format(_format_date(aa_start), _format_date(aa_end)),
                    'AB': 'AB ({}-{})'.format(_format_date(self.analysis_start_date),
                                              _format_date(self.study_end_date)),
                }
            )

        # Translate to human readable names
        result_table['metric_sql_name'] = result_table['metric']
        result_table['metric'] = result_table['metric'].replace(
            self.metric_name_mapping)
        result_table['desired_direction'] = result_table['metric_sql_name'].replace(
            self.metric_direction_mapping)

        if "quantile" not in result_table.columns:
            result_table["quantile"] = "NA"
        result_table.loc[result_table["quantile"].isna(), "quantile"] = "NA"

        # for quantile metrics, concatenate metric and quantile into final name
        result_table.loc[result_table['quantile'] != "NA", 'metric'] = (
                result_table.loc[result_table['quantile'] != "NA", 'metric'].astype(str)
                + ' P'
                + result_table.loc[result_table['quantile'] != "NA", 'quantile'].astype(str)
        )

        interaction_p_value_threshold = self.configurations.get(
            'interaction_p_value_threshold', interaction_p_value_threshold
        )

        hte_group_by_dims = ["control_id", "treatment_id"]
        for (control_id, treatment_id), result_table_treatment in result_table.groupby(hte_group_by_dims):
            self.print_html(
                '{} vs {}'.format(
                    control_id, treatment_id
                ),
                'h3'
            )
            for metric_name, result_table_batch in result_table_treatment.groupby("metric"):
                self.print_html(
                    'Metric: {}'.format(metric_name), 'h4'
                )
                # An empty string with id '<p id="{treatment_id}-{metric_name}-HTE-plots"> </p>'
                self.ab_printer.print_text(
                    text=' ',
                    html_tag='p',
                    attributes={
                        'id': "{treatment_id}-{metric_name}-HTE-plots".format(
                            treatment_id=treatment_id,
                            metric_name=metric_name
                        )
                    }
                )
                if hide_insignificant_hte_plots:
                    result_table_batch = result_table_batch[result_table_batch['hte_outcome'].isin(['hte_detected'])].copy()
                if len(result_table_batch) == 0:
                    print(f'No HTE is detected for metric "{metric_name}" in treatment {treatment_id}')
                    continue
                # Sort dimensions_field_name from the most heterogeneous to the least
                sorting_lookup_dict = dict(zip(
                    result_table_batch[dimensions_field_name],
                    result_table_batch[stats],
                ))

                result_table_batch[dimensions_field_name] = pd.Categorical(
                    result_table_batch[dimensions_field_name],
                    categories=sorted(
                        result_table_batch[dimensions_field_name].unique(),
                        key=lambda x: sorting_lookup_dict.get(x, None),
                        reverse=metric_level,
                    ),
                    ordered=True,
                )
                for dimensions, result_table_subset in result_table_batch.reset_index().groupby([dimensions_field_name]):
                    self.print_html(
                        'Breakdown by user groups: {}'.format(dimensions),
                        'h5'
                    )
                    # stat_fmtr based on metric dist (quantile vs. not)
                    metric_dist = result_table_subset["dist"].iloc[0]
                    if not stat_fmtr:
                        stat_fmtr = "{pct_diff:,.2f}% ({avg_control:,.3}→{avg_treatment:,.3}, {p_value:.4})"
                        if metric_dist == "quantile":
                            stat_fmtr = (
                                "{pct_diff:,.2f}% ({quantile_control:,.3}→{quantile_treatment:,.3}, {p_value:.4})<br>"
                                "Event count: {pct_diff_event_count:,.2f}% "
                                "({avg_volume_control:,.3}→{avg_volume_treatment:,.3}, {p_value_event_count:.4})"
                            )
                    # print table and plots
                    pivot_table_rows = ['control_id', 'treatment_id']
                    if self._calculate_aa:
                        pivot_table_rows.append('period')
                    if user_level:
                        pivot_table_cols = ['Metric', dimensions_field_name, values_field_name, 'Sample Sizes']
                        result_table_subset.loc[:, "Sample Sizes"] = result_table_subset.apply(
                            lambda x: visualize_sample_sizes(x.count_treatment, x.count_control, x.get("sspm_pval")),
                            axis=1
                        )
                        result_table_subset_ABonly = result_table_subset[
                            result_table_subset.period.str[:2] == 'AB'] if self._calculate_aa else result_table_subset
                        # pivot table of Stats
                        pivot_table(
                            result_table_subset,
                            output="table",
                            stat_fmtr=stat_fmtr,
                            rows=pivot_table_rows,
                            cols=pivot_table_cols,
                            report=self,
                        )
                        # bar chart of treatment effect by user groups
                        user_hte_plot(
                            result_table_subset_ABonly,
                            # this will print out hte plot in addition to any messages
                            metric="metric",
                            interaction_p_value_threshold=interaction_p_value_threshold,
                        )
                        # hyperlink back to summary table '<a href="#{treatment_id}-{metric_name}-summary-table">Back to summary table</a>'
                        self.ab_printer.print_text(
                            text='Back to summary table',
                            html_tag='a',
                            attributes={
                                'href': "#{treatment_id}-{metric_name}-summary-table".format(
                                    treatment_id=treatment_id,
                                    metric_name=metric_name
                                )
                            }
                        )
                    elif metric_level and metric_dist in ['cont', 'bin', 'quantile']:
                        self.print_html(
                            '{} by field: {}'.format(
                                metric_name,
                                dimensions
                            ),
                            'h4'
                        )
                        pivot_table_cols = ['Metric', dimensions_field_name, values_field_name]
                        # pivot table of Stats
                        pivot_table(result_table_subset,
                                    output='table',
                                    stat_fmtr=stat_fmtr,
                                    # intentionally flip cols and rows to make table long (vs. wide)
                                    cols=pivot_table_rows,
                                    rows=pivot_table_cols,
                                    report=self,
                                    )
                        # pivot table of metric breakdown contribution %, distribution % and contribution
                        result_table_subset_ABonly = result_table_subset[
                            result_table_subset.period.str[:2] == 'AB'] if self._calculate_aa else result_table_subset
                        # to distribution ratio
                        df = pd.pivot_table(result_table_subset_ABonly,
                                            index=["metric", dimensions_field_name, values_field_name],
                                            values=['metric_breakdown_dist_pct',
                                                    'metric_breakdown_abs_diff_pct',
                                                    'metric_breakdown_abs_diff_to_dist_ratio']
                                            )
                        # adjust table format
                        df["Contribution %"] = (df["metric_breakdown_abs_diff_pct"] * 100).apply("{0:.2f}%".format)
                        df["Distribution %"] = (df["metric_breakdown_dist_pct"] * 100).apply("{0:.2f}%".format)
                        df["Contribution to Distribution Ratio"] = df["metric_breakdown_abs_diff_to_dist_ratio"].apply(
                            "{0:.1f}".format)
                        self.print_html(
                            df[["Contribution %", "Distribution %", "Contribution to Distribution Ratio"]].style.render(),
                            "div")
                        # show plots
                        if metric_dist == "quantile":
                            field_hte_plot_quantile_stats(result_table_subset_ABonly,
                                                          metric_name=metric_name,
                                                          )
                            field_hte_plot(result_table_subset_ABonly,
                                           metric_name="Event Count of " + metric_name,
                                           avg_control="avg_volume_control",
                                           avg_treatment="avg_volume_treatment",
                                           pct_diff="pct_diff_event_count",
                                           pct_diff_lo="pct_diff_lo_event_count",
                                           pct_diff_hi="pct_diff_hi_event_count",
                                           )
                        else:
                            field_hte_plot(result_table_subset_ABonly,
                                           metric_name=metric_name,
                                           )
                    elif metric_level and metric_dist == "ratio":
                        print("We currently don't support metric fields breakdown for ratio metrics.")

    def visualize_hte_summary(self,
                              user_level=True,
                              metric_level=False,
                              ):
        if not self.results:
            return
        if user_level and metric_level:
            raise ValueError("currently we don't support user_level and metric_level both True at the same time")
        if user_level:
            dimensions = "breakdown_dimensions"
            exclude_dimensions = "metric_breakdown_dimensions"
            hte_tested = "hte_tested"
            hte_outcome = "hte_type"
            stats = "interaction_p_value"
            dimensions_plus_stats = "breakdown_dimensions_p_value"
            hte_stats = "HTE p_value"
            not_tested_msg = (
                '<span style="color:red">Some user groups are not included in HTE test due to small sample size '
                '(<10k) or if they are groups like none, nan, unknown or other.</span>'
            )
            na_explain = "N/A <br>(less than 2 groups with 10k+ users)"
            self.train_hte_tree()
        else:  # metric_level:
            dimensions = "metric_breakdown_dimensions"
            exclude_dimensions = "breakdown_dimensions"
            hte_tested = "metric_breakdown_hte_tested"
            hte_outcome = "metric_breakdown_hte_outcome"
            stats = "chisq_dist"
            dimensions_plus_stats = "metric_breakdown_dimensions_chisq_dist"
            hte_stats = "chisq_dist"
            not_tested_msg = (
                '<span style="color:red">Metric field(s) with zero event counts are not included in the test.</span>'
            )
            na_explain = (
                "N/A <br>(less than 2 groups with > 0 event count or ratio metrics, which we don't currently support)"
            )


        normalized_results = self.get_normalized_results()
        if exclude_dimensions in normalized_results.columns:
            normalized_results = (
                normalized_results[normalized_results[exclude_dimensions] == "Overall"].copy()
            )
        if not all(normalized_results[hte_tested]):
            self.print_html(not_tested_msg, "p")

        # exclude overall results
        normalized_results = (
            normalized_results[(normalized_results[dimensions] != "Overall")]
            .copy()
            .reset_index(None, drop=True)
        )
        # human readable name
        normalized_results['origin_metric_name'] = normalized_results['metric']
        normalized_results['metric'] = normalized_results['metric'].replace(self.metric_name_mapping)
        # revert the added suffix _ by generate_breakdown_field_dictionary
        normalized_results[dimensions] = (
            normalized_results[dimensions].replace(
                "_$", "", regex=True
            )
        )

        # combine metric and quantile into final metric name
        if "quantile" not in normalized_results.columns:
            normalized_results["quantile"] = "NA"
        normalized_results.loc[normalized_results["quantile"].isna(), "quantile"] = "NA"
        normalized_results.loc[normalized_results['quantile'] != "NA", 'metric'] = (
                normalized_results['metric'].astype(str) + ' P' + normalized_results['quantile'].astype(str)
        )

        self.print_html("Summary tables are presented in the order of [treatment id, metric name]", "p")
        for treatment_id, treatment_df in normalized_results.groupby('treatment_id'):
            self.print_html(
                'Treatment ID: {}'.format(treatment_id), 'h3'
            )
            for metric_name, normalized_results_subset in treatment_df.groupby("metric"):
                self.print_html(
                    'Metric: {}'.format(metric_name), 'h4'
                )
                # A hyperlink with id, go to HTE plots
                #'<a href="#{treatment_id}-{metric_name}-HTE-plots" id="{treatment_id}-{metric_name}-summary-table">Link to HTE plots</a>'
                self.ab_printer.print_text(
                    text='Link to HTE plots',
                    html_tag='a',
                    attributes={
                        'href': "#{treatment_id}-{metric_name}-HTE-plots".format(
                            treatment_id=treatment_id,
                            metric_name=metric_name
                        ),
                        'id': "{treatment_id}-{metric_name}-summary-table".format(
                            treatment_id=treatment_id,
                            metric_name=metric_name
                        )
                    }
                )
                df = normalized_results_subset.copy()
                # summarize different cases: HTE tested (including HTE detected vs. not) vs. not tested
                all_breakdowns = list(df[dimensions].unique())
                breakdowns_tested = list(df.loc[df[hte_tested], dimensions].unique())
                breakdowns_no_tested = [breakdown for breakdown in all_breakdowns if breakdown not in breakdowns_tested]

                df_tested = df[df[hte_tested]].groupby(dimensions).first()[
                    [hte_outcome, stats]
                ].reset_index(None)
                df_tested[stats] = df_tested[stats].map("{:.4f}".format).values
                df_tested = df_tested.rename(columns={hte_outcome: "HTE"})
                df_tested.loc[:, "HTE"].fillna("No HTE", inplace=True)  # these cases are tested but no HTE detected

                df_no_tested = pd.DataFrame({
                    dimensions: breakdowns_no_tested,
                    'HTE': "N/A",
                    stats: "N/A"
                })
                df_summary = pd.concat([df_tested, df_no_tested]).reset_index(None, drop=True)

                # sort summary table by stats to put most significant results first
                if metric_level:
                    df_summary = df_summary.sort_values(stats, ascending=False)
                df_summary[dimensions_plus_stats] = (
                        df_summary[dimensions] + " (" + hte_stats + "= " + df_summary[stats] + ")"
                )
                # Add description when HTE is None or N/A
                df_summary["HTE"].replace(
                    {
                        'N/A': na_explain
                    },
                    inplace=True,
                )
                if user_level:
                    # Sort by most interesting HTEs
                    htes = list(df_summary["HTE"].unique())
                    hte_levels = sorted(
                        htes, key=lambda x: list(hte_types.keys()).index(x) if x in hte_types.keys() else 100
                    )
                    df_summary["HTE"] = df_summary["HTE"].astype('category')
                    df_summary["HTE"].cat.reorder_categories(hte_levels, inplace=True)
                    df_summary = df_summary.sort_values([stats, 'HTE'])
                if metric_level:
                    self.print_html(
                        'The summary table is ordered by ' +
                        stats +
                        ', from the most interesting fields to the least interesting fields', 'p'
                    )
                else:
                    self.print_html('The summary table is ordered by ' + stats, 'p')
                self.print_html(
                    df_summary
                    #  Show HTE results grouped by HTE Type
                    .groupby(['HTE'])[dimensions_plus_stats].apply(lambda x: "<br>".join(x)).reset_index()
                    # Apply color coding to HTE type
                    .style.apply(highlight_values_with_color(hte_types.keys(), "yellow"), subset=['HTE'])
                    .hide_index().render(),
                    "div"
                )
                if user_level:
                    origin_metric_name = df['origin_metric_name'].values[0]
                    origin_treatment_id = treatment_id[:treatment_id.index(' ')]
                    if (origin_treatment_id, origin_metric_name) in self.hte_tree_results:
                        self.print_html(('HTE tree is generated by dividing the data recursively '
                                         'in a way that maximizes the T-Statistics for treatment effects '
                                         'between the branches on the left and right.'), 'p')
                        self.print_html(('Tree result is using honest tree, and the sample size is half of the AB experiment.'), 'p')
                        tree, OrdinalEnc, TargetEnc = self.hte_tree_results[(origin_treatment_id, origin_metric_name)]
                        plot_tstat_ab_tree(tree,  TargetEnc, OrdinalEnc)
                        hte_tree_table_view(tree, TargetEnc, OrdinalEnc, printer=self.ab_printer)

    def train_hte_tree(self):
        self.hte_tree_results = {}
        if self._calculate_aa:
            user_count = self.sample_sizes[CUMULATIVE].query('period == "AB"')['user_count'].sum()
        else:
            user_count = self.sample_sizes[CUMULATIVE]['user_count'].sum()
        if user_count > 300_000_000:
            self.print_html('More than 300m users in this experiment, skip HTE tree training.', 'p')
            return
        for mt in self.metric_tables:
            if len(mt.get_metrics_by_dist("cont")) == 0 and len(mt.get_metrics_by_dist("bin")) == 0:
                # we don't support quantile and ratio metrics
                continue
            # get data
            df = aggregate_metric_table_by_ghost_user_id(
                metric_table=mt,
                table=self.get_joined_table_name(mt, full=True),
                user_mapping_table=self.get_mapping_sql('standard'),
                group_cols=self.user_group_vars,
                group_filters=None,     # ignore group filters for now
                bq_project=self.bq_project,
                bq_client=self.bq_client
            )
            for tid in self.treatment_ids:
                for metric in mt.get_metric_objects_by_dist("cont") + mt.get_metric_objects_by_dist("bin"):

                    # process categorical features
                    X, y, t, OrdinalEnc, TargetEnc = preprocessing.feature_encoding(df,
                                                     X_columns=self.user_group_vars,
                                                     y_column=metric.col,
                                                     control_id=self.control_id,
                                                     treatment_id=tid)
                   
                    # train eval dataset split
                    X_train, X_eval, y_train, y_eval, t_train, t_eval = train_test_split(X, y, t, test_size=0.5, random_state=42)

                    # train model
                    min_samples_leaf = int(max(10000, len(df)*0.05))
                    tree_model = tree.SquaredTStatisticABTree(max_depth=3, min_samples_leaf=min_samples_leaf).fit(X_train, y_train, t_train)

                    # evaluate model on eval set - honest tree
                    tree_eval= tree_model.evaluate(X_eval, y_eval, t_eval)
                   
                    # save the honest tree model and encoder
                    self.hte_tree_results[(tid, metric.col)] = (tree_eval, OrdinalEnc, TargetEnc)

    def execute(self,
                group_filters=None,
                overwrite=False,
                cumulative_trend=False,
                skip_export=True,
                ):
        """Do all data munging"""
        if not group_filters:
            group_filters = {}
        self.group_filters = group_filters
        self.count_sample_sizes()
        self.run_joins(overwrite=overwrite)
        for kind in (CUMULATIVE, DAILY):
            self.logger.info("Calculating {} results".format(kind))
            self.calculate(
                kind,
                cumulative_trend=cumulative_trend,
            )
        if self.has_user_breakdowns:
            for user_group_bys in self.user_group_by_list:
                for kind in (CUMULATIVE, DAILY):
                    self.logger.info("Calculating {} results for breakdown: {}".format(
                        kind, ", ".join(user_group_bys)
                    ))
                    # Todo: do not re-calculate if already calculated
                    self.calculate(
                        kind,
                        user_group_bys,
                        group_filters={
                            k: v for k, v in six.iteritems(group_filters)
                            if k in user_group_bys or k == 'treatment_id'
                        },
                        cumulative_trend=cumulative_trend,
                    )
        if not skip_export:
            # This is the old upload method uploading different data frames as separate tables
            # using a different naming convention for the tables.
            # Keep it here for now.
            banjo.abtest.utils.export_ab_metrics(self)

        if self.report_key is not None:
            self.upload_normalized_results_to_gcs()

    ##Same as execute report but executing with sampling
    def execute_with_sampling(self,
                             sampling_percent,
                             group_filters=None,
                             overwrite=False,
                             cumulative_trend=False,
                             skip_export=True,
                             ):
        if not group_filters:
            group_filters = {}
        self.group_filters = group_filters
        self.count_sample_sizes()
        self.run_joins_with_sampling(sampling_percent, overwrite=overwrite)
        for kind in (CUMULATIVE, DAILY):
            self.logger.info("Calculating {} results".format(kind))
            self.calculate(
                kind,
                cumulative_trend=cumulative_trend,
            )
        if self.has_user_breakdowns:
            for user_group_bys in self.user_group_by_list:
                for kind in (CUMULATIVE, DAILY):
                    self.logger.info("Calculating {} results for breakdown: {}".format(
                        kind, ", ".join(user_group_bys)
                    ))
                    # Todo: do not re-calculate if already calculated
                    self.calculate(
                        kind,
                        user_group_bys,
                        group_filters={
                            k: v for k, v in six.iteritems(group_filters)
                            if k in user_group_bys or k == 'treatment_id'
                        },
                        cumulative_trend=cumulative_trend,
                    )
        if not skip_export:
            banjo.abtest.utils.export_ab_metrics(self)
    
        if self.report_key is not None:
            self.upload_normalized_results_to_gcs()


    def generate_report_skeleton(self,
                                 facet_bys=None,
                                 group_bys=None,
                                 group_filters=None,
                                 metric_filters=None,
                                 format_pvalue=True,
                                 table_format='table',
                                 stat_fmtr=None,
                                 extra_table_cols=None,
                                 display_config=None,
                                 highlight_sspm=False,
                                 display_period=None
                                 ):
        """Called by generate report to create the standard tables and figures"""
        graphs = list()
        if not display_config:
            display_config = {
                "daily": ["trend"],
                "cumulative": ["table", "forest"],
            }

        cumulative_results = self.get_results(
            CUMULATIVE,
            group_bys,
            metric_filters)
        daily_results = self.get_results(
            DAILY,
            group_bys,
            metric_filters)

        # TODO: what if users want to dictate facet_bys
        if not facet_bys:
            if group_bys is not None:
                facet_bys = list(group_bys)
            else:
                facet_bys = []
        if 'treatment_id' not in facet_bys and len(self.treatment_ids) > 1:
            facet_bys.insert(0, 'treatment_id')

        if self._calculate_aa:
            facet_bys.append('period')

        if self._results_have_quantile_metrics(cumulative_results) and 'quantile' not in facet_bys:
            facet_bys.append('quantile')

        if cumulative_results is not None:
            self.logger.info("cumulative_results is not none")
            if "table" in display_config[CUMULATIVE]:
                self.print_html(
                    'Comparison of user overall (cumulative) metric values during the study period', 'h5'
                )
            graphs.append(self.visualize(cumulative_results,
                                         group_vars=group_bys,
                                         group_filters=group_filters,
                                         facet_bys=facet_bys,
                                         format_pvalue=format_pvalue,
                                         table_format=table_format,
                                         stat_fmtr=stat_fmtr,
                                         extra_table_cols=extra_table_cols,
                                         display_items=display_config[CUMULATIVE],
                                         display_period=display_period,
                                         summary_metrics_type=CUMULATIVE,
                                         last_day_only=True,
                                         highlight_sspm=highlight_sspm))

        daily_only_metrics = self.get_metrics_by_type(DAILY, excluded_types=[CUMULATIVE])
        if daily_only_metrics:
            self.print_html(
                'Comparison of user daily metric values for metrics that cannot be aggregated across multiple days',
                'h5'
            )
            self.visualize(daily_results.loc[daily_results.metric.isin(daily_only_metrics), :].copy(),
                           group_vars=group_bys,
                           group_filters=group_filters,
                           facet_bys=facet_bys,
                           format_pvalue=format_pvalue,
                           table_format=table_format,
                           stat_fmtr=stat_fmtr,
                           extra_table_cols=extra_table_cols,
                           show_table=True,
                           summary_metrics_type=DAILY,
                           last_day_only=False,
                           highlight_sspm=highlight_sspm)

        if daily_results is not None:
            self.logger.info("daily_result is not none")
            if "table" in display_config[DAILY]:
                self.print_html(
                    'Comparison of user daily metric values during the study period', 'h5'
                )
            graphs.append(self.visualize(daily_results,
                                         group_vars=group_bys,
                                         group_filters=group_filters,
                                         facet_bys=facet_bys,
                                         format_pvalue=format_pvalue,
                                         table_format=table_format,
                                         stat_fmtr=stat_fmtr,
                                         extra_table_cols=extra_table_cols,
                                         display_items=display_config[DAILY],
                                         summary_metrics_type=DAILY,
                                         highlight_sspm=highlight_sspm
                                         )
                          )
        return graphs

    def generate_report(self,
                        facet_bys=None,
                        group_filters=None,
                        metric_filters=None,
                        format_pvalue=True,
                        table_format='table',
                        stat_fmtr=None,
                        extra_table_cols=None,
                        display_config=None,
                        show_all=True,
                        show_breakdowns=True,
                        highlight_sspm=True,
                        display_period=None,
                        ):
        """Generate A/B study analysis report"""
        #  TODO: render with a Jinja template
        if self._report_with_cuped:
            self.print_html("""
            You report contains results using
            [the CUPED variance reduction method](https://exp-platform.com/cuped/) (labeled CUPED in the tables).
            This method requires that there is no carryover effect from the previous study versions.
            If this study version reuses the same randomization seed as the previous versions,
            the CUPED results are invalid.""", 'h5')
        if group_filters is None:
            group_filters = {}
        valid_values = {"AA", "AB", "CUPED"}
        if display_period:
            display_period = [period.upper() for period in display_period]
            for value in display_period:
                if value not in valid_values:
                    raise ValueError(f"Invalid entry: '{value}' is not one of {valid_values}")
                if value == "CUPED" and not self._report_with_cuped:
                    raise ValueError("CUPED results cannot be reported with CUPED not enabled")
        graphs = list()
        if show_all:
            self.print_html('Overall', 'h5')
            graphs.append(self.generate_report_skeleton(facet_bys=facet_bys,
                                                        group_bys=None,
                                                        group_filters=None,
                                                        metric_filters=metric_filters,
                                                        format_pvalue=format_pvalue,
                                                        table_format=table_format,
                                                        stat_fmtr=stat_fmtr,
                                                        extra_table_cols=extra_table_cols,
                                                        display_config=display_config,
                                                        highlight_sspm=highlight_sspm,
                                                        display_period=display_period,
                                                        )
                          )

        if self.has_user_breakdowns and show_breakdowns:
            for user_group_bys in self.user_group_by_list:
                self.print_html(
                    'Breakdown by user groups: {}'.format(", ".join(user_group_bys)),
                    'h5'
                )
                breakdown_extra_cols = ['count_control', 'count_treatment']
                if extra_table_cols:
                    for col in reversed(extra_table_cols):
                        if col not in breakdown_extra_cols:
                            breakdown_extra_cols.insert(0, col)
                graphs.append(
                    self.generate_report_skeleton(
                        facet_bys=facet_bys,
                        group_bys=user_group_bys,
                        group_filters={
                            k: v for k, v in six.iteritems(group_filters)
                            if k in user_group_bys or k in ['treatment_id', 'metric']
                        },
                        metric_filters=metric_filters,
                        format_pvalue=format_pvalue,
                        table_format=table_format,
                        stat_fmtr=stat_fmtr,
                        extra_table_cols=breakdown_extra_cols,
                        display_config=display_config,
                        highlight_sspm=highlight_sspm,
                        display_period=display_period,
                    )
                )
        return graphs

    def generate_hte_report(self,
                            user_level=True,
                            metric_level=False,
                            metric_filters=None,
                            show_tldr=True,
                            stat_fmtr=None,
                            hide_insignificant_hte_plots=True
                            ):
        """Generate A/B study HTE report at user_level or metric_level """
        if user_level and metric_level:
            raise ValueError("We don't support summarizing user_level and metric_level together. "
                             "Please call this function twice to output both.")
        if self.has_user_breakdowns and user_level:
            if show_tldr:
                self.print_html("", "hr")
                self.print_html("User Level Breakdown TL;DR", 'h2')
                self.visualize_hte_summary()
                self.print_html("", "hr")

            if self.user_group_by_list:
                self.print_html("Detailed Breakdowns by User Groups", 'h2')
                if hide_insignificant_hte_plots:
                    self.print_html("HTE plots are presented in the order of [treatment id, metric name, HTE p-value], only significant breakdown dimensions are shown", "p")
                else:
                    self.print_html("HTE plots are presented in the order of [treatment id, metric name, HTE p-value], all breakdown dimensions are shown", "p")

                df_list = []
                for user_group_bys in self.user_group_by_list:
                    df = self.get_results(CUMULATIVE, user_group_bys, metric_filters=metric_filters)
                    if df is not None:
                        df_list.append(df)
                if df_list:
                    cumulative_results = pd.concat(df_list)
                else:
                    cumulative_results = None

                if cumulative_results is not None:
                    self.visualize_hte_breakdowns(
                        cumulative_results,
                        stat_fmtr=stat_fmtr,
                        hide_insignificant_hte_plots=hide_insignificant_hte_plots
                    )
        elif metric_level:
            cumulative_results = self.get_results(
                CUMULATIVE, metric_filters=metric_filters, exclude_field_breakdown=False
            )
            if cumulative_results is None or "metric_breakdown_dimensions" not in cumulative_results.columns:
                return
            cumulative_results["metric_breakdown_dimensions"] = (
                cumulative_results["metric_breakdown_dimensions"].replace(
                    "_$", "", regex=True
                )
            )
            if show_tldr:
                self.print_html("", "hr")
                self.print_html("Metric Level Breakdown TL;DR", 'h2')
                self.visualize_hte_summary(user_level=False, metric_level=True)
                self.print_html("", "hr")

            # exclude overall results
            cumulative_results = cumulative_results[
                cumulative_results["metric_breakdown_dimensions"] != "Overall"].copy()

            self.print_html("Detailed Breakdowns by Metric Fields (Sorted by the Degree of HTE)", 'h2')
            self.visualize_hte_breakdowns(
                cumulative_results,
                user_level=False,
                metric_level=True,
                stat_fmtr=stat_fmtr,
            )
        return

    def get_results(self,
                    kind,
                    group_vars=None,
                    metric_filters=None,
                    exclude_field_breakdown=True,
                    metric_breakdown_dimensions: List[str] = None,
                    ):
        """Return a copy of the results for safe manipulation"""
        key = self.get_results_key(kind, group_vars)
        if key not in self.results:
            return None

        results = self.results[key].copy()
        if metric_filters:
            filters = metric_filters.copy()

            use_regex = filters.pop('regex', False)  # set to false is omitted
            reorder = filters.pop('reorder', True)
            for filter_name, filter_values in filters.items():
                if filter_name == 'metrics':
                    filter_name = 'metric'
                warnings.warn("Use 'metric' instead of 'metrics' to filter the metric names", DeprecationWarning)

                if filter_name not in results:
                    raise ValueError("{} is not a valid column ({})".format(filter_name, results.columns.tolist()))
                if use_regex:
                    results = results[results[filter_name].str.contains('|'.join(filter_values), regex=True)].copy()
                else:
                    results = results[results[filter_name].isin(filter_values)].copy()
                    if reorder:
                        available_values = results[filter_name].unique().tolist()
                        categorical_dtype = pd.api.types.CategoricalDtype(
                            [value for value in filter_values if value in available_values],
                            ordered=True
                        )
                        results[filter_name] = results.metric.astype(categorical_dtype)
        self._apply_exp_name(results)
        if exclude_field_breakdown and "metric_breakdown_dimensions" in results.columns:
            results = results.loc[results["metric_breakdown_dimensions"].isna(), :].copy()
        if metric_breakdown_dimensions:
            results = results.query("metric_breakdown_dimensions == '{}'"
                                    .format("|".join(sorted(metric_breakdown_dimensions))))
        return results

    def _apply_exp_name(self, df):
        if self.exp_id_to_name:
            exp_id_to_name_mapping = {exp_id: "{0} ({1})".format(exp_id, exp_name)
                                      for exp_id, exp_name in self.exp_id_to_name.items()}
            df['treatment_id'] = df.treatment_id.replace(exp_id_to_name_mapping)
            df['control_id'] = df.control_id.replace(exp_id_to_name_mapping)

    def get_joined_table_name(
            self, metric_table, full=False, extra_bits=''):
        """Generate the name the bigquery table"""
        allowed_string_length = 100
        metrics = metric_table.name
        if len(metrics) > allowed_string_length:
            metrics = (
                    'metrics_'
                    + hash_string(metrics)
            )

        experiment_ids = '_'.join(sorted([self.control_id] + self.treatment_ids))
        if len(experiment_ids) > allowed_string_length:
            experiment_ids = 'experiment_id_' + hash_string(experiment_ids)
        table_name = '_'.join(
            [self.study_name,
             extra_bits if extra_bits else '_'.join([s for s in sorted(self.user_group_vars)]),
             metrics,
             metric_table.hash_string,
             experiment_ids,
             CUMULATIVE,
             self.analysis_start_date,
             self.study_end_date]
        )
        if full:
            table_name = '.'.join([
                self.bq_project, self.dest_dataset, table_name])
        return table_name

    def get_normalized_results(self):
        """Concatenate all results DataFrame for overall and for breakdowns

        Returns
        -------
        results: None or pd.DataFrame
        """
        df_list = []
        for results_key in self.results:
            kind, groups = self.unpack_results_key(results_key)
            df = self.get_results(kind, groups, exclude_field_breakdown=False)
            if kind == CUMULATIVE:
                df = self._get_last_day(df)
            self._apply_exp_name(df)
            if groups:
                df.drop(columns=groups)

            df_list.append(df)

        if df_list:
            results = pd.concat(df_list, sort=False)
            results['study_name'] = self.study_name
            results['study_start_date'] = self.study_start_date
            results['analysis_start_date'] = self.analysis_start_date
            results['study_end_date'] = self.study_end_date
            results['report_key'] = self.report_key
            results['exp_ds'] = results.exp_ds.astype(str)  # Todo: make sure this is string in upstream sql
            return results

    def upload_normalized_results_to_gcs(self):
        husky_notebook_results_bucket_name = "husky-notebook-results"
        blob_name = "results/banjo_abtest_results_{}.parquet".format(self.report_key)
        results = self.get_normalized_results()
        if results is None or results.empty:
            self.logger.error("Results data frame is empty for report {}; GCS uploading skipped".format(
                self.report_key))
        else:
            try:
                gcs.upload_dataframe_to_gcs(
                    results,
                    bucket_name=husky_notebook_results_bucket_name,
                    blob_name=blob_name,
                    project_id=self.bq_project,
                    index=False,
                )
            except Exception as e:
                self.logger.info("Uploading results for %s failed with error message: " % self.report_key)
                self.logger.info(str(e))

    def upload_excel_output_to_gcs(self):
        husky_notebook_results_bucket_name = "husky-notebook-results"
        if not self.report_key:
            file_name_suffix = hash(self)
        else:
            file_name_suffix = self.report_key

        blob_name = "excels/banjo_abtest_results_{}.xlsx".format(file_name_suffix)
        try:
            gcs.upload_from_filename(
                self.excel_output_path,
                bucket_name=husky_notebook_results_bucket_name,
                blob_name=blob_name,
                project_id=self.bq_project,
                content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            )
        except Exception as e:
            self.logger.info("Uploading excel for %s failed with error message: " % file_name_suffix)
            self.logger.info(str(e))

        return "https://storage.cloud.google.com/{}/{}".format(husky_notebook_results_bucket_name, blob_name)

    @staticmethod
    def get_results_key(kind, group_vars):
        if not group_vars:
            group_key = ''
        else:
            group_vars = sorted(group_vars)
            group_key = ','.join(group_vars)
            group_key = '|' + group_key
        return kind + group_key

    @staticmethod
    def unpack_results_key(key):
        if '|' not in key:
            return key, []

        kind, group_key = key.split('|')
        groups = group_key.split(',')
        return kind, groups

    def _get_last_day(self, frame, ds='exp_ds', replace_ds_with_range=True):
        if frame is None or frame.empty:
            self.logger.warning("_get_last_day received no data")
            return None
        last_day = frame.loc[frame[ds] == frame[ds].max(), :].copy()
        if replace_ds_with_range:
            ts_gap = infer_dt_tick_gap(frame[ds])  # plots.infer_dt_tick_gap
            ts_fmt = '%m/%d' if ts_gap >= datetime.timedelta(days=1) else '%m/%d %H:%M'
            try:
                last_day[ds] = ' '.join([
                    frame[ds].min().strftime(ts_fmt),
                    'to',
                    frame[ds].max().strftime(ts_fmt)
                ])
            except AttributeError:  # ds is not datetime like
                last_day[ds] = ' '.join([
                    pd.to_datetime(self.analysis_start_date).strftime('%m/%d'),
                    'to',
                    pd.to_datetime(self.study_end_date).strftime('%m/%d'),
                ])
        else:
            last_day[ds] = CUMULATIVE
        return last_day

    @staticmethod
    def gather_experiments(frame, extra_stat_cols=None):
        """Given a frame in the final A/B results where each row
        is a comparison between control and treatment,
        this method stacks treatment experiments and control experiments
        and form a long-format table
        """
        stat_cols = ['count', 'sum', 'sum_square', 'avg', 'bin']
        if extra_stat_cols:
            stat_cols.extend(extra_stat_cols)

        treatments = (
            frame.loc[:,
            ['exp_ds', 'metric', 'treatment_id'] +
            [c + '_treatment' for c in stat_cols]]
            .rename(columns={c + '_treatment': c for c in stat_cols})
            .rename(columns={'treatment_id': 'exp_id'})
        )
        control = (
            frame.loc[frame.treatment_id == frame.treatment_id.min(),
                      ['exp_ds', 'metric', 'control_id'] +
                      [c + '_control' for c in stat_cols]]
            .rename(columns={c + '_control': c for c in stat_cols})
            .rename(columns={'control_id': 'exp_id'})
        )

        experiments = pd.concat([treatments, control], axis=0)
        return experiments

    def generate_aa_report(self,
                           results=None,
                           exp_props=None,
                           digits=2,
                           format_pvalue=True,
                           ):
        """Generate A/A testing results table

        Parameters
        ----------
        results
        exp_props
        digits
        format_pvalue

        Returns
        -------

        """
        if results is None:
            results = self.get_results(CUMULATIVE)
        last_day = self._get_last_day(results)

        def num_fmt(n):
            return ('{:.' + str(n) + 'f}').format

        experiments = self.gather_experiments(last_day)
        metrics = experiments.metric.unique()
        aa_results = []

        for i, metric in enumerate(metrics):
            metric_values = experiments.loc[experiments.metric == metric, :]
            ind = list(metric_values.exp_id) + ['P-value']

            if i == 0:
                # Chisq for counts; just need to do this once as the counts
                # should be the same for all metrics
                counts = metric_values['count']

                # make sure the order is correct
                if exp_props:
                    if not isinstance(exp_props, dict):
                        raise ValueError('Experiment proportions must be'
                                         ' specified as a dict with exp_id '
                                         'as keys.')
                    exp_prop_list = [exp_props[v]
                                     for v in ind if v in exp_props]
                else:
                    exp_prop_list = exp_props
                p, _ = chisq_test_sample_size(
                    counts,
                    expected_props=exp_prop_list
                )

                aa_results.append(
                    pd.DataFrame(
                        {'count': list(counts.map(num_fmt(0))) + [p]},
                        index=ind
                    )
                )

            if not all(metric_values['sum'].isnull()):
                p, _ = anova_sample_mean(
                    metric_values['count'],
                    metric_values['sum'],
                    metric_values['sum_square'],
                )

                aa_results.append(
                    pd.DataFrame(
                        {metric:
                             list(metric_values.avg.map(num_fmt(digits))) + [p]},
                        index=ind
                    )
                )

        aa_table = (pd.concat(aa_results, axis=1, join='inner')
                    .rename(columns=self.metric_name_mapping)
                    .rename(columns={'count': 'Unique Users'}))

        # Write interpretation
        sentences = []
        failed = '<span class="text-danger">failed</span>'
        passed = '<span class="text-success">passed</span>'
        for i, metric_name in enumerate(aa_table.columns):
            if aa_table.loc['P-value', :][i] < ALPHA_LEVEL:
                sentence = '<em>{}</em> {} A/A testing.'.format(
                    metric_name, failed)
            else:
                sentence = '<em>{}</em> {} A/A testing.'.format(
                    metric_name, passed)
            sentences.append(sentence)

        self.print_html('A/A test results:', 'h3')
        self.print_html(
            '<li>' + '</li><li>'.join(sentences) + '</li>',
            'ul'
        )

        if format_pvalue:
            aa_table.loc['P-value', :] = get_formatted_pvalue(
                aa_table.loc['P-value', :]
            )

        aa_table = (
            aa_table.sort_index()
            .reset_index()
            .rename(columns={'index': 'Experiment ID'})
            .assign(Date=experiments['exp_ds'].iloc[0])
        )

        return aa_table

    def generate_power_table(self,
                             exp_id,
                             traffic,
                             effect_size,
                             alpha=0.05,
                             beta=0.8,
                             overwrite_table=False,
                             ):
        """Returns a data frame with sample sizes needed and corresponding traffic percentages
        for given effect sizes.
        """
        self.run_joins(CUMULATIVE, overwrite=overwrite_table)

        cumulative_wide = self.aggregate(CUMULATIVE, extra_cont_stats=['STDDEV({col})/AVG({col}) AS {col}_cv'])
        power_table = reshape_aggregated(cumulative_wide[cumulative_wide.exp_id == exp_id])
        power_table = power_table[power_table.value.notnull()]

        user_count = {}
        for i, row in enumerate(power_table.values):
            exp_ds, exp_id, value, variable, stat = row
            if variable == "user":
                user_count[exp_ds] = value

        def get_sample_size(cv, effect, alpha_, beta_):
            """ Computes the sample size needed with given CV and effect size.
            """
            q_sig = norm.ppf(1 - alpha_ / 2)
            q_power = norm.ppf(beta_)
            n = 2 * (q_sig + q_power) * (q_sig + q_power) * cv * cv / (effect * effect)
            return n

        lookup_table = []
        for e in effect_size:
            for i, row in enumerate(power_table.values):
                exp_ds, exp_id, value, variable, stat = row
                if stat == 'cv':
                    sample_size_needed = get_sample_size(value, e, alpha, beta)
                    days = (pd.to_datetime(exp_ds) - pd.to_datetime(self.analysis_start_date)).days + 1
                    lookup_table.append(
                        dict(
                            days=days,
                            metric=variable,
                            effect_size='{:.1%}'.format(e),
                            sample_size_needed=locale.format("%d", sample_size_needed, grouping=True),
                            traffic_needed='{:.1%}'.format(
                                sample_size_needed * traffic / (user_count.get(exp_ds) * 100)
                            )
                        )
                    )
        return pd.DataFrame(lookup_table)

    def write_excel(self, table, sheet_name, freeze_panes=None):
        def _html_to_excel(value):
            try:
                return value.replace("<br>", "\n")
            except:
                return value

        if self.excel_output_path:
            with pd.ExcelWriter(
                    self.excel_output_path,
                    engine="openpyxl",
                    mode="w" if self._excel_sheet_seq_no == 0 else "a"
            ) as writer:
                sheet_name = sheet_name[:31]
                table.data = table.data.applymap(_html_to_excel)
                table.to_excel(writer, sheet_name=sheet_name, freeze_panes=freeze_panes)
                self._excel_sheet_seq_no += 1
                # set column widths
                ws = writer.book[sheet_name]
                dims = {}
                for row in ws.rows:
                    for cell in row:
                        if cell.value:
                            dims[cell.column_letter] = max((dims.get(cell.column_letter, 0), len(str(cell.value))))
                for col, width in dims.items():
                    ws.column_dimensions[col].width = min(width, 35)

    @property
    def metric_name_mapping(self):
        """Return a dict that maps metric sql names to human names"""
        mapping = {}
        for metric_table in self.metric_tables:
            mapping.update(metric_table.name_mapping)
        # Deduplicate values. Add suffix (2), (3) to duplicated ones
        name_counts = {}
        for field, name in mapping.items():
            if name in name_counts:
                name_counts[name] += 1
                mapping[field] = "{} ({})".format(mapping[field], name_counts[name])
            else:
                name_counts[name] = 1

        return mapping

    @property
    def metric_direction_mapping(self):
        """Return a dict that maps metric sql names to their desired directions"""
        mapping = {}
        for metric_table in self.metric_tables:
            for metric in metric_table.metrics:
                mapping[metric.col] = metric.desired_direction
        if self.additional_metric_direction_mapping:
            mapping.update(self.additional_metric_direction_mapping)
        return mapping

    @property
    def metric_stat_fmtr_mapping(self):
        """Return a dict that maps metric sql names to their desired stat formatters"""
        mapping = {}
        for metric_table in self.metric_tables:
            for metric in metric_table.metrics:
                if metric.stat_fmtr:
                    mapping[metric.col] = metric.stat_fmtr
        return mapping

    @staticmethod
    def _results_have_quantile_metrics(data_frame):
        return (
                "quantile" in data_frame
                and not all(data_frame.loc[:, "quantile"].isnull())
                and not all(data_frame.loc[:, "quantile"] == "NA")
        )

    @staticmethod
    def infer_breakdown_order(levels):
        """Return sensible order of the levels by extracting numbers and order them
        For each level, define a tuple by extracting all numbers from the level and format each number as a string
        that maintains the numeric order when sorted as string, e.g. '00000016.000'. If no string is found, use
        a tuple containing the level itself.

        Examples
        --------
        >>> levels = Report.infer_breakdown_order(['4-8M', '1-2M', '>16M', '0-1M', '2-4M', None, '8-16M'])
        >>> print(levels)
        ['0-1M', '1-2M', '2-4M', '4-8M', '8-16M', '>16M', None]
        """

        def _extract_num_or_self(level):
            if not isinstance(level, str):
                return ("zz" + str(level),)
            numbers = re.findall(r"[0-9]+\.?[0-9]*", level)
            if not numbers:
                return (level,)
            else:
                return tuple("{:012.3f}".format(float(number)) for number in numbers)

        return sorted(levels, key=_extract_num_or_self)

    def print_html(self, text, html_tag):
        attributes = {}
        if html_tag.lower() in ["h1", "h2", "h3", "h4"]:
            id_value = "{}--{}".format(re.sub(r'[^0-9a-zA-Z]+', '-', text), datetime.datetime.now().strftime("%f"))
            attributes["id"] = id_value
            self.anchor_list.append((html_tag, id_value, text))

        self.ab_printer.print_text(text, html_tag, attributes)


class CustomReport(Report):
    """
    Run the analysis with a custom mapping table definition
    """

    def __init__(self,
                 custom_mapping_sql=None,
                 materializing_mapping_table=True,
                 overwrite_mapping_table=False,
                 mapping_table_hash=None,
                 bq_dialect='legacy',
                 old_mapping_format=True,
                 **kwargs):
        """ Report with your own SQL

        Parameters
        ----------
        custom_mapping_sql : str
            The sql that defines the mapping from the mapping_user_col to exp_ids and some information
            about the analysis range
        materializing_mapping_table : bool
            Whether to generate a mapping table with custom_mapping_sql. Strongly recommended as it makes
            subsequent joins faster
        overwrite_mapping_table : bool
            (Deprecated) whether the overwrite the existing mapping table with the same name.
            This option should rarely be needed because we now embed the hash of the sql in the table name already.
            The same name suggests that the SQL is exactly the same as the previous run and there's no need to
            regenerate it.
        mapping_table_hash : str
            Set this if you want supply you own mapping table identifier
        bq_dialect : str
            "standard" or "legacy"
        old_mapping_format : bool
            The old format (pre Jan 2021):
            schema: ghost_user_id, exp_id, ts, exposure_ts
            The table should have one row per user for every day the user is exposed to the study.
            exposure_ts is optional and only used when joining with hourly granularity table.

            The join conditions are
            (1) mapping.ghost_user_id = metrics.ghost_user_id AND mapping.ts = metrics.ts
            (2) For hourly metrics only
            mapping.ghost_user_id = metrics.ghost_user_id AND mapping.ts = metrics.ts
            AND (metrics.metric_ts IS NULL OR mapping.exposure_ts <= metrics.metric_ts)

            The new format (post Jan 2021):
            schema: ghost_user_id, exp_id, exposure_ts, exposure_ds
            The table should have one row per user.

            The join conditions are
            (1) mapping.ghost_user_id = metrics.ghost_user_id AND mapping.exposure_ds <= metrics.ts
            (2) For hourly metrics only
            mapping.ghost_user_id = metrics.ghost_user_id AND mapping.exposure_ds <= metrics.ts
            AND (metrics.metric_ts IS NULL OR mapping.exposure_ts <= metrics.metric_ts)

        kwargs : dict
        """
        super(CustomReport, self).__init__(**kwargs)

        if bq_dialect not in ['standard', 'legacy']:
            raise ValueError('bq_dialect must be standard or legacy')

        self.bq_dialect = bq_dialect
        self.materialized_table_name = None

        self._custom_mapping_sql = custom_mapping_sql
        self._old_mapping_format = old_mapping_format
        self.wrap_old_mapping_sql()
        if mapping_table_hash is not None:
            self._mapping_table_hash = mapping_table_hash
        else:
            self._mapping_table_hash = hash_string(self._custom_mapping_sql)
        if materializing_mapping_table:
            self.materialize_mapping_table(overwrite=overwrite_mapping_table)

    def wrap_old_mapping_sql(self):
        if self._old_mapping_format:
            if "exposure_ts" in self._custom_mapping_sql:
                exposure_ts_clause = "MIN(exposure_ts) AS exposure_ts"
            else:
                exposure_ts_clause = ""

            self._custom_mapping_sql = """
            SELECT {mapping_user_col}, exp_id, {group_cols}
              MIN(ts) AS exposure_ds,
              {exposure_ts_clause}
            FROM ({input_custom_mapping_sql})
            GROUP BY {mapping_user_col}, exp_id
            """.format(
                mapping_user_col=self.mapping_user_col,
                group_cols="".join([
                    "{1}({0}) AS {0}, ".format(var, "ANY_VALUE" if self.bq_dialect == "standard" else "FIRST")
                    for var in self.user_group_vars
                ]),
                exposure_ts_clause=exposure_ts_clause,
                input_custom_mapping_sql=self._custom_mapping_sql,
            )
            self._old_mapping_format = False
        else:
            self._custom_mapping_sql = self._custom_mapping_sql

    @property
    def custom_mapping_sql(self):
        """Returns the legacy compatible custom_mapping_sql for backwards compatibility"""
        return self.get_mapping_sql(dialect="legacy")

    @custom_mapping_sql.setter
    def custom_mapping_sql(self, sql):
        self.bq_dialect = "legacy"
        self._custom_mapping_sql = sql
        self.materialized_table_name = None
        # todo(xhe): remove two lines once no one uses the old mapping format ETA: 2022
        self._old_mapping_format = True
        self.wrap_old_mapping_sql()
        self._mapping_table_hash = hash_string(sql)

    @property
    def custom_mapping_stdsql(self):
        return self.get_mapping_sql(dialect="standard")

    @custom_mapping_stdsql.setter
    def custom_mapping_stdsql(self, sql):
        self.bq_dialect = "standard"
        self._custom_mapping_sql = sql
        self.materialized_table_name = None
        # todo(xhe): remove two lines once no one uses the old mapping format ETA: 2022
        self._old_mapping_format = True
        self.wrap_old_mapping_sql()
        self._mapping_table_hash = hash_string(sql)

    def get_mapping_sql(self, dialect):
        """Method that returns the mapping logic for the given BQ dialect, to be joined with MetricTable.sql"""
        if dialect != self.bq_dialect and self.materialized_table_name is None:
            raise TypeError("Mapping SQL for dialect {} not available. Please "
                            "call materialize_mapping_table first to create the mapping table.".format(dialect))
        if self.materialized_table_name is None:
            return '({})'.format(self._custom_mapping_sql)
        elif dialect == "legacy":
            return '[{}:{}.{}]'.format(
                self.bq_project,
                self.dest_dataset,
                self.materialized_table_name
            )
        else:
            return '`{}.{}.{}`'.format(
                self.bq_project,
                self.dest_dataset,
                self.materialized_table_name
            )

    def get_joined_table_name(
            self, metric_table, full=False, extra_bits=''):
        extra_bits = hash_string(self._mapping_table_hash + '_'.join([s for s in sorted(self.user_group_vars)]))
        return super(CustomReport, self).get_joined_table_name(
            metric_table, full, extra_bits
        )

    def materialize_mapping_table(self, overwrite=False):
        """Optionally materialize the table since the mapping table
        is usually used multiple times to join with different metrics
        """
        mapping_table_name = (
            "{study_name}_mapping_{hash_code}__{start_date}__{end_date}"
        ).format(
            study_name=self.study_name,
            start_date=self.analysis_start_date,
            end_date=self.study_end_date,
            hash_code=self._mapping_table_hash,
        )
        if (not overwrite
                and table_exists(
                    client=self.bq_client,
                    table_reference=bigquery.TableReference.from_string(
                        f"{self.bq_project}.{self.dest_dataset}.{mapping_table_name}"
                    )
                )
        ):
            self.logger.info('Table {} already exists, skipping. '
                             'use overwrite=True if want to rerun the query '
                             'to overwrite it'.format(mapping_table_name))
        else:
            try:
                self.logger.info('"Materializing study mapping table.')
                submit_sync_query(
                    query=self.get_mapping_sql(dialect=self.bq_dialect),
                    client=self.bq_client,
                    dest_project_id=self.bq_project,
                    dest_dataset_id=self.dest_dataset,
                    dest_table_name=mapping_table_name,
                    write_disposition="WRITE_TRUNCATE",
                    maximum_billing_tier=1,
                    priority=self.bq_priority,
                    bq_retry=self.bq_retry,
                    dialect=self.bq_dialect,
                )
            except BigQueryRuntimeError:
                self.logger.error('Encountered BigQuery error while submitting the '
                                  'job creating the mapping table')
                raise

        self.materialized_table_name = mapping_table_name

    def generate_winners_losers_report(
            self,
            winners_losers_dimensions: List[str],
            order_by_metrics: Union[List[str], None] = None,
            display_metrics: Union[List[str], None] = None,
            user_group_bys: Union[List[str], None] = None,
            p_value_threshold: float = 0.1,
            n_winners: int = 20,
            n_losers: int = 20,
            enrichment_sql=None,
            winners_losers_results: pd.DataFrame = None,
            top_n_candidates: int = None,
            enrichment_group_bys: Union[List[str], None] = None,
    ) -> None:
        """ Generate the winners losers reports
        In an A/B study we refer to as winners/losers pieces of content or other entities that gain or lose the most.
        For example: the publisher stories that gain the most viewers in the treatments. Or the lens that lose
        the most swipes.

        To use this method to generate the Winners/Losers report, you will need to add a FieldBreakdownMetricTable
        instance with the appropriate metric dimensions (e.g. story_id, lens_id) while initializing the Report
        instance as part of the metric_tables list.

        The winners and losers are sorted by absolute changes in metric values.

        For an example: please see [this example notebook]
        (https://github.sc-corp.net/Snapchat/pyanalytics/blob/master/demo/abtest/abtest_winners_losers.ipynb)

        Possible features
        * Multiple testing correction: this is tricky as results would change as the user changes how many top
          entities to keep
        * Display hyperlinks instead of entity_id if we have internal tools visualizing the entities ranked, e.g.
          Story Studio, some web app that has details for lenses.

        Parameters
        ----------
        winners_losers_dimensions
            Along which dimensions we should rank and report the top winners and losers
            This should match the breakdown list you specify in the FieldBreakdownMetricTable instance.
        order_by_metrics
            For each metric specified in this list, we will report one set of winners losers results
            sorted by the metric.
            If you do not supply the list, we will default to using all available metrics. It's recommended that
            you pick one or a limited number of metrics here.
        display_metrics
        user_group_bys
            Whether to report the winners/losers in certain *user* breakdowns. Available options are those that you
            supply to Report(..., user_group_bys, ...)
        p_value_threshold
            We keep only results with p-values under this threshold
        n_winners
            The number of winners to display
        n_losers
            The number of losers to display
        enrichment_sql
            The query (string) providing the supplemental information to be added into the report visualization.
            The result of this sql should include all the winners_losers_dimensions and any supplemental information that
            will display in the visualization (all columns will be displayed). If there are duplicated records for the
            same winners_losers_dimention in this sql, only one record will be randomly selected and displayed.
        winners_losers_results
            Dataframe from where winner/loser units are identified. By default, this uses the get_results function
            to pull from the report, with the metric_break_down value when the report is created.
        top_n_candidates
            Filter candidates by the top n order_by_metrics (use case: Getting winners / losers report of top creators)
        enrichment_group_bys
            Group Bys based on columns from enrichment_sql
        Returns
        -------

        """
        if not user_group_bys:
            user_group_bys = []
        winners_losers_dimension_name = ", ".join(winners_losers_dimensions)

        if winners_losers_results is None:
            winners_losers_results = self.get_results(
                "cumulative",
                exclude_field_breakdown = False,
                group_vars = user_group_bys,
                metric_breakdown_dimensions = winners_losers_dimensions
            )

        all_metrics = winners_losers_results.metric.unique().tolist()
        if not order_by_metrics:
            order_by_metrics = all_metrics
        if not display_metrics:
            display_metrics = all_metrics
        if not set(order_by_metrics).issubset(set(all_metrics)):
            raise ValueError(f"`order_by_metrics` must be subset of the available metrics: {all_metrics}")
        if not set(display_metrics).issubset(set(all_metrics)):
            raise ValueError(f"`display_metrics` must be subset of the available metrics: {all_metrics}")
        if enrichment_group_bys:
            if not enrichment_sql:
                raise ValueError("The enrichment_sql field must be specified when specify a value for enrichment_group_bys")
        if enrichment_sql:
            df_enrichment = gbq.read_gbq(
                enrichment_sql,
                client=self.bq_client,
                dialect="standard",
                priority=self.bq_priority,
            )
        for group_ids, results_subset in winners_losers_results.groupby(["treatment_id"] + ([] if enrichment_group_bys else user_group_bys)):
            if isinstance(group_ids, str) and not enrichment_group_bys:
                group_ids = [group_ids]
            treatment_id = group_ids[0]
            user_breakdown_values = group_ids[1:]
            for ascending, wl_label, top_n in [(False, "Winners", n_winners), (True, "Losers", n_losers)]:
                # compute overall positive negative necessary?
                for metric in order_by_metrics:
                    candidates = (
                    results_subset
                    .query("p_value < {:.8f} or metric != '{}'".format(
                        p_value_threshold, metric))
                    .copy()
                    )
                    winners_or_losers = (
                        candidates
                        .loc[
                        candidates.metric.isin([metric])
                        # keep negative or positive movers only
                        & (candidates["diff"] * (-1 if ascending else 1) > 0),
                        :
                        ]
                        .sort_values(['diff'], ascending=ascending)
                        .head(top_n)
                        .loc[:, winners_losers_dimensions]
                    )
                    if len(winners_or_losers) == 0:
                        continue
                    winners_or_losers["rank"] = list(range(1, len(winners_or_losers) + 1))
                    table_data = (
                        candidates
                        .loc[
                        candidates.metric.isin(
                            set(display_metrics + [metric])
                        ),
                        :
                        ]
                        .merge(winners_or_losers, how="inner", on=winners_losers_dimensions)
                        .copy()
                    )
                    if len(table_data) > 0:
                        # When user_group_bys used to generate the winner-losers report has higher granularity than the
                        # user_group_bys that defines the report, there might be empty table_data in this step. So
                        # add a step to check for empty tables and save calculation.
                        if enrichment_sql:
                            enrichment_data = df_enrichment[
                                reduce(lambda x, y: x & y,
                                       [df_enrichment['{}'.format(d)].isin(table_data['{}'.format(d)]) for d in
                                        winners_losers_dimensions]
                                       )
                            ]
                            table_data = table_data.merge(enrichment_data,
                                                          how="left",
                                                          on=winners_losers_dimensions)
                            if enrichment_group_bys:
                                user_group_bys = enrichment_group_bys
                        metric_display_name = self.metric_name_mapping[metric]
                        table_data['metric'] = table_data['metric'].replace(
                            {metric: f" Ranked by: {metric_display_name}"})
                        if enrichment_group_bys:
                            for enrichment_group_ids, enrichment_results_subset in table_data.groupby(["treatment_id"] + user_group_bys):
                                enrichment_user_breakdown_values = enrichment_group_ids[1:]
                                enrichment_treatment_id = enrichment_group_ids[0]
                                subpopulation_label = ", ".join(
                                    "{}={}".format(dim, val) for dim, val in zip(user_group_bys, enrichment_user_breakdown_values)
                                )
                                if not subpopulation_label:
                                    subpopulation_label = "overall"
                                title = (
                                    f"{wl_label} of {', '.join(winners_losers_dimensions)} in {subpopulation_label} by "
                                    f"{metric_display_name} for: {enrichment_treatment_id}"
                                )
                                self.ab_printer.print_text(title, 'h3')
                                self.visualize(
                                    enrichment_results_subset,
                                    group_vars=user_group_bys,
                                    show_table=True,
                                    last_day_only=True,
                                    should_pivot_table=True,
                                    pivot_table_rows=[value for value in ['rank'] + df_enrichment.columns.to_list() if value not in user_group_bys],
                                    pivot_table_cols=user_group_bys + ['metric'],
                                    stat_fmtr=self.configurations['stat_fmtr'],
                                )

                        else:
                            subpopulation_label = ", ".join(
                                "{}={}".format(dim, val) for dim, val in zip(user_group_bys, user_breakdown_values)
                            )
                            if not subpopulation_label:
                                subpopulation_label = "overall"
                            title = (
                                f"{wl_label} of {winners_losers_dimension_name} in {subpopulation_label} by "
                                f"{metric_display_name} for: {treatment_id}"
                            )
                            self.ab_printer.print_text(title, 'h3')
                            self.visualize(
                                table_data,
                                group_vars=user_group_bys,
                                show_table=True,
                                last_day_only=True,
                                should_pivot_table=True,
                                pivot_table_rows=['rank'] + (winners_losers_dimensions if not enrichment_sql else df_enrichment.columns.to_list()),
                                pivot_table_cols=user_group_bys + ['metric'],
                                stat_fmtr=self.configurations['stat_fmtr'],
                            )

class QuantileReport(Report):
    """
    Run the analysis with quantile metrics
    """

    def __init__(self, quantiles=None, **kwargs):
        err_msg = """
        This module has been deprecated. Use CohortReport instead. Example:
        quantile_report = CohortReport(
            study_name=STUDY_NAME,
            study_start_date=STUDY_START_DATE,
            study_end_date=STUDY_END_DATE,
            metric_tables=metrics,
            control_id=CONTROL_ID,
            treatment_ids=TREATMENT_IDS,
            user_group_bys=USER_BREAKDOWN_LIST,
            bq_project='sc-bq-gcs-billingonly',
            quantiles=["50", "90"]
        )
        """
        raise DeprecationWarning(err_msg)


def notebook_print(text, html_tag='h3'):
    IPythonReportPrinter().print_text(text, html_tag)


def get_timestamp_sql(timestamps):
    """Get SQL that defines a table with a TIMESTAMP field called ts"""
    sql = ', '.join('TIMESTAMP("{}")'.format(ts.strftime("%Y-%m-%d %H:%M:%S")) for ts in timestamps)
    return sql
