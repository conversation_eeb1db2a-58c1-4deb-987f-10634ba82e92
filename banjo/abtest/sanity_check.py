import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.ticker import FuncFormatter

from .stat_funcs import chisq_test_sample_size


def plot_(counts, expected=None):
    average = np.mean(counts)
    if expected is None:
        expected = [average] * len(counts)

    pct_diff = [(c - average) / average for c in counts]
    chisq = chisq_test_sample_size(counts, expected)

    fig, ax = plt.subplots()
    pd.Series(pct_diff).plot(kind='bar')
    ax.axhline(0, color='r', alpha=1 / 2)
    ax.yaxis.set_major_formatter(
        FuncFormatter(lambda y, _: '{:.2%}'.format(y)))
    ylim = ax.get_ylim()
    new_ylim = [min([-0.01, ylim[0]]),
                max([0.01, ylim[1]])
                ]
    ax.set_ylim(new_ylim)

    ax.set_title('Relative difference between observed and expected'
                 ' experiment sizes\n'
                 'Chisq test P-value = {:.3}'.format(chisq[0]))
