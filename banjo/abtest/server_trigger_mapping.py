"""Create mapping tables for server side logged A/B studies
See go/ab-guide and search for server side study for more details

Need the lease to feelinsonice-hard:prod_partition_events dataset
from go/prod-partition-events
"""

from __future__ import division

from banjo.utils.gbq import submit_sync_query
from .device_name_mapping import get_device_name_mapping_sql

EXTRACT_FEELINSONICE_ALL_MAPPING = """
    SELECT
      TIMESTAMP('{date} 00:00:00') AS ts,
      user,
      JSON_EXTRACT_SCALAR(other_params, "$.study_name") study_name,
      JSON_EXTRACT_SCALAR(other_params, "$.experiment_id") AS exp_id,
      CASE
        WHEN userAgent CONTAINS 'iOS' THEN 'iOS'
        WHEN userAgent CONTAINS 'Android' THEN 'Android'
        ELSE 'OTHER'
      END AS os,
      REGEXP_EXTRACT(userAgent, r'Snapchat/(\d*?\.\d*?)\D') AS app_version,
      CASE
        WHEN userAgent CONTAINS 'iOS'
          THEN IF(userAgent CONTAINS 'iPhone', 'iPhone' + REGEXP_EXTRACT(userAgent,r'.*iPhone(.*?)\;'), 'iOS_Others')
        WHEN userAgent CONTAINS 'Android'
          THEN IFNULL(
                 REGEXP_EXTRACT(userAgent, r'Snapchat/[\d+.]+\s[a-zA-Z]*[\s]*\(([\w\s\-]+); Android'),
                 'Android_Others'
          )
        ELSE 'Others'
      END AS device_version,
      COUNT(1) AS event_count
    FROM
      [feelinsonice-hrd:prod_partition_events.abtest_{date}]
    WHERE
      eventName = 'ABTEST_EXP_USER_GROUP'
    GROUP EACH BY
      user,
      study_name,
      exp_id,
      os,
      app_version,
      device_version
"""

ALL_MAPPING_TABLE_NAME = 'ALL_SERVER_SIDE_TRIGGER'
AB_SERVER_OVERALL_MAPPING_TABLE_PATH = 'sc-portal:abtest_server_v1_overall_user_mapping.overall_user_mapping'
CUMULATIVE_MAPPING_DATASET = "abtest_study_server_map"
DAILY_MAPPING_DATASET = "abtest_study_server_map"

# This follows the official pipeline for the client side mapping tables
CREATE_SINGLE_EXPERIMENT_MAPPING = """
    SELECT
      t_map.ts AS ts,
      t_map.user AS user,
      t_map.exp_id AS exp_id,
      t_map.os AS os,
      t_map.app_version AS app_version,
      t_map.device_name AS device_name,
      IFNULL(t_identity.country, 'ZZ') AS country,
      IFNULL(t_identity.user_start_month, 'UNKNOWN') AS user_start_month,
      IFNULL(t_identity.gender, 'UNKNOWN') AS gender,
      IFNULL(t_identity.age_group, 'UNKNOWN') AS age_group,
    FROM (
      SELECT
        ts,
        user,
        MAX(exp_id) AS exp_id,
        -- Follow the official pipeline's logic to take the maximum values
        MAX(os_version) AS os,
        MAX(snapchat_version) AS app_version,
        MAX({device_name_mapping}) AS device_name
      FROM
        [{table}_{date}]
      WHERE
        study_name =  '{study_name}'
      GROUP EACH BY
        ts, user
      HAVING
        COUNT(DISTINCT exp_id) = 1
    ) AS t_map
    LEFT JOIN EACH (
      SELECT
        user_name,
        MAX(l_90_country) AS country,
        MAX(LEFT(DATE(DATE_ADD(creation_time, -8, 'HOUR')), 7)) AS user_start_month,
        MAX(gender) AS gender,
        MAX(CASE
          WHEN age >= 55 THEN '55_plus'
          WHEN age >= 35 THEN '35_54'
          WHEN age >= 25 THEN '25_34'
          WHEN age >= 18 THEN '18_24'
          WHEN age >= 13 THEN '13_17'
          ELSE 'UNKNOWN'
        END) AS age_group,
      FROM
        [sc-analytics:report_user.user_demographic_{date}]
      GROUP EACH BY
        user_name
    ) AS t_identity
    ON
      t_map.user = t_identity.user_name
"""

DEVICE_NAME_MAPPING = get_device_name_mapping_sql(source_column="device_version")
SINGLE_MAPPING_TABLE_NAME = '{study_name}'
SINGLE_MAPPING_TABLE_NAME_CUMULATIVE = '{study_name}__{start_date}'

CREATE_CUMULATIVE_EXPERIMENT_MAPPING = """
    SELECT
      TIMESTAMP('{start} 00:00:00') AS start_ts,
      TIMESTAMP('{end} 00:00:00') AS ts,
      user,
      exp_id,
      os,
      app_version,
      device_name,
      country,
      user_start_month,
      gender,
      age_group,
    FROM (
      SELECT
        user,
        MAX(exp_id) AS exp_id,
        COUNT(DISTINCT exp_id) AS count_exp_id,
        LAST(os) AS os,
        LAST(app_version) AS app_version,
        LAST(device_name) AS device_name,
        LAST(country) AS country,
        LAST(user_start_month) AS user_start_month,
        LAST(gender) AS gender,
        LAST(age_group) AS age_group,
      FROM
        TABLE_DATE_RANGE_STRICT(
          [{table}__],
          TIMESTAMP('{start}'),
          TIMESTAMP('{end}')
        )
      GROUP EACH BY
        user
      HAVING
        count_exp_id = 1
    )
"""


def extract_all_mapping(
        date,
        bq_project,
        dest_dataset=DAILY_MAPPING_DATASET,
        dest_table=ALL_MAPPING_TABLE_NAME,
        write_disposition='WRITE_EMPTY'):
    raise AttributeError("We no longer need to create the overall mapping table. "
                         "It is not generated by a daily flowrida pipeline: "
                         "abtest_server_v1_overall_user_mapping_table. "
                         "This module has been updated to read from that table.")
    date = date.replace('-', '')
    query = EXTRACT_FEELINSONICE_ALL_MAPPING.format(
        date=date
    )
    bq_job = submit_sync_query(
        query,
        project_id=bq_project,
        dest_dataset_id=dest_dataset,
        dest_table_name='__'.join([dest_table, date]),
        write_disposition=write_disposition
    )

    return bq_job


def create_single_mapping(
        date,
        bq_project,
        study_name,
        ab_server_overall_mapping_table_path=AB_SERVER_OVERALL_MAPPING_TABLE_PATH,
        dest_dataset=DAILY_MAPPING_DATASET,
        write_disposition='WRITE_EMPTY'):
    date = date.replace('-', '')

    query = CREATE_SINGLE_EXPERIMENT_MAPPING.format(
        table=ab_server_overall_mapping_table_path,
        date=date,
        study_name=study_name,
        device_name_mapping=DEVICE_NAME_MAPPING,
    )
    dest_table = SINGLE_MAPPING_TABLE_NAME.format(study_name=study_name)

    bq_job = submit_sync_query(
        query,
        project_id=bq_project,
        dest_dataset_id=dest_dataset,
        dest_table_name='__'.join([dest_table, date]),
        write_disposition=write_disposition
    )
    return bq_job


def create_cumulative_mapping(
        start_date,
        end_date,
        bq_project,
        study_name,
        dest_dataset=CUMULATIVE_MAPPING_DATASET,
        write_disposition='WRITE_EMPTY'):
    start_date = start_date.replace('-', '')
    end_date = end_date.replace('-', '')
    source_table = (bq_project + ':'
                    + DAILY_MAPPING_DATASET + '.'
                    + study_name
                    )

    query = CREATE_CUMULATIVE_EXPERIMENT_MAPPING.format(
        table=source_table,
        start=start_date,
        end=end_date,
    )
    dest_table = SINGLE_MAPPING_TABLE_NAME_CUMULATIVE.format(
        study_name=study_name,
        start_date=start_date
    )

    bq_job = submit_sync_query(
        query,
        project_id=bq_project,
        dest_dataset_id=dest_dataset,
        dest_table_name='__'.join([dest_table, end_date]),
        write_disposition=write_disposition
    )
    return bq_job
