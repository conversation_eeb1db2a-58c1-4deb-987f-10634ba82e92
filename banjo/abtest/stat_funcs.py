"""
<PERSON><PERSON>'s Python code used in the GCE API service for A/B test backend
"""
import math
import numpy as np
from scipy.special import stdtr
from scipy import stats
import pandas as pd
import logging

SIG_T_VALUE = 1.960  # the t-score with sample size > 500 and confidence level of 95%
SIG_Z_VALUE = 1.95996398454005  # the z-score with confidence level of 95%

__all__ = [
    'calc_diff_population_means_large_independent_samples',
    'calc_diff_population_proportions_large_samples',
    'calc_population_proportion_large_samples',
    'calc_diff_event_quantiles_large_samples',
    'chisq_test_sample_size',
    'anova_sample_mean',
    'calc_chisq_dist',
]


def calc_diff_population_means_large_independent_samples(c_n, c_sum, c_sums,
                                                         t_n, t_sum, t_sums,
                                                         c_sum_original=None, t_sum_original=None):
    """Two-Sample Test for Equal Means

    Perform the test for the means of two independent large samples. This is a two-sided test for the null
    hypothesis that two independent samples have identical means.

    Examples
    --------

    >>> calc_diff_population_means_large_independent_samples(
    ...     1000,
    ...     500500,
    ...     333833500,
    ...     1000,
    ...     501500,
    ...     334835500
    ... )
    P_VALUE                                                       0.938296
    CONFIDENCE                     (-24.31613977946348, 26.31613977946348)
    DIFFERENCE                                                      0.1998
    DIFFERENCE_ERROR_MARGIN        (-4.860892978558529, 5.260493378158929)
    P_VALUE_RELATIVE_DIFFERENCE                                   0.938319
    dtype: object

    """
    c_mean = c_sum / (c_n * 1.0)
    t_mean = t_sum / (t_n * 1.0)

    c_variation = (
                          (c_sums / (c_n * 1.0)) - (c_mean * c_mean)) * (c_n * 1.0 / (c_n - 1))
    t_variation = (
                          (t_sums / (t_n * 1.0)) - (t_mean * t_mean)) * (t_n * 1.0 / (t_n - 1))

    delta_mean = t_mean - c_mean
    delta_mean_se = np.sqrt(c_variation / c_n + t_variation / t_n)
    conf_lb, conf_ub = delta_mean - SIG_T_VALUE * delta_mean_se, delta_mean + SIG_T_VALUE * delta_mean_se

    t_val = (c_mean - t_mean) / np.sqrt(c_variation / c_n + t_variation / t_n)
    dof = (c_variation / c_n + t_variation / t_n) ** 2 / \
          (c_variation ** 2 / c_n / c_n / (c_n - 1) + t_variation ** 2 / t_n / t_n / (t_n - 1))
    p_val = 2 * stdtr(dof, -np.abs(t_val))
    if c_sum_original is None:
        difference = 100 * (delta_mean / c_mean)
    else:
        original_c_mean = c_sum_original / c_n
        difference = 100 * (delta_mean / original_c_mean)

    var_difference = calc_variance_of_perc_diff_with_delta_method(c_n, t_n, c_sum, c_sums, t_sum, t_sums,
                                                                  c_sum_original, t_sum_original)
    difference_se = 100 * np.sqrt(var_difference)
    difference_error_margin_lb, difference_error_margin_ub = (
        difference - SIG_T_VALUE * difference_se,
        difference + SIG_T_VALUE * difference_se
    )

    z_val_relative_change = difference / difference_se
    p_val_relative_change = 2. * norm_cdf(-np.abs(z_val_relative_change))

    statistics = pd.Series({
        'P_VALUE': p_val,
        'CONFIDENCE': (conf_lb, conf_ub),
        'DIFFERENCE': difference,
        'DIFFERENCE_ERROR_MARGIN': (difference_error_margin_lb,
                                    difference_error_margin_ub),
        'P_VALUE_RELATIVE_DIFFERENCE': p_val_relative_change,
    })
    return statistics


def calc_diff_population_proportions_large_samples(
        ctrl_succ_cnt, ctrl_trial_cnt, trmt_succ_cnt, trmt_trial_cnt):
    """Two-sample proportion test

    Perform the test for the two population proportions of two large samples. This is a two-sided test for the null
    hypothesis that 2 samples have identical proportion values.

    Note that we would need to use pooled estimator of the population proportion when we perform the hypothesis
    testing, while we need the un-pooled version when we estimate the confidence interval.

    Examples
    --------
    >>> calc_diff_population_proportions_large_samples(
    ...     110,
    ...     1000,
    ...     570,
    ...     5000
    ... )
    P_VALUE                                                            0.712992
    CONFIDENCE                     (-0.017309341289033824, 0.02530934128903383)
    DIFFERENCE                                                          3.63636
    DIFFERENCE_ERROR_MARGIN           (-16.312859879704966, 23.585587152432247)
    P_VALUE_RELATIVE_DIFFERENCE                                        0.720889
    dtype: object

    """

    return calc_diff_population_means_large_independent_samples(ctrl_trial_cnt, ctrl_succ_cnt, ctrl_succ_cnt,
                                                                trmt_trial_cnt, trmt_succ_cnt, trmt_succ_cnt)


def calc_population_proportion_large_samples(succ_cnt, trial_cnt,
                                             prob_succ_hypothesized):
    """One-sample proportion test

    Perform the test for the population proportions of a large sample. This is a two-sided test for the null
    hypothesis that the sample has the given proportion value.

    Examples
    --------
    >>> calc_population_proportion_large_samples(
    ...     112,
    ...     522,
    ...     0.2
    ... )
    CONFIDENCE                 (0.179343123355, 0.249775650591)
    DIFFERENCE                                          7.27969
    DIFFERENCE_ERROR_MARGIN     (-10.3284383223, 24.8878252955)
    P_VALUE                                            0.405631
    dtype: object
    """
    # compute p_val
    std_dev_prop_hypothesized = np.sqrt(prob_succ_hypothesized * (
            1. - prob_succ_hypothesized) / trial_cnt)
    prob_succ_observed = float(succ_cnt) / float(trial_cnt)
    z_score = (prob_succ_observed - prob_succ_hypothesized
               ) / std_dev_prop_hypothesized
    p_val = 2. * stats.norm.cdf(-np.abs(z_score))

    # compute confidence interval
    std_dev_prop_observed = np.sqrt(prob_succ_observed *
                                    (1. - prob_succ_observed) / trial_cnt)
    conf_lb = prob_succ_observed - SIG_Z_VALUE * std_dev_prop_observed
    conf_ub = prob_succ_observed + SIG_Z_VALUE * std_dev_prop_observed

    # compute difference
    difference = (prob_succ_observed / prob_succ_hypothesized - 1.) * 100.
    difference_error_margin_lb, difference_error_margin_ub = (conf_lb / prob_succ_hypothesized - 1.) * 100., \
                                                             (conf_ub / prob_succ_hypothesized - 1.) * 100.

    statistics = pd.Series({
        'P_VALUE': p_val,
        'CONFIDENCE': (conf_lb, conf_ub),
        'DIFFERENCE': difference,
        'DIFFERENCE_ERROR_MARGIN': (difference_error_margin_lb,
                                    difference_error_margin_ub)
    })

    return statistics


def chisq_test_sample_size(counts, expected=None, expected_props=None):
    average = np.mean(counts)
    total = np.sum(counts)
    if expected is None:
        if expected_props:
            if abs(np.sum(expected_props) - 1) > 10e-12:
                raise ValueError('Sum of expected proportions must be 1')
            expected = [total * prop for prop in expected_props]
        else:
            logger = logging.getLogger(__name__)
            logger.warning('Chi-squared test: assuming the null hypothesis is '
                           'equal count/proportion across buckets. ')
            expected = [average] * len(counts)
    chisq = stats.chisquare(counts, expected)

    return chisq[1], chisq[0]


def anova_sample_mean(n, sum_, sum_of_squares):
    """ One-way ANOVA for multiple group sample means

    Parameters
    ----------
    n : :obj:`list` of :obj:`int`
    sum_ : :obj:`list` of :obj:`int`
    sum_of_squares :  :obj:`list` of :obj:`int`
        Sum of squares

    Returns
    -------
    p_value, f : tuple of float

    See Also
    --------
    scipy.stats.f_oneway and source code

    Examples
    --------
    >>> anova_sample_mean([20, 30, 40], [200, 350, 601], [3000, 7000, 8841])
    (0.012729843282028549, 4.590195543652495)
    >>> anova_sample_mean(
    ...     [6159339, 6166565, 6159358, 6159693, 6163911, 6161137, 6164617,
    ...      6157493, 6160645, 6159062],
    ...     [659275417, 661296122, 658727162, 658961748, 659872423, 660289655,
    ...      659137420, 659826937, 659754204, 657664511],
    ...     [473216550279, 476292642824, 471893188944, 473433852996,
    ...      474871000609, 475368071401, 473116623980, 477148018003,
    ...      474020668208, 470565442491]
    ... )
    (0.073124546341586702, 1.7458775883712239)
    """
    if len(n) != len(sum_) or len(n) != len(sum_of_squares):
        raise ValueError('All parameters must be of the same size')

    # Cast to Python native types
    n = [int(x) for x in n]
    sum_ = [float(x) for x in sum_]
    sum_of_squares = [float(x) for x in sum_of_squares]

    num_groups = len(n)
    bign = sum(n)

    # Naming: variables ending in bn/b are for "between treatments", wn/w are
    # for "within treatments"
    sstot = sum(sum_of_squares) - ((sum(sum_) / float(bign)) ** 2) * float(bign)
    ssbn = 0
    for i in range(num_groups):
        ssbn += sum_[i] ** 2 / float(n[i])

    ssbn -= (sum(sum_) ** 2 / float(bign))
    sswn = sstot - ssbn
    dfbn = num_groups - 1
    dfwn = bign - num_groups
    msb = ssbn / float(dfbn)
    msw = sswn / float(dfwn)
    f = msb / msw

    prob = stats.f.sf(f, dfbn, dfwn)

    return prob, f


def calc_diff_event_quantiles_large_samples(
        c_n, c_q, c_den, c_vol, c_vol2, c_gc, c_gc2, c_cov,
        t_n, t_q, t_den, t_vol, t_vol2, t_gc, t_gc2, t_cov,
):
    # Asymptotic quantile test

    def calc_var(t, t2, n):
        return t2 / (n * 1.0) - t * t / (n * n * 1.0)

    c_meanz = c_gc / (c_n * 1.0)
    c_meanp = c_vol / (c_n * 1.0)
    c_varz = calc_var(c_gc, c_gc2, c_n)
    c_varp = calc_var(c_vol, c_vol2, c_n)
    c_covpz = c_cov / (c_n * 1.0) - c_meanz * c_meanp
    c_variation = c_varz / (c_meanp * c_meanp) + c_varp * (c_meanz * c_meanz) / (
            c_meanp * c_meanp * c_meanp * c_meanp) - 2 * c_meanz * c_covpz / (
                          c_meanp * c_meanp * c_meanp)
    c_var = c_variation / (c_n * c_den * c_den)
    t_meanz = t_gc / (t_n * 1.0)
    t_meanp = t_vol / (t_n * 1.0)
    t_varz = calc_var(t_gc, t_gc2, t_n)
    t_varp = calc_var(t_vol, t_vol2, t_n)
    t_covpz = t_cov / (t_n * 1.0) - t_meanz * t_meanp
    t_variation = t_varz / (t_meanp * t_meanp) + t_varp * (t_meanz * t_meanz) / (
            t_meanp * t_meanp * t_meanp * t_meanp) - 2 * t_meanz * t_covpz / (
                          t_meanp * t_meanp * t_meanp)
    t_var = t_variation / (t_n * t_den * t_den)

    delta_quantile = t_q - c_q
    delta_quantile_se = np.sqrt(t_var + c_var)
    z_stat = (delta_quantile * 1.0) / delta_quantile_se
    p_val = 2 * stats.norm.cdf(- np.abs(z_stat))
    conf_lb, conf_ub = delta_quantile - SIG_T_VALUE * delta_quantile_se, delta_quantile + SIG_T_VALUE * delta_quantile_se

    difference = 100 * (delta_quantile / c_q)
    difference_error_margin_lb, difference_error_margin_ub = 100 * (
            conf_lb / c_q), 100 * (conf_ub / c_q)
    statistics = pd.Series({
        'P_VALUE': p_val,
        'CONFIDENCE': (conf_lb, conf_ub),
        'DIFFERENCE': difference,
        'DIFFERENCE_ERROR_MARGIN': (difference_error_margin_lb,
                                    difference_error_margin_ub)
    })
    return statistics


def calc_diff_ratio_metrics_large_independent_samples(
        c_n, c_x_sum, c_x_sums, c_y_sum, c_y_sums, c_xy_sum,
        t_n, t_x_sum, t_x_sums, t_y_sum, t_y_sums, t_xy_sum
):
    # Calculates the statistics of ratio metrics using delta method

    t_ratio = t_y_sum / (t_x_sum * 1.0)
    c_ratio = c_y_sum / (c_x_sum * 1.0)
    t_var = calc_variance_of_ratio_with_delta_method(c_n, c_x_sum, c_x_sums, c_y_sum, c_y_sums, c_xy_sum)
    c_var = calc_variance_of_ratio_with_delta_method(t_n, t_x_sum, t_x_sums, t_y_sum, t_y_sums, t_xy_sum)
    delta = t_ratio - c_ratio
    delta_se = np.sqrt(t_var + c_var)
    z_stat = (delta * 1.0) / delta_se
    p_val = 2 * stats.norm.cdf(- np.abs(z_stat))
    conf_lb, conf_ub = delta - 1.96 * delta_se, delta + 1.96 * delta_se

    difference = 100 * (delta / c_ratio)
    difference_error_margin_lb, difference_error_margin_ub = 100 * (
            conf_lb / c_ratio), 100 * (conf_ub / c_ratio)
    statistics = pd.Series({
        'P_VALUE': p_val,
        'CONFIDENCE': (conf_lb, conf_ub),
        'DIFFERENCE': difference,
        'DIFFERENCE_ERROR_MARGIN': (difference_error_margin_lb,
                                    difference_error_margin_ub)
    })
    return statistics


def calc_variance_of_ratio_with_delta_method(n, x_sum, x_sums, y_sum, y_sums, xy_sum):
    """Delta method required for ratio of random variables Y/X"""

    x_mean = x_sum / (n * 1.0)
    x_var = (x_sums - n * x_mean * x_mean) / (n * 1.0 - 1)

    y_mean = y_sum / (n * 1.0)
    y_var = (y_sums - n * y_mean * y_mean) / (n * 1.0 - 1)

    xy_cov = (xy_sum - n * x_mean * y_mean) / (n * 1.0 - 1)

    return (1 / (x_mean * x_mean)) * (y_var / n) + \
        ((y_mean * y_mean) / (x_mean * x_mean * x_mean * x_mean)) * (x_var / n) - \
        (2 * y_mean / (x_mean * x_mean * x_mean)) * (xy_cov / n)


def calc_variance_of_perc_diff_with_delta_method(n_x, n_y, x_sum, x_sums, y_sum, y_sums,
                                                 c_sum_original=None,
                                                 t_sum_original=None):
    """Calculate the variance of percentage delta with delta method Y/X"""
    x_mean = x_sum / (n_x * 1.0)
    x_var = x_sums / (n_x * 1.0) - x_mean * x_mean

    y_mean = y_sum / (n_y * 1.0)
    y_var = y_sums / (n_y * 1.0) - y_mean * y_mean

    if c_sum_original:
        x_mean = c_sum_original / (n_x * 1.0)
    if t_sum_original:
        y_mean = t_sum_original / (n_y * 1.0)

    return (1 / (x_mean * x_mean)) * (y_var / n_y) + \
        ((y_mean * y_mean) / (x_mean * x_mean * x_mean * x_mean)) * (x_var / n_x)


def norm_cdf(x):
    return (1.0 + math.erf(x / math.sqrt(2.0))) / 2.0


def calculate_bh_significance(p_values, fdr):
    """ Benjamini Hochberg p values adjustment
    Take a list of p values and return a list of bh-p-values. NaN and None will be returned as None.
    Raise exception on other invalid values.
    user guide: https://docs.google.com/document/d/16Oy-b8K4q9e9s_5n4RwvL_2civrH1zAWFjRhTVcaTqQ/edit#
    design doc: https://docs.google.com/document/d/11-n8L94FnalMM6blGgMzRtLuewkL2jdzomQ3VFZvyu4/edit#
    See also (ABconsole team): https://github.sc-corp.net/Snapchat/ab-test-console/blob/master/statistics/bh_adjust.py#L4

    Parameters
    ----------
    p_values : list of float
        The list of multiple comparison p-values
    fdr : float
        The FDR for determining significance

    Returns
    -------
    bh_q_values : list of float
        The list of BH q-values
    bh_significant : list of bool
        Wether each q-value is smaller than the threshold

    Examples
    --------
    >>> bh_q_values, bh_significant = calculate_bh_significance(
    ...     [0.001, np.nan, 0.002, 0.002, 0.99, 0.002, None, 0.3, 0.3, 0.9, 0.9],
    ...     fdr=0.05,
    ... )
    >>> print([round(q_value, 4) if q_value is not None else None for q_value in bh_q_values])
    [0.0045, None, 0.0045, 0.0045, 0.99, 0.0045, None, 0.45, 0.45, 0.99, 0.99]
    >>> print(bh_significant)
    [True, False, True, True, False, True, False, False, False, False, False]
    """
    bh_q_values = [None] * len(p_values)

    p_value_index_pairs = list()
    for i, p_val in enumerate(p_values):
        if p_val is None or np.isnan(p_val):
            continue
        try:
            p_val = float(p_val)
            if not math.isnan(p_val):
                p_value_index_pairs.append((i, p_val))
        except ImportError:
            raise
        except Exception:
            raise Exception("p value: %s is invalid" % p_val)

    p_value_index_pairs.sort(key=lambda p: p[1])  # sort by the size of the p-values

    bh_q_value_index_pairs = list()

    if len(p_value_index_pairs) > 0:
        bh_q_value_index_pairs.append(p_value_index_pairs[-1])
        total_num = len(p_value_index_pairs)
        for i in range(total_num - 2, -1, -1):
            bh_p_value = min(bh_q_value_index_pairs[-1][1], p_value_index_pairs[i][1] * total_num / float(i + 1))
            bh_q_value_index_pairs.append((p_value_index_pairs[i][0], bh_p_value))

        for bh_q_value_index_pair in bh_q_value_index_pairs:
            bh_q_values[bh_q_value_index_pair[0]] = bh_q_value_index_pair[1]

    bh_significant = [True if q is not None and q < fdr else False for q in bh_q_values]
    return bh_q_values, bh_significant

def calc_treatment_by_cohort_interaction(mean_treatment_effect, var_treatment_effect):
    """Test of Heterogeneous Treatment Effect using Multivariate Normal Distribution
    Perform the test for HTE detection. This is a two-sided test for the null
    hypothesis that the treatment effect is the same across cohorts.
    
    Implements http://go.sc-corp.net/interaction-test

    Parameters
    ----------
    mean_treatment_effect : list of float
        the list of treatment effect in each cohort [d1, d2, ...dk]
    var_treatment_effect : list of float
        the list of treatment effect variance in each cohort [v1, v2, ... vk]

    Examples
    --------
    >>> calc_treatment_by_cohort_interaction(
    ...     [2, 3, 1, 2],
    ...     [1, 2, 1.5, 3]
    ... )
    P_VALUE       0.763682
    CHI_SQUARE    1.155556
    dtype: float64
    """

    # sanity check to ensure the inputs make sense
    if len(mean_treatment_effect) != len(var_treatment_effect):
        raise ValueError('mean_treatment_effect and var_treatment_effect should have the same length.')

    # k is the number of cohort breakdowns;
    k = len(mean_treatment_effect)

    # create matrix A to easily compute d1 - d2, d1 - d3, ... d1 - dk
    A = np.matrix([[0 for row in range(k)] for col in range(k - 1)])
    for row in range(k - 1):
        A[row, 0] = 1
        A[row, row + 1] = -1
    diff_in_delta = A @ mean_treatment_effect

    # create diagonal matrix V.
    # the off diagonal values are zero given the treatment effects in different cohorts are independent.
    V = np.matrix([[0 if row != col else var_treatment_effect[row] for row in range(k)] for col in range(k)])

    # calculate chi square value associated with k - 1 degrees of freedom.
    try:
        chisq_value = (diff_in_delta @ (np.linalg.inv(A @ V @ A.transpose())) @ diff_in_delta.transpose())[0, 0]
    except np.linalg.LinAlgError:
        logging.warning("AVA_t' is not invertible")
        return pd.Series({
            'P_VALUE': np.nan,
            'CHI_SQUARE': np.nan
        })
    df = k - 1
    p_val = 1 - stats.chi2.cdf(chisq_value, df)

    statistics = pd.Series({
        'P_VALUE': p_val,
        'CHI_SQUARE': chisq_value
    })
    return statistics


def calc_chisq_dist(avg_control, avg_treatment):
    """Calculate chi-squared distance between two distributions.

    Parameters
    ----------
    avg_control : list of float
        the list of control avg in each metric field breakdown level
    avg_treatment : list of float
        the list of treatment avg in each metric field breakdown level
    Examples
    --------
    >>> calc_chisq_dist(
    ...     [25.172173, 3.263700, 0.276352],
    ...     [25.293046, 3.231805, 0.272182]
    ... )
    CHISQ_DIST  0.003503
    dtype: float64
    """
    # sanity check to ensure the inputs make sense
    if len(avg_control) != len(avg_treatment):
        raise ValueError('avg_control and avg_treatment should have the same length')
    # calculate chi-squared distance
    avg_control_all = sum(avg_control)
    avg_treatment_all = sum(avg_treatment)
    normalized_control = [x / avg_control_all for x in avg_control]
    normalized_treatment = [x / avg_treatment_all for x in avg_treatment]
    chi_squared_distance = 0
    for x, y in zip(normalized_control, normalized_treatment):
        if x + y > 0:
            chi_squared_distance += (x - y) * (x - y) / (x + y)
    # take sqrt of the chi-squared distance as the final output
    chisq_dist = math.sqrt(chi_squared_distance)
    return chisq_dist


def compute_fieller_ci(
        mean_c,
        mean_t,
        var_c,
        var_t,
        covariance_12=0,  # this is an assumption
        critical_value=SIG_Z_VALUE
):
    """ generic function of fieller confidence interval calculation
    """
    if mean_c ** 2 > critical_value ** 2 * var_c and var_c > 0:
        ci_center = (mean_c * mean_t - critical_value ** 2 * covariance_12) / (
                    mean_c ** 2 - critical_value ** 2 * var_c)
        bound_offset = (
                               (mean_c * mean_t - critical_value ** 2 * covariance_12) ** 2
                               - (mean_c ** 2 - critical_value ** 2 * var_c) * (
                                           mean_t ** 2 - critical_value ** 2 * var_t)
                       ) ** 0.5 \
                       / \
                       (mean_c ** 2 - critical_value ** 2 * var_c)
        return [ci_center - bound_offset - 1, ci_center + bound_offset - 1]
    else:
        return [float('NaN'), float('NaN')]


def calc_variance_of_ratio_with_delta_method_using_mean_and_var(
        avg_numerator,
        avg_denominator,
        var_numerator,
        var_denominator,
        covariance
):
    """This formula is a large-sample approximation of the Fieller theorem, and is described here:
    https://www.stat.cmu.edu/~hseltman/files/ratio.pdf.
    It assumes the ratio of two normally distributed variables (value_1/value_2) can be approximated as a
    t-distributed variable, which here is further approximated as normal.
    This function is utilized to help estimate a confidence interval for the ratio of
    two ratios (as for cost per install), since the Fieller theorem requires an input variance for numerator
    and denominator but does not output one itself.

    This function return the ratio point estimate and ratio variance, where ratio could be CPM, etc
    """
    if avg_numerator > 0 and avg_denominator > 0 and var_numerator > 0 and var_denominator > 0:
        ratio_est = avg_numerator / avg_denominator
        ratio_variance = ratio_est ** 2 * (
                var_numerator / (avg_numerator ** 2)
                - 2 * covariance / (avg_numerator * avg_denominator)
                + var_denominator / (avg_denominator ** 2)
        )
        return [ratio_est, ratio_variance]
    else:
        return [float('NaN'), float('NaN')]


def calcualte_rate_ci(
        avg_numerator_t,
        avg_denominator_t,
        var_numerator_t,
        var_denominator_t,
        covariance_t,
        avg_numerator_c,
        avg_denominator_c,
        var_numerator_c,
        var_denominator_c,
        covariance_c
):
    """ Use the Fieller method to compute the CI of rate metrics
    A rate is defined as the ratio of two ratios,
    e.g. rate = ratio_treatment/ratio_control, where ratio_treatment(control) could be CPM treatment(control)
    The variance of the denominator and numerator ratios are estimated
    using the delta method.

    - numerator_t: sum(numerator) in treatment, numerator_c: sum(numerator) in control
    - denominator_t: sum(denominator) in treatment, denominator_c: sum(denominator) in control
    """
    ratio_est_t, ratio_var_t = calc_variance_of_ratio_with_delta_method_using_mean_and_var(
        avg_numerator_t,
        avg_denominator_t,
        var_numerator_t,
        var_denominator_t,
        covariance_t
    )

    ratio_est_c, ratio_var_c = calc_variance_of_ratio_with_delta_method_using_mean_and_var(
        avg_numerator_c,
        avg_denominator_c,
        var_numerator_c,
        var_denominator_c,
        covariance_c
    )

    if avg_denominator_t != 0 and avg_denominator_c != 0:
        return compute_fieller_ci(ratio_est_c, ratio_est_t, ratio_var_c, ratio_var_t)  # , 0, 1.959964)
    else:
        return [float('NaN'), float('NaN')]
