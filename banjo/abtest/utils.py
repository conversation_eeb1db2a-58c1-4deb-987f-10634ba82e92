"""Utils for A/B testing analysis"""
from __future__ import division, print_function, unicode_literals

import logging
import os
import pandas as pd
import six
from datetime import datetime, timedelta

from banjo.utils import gbq

AB_METRICS_ARCHIVE_DATASET = "ab_metrics_archive"
AB_METRICS_ARCHIVE_PROJECT = "sc-bq-gcs-billingonly"


def _from_bytes(data, big_endian=False):
    """Converts bytes to integer"""
    if isinstance(data, str):
        data = bytearray(data)
    if big_endian:
        data = reversed(data)
    num = 0
    for offset, byte in enumerate(data):
        num += byte << (offset * 8)
    return num


def _twos_comp(num, bits):
    """compute the 2's complement of num"""
    if (num & (1 << (bits - 1))) != 0:  # if sign bit is set e.g., 8bit: 128-255
        num = num - (1 << bits)  # compute negative value
    return num


def get_user_hash(seed, username):
    """ Get the user hash used in AB backend

    https://github.sc-corp.net/Snapchat/appengine/blob/24745450071ea59befbcdd4183028d9fe1069743/
    snapchat-webapp/src/main/java/snapchat/experiment/Study.java#L575-L577

    Parameters
    ----------
    seed : str
        Seed. Usually the study name or study name plus the study version
    username : str
        The user name. (Note that A/B no longer uses user names in the hashing logic. Instead, please
        use UUID)

    Returns
    -------
    hash_ : int

    Python3 only for now because of int.from_bytes

    Examples
    --------
    >>> get_user_hash("NO_ALLOCATION_BIAS_UPLOAD", "premila2007")
    -1917524179720111626
    >>> get_user_hash("NO_ALLOCATION_BIAS_UPLOAD", "kaizreen")
    -7213869659945621922
    >>> get_user_hash("NO_ALLOCATION_BIAS_UPLOAD", "soma_koko")
    3174918394160081581
    """
    try:
        import mmh3
    except ImportError:
        logging.error("Please install mmh3 first to use the get_user_hash function.")
        raise
    
    source_string = seed + username
    first_n_bytes = 8

    hashed_bytes = mmh3.hash_bytes(source_string)[:first_n_bytes]

    if six.PY3:
        hash_ = int.from_bytes(
            hashed_bytes,
            byteorder='little',
            signed=True,
        )
    else:  # Python 2
        hash_ = int(_twos_comp(_from_bytes(
            hashed_bytes), bits=8 * first_n_bytes))

    return hash_


def get_table_name(study_name, kind, group_bys=None):
    if group_bys is None:
        cohort_name = "overall"
    else:
        cohort_name = "_".join(group_bys)
    return "{}.ab_metrics_{}_{}_{}".format(AB_METRICS_ARCHIVE_DATASET, cohort_name, kind, study_name)


def get_sql_selects(cols, trailing_comma=True):
    """Join a list of strings with comma"""
    if not cols:
        return ''
    else:
        return ','.join(cols) + (',' if trailing_comma else '')


def read_ab_metrics_archive(kind='cumulative', group_bys=None, min_user_per_group=100000, age_days=30, metric_filter=None):
    """Pull all of experimentation results from metrics archive with appropriate warnings"""
    if group_bys:
        group_list1, group_list2 = "\n,".join(group_bys), "_".join(group_bys)
    else:
        group_list1, group_list2 = "", "overall"
    if metric_filter:
        metric_filter_sql = "metric in (%s)" % ",".join(["'%s'" % e for e in metric_filter])
    else:
        metric_filter_sql = "1=1"
    if kind=='cumulative':
        extra_cols = ""
    else:
        extra_cols = "DATE_DIFF(DATE(M.exp_ds), PARSE_DATE('%Y%m%d', M.study_start_date), DAY) nth_day,\n"

    query = """
        SELECT
           E.*,
           {group_list1}
           metric,
           control_id, treatment_id,
           CONCAT(M.study_name, "-", control_id, "-", treatment_id) as exp_group,
           count_control, count_treatment,
           diff, pct_diff, p_value,
           pct_diff_hi, pct_diff_lo,
           {extra_cols}
           avg_control, avg_treatment,
           exp_ds
           -- sum_control, sum_treatment,
           -- sum_square_control, sum_square_treatment
        FROM
            `{project}.{dataset}.ab_metrics_{group_list2}_{kind}_*` M
        INNER JOIN (
            SELECT
                study_name, notebook_user, study_start_date,
                max(study_end_date) as study_end_date,
                max(notebook_time) as latest_run,
                DATE_DIFF(current_date, DATE(max(notebook_time)), DAY) age_days,
                count(*) as num_metrics
            FROM
                `{project}.{dataset}.ab_metrics_{group_list2}_{kind}_*`
            GROUP BY 1,2,3
            HAVING age_days <= {age_days}
            ORDER BY 1,2,3
        ) E
        ON
            M.study_name = E.study_name AND
            M.notebook_user = E.notebook_user AND
            M.notebook_time = E.latest_run
        WHERE
            count_control >= {min_user_per_group} AND
            count_treatment >= {min_user_per_group} AND
            {metric_filter_sql}

    """.format(group_list1=group_list1, group_list2=group_list2, kind=kind, min_user_per_group=min_user_per_group,
                dataset=AB_METRICS_ARCHIVE_DATASET, project=AB_METRICS_ARCHIVE_PROJECT, age_days=age_days,
                metric_filter_sql=metric_filter_sql,
                extra_cols=extra_cols)
    return pd.read_gbq(query, AB_METRICS_ARCHIVE_PROJECT, dialect="standard", verbose=False)


def read_study_from_ab_metrics_archive(report, kind='cumulative', group_bys=None):
    """Pull individual results from metrics archive with appropriate warnings"""
    from IPython.display import display, HTML
    
    table_name = get_table_name(report.study_name, kind, group_bys)
    query = "SELECT * FROM [%s:%s]" % (AB_METRICS_ARCHIVE_PROJECT, table_name)
    try:
        res = pd.read_gbq(query, project_id=AB_METRICS_ARCHIVE_PROJECT, verbose=False)
    except Exception as e:
        report.logger.info(str(e))
        report.logger.info("Error in reading from table %s!" % table_name)
        return None

    report.logger.info("Reading from table %s" % table_name)
    if res.notebook_user[0] != os.environ['USER']:
        display(HTML("<b> The results are run by someone else </b>"))
        display(res[['study_name', 'notebook_user',
                     'notebook_time']].head(1).transpose())
    if res.study_start_date[0] != report.study_start_date or res.study_end_date[0] != report.study_end_date:
        display(HTML("<b> The results are run for different time period </b>"))
        display(res[['study_name', 'study_start_date',
                     'study_end_date']].head(1).transpose())
    return res


def export_ab_metrics(report):
    """Upload AB metrics to BQ"""
    export_ab_metrics_for(report, 'cumulative')
    export_ab_metrics_for(report, 'daily')
    if report.has_user_breakdowns:
        for user_group_bys in report.user_group_by_list:
            export_ab_metrics_for(report, group_bys=user_group_bys)


def export_ab_metrics_for(report, kind='cumulative', group_bys=None):
    """Upload AB metrics to BQ"""
    if kind == 'cumulative':
        results = report._get_last_day(
            report.get_results('cumulative', group_bys)
        )
    elif kind == 'daily':
        results = report.get_results('daily', group_bys)
    else:
        raise ValueError("Wrong argument kind!")
        return
    if results is None or results.empty:
        report.logger.info("Empty results!")
        return

    results['study_name'] = report.study_name
    results['study_start_date'] = report.study_start_date
    results['study_end_date'] = report.study_end_date
    results['notebook_user'] = os.environ.get('USER', 'anon')
    results['notebook_time'] = datetime.now()

    try:
        table_name = get_table_name(report.study_name, kind, group_bys)
        report.logger.info("Exporting %s results to table %s (%d rows)" %
              (kind, table_name, len(results)))
        results.to_gbq(table_name, AB_METRICS_ARCHIVE_PROJECT, verbose=False, if_exists='replace', progress_bar=False)
    except Exception as e:
        report.logger.info(str(e))
        report.logger.info("[export_ab_metrics] Error in writing table %s" % table_name)
    return results


def add_days(current_date, delta_day):
    return (pd.to_datetime(current_date) + timedelta(days=delta_day)).strftime("%Y%m%d")
