from banjo.metric_monitor.metric_v2 import MetricMonitorMetric, MetricMonitorMetricTable, MetricMonitorReport
from banjo.metric_monitor.models_v2 import MetricMonitorPercChangeModel


# Threshold is based on the content type.
def get_perc_change_model(content_type):
    content_type_thresholds = {
        "ALL": 0.05,
        "SPOTLIGHT": 0.10,
        "NF_PREMIUM": 0.15,
        "NF_UGC": 0.15,
    }
    pecentage_threshold = content_type_thresholds[content_type]
    return MetricMonitorPercChangeModel(
        baseline_start_date_days_before=7,
        baseline_end_date_days_before=7,
        baseline_percentage_threshold=pecentage_threshold
    )


metric_name_mapping = {
    "story_view_time_hour": "story_view_time_second",
    "story_view": "story_view",
}

metric_names = ["story_view_time_hour", "story_view"]
regions = {"US": ["US"], "SA": ["SA"], "IN": ["IN"], "ROW": ["ROW"], "S11": ["REST_OF_S11", "GB"]}
os_types = ["ALL", "iOS", "Android"]
content_types = ["ALL", "NF_UGC", "NF_PREMIUM", "SPOTLIGHT"]

sql_template = """
SELECT
    CONCAT("20", _TABLE_SUFFIX) ds,
    {metric_definitions},    
from `sc-analytics.report_spotlight.mixed_feed_metrics_os_region_product_level_20*`
where CONCAT("20", _TABLE_SUFFIX) BETWEEN '{{start_date}}' AND '{{end_date}}'
    and os_type IN {os_types}
    and region IN {regions}
    and content_type in {content_types}
    and feed_type='ALL'
    and content_type_detailed='ALL'
    and section='SF_SPOTLIGHT'
GROUP BY 1
""".format(
    metric_definitions=",".join(
        [
            f"""(SUM(IF({f'region in {tuple(regions[region])}' if len(regions[region]) > 1 else f"region = '{regions[region][0]}'"} and os_type = '{os_type}' and content_type = '{content_type}', {metric_name},0))) """
            f"AS {metric_name}_{region}_{os_type}_{content_type}\n"
            for metric_name in metric_names for region in regions for os_type in os_types for content_type in content_types
        ]
    ),
    regions=tuple([r for region in regions.values() for r in region]),
    os_types=tuple(os_types),
    content_types=tuple(content_types)
)

spotlight_metrics = []
for metric_name in metric_names:
    for region in regions:
        for os_type in os_types:
            for content_type in content_types:
                # Skip the metric if either content_type or os_type is 'ALL' but not both
                if (content_type == "ALL" and os_type != "ALL") or (os_type == "ALL" and content_type != "ALL"):
                    continue

                metric = MetricMonitorMetric(
                    name=f'{metric_name}_{region}_{os_type}_{content_type}',
                    models=[get_perc_change_model(content_type)],
                )
                spotlight_metrics.append(metric)

monitor_report = MetricMonitorReport(
    report_name="Mixed Feed Engagement Watcher",
    metric_tables=[
        MetricMonitorMetricTable(
            sql_template=sql_template,
            metrics=spotlight_metrics
        )
    ],
    email_alert_to=[
        "<EMAIL>",
        "<EMAIL>",
    ],
)
