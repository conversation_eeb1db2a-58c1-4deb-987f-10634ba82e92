"""
Snap Ad Type Metrics (Topsnap)

"""

from __future__ import division, print_function
from banjo.abtest.metric import (
    Metric, MetricTable, POSITIVE, NEGATIVE
)
import pandas as pd
import logging
import banjo.teams.monetization.ab_utils as ab_utils

logger = logging.getLogger(__name__)

__all__ = [
    "ad_delivery_v2",
    "ad_engagement_v2",
    "df_organic_engagement",
    "ad_delivery_tracks",
    "ad_engagement_tracks",
    "story_ad_delivery_tracks",
    "ad_delivery_blizzard",
    "organic_engagement",
    "chat_feed_ad_engagement"
    # "ewa_delivery_tracks"
]

######### Snap ad delivery metrics #########
def ad_delivery_tracks(start_date, end_date, breakdown='dc'):
    """
    Parameters
    ----------
    start_date
    end_date 

    Returns
    ----------
    mt: MetricTable

    Usage
    ----------
    >>> ad_delivery_tracks('20191107', '20191110')
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    if breakdown == 'dc':
        prefix = [
            'overall_',
            'auto_advance_',
            'shows_',
            'shows_commercial_',
            'shows_skippable_',
            'content_interstitial_',
            'publisher_stories_',
            # 'games_',
            'interstitial_spotlight_',
            'public_stories_',
            'chat_feed_',
        ]
        base_table = 'sc-analytics.report_adsnap.ab_metrics_quest_tracks_main_*'
    elif breakdown == 'vs':
        prefix = [
            'overall_',
            'df_',
            # 'pf_',
            'df_subscriptions_',
            'df_for_you_',
            'discover_feed_friends_',
            'feed_',
            # 'other_'
            'sfmf_',
        ]
        base_table = 'sc-analytics.report_adsnap.ab_metrics_quest_tracks_main_*'
    elif breakdown == 'at':
        prefix = [
            'three_v_',
            'remote_webpage_',
            'app_install_',
            'deep_link_attachment_',
            'collection_',
            'longform_video_',
            'ad_to_place_',
            'ad_to_message_',
            'ad_to_lens_',
            'ad_to_call_',
            'dpa_'
        ]
    elif breakdown == 'dcvs':
        prefix = [
            'sfmf_shows_',
        ]
        base_table = 'sc-analytics.report_adsnap.ab_metrics_quest_tracks_dc_vs_*'

    volume_metrics = [
        'ad_impression',
        'ad_gav_revenue',
        'ad_net_revenue',
        'ad_track',
    ]

    unique_volume_metrics = [
        'ad_impression'
    ]

    # sum_volume_metrics = {
    #     'ad_available_inventory': [
    #         'ad_impression',
    #         'ad_nofill'
    #     ]
    # }
    sum_volume_metrics = {}

    base_list = ['']

    # rate_metrics = {
    #     'ad_fill_rate':['ad_impression','ad_available_inventory'],
    # }
    rate_metrics = {}

    metric_final_list = []
    sql_final_subselect = ""

    for base in base_list:
        metric_final_list += ab_utils.create_metric_list_v2(prefix, volume_metrics, rate_metrics, base, sum_volume_metrics)
        metric_final_list += ab_utils.create_unique_metric_list(prefix, unique_volume_metrics, base)
        sql_final_subselect += ab_utils.create_sql_statement_v2(prefix, volume_metrics, rate_metrics, base, sum_volume_metrics)
        sql_final_subselect += ab_utils.create_unique_sql_statement(prefix, unique_volume_metrics, base)

    # print(sql_final_subselect)

    mt = MetricTable(
        sql="""
        SELECT
            timestamp(table_date) as ts,
            ghost_user_id,

            {sql_final_subselect}

        FROM `{base_table}`
        WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
        GROUP BY 1, 2
        """.format(
            base_table = base_table,
            start=start_date, 
            end=end_date,
            sql_final_subselect=sql_final_subselect
        ),
        metrics=metric_final_list,
        inner_join_with_mapping=False,
        bq_dialect='standard',
        name="ad_delivery_tracks"
    )

    return mt

######### Snap ad organic metrics #########
def ad_delivery_blizzard(start_date, end_date, breakdown='dc'):
    """
    Parameters
    ----------
    start_date
    end_date 

    Returns
    ----------
    mt: MetricTable

    Usage
    ----------
    >>> ad_delivery_tracks('20191107', '20191110')
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    if breakdown == 'dc':
        prefix = [
            'overall_',
            'friends_story_',
            'creator_',
            'shows_',
            'publisher_',
            'spotlight_'
        ]
        base_table = 'sc-analytics.report_adsnap.rm_ad_load_general_*'
    elif breakdown == 'vs':
        prefix = [
            'overall_',
            '_4th_tab_',
            '_5th_tab_'
        ]
        base_table = 'sc-analytics.report_adsnap.rm_ad_load_general_*'
    # elif breakdown == 'at':
    #     prefix = [
    #         'three_v_',
    #         'remote_webpage_',
    #         'app_install_',
    #         'deep_link_attachment_',
    #         'collection_',
    #         'longform_video_',
    #         'ad_to_place_',
    #         'ad_to_message_',
    #         'ad_to_lens_',
    #         'ad_to_call_',
    #         'dpa_'
    #     ]

    volume_metrics = [
        'snaps_ad_load_num',
        'snaps_ad_load_denom',
        'ad_rate_ad_load_num',
        'ad_rate_ad_load_denom',        
    ]


    base_list = ['']

    rate_metrics = {
        'snaps_ad_load':['snaps_ad_load_num','snaps_ad_load_denom'],
        'ad_rate':['ad_rate_ad_load_num','ad_rate_ad_load_denom']
    }

    metric_final_list = []
    sql_final_subselect = ""

    for base in base_list:
        metric_final_list += ab_utils.create_metric_list_v2(prefix, volume_metrics, rate_metrics, base, {})
        sql_final_subselect += ab_utils.create_sql_statement_v2(prefix, volume_metrics, rate_metrics, base, {})

    # print(sql_final_subselect)

    mt = MetricTable(
        sql="""
        SELECT
            timestamp(ds) as ts,
            ghost_user_id,

            {sql_final_subselect}

        FROM `{base_table}`
        WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
        GROUP BY 1, 2
        """.format(
            base_table = base_table,
            start=start_date, 
            end=end_date,
            sql_final_subselect=sql_final_subselect
        ),
        metrics=metric_final_list,
        inner_join_with_mapping=False,
        bq_dialect='standard',
        name="ad_delivery_blizzard"
    )

    return mt

######### Snap and Story ad combined metrics #########
def snap_ad_and_story_ad_metrics(start_date, end_date, breakdown='dc'):
    """
    Parameters
    ----------
    start_date
    end_date 

    Returns
    ----------
    mt: MetricTable

    Usage
    ----------
    >>> ad_delivery_tracks('20191107', '20191110')
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    if breakdown == 'dc':
        prefix = [
            'overall_',
            'auto_advance_',
            'shows_',
            # 'shows_commercial_',
            # 'shows_skippable_',
            'content_interstitial_',
            'publisher_stories_',
            'interstitial_spotlight_',
            'public_stories_',
            'sfmf_',
            'chat_feed_'
        ]
        base_table_snap_ad = 'sc-analytics.report_adsnap.ab_metrics_quest_tracks_main_*'
        base_table_story_ad = 'sc-analytics.report_adsnap.ab_metrics_quest_tracks_story_ad_main_*'
        quality_table = 'sc-analytics.report_adsnap.user_ad_click_quality_scores_*'
        deep_clicks = 'sc-analytics.report_adsnap.user_ad_deep_clicks_*'
    elif breakdown == 'vs':
        prefix = [
            'overall_',
            'df_',
            # 'pf_',
            'df_subscriptions_',
            'df_for_you_',
            'discover_feed_friends_',
            'feed_',
            # 'other_'
            'sfmf_',
        ]
        base_table_snap_ad = 'sc-analytics.report_adsnap.ab_metrics_quest_tracks_main_*'
        base_table_story_ad = 'sc-analytics.report_adsnap.ab_metrics_quest_tracks_story_ad_main_*'
        quality_table = 'sc-analytics.report_adsnap.user_ad_click_quality_scores_*'
        deep_clicks = 'sc-analytics.report_adsnap.user_ad_deep_clicks_*'

    sum_volume_metrics_positive = {
        'snap_ad_and_story_ad_gav_revenue': [
        'ad_gav_revenue',
        'ad_story_gav_revenue'
        ],
        'snap_ad_and_story_ad_net_revenue': [
        'ad_net_revenue',
        'ad_story_net_revenue'
        ],
        'snap_ad_and_story_ad_available_inventory': [
        'ad_nofill',
        'ad_impression',
        'ad_story_paid_impression'
        ],
        'snap_ad_and_story_ad_impression': [
        'ad_impression',
        'ad_story_paid_impression'
        ],
        'snap_ad_and_story_clickable_ad_impression': [
        'ad_swipeable_impression',
        'ad_story_paid_impression'
        ],
        'snap_ad_and_story_ad_topsnap_view_time': [
        'ad_topsnap_view_time',
        'ad_story_topsnap_view_time'
        ],
        'snap_ad_and_story_ad_earned_impression': [
        'ad_impression',
        'ad_story_earned_impression'
        ],
        'snap_ad_and_story_ad_click_sko': [
            'ad_swipe_sko',
            'ad_story_click_sko'
        ],
        'snap_ad_and_story_ad_click_non_sko': [
            'ad_swipe_non_sko',
            'ad_story_click_non_sko'
        ],
        'snap_ad_and_story_ad_click': [
        'ad_swipe',
        'ad_story_click'
        ],
        'snap_ad_and_story_ad_click_swipe_up': [
        'ad_swipe_swipe_up',
        'ad_story_swipe_up'
        ],
        'snap_ad_and_story_ad_click_tap': [
        'ad_swipe_tap',
        'ad_story_tap'
        ],
        'snap_ad_and_story_ad_tap_card': [
            'ad_tap_card',
            'ad_story_tap_card'
        ],
        'snap_ad_and_story_ad_tap_tray': [
            'ad_tap_tray',
            'ad_story_tap_tray'
        ],
        'snap_ad_and_story_ad_reporting_total_clicks' : [
            'ad_reporting_total_clicks'
        ],
        'snap_ad_and_story_ad_reporting_deep_clicks': [
            'ad_reporting_deep_clicks'
        ],
        'snap_ad_and_story_ad_non_lqc': [
            'ad_swipe',
            'ad_story_click',
            '-ad_low_quality_click'
        ]
    }

    sum_volume_metrics_negative = {
        'snap_ad_and_story_ad_exit': [
        'ad_exit',
        'ad_story_exit'
        ],
        'snap_ad_and_story_ad_background': [
        'ad_background',
        'ad_story_background'
        ],
         'snap_ad_and_story_ad_low_quality_click' :
        [
            'ad_low_quality_click'
        ]
    }

    unique_volume_metrics = ['snap_ad_and_story_ad_impression'] ### used to construct active days / UU

    rate_metrics_positive = {
        'snap_ad_and_story_ad_fill_rate':['snap_ad_and_story_ad_impression','snap_ad_and_story_ad_available_inventory'],
        'snap_ad_and_story_ad_click_rate':['snap_ad_and_story_ad_click','snap_ad_and_story_clickable_ad_impression'],
        'snap_ad_and_story_ad_sko_click_rate':['snap_ad_and_story_ad_click_sko','snap_ad_and_story_clickable_ad_impression'],
        'snap_ad_and_story_ad_non_sko_click_rate':['snap_ad_and_story_ad_click_non_sko','snap_ad_and_story_clickable_ad_impression'],
        'snap_ad_and_story_ad_click_swipe_up_rate':['snap_ad_and_story_ad_click_swipe_up','snap_ad_and_story_clickable_ad_impression'],
        'snap_ad_and_story_ad_click_tap_rate':['snap_ad_and_story_ad_click_tap', 'snap_ad_and_story_clickable_ad_impression'],
        'snap_ad_and_story_ad_tap_card_rate': ['snap_ad_and_story_ad_tap_card', 'snap_ad_and_story_clickable_ad_impression'],
        'snap_ad_and_story_ad_tap_tray_rate': ['snap_ad_and_story_ad_tap_tray', 'snap_ad_and_story_clickable_ad_impression'],
        'snap_ad_and_story_ad_view_time_per_impression':['snap_ad_and_story_ad_topsnap_view_time','snap_ad_and_story_ad_earned_impression'],
        'snap_ad_and_story_ad_non_lqc_impression_rate': ['snap_ad_and_story_ad_non_lqc',  'snap_ad_and_story_ad_earned_impression'], #rate
        'snap_ad_and_story_ad_non_lqc_click_share': ['snap_ad_and_story_ad_non_lqc', 'snap_ad_and_story_ad_click'], #share
        'snap_ad_and_story_ad_deep_clicks_share': ['snap_ad_and_story_ad_reporting_deep_clicks','snap_ad_and_story_ad_reporting_total_clicks']
    }

    rate_metrics_negative = {
        'snap_ad_and_story_ad_exit_rate':['snap_ad_and_story_ad_exit','snap_ad_and_story_ad_earned_impression'],
        'snap_ad_and_story_ad_background_rate':['snap_ad_and_story_ad_background','snap_ad_and_story_ad_earned_impression'],
        'snap_ad_and_story_ad_lqc_impression_rate': ['snap_ad_and_story_ad_low_quality_click', 'snap_ad_and_story_ad_earned_impression'],
        'snap_ad_and_story_ad_lqc_click_share': ['snap_ad_and_story_ad_low_quality_click', 'snap_ad_and_story_ad_click'],
    }
    volume_metrics = []
    base = ''
    sum_volume_metrics = {**sum_volume_metrics_positive,**sum_volume_metrics_negative}
    rate_metrics = {**rate_metrics_positive, **rate_metrics_negative}
    metric_list = ab_utils.create_metric_list_v2(prefixes = prefix, volume_metrics = volume_metrics, rate_metrics = rate_metrics, base = base, sum_volume_metrics = sum_volume_metrics)
    sql_subselect = ab_utils.create_sql_statement_v2(prefixes = prefix, volume_metrics = volume_metrics, rate_metrics = rate_metrics, base = base, sum_volume_metrics = sum_volume_metrics)
    ### define active days metrics
    active_day_metric_list = ab_utils.create_unique_metric_list(prefixes = prefix, unique_metric_list = unique_volume_metrics, base=base)

    ### create sql subselect for active days metrics
    sql_subselect_active_days = """"""
    tmp_sql_line = """case when {metric} > 0 then 1 end AS {metric}_active_days,\n"""

    for prefix in prefix:
        for metric in unique_volume_metrics:
            sql_subselect_active_days += tmp_sql_line.format(metric = prefix+metric)

    mt = MetricTable(
        sql="""
        SELECT *,
            {sql_subselect_active_days}
        FROM (
        SELECT
            timestamp(table_date) as ts,
            ghost_user_id,

            {sql_subselect}

        FROM 
        (
        SELECT *
        FROM `{base_table_snap_ad}`
        WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
        ) snap_ads
        LEFT JOIN 
        (
        SELECT *
        FROM `{base_table_story_ad}`
        WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
        ) story_ads
        USING(table_date,ghost_user_id)
        LEFT JOIN 
            ( 
            SELECT lqclf.ghost_user_id, lqclf.table_date, * EXCEPT(ghost_user_id, table_date)
            FROM (
                SELECT * EXCEPT(app_version, os_type, device_model, device_cluster, app_variant, country, gender, age, inferred_age_bucket, ads_age) 
                FROM `{quality_table}` WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
                ) lqclf
            ) lqc
        USING(table_date,ghost_user_id)    
        LEFT JOIN (
            select  * EXCEPT(app_version, os_type, device_model, device_cluster, app_variant, country, gender, age, inferred_age_bucket, ads_age) 
            FROM  `{deep_clicks}`WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}' 
        ) dc
        USING(table_date,ghost_user_id)
        GROUP BY 1, 2
        )
        """.format(
            base_table_snap_ad = base_table_snap_ad,
            base_table_story_ad = base_table_story_ad,
            deep_clicks = deep_clicks,
            quality_table = quality_table,
            start=start_date, 
            end=end_date,
            sql_subselect=sql_subselect,
            sql_subselect_active_days = sql_subselect_active_days,
        ),
        metrics=metric_list + active_day_metric_list,
        inner_join_with_mapping=False,
        bq_dialect='standard',
        name="snap_ad_and_story_ad_metrics"
    )

    return mt

# def ad_engagement_v2(start_date, end_date):
#     """
#     Parameters
#     ----------
#     start_date
#     end_date 

#     Returns
#     ----------
#     mt: MetricTable

#     Usage
#     ----------
#     >>> ad_engagement_v2('20191107', '20191110')
#     """

#     start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
#     end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

#     source_table = """
#     TABLE_DATE_RANGE_STRICT(
#         [sc-bq-gcs-billingonly:revdatascience_dag.ab_user_count_metrics_cheetah_],
#         TIMESTAMP('{start}'),
#         TIMESTAMP('{end}')
#     )
#     """.format(
#         start=start_date,
#         end=end_date,
#     )

#     prefix = ['','userstory_','publisher_','shows_','contentinterstitial_']

#     volume_metrics = [
#         'view_count',
#         'exit_count',
#         'background_count',
#         'view_time',
#         'completion_count',
#         'swipeable_view_count',
#         'swipeable_swipe_count',
#         'pass_count'
#     ]

#     base_list = ['tracks_ad_']

#     rate_metrics = {
#         'swipe_rate':['swipeable_swipe_count','swipeable_view_count'],
#         'view_time_per_impression':['view_time','view_count'],
#         'exit_rate':['exit_count','view_count'],
#         'completion_rate':['completion_count','view_count'],
#         'background_rate':['background_count','view_count'],
#         'pass_rate':['pass_count','view_count']
#     }

#     metric_final_list = []
#     sql_final_subselect = ""

#     for base in base_list:
#         metric_final_list += ab_utils.create_metric_list(prefix, volume_metrics, rate_metrics, base, False)
#         sql_final_subselect += ab_utils.create_sql_statement(prefix, volume_metrics, rate_metrics, base, False)

#     mt = MetricTable(
#         sql="""
#         SELECT
#             timestamp(table_date) as ts,
#             ghost_user_id,

#             {sql_final_subselect}

#         FROM {source_table}
#         GROUP EACH BY 1, 2
#         """.format(source_table=source_table, sql_final_subselect=sql_final_subselect),
#         metrics=metric_final_list,
#         name="ad_engagement_v2"
#     )

#     return mt

# def df_snap_organic_engagement(start_date, end_date):
#     """
#     Parameters
#     ----------
#     start_date
#     end_date 

#     Returns
#     ----------
#     mt: MetricTable

#     Usage
#     ----------
#     >>> df_snap_organic_engagement('20191107', '20191110')
#     """

#     start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
#     end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

#     source_table = """
#     TABLE_DATE_RANGE_STRICT(
#         [sc-bq-gcs-billingonly:revdatascience_dag.ab_user_count_metrics_cheetah_],
#         TIMESTAMP('{start}'),
#         TIMESTAMP('{end}')
#     )
#     """.format(
#         start=start_date,
#         end=end_date,
#     )

#     prefix = ['df_foryou_','df_subscriptions_','df_']

#     volume_metrics = [
#         'view_count',
#         'exit_count',
#         'background_count',
#         'view_time',
#         'view_completions',
#         'swipeable_view_count',
#         'swipeable_swipe_count'
#     ]

#     base = 'snap_'

#     rate_metrics = {
#         'swipe_rate':['swipeable_swipe_count','swipeable_view_count'],
#         'view_time_per_snap':['view_time','view_count'],
#         'exit_rate':['exit_count','view_count'],
#         'completion_rate':['view_completions','view_count'],
#         'background_rate':['background_count','view_count']
#     }

#     metric_final_list = []
#     sql_final_subselect = ""

#     sql_final_subselect += ab_utils.create_sql_statement(prefix, volume_metrics, rate_metrics, base, False)

#     prefix += ['df_foryousubs_']

#     metric_final_list += ab_utils.create_metric_list(prefix, volume_metrics, rate_metrics, base, False)
    

#     mt = MetricTable(
#         sql="""
#         SELECT
#             timestamp(table_date) as ts,
#             ghost_user_id,

#             SUM(df_foryou_snap_view_count + df_subscriptions_snap_view_count) AS df_foryousubs_snap_view_count,
#             SUM(df_foryou_snap_exit_count + df_subscriptions_snap_exit_count) AS df_foryousubs_snap_exit_count,
#             SUM(df_foryou_snap_background_count + df_subscriptions_snap_background_count) AS df_foryousubs_snap_background_count,
#             SUM(df_foryou_snap_view_time + df_subscriptions_snap_view_time) AS df_foryousubs_snap_view_time,
#             SUM(df_foryou_snap_view_completions + df_subscriptions_snap_view_completions) AS df_foryousubs_snap_view_completions,
#             SUM(df_foryou_snap_swipeable_view_count + df_subscriptions_snap_swipeable_view_count) AS df_foryousubs_snap_swipeable_view_count,
#             SUM(df_foryou_snap_swipeable_swipe_count + df_subscriptions_snap_swipeable_swipe_count) AS df_foryousubs_snap_swipeable_swipe_count,

#             {sql_final_subselect}

#         FROM {source_table}
#         GROUP EACH BY 1, 2
#         """.format(source_table=source_table, sql_final_subselect=sql_final_subselect),
#         metrics=metric_final_list,
#         name="df_snap_organic_engagement"
#     )

#     return mt

# def df_story_organic_engagement(start_date, end_date):
#     """
#     Parameters
#     ----------
#     start_date
#     end_date 

#     Returns
#     ----------
#     mt: MetricTable

#     Usage
#     ----------
#     >>> df_story_organic_engagement('20191107', '20191110')
#     """

#     start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
#     end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

#     source_table = """
#     TABLE_DATE_RANGE_STRICT(
#         [sc-bq-gcs-billingonly:revdatascience_dag.ab_user_count_metrics_cheetah_],
#         TIMESTAMP('{start}'),
#         TIMESTAMP('{end}')
#     )
#     """.format(
#         start=start_date,
#         end=end_date,
#     )

#     prefix = ['df_foryou_','df_subscriptions_','df_']

#     volume_metrics = [
#         'view_count',
#         'view_time'
#     ]

#     base = 'story_'

#     rate_metrics = {
#         'view_time_per_story':['view_time','view_count']
#     }

#     metric_final_list = []
#     sql_final_subselect = ""

#     sql_final_subselect += ab_utils.create_sql_statement(prefix, volume_metrics, rate_metrics, base, False)

#     prefix += ['df_foryousubs_']

#     metric_final_list += ab_utils.create_metric_list(prefix, volume_metrics, rate_metrics, base, False)
    

#     mt = MetricTable(
#         sql="""
#         SELECT
#             timestamp(table_date) as ts,
#             ghost_user_id,

#             SUM(df_foryou_story_view_count + df_subscriptions_story_view_count) AS df_foryousubs_story_view_count,
#             SUM(df_foryou_story_view_time + df_subscriptions_story_view_time) AS df_foryousubs_story_view_time,

#             {sql_final_subselect}

#         FROM {source_table}
#         GROUP EACH BY 1, 2
#         """.format(source_table=source_table, sql_final_subselect=sql_final_subselect),
#         metrics=metric_final_list,
#         name="df_story_organic_engagement"
#     )

#     return mt

######### Snap ad engagement metrics #########
def ad_engagement_tracks(start_date, end_date, breakdown='dc'):
    """
    Parameters
    ----------
    start_date
    end_date 

    Returns
    ----------
    mt: MetricTable

    Usage
    ----------
    >>> ad_engagement_tracks('20191107', '20191110')
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    if breakdown == 'dc':
        prefix = [
            'overall_',
            'auto_advance_',
            'shows_',
            'shows_commercial_',
            'shows_skippable_',
            'content_interstitial_',
            'publisher_stories_',
            # 'games_',
            'interstitial_spotlight_',
            'public_stories_',
            'chat_feed_'
       ]
        base_table = 'sc-analytics.report_adsnap.ab_metrics_quest_tracks_main_*'
        quality_table = 'sc-analytics.report_adsnap.user_ad_click_quality_scores_*'
        # quality_table_mid_funnel = 'sc-analytics.report_adsnap.user_ad_click_quality_bounce_scores_*'

    elif breakdown == 'vs':
        prefix = [
            'overall_',
            'df_',
            # 'pf_',
            'df_subscriptions_',
            'df_for_you_',
            'discover_feed_friends_',
            'feed_',
            # 'other_',
            'sfmf_',
        ]
        base_table = 'sc-analytics.report_adsnap.ab_metrics_quest_tracks_main_*'
        quality_table = 'sc-analytics.report_adsnap.user_ad_click_quality_scores_*'
        # quality_table_mid_funnel = 'sc-analytics.report_adsnap.user_ad_click_quality_bounce_scores_*'

    elif breakdown == 'at':
        prefix = [
            'three_v_',
            'remote_webpage_',
            'app_install_',
            'deep_link_attachment_',
            'collection_',
            'longform_video_',
            'ad_to_place_',
            'ad_to_message_',
            'ad_to_lens_',
            'ad_to_call_',
            'dpa_'
        ]

    ## for shows in mixed feed
    elif breakdown == 'dcvs':
        prefix = [
            'sfmf_shows_',
       ]
        base_table = 'sc-analytics.report_adsnap.ab_metrics_quest_tracks_dc_vs_*'
        quality_table = 'sc-analytics.report_adsnap.user_ad_click_quality_scores_*'
        # quality_table_mid_funnel = 'sc-analytics.report_adsnap.user_ad_click_quality_bounce_scores_*'

    volume_metrics = [
        'ad_impression',
        'ad_track',
        'ad_topsnap_view_time',
        'ad_swipeable_impression',
        'ad_completable_impression',
        'ad_pass',
        'ad_exit',
        'ad_deep_link_success',
        'ad_swipeable_track',
        'ad_background',
        'ad_completion',
        'ad_swipe',
        'ad_swipe_sko',
        'ad_swipe_non_sko',
        'ad_bottomsnap_impression',
        'ad_bottomsnap_view_time',
        'ad_swipe_swipe_up',
        'ad_swipe_tap',
        'ad_tap_card',
        'ad_tap_tray',
        'ad_track_3s_plus',
        'ad_landing_page_view',
        'ad_low_quality_click_snap_ads',
        # 'ad_low_quality_click_mid_funnel',
        'ad_reporting_total_clicks_snap_ads',
        'ad_reporting_deep_clicks_snap_ads',

        

        # 'ad_accidental_swipe',
        # 'ad_accidental_swipeable_impression'
    ]

    unique_volume_metrics = [
        ''
    ]

    sum_volume_metrics = {
        'ad_non_lqc_snap_ads': [
            'ad_swipe',
            '-ad_low_quality_click'
        ]
    }

    base_list = ['']

    rate_metrics = {
        'ad_swipe_rate':['ad_swipe','ad_swipeable_impression'],
        'ad_sko_swipe_rate':['ad_swipe_sko','ad_swipeable_impression'],
        'ad_non_sko_swipe_rate':['ad_swipe_non_sko','ad_swipeable_impression'],
        'ad_swipe_swipe_up_rate':['ad_swipe_swipe_up','ad_swipeable_impression'],
        'ad_swipe_tap_rate':['ad_swipe_tap','ad_swipeable_impression'],
        'ad_tap_card_rate': ['ad_tap_card', 'ad_swipeable_impression'],
        'ad_tap_tray_rate': ['ad_tap_tray', 'ad_swipeable_impression'],
        'ad_view_time_per_impression':['ad_topsnap_view_time','ad_impression'],
        'ad_exit_rate':['ad_exit','ad_impression'],
        'ad_completion_rate':['ad_completion','ad_completable_impression'],
        'background_rate':['ad_background','ad_impression'],
        'pass_rate':['ad_pass','ad_impression'],
        'ad_bottomsnap_view_time_per_impression':['ad_bottomsnap_view_time','ad_bottomsnap_impression'],
        'pct_of_impressions_3s_plus': ['ad_track_3s_plus', 'ad_track'],
        'ad_landing_page_view_rate': ['ad_landing_page_view','ad_swipe'],
        'ad_lqc_impression_rate_snap_ads': ['ad_low_quality_click_snap_ads', 'ad_impression'],
        'ad_lqc_click_share_snap_ads': ['ad_low_quality_click_snap_ads', 'ad_swipe'],
        # 'ad_lqc_impression_rate_mid_funnel': ['ad_low_quality_click_mid_funnel', 'ad_impression'],
        'ad_non_lqc_impression_rate_snap_ads': ['ad_non_lqc_snap_ads', 'ad_impression'], #rate
        'ad_non_lqc_click_share_snap_ads': ['ad_non_lqc_snap_ads', 'ad_swipe'], #share
        'ad_deep_clicks_share_snap_ads': ['ad_reporting_deep_clicks_snap_ads','ad_reporting_total_clicks_snap_ads']

        # 'ad_accidental_swipe_ratio': ['ad_accidental_swipe', 'ad_bottomsnap_impression'],
        # 'ad_accidental_swipe_rate': ['ad_accidental_swipe', 'ad_accidental_swipeable_impression']
    }

    metric_final_list = []
    sql_final_subselect = ""

    for base in base_list:
        metric_final_list += ab_utils.create_metric_list_v2(prefix, volume_metrics, rate_metrics, base, sum_volume_metrics)
        # metric_final_list += ab_utils.create_unique_metric_list(prefix, unique_volume_metrics, base)
        sql_final_subselect += ab_utils.create_sql_statement_v2(prefix, volume_metrics, rate_metrics, base, sum_volume_metrics)
        # sql_final_subselect += ab_utils.create_unique_sql_statement(prefix, unique_volume_metrics, base)

    # print(sql_final_subselect)
    mt = MetricTable(
        sql="""
        SELECT
            timestamp(st.table_date) as ts,
            st.ghost_user_id,

            {sql_final_subselect}

        FROM `{base_table}` st
        LEFT JOIN 
            ( 
            SELECT lqclf.ghost_user_id, lqclf.table_date, * EXCEPT(ghost_user_id, table_date)
            FROM (
                SELECT * EXCEPT(app_version, os_type, device_model, device_cluster, app_variant, country, gender, age, inferred_age_bucket, ads_age) 
                FROM `{quality_table}` WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
                ) lqclf
        ) lqc
        ON LOWER(st.ghost_user_id) = LOWER(lqc.ghost_user_id) 
            AND lqc.table_date = st.table_date
        LEFT JOIN (
            select  * EXCEPT(app_version, os_type, device_model, device_cluster, app_variant, country, gender, age, inferred_age_bucket, ads_age) 
            FROM  `sc-analytics.report_adsnap.user_ad_deep_clicks_*`WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}' 
        ) dc
            ON LOWER(st.ghost_user_id) = LOWER(dc.ghost_user_id) 
             AND dc.table_date = st.table_date
        WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
        GROUP BY 1, 2
        """.format(
            base_table = base_table,
            quality_table=quality_table,
            start=start_date, 
            end=end_date,
            sql_final_subselect=sql_final_subselect
        ),
        metrics=metric_final_list,
        inner_join_with_mapping=False,
        bq_dialect='standard',
        name="ad_engagement_tracks"
    )

    return mt

def story_ad_delivery_tracks(start_date, end_date, breakdown='dc'):
    """
    Parameters
    ----------
    start_date
    end_date 

    Returns
    ----------
    mt: MetricTable

    Usage
    ----------
    >>> ad_delivery_tracks('20191107', '20191110')
    """

    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    if breakdown == 'dc':
        prefix = [
            'overall_',
            'auto_advance_',
            'content_interstitial_',
            # 'promoted_story_',
            'publisher_stories_',
            'shows_',
            'interstitial_spotlight_',
            'public_stories_',
            'chat_feed_',
        ]
        base_table = 'sc-analytics.report_adsnap.ab_metrics_quest_tracks_story_ad_main_*'
    elif breakdown == 'vs':
        prefix = [
            'overall_',
            'df_subscriptions_',
            'df_for_you_',
            'discover_feed_friends_',
            'sfmf_',
        ]
        base_table = 'sc-analytics.report_adsnap.ab_metrics_quest_tracks_story_ad_main_*'
    volume_metrics = [
        'ad_story_click',
        'ad_story_click_sko',
        'ad_story_click_non_sko',
        'ad_story_swipe_up',
        'ad_story_tap',
        'ad_story_tap_card',
        'ad_story_tap_tray',
        'ad_story_earned_impression',
        'ad_story_background',
        'ad_story_earned_swipeable_impression',
        'ad_story_nofill',
        'ad_story_open',
        'ad_story_topsnap_view_time',
        'ad_story_gav_revenue',
        'ad_story_net_revenue',
        'ad_story_paid_impression',
        'ad_story_completion',
        'ad_story_exit',
        'ad_story_landing_page_view',
    ]

    unique_volume_metrics = [
        'ad_story_paid_impression',
        'ad_story_earned_impression',
    ]

    sum_volume_metrics = {
        'ad_story_available_inventory': [
            'ad_story_paid_impression',
            'ad_story_nofill'
        ]
    }

    base_list = ['']

    rate_metrics = {
        'ad_story_fill_rate': ['ad_story_paid_impression','ad_story_available_inventory'],
        'ad_story_open_rate': ['ad_story_open', 'ad_story_paid_impression'],
        'ad_story_swipe_rate_earned': ['ad_story_swipe_up', 'ad_story_earned_swipeable_impression'],
        'ad_story_sko_click_rate_earned': ['ad_story_click_sko', 'ad_story_earned_swipeable_impression'],
        'ad_story_non_sko_click_rate_earned': ['ad_story_click_non_sko', 'ad_story_earned_swipeable_impression'],
        'ad_story_tap_earned': ['ad_story_tap', 'ad_story_earned_swipeable_impression'],
        'ad_story_tap_card_earned': ['ad_story_tap_card', 'ad_story_earned_swipeable_impression'],
        'ad_story_tap_tray_earned': ['ad_story_tap_tray', 'ad_story_earned_swipeable_impression'],
        'ad_story_swipe_rate_paid': ['ad_story_swipe_up', 'ad_story_paid_impression'],
        'ad_story_completion_rate': ['ad_story_completion', 'ad_story_open'],
        'ad_story_background_rate_open': ['ad_story_background', 'ad_story_open'],
        'ad_story_background_rate_paid': ['ad_story_background', 'ad_story_paid_impression'],
        'avg_viewtime_per_earned_impression': ['ad_story_topsnap_view_time', 'ad_story_earned_impression'],
        'avg_viewtime_per_story': ['ad_story_topsnap_view_time', 'ad_story_open'],
        'ad_story_exit_rate_earned': ['ad_story_exit', 'ad_story_earned_impression'],
        'ad_story_exit_rate_paid': ['ad_story_exit', 'ad_story_paid_impression'],
    }

    tile_volume_metrics = [
        'ad_story_swipe_up',
        'ad_story_earned_impression',
        'ad_story_background',
        'ad_story_earned_swipeable_impression',
        'ad_story_nofill',
        'ad_story_open',
        'ad_story_topsnap_view_time',
        'ad_story_gav_revenue',
        'ad_story_net_revenue',   
        'ad_story_paid_impression',
        'ad_story_completion',
        'ad_story_exit',
        'ad_story_tile_click',
        'ad_story_earned_impression_click'
    ]

    tile_rate_metrics = {
        'ad_story_fill_rate': ['ad_story_paid_impression','ad_story_available_inventory'],
        'ad_story_open_rate': ['ad_story_open', 'ad_story_paid_impression'],
        'ad_story_swipe_rate_earned': ['ad_story_earned_impression_click', 'ad_story_earned_swipeable_impression'],
        'ad_story_swipe_rate_paid': ['ad_story_swipe_up', 'ad_story_paid_impression'],
        'ad_story_completion_rate': ['ad_story_completion', 'ad_story_open'],
        'ad_story_background_rate_open': ['ad_story_background', 'ad_story_open'],
        'ad_story_background_rate_paid': ['ad_story_background', 'ad_story_paid_impression'],
        'avg_viewtime_per_earned_impression': ['ad_story_topsnap_view_time', 'ad_story_earned_impression'],
        'avg_viewtime_per_story': ['ad_story_topsnap_view_time', 'ad_story_open'],
        'ad_story_exit_rate_earned': ['ad_story_exit', 'ad_story_earned_impression'],
        'ad_story_exit_rate_paid': ['ad_story_exit', 'ad_story_paid_impression'],
        'ad_story_tile_click_rate': ['ad_story_tile_click', 'ad_story_paid_impression']

    }

    metric_final_list = []
    sql_final_subselect = ""

    for base in base_list:
        metric_final_list += ab_utils.create_metric_list_v2(prefix, volume_metrics, rate_metrics, base, sum_volume_metrics)
        metric_final_list += ab_utils.create_metric_list_v2(['promoted_story_'], tile_volume_metrics, tile_rate_metrics, base, sum_volume_metrics)
        metric_final_list += ab_utils.create_unique_metric_list(prefix + ['promoted_story_'], unique_volume_metrics, base)
        sql_final_subselect += ab_utils.create_sql_statement_v2(prefix, volume_metrics, rate_metrics, base, sum_volume_metrics)
        sql_final_subselect += ab_utils.create_sql_statement_v2(['promoted_story_'], tile_volume_metrics, tile_rate_metrics, base, sum_volume_metrics)
        sql_final_subselect += ab_utils.create_unique_sql_statement(prefix + ['promoted_story_'], unique_volume_metrics, base)

    # print(sql_final_subselect)

    mt = MetricTable(
        sql="""
        SELECT
            timestamp(table_date) as ts,
            ghost_user_id,

            {sql_final_subselect}

        FROM `{base_table}`
        WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
        GROUP BY 1, 2
        """.format(
            base_table = base_table,
            start=start_date, 
            end=end_date,
            sql_final_subselect=sql_final_subselect
        ),
        metrics=metric_final_list,
        inner_join_with_mapping=False,
        bq_dialect='standard',
        name="story_ad_delivery_tracks"
    )

    return mt

def ewa_delivery_tracks(start_date, end_date):
    # to update breakdown = dc/vs when view source if fixed
    """
    Parameters
    ----------
    start_date
    end_date 

    Returns
    ----------
    mt: MetricTable

    Usage
    ----------
    >>> ad_delivery_tracks('20191107', '20191110')
    """

    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    # if breakdown == 'dc':
    prefix = [
        'ewa_',
        'ewa_snapkit_',
        'ewa_discover_',
    ]
    base_table = 'sc-analytics.report_adsnap.ab_metrics_quest_tracks_ad_network_main_*'

    volume_metrics = [
        'ad_network_impression',
        'ad_network_topsnap_view_time',
        'ad_network_swipe',
        'ad_network_gav_revenue',
        'ad_network_nofill',
    ]

    unique_volume_metrics = [
        'ad_network_impression'
    ]

    sum_volume_metrics = {
        'ad_network_available_inventory': [
            'ad_network_impression',
            'ad_network_nofill'
        ]
    }

    base_list = ['']

    rate_metrics = {
        'ad_network_fill_rate': ['ad_network_impression','ad_network_available_inventory'],
        'ad_network_swipe_rate': ['ad_network_swipe', 'ad_network_impression'],
        'ad_network_view_time_per_impression':['ad_network_topsnap_view_time','ad_network_impression']
    }

    metric_final_list = []
    sql_final_subselect = ""

    for base in base_list:
        metric_final_list += ab_utils.create_metric_list_v2(prefix, volume_metrics, rate_metrics, base, sum_volume_metrics)
        metric_final_list += ab_utils.create_unique_metric_list(prefix, unique_volume_metrics, base)
        sql_final_subselect += ab_utils.create_sql_statement_v2(prefix, volume_metrics, rate_metrics, base, sum_volume_metrics)
        sql_final_subselect += ab_utils.create_unique_sql_statement(prefix, unique_volume_metrics, base)

    # print(sql_final_subselect)

    mt = MetricTable(
        sql="""
        SELECT
            timestamp(table_date) as ts,
            ghost_user_id,

            {sql_final_subselect}

        FROM `{base_table}`
        WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
        GROUP BY 1, 2
        """.format(
            base_table = base_table,
            start=start_date, 
            end=end_date,
            sql_final_subselect=sql_final_subselect
        ),
        metrics=metric_final_list,
        inner_join_with_mapping=False,
        bq_dialect='standard',
        name="ewa_delivery_tracks"
    )

    return mt


def organic_engagement(start_date, end_date, breakdown='dc'):
    """
    Parameters
    ----------
    start_date
    end_date 
    breakdown

    Returns
    ----------
    mt: MetricTable

    Usage
    ----------
    >>> organic_engagement('20191107', '20191110')
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    if breakdown == 'dc':
        prefix = [
            'overall_',
            'auto_advance_',
            'shows_',
            'shows_commercial_eligible_',
            'publisher_stories_',
            'sf_spotlight_',
            'maps_'
            # 'premium_content_'
        ]
        base_table = 'sc-analytics.report_adsnap.ab_metrics_blizzard_main_*'
    elif breakdown == 'vs':
        prefix = [
            'overall_',
            'df_',
            # 'pf_',
            'df_subscriptions_',
            'df_for_you_',
            'df_friends_',
            'feed_',
            # 'other_'
            # 'sfmf_',
        ]
        base_table = 'sc-analytics.report_adsnap.ab_metrics_blizzard_main_*'


    volume_metrics = [
        'snap_view_count',
        'snap_view_time',
        'story_view_count',
        'story_view_time',
    ]

    sum_volume_metrics = {}

    base_list = ['']

    rate_metrics = {
        'avg_organic_viewtime_per_story': ['story_view_time','story_view_count'],
        'avg_organic_viewtime_per_snap': ['snap_view_time','snap_view_count']
    }

    metric_final_list = []
    sql_final_subselect = ""

    for base in base_list:
        metric_final_list += ab_utils.create_metric_list_v2(prefix, volume_metrics, rate_metrics, base, sum_volume_metrics)
        sql_final_subselect += ab_utils.create_sql_statement_v2(prefix, volume_metrics, rate_metrics, base, sum_volume_metrics)

    # print(sql_final_subselect)

    mt = MetricTable(
        sql="""
        SELECT
            timestamp(table_date) as ts,
            ghost_user_id,

            {sql_final_subselect}

        FROM `{base_table}`
        WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
        GROUP BY 1, 2
        """.format(
            base_table = base_table,
            start=start_date, 
            end=end_date,
            sql_final_subselect=sql_final_subselect
        ),
        metrics=metric_final_list,
        inner_join_with_mapping=False,
        bq_dialect='standard',
        name="organic_engagement"
    )

    return mt

def chat_feed_ad_engagement(start_date, end_date, breakdown = 'dc'):
    prefix = ''
    if breakdown == 'dc':
        prefix = [
            'chat_feed_',
        ]
    base_table = 'sc-analytics.report_adsnap.ab_metrics_quest_tracks_main_*'

    volume_metrics = [
        'ad_impression',
        'ad_swipeable_impression',
        'ad_topsnap_view_time',
        'ad_background',
        'ad_completion',
        'ad_swipe',
        'ad_bottomsnap_impression',
        'ad_bottomsnap_view_time',
        'ad_landing_page_view',
        'topsnap_cta_tap',
        'chat_cell_cta_tap',
        'sponsored_snap_open',
    ]

    sum_volume_metrics = {}

    base_list = ['']

    rate_metrics = {
        'ad_swipe_rate':['ad_swipe','ad_swipeable_impression'],
        'ad_view_time_per_impression':['ad_topsnap_view_time','ad_impression'],
        'background_rate':['ad_background','ad_impression'],
        'ad_bottomsnap_view_time_per_impression':['ad_bottomsnap_view_time','ad_bottomsnap_impression'],
        'ad_landing_page_view_rate': ['ad_landing_page_view','ad_swipe'],
        'sponsored_snap_open_rate': ['sponsored_snap_open','ad_impression'],
        'sponsored_snap_topsnap_click_rate': ['topsnap_cta_tap','sponsored_snap_open'],
        'topsnap_cta_tap_share': ['topsnap_cta_tap','ad_swipe'],
        'ad_click_post_open': ['topsnap_cta_tap','sponsored_snap_open']
    }


    metric_final_list = []
    sql_final_subselect = ""

    for base in base_list:
        metric_final_list += ab_utils.create_metric_list_v2(prefix, volume_metrics, rate_metrics, base, sum_volume_metrics)
        sql_final_subselect += ab_utils.create_sql_statement_v2(prefix, volume_metrics, rate_metrics, base, sum_volume_metrics)

    # print(sql_final_subselect)

    mt = MetricTable(
        sql="""
            SELECT
                timestamp(table_date) as ts,
                ghost_user_id,
    
                {sql_final_subselect}
    
            FROM `{base_table}`
            WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
            GROUP BY 1, 2
            """.format(
            base_table = base_table,
            start=start_date,
            end=end_date,
            sql_final_subselect=sql_final_subselect
        ),
        metrics=metric_final_list,
        inner_join_with_mapping=False,
        bq_dialect='standard',
        name="chat_feed_ad_engagement"
    )

    return mt