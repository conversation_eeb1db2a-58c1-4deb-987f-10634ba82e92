#will move to a banjo file
from banjo.abtest.metric import NEGATIVE
from banjo.abtest.report import (
    Metric, MetricTable, Report, get_quest_metric_table,
    get_abtest_console_metric_table)
from banjo import abtest, utils # see: go/pya

OPERA_METRICS_TEMPLATE = """
SELECT
    ghost_user_id,
    TIMESTAMP(CONCAT(SUBSTR(_TABLE_SUFFIX,1,4),'-',SUBSTR(_TABLE_SUFFIX,5,2),'-',SUBSTR(_TABLE_SUFFIX,7,2))) AS ts,
    {inner_metric_columns}
  FROM `sc-network.playback_metrics.playback_user_level_*`
  WHERE
          SAFE_CAST(SUBSTR(_TABLE_SUFFIX, 0, 8) AS INT64) > 0
          AND SUBSTR(_TABLE_SUFFIX, -8) BETWEEN '{start_date}'
          AND '{end_date}'
 group by 1,2

"""

OPERA_METRICS_TEMPLATE_ADS = """
SELECT
    ghost_user_id,
    TIMESTAMP(CONCAT(SUBSTR(_TABLE_SUFFIX,1,4),'-',SU<PERSON><PERSON>(_TABLE_SUFFIX,5,2),'-',SUBSTR(_TABLE_SUFFIX,7,2))) AS ts,
    {inner_metric_columns}
  FROM `sc-network.playback_metrics.playback_user_level_*`
  WHERE
          SAFE_CAST(SUBSTR(_TABLE_SUFFIX, 0, 8) AS INT64) > 0
          AND SUBSTR(_TABLE_SUFFIX, -8) BETWEEN '{start_date}'
          AND '{end_date}'
          AND item_type = 'AD_SNAP'
 group by 1,2

"""

OPERA_METRICS_TEMPLATE_NON_ADS = """
SELECT
    ghost_user_id,
    TIMESTAMP(CONCAT(SUBSTR(_TABLE_SUFFIX,1,4),'-',SUBSTR(_TABLE_SUFFIX,5,2),'-',SUBSTR(_TABLE_SUFFIX,7,2))) AS ts,
    {inner_metric_columns}
  FROM `sc-network.playback_metrics.playback_user_level_*`
  WHERE
          SAFE_CAST(SUBSTR(_TABLE_SUFFIX, 0, 8) AS INT64) > 0
          AND SUBSTR(_TABLE_SUFFIX, -8) BETWEEN '{start_date}'
          AND '{end_date}'
          AND item_type != 'AD_SNAP'
 group by 1,2

"""

OPERA_METRICS = [
    {'metric_name': 'US_bad_session_rate_north_star',
    'metric_type': 'ratio',
    'denominator': 'US_playback_sessions_count',
    'numerator': 'US_bad_sessions_count',
    'desired_direction': NEGATIVE
    },
    {'metric_name': 'bad_session_rate_500ms',
     'metric_type': 'ratio',
     'denominator': 'playback_sessions_count',
     'numerator': 'bad_sessions_500ms_count',
     'desired_direction': NEGATIVE
     },
    
    {'metric_name': 'US_playback_sessions_count',
     'sum_columns': 'num_playback_session',
     'if_filter': "country in ('US')"
    },
    {'metric_name': 'playback_sessions_count',
     'sum_columns': 'num_playback_session',
     'if_filter': ''},
    
    {'metric_name': 'opera_sessions_count',
     'sum_columns': 'num_opera_session',
     'if_filter': ''},
     
    {'metric_name': 'watch_time',
     'sum_columns': 'view_time_ms',
     'if_filter': ''},
    
    {'metric_name': 'US_bad_sessions_count',
     'sum_columns': 'bad_session_500',
     'if_filter': "country in ('US')",
     'desired_direction': NEGATIVE
     },
    {'metric_name': 'bad_sessions_500ms_count',
     'sum_columns': 'bad_session_500',
     'if_filter': '',
     'desired_direction': NEGATIVE
     },
  
    {'metric_name': 'bad_sessions_startup_500ms_count',
     'sum_columns': 'if_startup_latency_500',
     'if_filter': '',
     'desired_direction': NEGATIVE
     },

     
    {'metric_name': 'bad_session_rate_startup_500ms',
     'metric_type': 'ratio',
     'denominator': 'playback_sessions_count',
     'numerator': 'bad_sessions_startup_500ms_count',
     'desired_direction': NEGATIVE
    },
    {'metric_name': 'bad_sessions_mid_playback_500ms_count',
     'sum_columns': 'if_mid_stall_500',
     'if_filter': '',
     'desired_direction': NEGATIVE
     },
    {'metric_name': 'bad_session_rate_mid_playback_500ms',
     'metric_type': 'ratio',
     'denominator': 'playback_sessions_count',
     'numerator': 'bad_sessions_mid_playback_500ms_count',
     'desired_direction': NEGATIVE
    },
    
    
    {'metric_name': 'bad_sessions_1000ms_count',
     'sum_columns': 'bad_session_1000',
     'if_filter': '',
     'desired_direction': NEGATIVE
     },
    
    {'metric_name': 'bad_session_rate_1000ms',
     'metric_type': 'ratio',
     'denominator': 'playback_sessions_count',
     'numerator': 'bad_sessions_1000ms_count',
     'if_filter': '',
     'desired_direction': NEGATIVE
     },
    {'metric_name': 'bad_sessions_pos_0_1s_count',
     'sum_columns': 'bad_session_1000',
     'if_filter': 'snap_view_index=1',
     'desired_direction': NEGATIVE
    },
    
    {'metric_name': 'bad_session_rate_1000ms_pos_0',
     'metric_type': 'ratio',
     'denominator': 'opera_sessions_count',
     'numerator': 'bad_sessions_pos_0_1s_count',
     'if_filter': '',
     'desired_direction': NEGATIVE
     },
   
    {'metric_name': 'startup_latency',
     'sum_columns': 'wait_ms_capped_60',
     'if_filter': '',
     'desired_direction': NEGATIVE
     },
    {'metric_name': 'watch_time_plus_loading_time',
     'sum_columns': 'view_time_ms+total_stall_time',
     'if_filter': ''},

    {'metric_name': 'total_stall_time',
     'sum_columns': 'total_stall_time',
     'if_filter': '',
     'desired_direction': NEGATIVE},
    
    
    {'metric_name': 'pct_watch_time_spent_loading',
     'metric_type': 'ratio',
     'denominator': 'watch_time_plus_loading_time',
     'numerator': 'total_stall_time',
     'desired_direction': NEGATIVE
     },
    {'metric_name': 'total_stall_time_pos_0',
     'sum_columns': 'total_stall_time',
     'if_filter': 'snap_view_index=1',
     'desired_direction': NEGATIVE},
    
    {'metric_name': 'stall_duration_mid',
     'sum_columns': 'stall_time_mid',
     'if_filter': '',
     'desired_direction': NEGATIVE},
     
    {'metric_name': 'pct_watch_time_spent_loading_mid_stall',
     'metric_type': 'ratio',
     'denominator': 'watch_time_plus_loading_time',
     'numerator': 'stall_duration_mid',
     'desired_direction': NEGATIVE
    },
    
    {'metric_name': 'pct_watch_time_spent_loading_startup_latency',
     'metric_type': 'ratio',
     'denominator': 'watch_time_plus_loading_time',
     'numerator': 'startup_latency',
     'desired_direction': NEGATIVE
    },
    
    
    {'metric_name': 'all_abandonments',
     'sum_columns': '(all_abandonment_modified)',
     'if_filter': '',
     'desired_direction': NEGATIVE
     },
    {'metric_name': 'all_abandonments_ratio',
     'metric_type': 'ratio',
     'denominator': 'playback_sessions_count',
     'numerator': 'all_abandonments',
     'desired_direction': NEGATIVE
     },
    {'metric_name': 'start_abandonments',
     'sum_columns': '(start_abandonment-start_abandonment_fast_scroll)',
     'if_filter': '',
     'desired_direction': NEGATIVE
     },

    {'metric_name': 'start_abandonments_ratio',
     'metric_type': 'ratio',
     'denominator': 'playback_sessions_count',
     'numerator': 'start_abandonments',
     'desired_direction': NEGATIVE
     },
    {'metric_name': 'mid_abandonments',
     'sum_columns': '(all_abandonment - start_abandonment)',
     'if_filter': '',
     'desired_direction': NEGATIVE
     },

    {'metric_name': 'mid_abandonments_ratio',
     'metric_type': 'ratio',
     'denominator': 'playback_sessions_count',
     'numerator': 'mid_abandonments',
     'desired_direction': NEGATIVE
     },

    {'metric_name': 'prefetch',
     'sum_columns': 'prefetch',
    'if_filter': ''
    },
    {'metric_name': 'prefetch_ratio',
     'metric_type': 'ratio',
     'denominator': 'playback_sessions_count',
     'numerator': 'prefetch',
     },
     

    {'metric_name': 'stall_duration_metadata_not_ready',
     'sum_columns': 'stall_duration_metadata_not_ready',
     'if_filter': '',
     'desired_direction': NEGATIVE},

     {'metric_name': 'stall_duration_player_not_ready',
     'sum_columns': 'stall_duration_player_not_ready',
     'if_filter': '',
     'desired_direction': NEGATIVE},


     {'metric_name': 'stall_duration_data_starvation',
     'sum_columns': 'stall_duration_data_stravation',
     'if_filter': '',
     'desired_direction': NEGATIVE},

    {'metric_name': 'fast_scroll_abandonments',
     'sum_columns': 'start_abandonment_fast_scroll',
     'if_filter': '',
     'desired_direction': NEGATIVE
     },
    
    {'metric_name': 'fast_scroll_abandonments_ratio',
     'metric_type': 'ratio',
     'denominator': 'playback_sessions_count',
     'numerator': 'fast_scroll_abandonments',
     'desired_direction': NEGATIVE
    },
    {'metric_name': 'spinner_snap',
     'sum_columns': 'spinner_snap',
     'desired_direction': NEGATIVE,
     'if_filter': ''},
    
    {'metric_name': 'spinner_snap_ratio',
     'metric_type': 'ratio',
     'denominator': 'playback_sessions_count',
     'numerator': 'spinner_snap',
     'desired_direction': NEGATIVE},
    
    
    {'metric_name': 'spinner_duration_ms',
     'sum_columns': 'spinner_duration_ms',
     'desired_direction': NEGATIVE,
      'if_filter': ''},
    
     {'metric_name': 'spinner_duration_ratio',
     'metric_type': 'ratio',
     'denominator': 'watch_time',
     'numerator': 'spinner_duration_ms',
     'desired_direction': NEGATIVE},
    
     {'metric_name': 'spinner_count',
     'sum_columns': 'spinner_count',
     'desired_direction': NEGATIVE,
     'if_filter': ''},

    
    {'metric_name': 'session_length',
     'metric_type': 'ratio',
     'denominator': 'opera_sessions_count',
     'numerator': 'playback_sessions_count',
    },

   # {'metric_name': 'viewport_width_px',
    # 'sum_columns': 'viewport_width_px',
    # 'if_filter': ''
     #},

    {'metric_name': 'stalled_on_exit',
     'sum_columns': 'stalled_on_exit',
     'if_filter': '',
     'desired_direction': NEGATIVE},

    {'metric_name': 'pct_exits_on_stalled',
     'metric_type': 'ratio',
     'denominator': 'playback_sessions_count',
     'numerator': 'stalled_on_exit',
     'desired_direction': NEGATIVE},

     {'metric_name': 'zero_view_time_session',
     'sum_columns': 'view_time_zero_session',
      'if_filter': '',
     'desired_direction': NEGATIVE
     },
    
    {'metric_name': 'intent_to_first_abandonment',
     'sum_columns': 'start_zero_view_session',
     'if_filter': '',
     'desired_direction': NEGATIVE
    },
     {'metric_name': 'intent_to_next_abandonment',
     'sum_columns': 'start_zero_view_session_next',
     'if_filter': '',
     'desired_direction': NEGATIVE
    },
    
    {'metric_name': 'startup_latency_pos_0',
     'sum_columns': 'start_latency',
     'if_filter': 'snap_view_index=1',
     'desired_direction': NEGATIVE},

    {'metric_name': 'startup_latency_pos_1_plus',
    'sum_columns': 'next_latency',
    'if_filter': 'snap_view_index>1',
    'desired_direction': NEGATIVE},
    
    {'metric_name': 'longer_500ms_stall_count',
    'sum_columns': 'total_stall_count_500ms',
    'if_filter': '',
    'desired_direction': NEGATIVE
    },

    {'metric_name': '720P_sessions',
    'sum_columns': 'num_playback_session',
    'if_filter': "resolution_variant in ('720P','1080P')",
    },
    
    {'metric_name': '720P_penetration_rate',
     'metric_type': 'ratio',
     'denominator': 'playback_sessions_count',
     'numerator': '720P_sessions',
    },
]


playback_signals = [
    'US_playback_sessions_count',
    'US_bad_sessions_count',
    'US_bad_session_rate_north_star',
    'playback_sessions_count',
    'opera_sessions_count',
    'watch_time',
    'bad_sessions_500ms_count',
    'bad_session_rate_500ms',
    'bad_sessions_startup_500ms_count',
    'bad_session_rate_startup_500ms',
    'bad_sessions_mid_playback_500ms_count',
    'bad_session_rate_mid_playback_500ms',
    'bad_sessions_1000ms_count',
    'bad_session_rate_1000ms',
    'bad_sessions_pos_0_1s_count',
    'bad_session_rate_1000ms_pos_0',
    'watch_time_plus_loading_time',
    'pct_watch_time_spent_loading',
    'pct_watch_time_spent_loading_startup_latency',
    'pct_watch_time_spent_loading_mid_stall',
    'all_abandonments',
    'all_abandonments_ratio',
    'start_abandonments',
    'start_abandonments_ratio',
    'mid_abandonments',
    'mid_abandonments_ratio',
    'prefetch',
    'prefetch_ratio',
    'session_length',
    '720P_sessions',
    '720P_penetration_rate', 
    'total_stall_time',
    'startup_latency',
    'startup_latency_pos_0',
    'startup_latency_pos_1_plus',
    'stall_duration_mid',
    'longer_500ms_stall_count',
    'stall_duration_metadata_not_ready',
    'stall_duration_player_not_ready',
    'stall_duration_data_starvation',      
]
playback_signal_additional = [
 
   
    'spinner_snap',
    'spinner_snap_ratio',
    'spinner_duration_ms',
    #'spinner_duration_ratio',
    'spinner_count',
    'stalled_on_exit',
    'pct_exits_on_stalled',
    'zero_view_time_session',
    'intent_to_first_abandonment',
    'intent_to_next_abandonment',
    'fast_scroll_abandonments',
    'fast_scroll_abandonments_ratio',
    'total_stall_time_pos_0',
    ]
#]
playback_section = [
    'all_sources',
    'sf_spotlight',
    'df_subscriptions',
    'feed',
    'df_friends',
    'df_for_you',
    'memories',
    'memories_featured_stories',
    'chat',
    'profile_story',
    'nyc',
    'others',
]

OPERA_SECTION_SLICES = [
    {'slice': 'all_sources',
     'metric_filter': ''},
    {'slice': 'df_subscriptions',
     'metric_filter': '''
     (view_source IN ('DF_SUBSCRIPTIONS'))
     '''},
    {'slice': 'feed',
     'metric_filter': '''
     (view_source IN ('FEED'))
     '''},
    {'slice': 'df_friends',
     'metric_filter': '''
     (view_source IN ('DF_FRIENDS','FF_EVERYWHERE'))
     '''},
    {'slice': 'sf_spotlight',
     'metric_filter': '''
     (view_source in ('SF_SPOTLIGHT','SF_SPOTLIGHT_MIXED_FEED'))
     '''},
    {'slice': 'df_for_you',
     'metric_filter': '''
     (view_source = 'DF_FOR_YOU')
     '''},
    {'slice': 'memories',
     'metric_filter': '''
     (view_source = 'MEMORIES')
     '''},
    {'slice': 'memories_featured_stories',
     'metric_filter': '''
     (view_source = 'MEMORIES_FEATURED_STORIES')
     '''},
    {'slice': 'chat',
     'metric_filter': '''
     (view_source = 'CHAT')
     '''},
    {'slice': 'profile_story',
     'metric_filter': '''
     (view_source = 'PROFILE_STORY')
     '''},
    {'slice': 'nyc',
     'metric_filter': '''
     (view_source = 'NYC')
     '''},
    {'slice': 'others',
     'metric_filter': '''
     (view_source not in  ('DF_SUBSCRIPTIONS','FEED','DF_FRIENDS','FF_EVERYWHERE','SF_SPOTLIGHT','SF_SPOTLIGHT_MIXED_FEED','DF_FOR_YOU','MEMORIES','MEMORIES_FEATURED_STORIES','CHAT','PROFILE_STORY','NYC'))
     '''},
]

playback_media = [
    'all_sources',
    'video',
    'image',
]

OPERA_MEDIA_SLICES = [
    {'slice': 'all_sources',
     'metric_filter': ''},
    {'slice': 'video',
     'metric_filter': '''
     (media_type IN ('VIDEO','VIDEO_NO_SOUND'))
     '''},
    {'slice': 'image',
     'metric_filter': '''
     (media_type IN ('IMAGE'))
     '''},
]

playback_item_type =[
    'all_sources',
    'ad_snap',
    'direct_snap',
    'local_media_snap',
    'discover_snap_show',
    'discover_snap_edition',
    'discover_snap_others',
    'story_snap_spotlight',
    'story_snap_friend',
    'story_snap_official',
    'story_snap_public',
    'story_snap_others',
    'NULL'
]

OPERA_ITEM_TYPE_SLICES =[
    {'slice': 'all_sources',
     'metric_filter': ''},
    {'slice': 'ad_snap',
     'metric_filter': '''
     (ITEM_TYPE IN ('AD_SNAP'))
     '''},
      {'slice': 'direct_snap',
     'metric_filter': '''
     (ITEM_TYPE IN ('DIRECT_SNAP'))
     '''},
      {'slice': 'local_media_snap',
     'metric_filter': '''
     (ITEM_TYPE IN ('LOCAL_MEDIA_SNAP'))
     '''},
    {'slice': 'discover_snap_show',
     'metric_filter': '''
     (ITEM_TYPE in ('DISCOVER_SNAP') and item_type_specific IN ('9','21'))
     '''},
    {'slice': 'discover_snap_edition',
     'metric_filter': '''
    (ITEM_TYPE in ('DISCOVER_SNAP') and item_type_feature is not null and item_type_specific NOT IN ('9','13','21','22'))
    '''},
    {'slice': 'discover_snap_others',
     'metric_filter': '''
    (ITEM_TYPE in ('DISCOVER_SNAP') and item_type_specific is null)
    '''},
    {'slice': 'story_snap_spotlight',
     'metric_filter': '''
     (ITEM_TYPE in ('STORY_SNAP') and item_type_feature IN ('COMMUNITY'))
     '''},
    {'slice': 'story_snap_friend',
     'metric_filter': '''
    (ITEM_TYPE in ('STORY_SNAP') and item_type_feature IN ('FRIEND'))
    '''},
    {'slice': 'story_snap_official',
     'metric_filter': '''
   (ITEM_TYPE in ('STORY_SNAP') and item_type_feature IN ('OFFICIAL'))
   '''},
    {'slice': 'story_snap_public',
     'metric_filter': '''
(ITEM_TYPE in ('STORY_SNAP') and item_type_feature IN ('PUBLIC'))
'''},
   {'slice': 'story_snap_others',
     'metric_filter': '''
(ITEM_TYPE in ('STORY_SNAP') and item_type_feature NOT IN ('COMMUNITY','FRIEND','PUBLIC','OFFICIAL'))
'''},
    {'slice': 'NULL',
     'metric_filter': '''
     (ITEM_TYPE is null)
     '''},
]

playback_mode =[
    'all_sources',
    'video_progressive_download',
    'video_non_streaming',
    'video_streaming_hls_or_dash',
    'image_playback_mode_not_applicable',
    'view_null_playback_mode',
]

OPERA_PLAYBACK_MODE_SLICES =[
    {'slice': 'all_sources',
     'metric_filter': ''},
    {'slice': 'video_progressive_download',
     'metric_filter': '''
     (MEDIA_TYPE in ('VIDEO','VIDEO_NO_SOUND') and PLAYBACK_MODE IN ('PROGRESSIVE_DOWNLOAD'))
     '''},
      {'slice': 'video_non_streaming',
     'metric_filter': '''
    (MEDIA_TYPE in ('VIDEO','VIDEO_NO_SOUND') and PLAYBACK_MODE IN ('NON_STREAMING'))
     '''},
      {'slice': 'video_streaming_hls_or_dash',
     'metric_filter': '''
     (MEDIA_TYPE in ('VIDEO','VIDEO_NO_SOUND') and PLAYBACK_MODE IN ('STREAMING_HLS','STREAMING_DASH'))
     '''},
      {'slice': 'image_playback_mode_not_applicable',
     'metric_filter': '''
     (MEDIA_TYPE IN ('IMAGE'))
     '''},
    {'slice': 'view_null_playback_mode',
     'metric_filter': '''
     (MEDIA_TYPE in ('VIDEO','VIDEO_NO_SOUND') and PLAYBACK_MODE is NULL)
     '''},
]
device_connectivity = [
 'mobile',
 'wifi',
 'others'
]

OPERA_DEVICE_CONNECTIVITY_SLICES = [
    {'slice': 'mobile',
     'metric_filter': '''
     (device_connectivity in ("MOBILE"))
     '''},
    {'slice': 'wifi',
     'metric_filter': '''
     (device_connectivity in ("WIFI"))
     '''},
    {'slice': 'others',
     'metric_filter': '''
     (device_connectivity not in ("WIFI","MOBILE"))
     '''},
]

snap_position = [
 'all_source_first_snaps',
 'all_source_subsequent_snaps',
 'SF_Spotlight_first_snaps',
 'SF_Spotlight_subsequent_snaps',
 'Friend_or_Mixed_Carousel_first_snaps',
 'Friend_or_Mixed_Carousel_subsequent_snaps',
]

OPERA_DEVICE_SNAP_POSITION = [
    {'slice': 'all_source_first_snaps',
     'metric_filter': '''
     (snap_view_index in (0,1))
     '''},
    {'slice': 'all_source_subsequent_snaps',
     'metric_filter': '''
     (snap_view_index >1)
     '''},
    {'slice': 'SF_Spotlight_first_snaps',
     'metric_filter': '''
     (snap_view_index in (0,1) and view_source in ('SF_SPOTLIGHT','SF_SPOTLIGHT_MIXED_FEED'))
     '''},
    {'slice': 'SF_Spotlight_subsequent_snaps',
     'metric_filter': '''
     (snap_view_index >1 and view_source in ('SF_SPOTLIGHT','SF_SPOTLIGHT_MIXED_FEED'))
     '''},
    {'slice': 'Friend_or_Mixed_Carousel_first_snaps',
     'metric_filter': '''
     (snap_view_index in (0,1) and view_source in ('DF_FRIENDS','FF_EVERYWHERE'))
     '''},
    {'slice': 'Friend_or_Mixed_Carousel_subsequent_snaps',
     'metric_filter': '''
     (snap_view_index >1 and view_source in ('DF_FRIENDS','FF_EVERYWHERE'))
     '''},
]

playback_bandwidth = [
    'all_groups',
    'above_16Mbitps',
    'between_4Mbitps_and_16Mbitps',
    'between_2Mbitps_and_4Mbitps',
    'under_2Mbitps',
    'Unknown',
]
    

OPERA_BANDWIDTH_SLICES = [
    {'slice': 'all_groups',
     'metric_filter': ''},
    {'slice': 'above_16Mbitps',
     'metric_filter': '''
     (connection_bandwidth_new in ('above_16Mbitps'))
     '''},
    {'slice': 'between_4Mbitps_and_16Mbitps',
     'metric_filter': '''
     (connection_bandwidth_new in ('between_4Mbitps_and_16Mbitps'))
     '''},
       {'slice': 'between_2Mbitps_and_4Mbitps',
     'metric_filter': '''
     (connection_bandwidth_new in ('between_2Mbitps_and_4Mbitps'))
     '''},
     
     {'slice': 'under_2Mbitps',
     'metric_filter': '''
     (connection_bandwidth_new in ('under_2Mbitps'))
     '''},
     {'slice': 'Unknown',
     'metric_filter': '''
     (connection_bandwidth_new in ('unknown'))
     '''},
]

playback_mode_bandwidth =[
    'video_progressive_download_under_2Mbitps',
    'video_progressive_download_above_2Mbitps',
    'video_non_streaming_under_2Mbitps',
    'video_non_streaming_above_2Mbitps',
    'video_streaming_hls_or_dash_under_2Mbitps',
    'video_streaming_hls_or_dash_above_2Mbitps'
]

OPERA_PLAYBACK_MODE_BANDWIDTH_SLICES =[
   
    {'slice': 'video_progressive_download_under_2Mbitps',
     'metric_filter': '''
     (MEDIA_TYPE in ('VIDEO','VIDEO_NO_SOUND') and PLAYBACK_MODE IN ('PROGRESSIVE_DOWNLOAD') and  (connection_bandwidth_new in ('under_2Mbitps')))
     '''},
       {'slice': 'video_progressive_download_above_2Mbitps',
     'metric_filter': '''
     (MEDIA_TYPE in ('VIDEO','VIDEO_NO_SOUND') and PLAYBACK_MODE IN ('PROGRESSIVE_DOWNLOAD') and  (connection_bandwidth_new in ('between_2Mbitps_and_4Mbitps','between_4Mbitps_and_16Mbitps','above_16Mbitps')))
     '''},
      {'slice': 'video_non_streaming_under_2Mbitps',
     'metric_filter': '''
    (MEDIA_TYPE in ('VIDEO','VIDEO_NO_SOUND') and PLAYBACK_MODE IN ('NON_STREAMING') and  (connection_bandwidth_new in ('under_2Mbitps')))
     '''},
    {'slice': 'video_non_streaming_above_2Mbitps',
     'metric_filter': '''
    (MEDIA_TYPE in ('VIDEO','VIDEO_NO_SOUND') and PLAYBACK_MODE IN ('NON_STREAMING') and (connection_bandwidth_new in ('between_2Mbitps_and_4Mbitps','between_4Mbitps_and_16Mbitps','above_16Mbitps')))
     '''},
      {'slice': 'video_streaming_hls_or_dash_under_2Mbitps',
     'metric_filter': '''
     (MEDIA_TYPE in ('VIDEO','VIDEO_NO_SOUND') and PLAYBACK_MODE IN ('STREAMING_HLS','STREAMING_DASH') and  (connection_bandwidth_new in ('under_2Mbitps')))
     '''},
          {'slice': 'video_streaming_hls_or_dash_above_2Mbitps',
     'metric_filter': '''
     (MEDIA_TYPE in ('VIDEO','VIDEO_NO_SOUND') and PLAYBACK_MODE IN ('STREAMING_HLS','STREAMING_DASH') and  (connection_bandwidth_new in ('between_2Mbitps_and_4Mbitps','between_4Mbitps_and_16Mbitps','above_16Mbitps')))
     '''},
]

#Quantile
##will move to a banjo file
def get_quantile_metrics(
       # table,
       # event_names,
        metrics,
        start,
        end,
        ads_filter,
        metric_table_name=None,
        #table_suffix='performance_events'
):
    if ads_filter == ['ads_only']:
        print(ads_filter)
        sql = """
          SELECT
            TIMESTAMP(DATE(TIMESTAMP_ADD(event_time, interval -8 HOUR))) AS ts,
            ghost_user_id,
            {metrics_sql}
            FROM `sc-network.playback_metrics.playback_session_level_*`
          WHERE
              SAFE_CAST(SUBSTR(_TABLE_SUFFIX, 0, 8) AS INT64) > 0
              AND SUBSTR(_TABLE_SUFFIX, -8) BETWEEN '{start_date}'
              AND '{end_date}'
              AND item_type ='AD_SNAP'
        """.format(
        start_date=start,
        end_date=end,
        metrics_sql=",\n".join(
            "{} \n AS {}".format(metric_sql, metric_name)
            for metric_name, metric_sql in metrics.items())
        )
    elif ads_filter == ['non_ads_only']:
        print(ads_filter)
        sql = """
          SELECT
            TIMESTAMP(DATE(TIMESTAMP_ADD(event_time, interval -8 HOUR))) AS ts,
            ghost_user_id,
            {metrics_sql}
            FROM `sc-network.playback_metrics.playback_session_level_*`
          WHERE
              SAFE_CAST(SUBSTR(_TABLE_SUFFIX, 0, 8) AS INT64) > 0
              AND SUBSTR(_TABLE_SUFFIX, -8) BETWEEN '{start_date}'
              AND '{end_date}'
              AND item_type !='AD_SNAP'
        """.format(
        start_date=start,
        end_date=end,
        metrics_sql=",\n".join(
            "{} \n AS {}".format(metric_sql, metric_name)
            for metric_name, metric_sql in metrics.items())
    )
        #table=table,
        #table_suffix=table_suffix,
        #source_dataset=SOURCE_DATASET.get(table, "report_search")
    else:
        print("no filter")
        sql = """
          SELECT
            TIMESTAMP(DATE(TIMESTAMP_ADD(event_time, interval -8 HOUR))) AS ts,
            ghost_user_id,
            {metrics_sql}
            FROM `sc-network.playback_metrics.playback_session_level_*`
          WHERE
              SAFE_CAST(SUBSTR(_TABLE_SUFFIX, 0, 8) AS INT64) > 0
              AND SUBSTR(_TABLE_SUFFIX, -8) BETWEEN '{start_date}'
              AND '{end_date}'
             
        """.format(
        start_date=start,
        end_date=end,
        metrics_sql=",\n".join(
            "{} \n AS {}".format(metric_sql, metric_name)
            for metric_name, metric_sql in metrics.items())
    )
    
    return MetricTable(
            sql=sql,
            metrics=[
                Metric(col=metric_name, dist='quantile', desired_direction=abtest.metric.NEGATIVE)
                for metric_name in metrics.keys()],
            name=metric_table_name,
            quantile_metrics=True,
            bq_dialect='standard'
        )
quantile_metric_list_essential ={
"pct_watch_time_spent_loading": "pct_watch_time_spent_loading",
    #"SAFE_DIVIDE(total_stall_time,(total_stall_time + view_time_ms))",
"wait_ms_all": "wait_ms",
"wait_ms_pos_0": "CASE WHEN snap_view_index=1 then wait_ms else NULL END",
"wait_ms_pos_1_plus": "CASE WHEN snap_view_index>1 then wait_ms else NULL END",
"wait_ms_non_abandonment" :"CASE WHEN playback_load_phase in ('INTENT_TO_NEXT_DISPLAYED', 'INTENT_TO_FIRST_DISPLAYED') then wait_ms else NULL END",
"wait_ms_abandonment": "CASE WHEN playback_load_phase not in ('INTENT_TO_NEXT_DISPLAYED',  'INTENT_TO_FIRST_DISPLAYED') THEN wait_ms ELSE NULL END",

}
quantile_metric_list ={
            "Overall_start_latency": "CASE WHEN playback_load_phase in ('INTENT_TO_FIRST_DISPLAYED','INTENT_TO_FIRST_ABANDONED') THEN wait_ms ELSE NULL END",
            "Overall_next_latency": "CASE WHEN playback_load_phase in ('INTENT_TO_NEXT_DISPLAYED','INTENT_TO_NEXT_ABANDONED')THEN wait_ms ELSE NULL END",
            #media_type
            "Image_start_latency": "CASE WHEN playback_load_phase in ('INTENT_TO_FIRST_DISPLAYED','INTENT_TO_FIRST_ABANDONED')  and media_type = 'IMAGE' THEN wait_ms ELSE NULL END",
            "Image_next_latency": "CASE WHEN playback_load_phase in ('INTENT_TO_NEXT_DISPLAYED','INTENT_TO_NEXT_ABANDONED') and media_type = 'IMAGE' THEN wait_ms ELSE NULL END",
            "Video_start_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_FIRST_DISPLAYED','INTENT_TO_FIRST_ABANDONED')  and media_type in ('VIDEO','VIDEO_NO_SOUND') THEN wait_ms ELSE NULL END",
            "Video_next_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_NEXT_DISPLAYED','INTENT_TO_NEXT_ABANDONED') and media_type in ('VIDEO','VIDEO_NO_SOUND') THEN wait_ms ELSE NULL END",
            "Overall_cp0_intent_to_opera_ready_ms": "intent_to_opera_ready_ms",
            "Overall_cp1_opera_ready_to_page_ready_ms": "intent_to_page_opened_ms - intent_to_opera_ready_ms",
            "Overall_cp2_page_ready_to_min_disp_ms": "intent_to_media_minimally_displayed_ms - intent_to_page_opened_ms",
            "Overall_cp3_min_disp_to_fully_disp_ms": "wait_ms - intent_to_media_minimally_displayed_ms",
            "Image_cp0_intent_to_opera_ready_ms": "CASE WHEN media_type = 'IMAGE' THEN intent_to_opera_ready_ms else null end",
            "Image_cp1_opera_ready_to_page_ready_ms": "CASE WHEN media_type = 'IMAGE' THEN intent_to_page_opened_ms - intent_to_opera_ready_ms else null end",
            "Image_cp2_page_ready_to_min_disp_ms": "CASE WHEN media_type = 'IMAGE' THEN intent_to_media_minimally_displayed_ms - intent_to_page_opened_ms else null end",
            "Image_cp3_min_disp_to_fully_disp_ms": "CASE WHEN media_type = 'IMAGE' THEN wait_ms - intent_to_media_minimally_displayed_ms else null end",
            "Video_cp0_intent_to_opera_ready_ms": "CASE WHEN media_type in ('VIDEO','VIDEO_NO_SOUND') THEN intent_to_opera_ready_ms else null end",
            "Video_cp1_opera_ready_to_page_ready_ms": "CASE WHEN media_type in ('VIDEO','VIDEO_NO_SOUND')  THEN intent_to_page_opened_ms - intent_to_opera_ready_ms else null end",
            "Video_cp2_page_ready_to_min_disp_ms": "CASE WHEN media_type in ('VIDEO','VIDEO_NO_SOUND')  THEN intent_to_media_minimally_displayed_ms - intent_to_page_opened_ms else null end",
            "Video_cp3_min_disp_to_fully_disp_ms": "CASE WHEN media_type in ('VIDEO','VIDEO_NO_SOUND') THEN wait_ms - intent_to_media_minimally_displayed_ms else null end",
            #view_source
            "DF_SUBSCRIPTIONS_start_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_FIRST_DISPLAYED','INTENT_TO_FIRST_ABANDONED')  and view_source ='DF_SUBSCRIPTIONS' THEN wait_ms ELSE NULL END",
            "DF_SUBSCRIPTIONS_next_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_NEXT_DISPLAYED','INTENT_TO_NEXT_ABANDONED') and view_source ='DF_SUBSCRIPTIONS' THEN wait_ms ELSE NULL END",
            "DF_SUBSCRIPTIONS_cp0_intent_to_opera_ready_ms": "CASE WHEN view_source = 'DF_SUBSCRIPTIONS' THEN intent_to_opera_ready_ms else null end",
            "DF_SUBSCRIPTIONS_cp1_opera_ready_to_page_ready_ms": "CASE WHEN view_source = 'DF_SUBSCRIPTIONS' THEN intent_to_page_opened_ms - intent_to_opera_ready_ms else null end",
            "DF_SUBSCRIPTIONS_cp2_page_ready_to_min_disp_ms": "CASE WHEN view_source = 'DF_SUBSCRIPTIONS' THEN intent_to_media_minimally_displayed_ms - intent_to_page_opened_ms else null end",
            "DF_SUBSCRIPTIONS_cp3_min_disp_to_fully_disp_ms": "CASE WHEN view_source = 'DF_SUBSCRIPTIONS' THEN wait_ms - intent_to_media_minimally_displayed_ms else null end",
            "FEED_start_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_FIRST_DISPLAYED','INTENT_TO_FIRST_ABANDONED')  and view_source ='FEED' THEN wait_ms ELSE NULL END",
            "FEED_next_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_NEXT_DISPLAYED','INTENT_TO_NEXT_ABANDONED') and view_source ='FEED' THEN wait_ms ELSE NULL END",
            "DF_FRIENDS_start_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_FIRST_DISPLAYED','INTENT_TO_FIRST_ABANDONED')  and view_source ='DF_FRIENDS' THEN wait_ms ELSE NULL END",
            "DF_FRIENDS_next_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_NEXT_DISPLAYED','INTENT_TO_NEXT_ABANDONED') and view_source ='DF_FRIENDS' THEN wait_ms ELSE NULL END",
            "DF_FRIENDS_cp0_intent_to_opera_ready_ms": "CASE WHEN view_source = 'DF_FRIENDS' THEN intent_to_opera_ready_ms else null end",
            "DF_FRIENDS_cp1_opera_ready_to_page_ready_ms": "CASE WHEN view_source = 'DF_FRIENDS' THEN intent_to_page_opened_ms - intent_to_opera_ready_ms else null end",
            "DF_FRIENDS_cp2_page_ready_to_min_disp_ms": "CASE WHEN view_source = 'DF_FRIENDS' THEN intent_to_media_minimally_displayed_ms - intent_to_page_opened_ms else null end",
            "DF_FRIENDS_cp3_min_disp_to_fully_disp_ms": "CASE WHEN view_source = 'DF_FRIENDS' THEN wait_ms - intent_to_media_minimally_displayed_ms else null end",
            "SF_SPOTLIGHT_start_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_FIRST_DISPLAYED','INTENT_TO_FIRST_ABANDONED')  and view_source in ('SF_SPOTLIGHT','SF_SPOTLIGHT_MIXED_FEED') THEN wait_ms ELSE NULL END",
            "SF_SPOTLIGHT_next_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_NEXT_DISPLAYED','INTENT_TO_NEXT_ABANDONED') and view_source in ('SF_SPOTLIGHT','SF_SPOTLIGHT_MIXED_FEED')  THEN wait_ms ELSE NULL END",
            "SF_SPOTLIGHT_cp0_intent_to_opera_ready_ms": "CASE WHEN view_source in ('SF_SPOTLIGHT','SF_SPOTLIGHT_MIXED_FEED') THEN intent_to_opera_ready_ms else null end",
            "SF_SPOTLIGHT_cp1_opera_ready_to_page_ready_ms": "CASE WHEN view_source in ('SF_SPOTLIGHT','SF_SPOTLIGHT_MIXED_FEED') THEN intent_to_page_opened_ms - intent_to_opera_ready_ms else null end",
            "SF_SPOTLIGHT_cp2_page_ready_to_min_disp_ms": "CASE WHEN view_source in ('SF_SPOTLIGHT','SF_SPOTLIGHT_MIXED_FEED') THEN intent_to_media_minimally_displayed_ms - intent_to_page_opened_ms else null end",
            "SF_SPOTLIGHT_cp3_min_disp_to_fully_disp_ms": "CASE WHEN view_source in ('SF_SPOTLIGHT','SF_SPOTLIGHT_MIXED_FEED') THEN wait_ms - intent_to_media_minimally_displayed_ms else null end",
            "DF_FOR_YOU_start_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_FIRST_DISPLAYED','INTENT_TO_FIRST_ABANDONED')  and view_source ='DF_FOR_YOU' THEN wait_ms ELSE NULL END",
            "DF_FOR_YOU_next_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_NEXT_DISPLAYED','INTENT_TO_NEXT_ABANDONED') and view_source ='DF_FOR_YOU' THEN wait_ms ELSE NULL END",
            "DF_FOR_YOU_cp0_intent_to_opera_ready_ms": "CASE WHEN view_source = 'DF_FOR_YOU' THEN intent_to_opera_ready_ms else null end",
            "DF_FOR_YOU_cp1_opera_ready_to_page_ready_ms": "CASE WHEN view_source = 'DF_FOR_YOU' THEN intent_to_page_opened_ms - intent_to_opera_ready_ms else null end",
            "DF_FOR_YOU_cp2_page_ready_to_min_disp_ms": "CASE WHEN view_source = 'DF_FOR_YOU' THEN intent_to_media_minimally_displayed_ms - intent_to_page_opened_ms else null end",
            "DF_FOR_YOU_cp3_min_disp_to_fully_disp_ms": "CASE WHEN view_source = 'DF_FOR_YOU' THEN wait_ms - intent_to_media_minimally_displayed_ms else null end",
            "MEMORIES_start_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_FIRST_DISPLAYED','INTENT_TO_FIRST_ABANDONED')  and view_source ='MEMORIES' THEN wait_ms ELSE NULL END",
            "MEMORIES_next_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_NEXT_DISPLAYED','INTENT_TO_NEXT_ABANDONED') and view_source ='MEMORIES' THEN wait_ms ELSE NULL END",
            "MEMORIES_FEATURED_STORIES_start_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_FIRST_DISPLAYED','INTENT_TO_FIRST_ABANDONED')  and view_source ='MEMORIES_FEATURED_STORIES' THEN wait_ms ELSE NULL END",
            "MEMORIES_FEATURED_STORIES_next_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_NEXT_DISPLAYED','INTENT_TO_NEXT_ABANDONED') and view_source ='MEMORIES_FEATURED_STORIES' THEN wait_ms ELSE NULL END",
            "CHAT_start_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_FIRST_DISPLAYED','INTENT_TO_FIRST_ABANDONED') and view_source ='CHAT' THEN wait_ms ELSE NULL END",
            "CHAT_next_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_NEXT_DISPLAYED','INTENT_TO_NEXT_ABANDONED') and view_source ='CHAT' THEN wait_ms ELSE NULL END",
            "PROFILE_STORY_start_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_FIRST_DISPLAYED','INTENT_TO_FIRST_ABANDONED')  and view_source ='PROFILE_STORY' THEN wait_ms ELSE NULL END",
            "PROFILE_STORY_next_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_NEXT_DISPLAYED','INTENT_TO_NEXT_ABANDONED') and view_source ='PROFILE_STORY' THEN wait_ms ELSE NULL END",
            "NYC_start_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_FIRST_DISPLAYED','INTENT_TO_FIRST_ABANDONED')  and view_source ='NYC' THEN wait_ms ELSE NULL END",
            "NYC_next_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_NEXT_DISPLAYED','INTENT_TO_NEXT_ABANDONED') and view_source ='NYC' THEN wait_ms ELSE NULL END",
            "OTHERS_start_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_FIRST_DISPLAYED','INTENT_TO_FIRST_ABANDONED')  and view_source not in  ('DF_SUBSCRIPTIONS','FEED','DF_FRIENDS','SF_SPOTLIGHT','SF_SPOTLIGHT_MIXED_FEED','DF_FOR_YOU','MEMORIES','MEMORIES_FEATURED_STORIES','CHAT','PROFILE_STORY','NYC') THEN wait_ms ELSE NULL END",
            "OTHERS_next_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_NEXT_DISPLAYED','INTENT_TO_NEXT_ABANDONED') and view_source not in  ('DF_SUBSCRIPTIONS','FEED','DF_FRIENDS','SF_SPOTLIGHT','SF_SPOTLIGHT_MIXED_FEED','DF_FOR_YOU','MEMORIES','MEMORIES_FEATURED_STORIES','CHAT','PROFILE_STORY','NYC') THEN wait_ms ELSE NULL END",
            #bandwidth
            "Above_16M_start_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_FIRST_DISPLAYED','INTENT_TO_FIRST_ABANDONED')  and connection_playback_bandwidth_bps>16000000 THEN wait_ms ELSE NULL END",
            "Above_16M_next_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_NEXT_DISPLAYED','INTENT_TO_NEXT_ABANDONED') and connection_playback_bandwidth_bps>16000000  THEN wait_ms ELSE NULL END",
            "Between_4M_and_16M_start_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_FIRST_DISPLAYED','INTENT_TO_FIRST_ABANDONED')  and (connection_playback_bandwidth_bps between 4000000 and 16000000) THEN wait_ms ELSE NULL END",
            "Between_4M_and_16M_next_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_NEXT_DISPLAYED','INTENT_TO_NEXT_ABANDONED')  and (connection_playback_bandwidth_bps between 4000000 and 16000000)THEN wait_ms ELSE NULL END",
            "Under_4M_start_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_FIRST_DISPLAYED','INTENT_TO_FIRST_ABANDONED')  and (connection_playback_bandwidth_bps < 4000000) THEN wait_ms ELSE NULL END",
            "Under_4M_next_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_NEXT_DISPLAYED','INTENT_TO_NEXT_ABANDONED') and (connection_playback_bandwidth_bps < 4000000) THEN wait_ms ELSE NULL END",
            "OTHERS_start_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_FIRST_DISPLAYED','INTENT_TO_FIRST_ABANDONED')  and (connection_playback_bandwidth_bps is null) THEN wait_ms ELSE NULL END",
            "OTHERS_next_latency":"CASE WHEN playback_load_phase in ('INTENT_TO_NEXT_DISPLAYED','INTENT_TO_NEXT_ABANDONED') and (connection_playback_bandwidth_bps is null) THEN wait_ms ELSE NULL END",
}
