""" Friend Feed metrics
Contact: asavino@
"""

from __future__ import division, print_function
from banjo.abtest.metric import (
    Metric, MetricTable, abtest_metrics_sql_helper
)
from datetime import timedelta
import pandas as pd
import logging
from banjo.abtest.metric import POSITIVE, NEGATIVE
from collections import OrderedDict
from banjo import abtest
from banjo.abtest.report import (
    Metric, MetricTable
)
from banjo.teams.personalization.abtest import discover_feed_metrics as dfab
from pandas.api.types import CategoricalDtype

logger = logging.getLogger(__name__)

logger.warning("Friend Feed metrics imported.  Check the actual "
               "queries run to ensure correctness")

__all__ = [
    "friend_feed_ready_overstat_metrics",
    "friend_feed_ready_latency_metrics",
    "g2f_success_latency",
    "g2f_ratio_and_failure_metrics",
    "g2f_sub_latencies",
    "g2f_failure_latency",
    "g2f_metrics_notification",
    "g2f_failure_reason_breakdown",
    "chat_display_ready_latency_metrics",
    "chat_display_ready_failure_rate",
    "chat_display_ready_failure_reason_breakdown"
    ]


def g2f_success_latency(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
    `sc-analytics.report_messaging.app_startup_feed_ready_events_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    g2f_success_latency = Metric(
        'g2f_success_latency',
        'G2FF Success Latency',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    g2f_success_latency_cold = Metric(
        'g2f_success_latency_cold',
        'G2FF Cold Success Latency',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    g2f_success_latency_warm = Metric(
        'g2f_success_latency_warm',
        'G2FF Warm Success Latency',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    g2f_success_latency_hot = Metric(
        'g2f_success_latency_hot',
        'G2FF Hot Success Latency',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    g2f_success_latency_login = Metric(
        'g2f_success_latency_login',
        'G2FF Login Success Latency',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        ghost_user_id,
        IF(latency_millis BETWEEN 0 AND 3600000 AND with_result_successful = TRUE, latency_millis, NULL) 
         AS g2f_success_latency,
        IF(latency_millis BETWEEN 0 AND 3600000 AND with_result_successful = TRUE AND source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='COLD', latency_millis, NULL) 
         AS g2f_success_latency_cold,
        IF(latency_millis BETWEEN 0 AND 3600000 AND with_result_successful = TRUE AND source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='WARM', latency_millis, NULL) 
         AS g2f_success_latency_warm,
        IF(latency_millis BETWEEN 0 AND 3600000 AND with_result_successful = TRUE AND source IN ('SOURCE_WARM_START', 'warm_start') AND app_startup_type ='WARM', latency_millis, NULL) 
         AS g2f_success_latency_hot,
        IF(latency_millis BETWEEN 0 AND 3600000 AND with_result_successful = TRUE and lower(source)='login', latency_millis, NULL) AS g2f_success_latency_login
        FROM
          {source_table}
        """.format(source_table=source_table),
        metrics=[g2f_success_latency, 
                 g2f_success_latency_cold,
                 g2f_success_latency_warm,
                 g2f_success_latency_hot,
                 g2f_success_latency_login],
        name="g2f_success_latency",
        quantile_metrics=True,
        bq_dialect="standard"
    )
    return mt



def g2f_failure_latency(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
    `sc-analytics.report_messaging.app_startup_feed_ready_events_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    g2f_failure_latency = Metric(
        'g2f_failure_latency',
        'G2FF Failure Latency',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    g2f_failure_latency_cold = Metric(
        'g2f_failure_latency_cold',
        'G2FF Cold Failure Latency',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    g2f_failure_latency_warm = Metric(
        'g2f_failure_latency_warm',
        'G2FF Warm Failure Latency',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    g2f_failure_latency_hot = Metric(
        'g2f_failure_latency_hot',
        'G2FF Hot Failure Latency',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    g2f_user_bailed_latency = Metric(
        'g2f_user_bailed_latency',
        'G2FF User Bailed Latency',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    g2f_user_bailed_latency_cold = Metric(
        'g2f_user_bailed_latency_cold',
        'G2FF Cold User Bailed Latency',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    g2f_user_bailed_latency_warm = Metric(
        'g2f_user_bailed_latency_warm',
        'G2FF Warm User Bailed Latency',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    g2f_user_bailed_latency_hot = Metric(
        'g2f_user_bailed_latency_hot',
        'G2FF Hot User Bailed Latency',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        ghost_user_id,
        IF(latency_millis BETWEEN 0 AND 3600000 AND with_result_successful = FALSE, latency_millis, NULL) 
         AS g2f_failure_latency,
        IF(latency_millis BETWEEN 0 AND 3600000 AND with_result_successful = FALSE AND source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='COLD' AND (failure_reason !='user_bailed' AND arroyo_failure_reason!='user_bailed'), latency_millis, NULL) 
         AS g2f_failure_latency_cold,
        IF(latency_millis BETWEEN 0 AND 3600000 AND with_result_successful = FALSE AND source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='WARM' AND (failure_reason !='user_bailed' AND arroyo_failure_reason!='user_bailed'), latency_millis, NULL) 
         AS g2f_failure_latency_warm,
        IF(latency_millis BETWEEN 0 AND 3600000 AND with_result_successful = FALSE AND source IN ('SOURCE_WARM_START', 'warm_start') AND app_startup_type ='WARM' AND (failure_reason !='user_bailed' AND arroyo_failure_reason!='user_bailed'), latency_millis, NULL) 
         AS g2f_failure_latency_hot,
         IF(latency_millis BETWEEN 0 AND 3600000 AND with_result_successful = FALSE AND (failure_reason ='user_bailed' OR arroyo_failure_reason='user_bailed'), latency_millis, NULL) 
         AS g2f_user_bailed_latency,
        IF(latency_millis BETWEEN 0 AND 3600000 AND with_result_successful = FALSE AND source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='COLD' AND (failure_reason ='user_bailed' OR arroyo_failure_reason='user_bailed'), latency_millis, NULL) 
         AS g2f_user_bailed_latency_cold,
        IF(latency_millis BETWEEN 0 AND 3600000 AND with_result_successful = FALSE AND source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='WARM' AND (failure_reason ='user_bailed' OR arroyo_failure_reason='user_bailed'), latency_millis, NULL) 
         AS g2f_user_bailed_latency_warm,
        IF(latency_millis BETWEEN 0 AND 3600000 AND with_result_successful = FALSE AND source IN ('SOURCE_WARM_START', 'warm_start') AND app_startup_type ='WARM' AND (failure_reason ='user_bailed' OR arroyo_failure_reason='user_bailed'), latency_millis, NULL) 
         AS g2f_user_bailed_latency_hot
        FROM
          {source_table}
        """.format(source_table=source_table),
        metrics=[g2f_failure_latency, 
                 g2f_failure_latency_cold,
                 g2f_failure_latency_warm,
                 g2f_failure_latency_hot,
                g2f_user_bailed_latency,
                g2f_user_bailed_latency_cold, 
                g2f_user_bailed_latency_warm, 
                g2f_user_bailed_latency_hot],
        name="g2f_failure_latency",
        quantile_metrics=True,
        bq_dialect="standard"
    )
    return mt
                        


def g2f_metrics_notification(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
    `sc-analytics.report_messaging.app_startup_feed_ready_events_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    g2f_success_latency_with_notif = Metric(
        'g2f_success_latency_with_notif',
        'G2FF Success Latency with Notification',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    g2f_success_latency_cold_with_notif = Metric(
        'g2f_success_latency_cold_with_notif',
        'G2FF Cold Success Latency with Notification',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    g2f_success_latency_warm_with_notif = Metric(
        'g2f_success_latency_warm_with_notif',
        'G2FF Warm Success Latency with Notification',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    g2f_success_latency_hot_with_notif = Metric(
        'g2f_success_latency_hot_with_notif',
        'G2FF Hot Success Latency with Notification',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    g2f_success_latency_without_notif = Metric(
        'g2f_success_latency_without_notif',
        'G2FF Success Latency without Notification',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    g2f_success_latency_cold_without_notif = Metric(
        'g2f_success_latency_cold_without_notif',
        'G2FF Cold Success Latency without Notification',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    g2f_success_latency_warm_without_notif = Metric(
        'g2f_success_latency_warm_without_notif',
        'G2FF Warm Success Latency without Notification',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    g2f_success_latency_hot_without_notif = Metric(
        'g2f_success_latency_hot_without_notif',
        'G2FF Hot Success Latency without Notification',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        ghost_user_id,
        IF(latency_millis BETWEEN 0 AND 3600000 and with_triggered_by_notification = TRUE, latency_millis, NULL) 
         AS g2f_success_latency_with_notif,
        IF(latency_millis BETWEEN 0 AND 3600000 AND source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='COLD' and with_triggered_by_notification = TRUE, latency_millis, NULL) 
         AS g2f_success_latency_cold_with_notif,
        IF(latency_millis BETWEEN 0 AND 3600000 AND source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='WARM' and with_triggered_by_notification = TRUE, latency_millis, NULL) 
         AS g2f_success_latency_warm_with_notif,
        IF(latency_millis BETWEEN 0 AND 3600000 AND source IN ('SOURCE_WARM_START', 'warm_start') AND app_startup_type ='WARM' and with_triggered_by_notification = TRUE, latency_millis, NULL) 
         AS g2f_success_latency_hot_with_notif,
        IF(latency_millis BETWEEN 0 AND 3600000 and with_triggered_by_notification = FALSE, latency_millis, NULL) 
         AS g2f_success_latency_without_notif,
        IF(latency_millis BETWEEN 0 AND 3600000 AND source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='COLD' and with_triggered_by_notification = FALSE, latency_millis, NULL) 
         AS g2f_success_latency_cold_without_notif,
        IF(latency_millis BETWEEN 0 AND 3600000 AND source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='WARM' and with_triggered_by_notification = FALSE, latency_millis, NULL) 
         AS g2f_success_latency_warm_without_notif,
        IF(latency_millis BETWEEN 0 AND 3600000 AND source IN ('SOURCE_WARM_START', 'warm_start') AND app_startup_type ='WARM' and with_triggered_by_notification = FALSE, latency_millis, NULL) 
         AS g2f_success_latency_hot_without_notif
        FROM
          {source_table}
        """.format(source_table=source_table),
        metrics=[g2f_success_latency_with_notif,
                g2f_success_latency_cold_with_notif,
                g2f_success_latency_warm_with_notif,
                g2f_success_latency_hot_with_notif,
                g2f_success_latency_without_notif,
                g2f_success_latency_cold_without_notif,
                g2f_success_latency_warm_without_notif,
                g2f_success_latency_hot_without_notif],
        name="g2f_metrics_notification",
        quantile_metrics=True,
        bq_dialect="standard"
    )
    return mt    
    
    

    
def g2f_sub_latencies(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
    `sc-analytics.report_messaging.app_startup_feed_ready_events_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )
    sync_feed_cold=Metric(col="sync_feed_cold",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    sync_feed_warm=Metric(col="sync_feed_warm",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    sync_feed_hot=Metric(col="sync_feed_hot",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    wait_till_sync_feed_cold=Metric(col="wait_till_sync_feed_cold",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    wait_till_sync_feed_warm=Metric(col="wait_till_sync_feed_warm",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    wait_till_sync_feed_hot=Metric(col="wait_till_sync_feed_hot",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    ranking_cold=Metric(col="ranking_cold",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    ranking_warm=Metric(col="ranking_warm",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    ranking_hot=Metric(col="ranking_hot",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    process_individual_sources_cold=Metric(col="process_individual_sources_cold",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    process_individual_sources_warm=Metric(col="process_individual_sources_warm",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    process_individual_sources_hot=Metric(col="process_individual_sources_hot",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    propagate_change_to_ui_cold=Metric(col="propagate_change_to_ui_cold",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    propagate_change_to_ui_warm=Metric(col="propagate_change_to_ui_warm",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    propagate_change_to_ui_hot=Metric(col="propagate_change_to_ui_hot",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    process_sync_feed_response_cold=Metric(col="process_sync_feed_response_cold",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    process_sync_feed_response_warm=Metric(col="process_sync_feed_response_warm",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    process_sync_feed_response_hot=Metric(col="process_sync_feed_response_hot",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    ios_reload_table_cold=Metric(col="ios_reload_table_cold",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    ios_reload_table_warm=Metric(col="ios_reload_table_warm",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    ios_reload_table_hot=Metric(col="ios_reload_table_hot",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    ios_process_feed_items_cold=Metric(col="ios_process_feed_items_cold",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    ios_process_feed_items_warm=Metric(col="ios_process_feed_items_warm",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    ios_process_feed_items_hot=Metric(col="ios_process_feed_items_hot",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    android_wait_till_ff_sync_cold=Metric(col="android_wait_till_ff_sync_cold",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    android_wait_till_ff_sync_warm=Metric(col="android_wait_till_ff_sync_warm",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    android_wait_till_ff_sync_hot=Metric(col="android_wait_till_ff_sync_hot",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    android_begin_recording_cold=Metric(col="android_begin_recording_cold",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    android_begin_recording_warm=Metric(col="android_begin_recording_warm",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)
    android_begin_recording_hot=Metric(col="android_begin_recording_hot",dist='quantile',daily=True,cumulative=True,desired_direction=abtest.metric.NEGATIVE)

    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        ghost_user_id,
        IF(source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='COLD', sync_feed, NULL) AS sync_feed_cold,
        IF(source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='WARM', sync_feed, NULL) AS sync_feed_warm,
        IF(source IN ('SOURCE_WARM_START', 'warm_start') AND app_startup_type ='WARM', sync_feed, NULL) AS sync_feed_hot,
        IF(source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='COLD', wait_till_sync_feed, NULL) AS wait_till_sync_feed_cold,
        IF(source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='WARM', wait_till_sync_feed, NULL) AS wait_till_sync_feed_warm,
        IF(source IN ('SOURCE_WARM_START', 'warm_start') AND app_startup_type ='WARM', wait_till_sync_feed, NULL) AS wait_till_sync_feed_hot, 
        IF(source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='COLD', ranking, NULL) AS ranking_cold,
        IF(source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='WARM', ranking, NULL) AS ranking_warm,
        IF(source IN ('SOURCE_WARM_START', 'warm_start') AND app_startup_type ='WARM', ranking, NULL) AS ranking_hot,
        IF(source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='COLD', process_individual_sources, NULL) AS process_individual_sources_cold,
        IF(source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='WARM', process_individual_sources, NULL) AS process_individual_sources_warm,
        IF(source IN ('SOURCE_WARM_START', 'warm_start') AND app_startup_type ='WARM', process_individual_sources, NULL) AS process_individual_sources_hot,
        IF(source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='COLD', propagate_change_to_ui, NULL)  AS propagate_change_to_ui_cold,
        IF(source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='WARM', propagate_change_to_ui, NULL) AS propagate_change_to_ui_warm,
        IF(source IN ('SOURCE_WARM_START', 'warm_start') AND app_startup_type ='WARM', propagate_change_to_ui, NULL) AS propagate_change_to_ui_hot, 
        IF(source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='COLD', process_sync_feed_response, NULL) AS process_sync_feed_response_cold,
        IF(source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='WARM', process_sync_feed_response, NULL) AS process_sync_feed_response_warm,
        IF(source IN ('SOURCE_WARM_START', 'warm_start') AND app_startup_type ='WARM', process_sync_feed_response, NULL) AS process_sync_feed_response_hot,
        IF(source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='COLD', reload_table, NULL) AS ios_reload_table_cold,
        IF(source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='WARM', reload_table, NULL) AS ios_reload_table_warm,
        IF(source IN ('SOURCE_WARM_START', 'warm_start') AND app_startup_type ='WARM', reload_table, NULL) AS ios_reload_table_hot,
        IF(source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='COLD', process_feed_items, NULL)  AS ios_process_feed_items_cold,
        IF(source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='WARM', process_feed_items, NULL) AS ios_process_feed_items_warm,
        IF(source IN ('SOURCE_WARM_START', 'warm_start') AND app_startup_type ='WARM', process_feed_items, NULL) AS ios_process_feed_items_hot,
        IF(source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='COLD', wait_till_ff_sync, NULL) AS android_wait_till_ff_sync_cold,
        IF(source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='WARM', wait_till_ff_sync, NULL) AS android_wait_till_ff_sync_warm,
        IF(source IN ('SOURCE_WARM_START', 'warm_start') AND app_startup_type ='WARM', wait_till_ff_sync, NULL) AS android_wait_till_ff_sync_hot,
        IF(source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='COLD', begin_recording, NULL) AS android_begin_recording_cold,
        IF(source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='WARM', begin_recording, NULL) AS android_begin_recording_warm,
        IF(source IN ('SOURCE_WARM_START', 'warm_start') AND app_startup_type ='WARM', begin_recording, NULL) AS android_begin_recording_hot,

        FROM
          {source_table}
        """.format(source_table=source_table),
        metrics=[sync_feed_cold,
                sync_feed_warm,
                sync_feed_hot,
                wait_till_sync_feed_cold,
                wait_till_sync_feed_warm,
                wait_till_sync_feed_hot,
                ranking_cold,
                ranking_warm,
                ranking_hot,
                process_individual_sources_cold,
                process_individual_sources_warm,
                process_individual_sources_hot,
                propagate_change_to_ui_cold,
                propagate_change_to_ui_warm,
                propagate_change_to_ui_hot,
                process_sync_feed_response_cold,
                process_sync_feed_response_warm,
                process_sync_feed_response_hot,
                ios_reload_table_cold,
                ios_reload_table_warm,
                ios_reload_table_hot,
                ios_process_feed_items_cold,
                ios_process_feed_items_warm,
                ios_process_feed_items_hot,
                android_wait_till_ff_sync_cold,
                android_wait_till_ff_sync_warm,
                android_wait_till_ff_sync_hot,
                android_begin_recording_cold,
                android_begin_recording_warm,
                android_begin_recording_hot],
        name="g2f_sub_latencies",
        quantile_metrics=True,
        bq_dialect="standard"
    )
    return mt 




def friend_feed_ready_latency_metrics(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
    `sc-analytics.report_messaging.friend_feed_ready_events_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    first_feed_entry_time_ms = Metric(
        'first_feed_entry_time_ms',
        'FFR First Feed Entry Time',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    first_feed_entry_time_cold = Metric(
        'first_feed_entry_time_cold',
        'FFR First Feed Entry Time Cold',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    first_feed_entry_time_warm = Metric(
        'first_feed_entry_time_warm',
        'FFR First Feed Entry Time Warm',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    first_feed_render_time_ms = Metric(
        'first_feed_render_time_ms',
        'FFR First Feed Entry Render Time',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    first_feed_render_time_cold = Metric(
        'first_feed_render_time_cold',
        'FFR First Feed Entry Render Time Cold',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    first_feed_render_time_warm = Metric(
        'first_feed_render_time_warm',
        'FFR First Feed Entry Render Time Warm',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    sync_render_time_ms = Metric(
        'sync_render_time_ms',
        'FFR Sync Render Time',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    sync_render_time_cold = Metric(
        'sync_render_time_cold',
        'FFR Sync Render Time Cold',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    sync_render_time_warm = Metric(
        'sync_render_time_warm',
        'FFR Sync Render Time Warm',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    
    sync_feed_entry_time_ms = Metric(
        'sync_feed_entry_time_ms',
        'FFR Feed Entry Time after Sync',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    sync_feed_entry_time_cold = Metric(
        'sync_feed_entry_time_cold',
        'FFR Feed Entry Time after Sync Cold',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    sync_feed_entry_time_warm = Metric(
        'sync_feed_entry_time_warm',
        'FFR Feed Entry Time after Sync Warm',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    sync_time_ms = Metric(
        'sync_time_ms',
        'FFR Sync Latency',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    sync_time_cold = Metric(
        'sync_time_cold',
        'FFR Sync Latency Cold',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    sync_time_warm = Metric(
        'sync_time_warm',
        'FFR Sync Latency Warm',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
#     first_feed_entry_to_sync_latency_ms = Metric(
#         'first_feed_entry_to_sync_latency_ms',
#         'FFR First Feed Entry to Sync Latency',
#         dist='quantile',
#         daily=True,
#         cumulative=True,
#     )
    
#     first_feed_entry_to_sync_latency_cold = Metric(
#         'first_feed_entry_to_sync_latency_cold',
#         'FFR First Feed Entry to Sync Latency Cold',
#         dist='quantile',
#         daily=True,
#         cumulative=True,
#     )
    
#     first_feed_entry_to_sync_latency_warm = Metric(
#         'first_feed_entry_to_sync_latency_warm',
#         'FFR First Feed Entry to Sync Latency Cold',
#         dist='quantile',
#         daily=True,
#         cumulative=True,
#     )
    
    feed_entry_to_sync_latency = Metric(
        'feed_entry_to_sync_latency',
        'Friend Feed Entry to Sync Latency in Stale Feed Scenario (i.e. time spent waiting on feed for sync to complete)',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    feed_entry_to_sync_latency_cold = Metric(
        'feed_entry_to_sync_latency_cold',
        'Friend Feed Entry to Sync Latency Cold in Stale Feed Scenario',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    feed_entry_to_sync_latency_warm = Metric(
        'feed_entry_to_sync_latency_warm',
        'Friend Feed Entry to Sync Latency Warm in Stale Feed Scenario',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        ghost_user_id,
        first_feed_entry_time_ms,
        IF(app_startup_type ='COLD', first_feed_entry_time_ms, NULL) AS first_feed_entry_time_cold,
        IF(app_startup_type ='WARM', first_feed_entry_time_ms, NULL)  AS first_feed_entry_time_warm,
        sync_render_time_ms,
        IF(app_startup_type ='COLD', sync_render_time_ms, NULL)  AS sync_render_time_cold,
        IF(app_startup_type ='WARM', sync_render_time_ms, NULL)  AS sync_render_time_warm,
        first_feed_render_time_ms,
        IF(app_startup_type ='COLD', first_feed_render_time_ms, NULL)  AS first_feed_render_time_cold,
        IF(app_startup_type ='WARM', first_feed_render_time_ms, NULL) AS first_feed_render_time_warm,
        sync_feed_entry_time_ms,
        IF(app_startup_type ='COLD', sync_feed_entry_time_ms, NULL) AS sync_feed_entry_time_cold,
        IF(app_startup_type ='WARM', sync_feed_entry_time_ms, NULL) AS sync_feed_entry_time_warm,
        sync_time_ms,
        IF(app_startup_type ='COLD', sync_time_ms, NULL) AS sync_time_cold,
        IF(app_startup_type ='WARM', sync_time_ms, NULL) AS sync_time_warm,
        first_feed_entry_to_sync_latency_ms,
        IF(app_startup_type ='COLD', first_feed_entry_to_sync_latency_ms, NULL)  AS first_feed_entry_to_sync_latency_cold,
        IF(app_startup_type ='WARM', first_feed_entry_to_sync_latency_ms, NULL) AS first_feed_entry_to_sync_latency_warm,
        IF(sync_feed_entry_time_ms>0 AND first_feed_entry_to_sync_latency_ms>0 and sync_time_ms>sync_feed_entry_time_ms AND number_of_conversations_synced is not null and number_of_conversations_synced>0, first_feed_entry_to_sync_latency_ms, NULL) AS feed_entry_to_sync_latency,
        IF(app_startup_type ='COLD' AND sync_feed_entry_time_ms>0 AND first_feed_entry_to_sync_latency_ms>0 and sync_time_ms>sync_feed_entry_time_ms AND number_of_conversations_synced is not null and number_of_conversations_synced>0, first_feed_entry_to_sync_latency_ms, NULL) AS feed_entry_to_sync_latency_cold,
        IF(app_startup_type ='WARM' AND sync_feed_entry_time_ms>0 AND first_feed_entry_to_sync_latency_ms>0 and sync_time_ms>sync_feed_entry_time_ms AND number_of_conversations_synced is not null and number_of_conversations_synced>0, first_feed_entry_to_sync_latency_ms, NULL) AS feed_entry_to_sync_latency_warm
        FROM
          {source_table}
        """.format(source_table=source_table),
        metrics=[first_feed_entry_time_ms,
            first_feed_entry_time_cold,
            first_feed_entry_time_warm,
            first_feed_render_time_ms,
            first_feed_render_time_cold,
            first_feed_render_time_warm,
            sync_render_time_ms,
            sync_render_time_cold,
            sync_render_time_warm,
            sync_feed_entry_time_ms,
            sync_feed_entry_time_cold,
            sync_feed_entry_time_warm,
            sync_time_ms,
            sync_time_cold,
            sync_time_warm,
#             first_feed_entry_to_sync_latency_ms,
#             first_feed_entry_to_sync_latency_cold,
#             first_feed_entry_to_sync_latency_warm,
            feed_entry_to_sync_latency,
            feed_entry_to_sync_latency_cold,
            feed_entry_to_sync_latency_warm
                ],
        name="friend_feed_ready_latency_metrics",
        quantile_metrics=True,
        bq_dialect="standard"
    )
    return mt  



def g2f_failure_reason_breakdown(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    >>> call_metrics('20170501', '20170503')
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
    `sc-analytics.report_messaging.app_startup_feed_ready_events_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )
    
    unavailable = Metric(
        'unavailable',
        'Unavailable',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    duplicate_request = Metric(
        'duplicate_request',
        'Duplicate Request',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    unauthorized = Metric(
        'unauthorized',
        'Unauthorized',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    timeout = Metric(
        'timeout',
        'Timeout',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    internal_error = Metric(
        'internal_error',
        'Internal Error',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    invalid_operation = Metric(
        'invalid_operation',
        'Invalid Operation',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    cancelled = Metric(
        'cancelled',
        'Cancelled',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    database_error = Metric(
        'database_error',
        'Database Error',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    storage_full = Metric(
        'storage_full',
        'Storage Full',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    database_corrupt = Metric(
        'database_corrupt',
        'Database Corrupt',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    native_conversation_sync = Metric(
        'native_conversation_sync',
        'Native Conversation Sync',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    cannot_connect_to_host = Metric(
        'cannot_connect_to_host',
        'Cannot Connect to Host',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    bad_server_response = Metric(
        'bad_server_response',
        'Bad Server Response',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    cancelled_ufs = Metric(
        'cancelled_ufs',
        'Cancelled UFS',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    internet_disconnected = Metric(
        'internet_disconnected',
        'Internet Disconnected',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    connection_lost = Metric(
        'connection_lost',
        'Connection Lost',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    timeout_ufs = Metric(
        'timeout_ufs',
        'Timeout UFS',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    cannot_connect_to_internet = Metric(
        'cannot_connect_to_internet',
        'Cannot Connect to Internet',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    network_changed = Metric(
        'network_changed',
        'Network Changed',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    hostname_not_resolved = Metric(
        'hostname_not_resolved',
        'Hostname not Resolved',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    other = Metric(
        'other',
        'Other',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    quic_protocol_failed = Metric(
        'quic_protocol_failed',
        'Quic Protocol Failed',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    connection_closed = Metric(
        'connection_closed',
        'Connection Closed',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    timed_out = Metric(
        'timed_out',
        'Timed Out',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    connection_refused = Metric(
        'connection_refused',
        'Connection Refused',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    connection_reset = Metric(
        'connection_reset',
        'Connection Reset',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    address_unreachable = Metric(
        'address_unreachable',
        'Address Unreachable',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    client_certificate_rejected = Metric(
        'client_certificate_rejected',
        'Client Certificate Rejected',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    
    cannot_parse_response = Metric(
        'cannot_parse_response',
        'Cannot Parse Response',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    secure_connection_failed = Metric(
        'secure_connection_failed',
        'Secure Connection Failed',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    connection_timed_out = Metric(
        'connection_timed_out',
        'Connection Timed Out',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    cannot_find_host = Metric(
        'cannot_find_host',
        'Cannot Find Host',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    quic_crypto_symmetric_key_setup_failed = Metric(
        'quic_crypto_symmetric_key_setup_failed',
        'Quic Crypto Symmetric Key Setup Failed',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    calls_active = Metric(
        'calls_active',
        'Calls Active',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    international_roaming_off = Metric(
        'international_roaming_off',
        'International Roaming Off',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    data_not_allowed = Metric(
        'data_not_allowed',
        'Data Not Allowed',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    cannot_decode_raw_data = Metric(
        'cannot_decode_raw_data',
        'Cannot Decode Raw Data',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    https_proxy_connection_failure = Metric(
        'https_proxy_connection_failure',
        'http Proxy Connection Failure',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        ghost_user_id,
        SUM(IF(failure_reason ='Unavailable' or arroyo_failure_reason='Unavailable', 1, NULL))  AS unavailable,
        SUM(IF(failure_reason ='Duplicate request' or arroyo_failure_reason ='Duplicate request', 1, NULL))  AS duplicate_request,
        SUM(IF(failure_reason ='Unauthorized' or arroyo_failure_reason ='Unauthorized', 1, NULL))  AS unauthorized,
        SUM(IF(failure_reason ='Timeout' or arroyo_failure_reason ='Timeout', 1, NULL))  AS timeout,
        SUM(IF(failure_reason ='Internal error' or arroyo_failure_reason ='Internal error', 1, NULL))  AS internal_error,
        SUM(IF(failure_reason ='Invalid operation' or arroyo_failure_reason ='Invalid operation', 1, NULL))  AS invalid_operation,
        SUM(IF(failure_reason ='Canceled' or arroyo_failure_reason ='Canceled', 1, NULL))  AS cancelled,
        SUM(IF(failure_reason ='DatabaseError' or arroyo_failure_reason ='DatabaseError', 1, NULL))  AS database_error,
        SUM(IF(failure_reason ='StorageFull' or arroyo_failure_reason ='StorageFull', 1, NULL))  AS storage_full,
        SUM(IF(failure_reason ='DatabaseCorrupted' or arroyo_failure_reason ='DatabaseCorrupted', 1, NULL)) AS database_corrupt,
        SUM(IF(arroyo_failure_reason ='native_conversation_sync', 1, NULL)) AS native_conversation_sync,
        SUM(IF(failure_reason ='-1004' or arroyo_failure_reason ='-1004' , 1, null)) AS cannot_connect_to_host,
        SUM(IF(failure_reason ='-1011' or arroyo_failure_reason ='-1011', 1, null)) AS bad_server_response,
        SUM(IF(failure_reason ='-999' or arroyo_failure_reason ='-999', 1, null)) AS cancelled_ufs,
        SUM(IF(failure_reason ='2' or arroyo_failure_reason ='2', 1, null)) AS internet_disconnected,
        SUM(IF(failure_reason ='-1005' or arroyo_failure_reason ='-1005', 1, null)) AS connection_lost,
        SUM(IF(failure_reason ='-1001' or arroyo_failure_reason ='-1001', 1, null)) AS timeout_ufs,
        SUM(IF(failure_reason ='-1009' or arroyo_failure_reason ='-1009', 1, null)) AS cannot_connect_to_internet,
        SUM(IF(failure_reason ='3' or arroyo_failure_reason ='3', 1, null)) AS network_changed,
        SUM(IF(failure_reason ='1' or arroyo_failure_reason ='1', 1, null)) AS hostname_not_resolved,
        SUM(IF(failure_reason ='11' or arroyo_failure_reason ='11', 1, null)) AS other,
        SUM(IF(failure_reason ='10' or arroyo_failure_reason ='10', 1, null)) AS quic_protocol_failed,
        SUM(IF(failure_reason ='5' or arroyo_failure_reason ='5', 1, null)) AS connection_closed,
        SUM(IF(failure_reason ='4' or arroyo_failure_reason ='4', 1, null)) AS timed_out,
        SUM(IF(failure_reason ='7' or arroyo_failure_reason ='7', 1, null)) AS connection_refused,
        SUM(IF(failure_reason ='8' or arroyo_failure_reason ='8', 1, null)) AS connection_reset,
        SUM(IF(failure_reason ='9' or arroyo_failure_reason ='9', 1, null)) AS address_unreachable,
        SUM(IF(failure_reason ='-1206' or arroyo_failure_reason ='-1206', 1, null)) AS client_certificate_rejected,
        SUM(IF(failure_reason ='-1017' or arroyo_failure_reason ='-1017', 1, null)) AS cannot_parse_response,
        SUM(IF(failure_reason ='-1200' or arroyo_failure_reason ='-1200', 1, null)) AS secure_connection_failed,
        SUM(IF(failure_reason ='6' or arroyo_failure_reason ='6', 1, null)) AS connection_timed_out,
        SUM(IF(failure_reason ='-1003' or arroyo_failure_reason ='-1003', 1, null)) AS cannot_find_host,
        SUM(IF(failure_reason ='53' or arroyo_failure_reason ='53', 1, null)) AS quic_crypto_symmetric_key_setup_failed,
        SUM(IF(failure_reason ='-1019' or arroyo_failure_reason ='-1019', 1, null)) AS calls_active,
        SUM(IF(failure_reason ='-1018' or arroyo_failure_reason ='-1018', 1, null)) AS international_roaming_off,
        SUM(IF(failure_reason ='-1020' or arroyo_failure_reason ='-1020', 1, null)) AS data_not_allowed,
        SUM(IF(failure_reason ='-1015' or arroyo_failure_reason ='-1015', 1, null)) AS cannot_decode_raw_data,
        SUM(IF(failure_reason ='310' or arroyo_failure_reason ='310', 1, null)) AS https_proxy_connection_failure
        FROM
          {source_table}
        GROUP BY 1,2
        """.format(source_table=source_table),
        metrics=[unavailable,
                duplicate_request,
                unauthorized,
                timeout,
                internal_error,
                invalid_operation,
                cancelled,
                database_error,
                storage_full,
                database_corrupt,
                native_conversation_sync,
                cannot_connect_to_host,
                bad_server_response,
                cancelled_ufs,
                internet_disconnected,
                connection_lost,
                timeout_ufs,
                cannot_connect_to_internet,
                network_changed,
                hostname_not_resolved,
                other,
                quic_protocol_failed,
                connection_closed,
                timed_out,
                connection_refused,
                connection_reset,
                address_unreachable,
                client_certificate_rejected,
                cannot_parse_response,
                secure_connection_failed,
                connection_timed_out,
                cannot_find_host,
                quic_crypto_symmetric_key_setup_failed,
                calls_active,
                international_roaming_off,
                data_not_allowed,
                cannot_decode_raw_data,
                https_proxy_connection_failure],
        name="g2f_failure_reason_breakdown",
        bq_dialect="standard"
    )
    return mt




def g2f_ratio_and_failure_metrics(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
    `sc-analytics.report_messaging.app_startup_feed_ready_events_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    g2f_count = Metric(
        'g2f_count',
        'G2FF Event Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.POSITIVE
        
    )
    
    g2f_cold_count = Metric(
        'g2f_cold_count',
        'G2FF Cold Start Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    g2f_cold_ratio = Metric(
        'g2f_cold_ratio',
        'G2FF Cold Start Ratio',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='g2f_cold_count', 
        denominator='g2f_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    g2f_warm_count = Metric(
        'g2f_warm_count',
        'G2FF Warm Start Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    g2f_warm_ratio = Metric(
        'g2f_warm_ratio',
        'G2FF Warm Start Ratio',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='g2f_warm_count', 
        denominator='g2f_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    g2f_hot_count = Metric(
        'g2f_hot_count',
        'G2FF Hot Start Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.POSITIVE
    )
    
    g2f_hot_ratio = Metric(
        'g2f_hot_ratio',
        'G2FF Hot Start Ratio',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='g2f_hot_count', 
        denominator='g2f_count',
        desired_direction=abtest.metric.POSITIVE
    )
    
    failure_count = Metric(
        'failure_count',
        'G2FF Failure Count (excluding user bailed)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    failure_rate = Metric(
        'failure_rate',
        'G2FF Failure Rate (excluding user bailed)',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='failure_count', 
        denominator='g2f_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    cold_failure_count = Metric(
        'cold_failure_count',
        'G2FF Cold Failure Count (excluding user bailed)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    cold_failure_rate = Metric(
        'cold_failure_rate',
        'G2FF Cold Failure Rate (excluding user bailed)',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='cold_failure_count', 
        denominator='g2f_cold_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    warm_failure_count = Metric(
        'warm_failure_count',
        'G2FF Warm Failure Count (excluding user bailed)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    warm_failure_rate = Metric(
        'warm_failure_rate',
        'G2FF Warm Failure Rate (excluding user bailed)',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='warm_failure_count', 
        denominator='g2f_warm_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    hot_failure_count = Metric(
        'hot_failure_count',
        'G2FF Hot Failure Count (excluding user bailed)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    hot_failure_rate = Metric(
        'hot_failure_rate',
        'G2FF Hot Failure Rate (excluding user bailed)',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='hot_failure_count', 
        denominator='g2f_hot_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    user_bailed_count = Metric(
        'user_bailed_count',
        'G2FF User Bailed Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    user_bailed_rate = Metric(
        'user_bailed_rate',
        'G2FF User Bailed Rate',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='user_bailed_count', 
        denominator='g2f_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    cold_user_bailed_count = Metric(
        'cold_user_bailed_count',
        'G2FF Cold User Bailed Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    cold_user_bailed_rate = Metric(
        'cold_user_bailed_rate',
        'G2FF Cold User Bailed Rate',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='cold_user_bailed_count', 
        denominator='g2f_cold_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    warm_user_bailed_count = Metric(
        'warm_user_bailed_count',
        'G2FF Warm User Bailed Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    warm_user_bailed_rate = Metric(
        'warm_user_bailed_rate',
        'G2FF Warm User Bailed Rate',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='warm_user_bailed_count', 
        denominator='g2f_warm_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    hot_user_bailed_count = Metric(
        'hot_user_bailed_count',
        'G2FF Hot User Bailed Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    hot_user_bailed_rate = Metric(
        'hot_user_bailed_rate',
        'G2FF Hot User Bailed Rate',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='hot_user_bailed_count', 
        denominator='g2f_hot_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    g2f_with_notif = Metric(
        'g2f_with_notif',
        'G2FF With Notification Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.POSITIVE
    )
    
    g2f_with_notif_rate = Metric(
        'g2f_with_notif_rate',
        'G2FF With Notification Rate',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='g2f_with_notif', 
        denominator='g2f_count',
        desired_direction=abtest.metric.POSITIVE
    )

    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        ghost_user_id,
        SUM(IF(latency_millis BETWEEN 0 AND 3600000, 1, NULL)) AS g2f_count,
        SUM(IF(latency_millis BETWEEN 0 AND 3600000 AND source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='COLD', 1, NULL)) AS g2f_cold_count,
        SUM(IF(latency_millis BETWEEN 0 AND 3600000 AND source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='WARM', 1, NULL)) AS g2f_warm_count,
        SUM(IF(latency_millis BETWEEN 0 AND 3600000 AND source IN ('SOURCE_WARM_START', 'warm_start') AND app_startup_type ='WARM', 1, NULL)) AS g2f_hot_count,
        SUM(IF(with_result_successful IS FALSE and (failure_reason !='user_bailed' AND arroyo_failure_reason!='user_bailed'), 1, NULL)) AS failure_count,
        SUM(IF(with_result_successful IS FALSE AND source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='COLD' and (failure_reason !='user_bailed' AND arroyo_failure_reason!='user_bailed'), 1, NULL)) AS cold_failure_count,
        SUM(IF(with_result_successful IS FALSE AND source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='WARM' and (failure_reason !='user_bailed' AND arroyo_failure_reason!='user_bailed'), 1, NULL)) AS warm_failure_count,
        SUM(IF(with_result_successful IS FALSE AND source IN ('SOURCE_WARM_START', 'warm_start') AND app_startup_type ='WARM' and (failure_reason !='user_bailed' AND arroyo_failure_reason!='user_bailed'), 1, NULL)) AS hot_failure_count,
        SUM(IF(with_result_successful IS FALSE and (failure_reason ='user_bailed' OR arroyo_failure_reason='user_bailed'), 1, NULL)) AS user_bailed_count,
        SUM(IF(with_result_successful IS FALSE AND source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='COLD' and (failure_reason ='user_bailed' OR arroyo_failure_reason='user_bailed'), 1, NULL)) AS cold_user_bailed_count,
        SUM(IF(with_result_successful IS FALSE AND source IN ('SOURCE_COLD_START', 'cold_start') AND app_startup_type ='WARM' and (failure_reason ='user_bailed' OR arroyo_failure_reason='user_bailed'), 1, NULL)) AS warm_user_bailed_count,
        SUM(IF(with_result_successful IS FALSE AND source IN ('SOURCE_WARM_START', 'warm_start') AND app_startup_type ='WARM' and (failure_reason ='user_bailed' OR arroyo_failure_reason='user_bailed'), 1, NULL)) AS hot_user_bailed_count,
        SUM(IF(latency_millis BETWEEN 0 AND 3600000 and with_triggered_by_notification = TRUE, latency_millis, NULL))/SUM(IF(latency_millis BETWEEN 0 AND 3600000, 1, NULL)) AS g2f_with_notif
        FROM
          {source_table}
        GROUP BY 1, 2
        """.format(source_table=source_table),
        metrics=[g2f_count,
                 g2f_cold_count, 
                 g2f_cold_ratio,
                 g2f_warm_count, 
                 g2f_warm_ratio,
                 g2f_hot_count, 
                 g2f_hot_ratio,
                 failure_count,
                 failure_rate,
                 cold_failure_count,
                 cold_failure_rate,
                 warm_failure_count,
                 warm_failure_rate,
                 hot_failure_count,
                 hot_failure_rate,
                 user_bailed_count,
                 user_bailed_rate,
                 cold_user_bailed_count,
                 cold_user_bailed_rate,
                 warm_user_bailed_count,
                 warm_user_bailed_rate,
                 hot_user_bailed_count,
                 hot_user_bailed_rate,
                 g2f_with_notif,
                 g2f_with_notif_rate],
        name="g2f_ratio_and_failure_metrics",
        bq_dialect="standard"
    )
    return mt


def friend_feed_ready_overstat_metrics(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
    `sc-analytics.report_messaging.friend_feed_ready_events_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    ffr_count = Metric(
        'ffr_count',
        'Friend Feed Ready Event Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.POSITIVE
        
    )
    
    ffr_cold_count = Metric(
        'ffr_cold_count',
        'FFR Cold Start Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_cold_ratio = Metric(
        'ffr_cold_ratio',
        'FFR Cold Start Ratio',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='ffr_cold_count', 
        denominator='ffr_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_warm_count = Metric(
        'ffr_warm_count',
        'FFR Warm Start Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.POSITIVE
    )
    
    ffr_warm_ratio = Metric(
        'ffr_warm_ratio',
        'FFR Warm Start Ratio',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='ffr_warm_count', 
        denominator='ffr_count',
        desired_direction=abtest.metric.POSITIVE
    )
    
    
    ffr_sync_not_complete = Metric(
        'ffr_sync_not_complete',
        'FFR Sync Not Complete (includes failure and user bailed)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_sync_not_complete_ratio = Metric(
        'ffr_sync_not_complete_ratio',
        'FFR Sync Not Complete Rate (includes failure and user bailed)',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='ffr_sync_not_complete', 
        denominator='ffr_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_cold_sync_not_complete = Metric(
        'ffr_cold_sync_not_complete',
        'FFR Cold Sync Not Complete (includes failure and user bailed)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_cold_sync_not_complete_ratio = Metric(
        'ffr_cold_sync_not_complete_ratio',
        'FFR Cold Sync Not Complete Rate (includes failure and user bailed)',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='ffr_cold_sync_not_complete', 
        denominator='ffr_cold_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_warm_sync_not_complete = Metric(
        'ffr_warm_sync_not_complete',
        'FFR Warm Sync Not Complete (includes failure and user bailed)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_warm_sync_not_complete_ratio = Metric(
        'ffr_warm_sync_not_complete_ratio',
        'FFR Warm Sync Not Complete Rate (includes failure and user bailed)',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='ffr_warm_sync_not_complete', 
        denominator='ffr_warm_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    
    ffr_sync_failed = Metric(
        'ffr_sync_failed',
        'FFR Sync Failure',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_sync_failed_ratio = Metric(
        'ffr_sync_failed_ratio',
        'FFR Sync Failed Rate',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='ffr_sync_failed', 
        denominator='ffr_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_cold_sync_failed = Metric(
        'ffr_cold_sync_failed',
        'FFR Cold Sync Failure',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_cold_sync_failed_ratio = Metric(
        'ffr_cold_sync_failed_ratio',
        'FFR Cold Sync Failed Rate',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='ffr_cold_sync_failed', 
        denominator='ffr_cold_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_warm_sync_failed = Metric(
        'ffr_warm_sync_failed',
        'FFR Warm Sync Failure',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_warm_sync_failed_ratio = Metric(
        'ffr_warm_sync_failed_ratio',
        'FFR Warm Sync Failed Rate',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='ffr_warm_sync_failed', 
        denominator='ffr_warm_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_sync_not_complete_null_failure_reason = Metric(
        'ffr_sync_not_complete_null_failure_reason',
        'FFR Sync Not Complete wtih null failure reason',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_sync_not_complete_null_failure_reason_ratio = Metric(
        'ffr_sync_not_complete_null_failure_reason_ratio',
        'FFR Sync Not Complete Rate with null failure reason',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='ffr_sync_not_complete_null_failure_reason', 
        denominator='ffr_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_cold_sync_not_complete_null_failure_reason = Metric(
        'ffr_cold_sync_not_complete_null_failure_reason',
        'FFR Cold Sync Not Complete wtih null failure reason',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_cold_sync_not_complete_null_failure_reason_ratio = Metric(
        'ffr_cold_sync_not_complete_null_failure_reason_ratio',
        'FFR Cold Sync Not Complete Rate with null failure reason',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='ffr_cold_sync_not_complete_null_failure_reason', 
        denominator='ffr_cold_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_warm_sync_not_complete_null_failure_reason = Metric(
        'ffr_warm_sync_not_complete_null_failure_reason',
        'FFR Warm Sync Not Complete wtih null failure reason',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_warm_sync_not_complete_null_failure_reason_ratio = Metric(
        'ffr_warm_sync_not_complete_null_failure_reason_ratio',
        'FFR Warm Sync Not Complete Rate with null failure reason',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='ffr_warm_sync_not_complete_null_failure_reason', 
        denominator='ffr_warm_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_user_bailed = Metric(
        'ffr_user_bailed',
        'FFR User Bailed',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_user_bailed_ratio = Metric(
        'ffr_user_bailed_ratio',
        'FFR User Bailed Rate',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='ffr_user_bailed', 
        denominator='ffr_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_cold_user_bailed = Metric(
        'ffr_cold_user_bailed',
        'FFR Cold User Bailed',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_cold_user_bailed_ratio = Metric(
        'ffr_cold_user_bailed_ratio',
        'FFR Cold User Bailed Rate',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='ffr_cold_user_bailed', 
        denominator='ffr_cold_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_warm_user_bailed = Metric(
        'ffr_warm_user_bailed',
        'FFR Warm User Bailed',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_warm_user_bailed_ratio = Metric(
        'ffr_warm_user_bailed_ratio',
        'FFR Warm User Bailed Rate',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='ffr_warm_user_bailed', 
        denominator='ffr_warm_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_user_bailed_client_ui = Metric(
        'ffr_user_bailed_client_ui',
        'FFR User Bailed from Client UI',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_user_bailed_client_ui_ratio = Metric(
        'ffr_user_bailed_client_ui_ratio',
        'FFR User Bailed Rate from Client UI',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='ffr_user_bailed_client_ui', 
        denominator='ffr_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_cold_user_bailed_client_ui = Metric(
        'ffr_cold_user_bailed_client_ui',
        'FFR Cold User Bailed from Client UI',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_cold_user_bailed_client_ui_ratio = Metric(
        'ffr_cold_user_bailed_client_ui_ratio',
        'FFR Cold User Bailed Rate from Client UI',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='ffr_cold_user_bailed_client_ui', 
        denominator='ffr_cold_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_warm_user_bailed_client_ui = Metric(
        'ffr_warm_user_bailed_client_ui',
        'FFR Warm User Bailed from Client UI',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    ffr_warm_user_bailed_client_ui_ratio = Metric(
        'ffr_warm_user_bailed_client_ui_ratio',
        'FFR Warm User Bailed Rate from Client UI',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='ffr_warm_user_bailed_client_ui', 
        denominator='ffr_warm_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    greater_than_0_convos_synced = Metric(
        'greater_than_0_convos_synced',
        'Greater than 0 Convos Synced Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    greater_than_0_convos_synced_ratio = Metric(
        'greater_than_0_convos_synced_ratio',
        'Greater than 0 Convos Synced Rate',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='greater_than_0_convos_synced', 
        denominator='ffr_warm_count',
        desired_direction=abtest.metric.NEGATIVE
    )
    
    stale_feed_count = Metric(
        'stale_feed_count',
        'Stale Feed Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )
    
    stale_feed_ratio = Metric(
        'stale_feed_ratio',
        'Stale Feed Ratio (primary overstat metric)',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='stale_feed_count', 
        denominator='ffr_count',
        desired_direction=abtest.metric.NEGATIVE
    )

    
    total_convos_synced = Metric(
        'total_convos_synced',
        'Total Convos Synced',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.POSITIVE
    )
    
    

    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        ghost_user_id,
        SUM(IF(event_name='FRIEND_FEED_READY',1,0)) as ffr_count,
        SUM(IF(event_name = 'FRIEND_FEED_READY' AND app_startup_type ='COLD', 1, NULL)) AS ffr_cold_count,
        SUM(IF(event_name = 'FRIEND_FEED_READY' AND app_startup_type ='WARM', 1, NULL)) AS ffr_warm_count,
        SUM(IF(did_sync_complete=FALSE,1,0)) AS ffr_sync_not_complete,
        SUM(IF(did_sync_complete=FALSE AND app_startup_type ='COLD',1,0)) AS ffr_cold_sync_not_complete,
        SUM(IF(did_sync_complete=FALSE AND app_startup_type ='WARM',1,0)) AS ffr_warm_sync_not_complete,
        SUM(IF(did_sync_complete=FALSE AND lower(failure_reason)<> 'user_bailed',1,0)) AS ffr_sync_failed,
        SUM(IF(did_sync_complete=FALSE AND lower(failure_reason)<> 'user_bailed' AND app_startup_type ='COLD',1,0)) AS ffr_cold_sync_failed,
        SUM(IF(did_sync_complete=FALSE AND lower(failure_reason)<> 'user_bailed'AND app_startup_type ='WARM',1,0)) AS ffr_warm_sync_failed,
        SUM(IF(did_sync_complete=FALSE AND failure_reason is null ,1,0)) AS ffr_sync_not_complete_null_failure_reason,
        SUM(IF(did_sync_complete=FALSE AND failure_reason is null AND app_startup_type ='COLD',1,0)) AS ffr_cold_sync_not_complete_null_failure_reason,
        SUM(IF(did_sync_complete=FALSE AND failure_reason is null AND app_startup_type ='WARM',1,0)) AS ffr_warm_sync_not_complete_null_failure_reason, 
        SUM(IF(did_sync_complete=FALSE AND failure_reason = 'user_bailed',1,0)) AS ffr_user_bailed,
        SUM(IF(did_sync_complete=FALSE AND failure_reason = 'user_bailed' AND app_startup_type ='COLD',1,0)) AS ffr_cold_user_bailed,
        SUM(IF(did_sync_complete=FALSE AND failure_reason = 'user_bailed'AND app_startup_type ='WARM',1,0)) AS ffr_warm_user_bailed,
        SUM(IF(did_sync_complete=FALSE AND failure_reason = 'USER_BAILED',1,0)) AS ffr_user_bailed_client_ui,
        SUM(IF(did_sync_complete=FALSE AND failure_reason = 'USER_BAILED' AND app_startup_type ='COLD',1,0)) AS ffr_cold_user_bailed_client_ui,
        SUM(IF(did_sync_complete=FALSE AND failure_reason = 'USER_BAILED'AND app_startup_type ='WARM',1,0)) AS ffr_warm_user_bailed_client_ui,
        SUM(IF((number_of_conversations_synced>0 AND number_of_conversations_synced is not null),1,0)) AS greater_than_0_convos_synced,
        SUM(IF(sync_feed_entry_time_ms is not null AND sync_feed_entry_time_ms>0 AND sync_time_ms is not null AND sync_time_ms>sync_feed_entry_time_ms AND number_of_conversations_synced is not null and number_of_conversations_synced>0 ,1,0)) AS stale_feed_count,
        SUM(IF(number_of_conversations_synced is null,0,number_of_conversations_synced)) AS total_convos_synced
        FROM
          {source_table}
        GROUP BY 1, 2
        """.format(source_table=source_table),
        metrics=[ffr_count,
                ffr_cold_count,
                ffr_cold_ratio,
                ffr_warm_count,
                ffr_warm_ratio,
                ffr_sync_not_complete,
                ffr_sync_not_complete_ratio,
                ffr_cold_sync_not_complete,
                ffr_cold_sync_not_complete_ratio,
                ffr_warm_sync_not_complete,
                ffr_warm_sync_not_complete_ratio,
                ffr_sync_failed,
                ffr_sync_failed_ratio,
                ffr_cold_sync_failed,
                ffr_cold_sync_failed_ratio,
                ffr_warm_sync_failed,
                ffr_warm_sync_failed_ratio,
                ffr_sync_not_complete_null_failure_reason,
                ffr_sync_not_complete_null_failure_reason_ratio,
                ffr_cold_sync_not_complete_null_failure_reason,
                ffr_cold_sync_not_complete_null_failure_reason_ratio,
                ffr_warm_sync_not_complete_null_failure_reason,
                ffr_warm_sync_not_complete_null_failure_reason_ratio,
                ffr_user_bailed,
                ffr_user_bailed_ratio,
                ffr_cold_user_bailed,
                ffr_cold_user_bailed_ratio,
                ffr_warm_user_bailed,
                ffr_warm_user_bailed_ratio,
                ffr_user_bailed_client_ui,
                ffr_user_bailed_client_ui_ratio,
                ffr_cold_user_bailed_client_ui,
                ffr_cold_user_bailed_client_ui_ratio,
                ffr_warm_user_bailed_client_ui,
                ffr_warm_user_bailed_client_ui_ratio,
                greater_than_0_convos_synced,
                greater_than_0_convos_synced_ratio,
                stale_feed_count,
                stale_feed_ratio,
                total_convos_synced],
        name="friend_feed_ready_overstat_metrics",
        bq_dialect="standard"
    )
    return mt

def chat_display_ready_latency_metrics(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """
    start_date = pd.to_datetime(start_date).strftime('%y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%y%m%d')
    source_table = """
    `sc-analytics.report_messaging.chat_display_ready_events_20*`
    WHERE _table_suffix between '{start}' and '{end}' AND event_name= 'CHAT_DISPLAY_READY'
    """.format(
        start=start_date,
        end=end_date,
    )

    cdr_total_latency = Metric(
        'cdr_total_latency',
        'Chat Display Ready Total Latency',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    cdr_initial_render_time = Metric(
        'cdr_initial_render_time',
        'Chat Display Ready Initial Render Time',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    cdr_chat_header_loading_time = Metric(
        'cdr_chat_header_loading_time',
        'Chat Display Ready Chat Header Loading Time',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    cdr_conversation_fetch_time = Metric(
        'cdr_conversation_fetch_time',
        'Chat Display Ready Conversation Fetch Time',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    cdr_conversation_data_fetch_time = Metric(
        'cdr_conversation_data_fetch_time',
        'Chat Display Ready Conversation Data Fetch Time',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    cdr_view_model_generation_time = Metric(
        'cdr_view_model_generation_time',
        'Chat Display Ready View Model Generation Time',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    cdr_wallpaper_loading_time = Metric(
        'cdr_wallpaper_loading_time',
        'Chat Display Ready Wallpaper Loading Time',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    

    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%y%m%d', _table_suffix) as ts,
        ghost_user_id,
        total_latency AS cdr_total_latency,
        initial_render_time_ms AS cdr_initial_render_time,
        chat_header_loading_time_ms AS cdr_chat_header_loading_time,
        conversation_fetch_time_ms AS cdr_conversation_fetch_time,
        conversation_data_fetch_time_ms AS cdr_conversation_data_fetch_time,
        view_model_generation_time_ms AS cdr_view_model_generation_time,
        wallpaper_loading_time_ms AS cdr_wallpaper_loading_time
        
        FROM
          {source_table}
        """.format(source_table=source_table, start=start_date, end=end_date),
        metrics=[cdr_total_latency,
                 cdr_initial_render_time,
                 cdr_chat_header_loading_time,
                 cdr_conversation_fetch_time,
                 cdr_conversation_data_fetch_time,
                 cdr_view_model_generation_time,
                 cdr_wallpaper_loading_time],
        name="chat_display_ready_latency_metrics",
        quantile_metrics=True,
        bq_dialect="standard"
    )
    return mt

def chat_display_ready_failure_rate(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """
    start_date = pd.to_datetime(start_date).strftime('%y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%y%m%d')
    source_table = """
    `sc-analytics.report_messaging.chat_display_ready_events_20*`
    WHERE _table_suffix between '{start}' and '{end}' AND event_name= 'CHAT_DISPLAY_READY'
    """.format(
        start=start_date,
        end=end_date,
    )

    cdr_event_count = Metric(
        'cdr_event_count',
        'Chat Display Ready Event Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.POSITIVE
        
    )

    cdr_failure_count = Metric(
        'cdr_failure_count',
        'Chat Display Ready Failure Event Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    cdr_failure_rate = Metric(
        'cdr_failure_rate',
        'Chat Display Ready Failure Rate',
        dist='ratio',
        daily=True,
        cumulative=True,
        numerator='cdr_failure_count', 
        denominator='cdr_event_count',
        desired_direction=abtest.metric.NEGATIVE
    )

    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%y%m%d', _table_suffix) as ts,
        ghost_user_id,
        SUM(1) as cdr_event_count,
        SUM(IF(failure_reason is not null, 1, NULL)) AS cdr_failure_count
        
        FROM
          {source_table}
        GROUP BY 1, 2
        """.format(source_table=source_table, start=start_date, end=end_date),
        metrics=[cdr_event_count,
                 cdr_failure_count,
                 cdr_failure_rate],
        name="chat_display_ready_failure_rate",
        bq_dialect="standard"
    )
    return mt

def chat_display_ready_failure_reason_breakdown(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """
    start_date = pd.to_datetime(start_date).strftime('%y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%y%m%d')
    source_table = """
    `sc-analytics.report_messaging.chat_display_ready_events_20*`
    WHERE _table_suffix between '{start}' and '{end}' AND event_name= 'CHAT_DISPLAY_READY'
    """.format(
        start=start_date,
        end=end_date,
    )

    user_bailed = Metric(
        'user_bailed',
        'User Bailed',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    timeout = Metric(
        'timeout',
        'Timeout',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    conversation_not_found = Metric(
        'conversation_not_found',
        'Conversation Not Found',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    fetch_conversation_identifier_failed = Metric(
        'fetch_conversation_identifier_failed',
        'Fetch Conversation Identifier Failed',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    fetch_snapchatter_failed = Metric(
        'fetch_snapchatter_failed',
        'Fetch SnapChatter Failed',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    fetch_messages_failed = Metric(
        'fetch_messages_failed',
        'Fetch Messages Failed',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    enter_conversation_failed = Metric(
        'enter_conversation_failed',
        'Enter Conversation Failed',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=abtest.metric.NEGATIVE
    )

    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%y%m%d', _table_suffix) as ts,
        ghost_user_id,
        SUM(IF(failure_reason="USER_BAILED", 1, NULL)) as user_bailed,
        SUM(IF(failure_reason="TIMEOUT", 1, NULL)) as timeout,
        SUM(IF(failure_reason="CONVERSATION_NOT_FOUND", 1, NULL)) as conversation_not_found,
        SUM(IF(failure_reason="FETCH_CONVERSATION_IDENTIFIER_FAILED", 1, NULL)) as fetch_conversation_identifier_failed,
        SUM(IF(failure_reason="FETCH_SNAPCHATTER_FAILED", 1, NULL)) as fetch_snapchatter_failed,
        SUM(IF(failure_reason="FETCH_MESSAGES_FAILED", 1, NULL)) as fetch_messages_failed,
        SUM(IF(failure_reason="ENTER_CONVERSATION_FAILED", 1, NULL)) as enter_conversation_failed
        
        FROM
          {source_table}
        GROUP BY 1, 2
        """.format(source_table=source_table, start=start_date, end=end_date),
        metrics=[user_bailed,
                 timeout,
                 conversation_not_found,
                 fetch_conversation_identifier_failed,
                 fetch_snapchatter_failed,
                 fetch_messages_failed,
                 enter_conversation_failed],
        name="chat_display_ready_failure_reason_breakdown",
        bq_dialect="standard"
    )
    return mt
