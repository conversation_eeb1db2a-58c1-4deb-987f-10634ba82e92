""" Map metrics
Contact: maps-data-science@
"""

from __future__ import division, print_function
from banjo.abtest.metric import (
    Metric, MetricTable, abtest_metrics_sql_helper
)
from datetime import timedelta
import pandas as pd
from banjo.abtest.metric import POSITIVE, NEGATIVE

__all__ = [
    "map_quality",
    "map_story_details",
    "map_actions", # merged with map_engagement
    "map_location_sharing",
    "map_location_sharing_setting_shift",
    "map_time_metrics",
    "map_tray_engagement",
    "map_performance_metrics",
    "map_off_platform_share",
    "map_friend_viewport",
    "map_zoom_level_metrics",
    "map_live_location_sharing", # renamed
    "map_friend_focus_view", # new
    "map_notifications",
    "map_clutter",
    "map_viewport_zoom_metrics",
    "map_location_sharing_list_size",
    "map_background_location_coverage"
]

def map_quality(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    
    map_error_count = Metric(
        'map_error_count',
        'Map Error Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )
    
    map_error_uu = Metric(
        'map_error_uu',
        'Map Error Unique User',
        dist='bin',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )
    
    map_fatal_error_count = Metric(
        'map_fatal_error_count',
        'Map Fatal Error Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )
    
    map_fatal_error_uu = Metric(
        'map_fatal_error_uu',
        'Map Fatal Error Unique User',
        dist='bin',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )
    
    def sql_callable(start_date, end_date):
        sql="""
        SELECT
            TIMESTAMP_SUB( event_date, INTERVAL 8 HOUR) AS ts,
            ghost_user_id,
            COUNT(1) AS map_error_count,
            IF(COUNT(1)>0,1,0) AS map_error_uu,
            SUM(IF(is_fatal,1,0)) AS map_fatal_error_count,
            IF(SUM(IF(is_fatal,1,0))>0,1,0) AS map_fatal_error_uu
        FROM `sc-analytics.report_maps.maps_error_events_*`
        WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
        GROUP BY
            1,
            2
        """.format(
            start=start_date,
            end=end_date
            )
        return sql
        
    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[
            map_error_count,
            map_error_uu,
            map_fatal_error_count,
            map_fatal_error_uu
                
        ],
        name="map_quality",
        bq_dialect='standard'
    )

    return mt
    

def map_story_details(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    >>> map_story('20170501', '20170503')
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    story_snap_heatmap_view_uu = Metric(
        'story_snap_heatmap_view_uu',
        'Heatmap Story Snap View Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )
 
    story_snap_poi_view_uu = Metric(
        'story_snap_poi_view_uu',
        'POI Story Snap View Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )

    story_snap_city_view_uu = Metric(
        'story_snap_city_view_uu',
        'City Story Snap View Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )

    story_snap_friend_view_uu = Metric(
        'story_snap_friend_view_uu',
        'Friend Story Snap View Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )

    place_story_view_uu_sum = Metric(
        'place_story_view_uu_sum',
        'Places Story Snap View Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )    

    story_snap_heatmap_view_count = Metric(
        'story_snap_heatmap_view_count',
        'Heatmap Story Snap View Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    story_snap_poi_view_count = Metric(
        'story_snap_poi_view_count',
        'POI Story Snap View Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    story_snap_city_view_count = Metric(
        'story_snap_city_view_count',
        'City Story Snap View Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    story_snap_friend_view_count = Metric(
        'story_snap_friend_view_count',
        'Friend Story Snap View Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    place_story_view_count_sum = Metric(
        'place_story_view_count_sum',
        'Places Story Snap View Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    story_snap_heatmap_view_time = Metric(
        'story_snap_heatmap_view_time',
        'Heatmap Story Snap View Time',
        dist='cont',
        daily=True,
        cumulative=True
    )

    story_snap_poi_view_time = Metric(
        'story_snap_poi_view_time',
        'POI Story Snap View Time',
        dist='cont',
        daily=True,
        cumulative=True
    )

    story_snap_city_view_time = Metric(
        'story_snap_city_view_time',
        'City Story Snap View Time',
        dist='cont',
        daily=True,
        cumulative=True
    )

    story_snap_friend_view_time = Metric(
        'story_snap_friend_view_time',
        'Friend Story Snap View Time',
        dist='cont',
        daily=True,
        cumulative=True
    )

    place_story_view_time_sum = Metric(
        'place_story_view_time_sum',
        'Places Story Snap View Time',
        dist='cont',
        daily=True,
        cumulative=True
    )

    place_story_post_count = Metric(
        'place_story_post_count',
        'Places Story Post Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    place_story_post_map_count = Metric(
        'place_story_post_map_count',
        'Places Story Post to Map Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    place_story_post_spotlight_count = Metric(
        'place_story_post_spotlight_count',
        'Places Story Post to Spotlight Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    def sql_callable(start_date, end_date):
        sql="""
            WITH dau AS (
            SELECT
              TIMESTAMP_SUB( event_date, INTERVAL 8 HOUR) AS ts,
              ghost_user_id
            FROM
              `sc-analytics.report_app.dau_user_country_20*`
            WHERE 
              _TABLE_SUFFIX between '{start_trunc}' AND '{end_trunc}' 
              AND APP_APPLICATION_OPEN_UU = 1
            GROUP BY
                1,
                2
            ),
            
            map_story_views as (
            SELECT
                TIMESTAMP_SUB( event_date, INTERVAL 8 HOUR) AS ts,
                ghost_user_id,

                if(sum(STORY_SNAP_DYNAMIC_HEATMAP_VIEW_COUNT)>0,1,0) as story_snap_heatmap_view_uu,
                if(sum(STORY_SNAP_DYNAMIC_POI_VIEW_COUNT)>0,1,0) as story_snap_poi_view_uu,
                if(sum(STORY_SNAP_DYNAMIC_CITY_VIEW_COUNT)>0,1,0) as story_snap_city_view_uu,
                if(sum(STORY_SNAP_USER_VIEW_COUNT+STORY_SNAP_GROUP_VIEW_COUNT+STORY_SNAP_MY_VIEW_COUNT)>0,1,0) as story_snap_friend_view_uu,

                sum(STORY_SNAP_DYNAMIC_HEATMAP_VIEW_COUNT) as story_snap_heatmap_view_count,
                sum(STORY_SNAP_DYNAMIC_POI_VIEW_COUNT) as story_snap_poi_view_count,
                sum(STORY_SNAP_DYNAMIC_CITY_VIEW_COUNT) AS story_snap_city_view_count,
                sum(STORY_SNAP_USER_VIEW_COUNT+STORY_SNAP_GROUP_VIEW_COUNT+STORY_SNAP_MY_VIEW_COUNT) as story_snap_friend_view_count,
                
                sum(STORY_SNAP_DYNAMIC_HEATMAP_VIEW_TIME) as story_snap_dynamic_heatmap_view_time,
                sum(STORY_SNAP_DYNAMIC_POI_VIEW_TIME) as story_snap_dynamic_poi_view_time,
                sum(STORY_SNAP_DYNAMIC_CITY_VIEW_TIME) AS story_snap_dynamic_city_view_time,
                sum(STORY_SNAP_USER_VIEW_TIME+STORY_SNAP_GROUP_VIEW_TIME+STORY_SNAP_MY_VIEW_TIME) as story_snap_friend_view_time,

            FROM `sc-analytics.report_maps.maps_dau_user_country_*`
            WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}' 
            GROUP BY
                1,
                2
            ),
            
            place_story_views AS (
            SELECT
              TIMESTAMP_SUB(event_date, INTERVAL 8 HOUR) AS ts,
              ghost_user_id,
              COUNT(1) AS place_story_view_count_sum,
              IF(COUNT(1)>0,1,0) AS place_story_view_uu_sum,
              SUM(IFNULL(time_viewed,0)) AS place_story_view_time,
            FROM
              `sc-analytics.report_maps.places_story_views_20*`
            WHERE
              _TABLE_SUFFIX between '{start_trunc}' AND '{end_trunc}' 
            GROUP BY
              1,
              2
            ),
            
            place_story_post AS (
            SELECT
              TIMESTAMP_SUB(event_date, INTERVAL 8 HOUR) AS ts,
              ghost_user_id,
              COUNT(1) AS place_story_post_count,
              SUM(CASE WHEN story_type = 'OUR' AND (community_submission_variant IS NULL OR community_submission_variant IN ('NONE', 'OUR', 'BOTH')) THEN 1 ELSE 0 END) AS place_story_post_map_count,
              SUM(CASE WHEN story_type = 'OUR' AND community_submission_variant IN ('SPOTLIGHT', 'BOTH') THEN 1 ELSE 0 END) AS place_story_post_spotlight_count
            FROM
              `sc-analytics.report_maps.places_story_post_20*`
            WHERE
              _TABLE_SUFFIX between '{start_trunc}' AND '{end_trunc}'
            GROUP BY
              1,
              2
            )
            
            SELECT
              COALESCE(msv.ts, psv.ts) AS ts,
              COALESCE(msv.ghost_user_id, psv.ghost_user_id) AS ghost_user_id,

              IFNULL(story_snap_heatmap_view_uu,0) AS story_snap_heatmap_view_uu,
              IFNULL(story_snap_poi_view_uu,0) AS story_snap_poi_view_uu,
              IFNULL(story_snap_city_view_uu,0) AS story_snap_city_view_uu,
              IFNULL(story_snap_friend_view_uu,0) AS story_snap_friend_view_uu,
              IFNULL(place_story_view_uu_sum,0) AS place_story_view_uu_sum,

              IFNULL(story_snap_heatmap_view_count,0) AS story_snap_heatmap_view_count,
              IFNULL(story_snap_poi_view_count,0) AS story_snap_poi_view_count,
              IFNULL(story_snap_city_view_count,0) AS story_snap_city_view_count,
              IFNULL(story_snap_friend_view_count,0) AS story_snap_friend_view_count,
              IFNULL(place_story_view_count_sum,0) AS place_story_view_count_sum,

              IFNULL(story_snap_dynamic_heatmap_view_time,0) AS story_snap_heatmap_view_time,
              IFNULL(story_snap_dynamic_poi_view_time,0) AS story_snap_poi_view_time,
              IFNULL(story_snap_dynamic_city_view_time,0) AS story_snap_city_view_time,
              IFNULL(story_snap_friend_view_time,0) AS story_snap_friend_view_time,
              IFNULL(place_story_view_time,0) AS place_story_view_time_sum,
              
              IFNULL(place_story_post_count,0) AS place_story_post_count,
              IFNULL(place_story_post_map_count,0) AS place_story_post_map_count,
              IFNULL(place_story_post_spotlight_count,0) AS place_story_post_spotlight_count,
            
            FROM dau
            LEFT JOIN map_story_views msv ON dau.ts = msv.ts AND dau.ghost_user_id = msv.ghost_user_id
            LEFT JOIN place_story_views psv ON dau.ts = psv.ts AND dau.ghost_user_id = psv.ghost_user_id
            LEFT JOIN place_story_post psp ON dau.ts = psp.ts AND dau.ghost_user_id = psp.ghost_user_id
            
        """.format(
            start=start_date,
            end=end_date,
            start_trunc=start_trunc,
            end_trunc=end_trunc
            )
        return sql
        
    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[
            story_snap_heatmap_view_uu,
            story_snap_poi_view_uu,
            story_snap_city_view_uu,
            story_snap_friend_view_uu,
            place_story_view_uu_sum,
            story_snap_heatmap_view_count,
            story_snap_poi_view_count,
            story_snap_city_view_count,
            story_snap_friend_view_count,
            place_story_view_count_sum,
            story_snap_heatmap_view_time,
            story_snap_poi_view_time,
            story_snap_city_view_time,
            story_snap_friend_view_time,
            place_story_view_time_sum,
            place_story_post_count,
            place_story_post_map_count,
            place_story_post_spotlight_count
        ],
        name="map_story_details",
        bq_dialect='standard'
    )

    return mt


def map_actions(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    >>> map_actions('20170501', '20170503')
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    
    map_dnu_uu = Metric(
        'map_dnu_uu',
        'Map New Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )

    session_time = Metric(
        'session_time',
        'Map Session Time',
        dist='cont',
        daily=True,
        cumulative=True
    )

    chat_create_count = Metric(
        'chat_create_count',
        'Chat Create Count',
        dist='cont',
        daily=True,
        cumulative=True
    )
    
    chat_create_uu = Metric(
        'chat_create_uu',
        'Chat Create Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )

    chat_send_count = Metric(
        'chat_send_count',
        'Chat Send Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    chat_send_uu = Metric(
        'chat_send_uu',
        'Chat Send Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )

    map_browse_action_count = Metric(
        'map_browse_action_count',
        'Map Browse Action Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    map_browse_action_uu = Metric(
        'map_browse_action_uu',
        'Map Browse Action Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )

    map_user_view_count = Metric(
        'map_user_view_count',
        'Map User View Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    map_user_view_uu = Metric(
        'map_user_view_uu',
        'Map User View Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )
    
    map_user_view_best_friend_count = Metric(
        'map_user_view_best_friend_count',
        'Map User View Best Friend Count',
        dist='cont',
        daily=True,
        cumulative=True
    )
    
    map_user_view_best_friend_uu = Metric(
        'map_user_view_best_friend_uu',
        'Map User View Best Friend Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )
    
    map_ttp_tap_count = Metric(
        'map_ttp_tap_count',
        'Map TTP Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    map_ttp_tap_uu = Metric(
        'map_ttp_tap_uu',
        'Map TTP Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )

    map_friend_count_avg = Metric(
        'map_friend_count_avg',
        'Map Friend Count Avg',
        dist='cont',
        daily=True,
        cumulative=True
    )

    map_best_friend_count_avg = Metric(
        'map_best_friend_count_avg',
        'Map Best Friend Count Avg',
        dist='cont',
        daily=True,
        cumulative=True
    )

    viewport_map_friend_count_avg = Metric(
        'viewport_map_friend_count_avg',
        'Map Viewport Friend Count Avg',
        dist='cont',
        daily=True,
        cumulative=True
    )

    viewport_map_best_friend_count_avg = Metric(
        'viewport_map_best_friend_count_avg',
        'Map Viewport Best Friend Count Avg',
        dist='cont',
        daily=True,
        cumulative=True
    )

    map_open_uu = Metric(
        'map_open_uu',
        'Map Open Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )

    map_open_count = Metric(
        'map_open_count',
        'Map Open Count',
        dist='cont',
        daily=True,
        cumulative=True
    )
    
    def sql_callable(start_date, end_date):
        sql = """
        SELECT
            TIMESTAMP_SUB( event_date, INTERVAL 8 HOUR) AS ts,
            ghost_user_id,

            sum(MAP_REGISTRATION_USER_SUCCESS_UU) as map_dnu_uu,
            sum(VIEW_TIME_SEC) as session_time,

            sum(CHAT_CHAT_CREATE_COUNT) as chat_create_count,
            IF(sum(CHAT_CHAT_CREATE_COUNT)>0,1,0) chat_create_uu,

            sum(CHAT_CHAT_SEND_COUNT) as chat_send_count,
            IF(sum(CHAT_CHAT_SEND_COUNT)>0,1,0) chat_send_uu,
            SUM(MAP_BROWSE_ACTION_PAN_COUNT+MAP_BROWSE_ACTION_PINCH_COUNT) AS map_browse_action_count,
            IF(SUM(MAP_BROWSE_ACTION_PAN_COUNT+MAP_BROWSE_ACTION_PINCH_COUNT)>0,1,0) AS map_browse_action_uu,
            SUM(MAP_USER_VIEW_COUNT) AS map_user_view_count,
            IF(SUM(MAP_USER_VIEW_COUNT)>0,1,0) AS map_user_view_uu,
            SUM(MAP_USER_VIEW_BEST_FRIEND_COUNT) AS map_user_view_best_friend_count,
            IF(SUM(MAP_USER_VIEW_BEST_FRIEND_COUNT)>0,1,0) AS map_user_view_best_friend_uu,
            SUM(MAP_TTP_TAP_COUNT) AS map_ttp_tap_count,
            IF(SUM(MAP_TTP_TAP_COUNT)>0,1,0) AS map_ttp_tap_uu,
            MAX(MAX_MAP_FRIEND_COUNT) AS map_friend_count_avg,
            MAX(MAX_MAP_BEST_FRIEND_COUNT) as map_best_friend_count_avg,
            MAX(MAX_VIEWPORT_FRIEND_ACCUM_COUNT) as viewport_map_friend_count_avg,
            MAX(MAX_VIEWPORT_BEST_FRIEND_ACCUM_COUNT) as viewport_map_best_friend_count_avg,
            MAX(1) as map_open_uu,
            SUM(MAP_MAP_OPEN_COUNT) as map_open_count
        FROM `sc-analytics.report_maps.maps_dau_user_country_*`
        WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}' 
        AND APP_APPLICATION_OPEN_UU=1
          GROUP BY
            1,
            2
        """.format(
            start=start_date,
            end=end_date
            )
        return sql
    
    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[
            map_dnu_uu,
            map_open_uu,
            map_open_count,
            session_time,
            chat_create_count,
            chat_create_uu,
            chat_send_count,
            chat_send_uu,
            map_browse_action_count,
            map_browse_action_uu,
            map_user_view_count,
            map_user_view_uu,
            map_user_view_best_friend_count,
            map_user_view_best_friend_uu,
            map_friend_count_avg,
            map_best_friend_count_avg,
            viewport_map_friend_count_avg,
            viewport_map_best_friend_count_avg,
            map_ttp_tap_count,
            map_ttp_tap_uu
        ],
        name="map_actions",
        bq_dialect='standard'
    )

    return mt


def map_friend_viewport(start_date, end_date):
    
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')


    map_friend_count = Metric(
        'map_friend_count',
        dist='quantile',
        daily=True,
        cumulative=True,
    )

    map_best_friend_count = Metric(
        'map_best_friend_count',
        dist='quantile',
        daily=True,
        cumulative=True,
    )

    viewport_map_friend_count = Metric(
        'viewport_map_friend_count',
        dist='quantile',
        daily=True,
        cumulative=True,
    )

    viewport_map_best_friend_count = Metric(
        'viewport_map_best_friend_count',
        dist='quantile',
        daily=True,
        cumulative=True,
    )    


    def sql_callable(start_date, end_date):
        sql = """
        SELECT
            TIMESTAMP_SUB( event_date, INTERVAL 8 HOUR) AS ts,
            ghost_user_id,
            MAX(MAX_MAP_FRIEND_COUNT) AS map_friend_count,
            MAX(MAX_MAP_BEST_FRIEND_COUNT) as map_best_friend_count,
            MAX(MAX_VIEWPORT_FRIEND_ACCUM_COUNT) as viewport_map_friend_count,
            MAX(MAX_VIEWPORT_BEST_FRIEND_ACCUM_COUNT) as viewport_map_best_friend_count,
        FROM `sc-analytics.report_maps.maps_dau_user_country_*`
        WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}' 
        AND APP_APPLICATION_OPEN_UU=1
          GROUP BY
            1,
            2
        """.format(
            start=start_date,
            end=end_date
            )
        return sql


    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[
            map_friend_count,
            map_best_friend_count,
            viewport_map_friend_count,
            viewport_map_best_friend_count
        ],
        inner_join_with_mapping=False,
        quantile_metrics=True,
        name="map_friend_viewport",
        bq_dialect='standard'
    )

    return mt


def map_location_sharing(start_date, end_date):
    
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    
    sharing_map_location_uu = Metric(
        'sharing_map_location_uu',
        'Sharing Location with Map Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )
    
    ghost_mode_uu = Metric(
        'ghost_mode_uu',
        'Ghost Mode Sharing Setting Unique User',
        dist='bin',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE,
    )

    all_friends_mode_uu = Metric(
        'all_friends_mode_uu',
        'All Friends Sharing Setting Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )
    
    custom_friends_mode_uu = Metric(
        'custom_friends_mode_uu',
        'Custom Friends Sharing Setting Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )
    
    blocklist_mode_uu = Metric(
        'blocklist_mode_uu',
        'Blocklist Mode Sharing Setting Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )
    
    device_sharing_location_true_uu = Metric(
        'device_sharing_location_true_uu',
        'Device Location Sharing Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )
    
    background_location_enabled_uu = Metric(
        'background_location_enabled_uu',
        'Background Location Enabled Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )
    
    foreground_location_enabled_uu = Metric(
        'foreground_location_enabled_uu',
        'Foreground Location Enabled Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )
    
    notification_authorization_enabled_uu = Metric(
        'notification_authorization_enabled_uu',
        'Notification Authorization Enabled Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )

    ghost_mode_update_uu = Metric(
        'ghost_mode_update_uu',
        'Ghost Mode Update Unique User',
        dist='bin',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE,
    )

    ghost_mode_update_count = Metric(
        'ghost_mode_update_count',
        'Ghost Mode Update Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE,
    )

    share_mode_update_uu = Metric(
        'share_mode_update_uu',
        'Share Mode Update Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )

    share_mode_update_count = Metric(
        'share_mode_update_count',
        'Share Mode Update Count',
        dist='cont',
        daily=True,
        cumulative=True
    )    
        
    def sql_callable(start_date, end_date):
        sql="""
        SELECT
          TIMESTAMP_SUB( event_date, INTERVAL 8 HOUR) AS ts,
          ghost_user_id,

          -- MAX(IF(location_sharing_setting in ('CUSTOM_FRIENDS','BLACKLIST_MODE', 'ALL_FRIENDS') AND device_location_permission_granted is true,1,0)) sharing_map_location_true_uu,
          -- MAX(IF(location_sharing_setting in ('CUSTOM_FRIENDS','BLACKLIST_MODE', 'ALL_FRIENDS') AND device_location_permission_granted is true,0,1)) sharing_map_location_false_uu,

          MAX(MAP_SHARE_LOCATION_SETTING_DEVICE_NEW) sharing_map_location_uu,


          MAX(IF(location_sharing_setting in ('GHOST_MODE'),1,0)) ghost_mode_uu,
          MAX(IF(location_sharing_setting in ('ALL_FRIENDS'),1,0)) all_friends_mode_uu,
          MAX(IF(location_sharing_setting in ('CUSTOM_FRIENDS'),1,0)) custom_friends_mode_uu,
          MAX(IF(location_sharing_setting in ('BLACKLIST_MODE'),1,0)) blocklist_mode_uu,

          MAX(IF(device_location_permission_granted is true,1,0)) device_sharing_location_true_uu,
          -- MAX(IF(device_location_permission_granted is false,1,0)) device_sharing_location_false_uu,
          
          MAX(IF(COALESCE(device_sharing_setting, location_permission_state) IN ('BACKGROUND', 'ALWAYS'),1,0)) AS background_location_enabled_uu,
          MAX(IF(COALESCE(device_sharing_setting, location_permission_state) IN ('FOREGROUND', 'WHILE_USING_APP'),1,0)) AS foreground_location_enabled_uu,
          
          MAX(notification_authorization_enabled) AS notification_authorization_enabled_uu,

          SUM(GHOST_MODE_USER_UPDATE_COUNT) ghost_mode_update_count,
          IF(SUM(GHOST_MODE_USER_UPDATE_COUNT)>0,1,0) ghost_mode_update_uu,

          SUM(SHARE_MODE_USER_UPDATE_COUNT) share_mode_update_count,
          IF(SUM(SHARE_MODE_USER_UPDATE_COUNT)>0,1,0) share_mode_update_uu,


        FROM `sc-analytics.report_maps.dau_maps_location_device_sharing_setting_*`
        WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
        GROUP BY
          1,
          2
        """.format(
            start=start_date,
            end=end_date
            )
        return sql
    
    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[
            sharing_map_location_uu,
            ghost_mode_uu,
            all_friends_mode_uu,
            custom_friends_mode_uu,
            blocklist_mode_uu,
            device_sharing_location_true_uu,
            background_location_enabled_uu,
            foreground_location_enabled_uu,
            notification_authorization_enabled_uu,
            ghost_mode_update_count,
            ghost_mode_update_uu,
            share_mode_update_count,
            share_mode_update_uu
        ],
        name="map_location_sharing",
        bq_dialect='standard'
    )
    
    return mt


def map_location_sharing_setting_shift(start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    all_friends_to_custom_friends_count = Metric(
        'all_friends_to_custom_friends_count',
        'All Friends to Custom Friends Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    all_friends_to_custom_friends_uu = Metric(
        'all_friends_to_custom_friends_uu',
        'All Friends to Custom Friends Unique Users',
        dist='bin',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    all_friends_to_blacklist_mode_count = Metric(
        'all_friends_to_blacklist_mode_count',
        'All Friends to Blacklist Mode Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    all_friends_to_blacklist_mode_uu = Metric(
        'all_friends_to_blacklist_mode_uu',
        'All Friends to Blacklist Mode Unique Users',
        dist='bin',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    all_friends_to_ghost_mode_count = Metric(
        'all_friends_to_ghost_mode_count',
        'All Friends to Ghost Mode Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    all_friends_to_ghost_mode_uu = Metric(
        'all_friends_to_ghost_mode_uu',
        'All Friends to Ghost Mode Unique Users',
        dist='bin',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    custom_friends_to_all_friends_count = Metric(
        'custom_friends_to_all_friends_count',
        'Custom Friends to All Friends Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    custom_friends_to_all_friends_uu = Metric(
        'custom_friends_to_all_friends_uu',
        'Custom Friends to All Friends Unique Users',
        dist='bin',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    custom_friends_to_blacklist_mode_count = Metric(
        'custom_friends_to_blacklist_mode_count',
        'Custom Friends to Blacklist Mode Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    custom_friends_to_blacklist_mode_uu = Metric(
        'custom_friends_to_blacklist_mode_uu',
        'Custom Friends to Blacklist Mode Unique Users',
        dist='bin',
        daily=True,
        cumulative=True
    )

    custom_friends_to_ghost_mode_count = Metric(
        'custom_friends_to_ghost_mode_count',
        'Custom Friends to Ghost Mode Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    custom_friends_to_ghost_mode_uu = Metric(
        'custom_friends_to_ghost_mode_uu',
        'Custom Friends to Ghost Mode Unique Users',
        dist='bin',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    blacklist_mode_to_all_friends_count = Metric(
        'blacklist_mode_to_all_friends_count',
        'Blacklist Mode to All Friends Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    blacklist_mode_to_all_friends_uu = Metric(
        'blacklist_mode_to_all_friends_uu',
        'Blacklist Mode to All Friends Unique Users',
        dist='bin',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    blacklist_mode_to_custom_friends_count = Metric(
        'blacklist_mode_to_custom_friends_count',
        'Blacklist Mode to Custom Friends Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    blacklist_mode_to_custom_friends_uu = Metric(
        'blacklist_mode_to_custom_friends_uu',
        'Blacklist Mode to Custom Friends Unique Users',
        dist='bin',
        daily=True,
        cumulative=True
    )

    blacklist_mode_to_ghost_mode_count = Metric(
        'blacklist_mode_to_ghost_mode_count',
        'Blacklist Mode to Ghost Mode Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    blacklist_mode_to_ghost_mode_uu = Metric(
        'blacklist_mode_to_ghost_mode_uu',
        'Blacklist Mode to Ghost Mode Unique Users',
        dist='bin',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    ghost_mode_to_all_friends_count = Metric(
        'ghost_mode_to_all_friends_count',
        'Ghost Mode to All Friends Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    ghost_mode_to_all_friends_uu = Metric(
        'ghost_mode_to_all_friends_uu',
        'Ghost Mode to All Friends Unique Users',
        dist='bin',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    ghost_mode_to_custom_friends_count = Metric(
        'ghost_mode_to_custom_friends_count',
        'Ghost Mode to Custom Friends Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    ghost_mode_to_custom_friends_uu = Metric(
        'ghost_mode_to_custom_friends_uu',
        'Ghost Mode to Custom Friends Unique Users',
        dist='bin',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    ghost_mode_to_blacklist_mode_count = Metric(
        'ghost_mode_to_blacklist_mode_count',
        'Ghost Mode to Blacklist Mode Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    ghost_mode_to_blaclist_mode_uu = Metric(
        'ghost_mode_to_blaclist_mode_uu',
        'Ghost Mode to Blacklist Mode Unique Users',
        dist='bin',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )


    def sql_callable(start_date, end_date):
        sql = """
        SELECT
          TIMESTAMP_SUB( event_date, INTERVAL 8 HOUR) AS ts,
          ghost_user_id,
          
          SUM(CASE WHEN previous_location_sharing_setting='ALL_FRIENDS' AND location_sharing_setting='CUSTOM_FRIENDS' THEN 1 ELSE 0 END) AS all_friends_to_custom_friends_count,
          MAX(CASE WHEN previous_location_sharing_setting='ALL_FRIENDS' AND location_sharing_setting='CUSTOM_FRIENDS' THEN 1 ELSE 0 END) AS all_friends_to_custom_friends_uu,
          SUM(CASE WHEN previous_location_sharing_setting='ALL_FRIENDS' AND location_sharing_setting='BLACKLIST_MODE' THEN 1 ELSE 0 END) AS all_friends_to_blacklist_mode_count,
          MAX(CASE WHEN previous_location_sharing_setting='ALL_FRIENDS' AND location_sharing_setting='BLACKLIST_MODE' THEN 1 ELSE 0 END) AS all_friends_to_blacklist_mode_uu,
          SUM(CASE WHEN previous_location_sharing_setting='ALL_FRIENDS' AND location_sharing_setting='GHOST_MODE' THEN 1 ELSE 0 END) AS all_friends_to_ghost_mode_count,
          MAX(CASE WHEN previous_location_sharing_setting='ALL_FRIENDS' AND location_sharing_setting='GHOST_MODE' THEN 1 ELSE 0 END) AS all_friends_to_ghost_mode_uu,

          SUM(CASE WHEN previous_location_sharing_setting='CUSTOM_FRIENDS' AND location_sharing_setting='ALL_FRIENDS' THEN 1 ELSE 0 END) AS custom_friends_to_all_friends_count,
          MAX(CASE WHEN previous_location_sharing_setting='CUSTOM_FRIENDS' AND location_sharing_setting='ALL_FRIENDS' THEN 1 ELSE 0 END) AS custom_friends_to_all_friends_uu,
          SUM(CASE WHEN previous_location_sharing_setting='CUSTOM_FRIENDS' AND location_sharing_setting='BLACKLIST_MODE' THEN 1 ELSE 0 END) AS custom_friends_to_blacklist_mode_count,
          MAX(CASE WHEN previous_location_sharing_setting='CUSTOM_FRIENDS' AND location_sharing_setting='BLACKLIST_MODE' THEN 1 ELSE 0 END) AS custom_friends_to_blacklist_mode_uu,
          SUM(CASE WHEN previous_location_sharing_setting='CUSTOM_FRIENDS' AND location_sharing_setting='GHOST_MODE' THEN 1 ELSE 0 END) AS custom_friends_to_ghost_mode_count,
          MAX(CASE WHEN previous_location_sharing_setting='CUSTOM_FRIENDS' AND location_sharing_setting='GHOST_MODE' THEN 1 ELSE 0 END) AS custom_friends_to_ghost_mode_uu,
          
          SUM(CASE WHEN previous_location_sharing_setting='BLACKLIST_MODE' AND location_sharing_setting='ALL_FRIENDS' THEN 1 ELSE 0 END) AS blacklist_mode_to_all_friends_count,
          MAX(CASE WHEN previous_location_sharing_setting='BLACKLIST_MODE' AND location_sharing_setting='ALL_FRIENDS' THEN 1 ELSE 0 END) AS blacklist_mode_to_all_friends_uu,
          SUM(CASE WHEN previous_location_sharing_setting='BLACKLIST_MODE' AND location_sharing_setting='CUSTOM_FRIENDS' THEN 1 ELSE 0 END) AS blacklist_mode_to_custom_friends_count,
          MAX(CASE WHEN previous_location_sharing_setting='BLACKLIST_MODE' AND location_sharing_setting='CUSTOM_FRIENDS' THEN 1 ELSE 0 END) AS blacklist_mode_to_custom_friends_uu,
          SUM(CASE WHEN previous_location_sharing_setting='BLACKLIST_MODE' AND location_sharing_setting='GHOST_MODE' THEN 1 ELSE 0 END) AS blacklist_mode_to_ghost_mode_count,
          MAX(CASE WHEN previous_location_sharing_setting='BLACKLIST_MODE' AND location_sharing_setting='GHOST_MODE' THEN 1 ELSE 0 END) AS blacklist_mode_to_ghost_mode_uu,
          
          SUM(CASE WHEN previous_location_sharing_setting='GHOST_MODE' AND location_sharing_setting='ALL_FRIENDS' THEN 1 ELSE 0 END) AS ghost_mode_to_all_friends_count,
          MAX(CASE WHEN previous_location_sharing_setting='GHOST_MODE' AND location_sharing_setting='ALL_FRIENDS' THEN 1 ELSE 0 END) AS ghost_mode_to_all_friends_uu,
          SUM(CASE WHEN previous_location_sharing_setting='GHOST_MODE' AND location_sharing_setting='CUSTOM_FRIENDS' THEN 1 ELSE 0 END) AS ghost_mode_to_custom_friends_count,
          MAX(CASE WHEN previous_location_sharing_setting='GHOST_MODE' AND location_sharing_setting='CUSTOM_FRIENDS' THEN 1 ELSE 0 END) AS ghost_mode_to_custom_friends_uu,
          SUM(CASE WHEN previous_location_sharing_setting='GHOST_MODE' AND location_sharing_setting='BLACKLIST_MODE' THEN 1 ELSE 0 END) AS ghost_mode_to_blacklist_mode_count,
          MAX(CASE WHEN previous_location_sharing_setting='GHOST_MODE' AND location_sharing_setting='BLACKLIST_MODE' THEN 1 ELSE 0 END) AS ghost_mode_to_blaclist_mode_uu  

        FROM `sc-analytics.report_maps.location_sharing_setting_updates_*`
        WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
        GROUP BY
          1,
          2
        """.format(
            start=start_date,
            end=end_date
        )
        return sql

    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[
            all_friends_to_custom_friends_count,
            all_friends_to_custom_friends_uu,
            all_friends_to_blacklist_mode_count,
            all_friends_to_blacklist_mode_uu,
            all_friends_to_ghost_mode_count,
            all_friends_to_ghost_mode_uu,
            custom_friends_to_all_friends_count,
            custom_friends_to_all_friends_uu,
            custom_friends_to_blacklist_mode_count,
            custom_friends_to_blacklist_mode_uu,
            custom_friends_to_ghost_mode_count,
            custom_friends_to_ghost_mode_uu,
            blacklist_mode_to_all_friends_count,
            blacklist_mode_to_all_friends_uu,
            blacklist_mode_to_custom_friends_count,
            blacklist_mode_to_custom_friends_uu,
            blacklist_mode_to_ghost_mode_count,
            blacklist_mode_to_ghost_mode_uu,
            ghost_mode_to_all_friends_count,
            ghost_mode_to_all_friends_uu,
            ghost_mode_to_custom_friends_count,
            ghost_mode_to_custom_friends_uu,
            ghost_mode_to_blacklist_mode_count,
            ghost_mode_to_blaclist_mode_uu
        ],
        name="map_location_sharing_setting_shift",
        bq_dialect='standard'
    )

    return mt


def map_location_sharing_list_size(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    custom_friend_count = Metric(
        'custom_friend_count',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE,
    )

    blacklist_friend_count = Metric(
        'blacklist_friend_count',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE,
    )

    def sql_callable(start_date, end_date):
        sql = """
        SELECT
          TIMESTAMP_SUB( event_date, INTERVAL 8 HOUR) AS ts,
          ghost_user_id,
          CASE WHEN location_sharing_setting = 'CUSTOM_FRIENDS' THEN custom_friend_count ELSE NULL END AS custom_friend_count,
          CASE WHEN location_sharing_setting = 'BLACKLIST_MODE' THEN blacklist_friend_count ELSE NULL END AS blacklist_friend_count,
        FROM `sc-analytics.report_maps.location_sharing_setting_20*`
        WHERE _TABLE_SUFFIX BETWEEN '{start_trunc}' AND '{end_trunc}'
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
        )
        return sql

    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[
            custom_friend_count,
            blacklist_friend_count
        ],
        inner_join_with_mapping=False,
        quantile_metrics=True,
        name="map_location_sharing_list_size",
        bq_dialect='standard'
    )

    return mt

def map_background_location_coverage(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """

    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    background_location_coverage_count = Metric(
        'background_location_coverage_count',
        'Background Location Coverage Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE,
    )

    background_location_coverage_uu = Metric(
        'background_location_coverage_uu',
        'Background Location Coverage UU',
        dist='bin',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE,
    )

    def sql_callable(start_date, end_date):
        sql = """
        SELECT 
          TIMESTAMP_TRUNC(_PARTITIONTIME, DAY) AS ts,
          ghost_id AS ghost_user_id,
          COUNT(DISTINCT encrypted_tile_id) AS background_location_coverage_count,
          IF(COUNT(1)>0,1,0) AS background_location_coverage_uu,
        FROM 
          `valis-location-history.location_coverage.location_coverage_metrics` 
        WHERE 
          TIMESTAMP_TRUNC(_PARTITIONTIME, DAY) BETWEEN PARSE_TIMESTAMP('%Y%m%d', '{start}') AND PARSE_TIMESTAMP('%Y%m%d', '{end}')
        GROUP BY 
          1, 2
        """.format(
            start=start_date,
            end=end_date
        )
        return sql

    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[
            background_location_coverage_count,
            background_location_coverage_uu
        ],
        name="map_background_location_coverage",
        bq_dialect='standard'
    )

    return mt

def map_time_metrics(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """    
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    
    map_session_count = Metric(
        'map_session_count',
        'Total Map Session Count',
        dist='cont',
        daily=True,
        cumulative=True
    )
    
    map_no_action_session_count = Metric(
        'map_no_action_session_count',
        'No Action Session Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    map_no_action_session_user = Metric(
        'map_no_action_session_user',
        'No Action Session Unique User',
        dist='bin',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    map_no_action_session_ratio = Metric(
        col='no_action_session_ratio', 
        numerator='map_no_action_session_count', 
        denominator='map_session_count', 
        dist='ratio', 
        desired_direction=NEGATIVE
    )
    
    map_browse_only_session_count = Metric(
        'map_browse_only_session_count',
        'Browsing Session Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    map_browse_only_session_user = Metric(
        'map_browse_only_session_user',
        'Browsing Session Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )

    map_browse_only_session_ratio = Metric(
        col='browing_session_ratio', 
        numerator='map_browse_only_session_count', 
        denominator='map_session_count', 
        dist='ratio'
    )
    
    map_action_session_count = Metric(
        'map_action_session_count',
        'Action Session Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    map_action_session_user = Metric(
        'map_action_session_user',
        'Action Session Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )

    map_action_session_ratio = Metric(
        col='action_session_ratio', 
        numerator='map_action_session_count', 
        denominator='map_session_count', 
        dist='ratio'
    )

    map_quality_session_count = Metric(
        'map_quality_session_count',
        'Quality Session Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    map_quality_session_user = Metric(
        'map_quality_session_user',
        'Quality Session Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )

    map_quality_session_ratio = Metric(
        col='quality_session_ratio', 
        numerator='map_quality_session_count', 
        denominator='map_session_count', 
        dist='ratio'
    )    

    map_non_quality_session_count = Metric(
        'map_non_quality_session_count',
        'Non Quality Session Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    map_non_quality_session_user = Metric(
        'map_non_quality_session_user',
        'Non Quality Session Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )

    map_non_quality_session_ratio = Metric(
        col='non_quality_session_ratio', 
        numerator='map_non_quality_session_count', 
        denominator='map_session_count', 
        dist='ratio'
    ) 

    def sql_callable(start_date, end_date):
        sql = """
        SELECT
            TIMESTAMP_SUB( event_date, INTERVAL 8 HOUR) AS ts,
            ghost_user_id,

            SUM(IF(session_time > 0,1,0)) AS map_session_count,
            
            SUM(IF(session_time > 0 AND tap_story_snap + tap_friend_view + tap_status_button  + tap_explore_button + tap_map_tray + tap_map_compas + browse_action_count=0,1,0)) AS map_no_action_session_count,
            MAX(IF(session_time > 0 AND tap_story_snap + tap_friend_view + tap_status_button  + tap_explore_button + tap_map_tray + tap_map_compas + browse_action_count=0,1,0)) AS map_no_action_session_user,
            
            SUM(IF(session_time > 0 AND tap_story_snap + tap_friend_view + tap_status_button  + tap_explore_button + tap_map_tray + tap_map_compas =0 AND browse_action_count > 0,1,0)) AS map_browse_only_session_count,
            MAX(IF(session_time > 0 AND tap_story_snap + tap_friend_view + tap_status_button  + tap_explore_button + tap_map_tray + tap_map_compas =0 AND browse_action_count > 0,1,0)) AS map_browse_only_session_user,
            
            SUM(IF(session_time > 0 AND tap_story_snap + tap_friend_view + tap_status_button  + tap_explore_button + tap_map_tray + tap_map_compas > 0,1,0)) AS map_action_session_count,
            MAX(IF(session_time > 0 AND tap_story_snap + tap_friend_view + tap_status_button  + tap_explore_button + tap_map_tray + tap_map_compas > 0,1,0)) AS map_action_session_user,

            SUM(IF(session_time >= 2 AND tap_story_snap + tap_friend_view + tap_status_button  + tap_explore_button + tap_map_tray + tap_map_compas + browse_action_count > 0,1,0)) AS map_quality_session_count,
            MAX(IF(session_time >= 2 AND tap_story_snap + tap_friend_view + tap_status_button  + tap_explore_button + tap_map_tray + tap_map_compas + browse_action_count > 0,1,0)) AS map_quality_session_user,

            SUM(IF(session_time < 2 OR tap_story_snap + tap_friend_view + tap_status_button  + tap_explore_button + tap_map_tray + tap_map_compas +  browse_action_count = 0,1,0)) AS map_non_quality_session_count,
            MAX(IF(session_time < 2 OR tap_story_snap + tap_friend_view + tap_status_button  + tap_explore_button + tap_map_tray + tap_map_compas +  browse_action_count = 0,1,0)) AS map_non_quality_session_user
            
        FROM `sc-analytics.report_maps.maps_session_interactions_*`
        WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}' 
        AND
            session_time IS NOT NULL
        AND 
            browse_action_count IS NOT NULL
        AND 
            session_time>0
        GROUP BY
            1,
            2
        """.format(
            start=start_date,
            end=end_date
            )
        return sql
    
    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[
            map_session_count,            
            map_no_action_session_count,
            map_no_action_session_user,
            map_no_action_session_ratio,
            map_browse_only_session_count,
            map_browse_only_session_user,
            map_browse_only_session_ratio,
            map_action_session_count,
            map_action_session_user,
            map_action_session_ratio,
            map_quality_session_count,
            map_quality_session_user,
            map_quality_session_ratio,
            map_non_quality_session_count,
            map_non_quality_session_user,
            map_non_quality_session_ratio
        ],
        name="map_time_metrics",
        bq_dialect='standard'
    )
    
    return mt

def map_tray_engagement(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    map_status_open_uu = Metric(
        'map_status_open_uu',
        'Me Tray Open Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )
    
    map_status_open_count = Metric(
        'map_status_open_count',
        'Me Tray Open Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    map_status_action_uu = Metric(
        'map_status_action_uu',
        'Me Tray Action Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )

    map_status_action_count = Metric(
        'map_status_action_count',
        'Me Tray Action Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    map_status_set_uu = Metric(
        'map_status_set_uu',
        'Actionmoji Set Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )

    map_status_set_count = Metric(
        'map_status_set_count',
        'Actionmoji Set Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    map_explore_open_uu = Metric(
        'map_explore_open_uu',
        'Friend Tray Open Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )

    map_explore_open_count = Metric(
        'map_explore_open_count',
        'Friend Tray Open Count',
        dist='cont',
        daily=True,
        cumulative=True
    )
    
    map_visual_tray_open_uu = Metric(
        'map_visual_tray_open_uu',
        'Visual Tray Open Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )

    map_visual_tray_open_count = Metric(
        'map_visual_tray_open_count',
        'Visual Tray Open Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    map_visual_tray_action_uu = Metric(
        'map_visual_tray_action_uu',
        'Visual Tray Action Unique User',
        dist='bin',
        daily=True,
        cumulative=True
    )

    map_visual_tray_action_count = Metric(
        'map_visual_tray_action_count',
        'Visual Tray Action Count',
        dist='cont',
        daily=True,
        cumulative=True
    )
    
    def sql_callable(start_date, end_date):
        sql = """
        SELECT
          TIMESTAMP_SUB( event_date, INTERVAL 8 HOUR) AS ts,
          ghost_user_id,
          SUM(MAP_STATUS_OPEN) AS map_status_open_count,
          IF(SUM(MAP_STATUS_OPEN)>0,1,0) AS map_status_open_uu,
          SUM(MAP_STATUS_ACTION) AS map_status_action_count,
          IF(SUM(MAP_STATUS_ACTION)>0,1,0) AS map_status_action_uu,
          SUM(MAP_STATUS_OPTION_SET) AS map_status_set_count,
          IF(SUM(MAP_STATUS_OPTION_SET)>0,1,0) AS map_status_set_uu,
          SUM(MAP_EXPLORE_OPEN_COUNT) AS map_explore_open_count,
          MAX(MAP_EXPLORE_OPEN_UU) AS map_explore_open_uu,

          SUM(VISUAL_TRAY_OPEN_COUNT) AS map_visual_tray_open_count,
          IF(SUM(VISUAL_TRAY_OPEN_COUNT)>0,1,0) AS map_visual_tray_open_uu,
          SUM(VISUAL_TRAY_ACTION_COUNT) AS map_visual_tray_action_count,
          IF(SUM(VISUAL_TRAY_ACTION_COUNT)>0,1,0) AS map_visual_tray_action_uu,

        FROM `sc-analytics.report_maps.maps_dau_user_country_*`
        WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}' 
            AND APP_APPLICATION_OPEN_UU=1
        GROUP BY
            1,
            2
        """.format(
            start=start_date,
            end=end_date
            )
        return sql
        
    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[
            map_status_open_count,
            map_status_open_uu,
            map_status_action_count,
            map_status_action_uu,
            map_status_set_count,
            map_status_set_uu,
            map_explore_open_count,
            map_explore_open_uu,
            map_visual_tray_open_count,
            map_visual_tray_open_uu,
            map_visual_tray_action_count,
            map_visual_tray_action_uu
        ],
        name="map_tray_engagement",
        bq_dialect='standard'
    )

    return mt


def map_performance_metrics(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """

    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    latency_map_ready = Metric(
        'latency_map_ready',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE,
    )

    latency_friend_load = Metric(
        'latency_friend_load',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE,
    )

    latency_ttp = Metric(
        'latency_ttp',
        dist='quantile',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE,
    )

    def sql_callable(start_date, end_date):
        sql = """
        SELECT
          TIMESTAMP_SUB( event_date, INTERVAL 8 HOUR) AS ts,
          ghost_user_id,
          case when event_name='MAP_MAP_READY' then latency_ms else NULL end as latency_map_ready,
          case when event_name='MAP_MAP_FRIEND_LOAD' then latency_ms else NULL end as latency_friend_load,
          case when event_name='MAP_TAP_TO_PLAY_LATENCY' then latency_ms else NULL end as latency_ttp
        FROM `sc-analytics.report_maps.daily_maps_ops_map_events_*`
        WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}' 
        """.format(
            start=start_date,
            end=end_date
            )
        return sql
        
    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[
            latency_map_ready,
            latency_friend_load,
            latency_ttp
       ],
       inner_join_with_mapping=False,
       quantile_metrics=True,
       name="map_performance_metrics",
       bq_dialect='standard'
    )

    return mt
    

def map_off_platform_share(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]
    
    map_off_platform_share_count = Metric(
        'map_off_platform_share_count',
        'Map Off Platform Share Total Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    map_off_platform_share_uu = Metric(
        'map_off_platform_share_uu',
        'Map Off Platform Share Unique User',
        dist='bin',
        daily=True,
        cumulative=True,
    )
    
    map_place_off_platform_share_count = Metric(
        'map_place_off_platform_share_count',
        'Map Place Off Platform Share Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    map_place_off_platform_share_uu = Metric(
        'map_place_off_platform_share_uu',
        'Map Place Off Platform Share Unique User',
        dist='bin',
        daily=True,
        cumulative=True,
    )
    
    map_snap_off_platform_share_count = Metric(
        'map_snap_off_platform_share_count',
        'Map Snap Off Platform Share Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    map_snap_off_platform_share_uu = Metric(
        'map_snap_off_platform_share_uu',
        'Map Snap Off Platform Share Unique User',
        dist='bin',
        daily=True,
        cumulative=True,
    )
    
    def sql_callable(start_date, end_date):
        sql = """
        SELECT
          TIMESTAMP(event_date) AS ts,
          ghost_user_id,
          
          COUNT(1) AS map_off_platform_share_count,
          IF(COUNT(1)>0,1,0) AS map_off_platform_share_uu,
          
          SUM(CASE WHEN STARTS_WITH(deep_link_url, 'https://www.snapchat.com/map/place') THEN 1 ELSE 0 END) AS map_place_off_platform_share_count,
          IF(SUM(CASE WHEN STARTS_WITH(deep_link_url, 'https://www.snapchat.com/map/place') THEN 1 ELSE 0 END)>0,1,0) AS map_place_off_platform_share_uu,
          
          SUM(CASE WHEN STARTS_WITH(deep_link_url, 'https://map.snapchat.com/ttp/snap') THEN 1 ELSE 0 END) AS map_snap_off_platform_share_count,
          IF(SUM(CASE WHEN STARTS_WITH(deep_link_url, 'https://map.snapchat.com/ttp/snap') THEN 1 ELSE 0 END)>0,1,0) AS map_snap_off_platform_share_uu,
          
        FROM `sc-analytics.report_growth.off_platform_share_20*`
        WHERE
          _TABLE_SUFFIX BETWEEN '{start_trunc}' AND '{end_trunc}' AND
          deep_link_source = 'MAP'
        GROUP BY
          1,
          2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            )

        return sql

    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[
            map_off_platform_share_count,
            map_off_platform_share_uu,
            map_place_off_platform_share_count,
            map_place_off_platform_share_uu,
            map_snap_off_platform_share_count,
            map_snap_off_platform_share_uu
        ],
        name="place_profile_off_platform_share",
        bq_dialect='standard'
    )

    return mt


def map_zoom_level_metrics(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    
    map_user_view_count_zoom_0_to_9 = Metric(
        'map_user_view_count_zoom_0_to_9',
        'Map User View Count (Zoom Level 0 to 9)',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    map_user_view_count_zoom_10_to_12 = Metric(
        'map_user_view_count_zoom_10_to_12',
        'Map User View Count (Zoom Level 10 to 12)',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    map_user_view_count_zoom_13_to_14 = Metric(
        'map_user_view_count_zoom_13_to_14',
        'Map User View Count (Zoom Level 13 to 14)',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    map_user_view_count_zoom_15_to_20 = Metric(
        'map_user_view_count_zoom_15_to_20',
        'Map User View Count (Zoom Level 15 to 20)',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    place_profile_open_count_zoom_0_to_9 = Metric(
        'place_profile_open_count_zoom_0_to_9',
        'Place Profile Open Count (Zoom Level 0 to 9)',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    place_profile_open_count_zoom_10_to_12 = Metric(
        'place_profile_open_count_zoom_10_to_12',
        'Place Profile Open Count (Zoom Level 10 to 12)',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    place_profile_open_count_zoom_13_to_14 = Metric(
        'place_profile_open_count_zoom_13_to_14',
        'Place Profile Open Count (Zoom Level 13 to 14)',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    place_profile_open_count_zoom_15_to_20 = Metric(
        'place_profile_open_count_zoom_15_to_20',
        'Place Profile Open Count (Zoom Level 15 to 20)',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    
    map_story_tap_count_zoom_0_to_9 = Metric(
        'map_story_tap_count_zoom_0_to_9',
        'Map Story Tap Count (Zoom Level 0 to 9)',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    map_story_tap_count_zoom_10_to_12 = Metric(
        'map_story_tap_count_zoom_10_to_12',
        'Map Story Tap Count (Zoom Level 10 to 12)',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    map_story_tap_count_zoom_13_to_14 = Metric(
        'map_story_tap_count_zoom_13_to_14',
        'Map Story Tap Count (Zoom Level 13 to 14)',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    map_story_tap_count_zoom_15_to_20 = Metric(
        'map_story_tap_count_zoom_15_to_20',
        'Map Story Tap Count (Zoom Level 15 to 20)',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    
    def sql_callable(start_date, end_date):
        sql = """
        SELECT
          TIMESTAMP_SUB(event_date, INTERVAL 8 HOUR) as ts,
          ghost_user_id,
          
          SUM(CASE WHEN event = 'MAP_USER_VIEW' THEN IFNULL(zoom_0, 0)+IFNULL(zoom_1, 0)+IFNULL(zoom_2, 0)+IFNULL(zoom_3, 0)+IFNULL(zoom_4, 0)+IFNULL(zoom_5, 0)+IFNULL(zoom_6, 0)+IFNULL(zoom_7, 0)+IFNULL(zoom_8, 0)+IFNULL(zoom_9, 0) ELSE 0 END) AS map_user_view_count_zoom_0_to_9,
          SUM(CASE WHEN event = 'MAP_USER_VIEW' THEN IFNULL(zoom_10, 0)+IFNULL(zoom_11, 0)+IFNULL(zoom_12, 0) ELSE 0 END) AS map_user_view_count_zoom_10_to_12,
          SUM(CASE WHEN event = 'MAP_USER_VIEW' THEN IFNULL(zoom_13, 0)+IFNULL(zoom_14, 0) ELSE 0 END) AS map_user_view_count_zoom_13_to_14,
          SUM(CASE WHEN event = 'MAP_USER_VIEW' THEN IFNULL(zoom_15, 0)+IFNULL(zoom_16, 0)+IFNULL(zoom_17, 0)+IFNULL(zoom_18, 0)+IFNULL(zoom_19, 0)+IFNULL(zoom_20, 0) ELSE 0 END) AS map_user_view_count_zoom_15_to_20,
          
          SUM(CASE WHEN event = 'PLACES_PROFILE_OPEN' THEN IFNULL(zoom_0, 0)+IFNULL(zoom_1, 0)+IFNULL(zoom_2, 0)+IFNULL(zoom_3, 0)+IFNULL(zoom_4, 0)+IFNULL(zoom_5, 0)+IFNULL(zoom_6, 0)+IFNULL(zoom_7, 0)+IFNULL(zoom_8, 0)+IFNULL(zoom_9, 0) ELSE 0 END) AS place_profile_open_count_zoom_0_to_9,
          SUM(CASE WHEN event = 'PLACES_PROFILE_OPEN' THEN IFNULL(zoom_10, 0)+IFNULL(zoom_11, 0)+IFNULL(zoom_12, 0) ELSE 0 END) AS place_profile_open_count_zoom_10_to_12,
          SUM(CASE WHEN event = 'PLACES_PROFILE_OPEN' THEN IFNULL(zoom_13, 0)+IFNULL(zoom_14, 0) ELSE 0 END) AS place_profile_open_count_zoom_13_to_14,
          SUM(CASE WHEN event = 'PLACES_PROFILE_OPEN' THEN IFNULL(zoom_15, 0)+IFNULL(zoom_16, 0)+IFNULL(zoom_17, 0)+IFNULL(zoom_18, 0)+IFNULL(zoom_19, 0)+IFNULL(zoom_20, 0) ELSE 0 END) AS place_profile_open_count_zoom_15_to_20,
          
          SUM(CASE WHEN event = 'MAP_TTP_ACTION' THEN IFNULL(zoom_0, 0)+IFNULL(zoom_1, 0)+IFNULL(zoom_2, 0)+IFNULL(zoom_3, 0)+IFNULL(zoom_4, 0)+IFNULL(zoom_5, 0)+IFNULL(zoom_6, 0)+IFNULL(zoom_7, 0)+IFNULL(zoom_8, 0)+IFNULL(zoom_9, 0) ELSE 0 END) AS map_story_tap_count_zoom_0_to_9,
          SUM(CASE WHEN event = 'MAP_TTP_ACTION' THEN IFNULL(zoom_10, 0)+IFNULL(zoom_11, 0)+IFNULL(zoom_12, 0) ELSE 0 END) AS map_story_tap_count_zoom_10_to_12,
          SUM(CASE WHEN event = 'MAP_TTP_ACTION' THEN IFNULL(zoom_13, 0)+IFNULL(zoom_14, 0) ELSE 0 END) AS map_story_tap_count_zoom_13_to_14,
          SUM(CASE WHEN event = 'MAP_TTP_ACTION' THEN IFNULL(zoom_15, 0)+IFNULL(zoom_16, 0)+IFNULL(zoom_17, 0)+IFNULL(zoom_18, 0)+IFNULL(zoom_19, 0)+IFNULL(zoom_20, 0) ELSE 0 END) AS map_story_tap_count_zoom_15_to_20,

        FROM `sc-analytics.report_maps.maps_action_zoom_interaction_*`
        WHERE
          _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
        GROUP BY
          1,
          2
        """.format(
            start=start_date,
            end=end_date
            )

        return sql

    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[
            map_user_view_count_zoom_0_to_9,
            map_user_view_count_zoom_10_to_12,
            map_user_view_count_zoom_13_to_14,
            map_user_view_count_zoom_15_to_20,
            place_profile_open_count_zoom_0_to_9,
            place_profile_open_count_zoom_10_to_12,
            place_profile_open_count_zoom_13_to_14,
            place_profile_open_count_zoom_15_to_20,
            map_story_tap_count_zoom_0_to_9,
            map_story_tap_count_zoom_10_to_12,
            map_story_tap_count_zoom_13_to_14,
            map_story_tap_count_zoom_15_to_20
        ],
        name="map_zoom_level_metrics",
        bq_dialect='standard'
    )

    return mt


def map_live_location_sharing(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """

    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    indefinite_live_location_session_start_count = Metric(
        'indefinite_live_location_session_start_count',
        'Indefinite Live Location Session Start Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    indefinite_live_location_session_start_uu = Metric(
        'indefinite_live_location_session_start_uu',
        'Indefinite Live Location Session Start Unique User',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    indefinite_live_location_session_end_count = Metric(
        'indefinite_live_location_session_end_count',
        'Indefinite Live Location Session End Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    indefinite_live_location_session_end_uu = Metric(
        'indefinite_live_location_session_end_uu',
        'Indefinite Live Location Session End Unique User',
        dist='bin',
        daily=True,
        cumulative=True,
    )
    
    live_location_notification_count = Metric(
        'live_location_notification_count',
        'Live Location Notification Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    live_location_notification_success_count = Metric(
        'live_location_notification_success_count',
        'Live Location Notification (Success) Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    live_location_notification_fail_no_background_permissions_count = Metric(
        'live_location_notification_fail_no_background_permissions_count',
        'Live Location Notification (Fail No Background Permission) Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    live_location_notification_fail_no_precise_location_permissions_count = Metric(
        'live_location_notification_fail_no_precise_location_permissions_count',
        'Live Location Notification (Fail No Precise Location Permissions) Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    live_location_notification_success_rate = Metric(
        col='live_location_notification_success_rate',
        numerator='live_location_notification_success_count',
        denominator='live_location_notification_count',
        dist='ratio'
    )
    
    live_location_notification_fail_no_background_permissions_rate = Metric(
        col='live_location_notification_fail_no_background_permissions_rate',
        numerator='live_location_notification_fail_no_background_permissions_count',
        denominator='live_location_notification_count',
        dist='ratio'
    )
    
    live_location_notification_fail_no_precise_location_permissions_rate = Metric(
        col='live_location_notification_fail_no_precise_location_permissions_rate',
        numerator='live_location_notification_fail_no_precise_location_permissions_count',
        denominator='live_location_notification_count',
        dist='ratio'
    )


    def sql_callable(start_date, end_date):
        sql = """
        WITH live_location_session AS (
            SELECT
              TIMESTAMP(parse_datetime('%Y%m%d %H:%M:%S', CONCAT(_TABLE_SUFFIX, ' 00:00:00')), 'UTC') AS ts,
              ghost_user_id,
              COUNT(DISTINCT CASE WHEN action_name = 'START_LIVE_LOCATION_SHARING_SESSION' THEN LOWER(map_live_location_sharing_session_id) ELSE NULL END) AS indefinite_live_location_session_start_count,
              IF(SUM(CASE WHEN action_name = 'START_LIVE_LOCATION_SHARING_SESSION' THEN 1 ELSE 0 END)>0,1,0) AS indefinite_live_location_session_start_uu,
              COUNT(DISTINCT CASE WHEN action_name = 'END_LIVE_LOCATION_SHARING_SESSION' THEN LOWER(map_live_location_sharing_session_id) ELSE NULL END) AS indefinite_live_location_session_end_count,
              IF(SUM(CASE WHEN action_name = 'END_LIVE_LOCATION_SHARING_SESSION' THEN 1 ELSE 0 END)>0,1,0) AS indefinite_live_location_session_end_uu,
            FROM `sc-analytics.report_maps.maps_live_location_sharing_events_*`
            WHERE
              _TABLE_SUFFIX between '{start}' and '{end}' AND
              event_name = 'MAP_LIVE_LOCATION_SHARING' AND
              share_duration_ms = -1
            GROUP BY 1, 2
        ),
        
        live_location_notification AS (
            SELECT
              TIMESTAMP_SUB(event_date, INTERVAL 8 HOUR) as ts,
              ghost_user_id,
              COUNT(DISTINCT notification_id) AS live_location_notification_count,
              COUNT(DISTINCT CASE WHEN outcome = 'SUCCESS' THEN notification_id ELSE NULL END) AS live_location_notification_success_count,
              COUNT(DISTINCT CASE WHEN outcome = 'FAIL_NO_BACKGROUND_LOCATION_PERMISSIONS' THEN notification_id ELSE NULL END) AS live_location_notification_fail_no_background_permissions_count,
              COUNT(DISTINCT CASE WHEN outcome = 'FAIL_NO_PRECISE_LOCATION_PERMISSIONS' THEN notification_id ELSE NULL END) AS live_location_notification_fail_no_precise_location_permissions_count
            FROM `sc-analytics.report_maps.maps_live_location_sharing_notification_20*`
            WHERE
              _TABLE_SUFFIX BETWEEN '{start_trunc}' AND '{end_trunc}'
            GROUP BY 1, 2
        )
        
        SELECT
          COALESCE(a.ts, b.ts) AS ts,
          COALESCE(a.ghost_user_id, b.ghost_user_id) AS ghost_user_id,
          IFNULL(a.indefinite_live_location_session_start_count,0) AS indefinite_live_location_session_start_count,
          IFNULL(a.indefinite_live_location_session_start_uu,0) AS indefinite_live_location_session_start_uu,
          IFNULL(a.indefinite_live_location_session_end_count,0) AS indefinite_live_location_session_end_count,
          IFNULL(a.indefinite_live_location_session_end_uu,0) AS indefinite_live_location_session_end_uu,
          IFNULL(b.live_location_notification_count,0) AS live_location_notification_count,
          IFNULL(b.live_location_notification_success_count,0) AS live_location_notification_success_count,
          IFNULL(b.live_location_notification_fail_no_background_permissions_count,0) AS live_location_notification_fail_no_background_permissions_count,
          IFNULL(b.live_location_notification_fail_no_precise_location_permissions_count,0) AS live_location_notification_fail_no_precise_location_permissions_count
        FROM
          live_location_session a
        FULL OUTER JOIN
          live_location_notification b
        ON
          a.ts = b.ts AND a.ghost_user_id = b.ghost_user_id
        
        """.format(
            start=start_date,
            end=end_date,
            start_trunc=start_trunc,
            end_trunc=end_trunc
            )
        return sql
        
    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[
            indefinite_live_location_session_start_count,
            indefinite_live_location_session_start_uu,
            indefinite_live_location_session_end_count,
            indefinite_live_location_session_end_uu,
            live_location_notification_count,
            live_location_notification_success_count,
            live_location_notification_fail_no_background_permissions_count,
            live_location_notification_fail_no_precise_location_permissions_count,
            live_location_notification_success_rate,
            live_location_notification_fail_no_background_permissions_rate,
            live_location_notification_fail_no_precise_location_permissions_rate
       ],
       name="map_live_location_sharing",
       bq_dialect='standard'
    )

    return mt



def map_friend_focus_view(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """

    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')


    friend_focus_view_session_count = Metric(
        'friend_focus_view_session_count',
        'Friend Focus View Session Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    friend_focus_view_action_session_count = Metric(
        'friend_focus_view_action_session_count',
        'Friend Focus View with Action Session Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    friend_focus_view_action_session_ctr = Metric(
        col='friend_focus_view_action_session_ctr',
        numerator='friend_focus_view_action_session_count',
        denominator='friend_focus_view_session_count',
        dist='ratio'
    )

    friend_focus_view_session_uu = Metric(
        'friend_focus_view_session_uu',
        'Friend Focus View Session Unique User',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    friend_focus_view_time = Metric(
        'friend_focus_view_time',
        'Friend Focus View Session Time (sec)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    tray_open_count = Metric(
        'tray_open_count',
        'Friend Focus View Tray Open Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    tray_cluster_open_count = Metric(
        'tray_cluster_open_count',
        '(Cluster) Friend Focus View Tray Open Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    tray_user_open_count = Metric(
        'tray_user_open_count',
        '(User) Friend Focus View Tray Open Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    avg_target_friend_count = Metric(
        'avg_target_friend_count',
        'Friend Focus View Avg Target Friend Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    avg_target_best_friend_count = Metric(
        'avg_target_best_friend_count',
        'Friend Focus View Avg Target Best Friend Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    tray_action_count = Metric(
        'tray_action_count',
        'Friend Focus View Tray Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    tray_cluster_action_count = Metric(
        'tray_cluster_action_count',
        '(Cluster) Friend Focus View Tray Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    tray_user_action_count = Metric(
        'tray_user_action_count',
        '(User) Friend Focus View Tray Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    share_live_location_tap = Metric(
        'share_live_location_tap',
        'Friend Focus View Share Live Location Tap',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    stop_live_location_tap = Metric(
        'stop_live_location_tap',
        'Friend Focus View Stop Live Location Tap',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE,
    )

    camera_tap = Metric(
        'camera_tap',
        'Friend Focus View Camera Tap',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    chat_tap = Metric(
        'chat_tap',
        'Friend Focus View Chat Tap',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    group_chat_tap = Metric(
        'group_chat_tap',
        'Friend Focus View Group Chat Tap',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    friend_tap = Metric(
        'friend_tap',
        'Friend Focus View Friend Tap',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    friend_story_tap = Metric(
        'friend_story_tap',
        'Friend Focus View Friend Story Tap',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    friend_profile_tap = Metric(
        'friend_profile_tap',
        'Friend Focus View Friend Profile Tap',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    update_bitmoji_tap = Metric(
        'update_bitmoji_tap',
        'Friend Focus View Update Bitmoji Tap',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    upsell_location_share_tap = Metric(
        'upsell_location_share_tap',
        'Friend Focus View Upsell Location Share Tap',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    directions_tap = Metric(
        'directions_tap',
        'Friend Focus View Directions Tap',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    def sql_callable(start_date, end_date):
        sql = """
                SELECT
                  event_date as ts,
                  ghost_user_id,
                  COUNT(1) AS friend_focus_view_session_count,
                  SUM(IF(tray_action_count>0,1,0)) AS friend_focus_view_action_session_count,
                  MAX(1) AS friend_focus_view_session_uu,
                  SUM(view_time_sec) as friend_focus_view_time,
                  sum(ifnull(tray_open_count,0)) tray_open_count,
                  sum(ifnull(tray_cluster_open_count,0)) tray_cluster_open_count,
                  sum(ifnull(tray_user_open_count,0)) tray_user_open_count,
                  avg(ifnull(target_friend_count,0)) avg_target_friend_count,
                  avg(ifnull(target_best_friend_count,0)) avg_target_best_friend_count,
                  sum(ifnull(tray_action_count,0)) tray_action_count,
                  sum(ifnull(tray_cluster_action_count,0)) tray_cluster_action_count,
                  sum(ifnull(tray_user_action_count,0)) tray_user_action_count,
                  sum(ifnull(share_live_location_tap,0)) share_live_location_tap,
                  sum(ifnull(stop_live_location_tap,0)) stop_live_location_tap,
                  sum(ifnull(camera_tap,0)) camera_tap,
                  sum(ifnull(chat_tap,0)) chat_tap,
                  sum(ifnull(group_chat_tap,0)) group_chat_tap,
                  sum(ifnull(friend_tap,0)) friend_tap,
                  sum(ifnull(friend_story_tap,0)) friend_story_tap,
                  sum(ifnull(friend_profile_tap,0)) friend_profile_tap,
                  sum(ifnull(update_bitmoji_tap,0)) update_bitmoji_tap,
                  sum(ifnull(upsell_location_share_tap,0)) upsell_location_share_tap,
                  sum(ifnull(walk_directions_tap,0)+ifnull(drive_directions_tap,0)+ifnull(more_directions_tap,0)) directions_tap,
                FROM
                  `sc-analytics.report_maps.maps_friend_focus_view_tray_interaction_*`
                WHERE
                  _TABLE_SUFFIX BETWEEN '{start}' 
                  AND '{end}'
                GROUP BY 
                  1,
                  2
        """.format(
            start=start_date,
            end=end_date
            )
        return sql
        
    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[
                friend_focus_view_session_count,
                friend_focus_view_action_session_count,
                friend_focus_view_action_session_ctr,
                friend_focus_view_session_uu,
                friend_focus_view_time,
                tray_open_count,
                tray_cluster_open_count,
                tray_user_open_count,
                avg_target_friend_count,
                avg_target_best_friend_count,
                tray_action_count,
                tray_cluster_action_count,
                tray_user_action_count,
                share_live_location_tap,
                stop_live_location_tap,
                camera_tap,
                chat_tap,
                group_chat_tap,
                friend_tap,
                friend_story_tap,
                friend_profile_tap,
                update_bitmoji_tap,
                upsell_location_share_tap,
                directions_tap
       ],
       name="map_friend_focus_view",
       bq_dialect='standard'
    )

    return mt

def map_notifications(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """

    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')


    map_notif_send = Metric(
        'map_notif_send',
        'Map Notification Send Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    map_notif_open = Metric(
        'map_notif_open',
        'Map Notification Open Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    map_notification_open_ctr = Metric(
        col='map_notification_open_ctr',
        numerator='map_notif_open',
        denominator='map_notif_send',
        dist='ratio'
    )

    map_notif_send_place_edits_added = Metric(
        'map_notif_send_place_edits_added',
        'Map Notification Send Count (Place Edits)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    map_notif_open_place_edits_added = Metric(
        'map_notif_open_place_edits_added',
        'Map Notification Open Count (Place Edits)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    map_notification_open_ctr_place_edits = Metric(
        col='map_notification_open_ctr_place_edits',
        numerator='map_notif_open_place_edits_added',
        denominator='map_notif_send_place_edits_added',
        dist='ratio'
    )

    map_notif_send_friend_travel_status = Metric(
        'map_notif_send_friend_travel_status',
        'Map Notification Send Count (Friend Travel Status)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    map_notif_open_friend_travel_status = Metric(
        'map_notif_open_friend_travel_status',
        'Map Notification Open Count (Friend Travel Status)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    map_notification_open_ctr_friend_travel_status = Metric(
        col='map_notification_open_ctr_friend_travel_status',
        numerator='map_notif_open_friend_travel_status',
        denominator='map_notif_send_friend_travel_status',
        dist='ratio'
    )

    map_notif_send_friend_back_in_town = Metric(
        'map_notif_send_friend_back_in_town',
        'Map Notification Send Count (Back in Town)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    map_notif_open_friend_back_in_town = Metric(
        'map_notif_open_friend_back_in_town',
        'Map Notification Open Count (Back in Town)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    map_notification_open_ctr_back_in_town = Metric(
        col='map_notification_open_ctr_back_in_town',
        numerator='map_notif_open_friend_back_in_town',
        denominator='map_notif_send_friend_back_in_town',
        dist='ratio'
    )

    chat_or_snap_send_10_min = Metric(
        'chat_or_snap_send_10_min',
        'Chat or Snap on Notif Open (<10 min)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    chat_or_snap_on_notif_open_10_min_ratio = Metric(
        col='chat_or_snap_on_notif_open_10_min_ratio',
        numerator='chat_or_snap_send_10_min',
        denominator='map_notif_open',
        dist='ratio'
    )

    chat_or_snap_send_10_min_friend_travel_status = Metric(
        'chat_or_snap_send_10_min_friend_travel_status',
        'Chat or Snap on Notif Open (<10 min, Friend Travel Status)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    chat_or_snap_on_notif_open_10_min_friend_travel_status_ratio = Metric(
        col='chat_or_snap_on_notif_open_10_min_friend_travel_status_ratio',
        numerator='chat_or_snap_send_10_min_friend_travel_status',
        denominator='map_notif_open_friend_travel_status',
        dist='ratio'
    )

    chat_or_snap_send_10_min_friend_back_in_town = Metric(
        'chat_or_snap_send_10_min_friend_back_in_town',
        'Chat or Snap on Notif Open (<10 min, Friend Back In Town)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    chat_or_snap_on_notif_open_10_min_friend_back_in_town_ratio = Metric(
        col='chat_or_snap_on_notif_open_10_min_friend_back_in_town_ratio',
        numerator='chat_or_snap_send_10_min_friend_back_in_town',
        denominator='map_notif_open_friend_back_in_town',
        dist='ratio'
    )

    chat_or_snap_send_same_day = Metric(
        'chat_or_snap_send_same_day',
        'Chat or Snap on Notif Open (Same Day)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    chat_or_snap_on_notif_open_same_day_ratio = Metric(
        col='chat_or_snap_on_notif_open_same_day_ratio',
        numerator='chat_or_snap_send_same_day',
        denominator='map_notif_open',
        dist='ratio'
    )

    chat_or_snap_send_same_day_friend_travel_status = Metric(
        'chat_or_snap_send_same_day_friend_travel_status',
        'Chat or Snap on Notif Open (Same Day, Friend Travel Status)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    chat_or_snap_on_notif_open_same_day_friend_travel_status_ratio = Metric(
        col='chat_or_snap_on_notif_open_same_day_friend_travel_status_ratio',
        numerator='chat_or_snap_send_same_day_friend_travel_status',
        denominator='map_notif_open_friend_travel_status',
        dist='ratio'
    )

    chat_or_snap_send_same_day_friend_back_in_town = Metric(
        'chat_or_snap_send_same_day_friend_back_in_town',
        'Chat or Snap on Notif Open (Same Day, Friend Back In Town)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    chat_or_snap_on_notif_open_same_day_friend_back_in_town_ratio = Metric(
        col='chat_or_snap_on_notif_open_same_day_friend_back_in_town_ratio',
        numerator='chat_or_snap_send_same_day_friend_back_in_town',
        denominator='map_notif_open_friend_back_in_town',
        dist='ratio'
    )

    def sql_callable(start_date, end_date):
        sql = """
                SELECT
                  TIMESTAMP(event_date) AS ts,
                  ghost_user_id,
                  COUNT(1) AS map_notif_send,
                  SUM(
                  IF
                    (app_open_ts IS NOT NULL,1,0)) AS map_notif_open,
                  SUM(
                  IF
                    (notif_type='MAP_EDITS_PLACE_ADDED',1,0)) map_notif_send_place_edits_added,
                  SUM(
                  IF
                    (notif_type='MAP_EDITS_PLACE_ADDED'
                      AND app_open_ts IS NOT NULL,1,0)) map_notif_open_place_edits_added,
                  SUM(
                  IF
                    (notif_type='MAP_FRIEND_TRAVEL_STATUS',1,0)) map_notif_send_friend_travel_status,
                  SUM(
                  IF
                    (notif_type='MAP_FRIEND_TRAVEL_STATUS'
                      AND app_open_ts IS NOT NULL,1,0)) map_notif_open_friend_travel_status,
                  SUM(
                  IF
                    (notif_type='MAP_FRIEND_TRAVEL_STATUS_BACK_IN_TOWN',1,0)) map_notif_send_friend_back_in_town,
                  SUM(
                  IF
                    (notif_type='MAP_FRIEND_TRAVEL_STATUS_BACK_IN_TOWN'
                      AND app_open_ts IS NOT NULL,1,0)) map_notif_open_friend_back_in_town,
                  SUM(
                  IF
                    (chat_or_snap_send_count_10_min>0,1,0)) AS chat_or_snap_send_10_min,
                  SUM(
                  IF
                    (notif_type='MAP_FRIEND_TRAVEL_STATUS'
                      AND chat_or_snap_send_count_10_min>0,1,0)) AS chat_or_snap_send_10_min_friend_travel_status,
                  SUM(
                  IF
                    (notif_type='MAP_FRIEND_TRAVEL_STATUS_BACK_IN_TOWN'
                      AND chat_or_snap_send_count_10_min>0,1,0)) AS chat_or_snap_send_10_min_friend_back_in_town,
                  SUM(
                  IF
                    (chat_or_snap_send_count_same_day>0,1,0)) AS chat_or_snap_send_same_day,
                  SUM(
                  IF
                    (notif_type='MAP_FRIEND_TRAVEL_STATUS'
                      AND chat_or_snap_send_count_same_day>0,1,0)) AS chat_or_snap_send_same_day_friend_travel_status,
                  SUM(
                  IF
                    (notif_type='MAP_FRIEND_TRAVEL_STATUS_BACK_IN_TOWN'
                      AND chat_or_snap_send_count_same_day>0,1,0)) AS chat_or_snap_send_same_day_friend_back_in_town,
                FROM
                  `sc-analytics.report_maps.maps_notification_engagement_*`
                WHERE
                  _TABLE_SUFFIX BETWEEN '{start}'
                  AND '{end}'
                GROUP BY
                  1,
                  2
        """.format(
            start=start_date,
            end=end_date
            )
        return sql
        
    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[
                map_notif_send,
                map_notif_open,
                map_notification_open_ctr,
                map_notif_send_friend_travel_status,
                map_notif_open_friend_travel_status,
                map_notification_open_ctr_friend_travel_status,
                map_notif_send_friend_back_in_town,
                map_notif_open_friend_back_in_town,
                map_notification_open_ctr_back_in_town,
                map_notif_send_place_edits_added,
                map_notif_open_place_edits_added,
                map_notification_open_ctr_place_edits,
                chat_or_snap_send_10_min,
                chat_or_snap_on_notif_open_10_min_ratio,
                chat_or_snap_send_10_min_friend_travel_status,
                chat_or_snap_on_notif_open_10_min_friend_travel_status_ratio,
                chat_or_snap_send_10_min_friend_back_in_town,
                chat_or_snap_on_notif_open_10_min_friend_back_in_town_ratio,
                chat_or_snap_send_same_day,
                chat_or_snap_on_notif_open_same_day_ratio,
                chat_or_snap_send_same_day_friend_travel_status,
                chat_or_snap_on_notif_open_same_day_friend_travel_status_ratio,
                chat_or_snap_send_same_day_friend_back_in_town,
                chat_or_snap_on_notif_open_same_day_friend_back_in_town_ratio
       ],
       name="map_notifications",
       bq_dialect='standard'
    )

    return mt


def map_clutter(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """

    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')


    total_clutter_items = Metric(
        'total_clutter_items',
        'Total Clutter Item Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE,
    )

    total_clutter_score = Metric(
        'total_clutter_score',
        'Total Clutter Item Score',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE,
    )

    friend_incl_self_p50 = Metric(
        'friend_incl_self_p50',
        'Friend Item Count (P50)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    friend_cluster_p50 = Metric(
        'friend_cluster_p50',
        'Friend Cluster Count (P50)',
        dist='cont',
        daily=True,
        cumulative=True,
    )


    story_thumbnail_no_annotation_p50 = Metric(
        'story_thumbnail_no_annotation_p50',
        'Story Thumbnail No Annotation Count (P50)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE,
    )

    story_thumbnail_annotation_p50 = Metric(
        'story_thumbnail_annotation_p50',
        'Story Thumbnail Annotation Count (P50)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE,
    )

    basemap_places_microdots_p50 = Metric(
        'basemap_places_microdots_p50',
        'Basemap Places Microdots Count (P50)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE,
    )


    basemap_places_no_annotation_p50 = Metric(
        'basemap_places_no_annotation_p50',
        'Basemap Places No Annotations Count (P50)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE,
    )


    basemap_places_annotation_p50 = Metric(
        'basemap_places_annotation_p50',
        'Basemap Places Annotations Count (P50)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE,
    )

    friend_incl_self_p50_score = Metric(
        'friend_incl_self_p50_score',
        'Friend Item Score (P50)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    friend_cluster_p50_score = Metric(
        'friend_cluster_p50_score',
        'Friend Cluster Score (P50)',
        dist='cont',
        daily=True,
        cumulative=True,
    )


    story_thumbnail_no_annotation_p50_score = Metric(
        'story_thumbnail_no_annotation_p50_score',
        'Story Thumbnail No Annotation Score (P50)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE,
    )

    story_thumbnail_annotation_p50_score = Metric(
        'story_thumbnail_annotation_p50_score',
        'Story Thumbnail Annotation Score (P50)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE,
    )

    basemap_places_microdots_p50_score = Metric(
        'basemap_places_microdots_p50_score',
        'Basemap Places Microdots Score (P50)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE,
    )


    basemap_places_no_annotation_p50_score = Metric(
        'basemap_places_no_annotation_p50_score',
        'Basemap Places No Annotations Score (P50)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE,
    )


    basemap_places_annotation_p50_score = Metric(
        'basemap_places_annotation_p50_score',
        'Basemap Places Annotations Score (P50)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE,
    )

    map_clutter_score_p50 = Metric(
        'map_clutter_score_p50',
        'Map Viewport Clutter Score (P50)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE,
    )

    def sql_callable(start_date, end_date):
        sql = """
                SELECT
                  TIMESTAMP_SUB(event_date, INTERVAL 8 HOUR) as ts,
                  ghost_user_id,
                  
                  friend_incl_self_p50+
                  friend_cluster_p50+
                  story_thumbnail_no_annotation_p50+
                  story_thumbnail_annotation_p50+
                  basemap_places_microdots_p50+
                  basemap_places_no_annotation_p50+
                  basemap_places_annotation_p50 as total_clutter_items,

                  3*friend_incl_self_p50+
                  5*friend_cluster_p50+
                  2*story_thumbnail_no_annotation_p50+
                  2*story_thumbnail_annotation_p50+
                  0.2*basemap_places_microdots_p50+
                  0.5*basemap_places_no_annotation_p50+
                  1*basemap_places_annotation_p50 as total_clutter_score,

                  friend_incl_self_p50,
                  friend_cluster_p50,
                  story_thumbnail_no_annotation_p50,
                  story_thumbnail_annotation_p50,
                  basemap_places_microdots_p50,
                  basemap_places_no_annotation_p50,
                  basemap_places_annotation_p50,

                  3*friend_incl_self_p50 friend_incl_self_p50_score,
                  5*friend_cluster_p50 friend_cluster_p50_score,
                  2*story_thumbnail_no_annotation_p50 story_thumbnail_no_annotation_p50_score,
                  2*story_thumbnail_annotation_p50 story_thumbnail_annotation_p50_score,
                  0.2*basemap_places_microdots_p50 basemap_places_microdots_p50_score,
                  0.5*basemap_places_no_annotation_p50 basemap_places_no_annotation_p50_score,
                  1*basemap_places_annotation_p50 basemap_places_annotation_p50_score,

                  map_clutter_score_p50,

                FROM
                  `sc-analytics.report_maps.maps_viewport_impression_user_item_aggregated_*`
                WHERE
                  _TABLE_SUFFIX BETWEEN '{start}'
                  AND '{end}'
        """.format(
            start=start_date,
            end=end_date
            )
        return sql
        
    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[
                total_clutter_items,
                total_clutter_score,
                map_clutter_score_p50,
                friend_incl_self_p50,
                friend_cluster_p50,
                story_thumbnail_no_annotation_p50,
                story_thumbnail_annotation_p50,
                basemap_places_microdots_p50,
                basemap_places_no_annotation_p50,
                basemap_places_annotation_p50,
                friend_incl_self_p50_score,
                friend_cluster_p50_score,
                story_thumbnail_no_annotation_p50_score,
                story_thumbnail_annotation_p50_score,
                basemap_places_microdots_p50_score,
                basemap_places_no_annotation_p50_score,
                basemap_places_annotation_p50_score
       ],
       name="map_clutter",
       bq_dialect='standard'
    )

    return mt

def map_viewport_zoom_metrics(start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    initial_viewport_zoom = Metric(
        'initial_viewport_zoom',
        dist='quantile',
        daily=True,
        cumulative=True,
    )

    map_open_zoom = Metric(
        'map_open_zoom',
        dist='quantile',
        daily=True,
        cumulative=True,
    )

    map_close_zoom = Metric(
        'map_close_zoom',
        dist='quantile',
        daily=True,
        cumulative=True,
    )

    def sql_callable(start_date, end_date):
        sql = """
        SELECT
            TIMESTAMP_SUB(event_date, INTERVAL 8 HOUR) as ts,
            ghost_user_id,
            APPROX_QUANTILES(initial_viewport_zoom, 100)[OFFSET(50)] AS initial_viewport_zoom,
            APPROX_QUANTILES(map_open_zoom, 100)[OFFSET(50)] AS map_open_zoom,
            APPROX_QUANTILES(map_close_zoom, 100)[OFFSET(50)] AS map_close_zoom,
        FROM
            `sc-analytics.report_maps.maps_session_zoom_interaction_*`
        WHERE
            _TABLE_SUFFIX BETWEEN '{start}'
            AND '{end}'
        GROUP BY 1, 2
        """.format(
            start=start_date,
            end=end_date
        )
        return sql

    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[
            initial_viewport_zoom,
            map_open_zoom,
            map_close_zoom
        ],
        inner_join_with_mapping=False,
        quantile_metrics=True,
        name="map_viewport_zoom_metrics",
        bq_dialect='standard'
    )

    return mt