""" Memories Save and Backup Loss metrics
Contact: klei@, hwang2@
"""

from __future__ import division, print_function
from banjo.abtest.metric import (
    Metric, MetricTable
)
from banjo.abtest.metric import POSITIVE, NEGATIVE
import pandas as pd
import logging

logger = logging.getLogger(__name__)

logger.warning("Memories save & backup loss metrics imported.  Check the actual "
               "queries run to ensure correctness")

__all__ = [
    "memories_backup_loss_metrics",
    "memories_save_loss_metrics",
    "memories_save_loss_uu_metrics",
    "camera_roll_save_loss_metrics",
    "memories_backup_loss_rate_metrics",
    "gallery_save_event_memories_save_loss_metrics"
]
def memories_backup_loss_rate_metrics(start_date, end_date):
    """
            Parameters
            ----------
            start_date
            end_date
            Returns
            -------
            mt: MetricTable
            >>> memories_backup_loss_metrics('20190301', '20190303')
            """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
            `sc-analytics.report_memories.memories_all_snap_status_v3_2*`
             WHERE CONCAT('2', _TABLE_SUFFIX) BETWEEN '{start}' AND '{end}' AND snap_status IN (10,15,25,60,30,40,50)
            """.format(
        start=start_date,
        end=end_date,
    )

    BACKUP_ATTEMPT = Metric(
        'BACKUP_ATTEMPT',
        'Backup attempt',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    BACKUP_ATTEMPT_UU = Metric(
        'BACKUP_ATTEMPT_UU',
        'Backup Attempt UU',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    BACKUP_LOSS_OVERALL = Metric(
        'BACKUP_LOSS_OVERALL',
        'Backup Loss Overall',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_OVERALL_UU = Metric(
        'BACKUP_LOSS_OVERALL_UU',
        'Backup Loss Overall UU',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_RATE = Metric(
        "BACKUP_LOSS_RATE",
        "Backup Loss Rate",
        dist="ratio",
        numerator='BACKUP_LOSS_OVERALL',
        denominator='BACKUP_ATTEMPT',
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_UU_RATE = Metric(
        "BACKUP_LOSS_UU_RATE",
        "Backup Loss UU Rate",
        dist="ratio",
        numerator='BACKUP_LOSS_OVERALL_UU',
        denominator='BACKUP_ATTEMPT_UU',
        desired_direction=NEGATIVE
    )

    BACKUP_EXPIRE = Metric(
        'BACKUP_EXPIRE',
        'Backup Expire',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_EXPIRE_UU = Metric(
        'BACKUP_EXPIRE_UU',
        'Backup Expire UU',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_EXPIRE_RATE = Metric(
        "BACKUP_EXPIRE_RATE",
        "Backup Expire Rate",
        dist="ratio",
        numerator='BACKUP_EXPIRE',
        denominator='BACKUP_ATTEMPT',
        desired_direction=NEGATIVE
    )

    BACKUP_EXPIRE_UU_RATE = Metric(
        "BACKUP_EXPIRE_UU_RATE",
        "Backup Expire UU Rate",
        dist="ratio",
        numerator='BACKUP_EXPIRE_UU',
        denominator='BACKUP_ATTEMPT_UU',
        desired_direction=NEGATIVE
    )

    mt = MetricTable(
        sql="""
            SELECT
              PARSE_TIMESTAMP('%Y%m%d', CONCAT('2', _TABLE_SUFFIX)) AS ts,
              ghost_user_id,
              --
              COUNT(1) AS BACKUP_ATTEMPT,
              1 AS BACKUP_ATTEMPT_UU,
              -- loss
              SUM(IF(snap_status=30, 1, 0)) AS BACKUP_LOSS_OVERALL,
              MAX(IF(snap_status=30, 1, 0)) AS BACKUP_LOSS_OVERALL_UU,
              -- expire
              SUM(IF(snap_status=40, 1, 0)) AS BACKUP_EXPIRE,
              MAX(IF(snap_status=40, 1, 0)) AS BACKUP_EXPIRE_UU,
            FROM
              {source_table}
            GROUP BY
              ts,
              ghost_user_id
            """.format(source_table=source_table),

        metrics=[BACKUP_ATTEMPT,
                 BACKUP_ATTEMPT_UU,
                 BACKUP_LOSS_OVERALL,
                 BACKUP_LOSS_OVERALL_UU,
                 BACKUP_LOSS_RATE,
                 BACKUP_LOSS_UU_RATE,
                 BACKUP_EXPIRE,
                 BACKUP_EXPIRE_UU,
                 BACKUP_EXPIRE_RATE,
                 BACKUP_EXPIRE_UU_RATE
                 ],

        name = "memories_backup_loss_rate_metrics",
        bq_dialect='standard'
    )
    return mt


def memories_backup_loss_metrics(start_date, end_date):
    """
        Parameters
        ----------
        start_date
        end_date
        Returns
        -------
        mt: MetricTable
        >>> memories_backup_loss_metrics('20190301', '20190303')
        """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
        `sc-analytics.report_memories.memories_missing_snap_v3_2*`
             WHERE CONCAT('2', _TABLE_SUFFIX) BETWEEN '{start}' AND '{end}' AND snap_status = 30
        """.format(
        start=start_date,
        end=end_date,
    )

    BACKUP_LOSS = Metric(
        'BACKUP_LOSS',
        'Backup Loss',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_UU = Metric(
        'BACKUP_LOSS_UU',
        'Backup Loss User',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_ABANDON = Metric(
        'BACKUP_LOSS_ABANDON',
        'Backup Loss Abandon',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_ABANDON_UU = Metric(
        'BACKUP_LOSS_ABANDON_UU',
        'Backup Loss Abandon User',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_CHECKPOINT = Metric(
        'BACKUP_LOSS_CHECKPOINT',
        'Backup Loss Checkpoint',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_CHECKPOINT_UU = Metric(
        'BACKUP_LOSS_CHECKPOINT_UU',
        'Backup Loss Checkpoint User',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_CONFLICT_ENCRYPT = Metric(
        'BACKUP_LOSS_CONFLICT_ENCRYPT',
        'Backup Loss Conflict Encrypt',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_CONFLICT_ENCRYPT_UU = Metric(
        'BACKUP_LOSS_CONFLICT_ENCRYPT_UU',
        'Backup Loss Conflict Encrypt User',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_MEDIA_FILE_MISSING = Metric(
        'BACKUP_LOSS_MEDIA_FILE_MISSING',
        'Backup Loss Media File Missing',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_MEDIA_FILE_MISSING_UU = Metric(
        'BACKUP_LOSS_MEDIA_FILE_MISSING_UU',
        'Backup Loss Media File Missing User',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_MEDIA_OBJECT_MISSING = Metric(
        'BACKUP_LOSS_MEDIA_OBJECT_MISSING',
        'Backup Loss Media Object Missing',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_MEDIA_OBJECT_MISSING_UU = Metric(
        'BACKUP_LOSS_MEDIA_OBJECT_MISSING_UU',
        'Backup Loss Media Object Missing User',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_SNAP_MISSING = Metric(
        'BACKUP_LOSS_SNAP_MISSING',
        'Backup Loss Snap Missing',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_SNAP_MISSING_UU = Metric(
        'BACKUP_LOSS_SNAP_MISSING_UU',
        'Backup Loss Snap Missing User',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_GALLERY_DATA_DELETE_ATTEMPT = Metric(
        'BACKUP_LOSS_GALLERY_DATA_DELETE_ATTEMPT',
        'Backup Loss Gallery Data Delete Attempt',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_GALLERY_DATA_DELETE_ATTEMPT_UU = Metric(
        'BACKUP_LOSS_GALLERY_DATA_DELETE_ATTEMPT_UU',
        'Backup Loss Gallery Data Delete Attempt User',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_GALLERY_DB_INIT = Metric(
        'BACKUP_LOSS_GALLERY_DB_INIT',
        'Backup Loss Gallery DB Init',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_GALLERY_DB_INIT_UU = Metric(
        'BACKUP_LOSS_GALLERY_DB_INIT_UU',
        'Backup Loss Gallery DB Init User',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_LOGIN = Metric(
        'BACKUP_LOSS_LOGIN',
        'Backup Loss Login',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_LOGIN_UU = Metric(
        'BACKUP_LOSS_LOGIN_UU',
        'Backup Loss Login User',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_LOGOUT = Metric(
        'BACKUP_LOSS_LOGOUT',
        'Backup Loss Logout',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_LOGOUT_UU = Metric(
        'BACKUP_LOSS_LOGOUT_UU',
        'Backup Loss Logout User',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_SETTINGS = Metric(
        'BACKUP_LOSS_SETTINGS',
        'Backup Loss Settings',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_SETTINGS_UU = Metric(
        'BACKUP_LOSS_SETTINGS_UU',
        'Backup Loss Settings User',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_NEW_INSTALL_TRUE = Metric(
        'BACKUP_LOSS_NEW_INSTALL_TRUE',
        'Backup Loss New Install True',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_NEW_INSTALL_TRUE_UU = Metric(
        'BACKUP_LOSS_NEW_INSTALL_TRUE_UU',
        'Backup Loss New Install True User',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_NEW_INSTALL_FALSE = Metric(
        'BACKUP_LOSS_NEW_INSTALL_FALSE',
        'Backup Loss New Install False',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    BACKUP_LOSS_NEW_INSTALL_FALSE_UU = Metric(
        'BACKUP_LOSS_NEW_INSTALL_FALSE_UU',
        'Backup Loss New Install False User',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    mt = MetricTable(
        sql="""
            SELECT
              PARSE_TIMESTAMP('%Y%m%d', CONCAT('2', _TABLE_SUFFIX)) AS ts,
              ghost_user_id,
              COUNT(1) AS BACKUP_LOSS,
              1 AS BACKUP_LOSS_UU,
              -- reason
              SUM(IF(reason = "ABANDON", 1, 0)) AS BACKUP_LOSS_ABANDON,
              MAX(IF(reason = "ABANDON", 1, 0)) AS BACKUP_LOSS_ABANDON_UU,
              SUM(IF(reason = "CHECKPOINT", 1, 0)) AS BACKUP_LOSS_CHECKPOINT,
              MAX(IF(reason = "CHECKPOINT", 1, 0)) AS BACKUP_LOSS_CHECKPOINT_UU,
              -- error reason
              SUM(IF(error_reason = "CONFLICT_ENCRYPT", 1, 0)) AS BACKUP_LOSS_CONFLICT_ENCRYPT,
              MAX(IF(error_reason = "CONFLICT_ENCRYPT", 1, 0)) AS BACKUP_LOSS_CONFLICT_ENCRYPT_UU,
              SUM(IF(error_reason = "MEDIA_FILE_MISSING", 1, 0)) AS BACKUP_LOSS_MEDIA_FILE_MISSING,
              MAX(IF(error_reason = "MEDIA_FILE_MISSING", 1, 0)) AS BACKUP_LOSS_MEDIA_FILE_MISSING_UU,
              SUM(IF(error_reason = "MEDIA_OBJECT_MISSING", 1, 0)) AS BACKUP_LOSS_MEDIA_OBJECT_MISSING,
              MAX(IF(error_reason = "MEDIA_OBJECT_MISSING", 1, 0)) AS BACKUP_LOSS_MEDIA_OBJECT_MISSING_UU,
              SUM(IF(error_reason = "SNAP_MISSING", 1, 0)) AS BACKUP_LOSS_SNAP_MISSING,
              MAX(IF(error_reason = "SNAP_MISSING", 1, 0)) AS BACKUP_LOSS_SNAP_MISSING_UU,
              -- last_init_event
              SUM(IF(last_init_event = "GALLERY_DATA_DELETE_ATTEMPT", 1, 0)) AS BACKUP_LOSS_GALLERY_DATA_DELETE_ATTEMPT,
              MAX(IF(last_init_event = "GALLERY_DATA_DELETE_ATTEMPT", 1, 0)) AS BACKUP_LOSS_GALLERY_DATA_DELETE_ATTEMPT_UU,
              SUM(IF(last_init_event = "GALLERY_DB_INIT", 1, 0)) AS BACKUP_LOSS_GALLERY_DB_INIT,
              MAX(IF(last_init_event = "GALLERY_DB_INIT", 1, 0)) AS BACKUP_LOSS_GALLERY_DB_INIT_UU,
              -- last_init_event_context
              SUM(IF(last_init_event_context = "LOGIN", 1, 0)) AS BACKUP_LOSS_LOGIN,
              MAX(IF(last_init_event_context = "LOGIN", 1, 0)) AS BACKUP_LOSS_LOGIN_UU,
              SUM(IF(last_init_event_context = "LOGOUT", 1, 0)) AS BACKUP_LOSS_LOGOUT,
              MAX(IF(last_init_event_context = "LOGOUT", 1, 0)) AS BACKUP_LOSS_LOGOUT_UU,
              SUM(IF(last_init_event_context = "SETTINGS", 1, 0)) AS BACKUP_LOSS_SETTINGS,
              MAX(IF(last_init_event_context = "SETTINGS", 1, 0)) AS BACKUP_LOSS_SETTINGS_UU,
              -- last_init_event_new_install
              SUM(IF(last_init_event_new_install IS TRUE, 1, 0)) AS BACKUP_LOSS_NEW_INSTALL_TRUE,
              MAX(IF(last_init_event_new_install IS TRUE, 1, 0)) AS BACKUP_LOSS_NEW_INSTALL_TRUE_UU,
              SUM(IF(last_init_event_new_install IS FALSE, 1, 0)) AS BACKUP_LOSS_NEW_INSTALL_FALSE,
              MAX(IF(last_init_event_new_install IS FALSE, 1, 0)) AS BACKUP_LOSS_NEW_INSTALL_FALSE_UU,
            FROM
              {source_table}
            GROUP BY
              ts,
              ghost_user_id
            """.format(source_table=source_table),
        metrics=[BACKUP_LOSS,
                 BACKUP_LOSS_UU,
                 BACKUP_LOSS_ABANDON,
                 BACKUP_LOSS_ABANDON_UU,
                 BACKUP_LOSS_CHECKPOINT,
                 BACKUP_LOSS_CHECKPOINT_UU,
                 BACKUP_LOSS_CONFLICT_ENCRYPT,
                 BACKUP_LOSS_CONFLICT_ENCRYPT_UU,
                 BACKUP_LOSS_MEDIA_FILE_MISSING,
                 BACKUP_LOSS_MEDIA_FILE_MISSING_UU,
                 BACKUP_LOSS_MEDIA_OBJECT_MISSING,
                 BACKUP_LOSS_MEDIA_OBJECT_MISSING_UU,
                 BACKUP_LOSS_SNAP_MISSING,
                 BACKUP_LOSS_SNAP_MISSING_UU,
                 BACKUP_LOSS_GALLERY_DATA_DELETE_ATTEMPT,
                 BACKUP_LOSS_GALLERY_DATA_DELETE_ATTEMPT_UU,
                 BACKUP_LOSS_GALLERY_DB_INIT,
                 BACKUP_LOSS_GALLERY_DB_INIT_UU,
                 BACKUP_LOSS_LOGIN,
                 BACKUP_LOSS_LOGIN_UU,
                 BACKUP_LOSS_LOGOUT,
                 BACKUP_LOSS_LOGOUT_UU,
                 BACKUP_LOSS_SETTINGS,
                 BACKUP_LOSS_SETTINGS_UU,
                 BACKUP_LOSS_NEW_INSTALL_TRUE,
                 BACKUP_LOSS_NEW_INSTALL_TRUE_UU,
                 BACKUP_LOSS_NEW_INSTALL_FALSE,
                 BACKUP_LOSS_NEW_INSTALL_FALSE_UU
                 ],
        name="memories_backup_loss_metrics",
        bq_dialect='standard'
    )
    return mt


def memories_save_loss_metrics(start_date, end_date):
    """
        Parameters
        ----------
        start_date
        end_date
        Returns
        -------
        mt: MetricTable
        >>> memories_save_loss_metrics('20190301', '20190303')
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
        `sc-analytics.report_memories.lost_content_event_funnel_user_info_v2_2*`
             WHERE CONCAT('2', _TABLE_SUFFIX) BETWEEN '{start}' AND '{end}'
        """.format(
        start=start_date,
        end=end_date,
    )

    SAVE_ATTEMPT = Metric(
        'save_attempt_count',
        'Save Attempt',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    SAVE_LOSS = Metric(
        'save_lost_count',
        'Save Loss (V1)',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    SAVE_LOSS_GALLERY_SAVE_EVENT_COUNT = Metric(
        'save_attempt_gallery_save_event_lost_count',
        'Save Loss (V2)',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    LOSS_TO_ATTEMPT_RATE = Metric(
        'loss_to_attempt_rate',
        'Save Loss Rate (V1)',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_lost_count',
        denominator='save_attempt_count',
        desired_direction=NEGATIVE
    )
    
    SAVE_ATTEMPT_GALLERY_SAVE_EVENT_START_COUNT = Metric(
        'save_attempt_gallery_save_event_start_count',
        'Save Attempt (V2 Start)',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    LOSS_TO_ATTEMPT_RATE_GALLERY_SAVE_EVENT = Metric(
        'loss_to_attempt_rate_gallery_save_event',
        'Save Loss Rate (V2)',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_attempt_gallery_save_event_lost_count',
        denominator='save_attempt_gallery_save_event_start_count',
        desired_direction=NEGATIVE
    )
    
    SAVE_ATTEMPT_GALLERY_SAVE_EVENT_START_LOST_COUNT = Metric(
        'save_attempt_gallery_save_event_start_lost_count',
        'Gallery Save Event Start Loss',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    LOSS_TO_ACTION_RATE = Metric(
        'loss_to_action_rate',
        'Gallery Save Event Start Loss Rate',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_attempt_gallery_save_event_start_lost_count',
        denominator='save_attempt_count',
        desired_direction=NEGATIVE
    )

    GALLERY_SAVE_EVENT_START_COUNT = Metric(
        'gallery_save_event_start_count', 
        'Gallery Save Event Start', 
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    GALLERY_SAVE_EVENT_LOSS_COUNT = Metric(
        'gallery_save_event_lost_count', 
        'Gallery Save Event Loss', 
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    GALLERY_SAVE_EVENT_PREVIEW_START_COUNT = Metric(
        'gallery_save_event_preview_start_count', 
        'Gallery Save Event Preview Start', 
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    GALLERY_SAVE_EVENT_PREVIEW_LOSS_COUNT = Metric(
        'gallery_save_event_preview_lost_count', 
        'Gallery Save Event Preview Loss', 
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    GALLERY_SAVE_EVENT_NON_PREVIEW_START_COUNT = Metric(
        'gallery_save_event_non_preview_start_count', 
        'Gallery Save Event Non-Preview Start', 
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )
    
    GALLERY_SAVE_EVENT_NON_PREVIEW_LOSS_COUNT = Metric(
        'gallery_save_event_non_preview_lost_count',
        'Gallery Save Event Non-Preview Loss',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    OPERATIONAL_SAVE_LOSS_RATE = Metric(
        'operational_save_loss_rate',
        'Operational Save Loss Rate',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='gallery_save_event_lost_count',
        denominator='gallery_save_event_start_count',
        desired_direction=NEGATIVE
    )

    PREVIEW_OPERATIONAL_SAVE_LOSS_RATE = Metric(
        'preview_operational_save_loss_rate',
        'Preview Operational Save Loss Rate',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='gallery_save_event_preview_lost_count',
        denominator='gallery_save_event_preview_start_count',
        desired_direction=NEGATIVE
    )

    NON_PREVIEW_OPERATIONAL_SAVE_LOSS_RATE = Metric(
        'non_preview_operational_save_loss_rate',
        'Non Preview Operational Save Loss Rate',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='gallery_save_event_non_preview_lost_count',
        denominator='gallery_save_event_non_preview_start_count',
        desired_direction=NEGATIVE
    )

    SAVE_ATTEMPT_GALLERY_ONLY = Metric(
        'save_attempt_gallery_only_count',
        'Save Attempt - Memories Only',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )
    
    SAVE_LOSS_GALLERY_ONLY = Metric(
        'save_lost_gallery_only_count',
        'Save Loss - Memories Only (V1)',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    SAVE_LOSS_GALLERY_SAVE_EVENT_GALLERY_ONLY_COUNT = Metric(
        'save_attempt_gallery_only_save_event_lost_count',
        'Save Loss - Memories Only (V2)',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    LOSS_TO_ATTEMPT_RATE_GALLERY_ONLY = Metric(
        'loss_to_attempt_rate_gallery_only',
        'Save Loss Rate - Memories Only (V1)',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_lost_gallery_only_count',
        denominator='save_attempt_gallery_only_count',
        desired_direction=NEGATIVE
    )

    # NEW: Added missing metric for the denominator below
    SAVE_ATTEMPT_GALLERY_ONLY_SAVE_EVENT_START_COUNT = Metric(
        'save_attempt_gallery_only_save_event_start_count',
        'Save Attempt - Memories Only (V2 Start)',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    SAVE_ATTEMPT_GALLERY_ONLY_SAVE_EVENT_START_LOST_COUNT = Metric(
        'save_attempt_gallery_only_save_event_start_lost_count',
        'Gallery Save Event Start Loss - Memories Only',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )    

    LOSS_TO_ATTEMPT_RATE_GALLERY_SAVE_EVENT_GALLERY_ONLY = Metric(
        'loss_to_attempt_rate_gallery_save_event_gallery_only',
        'Save Loss Rate (V2) - Memories Only',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_attempt_gallery_only_save_event_lost_count',
        denominator='save_attempt_gallery_only_save_event_start_count',
        desired_direction=NEGATIVE
    )

    LOSS_TO_ACTION_RATE_GALLERY_ONLY = Metric(
        'loss_to_action_rate_gallery_only',
        'Gallery Save Event Start Loss Rate - Gallery Only',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_attempt_gallery_only_save_event_start_lost_count',
        denominator='save_attempt_gallery_only_count',
        desired_direction=NEGATIVE
    )    

    SAVE_ATTEMPT_GALLERY_CAMERA_ROLL = Metric(
        'save_attempt_gallery_camera_roll_count',
        'Save Attempt - Memories & Camera Roll',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    SAVE_LOSS_GALLERY_CAMERA_ROLL = Metric(
        'save_lost_gallery_in_gallery_camera_roll_count',
        'Save Loss - Memories & Camera Roll (V1)',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    SAVE_LOSS_GALLERY_SAVE_EVENT_GALLERY_CAMERA_ROLL_COUNT = Metric(
        'save_attempt_gallery_camera_roll_save_event_lost_count',
        'Save Loss - Memories & Camera Roll (V2)',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    LOSS_TO_ATTEMPT_RATE_GALLERY_CAMERA_ROLL = Metric(
        'loss_to_attempt_rate_gallery_camera_roll',
        'Save Loss Rate - Memories & Camera Roll (Memories path)',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_lost_gallery_in_gallery_camera_roll_count',
        denominator='save_attempt_gallery_camera_roll_count',
        desired_direction=NEGATIVE
    )

    SAVE_ATTEMPT_GALLERY_CAMERA_ROLL_SAVE_EVENT_START_COUNT = Metric(
        'save_attempt_gallery_camera_roll_save_event_start_count',
        'Save Attempt - M&CR (V2 Start)',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    LOSS_TO_ATTEMPT_RATE_GALLERY_SAVE_EVENT_GALLERY_CAMERA_ROLL = Metric(
        'loss_to_attempt_rate_gallery_save_event_gallery_camera_roll',
        'Save Loss Rate - Memories & Camera Roll (V2)',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_attempt_gallery_camera_roll_save_event_lost_count',
        denominator='save_attempt_gallery_camera_roll_save_event_start_count',
        desired_direction=NEGATIVE
    )

    SAVE_ATTEMPT_GALLERY_CAMERA_ROLL_SAVE_EVENT_START_LOST_COUNT = Metric(
        'save_attempt_gallery_camera_roll_save_event_start_lost_count',
        'Gallery Camera Roll Save Event Start Lost',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    LOSS_TO_ACTION_RATE_GALLERY_CAMERA_ROLL = Metric(
        'loss_to_action_rate_gallery_camera_roll',
        'Gallery Save Event Start Loss Rate - Gallery & Camera Roll',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_attempt_gallery_camera_roll_save_event_start_lost_count',
        denominator='save_attempt_gallery_camera_roll_count',
        desired_direction=NEGATIVE
    )    

    SAVE_ATTEMPT_IMAGE = Metric(
        'save_attempt_image_count',
        'Save Attempt Image',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    SAVE_LOSS_IMAGE = Metric(
        'save_lost_image_count',
        'Save Loss Image',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )
    
    SAVE_ATTEMPT_GALLERY_SAVE_EVENT_START_LOST_IMAGE_COUNT = Metric(
        'save_attempt_gallery_save_event_start_lost_image_count',
        'Gallery Save Event Start Lost Image',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    LOSS_TO_ACTION_RATE_IMAGE = Metric(
        'loss_to_action_rate_image',
        'Gallery Save Event Image Start Loss Rate',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_attempt_gallery_save_event_start_lost_image_count',
        denominator='save_attempt_image_count',
        desired_direction=NEGATIVE
    )

    SAVE_ATTEMPT_GALLERY_SAVE_EVENT_LOST_IMAGE_COUNT = Metric(
        'save_attempt_gallery_save_event_lost_image_count',
        'Gallery Save Event Lost Image',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )
    SAVE_ATTEMPT_GALLERY_SAVE_EVENT_START_IMAGE_COUNT = Metric(
        'save_attempt_gallery_save_event_start_image_count',
        'Gallery Save Event Start Image',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    LOSS_TO_ATTEMPT_RATE_GALLERY_SAVE_EVENT_IMAGE = Metric(
        'loss_to_attempt_rate_gallery_save_event_image',
        'Save Loss Rate Image (V2)',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_attempt_gallery_save_event_lost_image_count',
        denominator='save_attempt_gallery_save_event_start_image_count',
        desired_direction=NEGATIVE
    )

    LOSS_TO_ATTEMPT_RATE_IMAGE = Metric(
        'loss_to_attempt_rate_image',
        'Save Loss Rate Image',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_lost_image_count',
        denominator='save_attempt_image_count',
        desired_direction=NEGATIVE
    )

    SAVE_ATTEMPT_VIDEO = Metric(
        'save_attempt_video_count',
        'Save Attempt Video',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    SAVE_LOSS_VIDEO = Metric(
        'save_lost_video_count',
        'Save Loss Video',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    SAVE_ATTEMPT_GALLERY_SAVE_EVENT_START_LOST_VIDEO_COUNT = Metric(
        'save_attempt_gallery_save_event_start_lost_video_count',
        'Gallery Save Event Start Lost Video',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    LOSS_TO_ACTION_RATE_VIDEO = Metric(
        'loss_to_action_rate_video',
        'Gallery Save Event Video Start Loss Rate',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_attempt_gallery_save_event_start_lost_video_count',
        denominator='save_attempt_video_count',
        desired_direction=NEGATIVE
    )

    SAVE_ATTEMPT_GALLERY_SAVE_EVENT_LOST_VIDEO_COUNT = Metric(
        'save_attempt_gallery_save_event_lost_video_count',
        'Gallery Save Event Lost Video',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )
    SAVE_ATTEMPT_GALLERY_SAVE_EVENT_START_VIDEO_COUNT = Metric(
        'save_attempt_gallery_save_event_start_video_count',
        'Gallery Save Event Start Video',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    LOSS_TO_ATTEMPT_RATE_GALLERY_SAVE_EVENT_VIDEO = Metric(
        'loss_to_attempt_rate_gallery_save_event_video',
        'Save Loss Rate Video (V2)',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_attempt_gallery_save_event_lost_video_count',
        denominator='save_attempt_gallery_save_event_start_video_count',
        desired_direction=NEGATIVE
    )

    LOSS_TO_ATTEMPT_RATE_VIDEO = Metric(
        'loss_to_attempt_rate_video',
        'Save Loss Rate Video',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_lost_video_count',
        denominator='save_attempt_video_count',
        desired_direction=NEGATIVE
    )    

    ## media type * destination

    SAVE_ATTEMPT_IMAGE_GALLERY_CAMERA_ROLL = Metric(
        'save_attempt_image_gallery_camera_roll_count',
        'Save Attempt Image - Memories & Camera Roll',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    SAVE_LOSS_IMAGE_GALLERY_CAMERA_ROLL = Metric(
        'save_lost_gallery_image_in_gallery_camera_roll_count',
        'Save Loss Image - Memories & Camera Roll (Memories path)',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    LOSS_TO_ATTEMPT_RATE_IMAGE_GALLERY_CAMERA_ROLL = Metric(
        'loss_to_attempt_rate_image_gallery_camera_roll',
        'Save Loss Rate Image - Memories & Camera Roll (Memories path)',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_lost_gallery_image_in_gallery_camera_roll_count',
        denominator='save_attempt_image_gallery_camera_roll_count',
        desired_direction=NEGATIVE
    )

    SAVE_ATTEMPT_VIDEO_GALLERY_CAMERA_ROLL = Metric(
        'save_attempt_video_gallery_camera_roll_count',
        'Save Attempt VIDEO - Memories & Camera Roll',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    SAVE_LOSS_VIDEO_GALLERY_CAMERA_ROLL = Metric(
        'save_lost_gallery_video_in_gallery_camera_roll_count',
        'Save Loss VIDEO - Memories & Camera Roll (Memories path)',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    LOSS_TO_ATTEMPT_RATE_VIDEO_GALLERY_CAMERA_ROLL = Metric(
        'loss_to_attempt_rate_video_gallery_camera_roll',
        'Save Loss Rate VIDEO - Memories & Camera Roll (Memories path)',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_lost_gallery_video_in_gallery_camera_roll_count',
        denominator='save_attempt_video_gallery_camera_roll_count',
        desired_direction=NEGATIVE
    )

    SAVE_LOSS_ADJUSTMENT = Metric(
        'save_lost_adjustment_count',
        'Save Loss Adjustment',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    LOSS_TO_ATTEMPT_RATE_ADJUSTMENT = Metric(
        'loss_to_attempt_rate_adjustment',
        'Save Adjustment Loss Rate',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_lost_adjustment_count',
        denominator='save_attempt_count',
        desired_direction=NEGATIVE
    )

    SAVE_LOSS_IMAGE_ADJUSTMENT = Metric(
        'save_lost_adjustment_image_count',
        'Save Loss Image Adjustment',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    LOSS_TO_ATTEMPT_RATE_IMAGE_ADJUSTMENT = Metric(
        'loss_to_attempt_rate_image_adjustment',
        'Save Adjustment Loss Rate Image',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_lost_adjustment_image_count',
        denominator='save_attempt_image_count',
        desired_direction=NEGATIVE
    )

    SAVE_LOSS_VIDEO_ADJUSTMENT = Metric(
        'save_lost_adjustment_video_count',
        'Save Loss Video Adjustment',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    LOSS_TO_ATTEMPT_RATE_VIDEO_ADJUSTMENT = Metric(
        'loss_to_attempt_rate_video_adjustment',
        'Save Adjustment Loss Rate Video',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_lost_adjustment_video_count',
        denominator='save_attempt_video_count',
        desired_direction=NEGATIVE
    )

    SAVE_ATTEMPT_TIMELINE = Metric(
        'save_attempt_timeline_count',
        'Save Attempt Timeline',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    SAVE_LOSS_TIMELINE = Metric(
        'save_lost_timeline_count',
        'Save Loss Timeline',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    LOSS_TO_ATTEMPT_RATE_TIMELINE = Metric(
        'loss_to_attempt_rate_timeline',
        'Save Loss Rate Timeline',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_lost_timeline_count',
        denominator='save_attempt_timeline_count',
        desired_direction=NEGATIVE
    )

    SAVE_ATTEMPT_NON_TIMELINE = Metric(
        'save_attempt_non_timeline_count',
        'Save Attempt non Timeline',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    SAVE_LOSS_NON_TIMELINE = Metric(
        'save_lost_non_timeline_count',
        'Save Loss non Timeline',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    LOSS_TO_ATTEMPT_RATE_NON_TIMELINE = Metric(
        'loss_to_attempt_rate_non_timeline',
        'Save Loss Rate non Timeline',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_lost_non_timeline_count',
        denominator='save_attempt_non_timeline_count',
        desired_direction=NEGATIVE
    )

    mt = MetricTable(
        sql="""
        SELECT
            PARSE_TIMESTAMP('%Y%m%d', CONCAT('2', _TABLE_SUFFIX)) AS ts,
            ghost_user_id,
            -- overall save start v1
            SUM(save_start_unique_session) AS save_attempt_count,
            -- overall save start v2
            SUM(gallery_save_event_start_unique_session) AS save_attempt_gallery_save_event_start_count,
            -- overall save loss v1
            SUM(save_lost_unique_session ) AS save_lost_count,
            -- overall save loss v2
            SUM(gallery_save_event_start_lost_unique_session) AS save_attempt_gallery_save_event_start_lost_count,
            SUM(gallery_save_event_lost_unique_session) AS save_attempt_gallery_save_event_lost_count,
            -- gallery save event
            SUM(save_event_start_gallery_only_unique_session) AS gallery_save_event_start_count,
            SUM(save_event_start_gallery_only_unique_session) - SUM(save_event_finish_gallery_only_unique_session) AS gallery_save_event_lost_count,
            SUM(save_event_start_preview_gallery_only_unique_session) AS gallery_save_event_preview_start_count,
            SUM(save_event_start_preview_gallery_only_unique_session) - SUM(save_event_finish_preview_gallery_only_unique_session) AS gallery_save_event_preview_lost_count,
            SUM(save_event_start_non_preview_gallery_only_unique_session) AS gallery_save_event_non_preview_start_count,
            SUM(save_event_start_non_preview_gallery_only_unique_session) - SUM(save_event_finish_non_preview_gallery_only_unique_session) AS gallery_save_event_non_preview_lost_count,
            -- destination
            -- camera roll & gallery v1
            SUM(save_start_gallery_camera_roll_unique_session) AS save_attempt_gallery_camera_roll_count,
            SUM(save_lost_gallery_camera_roll_unique_session) AS save_lost_gallery_camera_roll_count,
            SUM(save_lost_gallery_in_gallery_camera_roll_unique_session) AS save_lost_gallery_in_gallery_camera_roll_count,
            -- camera roll & gallery v2
            SUM(gallery_camera_roll_save_event_start_unique_session) AS save_attempt_gallery_camera_roll_save_event_start_count,             
            SUM(gallery_camera_roll_save_event_start_lost_unique_session) AS save_attempt_gallery_camera_roll_save_event_start_lost_count,
            SUM(save_lost_gallery_camera_roll_save_event_unique_session) AS save_attempt_gallery_camera_roll_save_event_lost_count,
            -- camera roll 
            SUM(save_start_camera_roll_unique_session) AS save_attempt_camera_roll_count,
            SUM(save_lost_camera_roll_unique_session) AS save_lost_camera_roll_count,
            -- gallery v1
            SUM(save_start_gallery_only_unique_session) AS save_attempt_gallery_only_count,
            SUM(save_lost_gallery_only_unique_session) AS save_lost_gallery_only_count,
            -- gallery v2
            SUM(gallery_only_save_event_start_unique_session) AS save_attempt_gallery_only_save_event_start_count,
            SUM(gallery_only_save_event_start_lost_unique_session) AS save_attempt_gallery_only_save_event_start_lost_count,
            SUM(save_lost_gallery_save_event_only_unique_session) AS save_attempt_gallery_only_save_event_lost_count,
            -- image save loss v1
            SUM(IF(capture_action_type = "TAP", save_start_unique_session, 0)) AS save_attempt_image_count,
            SUM(IF(capture_action_type = "TAP", save_lost_unique_session, 0)) AS save_lost_image_count,
            -- image save loss v2
            SUM(IF(capture_action_type = "TAP", gallery_save_event_start_unique_session,0)) AS save_attempt_gallery_save_event_start_image_count,
            SUM(IF(capture_action_type = "TAP", gallery_save_event_start_lost_unique_session,0)) AS save_attempt_gallery_save_event_start_lost_image_count,
            SUM(IF(capture_action_type = "TAP", gallery_save_event_lost_unique_session,0)) AS save_attempt_gallery_save_event_lost_image_count,
            -- video save loss v1
            SUM(IF(capture_action_type = "TAP_AND_HOLD", save_start_unique_session, 0)) AS save_attempt_video_count,
            SUM(IF(capture_action_type = "TAP_AND_HOLD", save_lost_unique_session, 0)) AS save_lost_video_count,
            -- video save loss v2
            SUM(IF(capture_action_type = "TAP_AND_HOLD", gallery_save_event_start_unique_session,0)) AS save_attempt_gallery_save_event_start_video_count,
            SUM(IF(capture_action_type = "TAP_AND_HOLD", gallery_save_event_start_lost_unique_session,0)) AS save_attempt_gallery_save_event_start_lost_video_count,
            SUM(IF(capture_action_type = "TAP_AND_HOLD", gallery_save_event_lost_unique_session,0)) AS save_attempt_gallery_save_event_lost_video_count,

            -- media type * destination
            ---- image
            SUM(IF(capture_action_type = "TAP", save_start_gallery_camera_roll_unique_session, 0)) AS save_attempt_image_gallery_camera_roll_count,
            SUM(IF(capture_action_type = "TAP", save_lost_gallery_camera_roll_unique_session, 0)) AS save_lost_image_gallery_camera_roll_count,
            SUM(IF(capture_action_type = "TAP", save_lost_gallery_in_gallery_camera_roll_unique_session, 0)) AS save_lost_gallery_image_in_gallery_camera_roll_count,
            SUM(IF(capture_action_type = "TAP", save_start_camera_roll_unique_session, 0)) AS save_attempt_image_camera_roll_count,
            SUM(IF(capture_action_type = "TAP", save_lost_camera_roll_unique_session, 0)) AS save_lost_image_camera_roll_count,
            # SUM(IF(capture_action_type = "TAP", save_start_gallery_unique_session, 0)) AS save_attempt_image_gallery_count,
            # SUM(IF(capture_action_type = "TAP", save_lost_gallery_unique_session, 0)) AS save_lost_image_gallery_count,
            ---- video
            SUM(IF(capture_action_type = "TAP_AND_HOLD", save_start_gallery_camera_roll_unique_session, 0)) AS save_attempt_video_gallery_camera_roll_count,
            SUM(IF(capture_action_type = "TAP_AND_HOLD", save_lost_gallery_camera_roll_unique_session, 0)) AS save_lost_video_gallery_camera_roll_count,
            SUM(IF(capture_action_type = "TAP_AND_HOLD", save_lost_gallery_in_gallery_camera_roll_unique_session, 0)) AS save_lost_gallery_video_in_gallery_camera_roll_count,
            SUM(IF(capture_action_type = "TAP_AND_HOLD", save_start_camera_roll_unique_session, 0)) AS save_attempt_video_camera_roll_count,
            SUM(IF(capture_action_type = "TAP_AND_HOLD", save_lost_camera_roll_unique_session, 0)) AS save_lost_video_camera_roll_count,
            # SUM(IF(capture_action_type = "TAP_AND_HOLD", save_start_gallery_unique_session, 0)) AS save_attempt_video_gallery_count,
            # SUM(IF(capture_action_type = "TAP_AND_HOLD", save_lost_gallery_unique_session, 0)) AS save_lost_video_gallery_count,

            -- loss with adjustment
            -- overall
            SUM(IF((save_lost_unique_session - IFNULL(save_lost_adjustment,0))>0, (save_lost_unique_session - IFNULL(save_lost_adjustment,0)), 0)) AS save_lost_adjustment_count,
            SUM(IF(capture_action_type = "TAP"
                AND (save_lost_unique_session - IFNULL(save_lost_adjustment,0))>0, (save_lost_unique_session - IFNULL(save_lost_adjustment,0)), 0)) AS save_lost_adjustment_image_count,
            SUM(IF(capture_action_type = "TAP_AND_HOLD"
                AND (save_lost_unique_session - IFNULL(save_lost_adjustment,0))>0, (save_lost_unique_session - IFNULL(save_lost_adjustment,0)), 0)) AS save_lost_adjustment_video_count,

            -- timeline draft
            SUM(IF(capture_with_camera_mode LIKE "%TIMELINE%", save_start_unique_session, 0)) AS save_attempt_timeline_count,
            SUM(IF(capture_with_camera_mode LIKE "%TIMELINE%", save_lost_unique_session, 0)) AS save_lost_timeline_count,

            SUM(IF(capture_with_camera_mode NOT LIKE "%TIMELINE%", save_start_unique_session, 0)) AS save_attempt_non_timeline_count,
            SUM(IF(capture_with_camera_mode NOT LIKE "%TIMELINE%", save_lost_unique_session, 0)) AS save_lost_non_timeline_count

        FROM
            {source_table}
        GROUP BY
            ts,
            ghost_user_id
        """.format(source_table=source_table),
        metrics=[
                 ## Overall Save Loss
                 SAVE_ATTEMPT,
                 SAVE_LOSS,
                 SAVE_LOSS_GALLERY_SAVE_EVENT_COUNT,
                 LOSS_TO_ATTEMPT_RATE,
                 LOSS_TO_ATTEMPT_RATE_GALLERY_SAVE_EVENT,
                 LOSS_TO_ACTION_RATE,
                 SAVE_ATTEMPT_GALLERY_SAVE_EVENT_START_COUNT, 
                 SAVE_ATTEMPT_GALLERY_SAVE_EVENT_START_LOST_COUNT,
                 
                 ## Operational Save Loss
                 OPERATIONAL_SAVE_LOSS_RATE,
                 PREVIEW_OPERATIONAL_SAVE_LOSS_RATE,
                 NON_PREVIEW_OPERATIONAL_SAVE_LOSS_RATE,
                 GALLERY_SAVE_EVENT_START_COUNT,
                 GALLERY_SAVE_EVENT_LOSS_COUNT,
                 GALLERY_SAVE_EVENT_PREVIEW_START_COUNT,
                 GALLERY_SAVE_EVENT_PREVIEW_LOSS_COUNT,
                 GALLERY_SAVE_EVENT_NON_PREVIEW_START_COUNT,
                 GALLERY_SAVE_EVENT_NON_PREVIEW_LOSS_COUNT,
                 
                 ## Save Loss - Memories Only 
                 SAVE_ATTEMPT_GALLERY_ONLY,
                 SAVE_LOSS_GALLERY_ONLY,
                 SAVE_LOSS_GALLERY_SAVE_EVENT_GALLERY_ONLY_COUNT,
                 LOSS_TO_ATTEMPT_RATE_GALLERY_ONLY,
                 LOSS_TO_ATTEMPT_RATE_GALLERY_SAVE_EVENT_GALLERY_ONLY,
                 LOSS_TO_ACTION_RATE_GALLERY_ONLY,
                 SAVE_ATTEMPT_GALLERY_ONLY_SAVE_EVENT_START_COUNT,
                 SAVE_ATTEMPT_GALLERY_ONLY_SAVE_EVENT_START_LOST_COUNT,
                 
                 ## Save Loss - Memories & Camera Roll
                 SAVE_ATTEMPT_GALLERY_CAMERA_ROLL,
                 SAVE_LOSS_GALLERY_CAMERA_ROLL,
                 SAVE_LOSS_GALLERY_SAVE_EVENT_GALLERY_CAMERA_ROLL_COUNT,
                 LOSS_TO_ATTEMPT_RATE_GALLERY_CAMERA_ROLL,
                 LOSS_TO_ATTEMPT_RATE_GALLERY_SAVE_EVENT_GALLERY_CAMERA_ROLL,
                 LOSS_TO_ACTION_RATE_GALLERY_CAMERA_ROLL,
                 SAVE_ATTEMPT_GALLERY_CAMERA_ROLL_SAVE_EVENT_START_COUNT, 
                 SAVE_ATTEMPT_GALLERY_CAMERA_ROLL_SAVE_EVENT_START_LOST_COUNT,
                 
                 ## Save Loss - Image
                 SAVE_ATTEMPT_IMAGE,
                 SAVE_LOSS_IMAGE,
                 LOSS_TO_ATTEMPT_RATE_IMAGE,
                 LOSS_TO_ATTEMPT_RATE_GALLERY_SAVE_EVENT_IMAGE,
                 LOSS_TO_ACTION_RATE_IMAGE,
                 SAVE_ATTEMPT_GALLERY_SAVE_EVENT_START_LOST_IMAGE_COUNT,
                 SAVE_ATTEMPT_GALLERY_SAVE_EVENT_LOST_IMAGE_COUNT,
                 SAVE_ATTEMPT_GALLERY_SAVE_EVENT_START_IMAGE_COUNT,
                 
                 ## Save Loss - Video
                 SAVE_ATTEMPT_VIDEO,
                 SAVE_LOSS_VIDEO,
                 LOSS_TO_ATTEMPT_RATE_VIDEO,
                 LOSS_TO_ATTEMPT_RATE_GALLERY_SAVE_EVENT_VIDEO,
                 LOSS_TO_ACTION_RATE_VIDEO,
                 SAVE_ATTEMPT_GALLERY_SAVE_EVENT_START_LOST_VIDEO_COUNT,
                 SAVE_ATTEMPT_GALLERY_SAVE_EVENT_LOST_VIDEO_COUNT,
                 SAVE_ATTEMPT_GALLERY_SAVE_EVENT_START_VIDEO_COUNT,
                 
                 ## Save Loss - Memories & Camera Roll Image 
                 SAVE_ATTEMPT_IMAGE_GALLERY_CAMERA_ROLL,
                 SAVE_LOSS_IMAGE_GALLERY_CAMERA_ROLL,
                 LOSS_TO_ATTEMPT_RATE_IMAGE_GALLERY_CAMERA_ROLL,

                 ## Save Loss - Memories & Camera Roll Video 
                 SAVE_ATTEMPT_VIDEO_GALLERY_CAMERA_ROLL,
                 SAVE_LOSS_VIDEO_GALLERY_CAMERA_ROLL,
                 LOSS_TO_ATTEMPT_RATE_VIDEO_GALLERY_CAMERA_ROLL,

                 ## Save Loss Adjustment
                 SAVE_LOSS_ADJUSTMENT,
                 LOSS_TO_ATTEMPT_RATE_ADJUSTMENT,
                 SAVE_LOSS_IMAGE_ADJUSTMENT,
                 LOSS_TO_ATTEMPT_RATE_IMAGE_ADJUSTMENT,
                 SAVE_LOSS_VIDEO_ADJUSTMENT,
                 LOSS_TO_ATTEMPT_RATE_VIDEO_ADJUSTMENT,

                 ## Save Loss - Timeline
                 SAVE_ATTEMPT_TIMELINE,
                 SAVE_LOSS_TIMELINE,
                 LOSS_TO_ATTEMPT_RATE_TIMELINE,

                 ## Save Loss - Non Timeline
                 SAVE_ATTEMPT_NON_TIMELINE,
                 SAVE_LOSS_NON_TIMELINE,
                 LOSS_TO_ATTEMPT_RATE_NON_TIMELINE,
                 ],
        name="memories_save_loss_metrics",
        bq_dialect='standard'
    )
    return mt


def memories_save_loss_uu_metrics(start_date, end_date):
    """
        Parameters
        ----------
        start_date
        end_date
        Returns
        -------
        mt: MetricTable
        >>> memories_backup_loss_uu_metrics('20190301', '20190303')
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
        `sc-analytics.report_memories.lost_content_event_funnel_user_info_v2_2*`
             WHERE CONCAT('2', _TABLE_SUFFIX) BETWEEN '{start}' AND '{end}'
        """.format(
        start=start_date,
        end=end_date,
    )

    SAVE_ATTEMPT_UU = Metric(
        'save_attempt_uu',
        'Save Attempt UU',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    SAVE_LOSS_UU = Metric(
        'save_lost_uu',
        'Save Loss UU',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    LOSS_TO_ATTEMPT_UU_RATE = Metric(
        'loss_to_attempt_uu_rate',
        'UU Save Loss Rate',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_lost_uu',
        denominator='save_attempt_uu',
        desired_direction=NEGATIVE
    )

    SAVE_ATTEMPT_IMAGE_UU = Metric(
        'save_attempt_image_uu',
        'Save Attempt Image UU',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    SAVE_LOSS_IMAGE_UU = Metric(
        'save_lost_image_uu',
        'Save Loss Image UU',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    LOSS_TO_ATTEMPT_UU_RATE_IMAGE = Metric(
        'loss_to_attempt_uu_rate_image',
        'UU Save Loss Rate Image',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_lost_image_uu',
        denominator='save_attempt_image_uu',
        desired_direction=NEGATIVE
    )

    SAVE_ATTEMPT_VIDEO_UU = Metric(
        'save_attempt_video_uu',
        'Save Attempt Video UU',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    SAVE_LOSS_VIDEO_UU = Metric(
        'save_lost_video_uu',
        'Save Loss Video UU',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    LOSS_TO_ATTEMPT_UU_RATE_VIDEO = Metric(
        'loss_to_attempt_uu_rate_video',
        'UU Save Loss Rate Video',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_lost_video_uu',
        denominator='save_attempt_video_uu',
        desired_direction=NEGATIVE
    )

    SAVE_LOSS_ADJUSTMENT_UU = Metric(
        'save_lost_adjustment_uu',
        'Save Loss Adjustment UU',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    LOSS_TO_ATTEMPT_UU_RATE_ADJUSTMENT = Metric(
        'loss_to_attempt_uu_rate_adjustment',
        'UU Save Adjustment Loss Rate',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_lost_adjustment_uu',
        denominator='save_attempt_uu',
        desired_direction=NEGATIVE
    )

    SAVE_LOSS_IMAGE_ADJUSTMENT_UU = Metric(
        'save_lost_adjustment_image_uu',
        'Save Loss Image Adjustment UU',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    LOSS_TO_ATTEMPT_UU_RATE_IMAGE_ADJUSTMENT = Metric(
        'loss_to_attempt_uu_rate_image_adjustment',
        'UU Save Adjustment Loss Rate Image',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_lost_adjustment_image_uu',
        denominator='save_attempt_image_uu',
        desired_direction=NEGATIVE
    )

    SAVE_LOSS_VIDEO_ADJUSTMENT_UU = Metric(
        'save_lost_adjustment_video_uu',
        'Save Loss Video Adjustment UU',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    LOSS_TO_ATTEMPT_UU_RATE_VIDEO_ADJUSTMENT = Metric(
        'loss_to_attempt_uu_rate_video_adjustment',
        'UU Save Adjustment Loss Rate Video',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_lost_adjustment_video_uu',
        denominator='save_attempt_video_uu',
        desired_direction=NEGATIVE
    )

    SAVE_ATTEMPT_TIMELINE_UU = Metric(
        'save_attempt_timeline_uu',
        'Save Attempt Timeline UU',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    SAVE_LOSS_TIMELINE_UU = Metric(
        'save_lost_timeline_uu',
        'Save Loss Timeline UU',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    LOSS_TO_ATTEMPT_UU_RATE_TIMELINE = Metric(
        'loss_to_attempt_uu_rate_timeline',
        'UU Save Loss Rate Timeline',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_lost_timeline_uu',
        denominator='save_attempt_timeline_uu',
        desired_direction=NEGATIVE
    )

    SAVE_ATTEMPT_NON_TIMELINE_UU = Metric(
        'save_attempt_non_timeline_uu',
        'Save Attempt non Timeline UU',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    SAVE_LOSS_NON_TIMELINE_UU = Metric(
        'save_lost_non_timeline_uu',
        'Save Loss non Timeline UU',
        dist='bin',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    LOSS_TO_ATTEMPT_UU_RATE_NON_TIMELINE = Metric(
        'loss_to_attempt_uu_rate_non_timeline',
        'UU Save Loss Rate non Timeline',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_lost_non_timeline_uu',
        denominator='save_attempt_non_timeline_uu',
        desired_direction=NEGATIVE
    )

    mt = MetricTable(
        sql="""
        SELECT
            PARSE_TIMESTAMP('%Y%m%d', CONCAT('2', _TABLE_SUFFIX)) AS ts,
            ghost_user_id,
            MAX(IF(save_start_unique_session>0, 1, 0)) AS save_attempt_uu,
            -- overall save loss
            MAX(IF(save_lost_unique_session>0, 1, 0)) AS save_lost_uu,
            -- destination
            -- camera roll & gallery
            MAX(IF(save_start_gallery_camera_roll_unique_session>0, 1, 0)) AS save_attempt_gallery_camera_roll_uu,
            MAX(IF(save_lost_gallery_camera_roll_unique_session>0, 1, 0)) AS save_lost_gallery_camera_roll_uu,
            # -- gallery
            # SUM(save_start_gallery_unique_session) AS save_attempt_gallery_count,
            # MAX(IF(save_start_gallery_unique_session>0, 1, 0)) AS save_attempt_gallery_uu,
            # SUM(save_lost_gallery_unique_session) AS save_lost_gallery_count,
            # MAX(IF(save_lost_gallery_unique_session>0, 1, 0)) AS save_lost_gallery_uu,
            -- image save loss
            MAX(IF(capture_action_type = "TAP"
                AND save_start_unique_session>0, 1, 0)) AS save_attempt_image_uu,
            MAX(IF(capture_action_type = "TAP"
                AND save_lost_unique_session>0, 1, 0)) AS save_lost_image_uu,
            -- video save loss
            MAX(IF(capture_action_type = "TAP_AND_HOLD"
                AND save_start_unique_session>0, 1, 0)) AS save_attempt_video_uu,
            MAX(IF(capture_action_type = "TAP_AND_HOLD"
                AND save_lost_unique_session>0, 1, 0)) AS save_lost_video_uu,

            -- loss with adjustment
            -- overall
            MAX(IF((save_lost_unique_session - IFNULL(save_lost_adjustment,0))>0, 1, 0)) AS save_lost_adjustment_uu,
            MAX(IF(capture_action_type = "TAP"
                AND (save_lost_unique_session - IFNULL(save_lost_adjustment,0))>0, 1, 0)) AS save_lost_adjustment_image_uu,
            MAX(IF(capture_action_type = "TAP_AND_HOLD"
                AND (save_lost_unique_session - IFNULL(save_lost_adjustment,0))>0, 1, 0)) AS save_lost_adjustment_video_uu,

            -- timeline draft
            MAX(IF(capture_with_camera_mode LIKE "%TIMELINE%" AND save_start_unique_session>0, 1, 0)) AS save_attempt_timeline_uu,
            MAX(IF(capture_with_camera_mode LIKE "%TIMELINE%" AND save_lost_unique_session>0, 1, 0)) AS save_lost_timeline_uu,

            MAX(IF(capture_with_camera_mode NOT LIKE "%TIMELINE%" AND save_start_unique_session>0, 1, 0)) AS save_attempt_non_timeline_uu,
            MAX(IF(capture_with_camera_mode NOT LIKE "%TIMELINE%" AND save_lost_unique_session>0, 1, 0)) AS save_lost_non_timeline_uu,

        FROM
            {source_table}
        GROUP BY
            ts,
            ghost_user_id
        """.format(source_table=source_table),
        metrics=[
            SAVE_ATTEMPT_UU,
            SAVE_LOSS_UU,
            LOSS_TO_ATTEMPT_UU_RATE,

            SAVE_ATTEMPT_IMAGE_UU,
            SAVE_LOSS_IMAGE_UU,
            LOSS_TO_ATTEMPT_UU_RATE_IMAGE,
            SAVE_ATTEMPT_VIDEO_UU,
            SAVE_LOSS_VIDEO_UU,
            LOSS_TO_ATTEMPT_UU_RATE_VIDEO,

            ##
            SAVE_LOSS_ADJUSTMENT_UU,
            LOSS_TO_ATTEMPT_UU_RATE_ADJUSTMENT,
            SAVE_LOSS_IMAGE_ADJUSTMENT_UU,
            LOSS_TO_ATTEMPT_UU_RATE_IMAGE_ADJUSTMENT,
            SAVE_LOSS_VIDEO_ADJUSTMENT_UU,
            LOSS_TO_ATTEMPT_UU_RATE_VIDEO_ADJUSTMENT,

            ##
            SAVE_ATTEMPT_TIMELINE_UU,
            SAVE_LOSS_TIMELINE_UU,
            LOSS_TO_ATTEMPT_UU_RATE_TIMELINE,

            SAVE_ATTEMPT_NON_TIMELINE_UU,
            SAVE_LOSS_NON_TIMELINE_UU,
            LOSS_TO_ATTEMPT_UU_RATE_NON_TIMELINE,
        ],
        name="memories_save_loss_uu_metrics",
        bq_dialect='standard'
    )
    return mt


def camera_roll_save_loss_metrics(start_date, end_date): 
    """
        Parameters
        ----------
        start_date
        end_date
        Returns
        -------
        mt: MetricTable
        >>> memories_backup_loss_metrics('20190301', '20190303')
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
        `sc-analytics.report_memories.lost_content_event_funnel_user_info_v2_2*`
             WHERE CONCAT('2', _TABLE_SUFFIX) BETWEEN '{start}' AND '{end}'
        """.format(
        start=start_date,
        end=end_date,
    )

    # overall camera roll save loss metrics

    CAMERA_ROLL_SAVE_ATTEMPT_COUNT = Metric(
        'camera_roll_save_start_count',
        'Camera Roll Save Attempt Count',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    CAMERA_ROLL_SAVE_LOSS_COUNT = Metric(
        'camera_roll_save_lost_count',
        'Camera Roll Save Loss Count',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    ) 

    CAMERA_ROLL_SAVE_LOSS_UU = Metric(
        'camera_roll_save_lost_uu',
        'Camera Roll Save Loss UU',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    ) 

    CAMERA_ROLL_CAPTURE_SESSION_SAVE_LOSS_RATE = Metric(
        'camera_roll_capture_session_save_loss_rate',
        'Camera Roll Capture Session Save Loss Rate',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='camera_roll_save_lost_count',
        denominator='camera_roll_save_start_count',
        desired_direction=NEGATIVE
    )

    SAVE_ATTEMPT_CAMERA_ROLL_ONLY = Metric(
        'save_attempt_camera_roll_only_count',
        'Save Attempt - Camera Roll Only',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    SAVE_LOSS_CAMERA_ROLL_ONLY = Metric(
        'save_lost_camera_roll_only_count',
        'Save Loss - Camera Roll Only',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    LOSS_TO_ATTEMPT_RATE_CAMERA_ROLL_ONLY = Metric(
        'loss_to_attempt_rate_camera_roll_only',
        'Save Loss Rate - Camera Roll Only',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_lost_camera_roll_only_count',
        denominator='save_attempt_camera_roll_only_count',
        desired_direction=NEGATIVE
    )

    SAVE_ATTEMPT_GALLERY_CAMERA_ROLL = Metric( 
        'save_attempt_gallery_camera_roll_count',
        'Save Attempt - Memories & Camera Roll',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    SAVE_LOSS_GALLERY_CAMERA_ROLL_CAMERA_ROLL_PATH = Metric( 
        'save_lost_camera_roll_in_gallery_camera_roll_count',
        'Save Loss - Memories & Camera Roll (Camera Roll path)',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    LOSS_TO_ATTEMPT_RATE_GALLERY_CAMERA_ROLL_CAMERA_ROLL_PATH = Metric( 
        'loss_to_attempt_rate_gallery_camera_roll_camera_roll_path',
        'Save Loss Rate - Memories & Camera Roll (Camera Roll path)',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='save_lost_camera_roll_in_gallery_camera_roll_count',
        denominator='save_attempt_gallery_camera_roll_count',
        desired_direction=NEGATIVE
    )

    # by media type: image

    CAMERA_ROLL_SAVE_ATTEMPT_IMAGE_COUNT = Metric(
        'camera_roll_save_start_image_count',
        'Camera Roll Save Attempt Image Count',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    CAMERA_ROLL_SAVE_LOSS_IMAGE_COUNT = Metric(
        'camera_roll_save_lost_image_count',
        'Camera Roll Save Loss Image Count',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    ) 

    CAMERA_ROLL_SAVE_LOSS_IMAGE_UU = Metric(
        'camera_roll_save_lost_image_uu',
        'Camera Roll Save Loss Image UU',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    ) 

    CAMERA_ROLL_CAPTURE_SESSION_SAVE_LOSS_IMAGE_RATE = Metric(
        'camera_roll_capture_session_save_loss_image_rate',
        'Camera Roll Capture Session Save Loss Image Rate',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='camera_roll_save_lost_image_count',
        denominator='camera_roll_save_start_image_count',
        desired_direction=NEGATIVE
    )

    # by media type: Video

    CAMERA_ROLL_SAVE_ATTEMPT_VIDEO_COUNT = Metric(
        'camera_roll_save_start_video_count',
        'Camera Roll Save Attempt Video Count',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    CAMERA_ROLL_SAVE_LOSS_VIDEO_COUNT = Metric(
        'camera_roll_save_lost_video_count',
        'Camera Roll Save Loss Video Count',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    ) 

    CAMERA_ROLL_SAVE_LOSS_VIDEO_UU = Metric(
        'camera_roll_save_lost_video_uu',
        'Camera Roll Save Loss Video UU',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    ) 

    CAMERA_ROLL_CAPTURE_SESSION_SAVE_LOSS_VIDEO_RATE = Metric(
        'camera_roll_capture_session_save_loss_video_rate',
        'Camera Roll Capture Session Save Loss Video Rate',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='camera_roll_save_lost_video_count',
        denominator='camera_roll_save_start_video_count',
        desired_direction=NEGATIVE
    )

    # with lens
    CAMERA_ROLL_SAVE_ATTEMPT_WITH_LENS_COUNT = Metric(
        'camera_roll_save_start_with_lens_count',
        'Camera Roll Save Attempt With Lens Count',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    CAMERA_ROLL_SAVE_LOSS_WITH_LENS_COUNT = Metric(
        'camera_roll_save_lost_with_lens_count',
        'Camera Roll Save Loss With Lens Count',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    ) 

    CAMERA_ROLL_SAVE_LOSS_WITH_LENS_UU = Metric(
        'camera_roll_save_lost_with_lens_uu',
        'Camera Roll Save Loss With Lens UU',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    ) 

    CAMERA_ROLL_CAPTURE_SESSION_SAVE_LOSS_WITH_LENS_RATE = Metric(
        'camera_roll_capture_session_save_loss_with_lens_rate',
        'Camera Roll Capture Session Save Loss with Lens Rate',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='camera_roll_save_lost_with_lens_count',
        denominator='camera_roll_save_start_with_lens_count',
        desired_direction=NEGATIVE
    )


    # without lens
    CAMERA_ROLL_SAVE_ATTEMPT_WITHOUT_LENS_COUNT = Metric(
        'camera_roll_save_start_without_lens_count',
        'Camera Roll Save Attempt Without Lens Count',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    CAMERA_ROLL_SAVE_LOSS_WITHOUT_LENS_COUNT = Metric(
        'camera_roll_save_lost_without_lens_count',
        'Camera Roll Save Loss Without Lens Count',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    ) 

    CAMERA_ROLL_SAVE_LOSS_WITHOUT_LENS_UU = Metric(
        'camera_roll_save_lost_without_lens_uu',
        'Camera Roll Save Loss Without Lens UU',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    ) 

    CAMERA_ROLL_CAPTURE_SESSION_SAVE_LOSS_WITHOUT_LENS_RATE = Metric(
        'camera_roll_capture_session_save_loss_without_lens_rate',
        'Camera Roll Capture Session Save Loss without Lens Rate',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='camera_roll_save_lost_without_lens_count',
        denominator='camera_roll_save_start_without_lens_count',
        desired_direction=NEGATIVE
    )

    mt = MetricTable(
    sql="""
        SELECT
            PARSE_TIMESTAMP('%Y%m%d', CONCAT('2', _TABLE_SUFFIX)) AS ts,
            ghost_user_id,
            SUM(save_start_camera_roll_unique_session) AS camera_roll_save_start_count, 
            SUM(IF(save_lost_after_capture_save_unique_session_camera_roll IS NULL, 0, save_lost_after_capture_save_unique_session_camera_roll - IFNULL(save_lost_adjustment,0))) AS camera_roll_save_lost_count,
            MAX(IF(save_lost_after_capture_save_unique_session_camera_roll IS NOT NULL AND (save_lost_after_capture_save_unique_session_camera_roll - IFNULL(save_lost_adjustment,0))>0, 1, 0)) AS camera_roll_save_lost_uu,
            SUM(save_start_camera_roll_only_unique_session) AS save_attempt_camera_roll_only_count,
            SUM(save_lost_camera_roll_only_unique_session) AS save_lost_camera_roll_only_count,
            SUM(save_start_gallery_camera_roll_unique_session) AS save_attempt_gallery_camera_roll_count, 
            SUM(save_lost_camera_roll_in_gallery_camera_roll_unique_session) AS save_lost_camera_roll_in_gallery_camera_roll_count,
            SUM(IF(capture_action_type = 'TAP', save_start_camera_roll_unique_session, 0)) AS camera_roll_save_start_image_count, 
            SUM(IF(capture_action_type = 'TAP', IF(save_lost_after_capture_save_unique_session_camera_roll IS NULL, 0, save_lost_after_capture_save_unique_session_camera_roll - IFNULL(save_lost_adjustment,0)),0)) AS camera_roll_save_lost_image_count, 
            MAX(IF(capture_action_type = 'TAP' AND (IF(save_lost_after_capture_save_unique_session_camera_roll IS NULL, 0, save_lost_after_capture_save_unique_session_camera_roll - IFNULL(save_lost_adjustment,0))>0), 1, 0)) AS camera_roll_save_lost_image_uu,
            SUM(IF(capture_action_type = 'TAP_AND_HOLD', save_start_camera_roll_unique_session, 0)) AS camera_roll_save_start_video_count, 
            SUM(IF(capture_action_type = 'TAP_AND_HOLD', IF(save_lost_after_capture_save_unique_session_camera_roll IS NULL, 0, save_lost_after_capture_save_unique_session_camera_roll - IFNULL(save_lost_adjustment,0)),0)) AS camera_roll_save_lost_video_count, 
            MAX(IF(capture_action_type = 'TAP_AND_HOLD' AND (IF(save_lost_after_capture_save_unique_session_camera_roll IS NULL, 0, save_lost_after_capture_save_unique_session_camera_roll - IFNULL(save_lost_adjustment,0))>0), 1, 0)) AS camera_roll_save_lost_video_uu,
            # lens
            SUM(IF(capture_with_lens = '1', save_start_camera_roll_unique_session, 0)) AS camera_roll_save_start_with_lens_count, 
            SUM(IF(capture_with_lens = '1', IF(save_lost_after_capture_save_unique_session_camera_roll IS NULL, 0, save_lost_after_capture_save_unique_session_camera_roll - IFNULL(save_lost_adjustment,0)),0)) AS camera_roll_save_lost_with_lens_count, 
            MAX(IF(capture_with_lens = '1' AND (IF(save_lost_after_capture_save_unique_session_camera_roll IS NULL, 0, save_lost_after_capture_save_unique_session_camera_roll - IFNULL(save_lost_adjustment,0))>0), 1, 0)) AS camera_roll_save_lost_with_lens_uu,
            # without lens 
            SUM(IF(capture_with_lens IN ('0', ''), save_start_camera_roll_unique_session, 0)) AS camera_roll_save_start_without_lens_count, 
            SUM(IF(capture_with_lens IN ('0', ''), IF(save_lost_after_capture_save_unique_session_camera_roll IS NULL, 0, save_lost_after_capture_save_unique_session_camera_roll - IFNULL(save_lost_adjustment,0)),0)) AS camera_roll_save_lost_without_lens_count, 
            MAX(IF(capture_with_lens IN ('0', '') AND (IF(save_lost_after_capture_save_unique_session_camera_roll IS NULL, 0, save_lost_after_capture_save_unique_session_camera_roll - IFNULL(save_lost_adjustment,0))>0), 1, 0)) AS camera_roll_save_lost_without_lens_uu
            
            # save to camera roll only metrics
            #SUM(save_start_camera_roll_unique_session) AS save_attempt_gallery_only_count,
            #SUM(save_fail_gallery_only_unique_session) AS save_lost_gallery_only_count,
        FROM
            {source_table}
        GROUP BY
            ts,
            ghost_user_id
        """.format(source_table=source_table),
        metrics=[CAMERA_ROLL_SAVE_ATTEMPT_COUNT,
                 CAMERA_ROLL_SAVE_LOSS_COUNT,
                 CAMERA_ROLL_SAVE_LOSS_UU,
                 CAMERA_ROLL_CAPTURE_SESSION_SAVE_LOSS_RATE,
                 SAVE_ATTEMPT_CAMERA_ROLL_ONLY,
                 SAVE_LOSS_CAMERA_ROLL_ONLY,
                 LOSS_TO_ATTEMPT_RATE_CAMERA_ROLL_ONLY,
                 SAVE_ATTEMPT_GALLERY_CAMERA_ROLL,
                 SAVE_LOSS_GALLERY_CAMERA_ROLL_CAMERA_ROLL_PATH,
                 LOSS_TO_ATTEMPT_RATE_GALLERY_CAMERA_ROLL_CAMERA_ROLL_PATH,
                 CAMERA_ROLL_SAVE_ATTEMPT_IMAGE_COUNT,
                 CAMERA_ROLL_SAVE_LOSS_IMAGE_COUNT,
                 CAMERA_ROLL_SAVE_LOSS_IMAGE_UU,
                 CAMERA_ROLL_CAPTURE_SESSION_SAVE_LOSS_IMAGE_RATE,
                 CAMERA_ROLL_SAVE_ATTEMPT_VIDEO_COUNT,
                 CAMERA_ROLL_SAVE_LOSS_VIDEO_COUNT,
                 CAMERA_ROLL_SAVE_LOSS_VIDEO_UU,
                 CAMERA_ROLL_CAPTURE_SESSION_SAVE_LOSS_VIDEO_RATE,
                 CAMERA_ROLL_SAVE_ATTEMPT_WITH_LENS_COUNT,
                 CAMERA_ROLL_SAVE_LOSS_WITH_LENS_COUNT,
                 CAMERA_ROLL_SAVE_LOSS_WITH_LENS_UU,
                 CAMERA_ROLL_CAPTURE_SESSION_SAVE_LOSS_WITH_LENS_RATE,
                 CAMERA_ROLL_SAVE_ATTEMPT_WITHOUT_LENS_COUNT,
                 CAMERA_ROLL_SAVE_LOSS_WITHOUT_LENS_COUNT,
                 CAMERA_ROLL_SAVE_LOSS_WITHOUT_LENS_UU,
                 CAMERA_ROLL_CAPTURE_SESSION_SAVE_LOSS_WITHOUT_LENS_RATE
                ],
        name="camera_roll_save_loss_metrics",
        bq_dialect='standard'
    )
    return mt
    

def gallery_save_event_memories_save_loss_metrics(start_date, end_date): 
    """
        Parameters
        ----------
        start_date
        end_date
        Returns
        -------
        mt: MetricTable
        >>> gallery_save_event_memories_save_loss_metrics('20190301', '20190303')
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
        `sc-analytics.prod_analytics_snap.daily_events_2*`
             WHERE CONCAT('2', _TABLE_SUFFIX) BETWEEN '{start}' AND '{end}'
        """.format(
        start=start_date,
        end=end_date,
    )

    # gallery save event memories save loss metrics
    MEMORIES_SAVE_ATTEMPT_COUNT = Metric(
        'memories_save_start_count',
        'Memories Save Attempt Count',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=POSITIVE
    )

    MEMORIES_SAVE_LOSS_COUNT = Metric(
        'gallery_save_event_save_lost_count',
        'Memories Save Loss Count',
        dist='cont',
        daily=False,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    MEMORIES_SAVE_LOSS_RATE = Metric(
        'gallery_save_event_save_loss_rate',
        'Memories Save Loss Rate',
        dist='ratio',
        daily=False,
        cumulative=True,
        numerator='gallery_save_event_save_lost_count',
        denominator='memories_save_start_count',
        desired_direction=NEGATIVE
    )

    mt = MetricTable(
    sql="""
        WITH save_session AS (
            SELECT
                PARSE_TIMESTAMP('%Y%m%d', CONCAT('2', _TABLE_SUFFIX)) AS ts,
                ghost_user_id,        
                capture_session_id,
                MIN(IF(event_name = 'DIRECT_SNAP_CREATE' , 1, NULL)) AS has_capture_done,
                MIN(IF(event_name = 'DIRECT_SNAP_ACTION'
                AND (page IS NULL OR page IN ('GALLERY','MEMORIES'))
                AND (with_gallery = TRUE), 1, NULL)) AS has_save_start, -- ONLY FOR Memories save start
                MIN(IF(event_name = "GALLERY_SAVE_EVENT"
                    AND (step = "FINISH"), 1, NULL)) AS has_save_done, -- NEW DEFINITION
                NULL AS has_save_odp_done,
                MIN(IF(event_name = 'CAMERA_CONTENT_RECOVERY_ATTEMPT'
                    AND (success = TRUE), 1, NULL)) AS has_recovery_success,
            FROM {source_table}
            GROUP BY 1,2,3)
            SELECT
            ts,
            ghost_user_id,
            SUM(save_start_unique_session) as memories_save_start_count,
            SUM(gallery_save_event_save_lost_unique_session) as gallery_save_event_save_lost_count,
            SUM(gallery_save_event_save_lost_after_capture_save_unique_session) as gallery_save_event_save_lost_after_capture_save_count,
            SUM(gallery_save_event_save_lost_without_odp_unique_session) as gallery_save_event_save_lost_without_odp_count
            FROM
            (
                SELECT
                    ts,
                    ghost_user_id,
                    SUM(IF(has_save_start IS NOT NULL, 1, 0)) AS save_start_unique_session,
                    SUM(IF(has_save_start IS NOT NULL
                        AND has_save_done IS NULL
                        AND has_save_odp_done IS NULL,1,0)) AS gallery_save_event_save_lost_unique_session,
                    SUM(IF(has_capture_done IS NOT NULL
                        AND has_save_start IS NOT NULL
                        AND has_save_done IS NULL
                        AND has_save_odp_done IS NULL
                        AND has_recovery_success IS NULL, 1, 0)) AS gallery_save_event_save_lost_after_capture_save_unique_session,
                    SUM(IF(has_save_start IS NOT NULL
                        AND has_save_done IS NULL
                        AND has_recovery_success IS NULL, 1, 0)) AS gallery_save_event_save_lost_without_odp_unique_session,
                FROM save_session
                GROUP BY ts, ghost_user_id)
            GROUP BY 1,2
        """.format(source_table=source_table),
        metrics=[MEMORIES_SAVE_ATTEMPT_COUNT,
                 MEMORIES_SAVE_LOSS_COUNT,
                 MEMORIES_SAVE_LOSS_RATE
                ],
        name="gallery_save_event_memories_save_loss_metrics",
        bq_dialect='standard'
    )
    return mt
    