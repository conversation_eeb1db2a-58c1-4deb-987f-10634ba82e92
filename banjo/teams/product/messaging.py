""" Messaging metrics
Contact: asavino@ mzhang5@ zwang9@ yzhu7@
"""

from __future__ import division, print_function
from banjo.abtest.metric import (
    Metric, MetricTable, abtest_metrics_sql_helper
)
from datetime import timedelta
import pandas as pd
import logging
from banjo.abtest.metric import POSITIVE, NEGATIVE, NEUTRAL
from collections import OrderedDict
from banjo import abtest
from banjo.abtest.report import (
    Metric, MetricTable
)
from banjo.teams.personalization.abtest import discover_feed_metrics as dfab
from pandas.api.types import CategoricalDtype

import re

from banjo.abtest.field_breakdown_metric_table import FieldBreakdownMetricTable

logger = logging.getLogger(__name__)

logger.warning("Messaging metrics imported.  Check the actual "
               "queries run to ensure correctness")

__all__ = [
      'chat_metrics',
      'chat_detil_metrics',
      'snap_detail_metrics',
      'snap_save_delete_metrics',
      'chat_save_metrics',
      'new_chat_page',
      'voice_notes',
      'share_extension_metrics',
      'non_friend_metrics',
      'chat_erase_mode_metrics',
      'friend_feed_shortcut_metrics',
      'relationship_closeness_metrics',
      'notification_platform_metrics',
      'notification_to_message_ready_failure_metrics',
      'notification_to_message_ready_latency_metrics',
      "notification_os_breakdown_metrics",
      "friend_feed_state_metrics",
      "sponsored_snap_metrics"
    ]




def chat_metrics(start_date, end_date):
#     start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
#     end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    start_date, end_date = start_date[-6:], end_date[-6:]
    

    chat_create=Metric(col="chat_create", dist="cont", daily=True, cumulative=True)
    chat_create_group=Metric(col="chat_create_group", dist="cont", daily=True, cumulative=True)
    chat_create_ind=Metric(col="chat_create_ind", dist="cont", daily=True, cumulative=True)
    chat_create_feed=Metric(col="chat_create_feed", dist="cont", daily=True, cumulative=True)
    chat_create_notification=Metric(col="chat_create_notification", dist="cont", daily=True, cumulative=True)
    chat_create_story_reply=Metric(col="chat_create_story_reply", dist="cont", daily=True, cumulative=True)
    chat_create_snap_reply=Metric(col="chat_create_snap_reply", dist="cont", daily=True, cumulative=True)
    chat_create_search=Metric(col="chat_create_search", dist="cont", daily=True, cumulative=True)
    chat_create_in_app_notification=Metric(col="chat_create_in_app_notification", dist="cont", daily=True, cumulative=True)
    chat_create_new_chat=Metric(col="chat_create_new_chat", dist="cont", daily=True, cumulative=True)
    chat_create_other=Metric(col="chat_create_other", dist="cont", daily=True, cumulative=True)
    chat_page_enter_from_siri_shortcut_count=Metric(col="chat_page_enter_from_siri_shortcut_count", dist="cont", daily=True, cumulative=True)

    chat_send=Metric(col="chat_send", dist="cont", daily=True, cumulative=True)
    chat_send_group=Metric(col="chat_send_group", dist="cont", daily=True, cumulative=True)
    chat_send_ind=Metric(col="chat_send_ind", dist="cont", daily=True, cumulative=True)
    chat_send_unique_chat_id=Metric(col="chat_send_unique_chat_id", dist="cont", daily=True, cumulative=True)
    chat_send_feed=Metric(col="chat_send_feed", dist="cont", daily=True, cumulative=True)
    chat_send_notification=Metric(col="chat_send_notification", dist="cont", daily=True, cumulative=True)
    chat_send_source_story_reply=Metric(col="chat_send_source_story_reply", dist="cont", daily=True, cumulative=True)
    chat_send_snap_reply=Metric(col="chat_send_snap_reply", dist="cont", daily=True, cumulative=True)
    chat_send_search=Metric(col="chat_send_search", dist="cont", daily=True, cumulative=True)
    chat_send_in_app_notification=Metric(col="chat_send_in_app_notification", dist="cont", daily=True, cumulative=True)
    chat_send_new_chat=Metric(col="chat_send_new_chat", dist="cont", daily=True, cumulative=True)
    chat_send_souce_other=Metric(col="chat_send_souce_other", dist="cont", daily=True, cumulative=True)
    CHAT_CHAT_SEND_FF_SHORTCUT=Metric(col="CHAT_CHAT_SEND_FF_SHORTCUT", dist="cont", daily=True, cumulative=True)
    CHAT_CHAT_SEND_FF_SHORTCUT_UNREAD_CONVERSATIONS=Metric(col="CHAT_CHAT_SEND_FF_SHORTCUT_UNREAD_CONVERSATIONS", dist="cont", daily=True, cumulative=True)
    CHAT_CHAT_SEND_FF_SHORTCUT_GROUPS=Metric(col="CHAT_CHAT_SEND_FF_SHORTCUT_GROUPS", dist="cont", daily=True, cumulative=True)
    CHAT_CHAT_SEND_FF_SHORTCUT_STORIES=Metric(col="CHAT_CHAT_SEND_FF_SHORTCUT_STORIES", dist="cont", daily=True, cumulative=True)
    CHAT_CHAT_SEND_FF_SHORTCUT_BESTFRIENDS=Metric(col="CHAT_CHAT_SEND_FF_SHORTCUT_BESTFRIENDS", dist="cont", daily=True, cumulative=True)
    CHAT_CHAT_SEND_FF_SHORTCUT_CUSTOM=Metric(col="CHAT_CHAT_SEND_FF_SHORTCUT_CUSTOM", dist="cont", daily=True, cumulative=True)
    CHAT_CHAT_SEND_FF_SHORTCUT_MYAI=Metric(col="CHAT_CHAT_SEND_FF_SHORTCUT_MYAI", dist="cont", daily=True, cumulative=True)
    CHAT_CHAT_SEND_FF_SHORTCUT_STREAKS=Metric(col="CHAT_CHAT_SEND_FF_SHORTCUT_STREAKS", dist="cont", daily=True, cumulative=True)
    chat_send_text=Metric(col="chat_send_text", dist="cont", daily=True, cumulative=True)
    chat_send_note=Metric(col="chat_send_note", dist="cont", daily=True, cumulative=True)
    chat_send_sticker=Metric(col="chat_send_sticker", dist="cont", daily=True, cumulative=True)
    chat_send_media=Metric(col="chat_send_media", dist="cont", daily=True, cumulative=True)
    chat_send_media_image=Metric(col="chat_send_media_image", dist="cont", daily=True, cumulative=True)
    chat_send_media_video=Metric(col="chat_send_media_video", dist="cont", daily=True, cumulative=True)
    chat_send_media_video_no_sound=Metric(col="chat_send_media_video_no_sound", dist="cont", daily=True, cumulative=True)
    chat_send_story_reply=Metric(col="chat_send_story_reply", dist="cont", daily=True, cumulative=True)
    chat_send_without_story_reply=Metric(col="chat_send_without_story_reply", dist="cont", daily=True, cumulative=True)
    chat_send_other=Metric(col="chat_send_other", dist="cont", daily=True, cumulative=True)
    chat_send_content_share_total=Metric(col="chat_send_content_share_total", dist="cont", daily=True, cumulative=True)
    chat_send_story_share=Metric(col="chat_send_story_share", dist="cont", daily=True, cumulative=True)
    chat_send_non_friend_story_share=Metric(col="chat_send_non_friend_story_share", dist="cont", daily=True, cumulative=True)
    chat_send_spotlight_story_share=Metric(col="chat_send_spotlight_story_share", dist="cont", daily=True, cumulative=True)
    chat_send_map_story_share=Metric(col="chat_send_map_story_share", dist="cont", daily=True, cumulative=True)
    chat_send_with_phone_number=Metric(col="chat_send_with_phone_number", dist="cont", daily=True, cumulative=True)
    chat_send_with_location=Metric(col="chat_send_with_location", dist="cont", daily=True, cumulative=True)
    chat_send_with_url=Metric(col="chat_send_with_url", dist="cont", daily=True, cumulative=True)
    chat_reply_send=Metric(col="chat_reply_send", dist="cont", daily=True, cumulative=True)
    chat_reply_send_initiation_type_null=Metric(col="chat_reply_send_initiation_type_null", dist="cont", daily=True, cumulative=True)
    chat_reply_send_message_action_menu=Metric(col="chat_reply_send_message_action_menu", dist="cont", daily=True, cumulative=True)
    chat_reply_send_message_swipe=Metric(col="chat_reply_send_message_swipe", dist="cont", daily=True, cumulative=True)
    chat_reply_send_media_swipe_up=Metric(col="chat_reply_send_media_swipe_up", dist="cont", daily=True, cumulative=True)
    chat_reply_send_media_action_menu=Metric(col="chat_reply_send_media_action_menu", dist="cont", daily=True, cumulative=True)
    chat_bitmoji_reaction_send=Metric(col="chat_bitmoji_reaction_send", dist="cont", daily=True, cumulative=True)
    chat_bitmoji_reaction_send_action_menu=Metric(col="chat_bitmoji_reaction_send_action_menu", dist="cont", daily=True, cumulative=True)
    chat_bitmoji_reaction_send_detail_view=Metric(col="chat_bitmoji_reaction_send_detail_view", dist="cont", daily=True, cumulative=True)
    chat_combined_reaction_send=Metric(col="chat_combined_reaction_send", dist="cont", daily=True, cumulative=True)
    chat_emoji_reaction_send=Metric(col="chat_emoji_reaction_send", dist="cont", daily=True, cumulative=True)
    CHAT_SEND_CELL_POSITION_0=Metric(col="CHAT_SEND_CELL_POSITION_0", dist="cont", daily=True, cumulative=True)
    CHAT_SEND_CELL_POSITION_1=Metric(col="CHAT_SEND_CELL_POSITION_1", dist="cont", daily=True, cumulative=True)
    CHAT_SEND_CELL_POSITION_2=Metric(col="CHAT_SEND_CELL_POSITION_2", dist="cont", daily=True, cumulative=True)
    CHAT_SEND_CELL_POSITION_3=Metric(col="CHAT_SEND_CELL_POSITION_3", dist="cont", daily=True, cumulative=True)
    CHAT_SEND_CELL_POSITION_4=Metric(col="CHAT_SEND_CELL_POSITION_4", dist="cont", daily=True, cumulative=True)
    CHAT_SEND_CELL_POSITION_5=Metric(col="CHAT_SEND_CELL_POSITION_5", dist="cont", daily=True, cumulative=True)
    CHAT_SEND_CELL_POSITION_6=Metric(col="CHAT_SEND_CELL_POSITION_6", dist="cont", daily=True, cumulative=True)
    CHAT_SEND_CELL_POSITION_7=Metric(col="CHAT_SEND_CELL_POSITION_7", dist="cont", daily=True, cumulative=True)
    CHAT_SEND_CELL_POSITION_8=Metric(col="CHAT_SEND_CELL_POSITION_8", dist="cont", daily=True, cumulative=True)
    CHAT_SEND_CELL_POSITION_larger_than_8=Metric(col="CHAT_SEND_CELL_POSITION_larger_than_8", dist="cont", daily=True, cumulative=True)

    chat_view=Metric(col="chat_view", dist="cont", daily=True, cumulative=True)
    chat_view_group=Metric(col="chat_view_group", dist="cont", daily=True, cumulative=True)
    chat_view_ind=Metric(col="chat_view_ind", dist="cont", daily=True, cumulative=True)
    chat_page_view=Metric(col="chat_page_view", dist="cont", daily=True, cumulative=True)
    chat_page_view_group=Metric(col="chat_page_view_group", dist="cont", daily=True, cumulative=True)
    chat_page_view_ind=Metric(col="chat_page_view_ind", dist="cont", daily=True, cumulative=True)
    chat_view_unique_chat_id=Metric(col="chat_view_unique_chat_id", dist="cont", daily=True, cumulative=True)
    chat_view_feed=Metric(col="chat_view_feed", dist="cont", daily=True, cumulative=True)
    chat_view_notification=Metric(col="chat_view_notification", dist="cont", daily=True, cumulative=True)
    chat_view_null=Metric(col="chat_view_null", dist="cont", daily=True, cumulative=True)
    chat_view_source_other=Metric(col="chat_view_source_other", dist="cont", daily=True, cumulative=True)
    chat_view_text=Metric(col="chat_view_text", dist="cont", daily=True, cumulative=True)
    chat_view_media=Metric(col="chat_view_media", dist="cont", daily=True, cumulative=True)
    chat_view_media_image=Metric(col="chat_view_media_image", dist="cont", daily=True, cumulative=True)
    chat_view_media_video=Metric(col="chat_view_media_video", dist="cont", daily=True, cumulative=True)
    chat_view_media_video_no_sound=Metric(col="chat_view_media_video_no_sound", dist="cont", daily=True, cumulative=True)
    chat_view_sticker=Metric(col="chat_view_sticker", dist="cont", daily=True, cumulative=True)
    chat_view_note=Metric(col="chat_view_note", dist="cont", daily=True, cumulative=True)
    chat_view_story_reply=Metric(col="chat_view_story_reply", dist="cont", daily=True, cumulative=True)
    chat_view_other=Metric(col="chat_view_other", dist="cont", daily=True, cumulative=True)
    chat_view_with_phone_number=Metric(col="chat_view_with_phone_number", dist="cont", daily=True, cumulative=True)
    chat_view_with_location=Metric(col="chat_view_with_location", dist="cont", daily=True, cumulative=True)
    chat_view_with_url=Metric(col="chat_view_with_url", dist="cont", daily=True, cumulative=True)
    chat_reply_view=Metric(col="chat_reply_view", dist="cont", daily=True, cumulative=True)
    chat_reply_view_status_available=Metric(col="chat_reply_view_status_available", dist="cont", daily=True, cumulative=True)
    chat_reply_view_status_deleted=Metric(col="chat_reply_view_status_deleted", dist="cont", daily=True, cumulative=True)
    chat_reply_view_status_unavailable=Metric(col="chat_reply_view_status_unavailable", dist="cont", daily=True, cumulative=True)
    chat_reply_view_status_joined_after_message_sent=Metric(col="chat_reply_view_status_joined_after_message_sent", dist="cont", daily=True, cumulative=True)
    chat_reply_status_unknown=Metric(col="chat_reply_status_unknown", dist="cont", daily=True, cumulative=True)
    chat_bitmoji_reaction_view=Metric(col="chat_bitmoji_reaction_view", dist="cont", daily=True, cumulative=True)
    chat_combined_reaction_view=Metric(col="chat_combined_reaction_view", dist="cont", daily=True, cumulative=True)
    chat_emoji_reaction_view=Metric(col="chat_emoji_reaction_view", dist="cont", daily=True, cumulative=True)
    CHAT_VIEW_CELL_POSITION_0=Metric(col="CHAT_VIEW_CELL_POSITION_0", dist="cont", daily=True, cumulative=True)
    CHAT_VIEW_CELL_POSITION_1=Metric(col="CHAT_VIEW_CELL_POSITION_1", dist="cont", daily=True, cumulative=True)
    CHAT_VIEW_CELL_POSITION_2=Metric(col="CHAT_VIEW_CELL_POSITION_2", dist="cont", daily=True, cumulative=True)
    CHAT_VIEW_CELL_POSITION_3=Metric(col="CHAT_VIEW_CELL_POSITION_3", dist="cont", daily=True, cumulative=True)
    CHAT_VIEW_CELL_POSITION_4=Metric(col="CHAT_VIEW_CELL_POSITION_4", dist="cont", daily=True, cumulative=True)
    CHAT_VIEW_CELL_POSITION_5=Metric(col="CHAT_VIEW_CELL_POSITION_5", dist="cont", daily=True, cumulative=True)
    CHAT_VIEW_CELL_POSITION_6=Metric(col="CHAT_VIEW_CELL_POSITION_6", dist="cont", daily=True, cumulative=True)
    CHAT_VIEW_CELL_POSITION_7=Metric(col="CHAT_VIEW_CELL_POSITION_7", dist="cont", daily=True, cumulative=True)
    CHAT_VIEW_CELL_POSITION_8=Metric(col="CHAT_VIEW_CELL_POSITION_8", dist="cont", daily=True, cumulative=True)
    CHAT_VIEW_CELL_POSITION_larger_than_8=Metric(col="CHAT_VIEW_CELL_POSITION_larger_than_8", dist="cont", daily=True, cumulative=True) 

    chat_send_recipient_count=Metric(col="chat_send_recipient_count", dist="cont", daily=True, cumulative=True)
    chat_send_note_recipient_count=Metric(col="chat_send_note_recipient_count", dist="cont", daily=True, cumulative=True)
    chat_send_media_recipient_count=Metric(col="chat_send_media_recipient_count", dist="cont", daily=True, cumulative=True)
    chat_send_with_mention_count=Metric(col="chat_send_with_mention_count", dist="cont", daily=True, cumulative=True)
    chat_group_send_with_mention_count=Metric(col="chat_group_send_with_mention_count", dist="cont", daily=True, cumulative=True)
    chat_ind_send_with_mention_count=Metric(col="chat_ind_send_with_mention_count", dist="cont", daily=True, cumulative=True)
    display_name_search_with_at_symbol_count=Metric(col="display_name_search_with_at_symbol_count", dist="cont", daily=True, cumulative=True)
    display_name_search_without_at_symbol_count=Metric(col="display_name_search_without_at_symbol_count", dist="cont", daily=True, cumulative=True)
    username_search_with_at_symbol_count=Metric(col="username_search_with_at_symbol_count", dist="cont", daily=True, cumulative=True)
    search_without_at_symbol_visible_count=Metric(col="search_without_at_symbol_visible_count", dist="cont", daily=True, cumulative=True)
    search_with_at_symbol_visible_count=Metric(col="search_with_at_symbol_visible_count", dist="cont", daily=True, cumulative=True)
    chat_view_with_mention_count=Metric(col="chat_view_with_mention_count", dist="cont", daily=True, cumulative=True)
    chat_group_view_with_mention_count=Metric(col="chat_group_view_with_mention_count", dist="cont", daily=True, cumulative=True)
    chat_ind_view_with_mention_count=Metric(col="chat_ind_view_with_mention_count", dist="cont", daily=True, cumulative=True)

    chat_create_active_day=Metric(col="chat_create_active_day", dist="cont", daily=True, cumulative=True)
    chat_create_group_active_day=Metric(col="chat_create_group_active_day", dist="cont", daily=True, cumulative=True)
    chat_create_ind_active_day=Metric(col="chat_create_ind_active_day", dist="cont", daily=True, cumulative=True)
    chat_create_feed_active_day=Metric(col="chat_create_feed_active_day", dist="cont", daily=True, cumulative=True)
    chat_create_notification_active_day=Metric(col="chat_create_notification_active_day", dist="cont", daily=True, cumulative=True)
    chat_create_story_reply_active_day=Metric(col="chat_create_story_reply_active_day", dist="cont", daily=True, cumulative=True)
    chat_create_snap_reply_active_day=Metric(col="chat_create_snap_reply_active_day", dist="cont", daily=True, cumulative=True)
    chat_create_search_active_day=Metric(col="chat_create_search_active_day", dist="cont", daily=True, cumulative=True)
    chat_create_in_app_notification_active_day=Metric(col="chat_create_in_app_notification_active_day", dist="cont", daily=True, cumulative=True)
    chat_create_new_chat_active_day=Metric(col="chat_create_new_chat_active_day", dist="cont", daily=True, cumulative=True)
    chat_create_other_active_day=Metric(col="chat_create_other_active_day", dist="cont", daily=True, cumulative=True)
    chat_page_enter_from_siri_shortcut_count_active_day=Metric(col="chat_page_enter_from_siri_shortcut_count_active_day", dist="cont", daily=True, cumulative=True)

    chat_send_active_day=Metric(col="chat_send_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_group_active_day=Metric(col="chat_send_group_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_ind_active_day=Metric(col="chat_send_ind_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_unique_chat_id_active_day=Metric(col="chat_send_unique_chat_id_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_feed_active_day=Metric(col="chat_send_feed_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_notification_active_day=Metric(col="chat_send_notification_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_source_story_reply_active_day=Metric(col="chat_send_source_story_reply_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_snap_reply_active_day=Metric(col="chat_send_snap_reply_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_search_active_day=Metric(col="chat_send_search_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_in_app_notification_active_day=Metric(col="chat_send_in_app_notification_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_new_chat_active_day=Metric(col="chat_send_new_chat_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_souce_other_active_day=Metric(col="chat_send_souce_other_active_day", dist="cont", daily=True, cumulative=True)
    CHAT_CHAT_SEND_FF_SHORTCUT_ACTIVE_DAY=Metric(col="CHAT_CHAT_SEND_FF_SHORTCUT_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    CHAT_CHAT_SEND_FF_SHORTCUT_UNREAD_CONVERSATIONS_ACTIVE_DAY=Metric(col="CHAT_CHAT_SEND_FF_SHORTCUT_UNREAD_CONVERSATIONS_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    CHAT_CHAT_SEND_FF_SHORTCUT_GROUPS_ACTIVE_DAY=Metric(col="CHAT_CHAT_SEND_FF_SHORTCUT_GROUPS_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    CHAT_CHAT_SEND_FF_SHORTCUT_STORIES_ACTIVE_DAY=Metric(col="CHAT_CHAT_SEND_FF_SHORTCUT_STORIES_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    CHAT_CHAT_SEND_FF_SHORTCUT_BESTFRIENDS_ACTIVE_DAY=Metric(col="CHAT_CHAT_SEND_FF_SHORTCUT_BESTFRIENDS_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    CHAT_CHAT_SEND_FF_SHORTCUT_CUSTOM_ACTIVE_DAY=Metric(col="CHAT_CHAT_SEND_FF_SHORTCUT_CUSTOM_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    CHAT_CHAT_SEND_FF_SHORTCUT_MYAI_ACTIVE_DAY=Metric(col="CHAT_CHAT_SEND_FF_SHORTCUT_MYAI_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    CHAT_CHAT_SEND_FF_SHORTCUT_STREAKS_ACTIVE_DAY=Metric(col="CHAT_CHAT_SEND_FF_SHORTCUT_STREAKS_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    chat_send_text_active_day=Metric(col="chat_send_text_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_note_active_day=Metric(col="chat_send_note_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_sticker_active_day=Metric(col="chat_send_sticker_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_media_active_day=Metric(col="chat_send_media_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_media_image_active_day=Metric(col="chat_send_media_image_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_media_video_active_day=Metric(col="chat_send_media_video_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_media_video_no_sound_active_day=Metric(col="chat_send_media_video_no_sound_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_story_reply_active_day=Metric(col="chat_send_story_reply_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_without_story_reply_active_day=Metric(col="chat_send_without_story_reply_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_other_active_day=Metric(col="chat_send_other_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_content_share_total_active_day=Metric(col="chat_send_content_share_total_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_story_share_active_day=Metric(col="chat_send_story_share_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_non_friend_story_share_active_day=Metric(col="chat_send_non_friend_story_share_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_spotlight_story_share_active_day=Metric(col="chat_send_spotlight_story_share_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_map_story_share_active_day=Metric(col="chat_send_map_story_share_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_with_phone_number_active_day=Metric(col="chat_send_with_phone_number_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_with_location_active_day=Metric(col="chat_send_with_location_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_with_url_active_day=Metric(col="chat_send_with_url_active_day", dist="cont", daily=True, cumulative=True)
    chat_reply_send_active_day=Metric(col="chat_reply_send_active_day", dist="cont", daily=True, cumulative=True)
    chat_reply_send_initiation_type_null_active_day=Metric(col="chat_reply_send_initiation_type_null_active_day", dist="cont", daily=True, cumulative=True)
    chat_reply_send_message_action_menu_active_day=Metric(col="chat_reply_send_message_action_menu_active_day", dist="cont", daily=True, cumulative=True)
    chat_reply_send_message_swipe_active_day=Metric(col="chat_reply_send_message_swipe_active_day", dist="cont", daily=True, cumulative=True)
    chat_reply_send_media_swipe_up_active_day=Metric(col="chat_reply_send_media_swipe_up_active_day", dist="cont", daily=True, cumulative=True)
    chat_reply_send_media_action_menu_active_day=Metric(col="chat_reply_send_media_action_menu_active_day", dist="cont", daily=True, cumulative=True)
    chat_bitmoji_reaction_send_active_day=Metric(col="chat_bitmoji_reaction_send_active_day", dist="cont", daily=True, cumulative=True)
    chat_bitmoji_reaction_send_action_menu_active_day=Metric(col="chat_bitmoji_reaction_send_action_menu_active_day", dist="cont", daily=True, cumulative=True)
    chat_bitmoji_reaction_send_detail_view_active_day=Metric(col="chat_bitmoji_reaction_send_detail_view_active_day", dist="cont", daily=True, cumulative=True)
    chat_combined_reaction_send_active_day=Metric(col="chat_combined_reaction_send_active_day", dist="cont", daily=True, cumulative=True)
    chat_emoji_reaction_send_active_day=Metric(col="chat_emoji_reaction_send_active_day", dist="cont", daily=True, cumulative=True)
    CHAT_SEND_CELL_POSITION_0_ACTIVE_DAY=Metric(col="CHAT_SEND_CELL_POSITION_0_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    CHAT_SEND_CELL_POSITION_1_ACTIVE_DAY=Metric(col="CHAT_SEND_CELL_POSITION_1_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    CHAT_SEND_CELL_POSITION_2_ACTIVE_DAY=Metric(col="CHAT_SEND_CELL_POSITION_2_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    CHAT_SEND_CELL_POSITION_3_ACTIVE_DAY=Metric(col="CHAT_SEND_CELL_POSITION_3_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    CHAT_SEND_CELL_POSITION_4_ACTIVE_DAY=Metric(col="CHAT_SEND_CELL_POSITION_4_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    CHAT_SEND_CELL_POSITION_5_ACTIVE_DAY=Metric(col="CHAT_SEND_CELL_POSITION_5_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    CHAT_SEND_CELL_POSITION_6_ACTIVE_DAY=Metric(col="CHAT_SEND_CELL_POSITION_6_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    CHAT_SEND_CELL_POSITION_7_ACTIVE_DAY=Metric(col="CHAT_SEND_CELL_POSITION_7_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    CHAT_SEND_CELL_POSITION_8_ACTIVE_DAY=Metric(col="CHAT_SEND_CELL_POSITION_8_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    CHAT_SEND_CELL_POSITION_larger_than_8_ACTIVE_DAY=Metric(col="CHAT_SEND_CELL_POSITION_larger_than_8_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)

    chat_view_active_day=Metric(col="chat_view_active_day", dist="cont", daily=True, cumulative=True)
    chat_view_group_active_day=Metric(col="chat_view_group_active_day", dist="cont", daily=True, cumulative=True)
    chat_view_ind_active_day=Metric(col="chat_view_ind_active_day", dist="cont", daily=True, cumulative=True)
    chat_page_view_active_day=Metric(col="chat_page_view_active_day", dist="cont", daily=True, cumulative=True)
    chat_page_view_group_active_day=Metric(col="chat_page_view_group_active_day", dist="cont", daily=True, cumulative=True)
    chat_page_view_ind_active_day=Metric(col="chat_page_view_ind_active_day", dist="cont", daily=True, cumulative=True)
    chat_view_unique_chat_id_active_day=Metric(col="chat_view_unique_chat_id_active_day", dist="cont", daily=True, cumulative=True)
    chat_view_feed_active_day=Metric(col="chat_view_feed_active_day", dist="cont", daily=True, cumulative=True)
    chat_view_notification_active_day=Metric(col="chat_view_notification_active_day", dist="cont", daily=True, cumulative=True)
    chat_view_null_active_day=Metric(col="chat_view_null_active_day", dist="cont", daily=True, cumulative=True)
    chat_view_source_other_active_day=Metric(col="chat_view_source_other_active_day", dist="cont", daily=True, cumulative=True)
    chat_view_text_active_day=Metric(col="chat_view_text_active_day", dist="cont", daily=True, cumulative=True)
    chat_view_media_active_day=Metric(col="chat_view_media_active_day", dist="cont", daily=True, cumulative=True)
    chat_view_media_image_active_day=Metric(col="chat_view_media_image_active_day", dist="cont", daily=True, cumulative=True)
    chat_view_media_video_active_day=Metric(col="chat_view_media_video_active_day", dist="cont", daily=True, cumulative=True)
    chat_view_media_video_no_sound_active_day=Metric(col="chat_view_media_video_no_sound_active_day", dist="cont", daily=True, cumulative=True)
    chat_view_sticker_active_day=Metric(col="chat_view_sticker_active_day", dist="cont", daily=True, cumulative=True)
    chat_view_note_active_day=Metric(col="chat_view_note_active_day", dist="cont", daily=True, cumulative=True)
    chat_view_story_reply_active_day=Metric(col="chat_view_story_reply_active_day", dist="cont", daily=True, cumulative=True)
    chat_view_other_active_day=Metric(col="chat_view_other_active_day", dist="cont", daily=True, cumulative=True)
    chat_view_with_phone_number_active_day=Metric(col="chat_view_with_phone_number_active_day", dist="cont", daily=True, cumulative=True)
    chat_view_with_location_active_day=Metric(col="chat_view_with_location_active_day", dist="cont", daily=True, cumulative=True)
    chat_view_with_url_active_day=Metric(col="chat_view_with_url_active_day", dist="cont", daily=True, cumulative=True)
    chat_reply_view_active_day=Metric(col="chat_reply_view_active_day", dist="cont", daily=True, cumulative=True)
    chat_reply_view_status_available_active_day=Metric(col="chat_reply_view_status_available_active_day", dist="cont", daily=True, cumulative=True)
    chat_reply_view_status_deleted_active_day=Metric(col="chat_reply_view_status_deleted_active_day", dist="cont", daily=True, cumulative=True)
    chat_reply_view_status_unavailable_active_day=Metric(col="chat_reply_view_status_unavailable_active_day", dist="cont", daily=True, cumulative=True)
    chat_reply_view_status_joined_after_message_sent_active_day=Metric(col="chat_reply_view_status_joined_after_message_sent_active_day", dist="cont", daily=True, cumulative=True)
    chat_reply_status_unknown_active_day=Metric(col="chat_reply_status_unknown_active_day", dist="cont", daily=True, cumulative=True)
    chat_bitmoji_reaction_view_active_day=Metric(col="chat_bitmoji_reaction_view_active_day", dist="cont", daily=True, cumulative=True)
    chat_combined_reaction_view_active_day=Metric(col="chat_combined_reaction_view_active_day", dist="cont", daily=True, cumulative=True)
    chat_emoji_reaction_view_active_day=Metric(col="chat_emoji_reaction_view_active_day", dist="cont", daily=True, cumulative=True)
    CHAT_VIEW_CELL_POSITION_0_ACTIVE_DAY=Metric(col="CHAT_VIEW_CELL_POSITION_0_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    CHAT_VIEW_CELL_POSITION_1_ACTIVE_DAY=Metric(col="CHAT_VIEW_CELL_POSITION_1_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    CHAT_VIEW_CELL_POSITION_2_ACTIVE_DAY=Metric(col="CHAT_VIEW_CELL_POSITION_2_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    CHAT_VIEW_CELL_POSITION_3_ACTIVE_DAY=Metric(col="CHAT_VIEW_CELL_POSITION_3_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    CHAT_VIEW_CELL_POSITION_4_ACTIVE_DAY=Metric(col="CHAT_VIEW_CELL_POSITION_4_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    CHAT_VIEW_CELL_POSITION_5_ACTIVE_DAY=Metric(col="CHAT_VIEW_CELL_POSITION_5_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    CHAT_VIEW_CELL_POSITION_6_ACTIVE_DAY=Metric(col="CHAT_VIEW_CELL_POSITION_6_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    CHAT_VIEW_CELL_POSITION_7_ACTIVE_DAY=Metric(col="CHAT_VIEW_CELL_POSITION_7_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    CHAT_VIEW_CELL_POSITION_8_ACTIVE_DAY=Metric(col="CHAT_VIEW_CELL_POSITION_8_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    CHAT_VIEW_CELL_POSITION_larger_than_8_ACTIVE_DAY=Metric(col="CHAT_VIEW_CELL_POSITION_larger_than_8_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)

    sql="""
        SELECT 
        ghost_user_id,
        TIMESTAMP(DATE(event_date)) AS ts,
        SUM(CHAT_CHAT_CREATE) chat_create,
        SUM(CHAT_CHAT_CREATE_GROUP) chat_create_group,
        SUM(CHAT_CHAT_CREATE_IND) chat_create_ind,
        SUM(CHAT_CHAT_CREATE_FEED) chat_create_feed,
        SUM(CHAT_CHAT_CREATE_NOTIFICATION) chat_create_notification,
        SUM(CHAT_CHAT_CREATE_STORY_REPLY) chat_create_story_reply,
        SUM(CHAT_CHAT_CREATE_SNAP_REPLY) chat_create_snap_reply,
        SUM(CHAT_CHAT_CREATE_SEARCH) chat_create_search,
        SUM(CHAT_CHAT_CREATE_IN_APP_NOTIFICATION) chat_create_in_app_notification,
        SUM(CHAT_CHAT_CREATE_NEW_CHAT) chat_create_new_chat,
        SUM(CHAT_CHAT_CREATE_OTHER) chat_create_other,
        SUM(chat_page_enter_from_siri_shortcut) AS chat_page_enter_from_siri_shortcut_count,

        SUM(CHAT_CHAT_SEND) chat_send,
        SUM(CHAT_CHAT_SEND_GROUP) chat_send_group,
        SUM(CHAT_CHAT_SEND_IND) chat_send_ind,
        SUM(chat_send_unique_chat_id) as chat_send_unique_chat_id,
        SUM(CHAT_CHAT_SEND_SOURCE_CHAT) chat_send_feed,
        SUM(CHAT_CHAT_SEND_SOURCE_NOTIFICATION) chat_send_notification,
        SUM(CHAT_CHAT_SEND_SOURCE_CONTEXT_STORY_REPLY) chat_send_source_story_reply,
        SUM(CHAT_CHAT_SEND_SOURCE_CONTEXT_SNAP_REPLY) chat_send_snap_reply,
        SUM(CHAT_CHAT_SEND_SOURCE_SEARCH) chat_send_search,
        SUM(CHAT_CHAT_SEND_SOURCE_IN_APP_NOTIFICATION) chat_send_in_app_notification,
        SUM(CHAT_CHAT_SEND_SOURCE_NEW_CHAT) chat_send_new_chat,
        SUM(CHAT_CHAT_SEND_SOURCE_OTHER) chat_send_souce_other,
        SUM(CHAT_CHAT_SEND_FF_SHORTCUT) AS CHAT_CHAT_SEND_FF_SHORTCUT,
        SUM(CHAT_CHAT_SEND_FF_SHORTCUT_UNREAD_CONVERSATIONS) AS CHAT_CHAT_SEND_FF_SHORTCUT_UNREAD_CONVERSATIONS,
        SUM(CHAT_CHAT_SEND_FF_SHORTCUT_GROUPS) AS CHAT_CHAT_SEND_FF_SHORTCUT_GROUPS,
        SUM(CHAT_CHAT_SEND_FF_SHORTCUT_STORIES) AS CHAT_CHAT_SEND_FF_SHORTCUT_STORIES,
        SUM(CHAT_CHAT_SEND_FF_SHORTCUT_BESTFRIENDS) AS CHAT_CHAT_SEND_FF_SHORTCUT_BESTFRIENDS,
        SUM(CHAT_CHAT_SEND_FF_SHORTCUT_CUSTOM) AS CHAT_CHAT_SEND_FF_SHORTCUT_CUSTOM,
        SUM(CHAT_CHAT_SEND_FF_SHORTCUT_MYAI) AS CHAT_CHAT_SEND_FF_SHORTCUT_MYAI,
        SUM(CHAT_CHAT_SEND_FF_SHORTCUT_STREAKS) AS CHAT_CHAT_SEND_FF_SHORTCUT_STREAKS,
        SUM(chat_send_text) chat_send_text,
        SUM(chat_send_note) chat_send_note,
        SUM(chat_send_sticker) chat_send_sticker,
        SUM(chat_send_media) chat_send_media,
        SUM(chat_send_media_image) chat_send_media_image,
        SUM(chat_send_media_video) chat_send_media_video,
        SUM(chat_send_media_video_no_sound) chat_send_media_video_no_sound,
        SUM(chat_send_story_reply) chat_send_story_reply,
        SUM(CHAT_CHAT_SEND)-SUM(chat_send_story_reply) AS chat_send_without_story_reply,
        sum(chat_send_other) as chat_send_other, 
        sum(chat_send_content_share_total) as chat_send_content_share_total,
        SUM(chat_send_story_share) chat_send_story_share,
        SUM(chat_send_non_friend_story_share) chat_send_non_friend_story_share,
        SUM(chat_send_total_spotlight_story_share) chat_send_spotlight_story_share,
        SUM(chat_send_map_story_overll_share) chat_send_map_story_share,
        SUM(chat_send_with_phone_number) AS chat_send_with_phone_number,
        SUM(chat_send_with_location) AS chat_send_with_location,
        SUM(chat_send_with_url) AS chat_send_with_url,
        SUM(chat_reply_send) as chat_reply_send,
        SUM(chat_reply_send_initiation_type_null) as chat_reply_send_initiation_type_null,
        SUM(chat_reply_send_message_action_menu) as chat_reply_send_message_action_menu,
        SUM(chat_reply_send_message_swipe) as chat_reply_send_message_swipe,
        SUM(chat_reply_send_media_swipe_up) as chat_reply_send_media_swipe_up,
        SUM(chat_reply_send_media_action_menu) as chat_reply_send_media_action_menu,
        SUM(chat_bitmoji_reaction_send) AS chat_bitmoji_reaction_send,
        SUM(chat_bitmoji_reaction_send_action_menu) AS chat_bitmoji_reaction_send_action_menu,
        SUM(chat_bitmoji_reaction_send_detail_view) AS chat_bitmoji_reaction_send_detail_view,
        SUM(chat_combined_reaction_send) AS chat_combined_reaction_send,
        SUM(chat_emoji_reaction_send) AS chat_emoji_reaction_send,
        sum(CHAT_SEND_CELL_POSITION_0) AS CHAT_SEND_CELL_POSITION_0,
        sum(CHAT_SEND_CELL_POSITION_1) AS CHAT_SEND_CELL_POSITION_1,
        sum(CHAT_SEND_CELL_POSITION_2) AS CHAT_SEND_CELL_POSITION_2,
        sum(CHAT_SEND_CELL_POSITION_3) AS CHAT_SEND_CELL_POSITION_3,
        sum(CHAT_SEND_CELL_POSITION_4) AS CHAT_SEND_CELL_POSITION_4,
        sum(CHAT_SEND_CELL_POSITION_5) AS CHAT_SEND_CELL_POSITION_5,
        sum(CHAT_SEND_CELL_POSITION_6) AS CHAT_SEND_CELL_POSITION_6,
        sum(CHAT_SEND_CELL_POSITION_7) AS CHAT_SEND_CELL_POSITION_7,
        sum(CHAT_SEND_CELL_POSITION_8) AS CHAT_SEND_CELL_POSITION_8,
        sum(CHAT_SEND_CELL_POSITION_larger_than_8) AS CHAT_SEND_CELL_POSITION_larger_than_8,
        
        SUM(CHAT_CHAT_VIEW) chat_view,
        SUM(CHAT_CHAT_VIEW_GROUP) chat_view_group,
        SUM(CHAT_CHAT_VIEW_IND) chat_view_ind,
        SUM(chat_page_view) chat_page_view,
        SUM(chat_page_view_group) chat_page_view_group,
        SUM(chat_page_view_ind) chat_page_view_ind,
        SUM(chat_view_unique_chat_id) as chat_view_unique_chat_id,
        SUM(CHAT_CHAT_VIEW_SOURCE_CHAT) chat_view_feed,
        SUM(CHAT_CHAT_VIEW_SOURCE_NOTIFICATION) chat_view_notification,
        SUM(CHAT_CHAT_VIEW_SOURCE_NULL) chat_view_null,
        SUM(CHAT_CHAT_VIEW_SOURCE_OTHER) chat_view_source_other,
        SUM(chat_view_text) chat_view_text,
        SUM(chat_view_media) chat_view_media,
        SUM(chat_view_media_image) chat_view_media_image,
        SUM(chat_view_media_video) chat_view_media_video,
        SUM(chat_view_media_video_no_sound) chat_view_media_video_no_sound,
        SUM(chat_view_sticker) chat_view_sticker,
        SUM(chat_view_note) chat_view_note,
        SUM(chat_view_story_reply) chat_view_story_reply,
        SUM(chat_view_other) chat_view_other,
        SUM(chat_view_with_phone_number) AS chat_view_with_phone_number,
        SUM(chat_view_with_location) AS chat_view_with_location,
        SUM(chat_view_with_url) AS chat_view_with_url,
        SUM(chat_reply_view) as chat_reply_view,
        SUM(chat_reply_view_status_available) as chat_reply_view_status_available,
        SUM(chat_reply_view_status_deleted) as chat_reply_view_status_deleted,
        SUM(chat_reply_view_status_unavailable) as chat_reply_view_status_unavailable,
        SUM(chat_reply_view_status_joined_after_message_sent) as chat_reply_view_status_joined_after_message_sent,
        SUM(chat_reply_status_unknown) as chat_reply_status_unknown,
        SUM(chat_bitmoji_reaction_view) AS chat_bitmoji_reaction_view,
        SUM(chat_combined_reaction_view) AS chat_combined_reaction_view,
        SUM(chat_emoji_reaction_view) AS chat_emoji_reaction_view,
        sum(CHAT_VIEW_CELL_POSITION_0) AS CHAT_VIEW_CELL_POSITION_0,
        sum(CHAT_VIEW_CELL_POSITION_1) AS CHAT_VIEW_CELL_POSITION_1,
        sum(CHAT_VIEW_CELL_POSITION_2) AS CHAT_VIEW_CELL_POSITION_2,
        sum(CHAT_VIEW_CELL_POSITION_3) AS CHAT_VIEW_CELL_POSITION_3,
        sum(CHAT_VIEW_CELL_POSITION_4) AS CHAT_VIEW_CELL_POSITION_4,
        sum(CHAT_VIEW_CELL_POSITION_5) AS CHAT_VIEW_CELL_POSITION_5,
        sum(CHAT_VIEW_CELL_POSITION_6) AS CHAT_VIEW_CELL_POSITION_6,
        sum(CHAT_VIEW_CELL_POSITION_7) AS CHAT_VIEW_CELL_POSITION_7,
        sum(CHAT_VIEW_CELL_POSITION_8) AS CHAT_VIEW_CELL_POSITION_8,
        sum(CHAT_VIEW_CELL_POSITION_larger_than_8) AS CHAT_VIEW_CELL_POSITION_larger_than_8,

        sum(chat_send_recipient_count) as chat_send_recipient_count,
        sum(chat_send_voice_note_recipient_count) as chat_send_note_recipient_count,
        sum(chat_send_media_recipient_count) as chat_send_media_recipient_count,
        SUM(chat_send_with_mention_count) AS chat_send_with_mention_count,
        SUM(chat_group_send_with_mention_count) AS chat_group_send_with_mention_count,
        SUM(chat_ind_send_with_mention_count) AS chat_ind_send_with_mention_count,
        SUM(display_name_search_with_at_symbol_count) AS display_name_search_with_at_symbol_count,
        SUM(display_name_search_without_at_symbol_count) AS display_name_search_without_at_symbol_count,
        SUM(username_search_with_at_symbol_count) AS username_search_with_at_symbol_count,
        SUM(search_without_at_symbol_visible_count) AS search_without_at_symbol_visible_count,
        SUM(search_with_at_symbol_visible_count) AS search_with_at_symbol_visible_count,
        SUM(chat_view_with_mention_count) AS chat_view_with_mention_count,
        SUM(chat_group_view_with_mention_count) AS chat_group_view_with_mention_count,
        SUM(chat_ind_view_with_mention_count) AS chat_ind_view_with_mention_count,

        SUM(IF(chat_create>0,1,0)) as chat_create_active_day,
        SUM(IF(chat_create_group>0,1,0)) as chat_create_group_active_day,
        SUM(IF(chat_create_ind>0,1,0)) as chat_create_ind_active_day,
        SUM(IF(chat_create_feed>0,1,0)) as chat_create_feed_active_day,
        SUM(IF(chat_create_notification>0,1,0)) as chat_create_notification_active_day,
        SUM(IF(chat_create_story_reply>0,1,0)) as chat_create_story_reply_active_day,
        SUM(IF(chat_create_snap_reply>0,1,0)) as chat_create_snap_reply_active_day,
        SUM(IF(chat_create_search>0,1,0)) as chat_create_search_active_day,
        SUM(IF(chat_create_in_app_notification>0,1,0)) as chat_create_in_app_notification_active_day,
        SUM(IF(chat_create_new_chat>0,1,0)) as chat_create_new_chat_active_day,
        SUM(IF(chat_create_other>0,1,0)) as chat_create_other_active_day,
        SUM(IF(chat_page_enter_from_siri_shortcut_count>0,1,0)) as chat_page_enter_from_siri_shortcut_count_active_day,

        SUM(IF(chat_send>0,1,0)) as chat_send_active_day,
        SUM(IF(chat_send_group>0,1,0)) as chat_send_group_active_day,
        SUM(IF(chat_send_ind>0,1,0)) as chat_send_ind_active_day,
        SUM(IF(chat_send_unique_chat_id>0,1,0)) as chat_send_unique_chat_id_active_day,
        SUM(IF(chat_send_feed>0,1,0)) as chat_send_feed_active_day,
        SUM(IF(chat_send_notification>0,1,0)) as chat_send_notification_active_day,
        SUM(IF(chat_send_source_story_reply>0,1,0)) as chat_send_source_story_reply_active_day,
        SUM(IF(chat_send_snap_reply>0,1,0)) as chat_send_snap_reply_active_day,
        SUM(IF(chat_send_search>0,1,0)) as chat_send_search_active_day,
        SUM(IF(chat_send_in_app_notification>0,1,0)) as chat_send_in_app_notification_active_day,
        SUM(IF(chat_send_new_chat>0,1,0)) as chat_send_new_chat_active_day,
        SUM(IF(chat_send_souce_other>0,1,0)) as chat_send_souce_other_active_day,
        SUM(IF(CHAT_CHAT_SEND_FF_SHORTCUT>0,1,0)) as CHAT_CHAT_SEND_FF_SHORTCUT_ACTIVE_DAY,
        SUM(IF(CHAT_CHAT_SEND_FF_SHORTCUT_UNREAD_CONVERSATIONS>0,1,0)) as CHAT_CHAT_SEND_FF_SHORTCUT_UNREAD_CONVERSATIONS_ACTIVE_DAY,
        SUM(IF(CHAT_CHAT_SEND_FF_SHORTCUT_GROUPS>0,1,0)) as CHAT_CHAT_SEND_FF_SHORTCUT_GROUPS_ACTIVE_DAY,
        SUM(IF(CHAT_CHAT_SEND_FF_SHORTCUT_STORIES>0,1,0)) as CHAT_CHAT_SEND_FF_SHORTCUT_STORIES_ACTIVE_DAY,
        SUM(IF(CHAT_CHAT_SEND_FF_SHORTCUT_BESTFRIENDS>0,1,0)) as CHAT_CHAT_SEND_FF_SHORTCUT_BESTFRIENDS_ACTIVE_DAY,
        SUM(IF(CHAT_CHAT_SEND_FF_SHORTCUT_CUSTOM>0,1,0)) as CHAT_CHAT_SEND_FF_SHORTCUT_CUSTOM_ACTIVE_DAY,
        SUM(IF(CHAT_CHAT_SEND_FF_SHORTCUT_MYAI>0,1,0)) as CHAT_CHAT_SEND_FF_SHORTCUT_MYAI_ACTIVE_DAY,
        SUM(IF(CHAT_CHAT_SEND_FF_SHORTCUT_STREAKS>0,1,0)) as CHAT_CHAT_SEND_FF_SHORTCUT_STREAKS_ACTIVE_DAY,
        SUM(IF(chat_send_text>0,1,0)) as chat_send_text_active_day,
        SUM(IF(chat_send_note>0,1,0)) as chat_send_note_active_day,
        SUM(IF(chat_send_sticker>0,1,0)) as chat_send_sticker_active_day,
        SUM(IF(chat_send_media>0,1,0)) as chat_send_media_active_day,
        SUM(IF(chat_send_media_image>0,1,0)) as chat_send_media_image_active_day,
        SUM(IF(chat_send_media_video>0,1,0)) as chat_send_media_video_active_day,
        SUM(IF(chat_send_media_video_no_sound>0,1,0)) as chat_send_media_video_no_sound_active_day,
        SUM(IF(chat_send_story_reply>0,1,0)) as chat_send_story_reply_active_day,
        SUM(IF(chat_send_without_story_repl>0,1,0)) as chat_send_without_story_repl_active_day,
        SUM(IF(chat_send_other>0,1,0)) as chat_send_other_active_day,
        SUM(IF(chat_send_content_share_total>0,1,0)) as chat_send_content_share_total_active_day,
        SUM(IF(chat_send_story_share>0,1,0)) as chat_send_story_share_active_day,
        SUM(IF(chat_send_non_friend_story_share>0,1,0)) as chat_send_non_friend_story_share_active_day,
        SUM(IF(chat_send_spotlight_story_share>0,1,0)) as chat_send_spotlight_story_share_active_day,
        SUM(IF(chat_send_map_story_share>0,1,0)) as chat_send_map_story_share_active_day,
        SUM(IF(chat_send_with_phone_number>0,1,0)) as chat_send_with_phone_number_active_day,
        SUM(IF(chat_send_with_location>0,1,0)) as chat_send_with_location_active_day,
        SUM(IF(chat_send_with_url>0,1,0)) as chat_send_with_url_active_day,
        SUM(IF(chat_reply_send>0,1,0)) as chat_reply_send_active_day,
        SUM(IF(chat_reply_send_initiation_type_null>0,1,0)) as chat_reply_send_initiation_type_null_active_day,
        SUM(IF(chat_reply_send_message_action_menu>0,1,0)) as chat_reply_send_message_action_menu_active_day,
        SUM(IF(chat_reply_send_message_swipe>0,1,0)) as chat_reply_send_message_swipe_active_day,
        SUM(IF(chat_reply_send_media_swipe_up>0,1,0)) as chat_reply_send_media_swipe_up_active_day,
        SUM(IF(chat_reply_send_media_action_menu>0,1,0)) as chat_reply_send_media_action_menu_active_day,
        SUM(IF(chat_bitmoji_reaction_send>0,1,0)) as chat_bitmoji_reaction_send_active_day,
        SUM(IF(chat_bitmoji_reaction_send_action_menu>0,1,0)) as chat_bitmoji_reaction_send_action_menu_active_day,
        SUM(IF(chat_bitmoji_reaction_send_detail_view>0,1,0)) as chat_bitmoji_reaction_send_detail_view_active_day,
        SUM(IF(chat_combined_reaction_send>0,1,0)) as chat_combined_reaction_send_active_day,
        SUM(IF(chat_emoji_reaction_send>0,1,0)) as chat_emoji_reaction_send_active_day,
        SUM(IF(CHAT_SEND_CELL_POSITION_0>0,1,0)) as CHAT_SEND_CELL_POSITION_0_ACTIVE_DAY,
        SUM(IF(CHAT_SEND_CELL_POSITION_1>0,1,0)) as CHAT_SEND_CELL_POSITION_1_ACTIVE_DAY,
        SUM(IF(CHAT_SEND_CELL_POSITION_2>0,1,0)) as CHAT_SEND_CELL_POSITION_2_ACTIVE_DAY,
        SUM(IF(CHAT_SEND_CELL_POSITION_3>0,1,0)) as CHAT_SEND_CELL_POSITION_3_ACTIVE_DAY,
        SUM(IF(CHAT_SEND_CELL_POSITION_4>0,1,0)) as CHAT_SEND_CELL_POSITION_4_ACTIVE_DAY,
        SUM(IF(CHAT_SEND_CELL_POSITION_5>0,1,0)) as CHAT_SEND_CELL_POSITION_5_ACTIVE_DAY,
        SUM(IF(CHAT_SEND_CELL_POSITION_6>0,1,0)) as CHAT_SEND_CELL_POSITION_6_ACTIVE_DAY,
        SUM(IF(CHAT_SEND_CELL_POSITION_7>0,1,0)) as CHAT_SEND_CELL_POSITION_7_ACTIVE_DAY,
        SUM(IF(CHAT_SEND_CELL_POSITION_8>0,1,0)) as CHAT_SEND_CELL_POSITION_8_ACTIVE_DAY,
        SUM(IF(CHAT_SEND_CELL_POSITION_larger_than_8>0,1,0)) as CHAT_SEND_CELL_POSITION_larger_than_8_ACTIVE_DAY,

        SUM(IF(chat_view>0,1,0)) as chat_view_active_day,
        SUM(IF(chat_view_group>0,1,0)) as chat_view_group_active_day,
        SUM(IF(chat_view_ind>0,1,0)) as chat_view_ind_active_day,
        SUM(IF(chat_page_view>0,1,0)) as chat_page_view_active_day,
        SUM(IF(chat_page_view_group>0,1,0)) as chat_page_view_group_active_day,
        SUM(IF(chat_page_view_ind>0,1,0)) as chat_page_view_ind_active_day,
        SUM(IF(chat_view_unique_chat_id>0,1,0)) as chat_view_unique_chat_id_active_day,
        SUM(IF(chat_view_feed>0,1,0)) as chat_view_feed_active_day,
        SUM(IF(chat_view_notification>0,1,0)) as chat_view_notification_active_day,
        SUM(IF(chat_view_null>0,1,0)) as chat_view_null_active_day,
        SUM(IF(chat_view_source_other>0,1,0)) as chat_view_source_other_active_day,
        SUM(IF(chat_view_text>0,1,0)) as chat_view_text_active_day,
        SUM(IF(chat_view_media>0,1,0)) as chat_view_media_active_day,
        SUM(IF(chat_view_media_image>0,1,0)) as chat_view_media_image_active_day,
        SUM(IF(chat_view_media_video>0,1,0)) as chat_view_media_video_active_day,
        SUM(IF(chat_view_media_video_no_sound>0,1,0)) as chat_view_media_video_no_sound_active_day,
        SUM(IF(chat_view_sticker>0,1,0)) as chat_view_sticker_active_day,
        SUM(IF(chat_view_note>0,1,0)) as chat_view_note_active_day,
        SUM(IF(chat_view_story_reply>0,1,0)) as chat_view_story_reply_active_day,
        SUM(IF(chat_view_other>0,1,0)) as chat_view_other_active_day,
        SUM(IF(chat_view_with_phone_number>0,1,0)) as chat_view_with_phone_number_active_day,
        SUM(IF(chat_view_with_location>0,1,0)) as chat_view_with_location_active_day,
        SUM(IF(chat_view_with_url>0,1,0)) as chat_view_with_url_active_day,
        SUM(IF(chat_reply_view>0,1,0)) as chat_reply_view_active_day,
        SUM(IF(chat_reply_view_status_available>0,1,0)) as chat_reply_view_status_available_active_day,
        SUM(IF(chat_reply_view_status_deleted>0,1,0)) as chat_reply_view_status_deleted_active_day,
        SUM(IF(chat_reply_view_status_unavailable>0,1,0)) as chat_reply_view_status_unavailable_active_day,
        SUM(IF(chat_reply_view_status_joined_after_message_sent>0,1,0)) as chat_reply_view_status_joined_after_message_sent_active_day,
        SUM(IF(chat_reply_status_unknown>0,1,0)) as chat_reply_status_unknown_active_day,
        SUM(IF(chat_bitmoji_reaction_view>0,1,0)) as chat_bitmoji_reaction_view_active_day,
        SUM(IF(chat_combined_reaction_view>0,1,0)) as chat_combined_reaction_view_active_day,
        SUM(IF(chat_emoji_reaction_view>0,1,0)) as chat_emoji_reaction_view_active_day,
        SUM(IF(CHAT_VIEW_CELL_POSITION_0>0,1,0)) as CHAT_VIEW_CELL_POSITION_0_ACTIVE_DAY,
        SUM(IF(CHAT_VIEW_CELL_POSITION_1>0,1,0)) as CHAT_VIEW_CELL_POSITION_1_ACTIVE_DAY,
        SUM(IF(CHAT_VIEW_CELL_POSITION_2>0,1,0)) as CHAT_VIEW_CELL_POSITION_2_ACTIVE_DAY,
        SUM(IF(CHAT_VIEW_CELL_POSITION_3>0,1,0)) as CHAT_VIEW_CELL_POSITION_3_ACTIVE_DAY,
        SUM(IF(CHAT_VIEW_CELL_POSITION_4>0,1,0)) as CHAT_VIEW_CELL_POSITION_4_ACTIVE_DAY,
        SUM(IF(CHAT_VIEW_CELL_POSITION_5>0,1,0)) as CHAT_VIEW_CELL_POSITION_5_ACTIVE_DAY,
        SUM(IF(CHAT_VIEW_CELL_POSITION_6>0,1,0)) as CHAT_VIEW_CELL_POSITION_6_ACTIVE_DAY,
        SUM(IF(CHAT_VIEW_CELL_POSITION_7>0,1,0)) as CHAT_VIEW_CELL_POSITION_7_ACTIVE_DAY,
        SUM(IF(CHAT_VIEW_CELL_POSITION_8>0,1,0)) as CHAT_VIEW_CELL_POSITION_8_ACTIVE_DAY,
        SUM(IF(CHAT_VIEW_CELL_POSITION_larger_than_8>0,1,0)) as CHAT_VIEW_CELL_POSITION_larger_than_8_ACTIVE_DAY
        
        FROM
        `sc-analytics.report_growth.friends_feed_chat_user_level_20*`
        WHERE _TABLE_SUFFIX BETWEEN '{start_date}' AND '{end_date}' 
        GROUP BY 1,2
        """
    
    def sql_callable(start_date, end_date):
        start_date, end_date = start_date[-6:], end_date[-6:]
        return sql.format(start_date=start_date, end_date=end_date)

    mt = MetricTable(
        sql=sql.format(start_date=start_date, end_date=end_date),
        sql_callable=sql_callable,
        metrics=[chat_create,
                chat_create_group,
                chat_create_ind,
                chat_create_feed,
                chat_create_notification,
                chat_create_story_reply,
                chat_create_snap_reply,
                chat_create_search,
                chat_create_in_app_notification,
                chat_create_new_chat,
                chat_create_other,
                chat_page_enter_from_siri_shortcut_count,

                chat_send,
                chat_send_group,
                chat_send_ind,
                chat_send_unique_chat_id,
                chat_send_feed,
                chat_send_notification,
                chat_send_source_story_reply,
                chat_send_snap_reply,
                chat_send_search,
                chat_send_in_app_notification,
                chat_send_new_chat,
                chat_send_souce_other,
                CHAT_CHAT_SEND_FF_SHORTCUT,
                CHAT_CHAT_SEND_FF_SHORTCUT_UNREAD_CONVERSATIONS,
                CHAT_CHAT_SEND_FF_SHORTCUT_GROUPS,
                CHAT_CHAT_SEND_FF_SHORTCUT_STORIES,
                CHAT_CHAT_SEND_FF_SHORTCUT_BESTFRIENDS,
                CHAT_CHAT_SEND_FF_SHORTCUT_CUSTOM,
                CHAT_CHAT_SEND_FF_SHORTCUT_MYAI,
                CHAT_CHAT_SEND_FF_SHORTCUT_STREAKS,
                chat_send_text,
                chat_send_note,
                chat_send_sticker,
                chat_send_media,
                chat_send_media_image,
                chat_send_media_video,
                chat_send_media_video_no_sound,
                chat_send_story_reply,
                chat_send_without_story_reply,
                chat_send_other,
                chat_send_content_share_total,
                chat_send_story_share,
                chat_send_non_friend_story_share,
                chat_send_spotlight_story_share,
                chat_send_map_story_share,
                chat_send_with_phone_number,
                chat_send_with_location,
                chat_send_with_url,
                chat_reply_send,
                chat_reply_send_initiation_type_null,
                chat_reply_send_message_action_menu,
                chat_reply_send_message_swipe,
                chat_reply_send_media_swipe_up,
                chat_reply_send_media_action_menu,
                chat_bitmoji_reaction_send,
                chat_bitmoji_reaction_send_action_menu,
                chat_bitmoji_reaction_send_detail_view,
                chat_combined_reaction_send,
                chat_emoji_reaction_send,
                CHAT_SEND_CELL_POSITION_0,
                CHAT_SEND_CELL_POSITION_1,
                CHAT_SEND_CELL_POSITION_2,
                CHAT_SEND_CELL_POSITION_3,
                CHAT_SEND_CELL_POSITION_4,
                CHAT_SEND_CELL_POSITION_5,
                CHAT_SEND_CELL_POSITION_6,
                CHAT_SEND_CELL_POSITION_7,
                CHAT_SEND_CELL_POSITION_8,
                CHAT_SEND_CELL_POSITION_larger_than_8,

                chat_view,
                chat_view_group,
                chat_view_ind,
                chat_page_view,
                chat_page_view_group,
                chat_page_view_ind,
                chat_view_unique_chat_id,
                chat_view_feed,
                chat_view_notification,
                chat_view_null,
                chat_view_source_other,
                chat_view_text,
                chat_view_media,
                chat_view_media_image,
                chat_view_media_video,
                chat_view_media_video_no_sound,
                chat_view_sticker,
                chat_view_note,
                chat_view_story_reply,
                chat_view_other,
                chat_view_with_phone_number,
                chat_view_with_location,
                chat_view_with_url,
                chat_reply_view,
                chat_reply_view_status_available,
                chat_reply_view_status_deleted,
                chat_reply_view_status_unavailable,
                chat_reply_view_status_joined_after_message_sent,
                chat_reply_status_unknown,
                chat_bitmoji_reaction_view,
                chat_combined_reaction_view,
                chat_emoji_reaction_view,
                CHAT_VIEW_CELL_POSITION_0,
                CHAT_VIEW_CELL_POSITION_1,
                CHAT_VIEW_CELL_POSITION_2,
                CHAT_VIEW_CELL_POSITION_3,
                CHAT_VIEW_CELL_POSITION_4,
                CHAT_VIEW_CELL_POSITION_5,
                CHAT_VIEW_CELL_POSITION_6,
                CHAT_VIEW_CELL_POSITION_7,
                CHAT_VIEW_CELL_POSITION_8,
                CHAT_VIEW_CELL_POSITION_larger_than_8,

                chat_send_recipient_count,
                chat_send_note_recipient_count,
                chat_send_media_recipient_count,
                chat_send_with_mention_count,
                chat_group_send_with_mention_count,
                chat_ind_send_with_mention_count,
                display_name_search_with_at_symbol_count,
                display_name_search_without_at_symbol_count,
                username_search_with_at_symbol_count,
                search_without_at_symbol_visible_count,
                search_with_at_symbol_visible_count,
                chat_view_with_mention_count,
                chat_group_view_with_mention_count,
                chat_ind_view_with_mention_count,
                chat_create_active_day,
                chat_create_group_active_day,
                chat_create_ind_active_day,
                chat_create_feed_active_day,
                chat_create_notification_active_day,
                chat_create_story_reply_active_day,
                chat_create_snap_reply_active_day,
                chat_create_search_active_day,
                chat_create_in_app_notification_active_day,
                chat_create_new_chat_active_day,
                chat_create_other_active_day,
                chat_page_enter_from_siri_shortcut_count_active_day,

                chat_send_active_day,
                chat_send_group_active_day,
                chat_send_ind_active_day,
                chat_send_unique_chat_id_active_day,
                chat_send_feed_active_day,
                chat_send_notification_active_day,
                chat_send_source_story_reply_active_day,
                chat_send_snap_reply_active_day,
                chat_send_search_active_day,
                chat_send_in_app_notification_active_day,
                chat_send_new_chat_active_day,
                chat_send_souce_other_active_day,
                CHAT_CHAT_SEND_FF_SHORTCUT_ACTIVE_DAY,
                CHAT_CHAT_SEND_FF_SHORTCUT_UNREAD_CONVERSATIONS_ACTIVE_DAY,
                CHAT_CHAT_SEND_FF_SHORTCUT_GROUPS_ACTIVE_DAY,
                CHAT_CHAT_SEND_FF_SHORTCUT_STORIES_ACTIVE_DAY,
                CHAT_CHAT_SEND_FF_SHORTCUT_BESTFRIENDS_ACTIVE_DAY,
                CHAT_CHAT_SEND_FF_SHORTCUT_CUSTOM_ACTIVE_DAY,
                CHAT_CHAT_SEND_FF_SHORTCUT_MYAI_ACTIVE_DAY,
                CHAT_CHAT_SEND_FF_SHORTCUT_STREAKS_ACTIVE_DAY,
                chat_send_text_active_day,
                chat_send_note_active_day,
                chat_send_sticker_active_day,
                chat_send_media_active_day,
                chat_send_media_image_active_day,
                chat_send_media_video_active_day,
                chat_send_media_video_no_sound_active_day,
                chat_send_story_reply_active_day,
                chat_send_without_story_reply_active_day,
                chat_send_other_active_day,
                chat_send_content_share_total_active_day,
                chat_send_story_share_active_day,
                chat_send_non_friend_story_share_active_day,
                chat_send_spotlight_story_share_active_day,
                chat_send_map_story_share_active_day,
                chat_send_with_phone_number_active_day,
                chat_send_with_location_active_day,
                chat_send_with_url_active_day,
                chat_reply_send_active_day,
                chat_reply_send_initiation_type_null_active_day,
                chat_reply_send_message_action_menu_active_day,
                chat_reply_send_message_swipe_active_day,
                chat_reply_send_media_swipe_up_active_day,
                chat_reply_send_media_action_menu_active_day,
                chat_bitmoji_reaction_send_active_day,
                chat_bitmoji_reaction_send_action_menu_active_day,
                chat_bitmoji_reaction_send_detail_view_active_day,
                chat_combined_reaction_send_active_day,
                chat_emoji_reaction_send_active_day,
                CHAT_SEND_CELL_POSITION_0_ACTIVE_DAY,
                CHAT_SEND_CELL_POSITION_1_ACTIVE_DAY,
                CHAT_SEND_CELL_POSITION_2_ACTIVE_DAY,
                CHAT_SEND_CELL_POSITION_3_ACTIVE_DAY,
                CHAT_SEND_CELL_POSITION_4_ACTIVE_DAY,
                CHAT_SEND_CELL_POSITION_5_ACTIVE_DAY,
                CHAT_SEND_CELL_POSITION_6_ACTIVE_DAY,
                CHAT_SEND_CELL_POSITION_7_ACTIVE_DAY,
                CHAT_SEND_CELL_POSITION_8_ACTIVE_DAY,
                CHAT_SEND_CELL_POSITION_larger_than_8_ACTIVE_DAY,

                chat_view_active_day,
                chat_view_group_active_day,
                chat_view_ind_active_day,
                chat_page_view_active_day,
                chat_page_view_group_active_day,
                chat_page_view_ind_active_day,
                chat_view_unique_chat_id_active_day,
                chat_view_feed_active_day,
                chat_view_notification_active_day,
                chat_view_null_active_day,
                chat_view_source_other_active_day,
                chat_view_text_active_day,
                chat_view_media_active_day,
                chat_view_media_image_active_day,
                chat_view_media_video_active_day,
                chat_view_media_video_no_sound_active_day,
                chat_view_sticker_active_day,
                chat_view_note_active_day,
                chat_view_story_reply_active_day,
                chat_view_other_active_day,
                chat_view_with_phone_number_active_day,
                chat_view_with_location_active_day,
                chat_view_with_url_active_day,
                chat_reply_view_active_day,
                chat_reply_view_status_available_active_day,
                chat_reply_view_status_deleted_active_day,
                chat_reply_view_status_unavailable_active_day,
                chat_reply_view_status_joined_after_message_sent_active_day,
                chat_reply_status_unknown_active_day,
                chat_bitmoji_reaction_view_active_day,
                chat_combined_reaction_view_active_day,
                chat_emoji_reaction_view_active_day,
                CHAT_VIEW_CELL_POSITION_0_ACTIVE_DAY,
                CHAT_VIEW_CELL_POSITION_1_ACTIVE_DAY,
                CHAT_VIEW_CELL_POSITION_2_ACTIVE_DAY,
                CHAT_VIEW_CELL_POSITION_3_ACTIVE_DAY,
                CHAT_VIEW_CELL_POSITION_4_ACTIVE_DAY,
                CHAT_VIEW_CELL_POSITION_5_ACTIVE_DAY,
                CHAT_VIEW_CELL_POSITION_6_ACTIVE_DAY,
                CHAT_VIEW_CELL_POSITION_7_ACTIVE_DAY,
                CHAT_VIEW_CELL_POSITION_8_ACTIVE_DAY,
                CHAT_VIEW_CELL_POSITION_larger_than_8_ACTIVE_DAY
                ],
        name="chat_metrics",
        inner_join_with_mapping=False,
        bq_dialect="standard"
    )
    return mt



def chat_detail_metrics(start_date, end_date):
#     start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
#     end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    start_date, end_date = start_date[-6:], end_date[-6:]
    
    server_inbound_message_unique_tracking_id=Metric(col="server_inbound_message_unique_tracking_id", dist="cont", daily=True, cumulative=True)
    server_outbound_message_unique_tracking_id=Metric(col="server_outbound_message_unique_tracking_id", dist="cont", daily=True, cumulative=True)
    chat_conversation_pin=Metric(col="chat_conversation_pin", dist="cont", daily=True, cumulative=True)
    chat_conversation_unpin=Metric(col="chat_conversation_unpin", dist="cont", daily=True, cumulative=True)
    chat_conversation_net_pin_unpin=Metric(col="chat_conversation_net_pin_unpin", dist="cont", daily=True, cumulative=True)
    click_with_url=Metric(col="click_with_url", dist="cont", daily=True, cumulative=True)
    click_with_url_uu=Metric(col="click_with_url_uu", dist="cont", daily=True, cumulative=True)
    chat_mute_ind=Metric(col="chat_mute_ind", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    chat_unmute_ind=Metric(col="chat_unmute_ind", dist="cont", daily=True, cumulative=True,desired_direction=POSITIVE)
    chat_mute_indefinite_ind=Metric(col="chat_mute_indefinite_ind", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    chat_mute_one_hour_ind=Metric(col="chat_mute_one_hour_ind", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    chat_mute_eight_hours_ind=Metric(col="chat_mute_eight_hours_ind", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    chat_mute_twenty_four_hours_ind=Metric(col="chat_mute_twenty_four_hours_ind", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    chat_mute_mentions_only_ind=Metric(col="chat_mute_mentions_only_ind", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    chat_mute_group=Metric(col="chat_mute_group", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    chat_unmute_group=Metric(col="chat_unmute_group", dist="cont", daily=True, cumulative=True,desired_direction=POSITIVE)
    chat_mute_mentions_only_group=Metric(col="chat_mute_mentions_only_group", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    chat_mute_chats_and_snaps_group=Metric(col="chat_mute_chats_and_snaps_group", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    chat_mute_indefinite_group=Metric(col="chat_mute_indefinite_group", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    chat_mute_one_hour_group=Metric(col="chat_mute_one_hour_group", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    chat_mute_eight_hours_group=Metric(col="chat_mute_eight_hours_group", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    chat_mute_twenty_four_hours_group=Metric(col="chat_mute_twenty_four_hours_group", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    chat_screenshot_in_app_notification=Metric(col="chat_screenshot_in_app_notification", dist="cont", daily=True, cumulative=True,desired_direction=POSITIVE)
    chat_drawer_action_in_app_notification=Metric(col="chat_drawer_action_in_app_notification", dist="cont", daily=True, cumulative=True,desired_direction=POSITIVE)
    chat_screenshot_in_app_notification_uu=Metric(col="chat_screenshot_in_app_notification_uu", dist="cont", daily=True, cumulative=True,desired_direction=POSITIVE)
    chat_drawer_action_in_app_notification_uu=Metric(col="chat_drawer_action_in_app_notification_uu", dist="cont", daily=True, cumulative=True,desired_direction=POSITIVE)
    
    sql="""
        SELECT 
        ghost_user_id,
        TIMESTAMP(DATE(event_date)) AS ts,
        
        SUM(server_inbound_message_unique_tracking_id) as server_inbound_message_unique_tracking_id,
        SUM(server_outbound_message_unique_tracking_id) as server_outbound_message_unique_tracking_id,
        SUM(chat_conversation_pin) as chat_conversation_pin,
        SUM(chat_conversation_unpin) as chat_conversation_unpin,
        SUM(click_with_url) as click_with_url,
        SUM(click_with_url_uu) as click_with_url_uu,
        SUM(chat_conversation_pin) - SUM(chat_conversation_unpin) as chat_conversation_net_pin_unpin,        
        SUM(chat_mute_ind) chat_mute_ind,
        SUM(chat_unmute_ind) chat_unmute_ind,
        SUM(chat_mute_indefinite_ind) chat_mute_indefinite_ind,
        SUM(chat_mute_one_hour_ind) chat_mute_one_hour_ind,
        SUM(chat_mute_eight_hours_ind) chat_mute_eight_hours_ind,
        SUM(chat_mute_twenty_four_hours_ind) chat_mute_twenty_four_hours_ind,
        SUM(chat_mute_mentions_only_ind) chat_mute_mentions_only_ind,
        SUM(chat_mute_group) chat_mute_group,
        SUM(chat_unmute_group) chat_unmute_group,
        SUM(chat_mute_mentions_only_group) chat_mute_mentions_only_group,
        SUM(chat_mute_chats_and_snaps_group) chat_mute_chats_and_snaps_group,
        SUM(chat_mute_indefinite_group) chat_mute_indefinite_group,
        SUM(chat_mute_one_hour_group) chat_mute_one_hour_group,
        SUM(chat_mute_eight_hours_group) chat_mute_eight_hours_group,
        SUM(chat_mute_twenty_four_hours_group) chat_mute_twenty_four_hours_group,
        
        SUM(chat_screenshot_in_app_notification) as chat_screenshot_in_app_notification,
        SUM(chat_drawer_action_in_app_notification) as chat_drawer_action_in_app_notification,
        SUM(chat_screenshot_in_app_notification_uu) as chat_screenshot_in_app_notification_uu,
        SUM(chat_drawer_action_in_app_notification_uu) as chat_drawer_action_in_app_notification_uu,
            
        FROM
        `sc-analytics.report_growth.friends_feed_chat_detail_user_level_20*`
        WHERE _table_suffix between '{start_date}' and '{end_date}'
        GROUP BY 1,2
        """
    
    def sql_callable(start_date, end_date):
        start_date, end_date = start_date[-6:], end_date[-6:]
        return sql.format(start_date=start_date, end_date=end_date)

    mt = MetricTable(
        sql=sql.format(start_date=start_date, end_date=end_date),
        sql_callable=sql_callable,
        metrics=[
                server_inbound_message_unique_tracking_id,
                server_outbound_message_unique_tracking_id,
                chat_conversation_pin,
                chat_conversation_unpin,
                chat_conversation_net_pin_unpin,
                click_with_url,
                click_with_url_uu,
                chat_mute_ind,
                chat_unmute_ind,
                chat_mute_indefinite_ind,
                chat_mute_one_hour_ind,
                chat_mute_eight_hours_ind,
                chat_mute_twenty_four_hours_ind,
                chat_mute_mentions_only_ind,
                chat_mute_group,
                chat_unmute_group,
                chat_mute_mentions_only_group,
                chat_mute_chats_and_snaps_group,
                chat_mute_indefinite_group,
                chat_mute_one_hour_group,
                chat_mute_eight_hours_group,
                chat_mute_twenty_four_hours_group,
                chat_screenshot_in_app_notification,
                chat_drawer_action_in_app_notification,
                chat_screenshot_in_app_notification_uu,
                chat_drawer_action_in_app_notification_uu,

                ],
        name="chat_detail_metrics",
        inner_join_with_mapping=False,
        bq_dialect="standard"
    )
    return mt


def snap_detail_metrics(start_date, end_date):
#     start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
#     end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    start_date, end_date = start_date[-6:], end_date[-6:]
    
    DIRECT_SNAP_CREATE=Metric(col="DIRECT_SNAP_CREATE", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_CREATE_CAMERA=Metric(col="DIRECT_SNAP_CREATE_CAMERA", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_CREATE_FEED=Metric(col="DIRECT_SNAP_CREATE_FEED", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_CREATE_FEED_SNAP_REPLY=Metric(col="DIRECT_SNAP_CREATE_FEED_SNAP_REPLY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_CREATE_IN_CHAT=Metric(col="DIRECT_SNAP_CREATE_IN_CHAT", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_CREATE_IN_CHAT_from_ACTION_MENU=Metric(col="DIRECT_SNAP_CREATE_IN_CHAT_from_ACTION_MENU", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_CREATE_IN_CHAT_from_DOUBLE_TAP=Metric(col="DIRECT_SNAP_CREATE_IN_CHAT_from_DOUBLE_TAP", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_CREATE_ADD_TO_STORY=Metric(col="DIRECT_SNAP_CREATE_ADD_TO_STORY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_CREATE_MINI_PROFILE=Metric(col="DIRECT_SNAP_CREATE_MINI_PROFILE", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_CREATE_SEARCH=Metric(col="DIRECT_SNAP_CREATE_SEARCH", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_CREATE_with_SNAP_REPLY_STICKER=Metric(col="DIRECT_SNAP_CREATE_with_SNAP_REPLY_STICKER", dist="cont", daily=True, cumulative=True)
    SNAP_CREATE_IMAGE=Metric(col="SNAP_CREATE_IMAGE", dist="cont", daily=True, cumulative=True)
    SNAP_CREATE_VIDEO=Metric(col="SNAP_CREATE_VIDEO", dist="cont", daily=True, cumulative=True)
    SNAP_CREATE_GROUP=Metric(col="SNAP_CREATE_GROUP", dist="cont", daily=True, cumulative=True)
    SNAP_CREATE_IND=Metric(col="SNAP_CREATE_IND", dist="cont", daily=True, cumulative=True)
    SNAP_CREATE_IMAGE_GROUP=Metric(col="SNAP_CREATE_IMAGE_GROUP", dist="cont", daily=True, cumulative=True)
    SNAP_CREATE_IMAGE_IND=Metric(col="SNAP_CREATE_IMAGE_IND", dist="cont", daily=True, cumulative=True)
    SNAP_CREATE_VIDEO_GROUP=Metric(col="SNAP_CREATE_VIDEO_GROUP", dist="cont", daily=True, cumulative=True)
    SNAP_CREATE_VIDEO_IND=Metric(col="SNAP_CREATE_VIDEO_IND", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND=Metric(col="DIRECT_SNAP_SEND", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_CAMERA=Metric(col="DIRECT_SNAP_SEND_CAMERA", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_FEED=Metric(col="DIRECT_SNAP_SEND_FEED", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_FEED_SNAP_REPLY=Metric(col="DIRECT_SNAP_SEND_FEED_SNAP_REPLY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_IN_CHAT=Metric(col="DIRECT_SNAP_SEND_IN_CHAT", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_IN_CHAT_from_ACTION_MENU=Metric(col="DIRECT_SNAP_SEND_IN_CHAT_from_ACTION_MENU", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_IN_CHAT_from_DOUBLE_TAP=Metric(col="DIRECT_SNAP_SEND_IN_CHAT_from_DOUBLE_TAP", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_MINI_PROFILE=Metric(col="DIRECT_SNAP_SEND_MINI_PROFILE", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_SEARCH=Metric(col="DIRECT_SNAP_SEND_SEARCH", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_with_SNAP_REPLY_STICKER=Metric(col="DIRECT_SNAP_SEND_with_SNAP_REPLY_STICKER", dist="cont", daily=True, cumulative=True)
    snap_send_story_reply=Metric(col="snap_send_story_reply", dist="cont", daily=True, cumulative=True)
    snap_send_without_story_reply=Metric(col="snap_send_without_story_reply", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_FROM_SENDTO=Metric(col="SNAP_SEND_FROM_SENDTO", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_FROM_NON_SENDTO=Metric(col="SNAP_SEND_FROM_NON_SENDTO", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_FF_SHORTCUT=Metric(col="DIRECT_SNAP_SEND_FF_SHORTCUT", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_FF_SHORTCUT_UNREPLIED_CONVERSATIONS=Metric(col="DIRECT_SNAP_SEND_FF_SHORTCUT_UNREPLIED_CONVERSATIONS", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_FF_SHORTCUT_STREAKS=Metric(col="DIRECT_SNAP_SEND_FF_SHORTCUT_STREAKS", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_FF_SHORTCUT_UNREAD_CONVERSATIONS=Metric(col="DIRECT_SNAP_SEND_FF_SHORTCUT_UNREAD_CONVERSATIONS", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_FF_SHORTCUT_CUSTOM=Metric(col="DIRECT_SNAP_SEND_FF_SHORTCUT_CUSTOM", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_FF_SHORTCUT_BEST_FRIENDS=Metric(col="DIRECT_SNAP_SEND_FF_SHORTCUT_BEST_FRIENDS", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_CELL_POSITION_0=Metric(col="SNAP_SEND_CELL_POSITION_0", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_CELL_POSITION_1=Metric(col="SNAP_SEND_CELL_POSITION_1", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_CELL_POSITION_2=Metric(col="SNAP_SEND_CELL_POSITION_2", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_CELL_POSITION_3=Metric(col="SNAP_SEND_CELL_POSITION_3", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_CELL_POSITION_4=Metric(col="SNAP_SEND_CELL_POSITION_4", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_CELL_POSITION_5=Metric(col="SNAP_SEND_CELL_POSITION_5", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_CELL_POSITION_6=Metric(col="SNAP_SEND_CELL_POSITION_6", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_CELL_POSITION_7=Metric(col="SNAP_SEND_CELL_POSITION_7", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_CELL_POSITION_8=Metric(col="SNAP_SEND_CELL_POSITION_8", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_CELL_POSITION_larger_than_8=Metric(col="SNAP_SEND_CELL_POSITION_larger_than_8", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_IMAGE=Metric(col="SNAP_SEND_IMAGE", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_VIDEO=Metric(col="SNAP_SEND_VIDEO", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_GROUP=Metric(col="SNAP_SEND_GROUP", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_IND=Metric(col="SNAP_SEND_IND", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_IMAGE_GROUP=Metric(col="SNAP_SEND_IMAGE_GROUP", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_VIDEO_GROUP=Metric(col="SNAP_SEND_VIDEO_GROUP", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_IMAGE_IND=Metric(col="SNAP_SEND_IMAGE_IND", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_VIDEO_IND=Metric(col="SNAP_SEND_VIDEO_IND", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_VIEW=Metric(col="DIRECT_SNAP_VIEW", dist="cont", daily=True, cumulative=True)
    snap_view_unique_snap_id=Metric(col="snap_view_unique_snap_id", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_VIEW_FROM_FEED=Metric(col="DIRECT_SNAP_VIEW_FROM_FEED", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_VIEW_FROM_CHAT=Metric(col="DIRECT_SNAP_VIEW_FROM_CHAT", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_TIME_SEC=Metric(col="SNAP_VIEW_TIME_SEC", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_IMAGE=Metric(col="SNAP_VIEW_IMAGE", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_VIDEO=Metric(col="SNAP_VIEW_VIDEO", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_GROUP=Metric(col="SNAP_VIEW_GROUP", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_IND=Metric(col="SNAP_VIEW_IND", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_IMAGE_IND=Metric(col="SNAP_VIEW_IMAGE_IND", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_VIDEO_IND=Metric(col="SNAP_VIEW_VIDEO_IND", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_IMAGE_GROUP=Metric(col="SNAP_VIEW_IMAGE_GROUP", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_VIDEO_GROUP=Metric(col="SNAP_VIEW_VIDEO_GROUP", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_CELL_POSITION_0=Metric(col="SNAP_VIEW_CELL_POSITION_0", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_CELL_POSITION_1=Metric(col="SNAP_VIEW_CELL_POSITION_1", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_CELL_POSITION_2=Metric(col="SNAP_VIEW_CELL_POSITION_2", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_CELL_POSITION_3=Metric(col="SNAP_VIEW_CELL_POSITION_3", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_CELL_POSITION_4=Metric(col="SNAP_VIEW_CELL_POSITION_4", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_CELL_POSITION_5=Metric(col="SNAP_VIEW_CELL_POSITION_5", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_CELL_POSITION_6=Metric(col="SNAP_VIEW_CELL_POSITION_6", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_CELL_POSITION_7=Metric(col="SNAP_VIEW_CELL_POSITION_7", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_CELL_POSITION_8=Metric(col="SNAP_VIEW_CELL_POSITION_8", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_CELL_POSITION_larger_than_8=Metric(col="SNAP_VIEW_CELL_POSITION_larger_than_8", dist="cont", daily=True, cumulative=True)
    
    snap_recipient_send=Metric(col="snap_recipient_send", dist="cont", daily=True, cumulative=True)
    snap_single_recipient_send=Metric(col="snap_single_recipient_send", dist="cont", daily=True, cumulative=True)
    snap_multi_recipient_send=Metric(col="snap_multi_recipient_send", dist="cont", daily=True, cumulative=True)
    snap_recipient_send_from_sendto=Metric(col="snap_recipient_send_from_sendto", dist="cont", daily=True, cumulative=True)
    snap_recipient_one_to_one_send_from_sendto=Metric(col="snap_recipient_one_to_one_send_from_sendto", dist="cont", daily=True, cumulative=True)
    snap_recipient_non_unique_group_send_from_sendto=Metric(col="snap_recipient_non_unique_group_send_from_sendto", dist="cont", daily=True, cumulative=True)
    snap_recipient_unique_group_send_from_sendto=Metric(col="snap_recipient_unique_group_send_from_sendto", dist="cont", daily=True, cumulative=True)
    snap_recipient_send_one_to_one=Metric(col="snap_recipient_send_one_to_one", dist="cont", daily=True, cumulative=True)
    snap_group_recipient_send=Metric(col="snap_group_recipient_send", dist="cont", daily=True, cumulative=True)
    snap_group_non_unique_user_recipient_send=Metric(col="snap_group_non_unique_user_recipient_send", dist="cont", daily=True, cumulative=True)
    snap_group_unique_user_recipient_send=Metric(col="snap_group_unique_user_recipient_send", dist="cont", daily=True, cumulative=True)
    snap_recipient_send_from_feed=Metric(col="snap_recipient_send_from_feed", dist="cont", daily=True, cumulative=True)
    snap_recipient_send_one_to_one_from_feed=Metric(col="snap_recipient_send_one_to_one_from_feed", dist="cont", daily=True, cumulative=True)
    snap_group_non_unique_user_recipient_send_from_feed=Metric(col="snap_group_non_unique_user_recipient_send_from_feed", dist="cont", daily=True, cumulative=True)
    snap_group_unique_user_recipient_send_from_feed=Metric(col="snap_group_unique_user_recipient_send_from_feed", dist="cont", daily=True, cumulative=True)
    snap_recipient_send_from_CAMERA=Metric(col="snap_recipient_send_from_CAMERA", dist="cont", daily=True, cumulative=True)
    snap_recipient_send_one_to_one_from_CAMERA=Metric(col="snap_recipient_send_one_to_one_from_CAMERA", dist="cont", daily=True, cumulative=True)
    snap_group_non_unique_user_recipient_send_from_CAMERA=Metric(col="snap_group_non_unique_user_recipient_send_from_CAMERA", dist="cont", daily=True, cumulative=True)
    snap_group_unique_user_recipient_send_from_CAMERA=Metric(col="snap_group_unique_user_recipient_send_from_CAMERA", dist="cont", daily=True, cumulative=True)
    snap_recipient_send_from_FEED_SNAP_REPLY=Metric(col="snap_recipient_send_from_FEED_SNAP_REPLY", dist="cont", daily=True, cumulative=True)
    snap_recipient_send_one_to_one_from_FEED_SNAP_REPLY=Metric(col="snap_recipient_send_one_to_one_from_FEED_SNAP_REPLY", dist="cont", daily=True, cumulative=True)
    snap_group_non_unique_user_recipient_send_from_FEED_SNAP_REPLY=Metric(col="snap_group_non_unique_user_recipient_send_from_FEED_SNAP_REPLY", dist="cont", daily=True, cumulative=True)
    snap_group_unique_user_recipient_send_from_FEED_SNAP_REPLY=Metric(col="snap_group_unique_user_recipient_send_from_FEED_SNAP_REPLY", dist="cont", daily=True, cumulative=True)
    snap_recipient_send_from_IN_CHAT=Metric(col="snap_recipient_send_from_IN_CHAT", dist="cont", daily=True, cumulative=True)
    snap_recipient_send_one_to_one_from_IN_CHAT=Metric(col="snap_recipient_send_one_to_one_from_IN_CHAT", dist="cont", daily=True, cumulative=True)
    snap_group_non_unique_user_recipient_send_from_IN_CHAT=Metric(col="snap_group_non_unique_user_recipient_send_from_IN_CHAT", dist="cont", daily=True, cumulative=True)
    snap_group_unique_user_recipient_send_from_IN_CHAT=Metric(col="snap_group_unique_user_recipient_send_from_IN_CHAT", dist="cont", daily=True, cumulative=True)

    DIRECT_SNAP_CREATE_ACTIVE_DAY=Metric(col="DIRECT_SNAP_CREATE_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_CREATE_CAMERA_ACTIVE_DAY=Metric(col="DIRECT_SNAP_CREATE_CAMERA_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_CREATE_FEED_ACTIVE_DAY=Metric(col="DIRECT_SNAP_CREATE_FEED_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_CREATE_FEED_SNAP_REPLY_ACTIVE_DAY=Metric(col="DIRECT_SNAP_CREATE_FEED_SNAP_REPLY_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_CREATE_IN_CHAT_ACTIVE_DAY=Metric(col="DIRECT_SNAP_CREATE_IN_CHAT_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_CREATE_IN_CHAT_from_ACTION_MENU_ACTIVE_DAY=Metric(col="DIRECT_SNAP_CREATE_IN_CHAT_from_ACTION_MENU_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_CREATE_IN_CHAT_from_DOUBLE_TAP_ACTIVE_DAY=Metric(col="DIRECT_SNAP_CREATE_IN_CHAT_from_DOUBLE_TAP_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_CREATE_ADD_TO_STORY_ACTIVE_DAY=Metric(col="DIRECT_SNAP_CREATE_ADD_TO_STORY_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_CREATE_MINI_PROFILE_ACTIVE_DAY=Metric(col="DIRECT_SNAP_CREATE_MINI_PROFILE_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_CREATE_SEARCH_ACTIVE_DAY=Metric(col="DIRECT_SNAP_CREATE_SEARCH_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_CREATE_with_SNAP_REPLY_STICKER_ACTIVE_DAY=Metric(col="DIRECT_SNAP_CREATE_with_SNAP_REPLY_STICKER_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_ACTIVE_DAY=Metric(col="DIRECT_SNAP_SEND_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_CAMERA_ACTIVE_DAY=Metric(col="DIRECT_SNAP_SEND_CAMERA_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_FEED_ACTIVE_DAY=Metric(col="DIRECT_SNAP_SEND_FEED_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_FEED_SNAP_REPLY_ACTIVE_DAY=Metric(col="DIRECT_SNAP_SEND_FEED_SNAP_REPLY_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_IN_CHAT_ACTIVE_DAY=Metric(col="DIRECT_SNAP_SEND_IN_CHAT_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_IN_CHAT_from_ACTION_MENU_ACTIVE_DAY=Metric(col="DIRECT_SNAP_SEND_IN_CHAT_from_ACTION_MENU_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_IN_CHAT_from_DOUBLE_TAP_ACTIVE_DAY=Metric(col="DIRECT_SNAP_SEND_IN_CHAT_from_DOUBLE_TAP_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_MINI_PROFILE_ACTIVE_DAY=Metric(col="DIRECT_SNAP_SEND_MINI_PROFILE_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_SEARCH_ACTIVE_DAY=Metric(col="DIRECT_SNAP_SEND_SEARCH_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_with_SNAP_REPLY_STICKER_ACTIVE_DAY=Metric(col="DIRECT_SNAP_SEND_with_SNAP_REPLY_STICKER_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_FROM_SENDTO_ACTIVE_DAY=Metric(col="SNAP_SEND_FROM_SENDTO_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_FROM_NON_SENDTO_ACTIVE_DAY=Metric(col="SNAP_SEND_FROM_NON_SENDTO_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    snap_send_story_reply_active_day=Metric(col="snap_send_story_reply_active_day", dist="cont", daily=True, cumulative=True)
    snap_send_without_story_reply_active_day=Metric(col="snap_send_without_story_reply_active_day", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_FF_SHORTCUT_ACTIVE_DAY=Metric(col="DIRECT_SNAP_SEND_FF_SHORTCUT_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_FF_SHORTCUT_UNREPLIED_CONVERSATIONS_ACTIVE_DAY=Metric(col="DIRECT_SNAP_SEND_FF_SHORTCUT_UNREPLIED_CONVERSATIONS_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_FF_SHORTCUT_UNREAD_CONVERSATIONS_ACTIVE_DAY=Metric(col="DIRECT_SNAP_SEND_FF_SHORTCUT_UNREAD_CONVERSATIONS_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_FF_SHORTCUT_STREAKS_ACTIVE_DAY=Metric(col="DIRECT_SNAP_SEND_FF_SHORTCUT_STREAKS_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_FF_SHORTCUT_CUSTOM_ACTIVE_DAY=Metric(col="DIRECT_SNAP_SEND_FF_SHORTCUT_CUSTOM_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_SEND_FF_SHORTCUT_BEST_FRIENDS_ACTIVE_DAY=Metric(col="DIRECT_SNAP_SEND_FF_SHORTCUT_BEST_FRIENDS_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_CELL_POSITION_0_ACTIVE_DAY=Metric(col="SNAP_SEND_CELL_POSITION_0_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_CELL_POSITION_1_ACTIVE_DAY=Metric(col="SNAP_SEND_CELL_POSITION_1_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_CELL_POSITION_2_ACTIVE_DAY=Metric(col="SNAP_SEND_CELL_POSITION_2_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_CELL_POSITION_3_ACTIVE_DAY=Metric(col="SNAP_SEND_CELL_POSITION_3_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_CELL_POSITION_4_ACTIVE_DAY=Metric(col="SNAP_SEND_CELL_POSITION_4_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_CELL_POSITION_5_ACTIVE_DAY=Metric(col="SNAP_SEND_CELL_POSITION_5_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_CELL_POSITION_6_ACTIVE_DAY=Metric(col="SNAP_SEND_CELL_POSITION_6_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_CELL_POSITION_7_ACTIVE_DAY=Metric(col="SNAP_SEND_CELL_POSITION_7_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_CELL_POSITION_8_ACTIVE_DAY=Metric(col="SNAP_SEND_CELL_POSITION_8_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    SNAP_SEND_CELL_POSITION_larger_than_8_ACTIVE_DAY=Metric(col="SNAP_SEND_CELL_POSITION_larger_than_8_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_VIEW_ACTIVE_DAY=Metric(col="DIRECT_SNAP_VIEW_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_VIEW_FROM_FEED_ACTIVE_DAY=Metric(col="DIRECT_SNAP_VIEW_FROM_FEED_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    DIRECT_SNAP_VIEW_FROM_CHAT_ACTIVE_DAY=Metric(col="DIRECT_SNAP_VIEW_FROM_CHAT_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_CELL_POSITION_0_ACTIVE_DAY=Metric(col="SNAP_VIEW_CELL_POSITION_0_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_CELL_POSITION_1_ACTIVE_DAY=Metric(col="SNAP_VIEW_CELL_POSITION_1_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_CELL_POSITION_2_ACTIVE_DAY=Metric(col="SNAP_VIEW_CELL_POSITION_2_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_CELL_POSITION_3_ACTIVE_DAY=Metric(col="SNAP_VIEW_CELL_POSITION_3_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_CELL_POSITION_4_ACTIVE_DAY=Metric(col="SNAP_VIEW_CELL_POSITION_4_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_CELL_POSITION_5_ACTIVE_DAY=Metric(col="SNAP_VIEW_CELL_POSITION_5_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_CELL_POSITION_6_ACTIVE_DAY=Metric(col="SNAP_VIEW_CELL_POSITION_6_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_CELL_POSITION_7_ACTIVE_DAY=Metric(col="SNAP_VIEW_CELL_POSITION_7_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_CELL_POSITION_8_ACTIVE_DAY=Metric(col="SNAP_VIEW_CELL_POSITION_8_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    SNAP_VIEW_CELL_POSITION_larger_than_8_ACTIVE_DAY=Metric(col="SNAP_VIEW_CELL_POSITION_larger_than_8_ACTIVE_DAY", dist="cont", daily=True, cumulative=True)
    snap_single_recipient_send_active_day=Metric(col="snap_single_recipient_send_active_day", dist="cont", daily=True, cumulative=True)
    snap_multi_recipient_send_active_day=Metric(col="snap_multi_recipient_send_active_day", dist="cont", daily=True, cumulative=True)

    sql="""
        SELECT 
            ghost_user_id,
            TIMESTAMP(DATE(event_date)) AS ts,
            SUM(DIRECT_SNAP_CREATE) DIRECT_SNAP_CREATE,
            SUM(DIRECT_SNAP_CREATE_CAMERA) DIRECT_SNAP_CREATE_CAMERA,
            SUM(DIRECT_SNAP_CREATE_FEED) DIRECT_SNAP_CREATE_FEED,
            SUM(DIRECT_SNAP_CREATE_FEED_SNAP_REPLY) DIRECT_SNAP_CREATE_FEED_SNAP_REPLY,
            SUM(DIRECT_SNAP_CREATE_IN_CHAT) DIRECT_SNAP_CREATE_IN_CHAT,
            SUM(DIRECT_SNAP_CREATE_IN_CHAT_from_ACTION_MENU) DIRECT_SNAP_CREATE_IN_CHAT_from_ACTION_MENU,
            SUM(DIRECT_SNAP_CREATE_IN_CHAT_from_DOUBLE_TAP) DIRECT_SNAP_CREATE_IN_CHAT_from_DOUBLE_TAP,    
            SUM(DIRECT_SNAP_CREATE_ADD_TO_STORY) DIRECT_SNAP_CREATE_ADD_TO_STORY,
            SUM(DIRECT_SNAP_CREATE_MINI_PROFILE) DIRECT_SNAP_CREATE_MINI_PROFILE,
            SUM(DIRECT_SNAP_CREATE_SEARCH) DIRECT_SNAP_CREATE_SEARCH,
            SUM(DIRECT_SNAP_CREATE_with_SNAP_REPLY_STICKER) DIRECT_SNAP_CREATE_with_SNAP_REPLY_STICKER,  
            SUM(snap_create_image) SNAP_CREATE_IMAGE,
            SUM(snap_create_video) SNAP_CREATE_VIDEO,
            SUM(SNAP_CREATE_GROUP) as SNAP_CREATE_GROUP,
            SUM(SNAP_CREATE_IND) as SNAP_CREATE_IND,
            SUM(snap_create_image_group) SNAP_CREATE_IMAGE_GROUP,
            SUM(snap_create_image_ind) SNAP_CREATE_IMAGE_IND,
            SUM(snap_create_video_group) SNAP_CREATE_VIDEO_GROUP,
            SUM(snap_create_video_ind) SNAP_CREATE_VIDEO_IND,
            SUM(DIRECT_SNAP_SEND) DIRECT_SNAP_SEND,
            SUM(DIRECT_SNAP_SEND_CAMERA) DIRECT_SNAP_SEND_CAMERA,
            SUM(DIRECT_SNAP_SEND_FEED) DIRECT_SNAP_SEND_FEED,
            SUM(DIRECT_SNAP_SEND_FEED_SNAP_REPLY) DIRECT_SNAP_SEND_FEED_SNAP_REPLY,
            SUM(DIRECT_SNAP_SEND_IN_CHAT) DIRECT_SNAP_SEND_IN_CHAT,
            SUM(DIRECT_SNAP_SEND_IN_CHAT_from_ACTION_MENU) DIRECT_SNAP_SEND_IN_CHAT_from_ACTION_MENU,
            SUM(DIRECT_SNAP_SEND_IN_CHAT_from_DOUBLE_TAP) DIRECT_SNAP_SEND_IN_CHAT_from_DOUBLE_TAP,   
            SUM(DIRECT_SNAP_SEND_MINI_PROFILE) DIRECT_SNAP_SEND_MINI_PROFILE,
            SUM(DIRECT_SNAP_SEND_SEARCH) DIRECT_SNAP_SEND_SEARCH,
            SUM(DIRECT_SNAP_SEND_with_SNAP_REPLY_STICKER) DIRECT_SNAP_SEND_with_SNAP_REPLY_STICKER,  
            SUM(SNAP_SEND_FROM_SENDTO) AS SNAP_SEND_FROM_SENDTO,
            SUM(SNAP_SEND_FROM_NON_SENDTO) AS SNAP_SEND_FROM_NON_SENDTO,
            SUM(snap_send_story_reply) AS snap_send_story_reply,
            SUM(snap_send_without_story_reply) AS snap_send_without_story_reply,
            SUM(DIRECT_SNAP_SEND_FF_SHORTCUT) AS DIRECT_SNAP_SEND_FF_SHORTCUT,
            SUM(DIRECT_SNAP_SEND_FF_SHORTCUT_UNREPLIED_CONVERSATIONS) AS DIRECT_SNAP_SEND_FF_SHORTCUT_UNREPLIED_CONVERSATIONS,
            SUM(DIRECT_SNAP_SEND_FF_SHORTCUT_STREAKS) AS DIRECT_SNAP_SEND_FF_SHORTCUT_STREAKS,
            SUM(DIRECT_SNAP_SEND_FF_SHORTCUT_UNREAD_CONVERSATIONS) AS DIRECT_SNAP_SEND_FF_SHORTCUT_UNREAD_CONVERSATIONS,
            SUM(DIRECT_SNAP_SEND_FF_SHORTCUT_CUSTOM) AS DIRECT_SNAP_SEND_FF_SHORTCUT_CUSTOM,
            SUM(DIRECT_SNAP_SEND_FF_SHORTCUT_BEST_FRIENDS) AS DIRECT_SNAP_SEND_FF_SHORTCUT_BEST_FRIENDS,
            SUM(SNAP_SEND_CELL_POSITION_0) AS SNAP_SEND_CELL_POSITION_0,
            SUM(SNAP_SEND_CELL_POSITION_1) AS SNAP_SEND_CELL_POSITION_1,
            SUM(SNAP_SEND_CELL_POSITION_2) AS SNAP_SEND_CELL_POSITION_2,
            SUM(SNAP_SEND_CELL_POSITION_3) AS SNAP_SEND_CELL_POSITION_3,
            SUM(SNAP_SEND_CELL_POSITION_4) AS SNAP_SEND_CELL_POSITION_4,
            SUM(SNAP_SEND_CELL_POSITION_5) AS SNAP_SEND_CELL_POSITION_5,
            SUM(SNAP_SEND_CELL_POSITION_6) AS SNAP_SEND_CELL_POSITION_6,
            SUM(SNAP_SEND_CELL_POSITION_7) AS SNAP_SEND_CELL_POSITION_7,
            SUM(SNAP_SEND_CELL_POSITION_8) AS SNAP_SEND_CELL_POSITION_8,
            SUM(SNAP_SEND_CELL_POSITION_larger_than_8) AS SNAP_SEND_CELL_POSITION_larger_than_8,
            SUM(snap_send_image) SNAP_SEND_IMAGE,
            SUM(snap_send_video) SNAP_SEND_VIDEO,
            SUM(DIRECT_SNAP_SEND_GROUP) as SNAP_SEND_GROUP,
            SUM(DIRECT_SNAP_SEND_IND) as SNAP_SEND_IND,
            SUM(snap_send_image_group) SNAP_SEND_IMAGE_GROUP,
            SUM(snap_send_video_group) SNAP_SEND_VIDEO_GROUP,
            SUM(snap_send_image_ind) SNAP_SEND_IMAGE_IND,
            SUM(snap_send_video_ind) SNAP_SEND_VIDEO_IND,
            SUM(DIRECT_SNAP_VIEW) AS DIRECT_SNAP_VIEW,
            SUM(snap_view_unique_snap_id) as snap_view_unique_snap_id,
            SUM(SNAP_VIEW_FROM_FEED) AS DIRECT_SNAP_VIEW_FROM_FEED,
            SUM(SNAP_VIEW_FROM_CHAT) AS DIRECT_SNAP_VIEW_FROM_CHAT,
            SUM(DIRECT_SNAP_VIEW_TIME) AS SNAP_VIEW_TIME_SEC,
            SUM(snap_view_image) SNAP_VIEW_IMAGE,
            SUM(snap_view_video) SNAP_VIEW_VIDEO,
            SUM(DIRECT_SNAP_VIEW_GROUP) as SNAP_VIEW_GROUP,
            SUM(DIRECT_SNAP_VIEW_IND) as SNAP_VIEW_IND,
            SUM(snap_view_image_ind) SNAP_VIEW_IMAGE_IND,
            SUM(snap_view_video_ind) SNAP_VIEW_VIDEO_IND,
            SUM(snap_view_image_group) SNAP_VIEW_IMAGE_GROUP,
            SUM(snap_view_video_group) SNAP_VIEW_VIDEO_GROUP,
            SUM(SNAP_VIEW_CELL_POSITION_0) AS SNAP_VIEW_CELL_POSITION_0,
            SUM(SNAP_VIEW_CELL_POSITION_1) AS SNAP_VIEW_CELL_POSITION_1,
            SUM(SNAP_VIEW_CELL_POSITION_2) AS SNAP_VIEW_CELL_POSITION_2,
            SUM(SNAP_VIEW_CELL_POSITION_3) AS SNAP_VIEW_CELL_POSITION_3,
            SUM(SNAP_VIEW_CELL_POSITION_4) AS SNAP_VIEW_CELL_POSITION_4,
            SUM(SNAP_VIEW_CELL_POSITION_5) AS SNAP_VIEW_CELL_POSITION_5,
            SUM(SNAP_VIEW_CELL_POSITION_6) AS SNAP_VIEW_CELL_POSITION_6,
            SUM(SNAP_VIEW_CELL_POSITION_7) AS SNAP_VIEW_CELL_POSITION_7,
            SUM(SNAP_VIEW_CELL_POSITION_8) AS SNAP_VIEW_CELL_POSITION_8,
            SUM(SNAP_VIEW_CELL_POSITION_larger_than_8) AS SNAP_VIEW_CELL_POSITION_larger_than_8,
            
            SUM(DIRECT_SNAP_SEND_RECIPIENT_COUNT) AS snap_recipient_send,
            SUM(snap_send_single_recipient_count) AS snap_single_recipient_send,
            SUM(snap_send_multi_recipient_count) AS snap_multi_recipient_send,
            SUM(snap_recipient_send_from_sendto) AS snap_recipient_send_from_sendto,
            SUM(snap_recipient_one_to_one_send_from_sendto) AS snap_recipient_one_to_one_send_from_sendto,
            SUM(snap_recipient_non_unique_group_send_from_sendto) AS snap_recipient_non_unique_group_send_from_sendto,
            SUM(snap_recipient_unique_group_send_from_sendto) AS snap_recipient_unique_group_send_from_sendto,
            SUM(snap_recipient_send_one_to_one_correct) AS snap_recipient_send_one_to_one,
            SUM(snap_send_group) AS snap_group_recipient_send,
            SUM(snap_group_non_unique_user_recipient_count) AS snap_group_non_unique_user_recipient_send,
            SUM(snap_group_unique_user_recipient_count) AS snap_group_unique_user_recipient_send,
            SUM(snap_recipient_send_one_to_one_from_feed) AS snap_recipient_send_from_feed,
            SUM(snap_recipient_send_one_to_one_from_feed_correct) AS snap_recipient_send_one_to_one_from_feed,
            SUM(snap_group_non_unique_user_recipient_count_from_feed) AS snap_group_non_unique_user_recipient_send_from_feed,
            SUM(snap_group_unique_user_recipient_count_from_feed) AS snap_group_unique_user_recipient_send_from_feed,
            SUM(snap_recipient_send_one_to_one_from_CAMERA) AS snap_recipient_send_from_CAMERA,
            SUM(snap_recipient_send_one_to_one_from_CAMERA_correct) AS snap_recipient_send_one_to_one_from_CAMERA,
            SUM(snap_group_non_unique_user_recipient_count_from_CAMERA) AS snap_group_non_unique_user_recipient_send_from_CAMERA,
            SUM(snap_group_unique_user_recipient_count_from_CAMERA) AS snap_group_unique_user_recipient_send_from_CAMERA,
            SUM(snap_recipient_send_one_to_one_from_FEED_SNAP_REPLY) AS snap_recipient_send_from_FEED_SNAP_REPLY,
            SUM(snap_recipient_send_one_to_one_from_FEED_SNAP_REPLY_correct) AS snap_recipient_send_one_to_one_from_FEED_SNAP_REPLY,
            SUM(snap_group_non_unique_user_recipient_count_from_FEED_SNAP_REPLY) AS snap_group_non_unique_user_recipient_send_from_FEED_SNAP_REPLY,
            SUM(snap_group_unique_user_recipient_count_from_FEED_SNAP_REPLY) AS snap_group_unique_user_recipient_send_from_FEED_SNAP_REPLY,
            SUM(snap_recipient_send_one_to_one_from_IN_CHAT) AS snap_recipient_send_from_IN_CHAT,
            SUM(snap_recipient_send_one_to_one_from_IN_CHAT_correct) AS snap_recipient_send_one_to_one_from_IN_CHAT,
            SUM(snap_group_non_unique_user_recipient_count_from_IN_CHAT) AS snap_group_non_unique_user_recipient_send_from_IN_CHAT,
            SUM(snap_group_unique_user_recipient_count_from_IN_CHAT) AS snap_group_unique_user_recipient_send_from_IN_CHAT,

            SUM(IF(DIRECT_SNAP_CREATE>0,1,0)) DIRECT_SNAP_CREATE_ACTIVE_DAY,
            SUM(IF(DIRECT_SNAP_CREATE_CAMERA>0,1,0)) DIRECT_SNAP_CREATE_CAMERA_ACTIVE_DAY,
            SUM(IF(DIRECT_SNAP_CREATE_FEED>0,1,0)) DIRECT_SNAP_CREATE_FEED_ACTIVE_DAY,
            SUM(IF(DIRECT_SNAP_CREATE_FEED_SNAP_REPLY>0,1,0)) DIRECT_SNAP_CREATE_FEED_SNAP_REPLY_ACTIVE_DAY,
            SUM(IF(DIRECT_SNAP_CREATE_IN_CHAT>0,1,0)) DIRECT_SNAP_CREATE_IN_CHAT_ACTIVE_DAY,
            SUM(IF(DIRECT_SNAP_CREATE_IN_CHAT_from_ACTION_MENU>0,1,0)) DIRECT_SNAP_CREATE_IN_CHAT_from_ACTION_MENU_ACTIVE_DAY,
            SUM(IF(DIRECT_SNAP_CREATE_IN_CHAT_from_DOUBLE_TAP>0,1,0)) DIRECT_SNAP_CREATE_IN_CHAT_from_DOUBLE_TAP_ACTIVE_DAY,
            SUM(IF(DIRECT_SNAP_CREATE_ADD_TO_STORY>0,1,0)) DIRECT_SNAP_CREATE_ADD_TO_STORY_ACTIVE_DAY,
            SUM(IF(DIRECT_SNAP_CREATE_MINI_PROFILE>0,1,0)) DIRECT_SNAP_CREATE_MINI_PROFILE_ACTIVE_DAY,
            SUM(IF(DIRECT_SNAP_CREATE_SEARCH>0,1,0)) DIRECT_SNAP_CREATE_SEARCH_ACTIVE_DAY,
            SUM(IF(DIRECT_SNAP_CREATE_with_SNAP_REPLY_STICKER>0,1,0)) DIRECT_SNAP_CREATE_with_SNAP_REPLY_STICKER_ACTIVE_DAY,  
            SUM(IF(DIRECT_SNAP_SEND>0,1,0)) DIRECT_SNAP_SEND_ACTIVE_DAY,
            SUM(IF(DIRECT_SNAP_SEND_CAMERA>0,1,0)) DIRECT_SNAP_SEND_CAMERA_ACTIVE_DAY,
            SUM(IF(DIRECT_SNAP_SEND_FEED>0,1,0)) DIRECT_SNAP_SEND_FEED_ACTIVE_DAY,
            SUM(IF(DIRECT_SNAP_SEND_FEED_SNAP_REPLY>0,1,0)) DIRECT_SNAP_SEND_FEED_SNAP_REPLY_ACTIVE_DAY,
            SUM(IF(DIRECT_SNAP_SEND_IN_CHAT>0,1,0)) DIRECT_SNAP_SEND_IN_CHAT_ACTIVE_DAY,
            SUM(IF(DIRECT_SNAP_SEND_IN_CHAT_from_ACTION_MENU>0,1,0)) DIRECT_SNAP_SEND_IN_CHAT_from_ACTION_MENU_ACTIVE_DAY,
            SUM(IF(DIRECT_SNAP_SEND_IN_CHAT_from_DOUBLE_TAP>0,1,0)) DIRECT_SNAP_SEND_IN_CHAT_from_DOUBLE_TAP_ACTIVE_DAY,
            SUM(IF(DIRECT_SNAP_SEND_MINI_PROFILE>0,1,0)) DIRECT_SNAP_SEND_MINI_PROFILE_ACTIVE_DAY,
            SUM(IF(DIRECT_SNAP_SEND_SEARCH>0,1,0)) DIRECT_SNAP_SEND_SEARCH_ACTIVE_DAY,
            SUM(IF(DIRECT_SNAP_SEND_with_SNAP_REPLY_STICKER>0,1,0)) DIRECT_SNAP_SEND_with_SNAP_REPLY_STICKER_ACTIVE_DAY,
            SUM(IF(SNAP_SEND_FROM_SENDTO>0,1,0)) SNAP_SEND_FROM_SENDTO_ACTIVE_DAY,
            SUM(IF(SNAP_SEND_FROM_NON_SENDTO>0,1,0)) SNAP_SEND_FROM_NON_SENDTO_ACTIVE_DAY,
            SUM(snap_send_story_reply_uu) AS snap_send_story_reply_active_day,
            SUM(snap_send_without_story_reply_uu) AS snap_send_without_story_reply_active_day,
            SUM(DIRECT_SNAP_SEND_FF_SHORTCUT_UU) AS DIRECT_SNAP_SEND_FF_SHORTCUT_ACTIVE_DAY,
            SUM(DIRECT_SNAP_SEND_FF_SHORTCUT_UNREPLIED_CONVERSATIONS_UU) AS DIRECT_SNAP_SEND_FF_SHORTCUT_UNREPLIED_CONVERSATIONS_ACTIVE_DAY,
            SUM(DIRECT_SNAP_SEND_FF_SHORTCUT_UNREAD_CONVERSATIONS_UU) AS DIRECT_SNAP_SEND_FF_SHORTCUT_UNREAD_CONVERSATIONS_ACTIVE_DAY,
            SUM(DIRECT_SNAP_SEND_FF_SHORTCUT_STREAKS_UU) AS DIRECT_SNAP_SEND_FF_SHORTCUT_STREAKS_ACTIVE_DAY,
            SUM(DIRECT_SNAP_SEND_FF_SHORTCUT_CUSTOM_UU) AS DIRECT_SNAP_SEND_FF_SHORTCUT_CUSTOM_ACTIVE_DAY,
            SUM(DIRECT_SNAP_SEND_FF_SHORTCUT_BEST_FRIENDS_UU) AS DIRECT_SNAP_SEND_FF_SHORTCUT_BEST_FRIENDS_ACTIVE_DAY,
            SUM(SNAP_SEND_CELL_POSITION_0_UU) AS SNAP_SEND_CELL_POSITION_0_ACTIVE_DAY,
            SUM(SNAP_SEND_CELL_POSITION_1_UU) AS SNAP_SEND_CELL_POSITION_1_ACTIVE_DAY,
            SUM(SNAP_SEND_CELL_POSITION_2_UU) AS SNAP_SEND_CELL_POSITION_2_ACTIVE_DAY,
            SUM(SNAP_SEND_CELL_POSITION_3_UU) AS SNAP_SEND_CELL_POSITION_3_ACTIVE_DAY,
            SUM(SNAP_SEND_CELL_POSITION_4_UU) AS SNAP_SEND_CELL_POSITION_4_ACTIVE_DAY,
            SUM(SNAP_SEND_CELL_POSITION_5_UU) AS SNAP_SEND_CELL_POSITION_5_ACTIVE_DAY,
            SUM(SNAP_SEND_CELL_POSITION_6_UU) AS SNAP_SEND_CELL_POSITION_6_ACTIVE_DAY,
            SUM(SNAP_SEND_CELL_POSITION_7_UU) AS SNAP_SEND_CELL_POSITION_7_ACTIVE_DAY,
            SUM(SNAP_SEND_CELL_POSITION_8_UU) AS SNAP_SEND_CELL_POSITION_8_ACTIVE_DAY,
            SUM(SNAP_SEND_CELL_POSITION_larger_than_8_UU) AS SNAP_SEND_CELL_POSITION_larger_than_8_ACTIVE_DAY,
            SUM(IF(DIRECT_SNAP_VIEW>0,1,0)) DIRECT_SNAP_VIEW_ACTIVE_DAY,
            SUM(IF(SNAP_VIEW_FROM_FEED>0,1,0)) DIRECT_SNAP_VIEW_FROM_FEED_ACTIVE_DAY,
            SUM(IF(SNAP_VIEW_FROM_CHAT>0,1,0)) DIRECT_SNAP_VIEW_FROM_CHAT_ACTIVE_DAY,
            SUM(SNAP_VIEW_CELL_POSITION_0_UU) AS SNAP_VIEW_CELL_POSITION_0_ACTIVE_DAY,
            SUM(SNAP_VIEW_CELL_POSITION_1_UU) AS SNAP_VIEW_CELL_POSITION_1_ACTIVE_DAY,
            SUM(SNAP_VIEW_CELL_POSITION_2_UU) AS SNAP_VIEW_CELL_POSITION_2_ACTIVE_DAY,
            SUM(SNAP_VIEW_CELL_POSITION_3_UU) AS SNAP_VIEW_CELL_POSITION_3_ACTIVE_DAY,
            SUM(SNAP_VIEW_CELL_POSITION_4_UU) AS SNAP_VIEW_CELL_POSITION_4_ACTIVE_DAY,
            SUM(SNAP_VIEW_CELL_POSITION_5_UU) AS SNAP_VIEW_CELL_POSITION_5_ACTIVE_DAY,
            SUM(SNAP_VIEW_CELL_POSITION_6_UU) AS SNAP_VIEW_CELL_POSITION_6_ACTIVE_DAY,
            SUM(SNAP_VIEW_CELL_POSITION_7_UU) AS SNAP_VIEW_CELL_POSITION_7_ACTIVE_DAY,
            SUM(SNAP_VIEW_CELL_POSITION_8_UU) AS SNAP_VIEW_CELL_POSITION_8_ACTIVE_DAY,
            SUM(SNAP_VIEW_CELL_POSITION_larger_than_8_UU) AS SNAP_VIEW_CELL_POSITION_larger_than_8_ACTIVE_DAY,
            SUM(IF(snap_send_single_recipient_count>0,1,0)) snap_single_recipient_send_active_day,
            SUM(IF(snap_send_multi_recipient_count>0,1,0)) snap_multi_recipient_send_active_day
            

        FROM
        `sc-analytics.report_growth.friends_feed_snap_user_level_20*`
        WHERE _table_suffix between '{start_date}' and '{end_date}'
        GROUP BY 1, 2     
        """
    
    def sql_callable(start_date, end_date):
        start_date, end_date = start_date[-6:], end_date[-6:]
        return sql.format(start_date=start_date, end_date=end_date)
    
    mt = MetricTable(
        sql=sql.format(start_date=start_date, end_date=end_date),
        sql_callable = sql_callable,
        metrics=[DIRECT_SNAP_CREATE,
                DIRECT_SNAP_CREATE_CAMERA,
                DIRECT_SNAP_CREATE_FEED,
                DIRECT_SNAP_CREATE_FEED_SNAP_REPLY,
                DIRECT_SNAP_CREATE_IN_CHAT,
                DIRECT_SNAP_CREATE_IN_CHAT_from_ACTION_MENU,
                DIRECT_SNAP_CREATE_IN_CHAT_from_DOUBLE_TAP,
                DIRECT_SNAP_CREATE_ADD_TO_STORY,
                DIRECT_SNAP_CREATE_MINI_PROFILE,
                DIRECT_SNAP_CREATE_SEARCH,
                DIRECT_SNAP_CREATE_with_SNAP_REPLY_STICKER,
                SNAP_CREATE_IMAGE,
                SNAP_CREATE_VIDEO,
                SNAP_CREATE_GROUP,
                SNAP_CREATE_IND,
                SNAP_CREATE_IMAGE_GROUP,
                SNAP_CREATE_IMAGE_IND,
                SNAP_CREATE_VIDEO_GROUP,
                SNAP_CREATE_VIDEO_IND,
                DIRECT_SNAP_SEND,
                DIRECT_SNAP_SEND_CAMERA,
                DIRECT_SNAP_SEND_FEED,
                DIRECT_SNAP_SEND_FEED_SNAP_REPLY,
                DIRECT_SNAP_SEND_IN_CHAT,
                DIRECT_SNAP_SEND_IN_CHAT_from_ACTION_MENU,
                DIRECT_SNAP_SEND_IN_CHAT_from_DOUBLE_TAP,
                DIRECT_SNAP_SEND_MINI_PROFILE,
                DIRECT_SNAP_SEND_SEARCH,
                DIRECT_SNAP_SEND_with_SNAP_REPLY_STICKER,
                snap_send_story_reply,
                snap_send_without_story_reply,
                SNAP_SEND_FROM_SENDTO,
                SNAP_SEND_FROM_NON_SENDTO,
                DIRECT_SNAP_SEND_FF_SHORTCUT,
                DIRECT_SNAP_SEND_FF_SHORTCUT_UNREPLIED_CONVERSATIONS,
                DIRECT_SNAP_SEND_FF_SHORTCUT_STREAKS,
                DIRECT_SNAP_SEND_FF_SHORTCUT_UNREAD_CONVERSATIONS,
                DIRECT_SNAP_SEND_FF_SHORTCUT_CUSTOM,
                DIRECT_SNAP_SEND_FF_SHORTCUT_BEST_FRIENDS,
                SNAP_SEND_CELL_POSITION_0,
                SNAP_SEND_CELL_POSITION_1,
                SNAP_SEND_CELL_POSITION_2,
                SNAP_SEND_CELL_POSITION_3,
                SNAP_SEND_CELL_POSITION_4,
                SNAP_SEND_CELL_POSITION_5,
                SNAP_SEND_CELL_POSITION_6,
                SNAP_SEND_CELL_POSITION_7,
                SNAP_SEND_CELL_POSITION_8,
                SNAP_SEND_CELL_POSITION_larger_than_8,
                SNAP_SEND_IMAGE,
                SNAP_SEND_VIDEO,
                SNAP_SEND_GROUP,
                SNAP_SEND_IND,
                SNAP_SEND_IMAGE_GROUP,
                SNAP_SEND_VIDEO_GROUP,
                SNAP_SEND_IMAGE_IND,
                SNAP_SEND_VIDEO_IND,
                DIRECT_SNAP_VIEW,
                snap_view_unique_snap_id,
                DIRECT_SNAP_VIEW_FROM_FEED,
                DIRECT_SNAP_VIEW_FROM_CHAT,
                SNAP_VIEW_TIME_SEC,
                SNAP_VIEW_IMAGE,
                SNAP_VIEW_VIDEO,
                SNAP_VIEW_GROUP,
                SNAP_VIEW_IND,
                SNAP_VIEW_IMAGE_IND,
                SNAP_VIEW_VIDEO_IND,
                SNAP_VIEW_IMAGE_GROUP,
                SNAP_VIEW_VIDEO_GROUP,
                SNAP_VIEW_CELL_POSITION_0,
                SNAP_VIEW_CELL_POSITION_1,
                SNAP_VIEW_CELL_POSITION_2,
                SNAP_VIEW_CELL_POSITION_3,
                SNAP_VIEW_CELL_POSITION_4,
                SNAP_VIEW_CELL_POSITION_5,
                SNAP_VIEW_CELL_POSITION_6,
                SNAP_VIEW_CELL_POSITION_7,
                SNAP_VIEW_CELL_POSITION_8,
                SNAP_VIEW_CELL_POSITION_larger_than_8,

                snap_recipient_send,
                snap_single_recipient_send,
                snap_multi_recipient_send,
                snap_recipient_send_from_sendto,
                snap_recipient_one_to_one_send_from_sendto,
                snap_recipient_non_unique_group_send_from_sendto,
                snap_recipient_unique_group_send_from_sendto,
                snap_recipient_send_one_to_one,
                snap_group_recipient_send,
                snap_group_non_unique_user_recipient_send,
                snap_group_unique_user_recipient_send,
                snap_recipient_send_from_feed,
                snap_recipient_send_one_to_one_from_feed,
                snap_group_non_unique_user_recipient_send_from_feed,
                snap_group_unique_user_recipient_send_from_feed,
                snap_recipient_send_from_CAMERA,
                snap_recipient_send_one_to_one_from_CAMERA,
                snap_group_non_unique_user_recipient_send_from_CAMERA,
                snap_group_unique_user_recipient_send_from_CAMERA,
                snap_recipient_send_from_FEED_SNAP_REPLY,
                snap_recipient_send_one_to_one_from_FEED_SNAP_REPLY,
                snap_group_non_unique_user_recipient_send_from_FEED_SNAP_REPLY,
                snap_group_unique_user_recipient_send_from_FEED_SNAP_REPLY,
                snap_recipient_send_from_IN_CHAT,
                snap_recipient_send_one_to_one_from_IN_CHAT,
                snap_group_non_unique_user_recipient_send_from_IN_CHAT,
                snap_group_unique_user_recipient_send_from_IN_CHAT,

                DIRECT_SNAP_CREATE_ACTIVE_DAY,
                DIRECT_SNAP_CREATE_CAMERA_ACTIVE_DAY,
                DIRECT_SNAP_CREATE_FEED_ACTIVE_DAY,
                DIRECT_SNAP_CREATE_FEED_SNAP_REPLY_ACTIVE_DAY,
                DIRECT_SNAP_CREATE_IN_CHAT_ACTIVE_DAY,
                DIRECT_SNAP_CREATE_IN_CHAT_from_ACTION_MENU_ACTIVE_DAY,
                DIRECT_SNAP_CREATE_IN_CHAT_from_DOUBLE_TAP_ACTIVE_DAY,
                DIRECT_SNAP_CREATE_ADD_TO_STORY_ACTIVE_DAY,
                DIRECT_SNAP_CREATE_MINI_PROFILE_ACTIVE_DAY,
                DIRECT_SNAP_CREATE_SEARCH_ACTIVE_DAY,
                DIRECT_SNAP_CREATE_with_SNAP_REPLY_STICKER_ACTIVE_DAY,
                DIRECT_SNAP_SEND_ACTIVE_DAY,
                DIRECT_SNAP_SEND_CAMERA_ACTIVE_DAY,
                DIRECT_SNAP_SEND_FEED_ACTIVE_DAY,
                DIRECT_SNAP_SEND_FEED_SNAP_REPLY_ACTIVE_DAY,
                DIRECT_SNAP_SEND_IN_CHAT_ACTIVE_DAY,
                DIRECT_SNAP_SEND_IN_CHAT_from_ACTION_MENU_ACTIVE_DAY,
                DIRECT_SNAP_SEND_IN_CHAT_from_DOUBLE_TAP_ACTIVE_DAY,
                DIRECT_SNAP_SEND_MINI_PROFILE_ACTIVE_DAY,
                DIRECT_SNAP_SEND_SEARCH_ACTIVE_DAY,
                DIRECT_SNAP_SEND_with_SNAP_REPLY_STICKER_ACTIVE_DAY,
                SNAP_SEND_FROM_SENDTO_ACTIVE_DAY,
                SNAP_SEND_FROM_NON_SENDTO_ACTIVE_DAY,
                snap_send_story_reply_active_day,
                snap_send_without_story_reply_active_day,
                DIRECT_SNAP_SEND_FF_SHORTCUT_ACTIVE_DAY,
                DIRECT_SNAP_SEND_FF_SHORTCUT_UNREPLIED_CONVERSATIONS_ACTIVE_DAY,
                DIRECT_SNAP_SEND_FF_SHORTCUT_UNREAD_CONVERSATIONS_ACTIVE_DAY,
                DIRECT_SNAP_SEND_FF_SHORTCUT_STREAKS_ACTIVE_DAY,
                DIRECT_SNAP_SEND_FF_SHORTCUT_CUSTOM_ACTIVE_DAY,
                DIRECT_SNAP_SEND_FF_SHORTCUT_BEST_FRIENDS_ACTIVE_DAY,
                SNAP_SEND_CELL_POSITION_0_ACTIVE_DAY,
                SNAP_SEND_CELL_POSITION_1_ACTIVE_DAY,
                SNAP_SEND_CELL_POSITION_2_ACTIVE_DAY,
                SNAP_SEND_CELL_POSITION_3_ACTIVE_DAY,
                SNAP_SEND_CELL_POSITION_4_ACTIVE_DAY,
                SNAP_SEND_CELL_POSITION_5_ACTIVE_DAY,
                SNAP_SEND_CELL_POSITION_6_ACTIVE_DAY,
                SNAP_SEND_CELL_POSITION_7_ACTIVE_DAY,
                SNAP_SEND_CELL_POSITION_8_ACTIVE_DAY,
                SNAP_SEND_CELL_POSITION_larger_than_8_ACTIVE_DAY,
                DIRECT_SNAP_VIEW_ACTIVE_DAY,
                DIRECT_SNAP_VIEW_FROM_FEED_ACTIVE_DAY,
                DIRECT_SNAP_VIEW_FROM_CHAT_ACTIVE_DAY,
                SNAP_VIEW_CELL_POSITION_0_ACTIVE_DAY,
                SNAP_VIEW_CELL_POSITION_1_ACTIVE_DAY,
                SNAP_VIEW_CELL_POSITION_2_ACTIVE_DAY,
                SNAP_VIEW_CELL_POSITION_3_ACTIVE_DAY,
                SNAP_VIEW_CELL_POSITION_4_ACTIVE_DAY,
                SNAP_VIEW_CELL_POSITION_5_ACTIVE_DAY,
                SNAP_VIEW_CELL_POSITION_6_ACTIVE_DAY,
                SNAP_VIEW_CELL_POSITION_7_ACTIVE_DAY,
                SNAP_VIEW_CELL_POSITION_8_ACTIVE_DAY,
                SNAP_VIEW_CELL_POSITION_larger_than_8_ACTIVE_DAY,
                snap_single_recipient_send_active_day,
                snap_multi_recipient_send_active_day
                ],
        name="snap_detail_metrics",
        inner_join_with_mapping=False,
        bq_dialect="standard"
    )
    return mt


# Share Extension Usage
SHARE_EXTENSION_METRICS_SQL_TEMPLATE = """
SELECT
  ts,
  ghost_user_id,
  {calculation_clause}
FROM (
  SELECT
ghost_user_id,
      TIMESTAMP(DATE(event_date)) AS ts,
      SUM(share_extension_open) AS share_extension_open,
      SUM(share_extension_send) AS share_extension_send,
      SUM(share_extension_open_via_suggested_friend) AS share_extension_open_via_suggested_friend,
      SUM(share_extension_open_via_app_icon) AS share_extension_open_via_app_icon,
      SUM(share_extension_send_via_suggested_friend) AS share_extension_send_via_suggested_friend,
      SUM(share_extension_send_via_app_icon) AS share_extension_send_via_app_icon,
      SUM(share_extension_media_open) AS share_extension_media_open,
      SUM(share_extension_media_send) AS share_extension_media_send,
      SUM(share_extension_url_open) AS share_extension_url_open,
      SUM(share_extension_url_send) AS share_extension_url_send,
      SUM(share_extension_text_send) AS share_extension_text_send,
      SUM(share_extension_batched_media_open) AS share_extension_batched_media_open,
      SUM(share_extension_batched_media_send) AS share_extension_batched_media_send,
      SUM(share_extension_image_media_open) AS share_extension_image_media_open,
      SUM(share_extension_image_media_send) AS share_extension_image_media_send,
      SUM(share_extension_video_media_open) AS share_extension_video_media_open,
      SUM(share_extension_video_media_send) AS share_extension_video_media_send,
      SUM(share_extension_open_via_my_story) AS share_extension_open_via_my_story,
      SUM(share_extension_open_via_private_story) AS share_extension_open_via_private_story,
      SUM(share_extension_post_via_my_story) AS share_extension_post_via_my_story,
      SUM(share_extension_post_via_private_story) AS share_extension_post_via_private_story,
      SUM(share_extension_my_story_post) AS share_extension_my_story_post,
      SUM(share_extension_private_story_post) AS share_extension_private_story_post,

FROM 
    `sc-analytics.report_growth.friends_feed_chat_detail_user_level_2*`
    WHERE concat('2',_table_suffix) between '{start_date}' and '{end_date}'
GROUP BY ghost_user_id,ts) z
GROUP BY
  ts,
  ghost_user_id
"""
share_extension_metrics = [
     "share_extension_open",
     "share_extension_send",
     "share_extension_open_via_suggested_friend",
     "share_extension_open_via_app_icon",
     "share_extension_send_via_suggested_friend",
     "share_extension_send_via_app_icon",
     "share_extension_media_open",    
     "share_extension_media_send",
     "share_extension_url_open",      
     "share_extension_url_send",
     "share_extension_text_send",
     "share_extension_batched_media_open",
     "share_extension_batched_media_send",    
     "share_extension_image_media_open",
     "share_extension_image_media_send",      
     "share_extension_video_media_open",
     "share_extension_video_media_send",
     "share_extension_open_via_my_story",
     "share_extension_open_via_private_story",
     "share_extension_post_via_my_story",
     "share_extension_post_via_private_story",
     "share_extension_my_story_post",
     "share_extension_private_story_post",
     "share_extension_open_active_days",
     "share_extension_send_active_days",
     "share_extension_open_via_suggested_friend_active_days",
     "share_extension_open_via_app_icon_active_days",
     "share_extension_send_via_suggested_friend_active_days",
     "share_extension_send_via_app_icon_active_days",
     "share_extension_media_open_active_days",    
     "share_extension_media_send_active_days",
     "share_extension_url_open_active_days",      
     "share_extension_url_send_active_days",
     "share_extension_text_send_active_days",
     "share_extension_batched_media_open_active_days",
     "share_extension_batched_media_send_active_days",    
     "share_extension_image_media_open_active_days",
     "share_extension_image_media_send_active_days",      
     "share_extension_video_media_open_active_days",
     "share_extension_video_media_send_active_days",
     "share_extension_open_via_my_story_active_days",
     "share_extension_open_via_private_story_active_days",
     "share_extension_post_via_my_story_active_days",
     "share_extension_post_via_private_story_active_days",
     "share_extension_my_story_post_active_days",
     "share_extension_private_story_post_active_days",
]

def create_share_extension_metrics(start_date, end_date):  
    ''' Create metrics config for share extension.
    '''
    calculation_statement_list = []
    calculation_statement_count = """
        SUM({metric_root}) AS {metric},
    """
    calculation_statement_uu = """
        MAX(IF({metric_root}>0, 1, 0)) AS {metric},
    """
    calculation_statement_active_days = """
        SUM(IF({metric_root}>0, 1, 0)) AS {metric},
    """
    for m in share_extension_metrics:
        if m.endswith("uu"):
            calculation_statement_list.append(calculation_statement_uu.format(metric_root=m[:-3], metric=m))
        elif m.endswith("active_days"):
            calculation_statement_list.append(calculation_statement_active_days.format(metric_root=m[:-12], metric=m))
        else:
            calculation_statement_list.append(calculation_statement_count.format(metric=m, metric_root=m))
    calculation_clause = " ".join(calculation_statement_list)
    return MetricTable(
        sql=SHARE_EXTENSION_METRICS_SQL_TEMPLATE.format(
                       start_date=start_date,
                       end_date=end_date,
                       calculation_clause=calculation_clause),
        metrics=[Metric(col=m, dist='bin') if m.endswith("uu") else Metric(col=m, dist='cont') for m in share_extension_metrics]
          + [Metric(col='send_rate_from_share_extension_open', numerator='share_extension_send', denominator='share_extension_open', dist='ratio', desired_direction=POSITIVE, daily=False)]
          + [Metric(col='media_send_rate_from_share_extension_open', numerator='share_extension_media_send', denominator='share_extension_media_open', dist='ratio', desired_direction=POSITIVE, daily=False)]
          + [Metric(col='url_send_rate_from_share_extension_open', numerator='share_extension_url_send', denominator='share_extension_url_open', dist='ratio', desired_direction=POSITIVE, daily=False)]
          + [Metric(col='send_rate_from_share_extension_open_via_app_icon', numerator='share_extension_send_via_app_icon', denominator='share_extension_open_via_app_icon', dist='ratio', desired_direction=POSITIVE, daily=False)]
          + [Metric(col='send_rate_from_share_extension_open_via_suggested_friend', numerator='share_extension_send_via_suggested_friend', denominator='share_extension_open_via_suggested_friend', dist='ratio', desired_direction=POSITIVE, daily=False)],
        name='share_extension_metrics',
        bq_dialect="standard"
    )



# Snap Save and Delete Usage
SNAP_SAVE_DELETE_METRICS_SQL_TEMPLATE = """
SELECT
  TIMESTAMP(ts) AS ts,
  ghost_user_id,
  {calculation_clause}
FROM (
  SELECT
ghost_user_id,
    ds AS ts,
    SUM(snap_save) AS snap_save,
    SUM(snap_image_save) AS snap_image_save,
    SUM(snap_video_save) AS snap_video_save,
    SUM(snap_video_no_sound_save) AS snap_video_no_sound_save,            
    SUM(snap_delete) AS snap_delete,
    SUM(snap_image_delete) AS snap_image_delete,
    SUM(snap_video_delete) AS snap_video_delete,
    SUM(snap_video_no_sound_delete) AS snap_video_no_sound_delete,   
    SUM(snap_w_save_delete) AS snap_w_save_delete,
    SUM(snap_image_w_save_delete) AS snap_image_w_save_delete,
    SUM(snap_video_w_save_delete) AS snap_video_w_save_delete,
    SUM(snap_video_no_sound_w_save_delete) AS snap_video_no_sound_w_save_delete,
    SUM(snap_savable_send) AS snap_savable_send,
    SUM(snap_savable_image_send) AS snap_savable_image_send,
    SUM(snap_savable_video_send) AS snap_savable_video_send,
    SUM(snap_savable_video_no_sound_send) AS snap_savable_video_no_sound_send,
    SUM(snap_savable_view) AS snap_savable_view,
    SUM(snap_savable_image_view) AS snap_savable_image_view,
    SUM(snap_savable_video_view) AS snap_savable_video_view,
    SUM(snap_savable_video_no_sound_view) AS snap_savable_video_no_sound_view,

FROM 
`sc-analytics.report_growth.polaroid_snap_save_engagement_user_*`
    WHERE _table_suffix between '{start_date}' and '{end_date}'
GROUP BY ghost_user_id, ts) z
GROUP BY
  ts,
  ghost_user_id
"""
snap_save_delete_metrics = [
    "snap_save",
    "snap_image_save",
    "snap_video_save",
    "snap_video_no_sound_save",
    "snap_delete",
    "snap_image_delete",
    "snap_video_delete",
    "snap_video_no_sound_delete",
    "snap_w_save_delete",
    "snap_image_w_save_delete",
    "snap_video_w_save_delete",
    "snap_video_no_sound_w_save_delete",
    "snap_save_active_days",
    "snap_image_save_active_days",
    "snap_video_save_active_days",
    "snap_video_no_sound_save_active_days",
    "snap_delete_active_days",
    "snap_image_delete_active_days",
    "snap_video_delete_active_days",
    "snap_video_no_sound_delete_active_days",
    "snap_w_save_delete_active_days",
    "snap_image_w_save_delete_active_days",
    "snap_video_w_save_delete_active_days",
    "snap_video_no_sound_w_save_delete_active_days",  
    'snap_savable_send',
    'snap_savable_image_send',
    'snap_savable_video_send',
    'snap_savable_video_no_sound_send',
    'snap_savable_view',
    'snap_savable_image_view',
    'snap_savable_video_view',
    'snap_savable_video_no_sound_view',
]

def create_snap_save_delete_metrics(start_date, end_date):
    ''' Create metrics config for Snap Save and Delete.
    '''
    calculation_statement_list = []
    calculation_statement_count = """
        SUM({metric_root}) AS {metric},
    """
    calculation_statement_uu = """
        MAX(IF({metric_root}>0, 1, 0)) AS {metric},
    """
    calculation_statement_active_days = """
        SUM(IF({metric_root}>0, 1, 0)) AS {metric},
    """
    for m in snap_save_delete_metrics:
        if m.endswith("uu"):
            calculation_statement_list.append(calculation_statement_uu.format(metric_root=m[:-3], metric=m))
        elif m.endswith("active_days"):
            calculation_statement_list.append(calculation_statement_active_days.format(metric_root=m[:-12], metric=m))
        else:
            calculation_statement_list.append(calculation_statement_count.format(metric=m, metric_root=m))
    calculation_clause = " ".join(calculation_statement_list)
    return MetricTable(
        sql=SNAP_SAVE_DELETE_METRICS_SQL_TEMPLATE.format(
                       start_date=start_date,
                       end_date=end_date,
                       calculation_clause=calculation_clause),
        metrics=[Metric(col=m, dist='bin') if m.endswith("uu") else Metric(col=m, dist='cont') for m in snap_save_delete_metrics],
        name='snap_save_delete_metrics',
        bq_dialect="standard"
    )

# Chat Save, Unsave and Delete Usage
CHAT_SAVE_METRICS_SQL_TEMPLATE = """
SELECT
  ts,
  ghost_user_id,
  {calculation_clause}
FROM (
  SELECT
ghost_user_id,
    TIMESTAMP(DATE(event_date)) AS ts,
    SUM(CHAT_CHAT_SAVE) AS CHAT_CHAT_SAVE,
    SUM(CHAT_CHAT_UNSAVE) AS CHAT_CHAT_UNSAVE,
    SUM(CHAT_CHAT_ERASE) AS CHAT_CHAT_ERASE,
    SUM(CHAT_CHAT_ERASE_TEXT) AS CHAT_CHAT_ERASE_TEXT,            
    SUM(CHAT_CHAT_ERASE_SNAP) AS CHAT_CHAT_ERASE_SNAP,
    SUM(CHAT_CHAT_ERASE_SNAP_IMAGE) AS CHAT_CHAT_ERASE_SNAP_IMAGE,
    SUM(CHAT_CHAT_ERASE_SNAP_VIDEO) AS CHAT_CHAT_ERASE_SNAP_VIDEO,            
    SUM(CHAT_CHAT_ERASE_SNAP_VIDEO_NO_SOUND) AS CHAT_CHAT_ERASE_SNAP_VIDEO_NO_SOUND 

FROM 
    `sc-analytics.report_growth.friends_feed_chat_detail_user_level_2*`
    WHERE concat('2',_table_suffix) between '{start_date}' and '{end_date}'
GROUP BY ghost_user_id, ts) z
GROUP BY
  ts,
  ghost_user_id
"""
chat_save_metrics = [
    "CHAT_CHAT_SAVE",
    "CHAT_CHAT_UNSAVE",
    "CHAT_CHAT_ERASE",
    "CHAT_CHAT_ERASE_TEXT",
    "CHAT_CHAT_ERASE_SNAP",
    "CHAT_CHAT_ERASE_SNAP_IMAGE",
    "CHAT_CHAT_ERASE_SNAP_VIDEO",
    "CHAT_CHAT_ERASE_SNAP_VIDEO_NO_SOUND",
    "CHAT_CHAT_SAVE_active_days",
    "CHAT_CHAT_UNSAVE_active_days",
    "CHAT_CHAT_ERASE_active_days",
    "CHAT_CHAT_ERASE_TEXT_active_days",
    "CHAT_CHAT_ERASE_SNAP_active_days",
    "CHAT_CHAT_ERASE_SNAP_IMAGE_active_days",
    "CHAT_CHAT_ERASE_SNAP_VIDEO_active_days",
    "CHAT_CHAT_ERASE_SNAP_VIDEO_NO_SOUND_active_days",
]

def create_chat_save_metrics(start_date, end_date):
    ''' Create metrics config for Chat Save, Unsave and Delete.
    '''
    calculation_statement_list = []
    calculation_statement_count = """
        SUM({metric_root}) AS {metric},
    """
    calculation_statement_uu = """
        MAX(IF({metric_root}>0, 1, 0)) AS {metric},
    """
    calculation_statement_active_days = """
        SUM(IF({metric_root}>0, 1, 0)) AS {metric},
    """
    for m in chat_save_metrics:
        if m.endswith("uu"):
            calculation_statement_list.append(calculation_statement_uu.format(metric_root=m[:-3], metric=m))
        elif m.endswith("active_days"):
            calculation_statement_list.append(calculation_statement_active_days.format(metric_root=m[:-12], metric=m))
        else:
            calculation_statement_list.append(calculation_statement_count.format(metric=m, metric_root=m))
    calculation_clause = " ".join(calculation_statement_list)
    return MetricTable(
        sql=CHAT_SAVE_METRICS_SQL_TEMPLATE.format(
                       start_date=start_date,
                       end_date=end_date,
                       calculation_clause=calculation_clause),
        metrics=[Metric(col=m, dist='bin') if m.endswith("uu") else Metric(col=m, dist='cont') for m in chat_save_metrics],
        name='chat_save_metrics',
        bq_dialect="standard"
    )




def new_chat_page(start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    new_chat_button_click=Metric(col="new_chat_button_click", dist="cont", daily=True, cumulative=True)
    new_chat_button_click_from_create_button=Metric(col="new_chat_button_click_from_create_button", dist="cont", daily=True, cumulative=True)
    new_chat_button_click_from_feed_chat_options=Metric(col="new_chat_button_click_from_feed_chat_options", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit=Metric(col="new_chat_page_visit", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_render_successful=Metric(col="new_chat_page_visit_render_successful", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_render_unsuccessful=Metric(col="new_chat_page_visit_render_unsuccessful", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    new_chat_page_visit_new_group_button=Metric(col="new_chat_page_visit_new_group_button", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_name_group=Metric(col="new_chat_page_visit_name_group", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_cells_selected=Metric(col="new_chat_page_visit_cells_selected", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_cells_unselected=Metric(col="new_chat_page_visit_cells_unselected", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_recents_available=Metric(col="new_chat_page_visit_recents_available", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_friends_available=Metric(col="new_chat_page_visit_friends_available", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_best_friends_available=Metric(col="new_chat_page_visit_best_friends_available", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_group_available=Metric(col="new_chat_page_visit_group_available", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_recents_selected=Metric(col="new_chat_page_visit_recents_selected", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_friends_selected=Metric(col="new_chat_page_visit_friends_selected", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_best_friends_selected=Metric(col="new_chat_page_visit_best_friends_selected", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_group_selected=Metric(col="new_chat_page_visit_group_selected", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_search_selected=Metric(col="new_chat_page_visit_search_selected", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_time_spent_min=Metric(col="new_chat_page_visit_time_spent_min", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_error_message=Metric(col="new_chat_page_visit_error_message", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    new_chat_page_cancel=Metric(col="new_chat_page_cancel", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    new_chat_page_cancel_android=Metric(col="new_chat_page_cancel_android", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    new_chat_page_move_to_next=Metric(col="new_chat_page_move_to_next", dist="cont", daily=True, cumulative=True)
    new_chat_page_move_to_next_android=Metric(col="new_chat_page_move_to_next_android", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_next_page_chat=Metric(col="new_chat_page_visit_next_page_chat", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_next_page_feed=Metric(col="new_chat_page_visit_next_page_feed", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    new_chat_page_visit_next_page_call=Metric(col="new_chat_page_visit_next_page_call", dist="cont", daily=True, cumulative=True)
    chat_create_new_chat=Metric(col="chat_create_new_chat", dist="cont", daily=True, cumulative=True)
    group_chat_create_new_chat=Metric(col="group_chat_create_new_chat", dist="cont", daily=True, cumulative=True)
    one_to_one_chat_create_new_chat=Metric(col="one_to_one_chat_create_new_chat", dist="cont", daily=True, cumulative=True) 
    chat_send_new_chat=Metric(col="chat_send_new_chat", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_chat_page=Metric(col="new_chat_page_visit_chat_page", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_call_page=Metric(col="new_chat_page_visit_call_page", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_group_mode=Metric(col="new_chat_page_visit_group_mode", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_last_tab_new_chat=Metric(col="new_chat_page_visit_last_tab_new_chat", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_last_tab_new_call=Metric(col="new_chat_page_visit_last_tab_new_call", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_button_clicked_chat=Metric(col="new_chat_page_visit_button_clicked_chat", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_button_clicked_start_call=Metric(col="new_chat_page_visit_button_clicked_start_call", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_button_clicked_chat_with_group=Metric(col="new_chat_page_visit_button_clicked_chat_with_group", dist="cont", daily=True, cumulative=True)
    new_chat_page_visit_button_clicked_start_group_call=Metric(col="new_chat_page_visit_button_clicked_start_group_call", dist="cont", daily=True, cumulative=True)
    new_chat_page_search_attempt =Metric(col="new_chat_page_search_attempt", dist="cont", daily=True, cumulative=True)
    
    def get_new_chat_metrics(start_date, end_date):
        sql = """
        SELECT 
            ghost_user_id,
            TIMESTAMP(DATE(event_date)) AS ts,
            SUM(new_chat_button_click) AS new_chat_button_click,
            SUM(new_chat_button_click_from_create_button) AS new_chat_button_click_from_create_button,
            SUM(new_chat_button_click_from_feed_chat_options) AS new_chat_button_click_from_feed_chat_options,
            SUM(new_chat_page_visit) AS new_chat_page_visit,
            SUM(new_chat_page_visit_render_successful) AS new_chat_page_visit_render_successful,
            SUM(new_chat_page_visit_render_unsuccessful) AS new_chat_page_visit_render_unsuccessful,
            SUM(new_chat_page_visit_new_group_button) AS new_chat_page_visit_new_group_button,
            SUM(new_chat_page_visit_name_group) AS new_chat_page_visit_name_group,
            SUM(new_chat_page_visit_cells_selected) AS new_chat_page_visit_cells_selected,
            SUM(new_chat_page_visit_cells_unselected) AS new_chat_page_visit_cells_unselected,
            SUM(new_chat_page_visit_recents_available) AS new_chat_page_visit_recents_available,
            SUM(new_chat_page_visit_friends_available) AS new_chat_page_visit_friends_available,
            SUM(new_chat_page_visit_best_friends_available) AS new_chat_page_visit_best_friends_available,
            SUM(new_chat_page_visit_group_available) AS new_chat_page_visit_group_available,
            SUM(new_chat_page_visit_recents_selected) AS new_chat_page_visit_recents_selected,
            SUM(new_chat_page_visit_friends_selected) AS new_chat_page_visit_friends_selected,
            SUM(new_chat_page_visit_best_friends_selected) AS new_chat_page_visit_best_friends_selected,
            SUM(new_chat_page_visit_group_selected) AS new_chat_page_visit_group_selected,
            SUM(new_chat_page_visit_search_selected) AS new_chat_page_visit_search_selected,
            SUM(new_chat_page_visit_time_spent_min) AS new_chat_page_visit_time_spent_min,
            SUM(new_chat_page_visit_error_message) AS new_chat_page_visit_error_message,
            SUM(new_chat_page_cancel) AS new_chat_page_cancel,
            SUM(new_chat_page_cancel_android) AS new_chat_page_cancel_android,
            SUM(new_chat_page_move_to_next) AS new_chat_page_move_to_next,
            SUM(new_chat_page_move_to_next_android) AS new_chat_page_move_to_next_android,
            SUM(new_chat_page_visit_next_page_chat) AS new_chat_page_visit_next_page_chat,
            SUM(new_chat_page_visit_next_page_feed) AS new_chat_page_visit_next_page_feed,
            SUM(new_chat_page_visit_next_page_call) AS new_chat_page_visit_next_page_call,
            SUM(chat_create_new_chat) AS chat_create_new_chat,
            SUM(group_chat_create_new_chat) AS group_chat_create_new_chat,
            SUM(one_to_one_chat_create_new_chat) AS one_to_one_chat_create_new_chat,
            SUM(chat_send_new_chat) AS chat_send_new_chat,
            SUM(new_chat_page_visit_chat_page) AS new_chat_page_visit_chat_page,
            SUM(new_chat_page_visit_call_page) AS new_chat_page_visit_call_page,
            SUM(new_chat_page_visit_group_mode) AS new_chat_page_visit_group_mode,
            SUM(new_chat_page_visit_last_tab_new_chat) AS new_chat_page_visit_last_tab_new_chat,
            SUM(new_chat_page_visit_last_tab_new_call) AS new_chat_page_visit_last_tab_new_call,
            SUM(new_chat_page_visit_button_clicked_chat) AS new_chat_page_visit_button_clicked_chat,
            SUM(new_chat_page_visit_button_clicked_start_call) AS new_chat_page_visit_button_clicked_start_call,
            SUM(new_chat_page_visit_button_clicked_chat_with_group) AS new_chat_page_visit_button_clicked_chat_with_group,
            SUM(new_chat_page_visit_button_clicked_start_group_call) AS new_chat_page_visit_button_clicked_start_group_call,
            SUM(new_chat_page_search_attempt) AS new_chat_page_search_attempt

            FROM
            `sc-analytics.report_growth.friends_feed_chat_detail_user_level_2*`
            WHERE concat('2',_table_suffix) between '{start_date}' and '{end_date}'
            GROUP BY 1,2
            """.format(
           start_date=start_date,
           end_date=end_date)
        return sql
    
    mt = MetricTable(
        sql=None,
        metrics=[
        new_chat_button_click,
        new_chat_button_click_from_create_button,
        new_chat_button_click_from_feed_chat_options,
        new_chat_page_visit,
        new_chat_page_visit_render_successful,
        new_chat_page_visit_render_unsuccessful,
        new_chat_page_visit_new_group_button,
        new_chat_page_visit_name_group,
        new_chat_page_visit_cells_selected,
        new_chat_page_visit_cells_unselected,
        new_chat_page_visit_recents_available,
        new_chat_page_visit_friends_available,
        new_chat_page_visit_best_friends_available,
        new_chat_page_visit_group_available,
        new_chat_page_visit_recents_selected,
        new_chat_page_visit_friends_selected,
        new_chat_page_visit_best_friends_selected,
        new_chat_page_visit_group_selected,
        new_chat_page_visit_search_selected,
        new_chat_page_visit_time_spent_min,
        new_chat_page_visit_error_message,
        new_chat_page_cancel,
        new_chat_page_cancel_android,
        new_chat_page_move_to_next,
        new_chat_page_move_to_next_android,
        new_chat_page_visit_next_page_chat,
        new_chat_page_visit_next_page_feed,
        new_chat_page_visit_next_page_call,
        chat_create_new_chat,
        group_chat_create_new_chat,
        one_to_one_chat_create_new_chat,
        chat_send_new_chat,
        new_chat_page_visit_chat_page,
        new_chat_page_visit_call_page,
        new_chat_page_visit_group_mode,
        new_chat_page_visit_last_tab_new_chat,
        new_chat_page_visit_last_tab_new_call,
        new_chat_page_visit_button_clicked_chat,
        new_chat_page_visit_button_clicked_start_call,
        new_chat_page_visit_button_clicked_chat_with_group,
        new_chat_page_visit_button_clicked_start_group_call,
        new_chat_page_search_attempt],
        name="new_chat_page",
        inner_join_with_mapping=False,
        bq_dialect="standard",
        sql_callable=get_new_chat_metrics,
        aa=True
    )
    return mt


def voice_notes(start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    audio_note_create=Metric(col="audio_note_create", dist="cont", daily=True, cumulative=True)
    audio_note_create_total_note_time_sec=Metric(col="audio_note_create_total_note_time_sec", dist="cont", daily=True, cumulative=True)
    note_duration_per_create=Metric('note_duration_per_create','Note duration per note create in Sec',dist='cont',daily=True,cumulative=True)
    chat_send_note=Metric(col="chat_send_note", dist="cont", daily=True, cumulative=True)
    audio_note_send_duration_sec=Metric(col="audio_note_send_duration_sec", dist="cont", daily=True, cumulative=True)
    audio_note_send_non_zero_duration=Metric(col="audio_note_send_non_zero_duration", dist="cont", daily=True, cumulative=True)
    note_duration_per_send=Metric('note_duration_per_send','Note duration per note send in Sec',dist='cont',daily=True,cumulative=True)
    chat_view_note=Metric(col="chat_view_note", dist="cont", daily=True, cumulative=True)
    audio_note_view_duration_sec=Metric(col="audio_note_view_duration_sec", dist="cont", daily=True, cumulative=True)
    audio_note_view_non_zero_duration=Metric(col="audio_note_view_non_zero_duration", dist="cont", daily=True, cumulative=True)
    audio_note_playback=Metric(col="audio_note_playback", dist="cont", daily=True, cumulative=True)
    audio_note_playback_sender=Metric(col="audio_note_playback_sender", dist="cont", daily=True, cumulative=True)
    audio_note_playback_recipient=Metric(col="audio_note_playback_recipient", dist="cont", daily=True, cumulative=True)
    audio_note_listen_duration=Metric(col="audio_note_listen_duration", dist="cont", daily=True, cumulative=True)
    audio_note_scrub=Metric(col="audio_note_scrub", dist="cont", daily=True, cumulative=True)
    audio_note_speed_change=Metric(col="audio_note_speed_change", dist="cont", daily=True, cumulative=True)
    voice_note_show_transcript=Metric(col="voice_note_show_transcript", dist="cont", daily=True, cumulative=True)
    voice_note_show_transcript_tapped=Metric(col="voice_note_show_transcript_tapped", dist="cont", daily=True, cumulative=True)
    voice_note_show_transcript_tapped_eligible=Metric(col="voice_note_show_transcript_tapped_eligible", dist="cont", daily=True, cumulative=True)
    voice_note_show_transcript_tapped_not_eligible=Metric(col="voice_note_show_transcript_tapped_not_eligible", dist="cont", daily=True, cumulative=True)
    voice_note_feedback_pressed=Metric(col="voice_note_feedback_pressed", dist="cont", daily=True, cumulative=True)
    voice_note_thumbs_up_feedback_pressed=Metric(col="voice_note_thumbs_up_feedback_pressed", dist="cont", daily=True, cumulative=True)
    voice_note_thumbs_down_feedback_pressed=Metric(col="voice_note_thumbs_down_feedback_pressed", dist="cont", daily=True, cumulative=True)
    voice_note_consumption = Metric(col="voice_note_consumption", dist="cont", daily=True, cumulative=True)
    audio_note_create_uu=Metric(col="audio_note_create_uu", dist="cont", daily=True, cumulative=True)
    chat_send_note_uu=Metric(col="chat_send_note_uu", dist="cont", daily=True, cumulative=True)
    audio_note_send_non_zero_duration_uu=Metric(col="audio_note_send_non_zero_duration_uu", dist="cont", daily=True, cumulative=True)
    chat_view_note_uu=Metric(col="chat_view_note_uu", dist="cont", daily=True, cumulative=True)
    audio_note_view_non_zero_duration_uu=Metric(col="audio_note_view_non_zero_duration_uu", dist="cont", daily=True, cumulative=True)
    audio_note_playback_uu=Metric(col="audio_note_playback_uu", dist="cont", daily=True, cumulative=True)
    audio_note_playback_sender_uu=Metric(col="audio_note_playback_sender_uu", dist="cont", daily=True, cumulative=True)
    audio_note_playback_recipient_uu=Metric(col="audio_note_playback_recipient_uu", dist="cont", daily=True, cumulative=True)
    audio_note_listen_duration_uu=Metric(col="audio_note_listen_duration_uu", dist="cont", daily=True, cumulative=True)
    audio_note_scrub_uu=Metric(col="audio_note_scrub_uu", dist="cont", daily=True, cumulative=True)
    audio_note_speed_change_uu=Metric(col="audio_note_speed_change_uu", dist="cont", daily=True, cumulative=True)
    voice_note_show_transcript_uu=Metric(col="voice_note_show_transcript_uu", dist="cont", daily=True, cumulative=True)
    voice_note_show_transcript_tapped_uu=Metric(col="voice_note_show_transcript_tapped_uu", dist="cont", daily=True, cumulative=True)
    voice_note_show_transcript_tapped_eligible_uu=Metric(col="voice_note_show_transcript_tapped_eligible_uu", dist="cont", daily=True, cumulative=True)
    voice_note_show_transcript_tapped_not_eligible_uu=Metric(col="voice_note_show_transcript_tapped_not_eligible_uu", dist="cont", daily=True, cumulative=True)
    voice_note_feedback_pressed_uu=Metric(col="voice_note_feedback_pressed_uu", dist="cont", daily=True, cumulative=True)
    voice_note_thumbs_up_feedback_pressed_uu=Metric(col="voice_note_thumbs_up_feedback_pressed_uu", dist="cont", daily=True, cumulative=True)
    voice_note_thumbs_down_feedback_pressed_uu=Metric(col="voice_note_thumbs_down_feedback_pressed_uu", dist="cont", daily=True, cumulative=True)
    voice_note_consumption_uu = Metric(col="voice_note_consumption_uu", dist="cont", daily=True, cumulative=True)
    def get_voice_notes(start_date, end_date):
        sql = """
         select
                ghost_user_id,
                TIMESTAMP(DATE(event_date)) AS ts,
                SUM(audio_note_create) AS audio_note_create,
                SUM(audio_note_create_total_note_time_sec) AS audio_note_create_total_note_time_sec,
                IF(sum(audio_note_create)=0 or sum(audio_note_create) is null,null,SUM(audio_note_create_total_note_time_sec)/SUM(audio_note_create)) as note_duration_per_create,
                sum(chat_send_note) AS chat_send_note,
                SUM(audio_note_send_duration_sec) AS audio_note_send_duration_sec,
                SUM(audio_note_send_non_zero_duration) AS audio_note_send_non_zero_duration,
                IF(sum(chat_send_note)=0 or sum(chat_send_note) is null,null,SUM(audio_note_send_duration_sec)/SUM(chat_send_note)) as note_duration_per_send,
                sum(chat_view_note) AS chat_view_note,
                SUM(audio_note_view_duration_sec) AS audio_note_view_duration_sec,
                SUM(audio_note_view_non_zero_duration) AS audio_note_view_non_zero_duration,
                SUM(audio_note_playback) AS audio_note_playback,
                SUM(audio_note_playback_sender) AS audio_note_playback_sender,
                SUM(audio_note_playback_recipient) AS audio_note_playback_recipient,
                SUM(audio_note_listen_duration) AS audio_note_listen_duration,
                SUM(audio_note_scrub) AS audio_note_scrub,
                SUM(audio_note_speed_change) AS audio_note_speed_change,
                SUM(voice_note_show_transcript) AS voice_note_show_transcript,
                SUM(voice_note_show_transcript_tapped) AS voice_note_show_transcript_tapped,
                SUM(voice_note_show_transcript_tapped_eligible) AS voice_note_show_transcript_tapped_eligible,
                SUM(voice_note_show_transcript_tapped_not_eligible) AS voice_note_show_transcript_tapped_not_eligible,
                SUM(voice_note_feedback_pressed) AS voice_note_feedback_pressed,
                SUM(voice_note_thumbs_up_feedback_pressed) AS voice_note_thumbs_up_feedback_pressed,
                SUM(voice_note_thumbs_down_feedback_pressed) AS voice_note_thumbs_down_feedback_pressed,                
                SUM(voice_note_consumption) AS voice_note_consumption,
                hll_count.MERGE(hll_audio_note_create_uu) AS audio_note_create_uu,
                hll_count.MERGE(hll_chat_send_note_uu) AS chat_send_note_uu,
                hll_count.MERGE(hll_audio_note_send_non_zero_duration_uu) AS audio_note_send_non_zero_duration_uu,
                hll_count.MERGE(hll_chat_view_note_uu) AS chat_view_note_uu,
                hll_count.MERGE(hll_audio_note_view_non_zero_duration_uu) AS audio_note_view_non_zero_duration_uu,
                hll_count.MERGE(hll_audio_note_playback_uu) AS audio_note_playback_uu,
                hll_count.MERGE(hll_audio_note_playback_sender_uu) AS audio_note_playback_sender_uu,
                hll_count.MERGE(hll_audio_note_playback_recipient_uu) AS audio_note_playback_recipient_uu,
                hll_count.MERGE(hll_audio_note_listen_duration_uu) AS audio_note_listen_duration_uu,
                hll_count.MERGE(hll_audio_note_scrub_uu) AS audio_note_scrub_uu,
                hll_count.MERGE(hll_audio_note_speed_change_uu) AS audio_note_speed_change_uu,
                hll_count.MERGE(hll_voice_note_show_transcript_uu) AS voice_note_show_transcript_uu,
                hll_count.MERGE(hll_voice_note_show_transcript_tapped_uu) AS voice_note_show_transcript_tapped_uu,
                hll_count.MERGE(hll_voice_note_show_transcript_tapped_eligible_uu) AS voice_note_show_transcript_tapped_eligible_uu,
                hll_count.MERGE(hll_voice_note_show_transcript_tapped_not_eligible_uu) AS voice_note_show_transcript_tapped_not_eligible_uu,
                hll_count.MERGE(hll_voice_note_feedback_pressed_uu) AS voice_note_feedback_pressed_uu,
                hll_count.MERGE(hll_voice_note_thumbs_up_feedback_pressed_uu) AS voice_note_thumbs_up_feedback_pressed_uu,
                hll_count.MERGE(hll_voice_note_thumbs_down_feedback_pressed_uu) AS voice_note_thumbs_down_feedback_pressed_uu,
                hll_count.MERGE(hll_voice_note_consumption_uu) AS voice_note_consumption_uu,

                FROM
                `sc-analytics.report_messaging.messaging_dweb_chat_user_level_2*` 
                WHERE concat('2',_table_suffix) between '{start_date}' and '{end_date}'

                 group by 1,2
            """.format(
           start_date=start_date,
           end_date=end_date)
        return sql
    
    mt = MetricTable(
        sql=None,
        metrics=[
        audio_note_create,
        audio_note_create_total_note_time_sec,
        note_duration_per_create,
        chat_send_note,
        audio_note_send_duration_sec,
        audio_note_send_non_zero_duration,
        note_duration_per_send,
        chat_view_note,
        audio_note_view_duration_sec,
        audio_note_view_non_zero_duration,
        audio_note_playback,
        audio_note_playback_sender,
        audio_note_playback_recipient,
        audio_note_listen_duration,
        audio_note_scrub,
        audio_note_speed_change,
        voice_note_show_transcript,
        voice_note_show_transcript_tapped,
        voice_note_show_transcript_tapped_eligible,
        voice_note_show_transcript_tapped_not_eligible,
        voice_note_feedback_pressed,
        voice_note_thumbs_up_feedback_pressed,
        voice_note_thumbs_down_feedback_pressed,
        voice_note_consumption,
        audio_note_create_uu,
        chat_send_note_uu,
        audio_note_send_non_zero_duration_uu,
        chat_view_note_uu,
        audio_note_view_non_zero_duration_uu,
        audio_note_playback_uu,
        audio_note_playback_sender_uu,
        audio_note_playback_recipient_uu,
        audio_note_listen_duration_uu,
        audio_note_scrub_uu,
        audio_note_speed_change_uu,
        voice_note_show_transcript_uu,
        voice_note_show_transcript_tapped_uu,
        voice_note_show_transcript_tapped_eligible_uu,
        voice_note_show_transcript_tapped_not_eligible_uu,
        voice_note_feedback_pressed_uu,
        voice_note_thumbs_up_feedback_pressed_uu,
        voice_note_thumbs_down_feedback_pressed_uu,
        voice_note_consumption_uu,

        ],
        name="voice_notes",
        inner_join_with_mapping=False,
        bq_dialect="standard",
        sql_callable=get_voice_notes,
        aa=True
    )
    return mt



def voice_notes_other(start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    chat_send_note_negative = Metric(col="chat_send_note_negative", dist="cont", daily=True, cumulative=True)
    chat_send_note_zero = Metric(col="chat_send_note_zero", dist="cont", daily=True, cumulative=True)
    chat_send_note_1_30 = Metric(col="chat_send_note_1_30", dist="cont", daily=True, cumulative=True)
    chat_send_note_30_60 = Metric(col="chat_send_note_30_60", dist="cont", daily=True, cumulative=True)
    chat_send_note_60_120 = Metric(col="chat_send_note_60_120", dist="cont", daily=True, cumulative=True)
    chat_send_note_120_300 = Metric(col="chat_send_note_120_300", dist="cont", daily=True, cumulative=True)
    chat_send_note_300 = Metric(col="chat_send_note_300", dist="cont", daily=True, cumulative=True)
    chat_send_note_negative_uu = Metric(col="chat_send_note_negative_uu", dist="cont", daily=True, cumulative=True)
    chat_send_note_zero_uu = Metric(col="chat_send_note_zero_uu", dist="cont", daily=True, cumulative=True)
    chat_send_note_1_30_uu = Metric(col="chat_send_note_1_30_uu", dist="cont", daily=True, cumulative=True)
    chat_send_note_30_60_uu = Metric(col="chat_send_note_30_60_uu", dist="cont", daily=True, cumulative=True)
    chat_send_note_60_120_uu = Metric(col="chat_send_note_60_120_uu", dist="cont", daily=True, cumulative=True)
    chat_send_note_120_300_uu = Metric(col="chat_send_note_120_300_uu", dist="cont", daily=True, cumulative=True)
    chat_send_note_300_uu = Metric(col="chat_send_note_300_uu", dist="cont", daily=True, cumulative=True)
    def get_voice_notes(start_date, end_date):
        sql = """
         select
                ghost_user_id,
                TIMESTAMP(DATE(event_date)) AS ts,
                SUM(chat_send_note_negative) AS chat_send_note_negative,
                SUM(chat_send_note_zero) AS chat_send_note_zero,
                SUM(chat_send_note_1_30) AS chat_send_note_1_30,
                SUM(chat_send_note_30_60) AS chat_send_note_30_60,
                SUM(chat_send_note_60_120) AS chat_send_note_60_120,
                SUM(chat_send_note_120_300) AS chat_send_note_120_300,
                SUM(chat_send_note_300) AS chat_send_note_300,
                SUM(IF(chat_send_note_negative>0,1,0)) AS chat_send_note_negative_uu,
                SUM(IF(chat_send_note_zero>0,1,0)) AS chat_send_note_zero_uu,
                SUM(IF(chat_send_note_1_30>0,1,0)) AS chat_send_note_1_30_uu,
                SUM(IF(chat_send_note_30_60>0,1,0)) AS chat_send_note_30_60_uu,
                SUM(IF(chat_send_note_60_120>0,1,0)) AS chat_send_note_60_120_uu,
                SUM(IF(chat_send_note_120_300>0,1,0)) AS chat_send_note_120_300_uu,
                SUM(IF(chat_send_note_300>0,1,0)) AS chat_send_note_300_uu,

                FROM
                `sc-analytics.report_growth.friends_feed_chat_user_level_2*`
                WHERE concat('2',_table_suffix) between '{start_date}' and '{end_date}'

                 group by 1,2
            """.format(
           start_date=start_date,
           end_date=end_date)
        return sql
    
    mt = MetricTable(
        sql=None,
        metrics=[
        chat_send_note_zero,
        chat_send_note_1_30,
        chat_send_note_30_60,
        chat_send_note_60_120,
        chat_send_note_120_300,
        chat_send_note_300,
        chat_send_note_negative_uu,
        chat_send_note_zero_uu,
        chat_send_note_1_30_uu,
        chat_send_note_30_60_uu,
        chat_send_note_60_120_uu,
        chat_send_note_120_300_uu,
        chat_send_note_300_uu
        ],
        name="voice_notes_other",
        inner_join_with_mapping=False,
        bq_dialect="standard",
        sql_callable=get_voice_notes,
        aa=True
    )
    return mt




def non_friend_metrics(start_date, end_date):
#     start_date = pd.to_datetime(start_date).strftime('%Y%m%d')[1:]
#     end_date = pd.to_datetime(end_date).strftime('%Y%m%d')[1:]
    start_date, end_date = start_date[-6:], end_date[-6:]

    chat_chat_send=Metric(col="chat_chat_send", dist="cont", daily=True, cumulative=True)
    chat_send_none_friend=Metric(col="chat_send_none_friend", dist="cont", daily=True, cumulative=True)
    chat_send_mutual_friend=Metric(col="chat_send_mutual_friend", dist="cont", daily=True, cumulative=True)
    chat_send_blocked_friend=Metric(col="chat_send_blocked_friend", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    chat_send_following_friend=Metric(col="chat_send_following_friend", dist="cont", daily=True, cumulative=True)
    chat_send_followed_friend=Metric(col="chat_send_followed_friend", dist="cont", daily=True, cumulative=True)
    chat_chat_send_uu=Metric(col="chat_chat_send_uu", dist="cont", daily=True, cumulative=True)
    chat_send_none_friend_uu=Metric(col="chat_send_none_friend_uu", dist="cont", daily=True, cumulative=True)
    chat_send_mutual_friend_uu=Metric(col="chat_send_mutual_friend_uu", dist="cont", daily=True, cumulative=True)
    chat_send_blocked_friend_uu=Metric(col="chat_send_blocked_friend_uu", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    chat_send_following_friend_uu=Metric(col="chat_send_following_friend_uu", dist="cont", daily=True, cumulative=True)
    chat_send_followed_friend_uu=Metric(col="chat_send_followed_friend_uu", dist="cont", daily=True, cumulative=True)
    chat_chat_view=Metric(col="chat_chat_view", dist="cont", daily=True, cumulative=True)
    chat_view_none_friend=Metric(col="chat_view_none_friend", dist="cont", daily=True, cumulative=True)
    chat_view_mutual_friend=Metric(col="chat_view_mutual_friend", dist="cont", daily=True, cumulative=True)
    chat_view_blocked_friend=Metric(col="chat_view_blocked_friend", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    chat_view_following_friend=Metric(col="chat_view_following_friend", dist="cont", daily=True, cumulative=True)
    chat_view_followed_friend=Metric(col="chat_view_followed_friend", dist="cont", daily=True, cumulative=True)
    chat_chat_view_uu=Metric(col="chat_chat_view_uu", dist="cont", daily=True, cumulative=True)
    chat_view_none_friend_uu=Metric(col="chat_view_none_friend_uu", dist="cont", daily=True, cumulative=True)
    chat_view_mutual_friend_uu=Metric(col="chat_view_mutual_friend_uu", dist="cont", daily=True, cumulative=True)
    chat_view_blocked_friend_uu=Metric(col="chat_view_blocked_friend_uu", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    chat_view_following_friend_uu=Metric(col="chat_view_following_friend_uu", dist="cont", daily=True, cumulative=True)
    chat_view_followed_friend_uu=Metric(col="chat_view_followed_friend_uu", dist="cont", daily=True, cumulative=True)
    chat_chat_create=Metric(col="chat_chat_create", dist="cont", daily=True, cumulative=True)
    chat_create_none_friend=Metric(col="chat_create_none_friend", dist="cont", daily=True, cumulative=True)
    chat_create_mutual_friend=Metric(col="chat_create_mutual_friend", dist="cont", daily=True, cumulative=True)
    chat_create_blocked_friend=Metric(col="chat_create_blocked_friend", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    chat_create_following_friend=Metric(col="chat_create_following_friend", dist="cont", daily=True, cumulative=True)
    chat_create_followed_friend=Metric(col="chat_create_followed_friend", dist="cont", daily=True, cumulative=True)
    chat_chat_create_uu=Metric(col="chat_chat_create_uu", dist="cont", daily=True, cumulative=True)
    chat_create_none_friend_uu=Metric(col="chat_create_none_friend_uu", dist="cont", daily=True, cumulative=True)
    chat_create_mutual_friend_uu=Metric(col="chat_create_mutual_friend_uu", dist="cont", daily=True, cumulative=True)
    chat_create_blocked_friend_uu=Metric(col="chat_create_blocked_friend_uu", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    chat_create_following_friend_uu=Metric(col="chat_create_following_friend_uu", dist="cont", daily=True, cumulative=True)
    chat_create_followed_friend_uu=Metric(col="chat_create_followed_friend_uu", dist="cont", daily=True, cumulative=True)
    blocking_action=Metric(col="blocking_action", dist="bin", daily=True, cumulative=True,desired_direction=NEGATIVE)
    blocking_action_uu=Metric(col="blocking_action_uu", dist="bin", daily=True, cumulative=True,desired_direction=NEGATIVE)
    
    blocking_action_for_friends_only_privacy_user=Metric(col="blocking_action_for_friends_only_privacy_user", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    blocking_action_for_everyone_privacy_user=Metric(col="blocking_action_for_everyone_privacy_user", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    blocking_action_for_contact_book_privacy_user=Metric(col="blocking_action_for_contact_book_privacy_user", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    blocking_action_for_friends_only_privacy_user_uu=Metric(col="blocking_action_for_friends_only_privacy_user_uu", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    blocking_action_for_everyone_privacy_user_uu=Metric(col="blocking_action_for_everyone_privacy_user_uu", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    blocking_action_for_contact_book_privacy_user_uu=Metric(col="blocking_action_for_contact_book_privacy_user_uu", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    
    reporting_action=Metric(col="reporting_action", dist="bin", daily=True, cumulative=True,desired_direction=NEGATIVE)
    reporting_action_uu=Metric(col="reporting_action_uu", dist="bin", daily=True, cumulative=True,desired_direction=NEGATIVE)
    
    reporting_action_for_friends_only_privacy_user=Metric(col="reporting_action_for_friends_only_privacy_user", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    reporting_action_for_everyone_privacy_user=Metric(col="reporting_action_for_everyone_privacy_user", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    reporting_action_for_contact_book_privacy_user=Metric(col="reporting_action_for_contact_book_privacy_user", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    reporting_action_for_friends_only_privacy_user_uu=Metric(col="reporting_action_for_friends_only_privacy_user_uu", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    reporting_action_for_everyone_privacy_user_uu=Metric(col="reporting_action_for_everyone_privacy_user_uu", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    reporting_action_for_contact_book_privacy_user_uu=Metric(col="reporting_action_for_contact_book_privacy_user_uu", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    
    delete_action=Metric(col="delete_action", dist="bin", daily=True, cumulative=True,desired_direction=NEGATIVE)
    delete_action_uu=Metric(col="delete_action_uu", dist="bin", daily=True, cumulative=True,desired_direction=NEGATIVE)
    
    delete_action_for_friends_only_privacy_user=Metric(col="delete_action_for_friends_only_privacy_user", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    delete_action_for_everyone_privacy_user=Metric(col="delete_action_for_everyone_privacy_user", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    delete_action_for_contact_book_privacy_user=Metric(col="delete_action_for_contact_book_privacy_user", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    delete_action_for_friends_only_privacy_user_uu=Metric(col="delete_action_for_friends_only_privacy_user_uu", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    delete_action_for_everyone_privacy_user_uu=Metric(col="delete_action_for_everyone_privacy_user_uu", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    delete_action_for_contact_book_privacy_user_uu=Metric(col="delete_action_for_contact_book_privacy_user_uu", dist="cont", daily=True, cumulative=True,desired_direction=NEGATIVE)
    
    snap_privacy_everyone_uu=Metric(col="snap_privacy_everyone_uu", dist="bin", daily=True, cumulative=True)
    snap_privacy_friends_uu=Metric(col="snap_privacy_friends_uu", dist="bin", daily=True, cumulative=True)
    snap_privacy_default_uu=Metric(col="snap_privacy_default_uu", dist="bin", daily=True, cumulative=True)
    snap_privacy_contact_book_uu=Metric(col="snap_privacy_contact_book_uu", dist="bin", daily=True, cumulative=True)
    friends_to_everyone_uu=Metric(col="friends_to_everyone_uu", dist="bin", daily=True, cumulative=True)
    everyone_to_friends_uu=Metric(col="everyone_to_friends_uu", dist="bin", daily=True, cumulative=True)

    sql="""
            WITH MESSAGING AS 
            (
              SELECT 
                TIMESTAMP(DATE(event_date)) AS ts,
                ghost_user_id,
                SUM(CHAT_CHAT_SEND) chat_chat_send,
                SUM(chat_send_none_friend) chat_send_none_friend,
                SUM(chat_send_mutual) chat_send_mutual_friend,
                SUM(chat_send_blocked) chat_send_blocked_friend,
                SUM(chat_send_following) chat_send_following_friend,    
                SUM(chat_send_followed) chat_send_followed_friend,

                MAX(CHAT_CHAT_SEND_UU) chat_chat_send_uu,
                MAX(chat_send_none_friend_uu) chat_send_none_friend_uu,
                MAX(chat_send_mutual_uu) chat_send_mutual_friend_uu,
                MAX(chat_send_blocked_uu) chat_send_blocked_friend_uu,
                MAX(chat_send_following_uu) chat_send_following_friend_uu,    
                MAX(chat_send_followed_uu) chat_send_followed_friend_uu,

                SUM(CHAT_CHAT_VIEW) chat_chat_view,
                SUM(chat_view_none_friend) chat_view_none_friend,
                SUM(chat_view_mutual) chat_view_mutual_friend,
                SUM(chat_view_blocked) chat_view_blocked_friend,
                SUM(chat_view_following) chat_view_following_friend,    
                SUM(chat_view_followed) chat_view_followed_friend,  

                MAX(CHAT_CHAT_VIEW_UU) chat_chat_view_uu,
                MAX(chat_view_none_friend_uu) chat_view_none_friend_uu,
                MAX(chat_view_mutual_uu) chat_view_mutual_friend_uu,
                MAX(chat_view_blocked_uu) chat_view_blocked_friend_uu,
                MAX(chat_view_following_uu) chat_view_following_friend_uu,    
                MAX(chat_view_followed_uu) chat_view_followed_friend_uu,

                SUM(CHAT_CHAT_CREATE) chat_chat_create,
                SUM(chat_create_none_friend) chat_create_none_friend,
                SUM(chat_create_mutual_friend) chat_create_mutual_friend,
                SUM(chat_create_blocked_friend) chat_create_blocked_friend,
                SUM(chat_create_following_friend) chat_create_following_friend,    
                SUM(chat_create_followed_friend) chat_create_followed_friend,

                MAX(CHAT_CHAT_CREATE_UU) chat_chat_create_uu,
                MAX(chat_create_none_friend_uu) chat_create_none_friend_uu,
                MAX(chat_create_mutual_friend_uu) chat_create_mutual_friend_uu,
                MAX(chat_create_blocked_friend_uu) chat_create_blocked_friend_uu,
                MAX(chat_create_following_friend_uu) chat_create_following_friend_uu,    
                MAX(chat_create_followed_friend_uu) chat_create_followed_friend_uu                                               

              FROM
              `sc-analytics.report_growth.friends_feed_chat_user_level_20*`
                WHERE _table_suffix between '{start_date}' and '{end_date}'
                GROUP BY 1, 2
            ),

            FRIENDING AS 
            (
              SELECT 
                ts,
                ghost_user_id,
                SUM(blocking_action) blocking_action,
                MAX(blocking_action_uu) blocking_action_uu,
                SUM(reporting_action) reporting_action,
                MAX(reporting_action_uu) reporting_action_uu,
                SUM(delete_action) delete_action,
                MAX(delete_action_uu) delete_action_uu

              FROM 
                `sc-analytics.report_growth.chatv3_user_friending_activity_20*`
                WHERE _table_suffix between '{start_date}' and '{end_date}'
                GROUP BY 1, 2
            ),
            PRIVACY AS
            (SELECT    
                ts,
                ghost_user_id,
                COUNTIF(snap_privacy_curr='EVERYONE') snap_privacy_everyone_uu,
                COUNTIF(snap_privacy_curr='FRIENDS') snap_privacy_friends_uu,
                COUNTIF(snap_privacy_curr is null) snap_privacy_default_uu,
                COUNTIF(contact_book_enabled_curr is TRUE) contact_book_enabled_uu,
                MAX(FRIENDS_TO_EVERYONE_uu)  friends_to_everyone_uu,
                MAX(EVERYONE_TO_FRIENDS_uu) everyone_to_friends_uu

              FROM 
                `sc-analytics.report_growth.chatv3_user_privacy_setting_activity_20*`
                WHERE _table_suffix between '{start_date}' and '{end_date}'
                GROUP BY 1, 2
            )
            SELECT 
                M.ts,
                M.ghost_user_id,
                chat_chat_send,
                chat_send_none_friend,
                chat_send_mutual_friend,
                chat_send_blocked_friend,
                chat_send_following_friend,    
                chat_send_followed_friend,

                chat_chat_send_uu,
                chat_send_none_friend_uu,
                chat_send_mutual_friend_uu,
                chat_send_blocked_friend_uu,
                chat_send_following_friend_uu,    
                chat_send_followed_friend_uu,

                chat_chat_view,
                chat_view_none_friend,
                chat_view_mutual_friend,
                chat_view_blocked_friend,
                chat_view_following_friend,    
                chat_view_followed_friend,  

                chat_chat_view_uu,
                chat_view_none_friend_uu,
                chat_view_mutual_friend_uu,
                chat_view_blocked_friend_uu,
                chat_view_following_friend_uu,    
                chat_view_followed_friend_uu,

                chat_chat_create,
                chat_create_none_friend,
                chat_create_mutual_friend,
                chat_create_blocked_friend,
                chat_create_following_friend,    
                chat_create_followed_friend,

                chat_chat_create_uu,
                chat_create_none_friend_uu,
                chat_create_mutual_friend_uu,
                chat_create_blocked_friend_uu,
                chat_create_following_friend_uu,    
                chat_create_followed_friend_uu,                                               

                COALESCE(blocking_action,0) blocking_action,
                COALESCE(blocking_action_uu,0) blocking_action_uu,
                COALESCE(IF((snap_privacy_friends_uu)>0, blocking_action, 0),0) blocking_action_for_friends_only_privacy_user,
                COALESCE(IF((snap_privacy_everyone_uu)>0, blocking_action, 0),0) blocking_action_for_everyone_privacy_user,
                COALESCE(IF((contact_book_enabled_uu)>0, blocking_action, 0),0) blocking_action_for_contact_book_privacy_user,
                COALESCE(IF((snap_privacy_friends_uu)>0, blocking_action_uu, 0),0) blocking_action_for_friends_only_privacy_user_uu,
                COALESCE(IF((snap_privacy_everyone_uu)>0, blocking_action_uu, 0),0) blocking_action_for_everyone_privacy_user_uu,
                COALESCE(IF((contact_book_enabled_uu)>0, blocking_action_uu, 0),0) blocking_action_for_contact_book_privacy_user_uu,

                COALESCE(reporting_action,0) reporting_action,
                COALESCE(reporting_action_uu,0) reporting_action_uu,
                COALESCE(IF((snap_privacy_friends_uu)>0, reporting_action, 0),0) reporting_action_for_friends_only_privacy_user,
                COALESCE(IF((snap_privacy_everyone_uu)>0, reporting_action, 0),0) reporting_action_for_everyone_privacy_user,
                COALESCE(IF((contact_book_enabled_uu)>0, reporting_action, 0),0) reporting_action_for_contact_book_privacy_user,
                COALESCE(IF((snap_privacy_friends_uu)>0, reporting_action_uu, 0),0) reporting_action_for_friends_only_privacy_user_uu,
                COALESCE(IF((snap_privacy_everyone_uu)>0, reporting_action_uu, 0),0) reporting_action_for_everyone_privacy_user_uu,
                COALESCE(IF((contact_book_enabled_uu)>0, reporting_action_uu, 0),0) reporting_action_for_contact_book_privacy_user_uu,
                
                
                COALESCE(delete_action,0) delete_action,
                COALESCE(delete_action_uu,0) delete_action_uu,
                COALESCE(IF((snap_privacy_friends_uu)>0, delete_action, 0),0) delete_action_for_friends_only_privacy_user,
                COALESCE(IF((snap_privacy_everyone_uu)>0, delete_action, 0),0) delete_action_for_everyone_privacy_user,
                COALESCE(IF((contact_book_enabled_uu)>0, delete_action, 0),0) delete_action_for_contact_book_privacy_user,
                
                COALESCE(IF((snap_privacy_friends_uu)>0, delete_action_uu, 0),0) delete_action_for_friends_only_privacy_user_uu,
                COALESCE(IF((snap_privacy_everyone_uu)>0, delete_action_uu, 0),0) delete_action_for_everyone_privacy_user_uu,
                COALESCE(IF((contact_book_enabled_uu)>0, delete_action_uu, 0),0) delete_action_for_contact_book_privacy_user_uu,
                
                
                COALESCE(snap_privacy_everyone_uu,0) snap_privacy_everyone_uu,
                COALESCE(snap_privacy_friends_uu,0) snap_privacy_friends_uu,
                COALESCE(snap_privacy_default_uu,0) snap_privacy_default_uu,
                COALESCE(contact_book_enabled_uu,0) snap_privacy_contact_book_uu,
                COALESCE(friends_to_everyone_uu,0)  friends_to_everyone_uu,
                COALESCE(everyone_to_friends_uu,0) everyone_to_friends_uu
                
            FROM MESSAGING AS M LEFT JOIN FRIENDING AS F  
            ON M.ts=F.ts AND M.ghost_user_id=F.ghost_user_id
            LEFT JOIN PRIVACY AS P  
            ON M.ts=P.ts AND M.ghost_user_id=P.ghost_user_id
        """
    
    def sql_callable(start_date, end_date):
        start_date, end_date = start_date[-6:], end_date[-6:]
        return sql.format(start_date=start_date, end_date=end_date)
    
    mt = MetricTable(
        sql = sql.format(start_date=start_date, end_date=end_date),
        sql_callable = sql_callable,
        metrics=[
                chat_chat_send,
                chat_send_none_friend,
                chat_send_mutual_friend,
                chat_send_blocked_friend,
                chat_send_following_friend,    
                chat_send_followed_friend,
                chat_chat_send_uu,
                chat_send_none_friend_uu,
                chat_send_mutual_friend_uu,
                chat_send_blocked_friend_uu,
                chat_send_following_friend_uu,    
                chat_send_followed_friend_uu,
                chat_chat_view,
                chat_view_none_friend,
                chat_view_mutual_friend,
                chat_view_blocked_friend,
                chat_view_following_friend,    
                chat_view_followed_friend,  
                chat_chat_view_uu,
                chat_view_none_friend_uu,
                chat_view_mutual_friend_uu,
                chat_view_blocked_friend_uu,
                chat_view_following_friend_uu,    
                chat_view_followed_friend_uu,
                chat_chat_create,
                chat_create_none_friend,
                chat_create_mutual_friend,
                chat_create_blocked_friend,
                chat_create_following_friend,    
                chat_create_followed_friend,
                chat_chat_create_uu,
                chat_create_none_friend_uu,
                chat_create_mutual_friend_uu,
                chat_create_blocked_friend_uu,
                chat_create_following_friend_uu,    
                chat_create_followed_friend_uu,                                               
                blocking_action,

                blocking_action_uu,
                #blocking_action_for_nfm_user_uu,
                blocking_action_for_friends_only_privacy_user, 
                blocking_action_for_everyone_privacy_user,
                blocking_action_for_contact_book_privacy_user,
                blocking_action_for_friends_only_privacy_user_uu,
                blocking_action_for_everyone_privacy_user_uu,
                blocking_action_for_contact_book_privacy_user_uu,
            
                reporting_action,
                #reporting_action_for_nfm_user,
                reporting_action_uu,
                #reporting_action_for_nfm_user_uu,
                reporting_action_for_friends_only_privacy_user, 
                reporting_action_for_everyone_privacy_user,
                reporting_action_for_contact_book_privacy_user,
                reporting_action_for_friends_only_privacy_user_uu,
                reporting_action_for_everyone_privacy_user_uu,
                reporting_action_for_contact_book_privacy_user_uu,
            
                delete_action,
                delete_action_uu,
                delete_action_for_friends_only_privacy_user, 
                delete_action_for_everyone_privacy_user,
                delete_action_for_contact_book_privacy_user,
                delete_action_for_friends_only_privacy_user_uu,
                delete_action_for_everyone_privacy_user_uu,
                delete_action_for_contact_book_privacy_user_uu,
            
                snap_privacy_everyone_uu,
                #snap_privacy_everyone_for_nfm_user_uu,
                snap_privacy_friends_uu,
                #snap_privacy_friends_for_nfm_user_uu,
                snap_privacy_default_uu,
                #snap_privacy_default_for_nfm_user_uu,
                snap_privacy_contact_book_uu,
                friends_to_everyone_uu,
                #friends_to_everyone_for_nfm_user_uu,
                everyone_to_friends_uu,
                #everyone_to_friends_for_nfm_user_uu
                ],
        name="non_friend_metrics",
        inner_join_with_mapping=False,
        bq_dialect="standard"
    )
    return mt



def chat_erase_mode_metrics(start_date, end_date):
#     start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
#     end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    start_date, end_date = start_date[-6:], end_date[-6:]

    chat_erase_mode_update_hour24=Metric(col="chat_erase_mode_update_hour24", dist="cont", daily=True, cumulative=True)
    chat_erase_mode_update_hour24_migration=Metric(col="chat_erase_mode_update_hour24_migration", dist="cont", daily=True, cumulative=True)
    chat_erase_mode_update_hour24_by_user=Metric(col="chat_erase_mode_update_hour24_by_user", dist="cont", daily=True, cumulative=True)
    chat_erase_mode_update_immediate=Metric(col="chat_erase_mode_update_immediate", dist="cont", daily=True, cumulative=True)
    chat_erase_mode_update_day7=Metric(col="chat_erase_mode_update_day7", dist="cont", daily=True, cumulative=True)
    chat_erase_mode_update_day31=Metric(col="chat_erase_mode_update_day31", dist="cont", daily=True, cumulative=True)
    chat_erase_mode_update_infinite=Metric(col="chat_erase_mode_update_infinite", dist="cont", daily=True, cumulative=True)
    chat_erase_mode_update_toggle_by_user=Metric(col="chat_erase_mode_update_toggle_by_user", dist="cont", daily=True, cumulative=True)
    chat_chat_send_hour24=Metric(col="chat_chat_send_hour24", dist="cont", daily=True, cumulative=True)
    chat_ind_send_hour24=Metric(col="chat_ind_send_hour24", dist="cont", daily=True, cumulative=True)
    chat_group_send_hour24=Metric(col="chat_group_send_hour24", dist="cont", daily=True, cumulative=True)
    chat_chat_send_immediate=Metric(col="chat_chat_send_immediate", dist="cont", daily=True, cumulative=True)
    chat_ind_send_immediate=Metric(col="chat_ind_send_immediate", dist="cont", daily=True, cumulative=True)
    chat_group_send_immediate=Metric(col="chat_group_send_immediate", dist="cont", daily=True, cumulative=True)
    chat_chat_send_day7=Metric(col="chat_chat_send_day7", dist="cont", daily=True, cumulative=True)
    chat_ind_send_day7=Metric(col="chat_ind_send_day7", dist="cont", daily=True, cumulative=True)
    chat_group_send_day7=Metric(col="chat_group_send_day7", dist="cont", daily=True, cumulative=True)
    chat_chat_send_day31=Metric(col="chat_chat_send_day31", dist="cont", daily=True, cumulative=True)
    chat_ind_send_day31=Metric(col="chat_ind_send_day31", dist="cont", daily=True, cumulative=True)
    chat_group_send_day31=Metric(col="chat_group_send_day31", dist="cont", daily=True, cumulative=True)
    chat_chat_send_infinite=Metric(col="chat_chat_send_infinite", dist="cont", daily=True, cumulative=True)
    chat_ind_send_infinite=Metric(col="chat_ind_send_infinite", dist="cont", daily=True, cumulative=True)
    chat_group_send_infinite=Metric(col="chat_group_send_infinite", dist="cont", daily=True, cumulative=True)
    chat_chat_view_hour24=Metric(col="chat_chat_view_hour24", dist="cont", daily=True, cumulative=True)
    chat_ind_view_hour24=Metric(col="chat_ind_view_hour24", dist="cont", daily=True, cumulative=True)
    chat_group_view_hour24=Metric(col="chat_group_view_hour24", dist="cont", daily=True, cumulative=True)
    chat_chat_view_immediate=Metric(col="chat_chat_view_immediate", dist="cont", daily=True, cumulative=True)
    chat_ind_view_immediate=Metric(col="chat_ind_view_immediate", dist="cont", daily=True, cumulative=True)
    chat_group_view_immediate=Metric(col="chat_group_view_immediate", dist="cont", daily=True, cumulative=True)
    chat_chat_view_day7=Metric(col="chat_chat_view_day7", dist="cont", daily=True, cumulative=True)
    chat_ind_view_day7=Metric(col="chat_ind_view_day7", dist="cont", daily=True, cumulative=True)
    chat_group_view_day7=Metric(col="chat_group_view_day7", dist="cont", daily=True, cumulative=True)
    chat_chat_view_day31=Metric(col="chat_chat_view_day31", dist="cont", daily=True, cumulative=True)
    chat_ind_view_day31=Metric(col="chat_ind_view_day31", dist="cont", daily=True, cumulative=True)
    chat_group_view_day31=Metric(col="chat_group_view_day31", dist="cont", daily=True, cumulative=True)
    chat_chat_view_infinite=Metric(col="chat_chat_view_infinite", dist="cont", daily=True, cumulative=True)
    chat_ind_view_infinite=Metric(col="chat_ind_view_infinite", dist="cont", daily=True, cumulative=True)
    chat_group_view_infinite=Metric(col="chat_group_view_infinite", dist="cont", daily=True, cumulative=True)
    chat_chat_save_hour24=Metric(col="chat_chat_save_hour24", dist="cont", daily=True, cumulative=True)
    chat_chat_save_immediate=Metric(col="chat_chat_save_immediate", dist="cont", daily=True, cumulative=True)
    chat_chat_save_day7=Metric(col="chat_chat_save_day7", dist="cont", daily=True, cumulative=True)
    chat_chat_save_day31=Metric(col="chat_chat_save_day31", dist="cont", daily=True, cumulative=True)
    chat_chat_save_infinite=Metric(col="chat_chat_save_infinite", dist="cont", daily=True, cumulative=True)
    hour24_relationship_count=Metric(col="hour24_relationship_count", dist="cont", daily=True, cumulative=True)
    immediate_relationship_count=Metric(col="immediate_relationship_count", dist="cont", daily=True, cumulative=True)
    infinite_relationship_count=Metric(col="infinite_relationship_count", dist="cont", daily=True, cumulative=True)
    immediate_to_hour24_update_incremental_over_a_day=Metric(col="immediate_to_hour24_update_incremental_over_a_day", dist="cont", daily=True, cumulative=True)
    hour24_to_immediate_update_incremental_over_a_day=Metric(col="hour24_to_immediate_update_incremental_over_a_day", dist="cont", daily=True, cumulative=True) 
    hour24_to_infinite_update_incremental_over_a_day=Metric(col="hour24_to_infinite_update_incremental_over_a_day", dist="cont", daily=True, cumulative=True)
    immediate_to_infinite_update_incremental_over_a_day=Metric(col="immediate_to_infinite_update_incremental_over_a_day", dist="cont", daily=True, cumulative=True)
    infinite_to_hour24_update_incremental_over_a_day=Metric(col="infinite_to_hour24_update_incremental_over_a_day", dist="cont", daily=True, cumulative=True)
    infinite_to_immediate_update_incremental_over_a_day=Metric(col="infinite_to_immediate_update_incremental_over_a_day", dist="cont", daily=True, cumulative=True)

    chat_chat_save_hour24_snap=Metric(col="chat_chat_save_hour24_snap", dist="cont", daily=True, cumulative=True) 
    chat_chat_save_immediate_snap=Metric(col="chat_chat_save_immediate_snap", dist="cont", daily=True, cumulative=True) 
    chat_chat_unsave_hour24_snap=Metric(col="chat_chat_unsave_hour24_snap", dist="cont", daily=True, cumulative=True) 
    chat_chat_unsave_immediate_snap=Metric(col="chat_chat_unsave_immediate_snap", dist="cont", daily=True, cumulative=True) 
    chat_chat_erase_hour24_snap=Metric(col="chat_chat_erase_hour24_snap", dist="cont", daily=True, cumulative=True) 
    chat_chat_erase_immediate_snap=Metric(col="chat_chat_erase_immediate_snap", dist="cont", daily=True, cumulative=True) 
    
    snap_erase_mode_update_hour24=Metric(col="snap_erase_mode_update_hour24", dist="cont", daily=True, cumulative=True) 
    snap_erase_mode_update_immediate=Metric(col="snap_erase_mode_update_immediate", dist="cont", daily=True, cumulative=True) 
    snap_erase_mode_update_hour24_chat_settings=Metric(col="snap_erase_mode_update_hour24_chat_settings", dist="cont", daily=True, cumulative=True) 
    snap_erase_mode_update_hour24_chat_toggle_prompt=Metric(col="snap_erase_mode_update_hour24_chat_toggle_prompt", dist="cont", daily=True, cumulative=True) 
    snap_erase_mode_update_hour24_status_message=Metric(col="snap_erase_mode_update_hour24_status_message", dist="cont", daily=True, cumulative=True) 
    chat_snap_view_hour24=Metric(col="chat_snap_view_hour24", dist="cont", daily=True, cumulative=True) 
    chat_snap_view_immediate=Metric(col="chat_snap_view_immediate", dist="cont", daily=True, cumulative=True) 
    direct_snap_send_immediate=Metric(col="direct_snap_send_immediate", dist="cont", daily=True, cumulative=True) 
    direct_snap_send_hour24=Metric(col="direct_snap_send_hour24", dist="cont", daily=True, cumulative=True) 
    direct_snap_view_hour24=Metric(col="direct_snap_view_hour24", dist="cont", daily=True, cumulative=True) 
    direct_snap_view_immediate=Metric(col="direct_snap_view_immediate", dist="cont", daily=True, cumulative=True) 

    sql="""
            WITH MESSAGING AS 
            (
              SELECT 
                TIMESTAMP(DATE(event_date)) AS ts,
                ghost_user_id,
                SUM(CHAT_ERASE_MODE_UPDATE_HOUR24) chat_erase_mode_update_hour24,
                SUM(CHAT_ERASE_MODE_UPDATE_IMMEDIATE) chat_erase_mode_update_immediate,
                SUM(CHAT_ERASE_MODE_UPDATE_DAY7) chat_erase_mode_update_day7,
                SUM(CHAT_ERASE_MODE_UPDATE_DAY31) chat_erase_mode_update_day31,
                SUM(CHAT_ERASE_MODE_UPDATE_INFINITE) chat_erase_mode_update_infinite,
                SUM(CHAT_ERASE_MODE_UPDATE_HOUR24_MIGRATION) chat_erase_mode_update_hour24_migration,
                SUM(CHAT_ERASE_MODE_UPDATE_HOUR24_BY_USER) chat_erase_mode_update_hour24_by_user,
                SUM(CHAT_CHAT_SAVE_HOUR24) chat_chat_save_hour24,
                SUM(CHAT_CHAT_SAVE_IMMEDIATE) chat_chat_save_immediate,
                SUM(CHAT_CHAT_SAVE_DAY7) chat_chat_save_day7,
                SUM(CHAT_CHAT_SAVE_DAY31) chat_chat_save_day31,
                SUM(CHAT_CHAT_SAVE_INFINITE) chat_chat_save_infinite,
                SUM(chat_chat_save_hour24_snap) chat_chat_save_hour24_snap,
                SUM(chat_chat_save_immediate_snap) chat_chat_save_immediate_snap,
                SUM(chat_chat_unsave_hour24_snap) chat_chat_unsave_hour24_snap,
                SUM(chat_chat_unsave_immediate_snap) chat_chat_unsave_immediate_snap,
                SUM(chat_chat_erase_hour24_snap) chat_chat_erase_hour24_snap,
                SUM(chat_chat_erase_immediate_snap) chat_chat_erase_immediate_snap,
                SUM(snap_erase_mode_update_hour24) snap_erase_mode_update_hour24,
                SUM(snap_erase_mode_update_immediate) snap_erase_mode_update_immediate,
                SUM(snap_erase_mode_update_hour24_chat_settings) snap_erase_mode_update_hour24_chat_settings,
                SUM(snap_erase_mode_update_hour24_chat_toggle_prompt) snap_erase_mode_update_hour24_chat_toggle_prompt,
                SUM(snap_erase_mode_update_hour24_status_message) snap_erase_mode_update_hour24_status_message,
                SUM(chat_snap_view_hour24) chat_snap_view_hour24,
                SUM(chat_snap_view_immediate) chat_snap_view_immediate,

              FROM 
              `sc-analytics.report_growth.friends_feed_chat_detail_user_level_20*`
                WHERE _table_suffix between '{start_date}' and '{end_date}'
                GROUP BY 1, 2
            ),
            CHAT_SEND_VIEW AS 
            (
              SELECT 
                TIMESTAMP(DATE(event_date)) AS ts,
                ghost_user_id,
                SUM(CHAT_CHAT_SEND_HOUR24) chat_chat_send_hour24, 
                SUM(CHAT_IND_SEND_HOUR24) chat_ind_send_hour24,
                SUM(CHAT_GROUP_SEND_HOUR24) chat_group_send_hour24,
                SUM(CHAT_CHAT_SEND_IMMEDIATE) chat_chat_send_immediate,
                SUM(CHAT_IND_SEND_IMMEDIATE) chat_ind_send_immediate,
                SUM(CHAT_GROUP_SEND_IMMEDIATE) chat_group_send_immediate,
                SUM(CHAT_CHAT_SEND_DAY7) chat_chat_send_day7,
                SUM(CHAT_IND_SEND_DAY7) chat_ind_send_day7,
                SUM(CHAT_GROUP_SEND_DAY7) chat_group_send_day7,
                SUM(CHAT_CHAT_SEND_DAY31) chat_chat_send_day31,
                SUM(CHAT_IND_SEND_DAY31) chat_ind_send_day31,
                SUM(CHAT_GROUP_SEND_DAY31) chat_group_send_day31,
                SUM(CHAT_CHAT_SEND_INFINITE) chat_chat_send_infinite,
                SUM(CHAT_IND_SEND_INFINITE) chat_ind_send_infinite,
                SUM(CHAT_GROUP_SEND_INFINITE) chat_group_send_infinite,
                SUM(CHAT_CHAT_VIEW_HOUR24) chat_chat_view_hour24,
                SUM(CHAT_IND_VIEW_HOUR24) chat_ind_view_hour24,
                SUM(CHAT_GROUP_VIEW_HOUR24) chat_group_view_hour24,
                SUM(CHAT_CHAT_VIEW_IMMEDIATE) chat_chat_view_immediate,
                SUM(CHAT_IND_VIEW_IMMEDIATE) chat_ind_view_immediate,
                SUM(CHAT_GROUP_VIEW_IMMEDIATE) chat_group_view_immediate,
                SUM(CHAT_CHAT_VIEW_DAY7) chat_chat_view_day7,
                SUM(CHAT_IND_VIEW_DAY7) chat_ind_view_day7,
                SUM(CHAT_GROUP_VIEW_DAY7) chat_group_view_day7,
                SUM(CHAT_CHAT_VIEW_DAY31) chat_chat_view_day31,
                SUM(CHAT_IND_VIEW_DAY31) chat_ind_view_day31,
                SUM(CHAT_GROUP_VIEW_DAY31) chat_group_view_day31,
                SUM(CHAT_CHAT_VIEW_INFINITE) chat_chat_view_infinite,
                SUM(CHAT_IND_VIEW_INFINITE) chat_ind_view_infinite,
                SUM(CHAT_GROUP_VIEW_INFINITE) chat_group_view_infinite,

              FROM 
              `sc-analytics.report_growth.friends_feed_chat_user_level_20*`
                WHERE _table_suffix between '{start_date}' and '{end_date}'
                GROUP BY 1, 2
            ),
            ERASE_MODE_DAILY_UPDATE AS
            (
              SELECT 
                ts,
                ghost_user_id,
                SUM(immediate_relationship_count) immediate_relationship_count,
                SUM(hour24_relationship_count) hour24_relationship_count,
                SUM(infinite_relationship_count) infinite_relationship_count,
                SUM(IMMEDIATE_TO_HOUR24) immediate_to_hour24_update, 
                SUM(HOUR24_TO_IMMEDIATE) hour24_to_immediate_update,
                SUM(HOUR24_TO_INFINITE) hour24_to_infinite_update,
                SUM(IMMEDIATE_TO_INFINITE) immediate_to_infinite_update,
                SUM(INFINITE_TO_HOUR24) infinite_to_hour24_update,
                SUM(INFINITE_TO_IMMEDIATE) infinite_to_immediate_update

              FROM 
                `sc-analytics.report_growth.chatv3_user_erase_mode_setting_activity_20*`
                WHERE _table_suffix between '{start_date}' and '{end_date}'
                GROUP BY 1, 2
            ),

            SNAP AS 
            (
              SELECT 
                TIMESTAMP(DATE(event_date)) AS ts,
                ghost_user_id,
                SUM(direct_snap_send_immediate) direct_snap_send_immediate,
                SUM(direct_snap_send_hour24) direct_snap_send_hour24,
                SUM(direct_snap_view_hour24) direct_snap_view_hour24,
                SUM(direct_snap_view_immediate) direct_snap_view_immediate

              FROM 
                `sc-analytics.report_growth.friends_feed_snap_user_level_20*`
                WHERE _table_suffix between '{start_date}' and '{end_date}'
                GROUP BY 1, 2
            )

            SELECT 
                M.ts,
                M.ghost_user_id,
                chat_erase_mode_update_hour24,
                chat_erase_mode_update_hour24_migration,
                chat_erase_mode_update_hour24_by_user,
                chat_erase_mode_update_immediate,
                chat_erase_mode_update_day7,
                chat_erase_mode_update_day31,
                chat_erase_mode_update_infinite,
                chat_erase_mode_update_hour24_by_user + chat_erase_mode_update_immediate + chat_erase_mode_update_day7 + chat_erase_mode_update_day31 + chat_erase_mode_update_infinite as chat_erase_mode_update_toggle_by_user,
                chat_chat_save_hour24,
                chat_chat_save_immediate,
                chat_chat_save_day7,
                chat_chat_save_day31,
                chat_chat_save_infinite, 
                
                COALESCE(chat_chat_send_hour24,0) chat_chat_send_hour24, 
                COALESCE(chat_ind_send_hour24,0) chat_ind_send_hour24, 
                COALESCE(chat_group_send_hour24,0) chat_group_send_hour24, 

                COALESCE(chat_chat_send_immediate,0) chat_chat_send_immediate,
                COALESCE(chat_ind_send_immediate,0) chat_ind_send_immediate, 
                COALESCE(chat_group_send_immediate,0) chat_group_send_immediate, 

                COALESCE(chat_chat_send_day7,0) chat_chat_send_day7,
                COALESCE(chat_ind_send_day7,0) chat_ind_send_day7, 
                COALESCE(chat_group_send_day7,0) chat_group_send_day7, 

                COALESCE(chat_chat_send_day31,0) chat_chat_send_day31,
                COALESCE(chat_ind_send_day31,0) chat_ind_send_day31, 
                COALESCE(chat_group_send_day31,0) chat_group_send_day31, 

                COALESCE(chat_chat_send_infinite,0) chat_chat_send_infinite,
                COALESCE(chat_ind_send_infinite,0) chat_ind_send_infinite, 
                COALESCE(chat_group_send_infinite,0) chat_group_send_infinite, 

                COALESCE(chat_chat_view_hour24,0) chat_chat_view_hour24,
                COALESCE(chat_ind_view_hour24,0) chat_ind_view_hour24, 
                COALESCE(chat_group_view_hour24,0) chat_group_view_hour24, 

                COALESCE(chat_chat_view_immediate,0) chat_chat_view_immediate,
                COALESCE(chat_ind_view_immediate,0) chat_ind_view_immediate, 
                COALESCE(chat_group_view_immediate,0) chat_group_view_immediate, 

                COALESCE(chat_chat_view_day7,0) chat_chat_view_day7,
                COALESCE(chat_ind_view_day7,0) chat_ind_view_day7, 
                COALESCE(chat_group_view_day7,0) chat_group_view_day7, 

                COALESCE(chat_chat_view_day31,0) chat_chat_view_day31,
                COALESCE(chat_ind_view_day31,0) chat_ind_view_day31, 
                COALESCE(chat_group_view_day31,0) chat_group_view_day31, 

                COALESCE(chat_chat_view_infinite,0) chat_chat_view_infinite,
                COALESCE(chat_ind_view_infinite,0) chat_ind_view_infinite, 
                COALESCE(chat_group_view_infinite,0) chat_group_view_infinite, 

                COALESCE(immediate_relationship_count,0) immediate_relationship_count,
                COALESCE(hour24_relationship_count,0) hour24_relationship_count,
                COALESCE(infinite_relationship_count,0) infinite_relationship_count,
                COALESCE(immediate_to_hour24_update,0) immediate_to_hour24_update_incremental_over_a_day,
                COALESCE(hour24_to_immediate_update,0) hour24_to_immediate_update_incremental_over_a_day,
                COALESCE(hour24_to_infinite_update,0) hour24_to_infinite_update_incremental_over_a_day,
                COALESCE(immediate_to_infinite_update,0) immediate_to_infinite_update_incremental_over_a_day,
                COALESCE(infinite_to_hour24_update,0) infinite_to_hour24_update_incremental_over_a_day,
                COALESCE(infinite_to_immediate_update,0) infinite_to_immediate_update_incremental_over_a_day,

                COALESCE(chat_chat_save_hour24_snap,0) chat_chat_save_hour24_snap,
                COALESCE(chat_chat_save_immediate_snap,0) chat_chat_save_immediate_snap,
                COALESCE(chat_chat_unsave_hour24_snap,0) chat_chat_unsave_hour24_snap,
                COALESCE(chat_chat_unsave_immediate_snap,0) chat_chat_unsave_immediate_snap,
                COALESCE(chat_chat_erase_hour24_snap,0) chat_chat_erase_hour24_snap,
                COALESCE(chat_chat_erase_immediate_snap,0) chat_chat_erase_immediate_snap,
                COALESCE(snap_erase_mode_update_hour24,0) snap_erase_mode_update_hour24,
                COALESCE(snap_erase_mode_update_immediate,0) snap_erase_mode_update_immediate,
                COALESCE(snap_erase_mode_update_hour24_chat_settings,0) snap_erase_mode_update_hour24_chat_settings,
                COALESCE(snap_erase_mode_update_hour24_chat_toggle_prompt,0) snap_erase_mode_update_hour24_chat_toggle_prompt,
                COALESCE(snap_erase_mode_update_hour24_status_message,0) snap_erase_mode_update_hour24_status_message,
                COALESCE(chat_snap_view_hour24,0) chat_snap_view_hour24,
                COALESCE(chat_snap_view_immediate,0) chat_snap_view_immediate,
                COALESCE(direct_snap_send_immediate,0) direct_snap_send_immediate,
                COALESCE(direct_snap_send_hour24,0) direct_snap_send_hour24,
                COALESCE(direct_snap_view_hour24,0) direct_snap_view_hour24,
                COALESCE(direct_snap_view_immediate,0) direct_snap_view_immediate
                
            FROM MESSAGING AS M 
            LEFT JOIN CHAT_SEND_VIEW 
            ON M.ts=CHAT_SEND_VIEW.ts AND M.ghost_user_id=CHAT_SEND_VIEW.ghost_user_id
            LEFT JOIN ERASE_MODE_DAILY_UPDATE AS E  
            ON M.ts=E.ts AND M.ghost_user_id=E.ghost_user_id
            LEFT JOIN SNAP ON M.ts=SNAP.ts AND M.ghost_user_id=SNAP.ghost_user_id
        """
    
    def sql_callable(start_date, end_date):
        start_date, end_date = start_date[-6:], end_date[-6:]
        return sql.format(start_date=start_date, end_date=end_date)
    
    mt = MetricTable(
        sql= sql.format(start_date=start_date, end_date=end_date),
        sql_callable = sql_callable,
        metrics=[
                chat_erase_mode_update_hour24,
                chat_erase_mode_update_hour24_migration,
                chat_erase_mode_update_hour24_by_user,
                chat_erase_mode_update_immediate,
                chat_erase_mode_update_day7,
                chat_erase_mode_update_day31,
                chat_erase_mode_update_infinite,
                chat_erase_mode_update_toggle_by_user,
                chat_chat_send_hour24, 
                chat_ind_send_hour24, 
                chat_group_send_hour24, 
                chat_chat_send_immediate,
                chat_ind_send_immediate,
                chat_group_send_immediate,
                chat_chat_send_day7,
                chat_ind_send_day7,
                chat_group_send_day7,
                chat_chat_send_day31,
                chat_ind_send_day31,
                chat_group_send_day31,
                chat_chat_send_infinite,
                chat_ind_send_infinite,
                chat_group_send_infinite,
                chat_chat_view_hour24,
                chat_ind_view_hour24,
                chat_group_view_hour24,
                chat_chat_view_immediate,
                chat_ind_view_immediate,
                chat_group_view_immediate,
                chat_chat_view_day7,
                chat_ind_view_day7,
                chat_group_view_day7,
                chat_chat_view_day31,
                chat_ind_view_day31,
                chat_group_view_day31,
                chat_chat_view_infinite,
                chat_ind_view_infinite,
                chat_group_view_infinite,
                chat_chat_save_hour24,
                chat_chat_save_immediate,
                chat_chat_save_day7,
                chat_chat_save_day31,
                chat_chat_save_infinite,    
                immediate_relationship_count,
                hour24_relationship_count,
                infinite_relationship_count,
                immediate_to_hour24_update_incremental_over_a_day,
                hour24_to_immediate_update_incremental_over_a_day,
                hour24_to_infinite_update_incremental_over_a_day,
                immediate_to_infinite_update_incremental_over_a_day,
                infinite_to_hour24_update_incremental_over_a_day,
                infinite_to_immediate_update_incremental_over_a_day,
                direct_snap_send_immediate,
                direct_snap_send_hour24,
                direct_snap_view_hour24,
                direct_snap_view_immediate,
                chat_snap_view_hour24,
                chat_snap_view_immediate,
                chat_chat_save_hour24_snap,
                chat_chat_save_immediate_snap,
                chat_chat_unsave_hour24_snap,
                chat_chat_unsave_immediate_snap,
                chat_chat_erase_hour24_snap,
                chat_chat_erase_immediate_snap,
                snap_erase_mode_update_hour24,
                snap_erase_mode_update_immediate,
                snap_erase_mode_update_hour24_chat_settings,
                snap_erase_mode_update_hour24_chat_toggle_prompt,
                snap_erase_mode_update_hour24_status_message
                ],
        name="chat_erase_mode_metrics",
        inner_join_with_mapping=False,
        bq_dialect="standard"
    )
    return mt



def friend_feed_shortcut_metrics(start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
#     start_date, end_date = start_date[-6:], end_date[-6:]

    ff_shortcuts_available_all=Metric(col="ff_shortcuts_available_all", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_available_all_uu=Metric(col="ff_shortcuts_available_all_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_available_unread_uu=Metric(col="ff_shortcuts_available_unread_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_available_unreplied_uu=Metric(col="ff_shortcuts_available_unreplied_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_available_story_uu=Metric(col="ff_shortcuts_available_story_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_available_new_friends_uu=Metric(col="ff_shortcuts_available_new_friends_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_available_groups_uu=Metric(col="ff_shortcuts_available_groups_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_available_nearby_uu=Metric(col="ff_shortcuts_available_nearby_uu", dist="cont", daily=True, cumulative=True)

    ff_shortcuts_available_recently_active_friends_uu=Metric(col="ff_shortcuts_available_recently_active_friends_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_available_my_ai_uu=Metric(col="ff_shortcuts_available_my_ai_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_available_call_log_uu=Metric(col="ff_shortcuts_available_call_log_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_available_sent_uu=Metric(col="ff_shortcuts_available_sent_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_available_best_friends_uu=Metric(col="ff_shortcuts_available_best_friends_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_available_streaks_friends_uu=Metric(col="ff_shortcuts_available_streaks_friends_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_available_birthdays_friends_uu=Metric(col="ff_shortcuts_available_birthdays_friends_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_available_contacts_friends_uu=Metric(col="ff_shortcuts_available_contacts_friends_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_available_custom_uu=Metric(col="ff_shortcuts_available_custom_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_available_communities_uu=Metric(col="ff_shortcuts_available_communities_uu", dist="cont", daily=True, cumulative=True)

    ff_shortcuts_selected_all=Metric(col="ff_shortcuts_selected_all", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_unread=Metric(col="ff_shortcuts_selected_unread", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_unreplied=Metric(col="ff_shortcuts_selected_unreplied", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_story=Metric(col="ff_shortcuts_selected_story", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_new_friends=Metric(col="ff_shortcuts_selected_new_friends", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_groups=Metric(col="ff_shortcuts_selected_groups", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_nearby=Metric(col="ff_shortcuts_selected_nearby", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_recently_active_friends=Metric(col="ff_shortcuts_selected_recently_active_friends", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_my_ai=Metric(col="ff_shortcuts_selected_my_ai", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_call_log=Metric(col="ff_shortcuts_selected_call_log", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_sent=Metric(col="ff_shortcuts_selected_sent", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_best_friends=Metric(col="ff_shortcuts_selected_best_friends", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_streaks_friends=Metric(col="ff_shortcuts_selected_streaks_friends", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_birthdays_friends=Metric(col="ff_shortcuts_selected_birthdays_friends", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_contacts_friends=Metric(col="ff_shortcuts_selected_contacts_friends", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_custom=Metric(col="ff_shortcuts_selected_custom", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_communities=Metric(col="ff_shortcuts_selected_communities", dist="cont", daily=True, cumulative=True)

    ff_shortcuts_selected_all_uu=Metric(col="ff_shortcuts_selected_all_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_unread_uu=Metric(col="ff_shortcuts_selected_unread_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_unreplied_uu=Metric(col="ff_shortcuts_selected_unreplied_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_story_uu=Metric(col="ff_shortcuts_selected_story_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_new_friends_uu=Metric(col="ff_shortcuts_selected_new_friends_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_groups_uu=Metric(col="ff_shortcuts_selected_groups_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_nearby_uu=Metric(col="ff_shortcuts_selected_nearby_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_recently_active_friends_uu=Metric(col="ff_shortcuts_selected_recently_active_friends_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_my_ai_uu=Metric(col="ff_shortcuts_selected_my_ai_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_call_log_uu=Metric(col="ff_shortcuts_selected_call_log_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_sent_uu=Metric(col="ff_shortcuts_selected_sent_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_best_friends_uu=Metric(col="ff_shortcuts_selected_best_friends_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_streaks_friends_uu=Metric(col="ff_shortcuts_selected_streaks_friends_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_birthdays_friends_uu=Metric(col="ff_shortcuts_selected_birthdays_friends_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_contacts_friends_uu=Metric(col="ff_shortcuts_selected_contacts_friends_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_custom_uu=Metric(col="ff_shortcuts_selected_custom_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_selected_communities_uu=Metric(col="ff_shortcuts_selected_communities_uu", dist="cont", daily=True, cumulative=True)

    ff_shortcut_batch_reply_camera_button_clicked=Metric(col="ff_shortcut_batch_reply_camera_button_clicked", dist="cont", daily=True, cumulative=True)

    ff_shortcut_num_cell_inventory_unread=Metric(col="ff_shortcut_num_cell_inventory_unread", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_Groups=Metric(col="ff_shortcut_num_cell_inventory_Groups", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_unreplied=Metric(col="ff_shortcut_num_cell_inventory_unreplied", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_stories=Metric(col="ff_shortcut_num_cell_inventory_stories", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_nearby=Metric(col="ff_shortcut_num_cell_inventory_nearby", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_new_friends=Metric(col="ff_shortcut_num_cell_inventory_new_friends", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_birthday=Metric(col="ff_shortcut_num_cell_inventory_birthday", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_custom=Metric(col="ff_shortcut_num_cell_inventory_custom", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_streak=Metric(col="ff_shortcut_num_cell_inventory_streak", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_myai=Metric(col="ff_shortcut_num_cell_inventory_myai", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_communities=Metric(col="ff_shortcut_num_cell_inventory_communities", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_best_friends=Metric(col="ff_shortcut_num_cell_inventory_best_friends", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_sent=Metric(col="ff_shortcut_num_cell_inventory_sent", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_call_log=Metric(col="ff_shortcut_num_cell_inventory_call_log", dist="cont", daily=True, cumulative=True)

    ff_shortcut_num_cell_inventory_unread_deduped=Metric(col="ff_shortcut_num_cell_inventory_unread_deduped", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_groups_deduped=Metric(col="ff_shortcut_num_cell_inventory_groups_deduped", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_unreplied_deduped=Metric(col="ff_shortcut_num_cell_inventory_unreplied_deduped", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_stories_deduped=Metric(col="ff_shortcut_num_cell_inventory_stories_deduped", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_nearby_deduped=Metric(col="ff_shortcut_num_cell_inventory_nearby_deduped", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_new_friends_deduped=Metric(col="ff_shortcut_num_cell_inventory_new_friends_deduped", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_birthday_deduped=Metric(col="ff_shortcut_num_cell_inventory_birthday_deduped", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_custom_deduped=Metric(col="ff_shortcut_num_cell_inventory_custom_deduped", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_streak_deduped=Metric(col="ff_shortcut_num_cell_inventory_streak_deduped", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_myai_deduped=Metric(col="ff_shortcut_num_cell_inventory_myai_deduped", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_communities_deduped=Metric(col="ff_shortcut_num_cell_inventory_communities_deduped", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_best_friends_deduped=Metric(col="ff_shortcut_num_cell_inventory_best_friends_deduped", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_sent_deduped=Metric(col="ff_shortcut_num_cell_inventory_sent_deduped", dist="cont", daily=True, cumulative=True)
    ff_shortcut_num_cell_inventory_call_log_deduped=Metric(col="ff_shortcut_num_cell_inventory_call_log_deduped", dist="cont", daily=True, cumulative=True)

#     ff_shortcut_page_sync_successful_unread=Metric(col="ff_shortcut_page_sync_successful_unread", dist="cont", daily=True, cumulative=True)
#     ff_shortcut_page_sync_successful_Groups=Metric(col="ff_shortcut_page_sync_successful_Groups", dist="cont", daily=True, cumulative=True)
#     ff_shortcut_page_sync_successful_Unreplied=Metric(col="ff_shortcut_page_sync_successful_Unreplied", dist="cont", daily=True, cumulative=True)
#     ff_shortcut_page_sync_successful_stories=Metric(col="ff_shortcut_page_sync_successful_stories", dist="cont", daily=True, cumulative=True)
#     ff_shortcut_page_sync_successful_Nearby=Metric(col="ff_shortcut_page_sync_successful_Nearby", dist="cont", daily=True, cumulative=True)
#     ff_shortcut_page_sync_successful_New_friends=Metric(col="ff_shortcut_page_sync_successful_New_friends", dist="cont", daily=True, cumulative=True)
#     ff_shortcut_page_sync_successful_birthday=Metric(col="ff_shortcut_page_sync_successful_birthday", dist="cont", daily=True, cumulative=True)

#     ff_shortcut_page_sync_unsuccessful_unread=Metric(col="ff_shortcut_page_sync_unsuccessful_unread", dist="cont", daily=True, cumulative=True)
#     ff_shortcut_page_sync_unsuccessful_Groups=Metric(col="ff_shortcut_page_sync_unsuccessful_Groups", dist="cont", daily=True, cumulative=True)
#     ff_shortcut_page_sync_unsuccessful_Unreplied=Metric(col="ff_shortcut_page_sync_unsuccessful_Unreplied", dist="cont", daily=True, cumulative=True)
#     ff_shortcut_page_sync_unsuccessful_stories=Metric(col="ff_shortcut_page_sync_unsuccessful_stories", dist="cont", daily=True, cumulative=True)
#     ff_shortcut_page_sync_unsuccessful_Nearby=Metric(col="ff_shortcut_page_sync_unsuccessful_Nearby", dist="cont", daily=True, cumulative=True)
#     ff_shortcut_page_sync_unsuccessful_New_friends=Metric(col="ff_shortcut_page_sync_unsuccessful_New_friends", dist="cont", daily=True, cumulative=True)
#     ff_shortcut_page_sync_unsuccessful_birthday=Metric(col="ff_shortcut_page_sync_unsuccessful_birthday", dist="cont", daily=True, cumulative=True)

    ff_shortcut_exit_unselect_shortcut=Metric(col="ff_shortcut_exit_unselect_shortcut", dist="cont", daily=True, cumulative=True)
    ff_shortcut_exit_exit_feed=Metric(col="ff_shortcut_exit_exit_feed", dist="cont", daily=True, cumulative=True)
    ff_shortcut_exit_tap_cell=Metric(col="ff_shortcut_exit_tap_cell", dist="cont", daily=True, cumulative=True)
    ff_shortcut_exit_double_tap_cell=Metric(col="ff_shortcut_exit_double_tap_cell", dist="cont", daily=True, cumulative=True)
    ff_shortcut_exit_tap_reply_button=Metric(col="ff_shortcut_exit_tap_reply_button", dist="cont", daily=True, cumulative=True)
    ff_shortcut_next_page_messaging_feed=Metric(col="ff_shortcut_next_page_messaging_feed", dist="cont", daily=True, cumulative=True)
    ff_shortcut_next_page_camera_or_view_finder=Metric(col="ff_shortcut_next_page_camera_or_view_finder", dist="cont", daily=True, cumulative=True)
    ff_shortcut_next_page_opera=Metric(col="ff_shortcut_next_page_opera", dist="cont", daily=True, cumulative=True)
    ff_shortcut_next_page_friendsfeed=Metric(col="ff_shortcut_next_page_friendsfeed", dist="cont", daily=True, cumulative=True)

    ff_shortcuts_session_community_group_chat=Metric(col="ff_shortcuts_session_community_group_chat", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_session_community_group_chat_uu=Metric(col="ff_shortcuts_session_community_group_chat_uu", dist="cont", daily=True, cumulative=True)
    sendto_shortcuts_session_community_group_chat=Metric(col="sendto_shortcuts_session_community_group_chat", dist="cont", daily=True, cumulative=True)
    sendto_shortcuts_session_community_group_chat_uu=Metric(col="sendto_shortcuts_session_community_group_chat_uu", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_community_group_chat=Metric(col="ff_shortcuts_community_group_chat", dist="cont", daily=True, cumulative=True)
    ff_shortcuts_community_group_chat_uu=Metric(col="ff_shortcuts_community_group_chat_uu", dist="cont", daily=True, cumulative=True)

    sql="""
            SELECT 
            ghost_user_id, 
            TIMESTAMP(PARSE_DATE('%Y%m%d', _table_suffix)) as ts, 

            sum(ff_shortcuts_available_all) ff_shortcuts_available_all,
            sum(ff_shortcuts_available_all_uu) ff_shortcuts_available_all_uu,
            sum(ff_shortcuts_available_unread_uu) AS  ff_shortcuts_available_unread_uu,
            sum(ff_shortcuts_available_unreplied_uu) AS  ff_shortcuts_available_unreplied_uu,
            sum(ff_shortcuts_available_story_uu) AS  ff_shortcuts_available_story_uu,
            sum(ff_shortcuts_available_new_friends_uu) AS  ff_shortcuts_available_new_friends_uu,
            sum(ff_shortcuts_available_groups_uu) AS  ff_shortcuts_available_groups_uu,
            sum(ff_shortcuts_available_nearby_uu) AS  ff_shortcuts_available_nearby_uu,
            sum(ff_shortcuts_available_recently_active_friends_uu) AS ff_shortcuts_available_recently_active_friends_uu,
            sum(ff_shortcuts_available_my_ai_uu) AS  ff_shortcuts_available_my_ai_uu,
            sum(ff_shortcuts_available_call_log_uu) AS  ff_shortcuts_available_call_log_uu,
            sum(ff_shortcuts_available_sent_uu) AS  ff_shortcuts_available_sent_uu,
            sum(ff_shortcuts_available_best_friends_uu) AS  ff_shortcuts_available_best_friends_uu,
            sum(ff_shortcuts_available_streaks_friends_uu) AS  ff_shortcuts_available_streaks_friends_uu,
            sum(ff_shortcuts_available_birthdays_friends_uu) AS  ff_shortcuts_available_birthdays_friends_uu,
            sum(ff_shortcuts_available_contacts_friends_uu) AS  ff_shortcuts_available_contacts_friends_uu,
            if(sum(ff_shortcuts_available_custom)>0,1,0) AS ff_shortcuts_available_custom_uu,
            sum(ff_shortcuts_available_communities_uu) AS  ff_shortcuts_available_communities_uu,

            sum(ff_shortcuts_selected_all) ff_shortcuts_selected_all,
            sum(ff_shortcuts_selected_unread) AS  ff_shortcuts_selected_unread,
            sum(ff_shortcuts_selected_unreplied) AS  ff_shortcuts_selected_unreplied,
            sum(ff_shortcuts_selected_story) AS  ff_shortcuts_selected_story,
            sum(ff_shortcuts_selected_new_friends) AS  ff_shortcuts_selected_new_friends,
            sum(ff_shortcuts_selected_groups) AS  ff_shortcuts_selected_groups,
            sum(ff_shortcuts_selected_nearby) AS  ff_shortcuts_selected_nearby,
            sum(ff_shortcuts_selected_recently_active_friends) AS  ff_shortcuts_selected_recently_active_friends,
            sum(ff_shortcuts_selected_my_ai) AS  ff_shortcuts_selected_my_ai,
            sum(ff_shortcuts_selected_call_log) AS  ff_shortcuts_selected_call_log,
            sum(ff_shortcuts_selected_sent) AS  ff_shortcuts_selected_sent,
            sum(ff_shortcuts_selected_best_friends) AS  ff_shortcuts_selected_best_friends,
            sum(ff_shortcuts_selected_streaks_friends) AS  ff_shortcuts_selected_streaks_friends,
            sum(ff_shortcuts_selected_birthdays_friends) AS  ff_shortcuts_selected_birthdays_friends,
            sum(ff_shortcuts_selected_contacts_friends) AS  ff_shortcuts_selected_contacts_friends,
            sum(ff_shortcuts_selected_custom) AS ff_shortcuts_selected_custom,
            sum(ff_shortcuts_selected_communities) AS ff_shortcuts_selected_communities,

            sum(ff_shortcuts_selected_all_uu) ff_shortcuts_selected_all_uu,
            sum(ff_shortcuts_selected_unread_uu) AS  ff_shortcuts_selected_unread_uu,
            sum(ff_shortcuts_selected_unreplied_uu) AS  ff_shortcuts_selected_unreplied_uu,
            sum(ff_shortcuts_selected_story_uu) AS  ff_shortcuts_selected_story_uu,
            sum(ff_shortcuts_selected_new_friends_uu) AS  ff_shortcuts_selected_new_friends_uu,
            sum(ff_shortcuts_selected_groups_uu) AS  ff_shortcuts_selected_groups_uu,
            sum(ff_shortcuts_selected_nearby_uu) AS  ff_shortcuts_selected_nearby_uu,
            sum(ff_shortcuts_selected_recently_active_friends_uu) AS  ff_shortcuts_selected_recently_active_friends_uu,
            sum(ff_shortcuts_selected_my_ai_uu) AS  ff_shortcuts_selected_my_ai_uu,
            sum(ff_shortcuts_selected_call_log_uu) AS  ff_shortcuts_selected_call_log_uu,
            sum(ff_shortcuts_selected_sent_uu) AS  ff_shortcuts_selected_sent_uu,
            sum(ff_shortcuts_selected_best_friends_uu) AS  ff_shortcuts_selected_best_friends_uu,
            sum(ff_shortcuts_selected_streaks_friends_uu) AS  ff_shortcuts_selected_streaks_friends_uu,
            sum(ff_shortcuts_selected_birthdays_friends_uu) AS  ff_shortcuts_selected_birthdays_friends_uu,
            sum(ff_shortcuts_selected_contacts_friends_uu) AS  ff_shortcuts_selected_contacts_friends_uu,
            if(sum(ff_shortcuts_selected_custom)>0,1,0) AS ff_shortcuts_selected_custom_uu,
            sum(ff_shortcuts_selected_communities_uu) AS  ff_shortcuts_selected_communities_uu,

            sum(ff_shortcut_batch_reply_camera_button_clicked) AS  ff_shortcut_batch_reply_camera_button_clicked,

            sum(ff_shortcut_num_cell_inventory_unread) AS  ff_shortcut_num_cell_inventory_unread,
            sum(ff_shortcut_num_cell_inventory_Groups) AS  ff_shortcut_num_cell_inventory_groups,
            sum(ff_shortcut_num_cell_inventory_Unreplied) AS  ff_shortcut_num_cell_inventory_unreplied,
            sum(ff_shortcut_num_cell_inventory_stories) AS  ff_shortcut_num_cell_inventory_stories,
            sum(ff_shortcut_num_cell_inventory_Nearby) AS  ff_shortcut_num_cell_inventory_nearby,
            sum(ff_shortcut_num_cell_inventory_New_friends) AS  ff_shortcut_num_cell_inventory_new_friends,
            sum(ff_shortcut_num_cell_inventory_birthday) AS  ff_shortcut_num_cell_inventory_birthday,
            sum(ff_shortcut_num_cell_inventory_CUSTOM) AS ff_shortcut_num_cell_inventory_custom,
            sum(ff_shortcut_num_cell_inventory_streak) AS ff_shortcut_num_cell_inventory_streak,
            sum(ff_shortcut_num_cell_inventory_MERLIN) AS ff_shortcut_num_cell_inventory_myai,
            sum(ff_shortcut_num_cell_inventory_COMMUNITIES) AS ff_shortcut_num_cell_inventory_communities,
            sum(ff_shortcut_num_cell_inventory_BEST_FRIENDS) AS ff_shortcut_num_cell_inventory_best_friends,
            sum(ff_shortcut_num_cell_inventory_SENT) ff_shortcut_num_cell_inventory_sent,
            sum(ff_shortcut_num_cell_inventory_call_log) ff_shortcut_num_cell_inventory_call_log,

            sum(ff_shortcut_num_cell_inventory_unread_deduped) AS  ff_shortcut_num_cell_inventory_unread_deduped,
            sum(ff_shortcut_num_cell_inventory_Groups_deduped) AS  ff_shortcut_num_cell_inventory_groups_deduped,
            sum(ff_shortcut_num_cell_inventory_Unreplied_deduped) AS  ff_shortcut_num_cell_inventory_unreplied_deduped,
            sum(ff_shortcut_num_cell_inventory_stories_deduped) AS  ff_shortcut_num_cell_inventory_stories_deduped,
            sum(ff_shortcut_num_cell_inventory_Nearby_deduped) AS  ff_shortcut_num_cell_inventory_nearby_deduped,
            sum(ff_shortcut_num_cell_inventory_New_friends_deduped) AS  ff_shortcut_num_cell_inventory_new_friends_deduped,
            sum(ff_shortcut_num_cell_inventory_birthday_deduped) AS  ff_shortcut_num_cell_inventory_birthday_deduped,
            sum(ff_shortcut_num_cell_inventory_CUSTOM_deduped) AS ff_shortcut_num_cell_inventory_custom_deduped,
            sum(ff_shortcut_num_cell_inventory_streak_deduped) AS ff_shortcut_num_cell_inventory_streak_deduped,
            sum(ff_shortcut_num_cell_inventory_MERLIN_deduped) AS ff_shortcut_num_cell_inventory_myai_deduped,
            sum(ff_shortcut_num_cell_inventory_COMMUNITIES_deduped) AS ff_shortcut_num_cell_inventory_communities_deduped,
            sum(ff_shortcut_num_cell_inventory_BEST_FRIENDS_deduped) AS ff_shortcut_num_cell_inventory_best_friends_deduped,
            sum(ff_shortcut_num_cell_inventory_SENT_deduped) AS ff_shortcut_num_cell_inventory_sent_deduped,
            sum(ff_shortcut_num_cell_inventory_call_log_deduped) AS ff_shortcut_num_cell_inventory_call_log_deduped,

        --    sum(ff_shortcut_page_sync_successful_unread) AS  ff_shortcut_page_sync_successful_unread,
        --    sum(ff_shortcut_page_sync_successful_Groups) AS  ff_shortcut_page_sync_successful_Groups,
        --    sum(ff_shortcut_page_sync_successful_Unreplied) AS  ff_shortcut_page_sync_successful_Unreplied,
        --    sum(ff_shortcut_page_sync_successful_stories) AS  ff_shortcut_page_sync_successful_stories,
        --    sum(ff_shortcut_page_sync_successful_Nearby) AS  ff_shortcut_page_sync_successful_Nearby,
        --    sum(ff_shortcut_page_sync_successful_New_friends) AS  ff_shortcut_page_sync_successful_New_friends,
        --    sum(ff_shortcut_page_sync_successful_birthday) AS  ff_shortcut_page_sync_successful_birthday,

        --    sum(ff_shortcut_page_sync_unsuccessful_unread) AS  ff_shortcut_page_sync_unsuccessful_unread,
        --    sum(ff_shortcut_page_sync_unsuccessful_Groups) AS  ff_shortcut_page_sync_unsuccessful_Groups,
        --    sum(ff_shortcut_page_sync_unsuccessful_Unreplied) AS  ff_shortcut_page_sync_unsuccessful_Unreplied,
        --    sum(ff_shortcut_page_sync_unsuccessful_stories) AS  ff_shortcut_page_sync_unsuccessful_stories,
        --    sum(ff_shortcut_page_sync_unsuccessful_Nearby) AS  ff_shortcut_page_sync_unsuccessful_Nearby,
        --    sum(ff_shortcut_page_sync_unsuccessful_New_friends) AS  ff_shortcut_page_sync_unsuccessful_New_friends,
        --    sum(ff_shortcut_page_sync_unsuccessful_birthday) AS  ff_shortcut_page_sync_unsuccessful_birthday,

            sum(ff_shortcut_exit_unselect_shortcut) as ff_shortcut_exit_unselect_shortcut,
            sum(ff_shortcut_exit_exit_feed) as ff_shortcut_exit_exit_feed,
            sum(ff_shortcut_exit_tap_cell) as ff_shortcut_exit_tap_cell,
            sum(ff_shortcut_exit_double_tap_cell) as ff_shortcut_exit_double_tap_cell,
            sum(ff_shortcut_exit_tap_reply_button) as ff_shortcut_exit_tap_reply_button,
            sum(ff_shortcut_next_page_messaging_feed) as ff_shortcut_next_page_messaging_feed,
            sum(ff_shortcut_next_page_camera_or_view_finder) as ff_shortcut_next_page_camera_or_view_finder,
            sum(ff_shortcut_next_page_opera) as ff_shortcut_next_page_opera,
            sum(ff_shortcut_next_page_friendsfeed) as ff_shortcut_next_page_friendsfeed,

            sum(ff_shortcuts_session_community_group_chat) ff_shortcuts_session_community_group_chat,
            sum(ff_shortcuts_session_community_group_chat_uu) ff_shortcuts_session_community_group_chat_uu,
            sum(sendto_shortcuts_session_community_group_chat) sendto_shortcuts_session_community_group_chat,
            sum(sendto_shortcuts_session_community_group_chat_uu) sendto_shortcuts_session_community_group_chat_uu,
            sum(ff_shortcuts_community_group_chat) ff_shortcuts_community_group_chat,
            sum(ff_shortcuts_community_group_chat_uu) ff_shortcuts_community_group_chat_uu,

            FROM
            `sc-analytics.report_messaging.chatv3_user_friends_feed_activity_detail_*`
            WHERE _table_suffix between '{start_date}' and '{end_date}'
            GROUP BY 1,2
        """

    def sql_callable(start_date, end_date):
#         start_date, end_date = start_date[-6:], end_date[-6:]
        return sql.format(start_date=start_date, end_date=end_date)

    mt = MetricTable(
        sql= sql.format(start_date=start_date, end_date=end_date),
        sql_callable = sql_callable,
        metrics=[
                ff_shortcuts_available_all,
                ff_shortcuts_available_all_uu,
                ff_shortcuts_available_unread_uu,
                ff_shortcuts_available_unreplied_uu,
                ff_shortcuts_available_story_uu,
                ff_shortcuts_available_new_friends_uu,
                ff_shortcuts_available_groups_uu,
                ff_shortcuts_available_nearby_uu,
                ff_shortcuts_available_recently_active_friends_uu,
                ff_shortcuts_available_my_ai_uu,
                ff_shortcuts_available_call_log_uu,
                ff_shortcuts_available_sent_uu,
                ff_shortcuts_available_best_friends_uu,
                ff_shortcuts_available_streaks_friends_uu,
                ff_shortcuts_available_birthdays_friends_uu,
                ff_shortcuts_available_contacts_friends_uu,
                ff_shortcuts_available_custom_uu,
                ff_shortcuts_available_communities_uu,

                ff_shortcuts_selected_all,
                ff_shortcuts_selected_unread,
                ff_shortcuts_selected_unreplied,
                ff_shortcuts_selected_story,
                ff_shortcuts_selected_new_friends,
                ff_shortcuts_selected_groups,
                ff_shortcuts_selected_nearby,
                ff_shortcuts_selected_recently_active_friends,
                ff_shortcuts_selected_my_ai,
                ff_shortcuts_selected_call_log,
                ff_shortcuts_selected_sent,
                ff_shortcuts_selected_best_friends,
                ff_shortcuts_selected_streaks_friends,
                ff_shortcuts_selected_birthdays_friends,
                ff_shortcuts_selected_contacts_friends,
                ff_shortcuts_selected_custom,
                ff_shortcuts_selected_communities,

                ff_shortcuts_selected_all_uu,
                ff_shortcuts_selected_unread_uu,
                ff_shortcuts_selected_unreplied_uu,
                ff_shortcuts_selected_story_uu,
                ff_shortcuts_selected_new_friends_uu,
                ff_shortcuts_selected_groups_uu,
                ff_shortcuts_selected_nearby_uu,
                ff_shortcuts_selected_recently_active_friends_uu,
                ff_shortcuts_selected_my_ai_uu,
                ff_shortcuts_selected_call_log_uu,
                ff_shortcuts_selected_sent_uu,
                ff_shortcuts_selected_best_friends_uu,
                ff_shortcuts_selected_streaks_friends_uu,
                ff_shortcuts_selected_birthdays_friends_uu,
                ff_shortcuts_selected_contacts_friends_uu,
                ff_shortcuts_selected_custom_uu,
                ff_shortcuts_selected_communities_uu,

                ff_shortcut_batch_reply_camera_button_clicked,

                ff_shortcut_num_cell_inventory_unread,
                ff_shortcut_num_cell_inventory_Groups,
                ff_shortcut_num_cell_inventory_unreplied,
                ff_shortcut_num_cell_inventory_stories,
                ff_shortcut_num_cell_inventory_nearby,
                ff_shortcut_num_cell_inventory_new_friends,
                ff_shortcut_num_cell_inventory_birthday,
                ff_shortcut_num_cell_inventory_custom,
                ff_shortcut_num_cell_inventory_streak,
                ff_shortcut_num_cell_inventory_myai,
                ff_shortcut_num_cell_inventory_communities,
                ff_shortcut_num_cell_inventory_best_friends,
                ff_shortcut_num_cell_inventory_sent,
                ff_shortcut_num_cell_inventory_call_log,

                ff_shortcut_num_cell_inventory_unread_deduped,
                ff_shortcut_num_cell_inventory_groups_deduped,
                ff_shortcut_num_cell_inventory_unreplied_deduped,
                ff_shortcut_num_cell_inventory_stories_deduped,
                ff_shortcut_num_cell_inventory_nearby_deduped,
                ff_shortcut_num_cell_inventory_new_friends_deduped,
                ff_shortcut_num_cell_inventory_birthday_deduped,
                ff_shortcut_num_cell_inventory_custom_deduped,
                ff_shortcut_num_cell_inventory_streak_deduped,
                ff_shortcut_num_cell_inventory_myai_deduped,
                ff_shortcut_num_cell_inventory_communities_deduped,
                ff_shortcut_num_cell_inventory_best_friends_deduped,
                ff_shortcut_num_cell_inventory_sent_deduped,
                ff_shortcut_num_cell_inventory_call_log_deduped,

#                 ff_shortcut_page_sync_successful_unread,
#                 ff_shortcut_page_sync_successful_Groups,
#                 ff_shortcut_page_sync_successful_Unreplied,
#                 ff_shortcut_page_sync_successful_stories,
#                 ff_shortcut_page_sync_successful_Nearby,
#                 ff_shortcut_page_sync_successful_New_friends,
#                 ff_shortcut_page_sync_successful_birthday,

#                 ff_shortcut_page_sync_unsuccessful_unread,
#                 ff_shortcut_page_sync_unsuccessful_Groups,
#                 ff_shortcut_page_sync_unsuccessful_Unreplied,
#                 ff_shortcut_page_sync_unsuccessful_stories,
#                 ff_shortcut_page_sync_unsuccessful_Nearby,
#                 ff_shortcut_page_sync_unsuccessful_New_friends,
#                 ff_shortcut_page_sync_unsuccessful_birthday,

                ff_shortcut_exit_unselect_shortcut,
                ff_shortcut_exit_exit_feed,
                ff_shortcut_exit_tap_cell,
                ff_shortcut_exit_double_tap_cell,
                ff_shortcut_exit_tap_reply_button,
                ff_shortcut_next_page_messaging_feed,
                ff_shortcut_next_page_camera_or_view_finder,
                ff_shortcut_next_page_opera,
                ff_shortcut_next_page_friendsfeed,
                ff_shortcuts_session_community_group_chat,
                ff_shortcuts_session_community_group_chat_uu,
                sendto_shortcuts_session_community_group_chat,
                sendto_shortcuts_session_community_group_chat_uu,
                ff_shortcuts_community_group_chat,
                ff_shortcuts_community_group_chat_uu
                ],
        name="friend_feed_shortcut_metrics",
        inner_join_with_mapping=False,
        bq_dialect="standard"
    )
    return mt


def relationship_closeness_metrics(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
#     start_date, end_date = start_date[-6:], end_date[-6:]
    source_table = """
    `sc-analytics.report_growth.relationship_closeness_category_user_agg_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    relationship_count_close_plus=Metric(col="relationship_count_close_plus", dist="cont", daily=True, cumulative=True)
    relationship_count_loose=Metric(col="relationship_count_loose", dist="cont", daily=True, cumulative=True)
    relationship_count_casual=Metric(col="relationship_count_casual", dist="cont", daily=True, cumulative=True)
    relationship_count_acquaintance=Metric(col="relationship_count_acquaintance", dist="cont", daily=True, cumulative=True)
    relationship_count_close=Metric(col="relationship_count_close", dist="cont", daily=True, cumulative=True)
    relationship_count_strong=Metric(col="relationship_count_strong", dist="cont", daily=True, cumulative=True)
    relationship_count_other=Metric(col="relationship_count_other", dist="cont", daily=True, cumulative=True)
    chat_send_from_loose_relationship=Metric(col="chat_send_from_loose_relationship", dist="cont", daily=True, cumulative=True)
    chat_send_from_casual_relationship=Metric(col="chat_send_from_casual_relationship", dist="cont", daily=True, cumulative=True)
    chat_send_from_acquaintance_relationship=Metric(col="chat_send_from_acquaintance_relationship", dist="cont", daily=True, cumulative=True)
    chat_send_from_close_relationship=Metric(col="chat_send_from_close_relationship", dist="cont", daily=True, cumulative=True)
    chat_send_from_strong_relationship=Metric(col="chat_send_from_strong_relationship", dist="cont", daily=True, cumulative=True)
    chat_send_from_close_plus_relationship=Metric(col="chat_send_from_close_plus_relationship", dist="cont", daily=True, cumulative=True)
    snap_send_from_loose_relationship=Metric(col="snap_send_from_loose_relationship", dist="cont", daily=True, cumulative=True)
    snap_send_from_casual_relationship=Metric(col="snap_send_from_casual_relationship", dist="cont", daily=True, cumulative=True)
    snap_send_from_acquaintance_relationship=Metric(col="snap_send_from_acquaintance_relationship", dist="cont", daily=True, cumulative=True)
    snap_send_from_close_relationship=Metric(col="snap_send_from_close_relationship", dist="cont", daily=True, cumulative=True)
    snap_send_from_strong_relationship=Metric(col="snap_send_from_strong_relationship", dist="cont", daily=True, cumulative=True)
    snap_send_from_close_plus_relationship=Metric(col="snap_send_from_close_plus_relationship", dist="cont", daily=True, cumulative=True)
    story_view_from_loose_relationship=Metric(col="story_view_from_loose_relationship", dist="cont", daily=True, cumulative=True)
    story_view_from_casual_relationship=Metric(col="story_view_from_casual_relationship", dist="cont", daily=True, cumulative=True)
    story_view_from_acquaintance_relationship=Metric(col="story_view_from_acquaintance_relationship", dist="cont", daily=True, cumulative=True)
    story_view_from_close_relationship=Metric(col="story_view_from_close_relationship", dist="cont", daily=True, cumulative=True)
    story_view_from_strong_relationship=Metric(col="story_view_from_strong_relationship", dist="cont", daily=True, cumulative=True)
    story_view_from_close_plus_relationship=Metric(col="story_view_from_close_plus_relationship", dist="cont", daily=True, cumulative=True)
    map_user_view_from_loose_relationship=Metric(col="map_user_view_from_loose_relationship", dist="cont", daily=True, cumulative=True)
    map_user_view_from_casual_relationship=Metric(col="map_user_view_from_casual_relationship", dist="cont", daily=True, cumulative=True)
    map_user_view_from_acquaintance_relationship=Metric(col="map_user_view_from_acquaintance_relationship", dist="cont", daily=True, cumulative=True)
    map_user_view_from_close_relationship=Metric(col="map_user_view_from_close_relationship", dist="cont", daily=True, cumulative=True)
    map_user_view_from_strong_relationship=Metric(col="map_user_view_from_strong_relationship", dist="cont", daily=True, cumulative=True)
    map_user_view_from_close_plus_relationship=Metric(col="map_user_view_from_close_plus_relationship", dist="cont", daily=True, cumulative=True)
    user_with_1_plus_close_plus=Metric(col="user_with_1_plus_close_plus", dist="cont", daily=True, cumulative=True)
    user_with_1_plus_loose=Metric(col="user_with_1_plus_loose", dist="cont", daily=True, cumulative=True)
    user_with_1_plus_casual=Metric(col="user_with_1_plus_casual", dist="cont", daily=True, cumulative=True)
    user_with_1_plus_acquaintance=Metric(col="user_with_1_plus_acquaintance", dist="cont", daily=True, cumulative=True)
    user_with_1_plus_close=Metric(col="user_with_1_plus_close", dist="cont", daily=True, cumulative=True)
    user_with_1_plus_strong=Metric(col="user_with_1_plus_strong", dist="cont", daily=True, cumulative=True)
    user_with_1_plus_non_friend=Metric(col="user_with_1_plus_non_friend", dist="cont", daily=True, cumulative=True)
    chat_send_from_loose_relationship_active_day=Metric(col="chat_send_from_loose_relationship_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_from_casual_relationship_active_day=Metric(col="chat_send_from_casual_relationship_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_from_acquaintance_relationship_active_day=Metric(col="chat_send_from_acquaintance_relationship_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_from_close_relationship_active_day=Metric(col="chat_send_from_close_relationship_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_from_strong_relationship_active_day=Metric(col="chat_send_from_strong_relationship_active_day", dist="cont", daily=True, cumulative=True)
    chat_send_from_close_plus_relationship_active_day=Metric(col="chat_send_from_close_plus_relationship_active_day", dist="cont", daily=True, cumulative=True)
    snap_send_from_loose_relationship_active_day=Metric(col="snap_send_from_loose_relationship_active_day", dist="cont", daily=True, cumulative=True)
    snap_send_from_casual_relationship_active_day=Metric(col="snap_send_from_casual_relationship_active_day", dist="cont", daily=True, cumulative=True)
    snap_send_from_acquaintance_relationship_active_day=Metric(col="snap_send_from_acquaintance_relationship_active_day", dist="cont", daily=True, cumulative=True)
    snap_send_from_close_relationship_active_day=Metric(col="snap_send_from_close_relationship_active_day", dist="cont", daily=True, cumulative=True)
    snap_send_from_strong_relationship_active_day=Metric(col="snap_send_from_strong_relationship_active_day", dist="cont", daily=True, cumulative=True)
    snap_send_from_close_plus_relationship_active_day=Metric(col="snap_send_from_close_plus_relationship_active_day", dist="cont", daily=True, cumulative=True)
    story_view_from_loose_relationship_active_day=Metric(col="story_view_from_loose_relationship_active_day", dist="cont", daily=True, cumulative=True)
    story_view_from_casual_relationship_active_day=Metric(col="story_view_from_casual_relationship_active_day", dist="cont", daily=True, cumulative=True)
    story_view_from_acquaintance_relationship_active_day=Metric(col="story_view_from_acquaintance_relationship_active_day", dist="cont", daily=True, cumulative=True)
    story_view_from_close_relationship_active_day=Metric(col="story_view_from_close_relationship_active_day", dist="cont", daily=True, cumulative=True)
    story_view_from_strong_relationship_active_day=Metric(col="story_view_from_strong_relationship_active_day", dist="cont", daily=True, cumulative=True)
    story_view_from_close_plus_relationship_active_day=Metric(col="story_view_from_close_plus_relationship_active_day", dist="cont", daily=True, cumulative=True)
    map_user_view_from_loose_relationship_active_day=Metric(col="map_user_view_from_loose_relationship_active_day", dist="cont", daily=True, cumulative=True)
    map_user_view_from_casual_relationship_active_day=Metric(col="map_user_view_from_casual_relationship_active_day", dist="cont", daily=True, cumulative=True)
    map_user_view_from_acquaintance_relationship_active_day=Metric(col="map_user_view_from_acquaintance_relationship_active_day", dist="cont", daily=True, cumulative=True)
    map_user_view_from_close_relationship_active_day=Metric(col="map_user_view_from_close_relationship_active_day", dist="cont", daily=True, cumulative=True)
    map_user_view_from_strong_relationship_active_day=Metric(col="map_user_view_from_strong_relationship_active_day", dist="cont", daily=True, cumulative=True)
    map_user_view_from_close_plus_relationship_active_day=Metric(col="map_user_view_from_close_plus_relationship_active_day", dist="cont", daily=True, cumulative=True)

    sql="""
         SELECT 
         TIMESTAMP(PARSE_DATE('%Y%m%d', _table_suffix)) as ts, 
         ghost_user_id,

        SUM(bidirectional_friend_v3_relationship_count_strong+bidirectional_friend_v3_relationship_count_close) AS relationship_count_close_plus,
        SUM(bidirectional_friend_v3_relationship_count_loose) AS relationship_count_loose, 
        SUM(bidirectional_friend_v3_relationship_count_casual) AS relationship_count_casual,
        SUM(bidirectional_friend_v3_relationship_count_acquaintance) AS relationship_count_acquaintance,
        SUM(bidirectional_friend_v3_relationship_count_close) AS relationship_count_close,
        SUM(bidirectional_friend_v3_relationship_count_strong) AS relationship_count_strong,
        SUM(non_friend_relationship_count) AS relationship_count_other,
        SUM(chat_send_total_loose_relationship) AS chat_send_from_loose_relationship,
        SUM(chat_send_total_casual_relationship) AS chat_send_from_casual_relationship,
        SUM(chat_send_total_acquaintance_relationship) AS chat_send_from_acquaintance_relationship,
        SUM(chat_send_total_close_relationship) AS chat_send_from_close_relationship,
        SUM(chat_send_total_strong_relationship) AS chat_send_from_strong_relationship,
        SUM(chat_send_total_strong_relationship+chat_send_total_close_relationship) AS chat_send_from_close_plus_relationship,
        SUM(snap_send_total_loose_relationship) AS snap_send_from_loose_relationship,
        SUM(snap_send_total_casual_relationship) AS snap_send_from_casual_relationship,
        SUM(snap_send_total_acquaintance_relationship) AS snap_send_from_acquaintance_relationship,
        SUM(snap_send_total_close_relationship) AS snap_send_from_close_relationship,
        SUM(snap_send_total_strong_relationship) AS snap_send_from_strong_relationship,
        SUM(snap_send_total_strong_relationship+snap_send_total_close_relationship) AS snap_send_from_close_plus_relationship,
        SUM(story_view_total_loose_relationship) AS story_view_from_loose_relationship,
        SUM(story_view_total_casual_relationship) AS story_view_from_casual_relationship,
        SUM(story_view_total_acquaintance_relationship) AS story_view_from_acquaintance_relationship,
        SUM(story_view_total_close_relationship) AS story_view_from_close_relationship,
        SUM(story_view_total_strong_relationship) AS story_view_from_strong_relationship,
        SUM(story_view_total_strong_relationship+story_view_total_close_relationship) AS story_view_from_close_plus_relationship,
        SUM(map_user_view_total_loose_relationship) AS map_user_view_from_loose_relationship,
        SUM(map_user_view_total_casual_relationship) AS map_user_view_from_casual_relationship,
        SUM(map_user_view_total_acquaintance_relationship) AS map_user_view_from_acquaintance_relationship,
        SUM(map_user_view_total_close_relationship) AS map_user_view_from_close_relationship,
        SUM(map_user_view_total_strong_relationship) AS map_user_view_from_strong_relationship,
        SUM(map_user_view_total_strong_relationship+map_user_view_total_close_relationship) AS map_user_view_from_close_plus_relationship,
        IF(SUM(bidirectional_friend_v3_relationship_count_strong+bidirectional_friend_v3_relationship_count_close)>0,1,0) AS user_with_1_plus_close_plus,
        IF(SUM(bidirectional_friend_v3_relationship_count_loose)>0,1,0) AS user_with_1_plus_loose,
        IF(SUM(bidirectional_friend_v3_relationship_count_casual)>0,1,0) AS user_with_1_plus_casual,
        IF(SUM(bidirectional_friend_v3_relationship_count_acquaintance)>0,1,0) AS user_with_1_plus_acquaintance,
        IF(SUM(bidirectional_friend_v3_relationship_count_close)>0,1,0) AS user_with_1_plus_close,
        IF(SUM(bidirectional_friend_v3_relationship_count_strong)>0,1,0) AS user_with_1_plus_strong,
        IF(SUM(non_friend_relationship_count)>0,1,0) AS user_with_1_plus_non_friend,
        IF(SUM(chat_send_total_loose_relationship)>0,1,0) AS chat_send_from_loose_relationship_active_day,
        IF(SUM(chat_send_total_casual_relationship)>0,1,0) AS chat_send_from_casual_relationship_active_day,
        IF(SUM(chat_send_total_acquaintance_relationship)>0,1,0) AS chat_send_from_acquaintance_relationship_active_day,
        IF(SUM(chat_send_total_close_relationship)>0,1,0) AS chat_send_from_close_relationship_active_day,
        IF(SUM(chat_send_total_strong_relationship)>0,1,0) AS chat_send_from_strong_relationship_active_day,
        IF(SUM(chat_send_total_strong_relationship+chat_send_total_close_relationship)>0,1,0) AS chat_send_from_close_plus_relationship_active_day,
        IF(SUM(snap_send_total_loose_relationship)>0,1,0) AS snap_send_from_loose_relationship_active_day,
        IF(SUM(snap_send_total_casual_relationship)>0,1,0) AS snap_send_from_casual_relationship_active_day,
        IF(SUM(snap_send_total_acquaintance_relationship)>0,1,0) AS snap_send_from_acquaintance_relationship_active_day,
        IF(SUM(snap_send_total_close_relationship)>0,1,0) AS snap_send_from_close_relationship_active_day,
        IF(SUM(snap_send_total_strong_relationship)>0,1,0) AS snap_send_from_strong_relationship_active_day,
        IF(SUM(snap_send_total_strong_relationship+snap_send_total_close_relationship)>0,1,0) AS snap_send_from_close_plus_relationship_active_day,
        IF(SUM(story_view_total_loose_relationship)>0,1,0) AS story_view_from_loose_relationship_active_day,
        IF(SUM(story_view_total_casual_relationship)>0,1,0) AS story_view_from_casual_relationship_active_day,
        IF(SUM(story_view_total_acquaintance_relationship)>0,1,0) AS story_view_from_acquaintance_relationship_active_day,
        IF(SUM(story_view_total_close_relationship)>0,1,0) AS story_view_from_close_relationship_active_day,
        IF(SUM(story_view_total_strong_relationship)>0,1,0) AS story_view_from_strong_relationship_active_day,
        IF(SUM(story_view_total_strong_relationship+story_view_total_close_relationship)>0,1,0) AS story_view_from_close_plus_relationship_active_day,
        IF(SUM(map_user_view_total_loose_relationship)>0,1,0) AS map_user_view_from_loose_relationship_active_day,
        IF(SUM(map_user_view_total_casual_relationship)>0,1,0) AS map_user_view_from_casual_relationship_active_day,
        IF(SUM(map_user_view_total_acquaintance_relationship)>0,1,0) AS map_user_view_from_acquaintance_relationship_active_day,
        IF(SUM(map_user_view_total_close_relationship)>0,1,0) AS map_user_view_from_close_relationship_active_day,
        IF(SUM(map_user_view_total_strong_relationship)>0,1,0) AS map_user_view_from_strong_relationship_active_day,
        IF(SUM(map_user_view_total_strong_relationship+map_user_view_total_close_relationship)>0,1,0) AS map_user_view_from_close_plus_relationship_active_day,

        FROM
        `sc-analytics.report_growth.relationship_closeness_definition_user_agg_*`
        where _table_suffix between '{start_date}' and '{end_date}'
        GROUP BY 1, 2

        """
    
    def sql_callable(start_date, end_date):
#         start_date, end_date = start_date[-6:], end_date[-6:]
        return sql.format(start_date=start_date, end_date=end_date)
    
    mt = MetricTable(
        sql = sql.format(start_date=start_date, end_date=end_date),
        sql_callable = sql_callable,
        metrics=[
        relationship_count_close_plus,
        relationship_count_loose, 
        relationship_count_casual,
        relationship_count_acquaintance,
        relationship_count_close,
        relationship_count_strong,
        relationship_count_other,
        chat_send_from_loose_relationship,
        chat_send_from_casual_relationship,
        chat_send_from_acquaintance_relationship,
        chat_send_from_close_relationship,
        chat_send_from_strong_relationship,
        chat_send_from_close_plus_relationship,
        snap_send_from_loose_relationship,
        snap_send_from_casual_relationship,
        snap_send_from_acquaintance_relationship,
        snap_send_from_close_relationship,
        snap_send_from_strong_relationship,
        snap_send_from_close_plus_relationship,
        story_view_from_loose_relationship,
        story_view_from_casual_relationship,
        story_view_from_acquaintance_relationship,
        story_view_from_close_relationship,
        story_view_from_strong_relationship,
        story_view_from_close_plus_relationship,
        map_user_view_from_loose_relationship,
        map_user_view_from_casual_relationship,
        map_user_view_from_acquaintance_relationship,
        map_user_view_from_close_relationship,
        map_user_view_from_strong_relationship,
        map_user_view_from_close_plus_relationship,
        user_with_1_plus_close_plus,
        user_with_1_plus_loose,
        user_with_1_plus_casual,
        user_with_1_plus_acquaintance,
        user_with_1_plus_close,
        user_with_1_plus_strong,
        user_with_1_plus_non_friend,
        chat_send_from_loose_relationship_active_day,
        chat_send_from_casual_relationship_active_day,
        chat_send_from_acquaintance_relationship_active_day,
        chat_send_from_close_relationship_active_day,
        chat_send_from_strong_relationship_active_day,
        chat_send_from_close_plus_relationship_active_day,
        snap_send_from_loose_relationship_active_day,
        snap_send_from_casual_relationship_active_day,
        snap_send_from_acquaintance_relationship_active_day,
        snap_send_from_close_relationship_active_day,
        snap_send_from_strong_relationship_active_day,
        snap_send_from_close_plus_relationship_active_day,
        story_view_from_loose_relationship_active_day,
        story_view_from_casual_relationship_active_day,
        story_view_from_acquaintance_relationship_active_day,
        story_view_from_close_relationship_active_day,
        story_view_from_strong_relationship_active_day,
        story_view_from_close_plus_relationship_active_day,
        map_user_view_from_loose_relationship_active_day,
        map_user_view_from_casual_relationship_active_day,
        map_user_view_from_acquaintance_relationship_active_day,
        map_user_view_from_close_relationship_active_day,
        map_user_view_from_strong_relationship_active_day,
        map_user_view_from_close_plus_relationship_active_day
        ],
        name="relationship_closeness_metrics",
        bq_dialect="standard"
    )
    return mt


def notification_platform_metrics_overall(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """
#     start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
#     end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    start_date, end_date = start_date[-6:], end_date[-6:]
    source_table = """
    `sc-analytics.report_messaging.notif_notif_send_user_husky_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    notif_send = Metric(col="notif_send", dist="cont", daily=True, cumulative=True)
    notif_receive = Metric(col="notif_receive", dist="cont", daily=True, cumulative=True)
    notif_receive_in_app = Metric(col="notif_receive_in_app", dist="cont", daily=True, cumulative=True)
    notif_receive_system = Metric(col="notif_receive_system", dist="cont", daily=True, cumulative=True)
    notif_display = Metric(col="notif_display", dist="cont", daily=True, cumulative=True)
    notif_display_in_app = Metric(col="notif_display_in_app", dist="cont", daily=True, cumulative=True)
    notif_display_system = Metric(col="notif_display_system", dist="cont", daily=True, cumulative=True)
    notif_suppression = Metric(col="notif_suppression", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notif_suppression_in_app = Metric(col="notif_suppression_in_app", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notif_suppression_system = Metric(col="notif_suppression_system", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notif_unp = Metric(col="notif_unp", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notif_unp_in_app = Metric(col="notif_unp_in_app", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notif_unp_system = Metric(col="notif_unp_system", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notif_app_open = Metric(col="notif_app_open", dist="cont", daily=True, cumulative=True)

    notif_receive_rate = Metric(col='notif_receive_rate', numerator='notif_receive', denominator='notif_send', dist='ratio')
    notif_display_rate = Metric(col='notif_display_rate', numerator='notif_display', denominator='notif_receive', dist='ratio')
    notif_suppression_rate = Metric(col='notif_suppression_rate', numerator='notif_suppression', denominator='notif_receive', dist='ratio', desired_direction=NEGATIVE)
    notif_unp_rate = Metric(col='notif_unp_rate', numerator='notif_unp', denominator='notif_receive', dist='ratio', desired_direction=NEGATIVE)
    notif_in_app_display_rate = Metric(col='notif_in_app_display_rate', numerator='notif_display_in_app', denominator='notif_receive_in_app', dist='ratio')
    notif_in_app_suppression_rate = Metric(col='notif_in_app_suppression_rate', numerator='notif_suppression_in_app', denominator='notif_receive_in_app', dist='ratio', desired_direction=NEGATIVE)
    notif_in_app_unp_rate = Metric(col='notif_in_app_unp_rate', numerator='notif_unp_in_app', denominator='notif_receive_in_app', dist='ratio', desired_direction=NEGATIVE)
    notif_system_display_rate = Metric(col='notif_system_display_rate', numerator='notif_display_system', denominator='notif_receive_system', dist='ratio')
    notif_system_suppression_rate = Metric(col='notif_system_suppression_rate', numerator='notif_suppression_system', denominator='notif_receive_system', dist='ratio', desired_direction=NEGATIVE)
    notif_system_unp_rate = Metric(col='notif_system_unp_rate', numerator='notif_unp_system', denominator='notif_receive_system', dist='ratio', desired_direction=NEGATIVE)
    notif_app_open_rate = Metric(col='notif_app_open_rate', numerator='notif_app_open', denominator='notif_display', dist='ratio')

    notif_send_uu = Metric(col="notif_send_uu", dist="cont", daily=True, cumulative=True)
    notif_receive_uu = Metric(col="notif_receive_uu", dist="cont", daily=True, cumulative=True)
    notif_receive_in_app_uu = Metric(col="notif_receive_in_app_uu", dist="cont", daily=True, cumulative=True)
    notif_receive_system_uu = Metric(col="notif_receive_system_uu", dist="cont", daily=True, cumulative=True)
    notif_display_uu = Metric(col="notif_display_uu", dist="cont", daily=True, cumulative=True)
    notif_display_in_app_uu = Metric(col="notif_display_in_app_uu", dist="cont", daily=True, cumulative=True)
    notif_display_system_uu = Metric(col="notif_display_system_uu", dist="cont", daily=True, cumulative=True)
    notif_suppression_uu = Metric(col="notif_suppression_uu", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notif_suppression_in_app_uu = Metric(col="notif_suppression_in_app_uu", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notif_suppression_system_uu = Metric(col="notif_suppression_system_uu", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notif_unp_uu = Metric(col="notif_unp_uu", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notif_unp_in_app_uu = Metric(col="notif_unp_in_app_uu", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notif_unp_system_uu = Metric(col="notif_unp_system_uu", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notif_app_open_uu = Metric(col="notif_app_open_uu", dist="cont", daily=True, cumulative=True)

    notif_send_on_any_device = Metric(col="notif_send_on_any_device", dist="cont", daily=True, cumulative=True)
    notif_receive_on_any_device = Metric(col="notif_receive_on_any_device", dist="cont", daily=True, cumulative=True)
    notif_display_on_any_device = Metric(col="notif_display_on_any_device", dist="cont", daily=True, cumulative=True)
    notif_fail_on_any_device = Metric(col="notif_fail_on_any_device", dist="cont", desired_direction=NEGATIVE, daily=True, cumulative=True)
    notif_app_open_on_any_device = Metric(col="notif_app_open_on_any_device", dist="cont", daily=True, cumulative=True)
    notif_suppression_on_any_device = Metric(col="notif_suppression_on_any_device", dist="cont", desired_direction=NEGATIVE, daily=True, cumulative=True)
    unp_on_any_device = Metric(col="unp_on_any_device", dist="cont", desired_direction=NEGATIVE, daily=True, cumulative=True)

    notif_receive_rate_on_any_device = Metric(col='notif_receive_rate_on_any_device', numerator='notif_receive_on_any_device', denominator='notif_send_on_any_device', dist='ratio')
    notif_display_rate_on_any_device = Metric(col='notif_display_rate_on_any_device', numerator='notif_display_on_any_device', denominator='notif_receive_on_any_device', dist='ratio')
    notif_suppression_rate_on_any_device = Metric(col='notif_suppression_rate_on_any_device', numerator='notif_suppression_on_any_device', denominator='notif_receive_on_any_device', dist='ratio', desired_direction=NEGATIVE)
    notif_unp_rate_on_any_device = Metric(col='notif_unp_rate_on_any_device', numerator='unp_on_any_device', denominator='notif_receive_on_any_device', dist='ratio', desired_direction=NEGATIVE)
    notif_app_open_rate_on_any_device = Metric(col='notif_app_open_rate_on_any_device', numerator='notif_app_open_on_any_device', denominator='notif_display_on_any_device', dist='ratio')

    notif_system_enable_on_any_device = Metric(col="notif_system_enable_on_any_device", dist="cont", daily=True, cumulative=True)
    notif_system_disable_on_any_device = Metric(col="notif_system_disable_on_any_device", dist="cont", daily=True, cumulative=True)
    notif_system_enable_all_device = Metric(col="notif_system_enable_all_device", dist="cont", daily=True, cumulative=True)
    notif_system_disable_all_device = Metric(col="notif_system_disable_all_device", dist="cont", daily=True, cumulative=True)
    notif_system_enable_rate_on_any_device = Metric(col='notif_system_enable_rate_on_any_device', numerator='notif_system_enable_on_any_device', denominator='notif_system_update_uu', dist='ratio')
    notif_system_disable_rate_on_any_device = Metric(col='notif_system_disable_rate_on_any_device', numerator='notif_system_disable_on_any_device', denominator='notif_system_update_uu', dist='ratio')
    notif_system_enable_rate_all_device = Metric(col='notif_system_enable_rate_all_device', numerator='notif_system_enable_all_device', denominator='notif_system_update_count', dist='ratio')
    notif_system_disable_rate_all_device = Metric(col='notif_system_disable_rate_all_device', numerator='notif_system_disable_all_device', denominator='notif_system_update_count', dist='ratio')
    notif_system_update_uu = Metric(col="notif_system_update_uu", dist="cont", daily=True, cumulative=True)
    notif_system_update_count = Metric(col="notif_system_update_count", dist="cont", daily=True, cumulative=True)

    time_sensitive_notification_enable_all_device = Metric(col="time_sensitive_notification_enable_all_device", dist="cont", daily=True, cumulative=True)
    time_sensitive_notification_disable_all_device = Metric(col="time_sensitive_notification_disable_all_device", dist="cont", daily=True, cumulative=True)
    time_sensitive_notification_supported_all_device = Metric(col="time_sensitive_notification_supported_all_device", dist="cont", daily=True, cumulative=True)
    time_sensitive_notification_enable_rate_all_device = Metric(col='time_sensitive_notification_enable_rate_all_device', numerator='time_sensitive_notification_enable_all_device', denominator='time_sensitive_notification_supported_all_device', dist='ratio')

    sql="""
        WITH
        notif_send AS (
        SELECT
            TIMESTAMP(PARSE_DATE('%y%m%d', _table_suffix)) AS ts,
            ghost_user_id,
            SUM(notif_send) AS notif_send,
            SUM(notif_receive) AS notif_receive,
            SUM(notif_receive_in_app) AS notif_receive_in_app,
            SUM(notif_receive_system) AS notif_receive_system,
            SUM(notif_display) AS notif_display,
            SUM(notif_display_in_app) AS notif_display_in_app,
            SUM(notif_display_system) AS notif_display_system,
            SUM(notif_suppression) AS notif_suppression,
            SUM(notif_suppression_in_app) AS notif_suppression_in_app,
            SUM(notif_suppression_system) AS notif_suppression_system,
            SUM(notif_unp) AS notif_unp,
            SUM(notif_unp_in_app) AS notif_unp_in_app,
            SUM(notif_unp_system) AS notif_unp_system,
            SUM(notif_app_open) AS notif_app_open,

            IF(SUM(notif_send)>0,1,0) AS notif_send_uu,
            IF(SUM(notif_receive)>0,1,0) AS notif_receive_uu,
            IF(SUM(notif_receive_in_app)>0,1,0) AS notif_receive_in_app_uu,
            IF(SUM(notif_receive_system)>0,1,0) AS notif_receive_system_uu,
            IF(SUM(notif_display)>0,1,0) AS notif_display_uu,
            IF(SUM(notif_display_in_app)>0,1,0) AS notif_display_in_app_uu,
            IF(SUM(notif_display_system)>0,1,0) AS notif_display_system_uu,
            IF(SUM(notif_suppression)>0,1,0) AS notif_suppression_uu,
            IF(SUM(notif_suppression_in_app)>0,1,0) AS notif_suppression_in_app_uu,
            IF(SUM(notif_suppression_system)>0,1,0) AS notif_suppression_system_uu,
            IF(SUM(notif_unp)>0,1,0) AS notif_unp_uu,
            IF(SUM(notif_unp_in_app)>0,1,0) AS notif_unp_in_app_uu,
            IF(SUM(notif_unp_system)>0,1,0) AS notif_unp_system_uu,
            IF(SUM(notif_app_open)>0,1,0) AS notif_app_open_uu,

            SUM(notif_send_unique_linking_id) AS notif_send_on_any_device,
            SUM(notif_receive_unique_linking_id) AS notif_receive_on_any_device,
            SUM(notif_display_unique_linking_id) AS notif_display_on_any_device,
            SUM(notif_fail_unique_linking_id) AS notif_fail_on_any_device,
            SUM(notif_app_open_unique_linking_id) AS notif_app_open_on_any_device,
            SUM(notif_suppression_unique_linking_id) AS notif_suppression_on_any_device,
            SUM(unp_unique_linking_id) AS unp_on_any_device,
        FROM
            `sc-analytics.report_messaging.notif_notif_send_user_husky_20*`
        WHERE
            _table_suffix BETWEEN '{start_date}' AND '{end_date}'
        GROUP BY
            1,
            2 ),

        notif_enable AS (
        SELECT
            TIMESTAMP(PARSE_DATE('%y%m%d', _table_suffix)) AS ts,
            ghost_user_id,
            1 AS notif_system_update_uu,
            IF(SUM(notif_system_enable)>0,1,0) notif_system_enable_on_any_device,
            IF(SUM(notif_system_disable)>0,1,0) notif_system_disable_on_any_device,
            SUM(notif_system_enable) notif_system_enable_all_device,
            SUM(notif_system_disable) notif_system_disable_all_device,
            SUM(notif_system_enable)+SUM(notif_system_disable) notif_system_update_count,
            COUNTIF(time_sensitive_notification_setting=2) time_sensitive_notification_enable_all_device,
            COUNTIF(time_sensitive_notification_setting=1) time_sensitive_notification_disable_all_device,
            COUNTIF(time_sensitive_notification_setting>=1) time_sensitive_notification_supported_all_device,

        FROM
            `sc-analytics.report_messaging.notif_notif_system_disable_20*`
        WHERE
            _table_suffix BETWEEN '{start_date}' AND '{end_date}'
        GROUP BY
            1,
            2,
            3 )
            
        SELECT
            notif_send.*,
            notif_system_update_uu,
            notif_enable.notif_system_enable_on_any_device,
            notif_enable.notif_system_disable_on_any_device,
            notif_enable.notif_system_enable_all_device,
            notif_enable.notif_system_disable_all_device,
            notif_system_update_count,
            notif_enable.time_sensitive_notification_enable_all_device,
            notif_enable.time_sensitive_notification_disable_all_device,
            notif_enable.time_sensitive_notification_supported_all_device,
        FROM notif_send FULL OUTER JOIN notif_enable
            ON notif_send.ts=notif_enable.ts
            AND notif_send.ghost_user_id=notif_enable.ghost_user_id

        """
    
    def sql_callable(start_date, end_date):
        start_date, end_date = start_date[-6:], end_date[-6:]
        return sql.format(start_date=start_date, end_date=end_date)
    
    mt = MetricTable(
        sql = sql.format(start_date=start_date, end_date=end_date),
        sql_callable = sql_callable,
        metrics=[notif_send, 
                 notif_receive,
                 notif_receive_in_app,
                 notif_receive_system,
                 notif_display,
                 notif_display_in_app,
                 notif_display_system,
                 notif_suppression,
                 notif_suppression_in_app,
                 notif_suppression_system,
                 notif_unp,
                 notif_unp_in_app,
                 notif_unp_system,
                 notif_app_open,
                 notif_receive_rate,
                 notif_display_rate,
                 notif_suppression_rate,
                 notif_unp_rate,
                 notif_in_app_display_rate,
                 notif_in_app_suppression_rate,
                 notif_in_app_unp_rate,
                 notif_system_display_rate,
                 notif_system_suppression_rate,
                 notif_system_unp_rate,
                 notif_app_open_rate,
                 notif_send_uu, 
                 notif_receive_uu,
                 notif_receive_in_app_uu,
                 notif_receive_system_uu,
                 notif_display_uu,
                 notif_display_in_app_uu,
                 notif_display_system_uu,
                 notif_suppression_uu,
                 notif_suppression_in_app_uu,
                 notif_suppression_system_uu,
                 notif_unp_uu,
                 notif_unp_in_app_uu,
                 notif_unp_system_uu,
                 notif_app_open_uu,
                 notif_send_on_any_device,
                 notif_receive_on_any_device,
                 notif_display_on_any_device,
                 notif_fail_on_any_device,
                 notif_app_open_on_any_device,
                 notif_suppression_on_any_device,
                 unp_on_any_device,
                 notif_receive_rate_on_any_device,
                 notif_display_rate_on_any_device,
                 notif_suppression_rate_on_any_device,
                 notif_unp_rate_on_any_device,
                 notif_app_open_rate_on_any_device,
                 notif_system_enable_on_any_device,
                 notif_system_disable_on_any_device,
                 notif_system_enable_all_device,
                 notif_system_disable_all_device,
                 notif_system_enable_rate_on_any_device,
                 notif_system_disable_rate_on_any_device,
                 notif_system_enable_rate_all_device,
                 notif_system_disable_rate_all_device,
                 notif_system_update_uu,
                 notif_system_update_count,
                 time_sensitive_notification_enable_all_device,
                 time_sensitive_notification_disable_all_device,
                 time_sensitive_notification_supported_all_device,
                 time_sensitive_notification_enable_rate_all_device
        ],
        name="relationship_closeness_metrics",
        bq_dialect="standard"
    )
    return mt


def notification_platform_metrics_by_type(start_date, end_date, breakdowns, supplemented_notif_types=""):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
    `sc-analytics.report_messaging.notif_notif_send_user_husky_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )
    
    notif_send = Metric(col="notif_send", dist="cont", daily=True, cumulative=True)
    notif_receive = Metric(col="notif_receive", dist="cont", daily=True, cumulative=True)
    notif_receive_in_app = Metric(col="notif_receive_in_app", dist="cont", daily=True, cumulative=True)
    notif_receive_system = Metric(col="notif_receive_system", dist="cont", daily=True, cumulative=True)
    notif_display = Metric(col="notif_display", dist="cont", daily=True, cumulative=True)
    notif_display_in_app = Metric(col="notif_display_in_app", dist="cont", daily=True, cumulative=True)
    notif_display_system = Metric(col="notif_display_system", dist="cont", daily=True, cumulative=True)
    notif_suppression = Metric(col="notif_suppression", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notif_suppression_in_app = Metric(col="notif_suppression_in_app", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notif_suppression_system = Metric(col="notif_suppression_system", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notif_unp = Metric(col="notif_unp", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notif_unp_in_app = Metric(col="notif_unp_in_app", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notif_unp_system = Metric(col="notif_unp_system", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notif_app_open = Metric(col="notif_app_open", dist="cont", daily=True, cumulative=True)

    notif_receive_rate = Metric(col='notif_receive_rate', numerator='notif_receive', denominator='notif_send', dist='ratio')
    notif_display_rate = Metric(col='notif_display_rate', numerator='notif_display', denominator='notif_receive', dist='ratio')
    notif_suppression_rate = Metric(col='notif_suppression_rate', numerator='notif_suppression', denominator='notif_receive', dist='ratio', desired_direction=NEGATIVE)
    notif_unp_rate = Metric(col='notif_unp_rate', numerator='notif_unp', denominator='notif_receive', dist='ratio', desired_direction=NEGATIVE)
    notif_in_app_display_rate = Metric(col='notif_in_app_display_rate', numerator='notif_display_in_app', denominator='notif_receive_in_app', dist='ratio')
    notif_in_app_suppression_rate = Metric(col='notif_in_app_suppression_rate', numerator='notif_suppression_in_app', denominator='notif_receive_in_app', dist='ratio', desired_direction=NEGATIVE)
    notif_in_app_unp_rate = Metric(col='notif_in_app_unp_rate', numerator='notif_unp_in_app', denominator='notif_receive_in_app', dist='ratio', desired_direction=NEGATIVE)
    notif_system_display_rate = Metric(col='notif_system_display_rate', numerator='notif_display_system', denominator='notif_receive_system', dist='ratio')
    notif_system_suppression_rate = Metric(col='notif_system_suppression_rate', numerator='notif_suppression_system', denominator='notif_receive_system', dist='ratio', desired_direction=NEGATIVE)
    notif_system_unp_rate = Metric(col='notif_system_unp_rate', numerator='notif_unp_system', denominator='notif_receive_system', dist='ratio', desired_direction=NEGATIVE)
    notif_app_open_rate = Metric(col='notif_app_open_rate', numerator='notif_app_open', denominator='notif_display', dist='ratio')

    notif_send_uu = Metric(col="notif_send_uu", dist="cont", daily=True, cumulative=True)
    notif_receive_uu = Metric(col="notif_receive_uu", dist="cont", daily=True, cumulative=True)
    notif_receive_in_app_uu = Metric(col="notif_receive_in_app_uu", dist="cont", daily=True, cumulative=True)
    notif_receive_system_uu = Metric(col="notif_receive_system_uu", dist="cont", daily=True, cumulative=True)
    notif_display_uu = Metric(col="notif_display_uu", dist="cont", daily=True, cumulative=True)
    notif_display_in_app_uu = Metric(col="notif_display_in_app_uu", dist="cont", daily=True, cumulative=True)
    notif_display_system_uu = Metric(col="notif_display_system_uu", dist="cont", daily=True, cumulative=True)
    notif_suppression_uu = Metric(col="notif_suppression_uu", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notif_suppression_in_app_uu = Metric(col="notif_suppression_in_app_uu", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notif_suppression_system_uu = Metric(col="notif_suppression_system_uu", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notif_unp_uu = Metric(col="notif_unp_uu", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notif_unp_in_app_uu = Metric(col="notif_unp_in_app_uu", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notif_unp_system_uu = Metric(col="notif_unp_system_uu", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notif_app_open_uu = Metric(col="notif_app_open_uu", dist="cont", daily=True, cumulative=True)
    
    notif_send_on_any_device = Metric(col="notif_send_on_any_device", dist="cont", daily=True, cumulative=True)
    notif_receive_on_any_device = Metric(col="notif_receive_on_any_device", dist="cont", daily=True, cumulative=True)
    notif_display_on_any_device = Metric(col="notif_display_on_any_device", dist="cont", daily=True, cumulative=True)
    notif_fail_on_any_device = Metric(col="notif_fail_on_any_device", dist="cont", desired_direction=NEGATIVE, daily=True, cumulative=True)
    notif_app_open_on_any_device = Metric(col="notif_app_open_on_any_device", dist="cont", daily=True, cumulative=True)
    notif_suppression_on_any_device = Metric(col="notif_suppression_on_any_device", dist="cont", desired_direction=NEGATIVE, daily=True, cumulative=True)
    unp_on_any_device = Metric(col="unp_on_any_device", dist="cont", desired_direction=NEGATIVE, daily=True, cumulative=True)    
    
    notif_receive_rate_on_any_device = Metric(col='notif_receive_rate_on_any_device', numerator='notif_receive_on_any_device', denominator='notif_send_on_any_device', dist='ratio')
    notif_display_rate_on_any_device = Metric(col='notif_display_rate_on_any_device', numerator='notif_display_on_any_device', denominator='notif_receive_on_any_device', dist='ratio')
    notif_suppression_rate_on_any_device = Metric(col='notif_suppression_rate_on_any_device', numerator='notif_suppression_on_any_device', denominator='notif_receive_on_any_device', dist='ratio', desired_direction=NEGATIVE)
    notif_unp_rate_on_any_device = Metric(col='notif_unp_rate_on_any_device', numerator='unp_on_any_device', denominator='notif_receive_on_any_device', dist='ratio', desired_direction=NEGATIVE)
    notif_app_open_rate_on_any_device = Metric(col='notif_app_open_rate_on_any_device', numerator='notif_app_open_on_any_device', denominator='notif_display_on_any_device', dist='ratio')
    

    if len(supplemented_notif_types)>0:
        mt = FieldBreakdownMetricTable(
            sql="""
            SELECT
            ghost_user_id,
            TIMESTAMP(ts) AS ts,
            CASE WHEN notif_type IN ('CHAT','SNAP','GROUP_CHAT','GROUP_SNAP', 'TYPING', 'GROUP_TYPING', 'STORY', 'STORY_REPLY', 'MAP_LIVE_LOCATION', 'ADDFRIEND', 'SPOTLIGHT_FEED_SUBSCRIPTION_STORY', '{supplemented_notif_types}') THEN notif_type 
            WHEN notif_type LIKE '%AUDIO%' OR notif_type LIKE '%VIDEO%' THEN 'CALLING'
            ELSE 'OTHERS' END AS notif_type,
            SUM(notif_send) AS notif_send,
            SUM(notif_receive) AS notif_receive,
            SUM(notif_receive_in_app) AS notif_receive_in_app,
            SUM(notif_receive_system) AS notif_receive_system,
            SUM(notif_display) AS notif_display,
            SUM(notif_display_in_app) AS notif_display_in_app,
            SUM(notif_display_system) AS notif_display_system,
            SUM(notif_suppression) AS notif_suppression,
            SUM(notif_suppression_in_app) AS notif_suppression_in_app,
            SUM(notif_suppression_system) AS notif_suppression_system,
            SUM(notif_unp) AS notif_unp,
            SUM(notif_unp_in_app) AS notif_unp_in_app,
            SUM(notif_unp_system) AS notif_unp_system,
            SUM(notif_app_open) AS notif_app_open,

            IF(SUM(notif_send)>0,1,0) AS notif_send_uu,
            IF(SUM(notif_receive)>0,1,0) AS notif_receive_uu,
            IF(SUM(notif_receive_in_app)>0,1,0) AS notif_receive_in_app_uu,
            IF(SUM(notif_receive_system)>0,1,0) AS notif_receive_system_uu,
            IF(SUM(notif_display)>0,1,0) AS notif_display_uu,
            IF(SUM(notif_display_in_app)>0,1,0) AS notif_display_in_app_uu,
            IF(SUM(notif_display_system)>0,1,0) AS notif_display_system_uu,
            IF(SUM(notif_suppression)>0,1,0) AS notif_suppression_uu,
            IF(SUM(notif_suppression_in_app)>0,1,0) AS notif_suppression_in_app_uu,
            IF(SUM(notif_suppression_system)>0,1,0) AS notif_suppression_system_uu,
            IF(SUM(notif_unp)>0,1,0) AS notif_unp_uu,
            IF(SUM(notif_unp_in_app)>0,1,0) AS notif_unp_in_app_uu,
            IF(SUM(notif_unp_system)>0,1,0) AS notif_unp_system_uu,
            IF(SUM(notif_app_open)>0,1,0) AS notif_app_open_uu,
            
            SUM(notif_send_unique_linking_id) AS notif_send_on_any_device,
            SUM(notif_receive_unique_linking_id) AS notif_receive_on_any_device,
            SUM(notif_display_unique_linking_id) AS notif_display_on_any_device,
            SUM(notif_fail_unique_linking_id) AS notif_fail_on_any_device,
            SUM(notif_app_open_unique_linking_id) AS notif_app_open_on_any_device,
            SUM(notif_suppression_unique_linking_id) AS notif_suppression_on_any_device,
            SUM(unp_unique_linking_id) AS unp_on_any_device,
            

            FROM
            {source_table}
            GROUP BY 1, 2, 3
            """.format(source_table=source_table, supplemented_notif_types=supplemented_notif_types),
            metrics=[notif_send, 
                    notif_receive,
                    notif_receive_in_app,
                    notif_receive_system,
                    notif_display,
                    notif_display_in_app,
                    notif_display_system,
                    notif_suppression,
                    notif_suppression_in_app,
                    notif_suppression_system,
                    notif_unp,
                    notif_unp_in_app,
                    notif_unp_system,
                    notif_app_open,
                    notif_receive_rate,
                    notif_display_rate,
                    notif_suppression_rate,
                    notif_unp_rate,
                    notif_in_app_display_rate,
                    notif_in_app_suppression_rate,
                    notif_in_app_unp_rate,
                    notif_system_display_rate,
                    notif_system_suppression_rate,
                    notif_system_unp_rate,
                    notif_app_open_rate,
                    notif_send_uu, 
                    notif_receive_uu,
                    notif_receive_in_app_uu,
                    notif_receive_system_uu,
                    notif_display_uu,
                    notif_display_in_app_uu,
                    notif_display_system_uu,
                    notif_suppression_uu,
                    notif_suppression_in_app_uu,
                    notif_suppression_system_uu,
                    notif_unp_uu,
                    notif_unp_in_app_uu,
                    notif_unp_system_uu,
                    notif_app_open_uu,                     
                    notif_send_on_any_device,
                    notif_receive_on_any_device,
                    notif_display_on_any_device,
                    notif_fail_on_any_device,
                    notif_app_open_on_any_device,
                    notif_suppression_on_any_device,
                    unp_on_any_device,
                    notif_receive_rate_on_any_device,
                    notif_display_rate_on_any_device,
                    notif_suppression_rate_on_any_device,
                    notif_unp_rate_on_any_device,
                    notif_app_open_rate_on_any_device
            ],
            name="notification_platform_metrics",
            breakdowns=breakdowns,
            bq_dialect="standard"
        )
    else:
        mt = FieldBreakdownMetricTable(
            sql="""
            SELECT
            ghost_user_id,
            TIMESTAMP(ts) AS ts,
            CASE WHEN notif_type IN ('CHAT','SNAP','GROUP_CHAT','GROUP_SNAP', 'TYPING', 'GROUP_TYPING', 'STORY', 'STORY_REPLY', 'MAP_LIVE_LOCATION', 'ADDFRIEND', 'SPOTLIGHT_FEED_SUBSCRIPTION_STORY') THEN notif_type 
            WHEN notif_type LIKE '%AUDIO%' OR notif_type LIKE '%VIDEO%' THEN 'CALLING'
            ELSE 'OTHERS' END AS notif_type,
            SUM(notif_send) AS notif_send,
            SUM(notif_receive) AS notif_receive,
            SUM(notif_receive_in_app) AS notif_receive_in_app,
            SUM(notif_receive_system) AS notif_receive_system,
            SUM(notif_display) AS notif_display,
            SUM(notif_display_in_app) AS notif_display_in_app,
            SUM(notif_display_system) AS notif_display_system,
            SUM(notif_suppression) AS notif_suppression,
            SUM(notif_suppression_in_app) AS notif_suppression_in_app,
            SUM(notif_suppression_system) AS notif_suppression_system,
            SUM(notif_unp) AS notif_unp,
            SUM(notif_unp_in_app) AS notif_unp_in_app,
            SUM(notif_unp_system) AS notif_unp_system,
            SUM(notif_app_open) AS notif_app_open,

            IF(SUM(notif_send)>0,1,0) AS notif_send_uu,
            IF(SUM(notif_receive)>0,1,0) AS notif_receive_uu,
            IF(SUM(notif_receive_in_app)>0,1,0) AS notif_receive_in_app_uu,
            IF(SUM(notif_receive_system)>0,1,0) AS notif_receive_system_uu,
            IF(SUM(notif_display)>0,1,0) AS notif_display_uu,
            IF(SUM(notif_display_in_app)>0,1,0) AS notif_display_in_app_uu,
            IF(SUM(notif_display_system)>0,1,0) AS notif_display_system_uu,
            IF(SUM(notif_suppression)>0,1,0) AS notif_suppression_uu,
            IF(SUM(notif_suppression_in_app)>0,1,0) AS notif_suppression_in_app_uu,
            IF(SUM(notif_suppression_system)>0,1,0) AS notif_suppression_system_uu,
            IF(SUM(notif_unp)>0,1,0) AS notif_unp_uu,
            IF(SUM(notif_unp_in_app)>0,1,0) AS notif_unp_in_app_uu,
            IF(SUM(notif_unp_system)>0,1,0) AS notif_unp_system_uu,
            IF(SUM(notif_app_open)>0,1,0) AS notif_app_open_uu,

            SUM(notif_send_unique_linking_id) AS notif_send_on_any_device,
            SUM(notif_receive_unique_linking_id) AS notif_receive_on_any_device,
            SUM(notif_display_unique_linking_id) AS notif_display_on_any_device,
            SUM(notif_fail_unique_linking_id) AS notif_fail_on_any_device,
            SUM(notif_app_open_unique_linking_id) AS notif_app_open_on_any_device,
            SUM(notif_suppression_unique_linking_id) AS notif_suppression_on_any_device,
            SUM(unp_unique_linking_id) AS unp_on_any_device,

            FROM
            {source_table}
            GROUP BY 1, 2, 3
            """.format(source_table=source_table),
            metrics=[notif_send, 
                    notif_receive,
                    notif_receive_in_app,
                    notif_receive_system,
                    notif_display,
                    notif_display_in_app,
                    notif_display_system,
                    notif_suppression,
                    notif_suppression_in_app,
                    notif_suppression_system,
                    notif_unp,
                    notif_unp_in_app,
                    notif_unp_system,
                    notif_app_open,
                    notif_receive_rate,
                    notif_display_rate,
                    notif_suppression_rate,
                    notif_unp_rate,
                    notif_in_app_display_rate,
                    notif_in_app_suppression_rate,
                    notif_in_app_unp_rate,
                    notif_system_display_rate,
                    notif_system_suppression_rate,
                    notif_system_unp_rate,
                    notif_app_open_rate,
                    notif_send_uu, 
                    notif_receive_uu,
                    notif_receive_in_app_uu,
                    notif_receive_system_uu,
                    notif_display_uu,
                    notif_display_in_app_uu,
                    notif_display_system_uu,
                    notif_suppression_uu,
                    notif_suppression_in_app_uu,
                    notif_suppression_system_uu,
                    notif_unp_uu,
                    notif_unp_in_app_uu,
                    notif_unp_system_uu,
                    notif_app_open_uu,
                    notif_send_on_any_device,
                    notif_receive_on_any_device,
                    notif_display_on_any_device,
                    notif_fail_on_any_device,
                    notif_app_open_on_any_device,
                    notif_suppression_on_any_device,
                    unp_on_any_device,
                    notif_receive_rate_on_any_device,
                    notif_display_rate_on_any_device,
                    notif_suppression_rate_on_any_device,
                    notif_unp_rate_on_any_device,
                    notif_app_open_rate_on_any_device
            ],
            name="notification_platform_metrics",
            breakdowns=breakdowns,
            bq_dialect="standard"
        )

    return mt



def notification_to_message_ready_failure_metrics(start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    notification_to_message_ready_snap_fail=Metric(col="notification_to_message_ready_snap_fail", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notification_to_message_ready_snap_fail_prefetch_not_started_step=Metric(col="notification_to_message_ready_snap_fail_prefetch_not_started_step", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notification_to_message_ready_snap_fail_prefetch_step=Metric(col="notification_to_message_ready_snap_fail_prefetch_step", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notification_to_message_ready_snap_fail_target_screen_step=Metric(col="notification_to_message_ready_snap_fail_target_screen_step", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notification_to_message_ready_snap_fail_sync_step=Metric(col="notification_to_message_ready_snap_fail_sync_step", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
  
    notification_to_message_ready_chat_fail=Metric(col="notification_to_message_ready_chat_fail", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notification_to_message_ready_chat_fail_prefetch_not_started_step=Metric(col="notification_to_message_ready_chat_fail_prefetch_not_started_step", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notification_to_message_ready_chat_fail_prefetch_step=Metric(col="notification_to_message_ready_chat_fail_prefetch_step", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notification_to_message_ready_chat_fail_target_screen_step=Metric(col="notification_to_message_ready_chat_fail_target_screen_step", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)
    notification_to_message_ready_chat_fail_sync_step=Metric(col="notification_to_message_ready_chat_fail_sync_step", dist="cont", daily=True, cumulative=True, desired_direction=NEGATIVE)

    sql="""
        SELECT 
            ghost_user_id,
            TIMESTAMP(PARSE_DATE('%Y%m%d', _table_suffix)) as ts, 
            sum(notification_to_message_ready_snap_fail) as  notification_to_message_ready_snap_fail,
            sum(notification_to_message_ready_snap_fail_prefetch_not_started_step) as  notification_to_message_ready_snap_fail_prefetch_not_started_step,
            sum(notification_to_message_ready_snap_fail_prefetch_step) as  notification_to_message_ready_snap_fail_prefetch_step,
            sum(notification_to_message_ready_snap_fail_target_screen_step) as  notification_to_message_ready_snap_fail_target_screen_step,
            sum(notification_to_message_ready_snap_fail_sync_step) as  notification_to_message_ready_snap_fail_sync_step,
            sum(notification_to_message_ready_chat_fail) as  notification_to_message_ready_chat_fail,
            sum(notification_to_message_ready_chat_fail_prefetch_not_started_step) as  notification_to_message_ready_chat_fail_prefetch_not_started_step,
            sum(notification_to_message_ready_chat_fail_prefetch_step) as  notification_to_message_ready_chat_fail_prefetch_step,
            sum(notification_to_message_ready_chat_fail_target_screen_step) as  notification_to_message_ready_chat_fail_target_screen_step,
            sum(notification_to_message_ready_chat_fail_sync_step) as  notification_to_message_ready_chat_fail_sync_step,
        FROM
           `sc-analytics.report_messaging.notification_to_message_ready_activity_detail_*`
    WHERE _table_suffix between '{start_date}' and '{end_date}'
        GROUP BY 1,2
        """
    
    def sql_callable(start_date, end_date):
#         start_date, end_date = start_date[-6:], end_date[-6:]
        return sql.format(start_date=start_date, end_date=end_date)
    
    mt = MetricTable(
        sql = sql.format(start_date=start_date, end_date=end_date),
        sql_callable = sql_callable,
        metrics=[notification_to_message_ready_snap_fail,
                notification_to_message_ready_snap_fail_prefetch_not_started_step,
                notification_to_message_ready_snap_fail_prefetch_step,
                notification_to_message_ready_snap_fail_target_screen_step,
                notification_to_message_ready_snap_fail_sync_step,
                notification_to_message_ready_chat_fail,
                notification_to_message_ready_chat_fail_prefetch_not_started_step,
                notification_to_message_ready_chat_fail_prefetch_step,
                notification_to_message_ready_chat_fail_target_screen_step,
                notification_to_message_ready_chat_fail_sync_step],
        name="notification_to_message_ready_failure_metrics",
        inner_join_with_mapping=False,
        bq_dialect="standard"
    )
    return mt



notification_to_message_ready_latency_SQL = """
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        ghost_user_id,
        notif_tap_to_message_ready_chat_summed_success_latency,
        notif_tap_to_message_ready_chat_app_startup_latency,
        notif_tap_to_message_ready_chat_enter_target_screen_latency,
        notif_tap_to_message_ready_chat_sync_latency,
        notif_tap_to_message_ready_chat_prefetch_started_latency,
        notif_tap_to_message_ready_chat_prefetch_latency,
        notif_tap_to_message_ready_snap_summed_success_latency,
        notif_tap_to_message_ready_snap_app_startup_latency,
        notif_tap_to_message_ready_snap_enter_target_screen_latency,
        notif_tap_to_message_ready_snap_sync_latency,
        notif_tap_to_message_ready_snap_prefetch_started_latency,
        notif_tap_to_message_ready_snap_prefetch_latency,
        FROM
            `sc-analytics.report_messaging.notification_to_message_ready_activity_detail_*`
            WHERE _table_suffix between '{start}' and '{end}'
          """

LATENCY_METRICS_LIST = [
    'notif_tap_to_message_ready_chat_summed_success_latency',
    'notif_tap_to_message_ready_chat_app_startup_latency',
    'notif_tap_to_message_ready_chat_enter_target_screen_latency',
    'notif_tap_to_message_ready_chat_sync_latency',
    'notif_tap_to_message_ready_chat_prefetch_started_latency',
    'notif_tap_to_message_ready_chat_prefetch_latency',
    'notif_tap_to_message_ready_snap_summed_success_latency',
    'notif_tap_to_message_ready_snap_app_startup_latency',
    'notif_tap_to_message_ready_snap_enter_target_screen_latency',
    'notif_tap_to_message_ready_snap_sync_latency',
    'notif_tap_to_message_ready_snap_prefetch_started_latency',
    'notif_tap_to_message_ready_snap_prefetch_latency'
]


def notification_to_message_ready_latency_metrics(start_date, end_date):
    return MetricTable(
        sql=notification_to_message_ready_latency_SQL.format(
            start=start_date, end=end_date),
        metrics=[
            Metric(col=col, dist='quantile', desired_direction=NEGATIVE) for col in LATENCY_METRICS_LIST
        ],
        quantile_metrics=True,
        bq_dialect='standard',
        name='notification_to_message_ready_latency_metrics'
    )   


def notification_os_breakdown_metrics(start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    notif_send_ios = Metric(col="notif_send_ios", dist="cont", daily=True, cumulative=True)
    notif_send_android = Metric(col="notif_send_android", dist="cont", daily=True, cumulative=True)
    notif_send_web = Metric(col="notif_send_web", dist="cont", daily=True, cumulative=True)

    notif_receive_ios = Metric(col="notif_receive_ios", dist="cont", daily=True, cumulative=True)
    notif_receive_android = Metric(col="notif_receive_android", dist="cont", daily=True, cumulative=True)
    notif_receive_web = Metric(col="notif_receive_web", dist="cont", daily=True, cumulative=True)

    notif_display_ios = Metric(col="notif_display_ios", dist="cont", daily=True, cumulative=True)
    notif_display_android = Metric(col="notif_display_android", dist="cont", daily=True, cumulative=True)
    notif_display_web = Metric(col="notif_display_web", dist="cont", daily=True, cumulative=True)

    notif_fail_ios = Metric(col="notif_fail_ios", dist="cont", daily=True, cumulative=True)
    notif_fail_android = Metric(col="notif_fail_android", dist="cont", daily=True, cumulative=True)
    notif_fail_web = Metric(col="notif_fail_web", dist="cont", daily=True, cumulative=True)
    
    sql="""
        SELECT 
            ghost_user_id,
            TIMESTAMP(ts) AS ts,
            SUM(notif_send_ios) as notif_send_ios,
            SUM(notif_send_android) as notif_send_android,
            SUM(notif_send_web) as notif_send_web,
            SUM(notif_receive_ios) as notif_receive_ios,
            SUM(notif_receive_android) as notif_receive_android,
            SUM(notif_receive_web) as notif_receive_web,
            SUM(notif_display_ios) as notif_display_ios,
            SUM(notif_display_android) as notif_display_android,
            SUM(notif_display_web) as notif_display_web,
            SUM(notif_fail_ios) as notif_fail_ios,
            SUM(notif_fail_android) as notif_fail_android,
            SUM(notif_fail_web) as notif_fail_web
        FROM
          `sc-analytics.report_messaging.notif_notif_send_user_husky_*`
        WHERE _table_suffix between '{start_date}' and '{end_date}'
        GROUP BY 1,2
        """
    
    def sql_callable(start_date, end_date):
#         start_date, end_date = start_date[-6:], end_date[-6:]
        return sql.format(start_date=start_date, end_date=end_date)

    mt = MetricTable(
        sql = sql.format(start_date=start_date, end_date=end_date),
        sql_callable = sql_callable,
        metrics=[
                notif_send_ios,
                notif_send_android,
                notif_send_web,
                
                notif_receive_ios,
                notif_receive_android,
                notif_receive_web,

                notif_display_ios,
                notif_display_android,
                notif_display_web,

                notif_fail_ios,
                notif_fail_android,
                notif_fail_web
                ],
        name="notification_os_breakdown_metrics",
        inner_join_with_mapping=False,
        bq_dialect="standard"
    )
    return mt



def friend_feed_state_metrics(start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    ff_page_session_count = Metric(col="ff_page_session_count", dist="cont", daily=True, cumulative=True)
    app_session_count_w_ff_page_open = Metric(col="app_session_count_w_ff_page_open", dist="cont", daily=True, cumulative=True)
    ff_page_session_per_app_session = Metric(col='ff_page_session_per_app_session', numerator='ff_page_session_count', denominator='app_session_count_w_ff_page_open', dist='ratio')

    ff_page_session_w_no_unread_convo = Metric(col="ff_page_session_w_no_unread_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_1_plus_unread_convo = Metric(col="ff_page_session_w_1_plus_unread_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_no_unread_chat = Metric(col="ff_page_session_w_no_unread_chat", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_1_plus_unread_chat = Metric(col="ff_page_session_w_1_plus_unread_chat", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_6_plus_unread_chat = Metric(col="ff_page_session_w_6_plus_unread_chat", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_no_unread_snap = Metric(col="ff_page_session_w_no_unread_snap", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_1_plus_unread_snap = Metric(col="ff_page_session_w_1_plus_unread_snap", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_6_plus_unread_snap = Metric(col="ff_page_session_w_6_plus_unread_snap", dist="cont", daily=True, cumulative=True)

    ff_page_session_w_no_visible_cell = Metric(col="ff_page_session_w_no_visible_cell", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_1_plus_visible_cell = Metric(col="ff_page_session_w_1_plus_visible_cell", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_11_plus_visible_cell = Metric(col="ff_page_session_w_11_plus_visible_cell", dist="cont", daily=True, cumulative=True)

    ff_page_session_w_page_view_id_1 = Metric(col="ff_page_session_w_page_view_id_1", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_page_view_id_2 = Metric(col="ff_page_session_w_page_view_id_2", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_page_view_id_3_plus = Metric(col="ff_page_session_w_page_view_id_3_plus", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_1_convo_pinned = Metric(col="ff_page_session_w_1_convo_pinned", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_2_convo_pinned = Metric(col="ff_page_session_w_2_convo_pinned", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_3_convo_pinned = Metric(col="ff_page_session_w_3_convo_pinned", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_4_plus_convo_pinned = Metric(col="ff_page_session_w_4_plus_convo_pinned", dist="cont", daily=True, cumulative=True)
    ff_page_session_app_ui_5_tab = Metric(col="ff_page_session_app_ui_5_tab", dist="cont", daily=True, cumulative=True)
    ff_page_session_app_ui_3_tab = Metric(col="ff_page_session_app_ui_3_tab", dist="cont", daily=True, cumulative=True)

    sql="""
        SELECT 
            ghost_user_id,
            TIMESTAMP(event_date) as ts,
            ff_page_session_count,
            app_session_count as app_session_count_w_ff_page_open,
            ff_page_session_unread_conversations_0 as ff_page_session_w_no_unread_convo,
            ff_page_session_unread_conversations_1_plus as ff_page_session_w_1_plus_unread_convo,
            ff_page_session_unread_chats_0 as ff_page_session_w_no_unread_chat,
            ff_page_session_unread_chats_1_plus as ff_page_session_w_1_plus_unread_chat,
            ff_page_session_unread_chats_6_plus as ff_page_session_w_6_plus_unread_chat,
            ff_page_session_unread_snaps_0 as ff_page_session_w_no_unread_snap,
            ff_page_session_unread_snaps_1_plus as ff_page_session_w_1_plus_unread_snap,
            ff_page_session_unread_snaps_6_plus as ff_page_session_w_6_plus_unread_snap,
            ff_page_session_visible_cells_0 as ff_page_session_w_no_visible_cell,
            ff_page_session_visible_cells_1_plus as ff_page_session_w_1_plus_visible_cell,
            ff_page_session_visible_cells_11_plus as ff_page_session_w_11_plus_visible_cell,

            ff_page_session_w_page_view_id_1 as ff_page_session_w_page_view_id_1,
            ff_page_session_w_page_view_id_2 as ff_page_session_w_page_view_id_2,
            ff_page_session_w_page_view_id_3_plus as ff_page_session_w_page_view_id_3_plus,
            ff_page_session_w_convo_pinned_1 as ff_page_session_w_1_convo_pinned,
            ff_page_session_w_convo_pinned_2 as ff_page_session_w_2_convo_pinned,
            ff_page_session_w_convo_pinned_3 as ff_page_session_w_3_convo_pinned,
            ff_page_session_w_convo_pinned_4_plus as ff_page_session_w_4_plus_convo_pinned,
            ff_page_session_app_ui_5_tab as ff_page_session_app_ui_5_tab,
            ff_page_session_app_ui_3_tab as ff_page_session_app_ui_3_tab,

        FROM
          `sc-analytics.report_messaging.friends_feed_page_open_user_level_20*`
    WHERE concat('20',_table_suffix) between '{start_date}' and '{end_date}'
        """
    
    def sql_callable(start_date, end_date):
#         start_date, end_date = start_date[-6:], end_date[-6:]
        return sql.format(start_date=start_date, end_date=end_date)
    
    mt = MetricTable(
        sql = sql.format(start_date=start_date, end_date=end_date),
        sql_callable = sql_callable,
        metrics=[
                ff_page_session_count,
                app_session_count_w_ff_page_open,
                ff_page_session_per_app_session,
                ff_page_session_w_no_unread_convo,
                ff_page_session_w_1_plus_unread_convo,
                ff_page_session_w_no_unread_chat,
                ff_page_session_w_1_plus_unread_chat,
                ff_page_session_w_6_plus_unread_chat,
                ff_page_session_w_no_unread_snap,
                ff_page_session_w_1_plus_unread_snap,
                ff_page_session_w_6_plus_unread_snap,
                ff_page_session_w_no_visible_cell,
                ff_page_session_w_1_plus_visible_cell,
                ff_page_session_w_11_plus_visible_cell,
                ff_page_session_w_page_view_id_1,
                ff_page_session_w_page_view_id_2,
                ff_page_session_w_page_view_id_3_plus,
                ff_page_session_w_1_convo_pinned,
                ff_page_session_w_2_convo_pinned,
                ff_page_session_w_3_convo_pinned,
                ff_page_session_w_4_plus_convo_pinned,
                ff_page_session_app_ui_5_tab,
                ff_page_session_app_ui_3_tab
                
                ],
        name="friend_feed_state_metrics",
        inner_join_with_mapping=False,
        bq_dialect="standard"
    )
    return mt

def friend_feed_state_active_day_metrics(start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    ff_page_session_active_day = Metric(col="ff_page_session_active_day", dist="cont", daily=True, cumulative=True)
    app_session_w_ff_page_open_active_day = Metric(col="app_session_w_ff_page_open_active_day", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_no_unread_convo_active_day = Metric(col="ff_page_session_w_no_unread_convo_active_day", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_1_plus_unread_convo_active_day = Metric(col="ff_page_session_w_1_plus_unread_convo_active_day", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_no_unread_chat_active_day = Metric(col="ff_page_session_w_no_unread_chat_active_day", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_1_plus_unread_chat_active_day = Metric(col="ff_page_session_w_1_plus_unread_chat_active_day", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_6_plus_unread_chat_active_day = Metric(col="ff_page_session_w_6_plus_unread_chat_active_day", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_no_unread_snap_active_day = Metric(col="ff_page_session_w_no_unread_snap_active_day", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_1_plus_unread_snap_active_day = Metric(col="ff_page_session_w_1_plus_unread_snap_active_day", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_6_plus_unread_snap_active_day = Metric(col="ff_page_session_w_6_plus_unread_snap_active_day", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_no_visible_cell_active_day = Metric(col="ff_page_session_w_no_visible_cell_active_day", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_1_plus_visible_cell_active_day = Metric(col="ff_page_session_w_1_plus_visible_cell_active_day", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_11_plus_visible_cell_active_day = Metric(col="ff_page_session_w_11_plus_visible_cell_active_day", dist="cont", daily=True, cumulative=True)

    ff_page_session_w_page_view_id_1_active_day = Metric(col="ff_page_session_w_page_view_id_1_active_day", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_page_view_id_2_active_day = Metric(col="ff_page_session_w_page_view_id_2_active_day", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_page_view_id_3_plus_active_day = Metric(col="ff_page_session_w_page_view_id_3_plus_active_day", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_1_convo_pinned_active_day = Metric(col="ff_page_session_w_1_convo_pinned_active_day", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_2_convo_pinned_active_day = Metric(col="ff_page_session_w_2_convo_pinned_active_day", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_3_convo_pinned_active_day = Metric(col="ff_page_session_w_3_convo_pinned_active_day", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_4_plus_convo_pinned_active_day = Metric(col="ff_page_session_w_4_plus_convo_pinned_active_day", dist="cont", daily=True, cumulative=True)
    ff_page_session_app_ui_5_tab_active_day = Metric(col="ff_page_session_app_ui_5_tab_active_day", dist="cont", daily=True, cumulative=True)
    ff_page_session_app_ui_3_tab_active_day = Metric(col="ff_page_session_app_ui_3_tab_active_day", dist="cont", daily=True, cumulative=True)

    sql="""
        SELECT 
            ghost_user_id,
            TIMESTAMP(event_date) as ts,
            IF(ff_page_session_count>0,1,0) ff_page_session_active_day,
            IF(app_session_count>0,1,0) app_session_w_ff_page_open_active_day,

            IF(ff_page_session_unread_conversations_0>0,1,0) ff_page_session_w_no_unread_convo_active_day,
            IF(ff_page_session_unread_conversations_1_plus>0,1,0) ff_page_session_w_1_plus_unread_convo_active_day,
            IF(ff_page_session_unread_chats_0>0,1,0) ff_page_session_w_no_unread_chat_active_day,
            IF(ff_page_session_unread_chats_1_plus>0,1,0) ff_page_session_w_1_plus_unread_chat_active_day,
            IF(ff_page_session_unread_chats_6_plus>0,1,0) ff_page_session_w_6_plus_unread_chat_active_day,
            IF(ff_page_session_unread_snaps_0>0,1,0) ff_page_session_w_no_unread_snap_active_day,
            IF(ff_page_session_unread_snaps_1_plus>0,1,0) ff_page_session_w_1_plus_unread_snap_active_day,
            IF(ff_page_session_unread_snaps_6_plus>0,1,0) ff_page_session_w_6_plus_unread_snap_active_day,
            IF(ff_page_session_visible_cells_0>0,1,0) ff_page_session_w_no_visible_cell_active_day,
            IF(ff_page_session_visible_cells_1_plus>0,1,0) ff_page_session_w_1_plus_visible_cell_active_day,
            IF(ff_page_session_visible_cells_11_plus>0,1,0) ff_page_session_w_11_plus_visible_cell_active_day,

            IF(ff_page_session_w_page_view_id_1>0,1,0) as ff_page_session_w_page_view_id_1_active_day,
            IF(ff_page_session_w_page_view_id_2>0,1,0) as ff_page_session_w_page_view_id_2_active_day,
            IF(ff_page_session_w_page_view_id_3_plus>0,1,0) as ff_page_session_w_page_view_id_3_plus_active_day,
            IF(ff_page_session_w_convo_pinned_1>0,1,0) as ff_page_session_w_1_convo_pinned_active_day,
            IF(ff_page_session_w_convo_pinned_2>0,1,0) as ff_page_session_w_2_convo_pinned_active_day,
            IF(ff_page_session_w_convo_pinned_3>0,1,0) as ff_page_session_w_3_convo_pinned_active_day,
            IF(ff_page_session_w_convo_pinned_4_plus>0,1,0) as ff_page_session_w_4_plus_convo_pinned_active_day,
            IF(ff_page_session_app_ui_5_tab>0,1,0) as ff_page_session_app_ui_5_tab_active_day,
            IF(ff_page_session_app_ui_3_tab>0,1,0) as ff_page_session_app_ui_3_tab_active_day,

        FROM
          `sc-analytics.report_messaging.friends_feed_page_open_user_level_20*`
    WHERE concat('20',_table_suffix) between '{start_date}' and '{end_date}'
        """
    
    def sql_callable(start_date, end_date):
#         start_date, end_date = start_date[-6:], end_date[-6:]
        return sql.format(start_date=start_date, end_date=end_date)

    mt = MetricTable(
        sql = sql.format(start_date=start_date, end_date=end_date),
        sql_callable = sql_callable,
        metrics=[
                ff_page_session_active_day,
                app_session_w_ff_page_open_active_day,
                ff_page_session_w_no_unread_convo_active_day,
                ff_page_session_w_1_plus_unread_convo_active_day,
                ff_page_session_w_no_unread_chat_active_day,
                ff_page_session_w_1_plus_unread_chat_active_day,
                ff_page_session_w_6_plus_unread_chat_active_day,
                ff_page_session_w_no_unread_snap_active_day,
                ff_page_session_w_1_plus_unread_snap_active_day,
                ff_page_session_w_6_plus_unread_snap_active_day,
                ff_page_session_w_no_visible_cell_active_day,
                ff_page_session_w_1_plus_visible_cell_active_day,
                ff_page_session_w_11_plus_visible_cell_active_day,

                ff_page_session_w_page_view_id_1_active_day,
                ff_page_session_w_page_view_id_2_active_day,
                ff_page_session_w_page_view_id_3_plus_active_day,
                ff_page_session_w_1_convo_pinned_active_day,
                ff_page_session_w_2_convo_pinned_active_day,
                ff_page_session_w_3_convo_pinned_active_day,
                ff_page_session_w_4_plus_convo_pinned_active_day,
                ff_page_session_app_ui_5_tab_active_day,
                ff_page_session_app_ui_3_tab_active_day

                ],
        name="friend_feed_state_active_day_metrics",
        inner_join_with_mapping=False,
        bq_dialect="standard"
    )
    return mt


def friend_feed_state_convo_metrics(start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    ff_page_session_count = Metric(col="ff_page_session_count", dist="cont", daily=True, cumulative=True)
    total_unread_convo = Metric(col="total_unread_convo", dist="cont", daily=True, cumulative=True)
    total_new_unread_convo = Metric(col="total_new_unread_convo", dist="cont", daily=True, cumulative=True)
    total_unread_chat_convo = Metric(col="total_unread_chat_convo", dist="cont", daily=True, cumulative=True)
    total_unread_snap_convo = Metric(col="total_unread_snap_convo", dist="cont", daily=True, cumulative=True)
    total_pinned_convo = Metric(col="total_pinned_convo", dist="cont", daily=True, cumulative=True)
    
    total_unread_convo_per_ff_session = Metric(col='total_unread_convo_per_ff_session', numerator='total_unread_convo', denominator='ff_page_session_count', dist='ratio')
    total_new_unread_convo_per_ff_session = Metric(col='total_new_unread_convo_per_ff_session', numerator='total_new_unread_convo', denominator='ff_page_session_count', dist='ratio')
    total_unread_chat_convo_per_ff_session = Metric(col='total_unread_chat_convo_per_ff_session', numerator='total_unread_chat_convo', denominator='ff_page_session_count', dist='ratio')
    total_unread_snap_convo_per_ff_session = Metric(col='total_unread_snap_convo_per_ff_session', numerator='total_unread_snap_convo', denominator='ff_page_session_count', dist='ratio')
    total_pinned_convo_per_ff_session = Metric(col='total_pinned_convo_per_ff_session', numerator='total_pinned_convo', denominator='ff_page_session_count', dist='ratio')

    total_visible_cells = Metric(col="total_visible_cells", dist="cont", daily=True, cumulative=True)
    total_visible_unread_chat_cells = Metric(col="total_visible_unread_chat_cells", dist="cont", daily=True, cumulative=True)
    total_visible_unread_snap_cells = Metric(col="total_visible_unread_snap_cells", dist="cont", daily=True, cumulative=True)
    total_visible_cells_per_ff_session = Metric(col='total_visible_cells_per_ff_session', numerator='total_visible_cells', denominator='ff_page_session_count', dist='ratio')
    total_visible_unread_chat_cells_per_ff_session = Metric(col='total_visible_unread_chat_cells_per_ff_session', numerator='total_visible_unread_chat_cells', denominator='ff_page_session_count', dist='ratio')
    total_visible_unread_snap_cells_per_ff_session = Metric(col='total_visible_unread_snap_cells_per_ff_session', numerator='total_visible_unread_snap_cells', denominator='ff_page_session_count', dist='ratio')

    cell_count_w_state_read_chat = Metric(col="cell_count_w_state_read_chat", dist="cont", daily=True, cumulative=True)
    cell_count_w_state_read_snap_slient = Metric(col="cell_count_w_state_read_snap_slient", dist="cont", daily=True, cumulative=True)
    cell_count_w_state_new_snap_slient = Metric(col="cell_count_w_state_new_snap_slient", dist="cont", daily=True, cumulative=True)
    cell_count_w_state_new_snap_audio = Metric(col="cell_count_w_state_new_snap_audio", dist="cont", daily=True, cumulative=True)
    cell_count_w_state_read_snap_audio = Metric(col="cell_count_w_state_read_snap_audio", dist="cont", daily=True, cumulative=True)
    cell_count_w_state_new_chat = Metric(col="cell_count_w_state_new_chat", dist="cont", daily=True, cumulative=True)
    cell_count_w_state_read_reaction = Metric(col="cell_count_w_state_read_reaction", dist="cont", daily=True, cumulative=True)
    cell_count_w_state_new_snap_and_chat = Metric(col="cell_count_w_state_new_snap_and_chat", dist="cont", daily=True, cumulative=True)
    cell_count_w_state_missed_call = Metric(col="cell_count_w_state_missed_call", dist="cont", daily=True, cumulative=True)
    cell_count_w_state_typing = Metric(col="cell_count_w_state_typing", dist="cont", daily=True, cumulative=True)
    cell_count_w_state_active_call = Metric(col="cell_count_w_state_active_call", dist="cont", daily=True, cumulative=True)
    cell_count_w_state_new_reaction = Metric(col="cell_count_w_state_new_reaction", dist="cont", daily=True, cumulative=True)
    cell_count_w_state_incoming_call = Metric(col="cell_count_w_state_incoming_call", dist="cont", daily=True, cumulative=True)

    sql="""
        SELECT 
            ghost_user_id,
            TIMESTAMP(event_date) as ts,
            ff_page_session_count,

            total_number_unread_conversations as total_unread_convo,
            total_number_new_unread_conversations as total_new_unread_convo,
            total_number_unread_chats as total_unread_chat_convo,
            total_number_unread_snaps as total_unread_snap_convo,
            total_number_conversations_pinned as total_pinned_convo,

            total_visible_cells,
            total_visible_unread_chat_cells,
            total_visible_unread_snap_cells,

            cell_state_count_read_chat as cell_count_w_state_read_chat,
            cell_state_count_read_snap_slient as cell_count_w_state_read_snap_slient,
            cell_state_count_new_snap_slient as cell_count_w_state_new_snap_slient,
            cell_state_count_new_snap_audio as cell_count_w_state_new_snap_audio,
            cell_state_count_read_snap_audio as cell_count_w_state_read_snap_audio,
            cell_state_count_new_chat as cell_count_w_state_new_chat,
            cell_state_count_read_reaction as cell_count_w_state_read_reaction,
            cell_state_count_new_snap_and_chat as cell_count_w_state_new_snap_and_chat,
            cell_state_count_missed_call as cell_count_w_state_missed_call,
            cell_state_count_typing as cell_count_w_state_typing,
            cell_state_count_active_call as cell_count_w_state_active_call,
            cell_state_count_new_reaction as cell_count_w_state_new_reaction,
            cell_state_count_incoming_call as cell_count_w_state_incoming_call

        FROM
          `sc-analytics.report_messaging.friends_feed_page_open_user_level_20*`
    WHERE concat('20',_table_suffix) between '{start_date}' and '{end_date}'
        """
    
    def sql_callable(start_date, end_date):
#         start_date, end_date = start_date[-6:], end_date[-6:]
        return sql.format(start_date=start_date, end_date=end_date)    
    
    mt = MetricTable(
        sql = sql.format(start_date=start_date, end_date=end_date),
        sql_callable = sql_callable,
        metrics=[
                ff_page_session_count,
                total_unread_convo,
                total_new_unread_convo,
                total_unread_chat_convo,
                total_unread_snap_convo,
                total_pinned_convo,
                total_unread_convo_per_ff_session,
                total_new_unread_convo_per_ff_session,
                total_unread_chat_convo_per_ff_session,
                total_unread_snap_convo_per_ff_session,
                total_pinned_convo_per_ff_session,

                total_visible_cells,
                total_visible_unread_chat_cells,
                total_visible_unread_snap_cells,
                total_visible_cells_per_ff_session,
                total_visible_unread_chat_cells_per_ff_session,
                total_visible_unread_snap_cells_per_ff_session,

                cell_count_w_state_read_chat,
                cell_count_w_state_read_snap_slient,
                cell_count_w_state_new_snap_slient,
                cell_count_w_state_new_snap_audio,
                cell_count_w_state_read_snap_audio,
                cell_count_w_state_new_chat,
                cell_count_w_state_read_reaction,
                cell_count_w_state_new_snap_and_chat,
                cell_count_w_state_missed_call,
                cell_count_w_state_typing,
                cell_count_w_state_active_call,
                cell_count_w_state_new_reaction,
                cell_count_w_state_incoming_call
                ],
        name="friend_feed_state_convo_metrics",
        inner_join_with_mapping=False,
        bq_dialect="standard"
    )
    return mt




def friend_feed_state_detail_metrics(start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    ff_page_session_w_no_new_unread_convo = Metric(col="ff_page_session_w_no_new_unread_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_1_new_unread_convo = Metric(col="ff_page_session_w_1_new_unread_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_2_new_unread_convo = Metric(col="ff_page_session_w_2_new_unread_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_3_new_unread_convo = Metric(col="ff_page_session_w_3_new_unread_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_4_new_unread_convo = Metric(col="ff_page_session_w_4_new_unread_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_5_new_unread_convo = Metric(col="ff_page_session_w_5_new_unread_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_6_plus_new_unread_convo = Metric(col="ff_page_session_w_6_plus_new_unread_convo", dist="cont", daily=True, cumulative=True)

    ff_page_session_w_no_unread_convo = Metric(col="ff_page_session_w_no_unread_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_1_unread_convo = Metric(col="ff_page_session_w_1_unread_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_2_unread_convo = Metric(col="ff_page_session_w_2_unread_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_3_unread_convo = Metric(col="ff_page_session_w_3_unread_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_4_unread_convo = Metric(col="ff_page_session_w_4_unread_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_5_unread_convo = Metric(col="ff_page_session_w_5_unread_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_6_plus_unread_convo = Metric(col="ff_page_session_w_6_plus_unread_convo", dist="cont", daily=True, cumulative=True)

    ff_page_session_w_no_unread_chat_convo = Metric(col="ff_page_session_w_no_unread_chat_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_1_unread_chat_convo = Metric(col="ff_page_session_w_1_unread_chat_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_2_unread_chat_convo = Metric(col="ff_page_session_w_2_unread_chat_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_3_unread_chat_convo = Metric(col="ff_page_session_w_3_unread_chat_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_4_unread_chat_convo = Metric(col="ff_page_session_w_4_unread_chat_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_5_unread_chat_convo = Metric(col="ff_page_session_w_5_unread_chat_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_6_plus_unread_chat_convo = Metric(col="ff_page_session_w_6_plus_unread_chat_convo", dist="cont", daily=True, cumulative=True)

    ff_page_session_w_no_unread_snap_convo = Metric(col="ff_page_session_w_no_unread_snap_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_1_unread_snap_convo = Metric(col="ff_page_session_w_1_unread_snap_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_2_unread_snap_convo = Metric(col="ff_page_session_w_2_unread_snap_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_3_unread_snap_convo = Metric(col="ff_page_session_w_3_unread_snap_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_4_unread_snap_convo = Metric(col="ff_page_session_w_4_unread_snap_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_5_unread_snap_convo = Metric(col="ff_page_session_w_5_unread_snap_convo", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_6_plus_unread_snap_convo = Metric(col="ff_page_session_w_6_plus_unread_snap_convo", dist="cont", daily=True, cumulative=True)

    ff_page_session_w_no_visible_cell = Metric(col="ff_page_session_w_no_visible_cell", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_1_visible_cell = Metric(col="ff_page_session_w_1_visible_cell", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_2_visible_cells = Metric(col="ff_page_session_w_2_visible_cells", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_3_visible_cells = Metric(col="ff_page_session_w_3_visible_cells", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_4_visible_cells = Metric(col="ff_page_session_w_4_visible_cells", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_5_visible_cells = Metric(col="ff_page_session_w_5_visible_cells", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_6_visible_cells = Metric(col="ff_page_session_w_6_visible_cells", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_7_visible_cells = Metric(col="ff_page_session_w_7_visible_cells", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_8_visible_cells = Metric(col="ff_page_session_w_8_visible_cells", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_9_visible_cells = Metric(col="ff_page_session_w_9_visible_cells", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_10_visible_cells = Metric(col="ff_page_session_w_10_visible_cells", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_11_plus_visible_cells = Metric(col="ff_page_session_w_11_plus_visible_cells", dist="cont", daily=True, cumulative=True)

    ff_page_session_w_no_visible_unread_chat_cell = Metric(col="ff_page_session_w_no_visible_unread_chat_cell", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_1_visible_unread_chat_cell = Metric(col="ff_page_session_w_1_visible_unread_chat_cell", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_2_visible_unread_chat_cells = Metric(col="ff_page_session_w_2_visible_unread_chat_cells", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_3_visible_unread_chat_cells = Metric(col="ff_page_session_w_3_visible_unread_chat_cells", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_4_visible_unread_chat_cells = Metric(col="ff_page_session_w_4_visible_unread_chat_cells", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_5_visible_unread_chat_cells = Metric(col="ff_page_session_w_5_visible_unread_chat_cells", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_6_plus_visible_unread_chat_cells = Metric(col="ff_page_session_w_6_plus_visible_unread_chat_cells", dist="cont", daily=True, cumulative=True)

    ff_page_session_w_no_visible_unread_snap_cell = Metric(col="ff_page_session_w_no_visible_unread_snap_cell", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_1_visible_unread_snap_cell = Metric(col="ff_page_session_w_1_visible_unread_snap_cell", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_2_visible_unread_snap_cells = Metric(col="ff_page_session_w_2_visible_unread_snap_cells", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_3_visible_unread_snap_cells = Metric(col="ff_page_session_w_3_visible_unread_snap_cells", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_4_visible_unread_snap_cells = Metric(col="ff_page_session_w_4_visible_unread_snap_cells", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_5_visible_unread_snap_cells = Metric(col="ff_page_session_w_5_visible_unread_snap_cells", dist="cont", daily=True, cumulative=True)
    ff_page_session_w_6_plus_visible_unread_snap_cells = Metric(col="ff_page_session_w_6_plus_visible_unread_snap_cells", dist="cont", daily=True, cumulative=True)

    ff_page_session_w_in_call = Metric(col="ff_page_session_w_in_call", dist="cont", daily=True, cumulative=True)
    ff_page_session_cell_state_read_chat = Metric(col="ff_page_session_cell_state_read_chat", dist="cont", daily=True, cumulative=True)
    ff_page_session_cell_state_read_snap_slient = Metric(col="ff_page_session_cell_state_read_snap_slient", dist="cont", daily=True, cumulative=True)
    ff_page_session_cell_state_new_snap_slient = Metric(col="ff_page_session_cell_state_new_snap_slient", dist="cont", daily=True, cumulative=True)
    ff_page_session_cell_state_new_snap_audio = Metric(col="ff_page_session_cell_state_new_snap_audio", dist="cont", daily=True, cumulative=True)
    ff_page_session_cell_state_read_snap_audio = Metric(col="ff_page_session_cell_state_read_snap_audio", dist="cont", daily=True, cumulative=True)
    ff_page_session_cell_state_new_chat = Metric(col="ff_page_session_cell_state_new_chat", dist="cont", daily=True, cumulative=True)
    ff_page_session_cell_state_read_reaction = Metric(col="ff_page_session_cell_state_read_reaction", dist="cont", daily=True, cumulative=True)
    ff_page_session_cell_state_new_snap_and_chat = Metric(col="ff_page_session_cell_state_new_snap_and_chat", dist="cont", daily=True, cumulative=True)
    ff_page_session_cell_state_missed_call = Metric(col="ff_page_session_cell_state_missed_call", dist="cont", daily=True, cumulative=True)
    ff_page_session_cell_state_typing = Metric(col="ff_page_session_cell_state_typing", dist="cont", daily=True, cumulative=True)
    ff_page_session_cell_state_active_call = Metric(col="ff_page_session_cell_state_active_call", dist="cont", daily=True, cumulative=True)
    ff_page_session_cell_state_new_reaction = Metric(col="ff_page_session_cell_state_new_reaction", dist="cont", daily=True, cumulative=True)
    ff_page_session_cell_state_incoming_call = Metric(col="ff_page_session_cell_state_incoming_call", dist="cont", daily=True, cumulative=True)
    ff_page_session_cell_state_is_sent_by_user = Metric(col="ff_page_session_cell_state_is_sent_by_user", dist="cont", daily=True, cumulative=True)
    ff_page_session_cell_state_is_received_by_user = Metric(col="ff_page_session_cell_state_is_received_by_user", dist="cont", daily=True, cumulative=True)

    sql="""
        SELECT 
            ghost_user_id,
            TIMESTAMP(event_date) as ts,

            ff_page_session_new_unread_conversations_0 as ff_page_session_w_no_new_unread_convo,
            ff_page_session_new_unread_conversations_1 as ff_page_session_w_1_new_unread_convo,
            ff_page_session_new_unread_conversations_2 as ff_page_session_w_2_new_unread_convo,
            ff_page_session_new_unread_conversations_3 as ff_page_session_w_3_new_unread_convo,
            ff_page_session_new_unread_conversations_4 as ff_page_session_w_4_new_unread_convo,
            ff_page_session_new_unread_conversations_5 as ff_page_session_w_5_new_unread_convo,
            ff_page_session_new_unread_conversations_6_plus as ff_page_session_w_6_plus_new_unread_convo,

            ff_page_session_unread_conversations_0 as ff_page_session_w_no_unread_convo,
            ff_page_session_unread_conversations_1 as ff_page_session_w_1_unread_convo,
            ff_page_session_unread_conversations_2 as ff_page_session_w_2_unread_convo,
            ff_page_session_unread_conversations_3 as ff_page_session_w_3_unread_convo,
            ff_page_session_unread_conversations_4 as ff_page_session_w_4_unread_convo,
            ff_page_session_unread_conversations_5 as ff_page_session_w_5_unread_convo,
            ff_page_session_unread_conversations_6_plus as ff_page_session_w_6_plus_unread_convo,

            ff_page_session_unread_chats_0 as ff_page_session_w_no_unread_chat_convo,
            ff_page_session_unread_chats_1 as ff_page_session_w_1_unread_chat_convo,
            ff_page_session_unread_chats_2 as ff_page_session_w_2_unread_chat_convo,
            ff_page_session_unread_chats_3 as ff_page_session_w_3_unread_chat_convo,
            ff_page_session_unread_chats_4 as ff_page_session_w_4_unread_chat_convo,
            ff_page_session_unread_chats_5 as ff_page_session_w_5_unread_chat_convo,
            ff_page_session_unread_chats_6_plus as ff_page_session_w_6_plus_unread_chat_convo,

            ff_page_session_unread_snaps_0 as ff_page_session_w_no_unread_snap_convo,
            ff_page_session_unread_snaps_1 as ff_page_session_w_1_unread_snap_convo,
            ff_page_session_unread_snaps_2 as ff_page_session_w_2_unread_snap_convo,
            ff_page_session_unread_snaps_3 as ff_page_session_w_3_unread_snap_convo,
            ff_page_session_unread_snaps_4 as ff_page_session_w_4_unread_snap_convo,
            ff_page_session_unread_snaps_5 as ff_page_session_w_5_unread_snap_convo,
            ff_page_session_unread_snaps_6_plus as ff_page_session_w_6_plus_unread_snap_convo,

            ff_page_session_visible_cells_0 as ff_page_session_w_no_visible_cell,
            ff_page_session_visible_cells_1 as ff_page_session_w_1_visible_cell,
            ff_page_session_visible_cells_2 as ff_page_session_w_2_visible_cells,
            ff_page_session_visible_cells_3 as ff_page_session_w_3_visible_cells,
            ff_page_session_visible_cells_4 as ff_page_session_w_4_visible_cells,
            ff_page_session_visible_cells_5 as ff_page_session_w_5_visible_cells,
            ff_page_session_visible_cells_6 as ff_page_session_w_6_visible_cells,
            ff_page_session_visible_cells_7 as ff_page_session_w_7_visible_cells,
            ff_page_session_visible_cells_8 as ff_page_session_w_8_visible_cells,
            ff_page_session_visible_cells_9 as ff_page_session_w_9_visible_cells,
            ff_page_session_visible_cells_10 as ff_page_session_w_10_visible_cells,
            ff_page_session_visible_cells_11_plus as ff_page_session_w_11_plus_visible_cells,

            ff_page_session_visible_unread_chat_cells_0 as ff_page_session_w_no_visible_unread_chat_cell,
            ff_page_session_visible_unread_chat_cells_1 as ff_page_session_w_1_visible_unread_chat_cell,
            ff_page_session_visible_unread_chat_cells_2 as ff_page_session_w_2_visible_unread_chat_cells,
            ff_page_session_visible_unread_chat_cells_3 as ff_page_session_w_3_visible_unread_chat_cells,
            ff_page_session_visible_unread_chat_cells_4 as ff_page_session_w_4_visible_unread_chat_cells,
            ff_page_session_visible_unread_chat_cells_5 as ff_page_session_w_5_visible_unread_chat_cells,
            ff_page_session_visible_unread_chat_cells_6_plus as ff_page_session_w_6_plus_visible_unread_chat_cells,

            ff_page_session_visible_unread_snap_cells_0 as ff_page_session_w_no_visible_unread_snap_cell,
            ff_page_session_visible_unread_snap_cells_1 as ff_page_session_w_1_visible_unread_snap_cell,
            ff_page_session_visible_unread_snap_cells_2 as ff_page_session_w_2_visible_unread_snap_cells,
            ff_page_session_visible_unread_snap_cells_3 as ff_page_session_w_3_visible_unread_snap_cells,
            ff_page_session_visible_unread_snap_cells_4 as ff_page_session_w_4_visible_unread_snap_cells,
            ff_page_session_visible_unread_snap_cells_5 as ff_page_session_w_5_visible_unread_snap_cells,
            ff_page_session_visible_unread_snap_cells_6_plus as ff_page_session_w_6_plus_visible_unread_snap_cells,

            ff_page_session_w_in_call,
            ff_page_session_cell_state_read_chat,
            ff_page_session_cell_state_read_snap_slient,
            ff_page_session_cell_state_new_snap_slient,
            ff_page_session_cell_state_new_snap_audio,
            ff_page_session_cell_state_read_snap_audio,
            ff_page_session_cell_state_new_chat,
            ff_page_session_cell_state_read_reaction,
            ff_page_session_cell_state_new_snap_and_chat,
            ff_page_session_cell_state_missed_call,
            ff_page_session_cell_state_typing,
            ff_page_session_cell_state_active_call,
            ff_page_session_cell_state_new_reaction,
            ff_page_session_cell_state_incoming_call,
            ff_page_session_cell_state_is_sent_by_user,
            ff_page_session_cell_state_is_received_by_user

        FROM
          `sc-analytics.report_messaging.friends_feed_page_open_user_level_20*`
    WHERE concat('20',_table_suffix) between '{start_date}' and '{end_date}'
        """
    
    def sql_callable(start_date, end_date):
#         start_date, end_date = start_date[-6:], end_date[-6:]
        return sql.format(start_date=start_date, end_date=end_date)  
    
    mt = MetricTable(
        sql = sql.format(start_date=start_date, end_date=end_date),
        sql_callable = sql_callable,
        metrics=[
                ff_page_session_w_no_new_unread_convo,
                ff_page_session_w_1_new_unread_convo,
                ff_page_session_w_2_new_unread_convo,
                ff_page_session_w_3_new_unread_convo,
                ff_page_session_w_4_new_unread_convo,
                ff_page_session_w_5_new_unread_convo,
                ff_page_session_w_6_plus_new_unread_convo,

                ff_page_session_w_no_unread_convo,
                ff_page_session_w_1_unread_convo,
                ff_page_session_w_2_unread_convo,
                ff_page_session_w_3_unread_convo,
                ff_page_session_w_4_unread_convo,
                ff_page_session_w_5_unread_convo,
                ff_page_session_w_6_plus_unread_convo,

                ff_page_session_w_no_unread_chat_convo,
                ff_page_session_w_1_unread_chat_convo,
                ff_page_session_w_2_unread_chat_convo,
                ff_page_session_w_3_unread_chat_convo,
                ff_page_session_w_4_unread_chat_convo,
                ff_page_session_w_5_unread_chat_convo,
                ff_page_session_w_6_plus_unread_chat_convo,

                ff_page_session_w_no_unread_snap_convo,
                ff_page_session_w_1_unread_snap_convo,
                ff_page_session_w_2_unread_snap_convo,
                ff_page_session_w_3_unread_snap_convo,
                ff_page_session_w_4_unread_snap_convo,
                ff_page_session_w_5_unread_snap_convo,
                ff_page_session_w_6_plus_unread_snap_convo,

                ff_page_session_w_no_visible_cell,
                ff_page_session_w_1_visible_cell,
                ff_page_session_w_2_visible_cells,
                ff_page_session_w_3_visible_cells,
                ff_page_session_w_4_visible_cells,
                ff_page_session_w_5_visible_cells,
                ff_page_session_w_6_visible_cells,
                ff_page_session_w_7_visible_cells,
                ff_page_session_w_8_visible_cells,
                ff_page_session_w_9_visible_cells,
                ff_page_session_w_10_visible_cells,
                ff_page_session_w_11_plus_visible_cells,

                ff_page_session_w_no_visible_unread_chat_cell,
                ff_page_session_w_1_visible_unread_chat_cell,
                ff_page_session_w_2_visible_unread_chat_cells,
                ff_page_session_w_3_visible_unread_chat_cells,
                ff_page_session_w_4_visible_unread_chat_cells,
                ff_page_session_w_5_visible_unread_chat_cells,
                ff_page_session_w_6_plus_visible_unread_chat_cells,

                ff_page_session_w_no_visible_unread_snap_cell,
                ff_page_session_w_1_visible_unread_snap_cell,
                ff_page_session_w_2_visible_unread_snap_cells,
                ff_page_session_w_3_visible_unread_snap_cells,
                ff_page_session_w_4_visible_unread_snap_cells,
                ff_page_session_w_5_visible_unread_snap_cells,
                ff_page_session_w_6_plus_visible_unread_snap_cells,

                ff_page_session_w_in_call,
                ff_page_session_cell_state_read_chat,
                ff_page_session_cell_state_read_snap_slient,
                ff_page_session_cell_state_new_snap_slient,
                ff_page_session_cell_state_new_snap_audio,
                ff_page_session_cell_state_read_snap_audio,
                ff_page_session_cell_state_new_chat,
                ff_page_session_cell_state_read_reaction,
                ff_page_session_cell_state_new_snap_and_chat,
                ff_page_session_cell_state_missed_call,
                ff_page_session_cell_state_typing,
                ff_page_session_cell_state_active_call,
                ff_page_session_cell_state_new_reaction,
                ff_page_session_cell_state_incoming_call,
                ff_page_session_cell_state_is_sent_by_user,
                ff_page_session_cell_state_is_received_by_user

                ],
        name="friend_feed_state_detail_metrics",
        inner_join_with_mapping=False,
        bq_dialect="standard"
    )
    return mt


def friend_feed_state_cell_position_metrics(start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    ff_page_session_count = Metric(col="ff_page_session_count", dist="cont", daily=True, cumulative=True)
    app_session_count_w_ff_page_open = Metric(col="app_session_count_w_ff_page_open", dist="cont", daily=True, cumulative=True)

    cell_position_0_state_new_snap_per_ff_session = Metric(col="cell_position_0_state_new_snap_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_0_state_new_chat_per_ff_session = Metric(col="cell_position_0_state_new_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_0_state_new_snap_and_chat_per_ff_session = Metric(col="cell_position_0_state_new_snap_and_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_0_state_read_snap_per_ff_session = Metric(col="cell_position_0_state_read_snap_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_0_state_read_chat_per_ff_session = Metric(col="cell_position_0_state_read_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_0_state_read_reaction_per_ff_session = Metric(col="cell_position_0_state_read_reaction_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_0_state_new_reaction_per_ff_session = Metric(col="cell_position_0_state_new_reaction_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_0_state_call_per_ff_session = Metric(col="cell_position_0_state_call_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_0_state_typing_per_ff_session = Metric(col="cell_position_0_state_typing_per_ff_session", dist="cont", daily=True, cumulative=True)

    cell_position_1_state_new_snap_per_ff_session = Metric(col="cell_position_1_state_new_snap_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_1_state_new_chat_per_ff_session = Metric(col="cell_position_1_state_new_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_1_state_new_snap_and_chat_per_ff_session = Metric(col="cell_position_1_state_new_snap_and_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_1_state_read_snap_per_ff_session = Metric(col="cell_position_1_state_read_snap_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_1_state_read_chat_per_ff_session = Metric(col="cell_position_1_state_read_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_1_state_read_reaction_per_ff_session = Metric(col="cell_position_1_state_read_reaction_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_1_state_new_reaction_per_ff_session = Metric(col="cell_position_1_state_new_reaction_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_1_state_call_per_ff_session = Metric(col="cell_position_1_state_call_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_1_state_typing_per_ff_session = Metric(col="cell_position_1_state_typing_per_ff_session", dist="cont", daily=True, cumulative=True)

    cell_position_2_state_new_snap_per_ff_session = Metric(col="cell_position_2_state_new_snap_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_2_state_new_chat_per_ff_session = Metric(col="cell_position_2_state_new_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_2_state_new_snap_and_chat_per_ff_session = Metric(col="cell_position_2_state_new_snap_and_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_2_state_read_snap_per_ff_session = Metric(col="cell_position_2_state_read_snap_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_2_state_read_chat_per_ff_session = Metric(col="cell_position_2_state_read_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_2_state_read_reaction_per_ff_session = Metric(col="cell_position_2_state_read_reaction_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_2_state_new_reaction_per_ff_session = Metric(col="cell_position_2_state_new_reaction_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_2_state_call_per_ff_session = Metric(col="cell_position_2_state_call_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_2_state_typing_per_ff_session = Metric(col="cell_position_2_state_typing_per_ff_session", dist="cont", daily=True, cumulative=True)

    cell_position_3_state_new_snap_per_ff_session = Metric(col="cell_position_3_state_new_snap_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_3_state_new_chat_per_ff_session = Metric(col="cell_position_3_state_new_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_3_state_new_snap_and_chat_per_ff_session = Metric(col="cell_position_3_state_new_snap_and_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_3_state_read_snap_per_ff_session = Metric(col="cell_position_3_state_read_snap_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_3_state_read_chat_per_ff_session = Metric(col="cell_position_3_state_read_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_3_state_read_reaction_per_ff_session = Metric(col="cell_position_3_state_read_reaction_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_3_state_new_reaction_per_ff_session = Metric(col="cell_position_3_state_new_reaction_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_3_state_call_per_ff_session = Metric(col="cell_position_3_state_call_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_3_state_typing_per_ff_session = Metric(col="cell_position_3_state_typing_per_ff_session", dist="cont", daily=True, cumulative=True)

    cell_position_4_state_new_snap_per_ff_session = Metric(col="cell_position_4_state_new_snap_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_4_state_new_chat_per_ff_session = Metric(col="cell_position_4_state_new_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_4_state_new_snap_and_chat_per_ff_session = Metric(col="cell_position_4_state_new_snap_and_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_4_state_read_snap_per_ff_session = Metric(col="cell_position_4_state_read_snap_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_4_state_read_chat_per_ff_session = Metric(col="cell_position_4_state_read_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_4_state_read_reaction_per_ff_session = Metric(col="cell_position_4_state_read_reaction_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_4_state_new_reaction_per_ff_session = Metric(col="cell_position_4_state_new_reaction_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_4_state_call_per_ff_session = Metric(col="cell_position_4_state_call_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_4_state_typing_per_ff_session = Metric(col="cell_position_4_state_typing_per_ff_session", dist="cont", daily=True, cumulative=True)

    cell_position_5_state_new_snap_per_ff_session = Metric(col="cell_position_5_state_new_snap_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_5_state_new_chat_per_ff_session = Metric(col="cell_position_5_state_new_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_5_state_new_snap_and_chat_per_ff_session = Metric(col="cell_position_5_state_new_snap_and_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_5_state_read_snap_per_ff_session = Metric(col="cell_position_5_state_read_snap_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_5_state_read_chat_per_ff_session = Metric(col="cell_position_5_state_read_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_5_state_read_reaction_per_ff_session = Metric(col="cell_position_5_state_read_reaction_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_5_state_new_reaction_per_ff_session = Metric(col="cell_position_5_state_new_reaction_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_5_state_call_per_ff_session = Metric(col="cell_position_5_state_call_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_5_state_typing_per_ff_session = Metric(col="cell_position_5_state_typing_per_ff_session", dist="cont", daily=True, cumulative=True)

    cell_position_6_state_new_snap_per_ff_session = Metric(col="cell_position_6_state_new_snap_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_6_state_new_chat_per_ff_session = Metric(col="cell_position_6_state_new_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_6_state_new_snap_and_chat_per_ff_session = Metric(col="cell_position_6_state_new_snap_and_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_6_state_read_snap_per_ff_session = Metric(col="cell_position_6_state_read_snap_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_6_state_read_chat_per_ff_session = Metric(col="cell_position_6_state_read_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_6_state_read_reaction_per_ff_session = Metric(col="cell_position_6_state_read_reaction_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_6_state_new_reaction_per_ff_session = Metric(col="cell_position_6_state_new_reaction_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_6_state_call_per_ff_session = Metric(col="cell_position_6_state_call_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_6_state_typing_per_ff_session = Metric(col="cell_position_6_state_typing_per_ff_session", dist="cont", daily=True, cumulative=True)

    cell_position_7_state_new_snap_per_ff_session = Metric(col="cell_position_7_state_new_snap_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_7_state_new_chat_per_ff_session = Metric(col="cell_position_7_state_new_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_7_state_new_snap_and_chat_per_ff_session = Metric(col="cell_position_7_state_new_snap_and_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_7_state_read_snap_per_ff_session = Metric(col="cell_position_7_state_read_snap_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_7_state_read_chat_per_ff_session = Metric(col="cell_position_7_state_read_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_7_state_read_reaction_per_ff_session = Metric(col="cell_position_7_state_read_reaction_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_7_state_new_reaction_per_ff_session = Metric(col="cell_position_7_state_new_reaction_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_7_state_call_per_ff_session = Metric(col="cell_position_7_state_call_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_7_state_typing_per_ff_session = Metric(col="cell_position_7_state_typing_per_ff_session", dist="cont", daily=True, cumulative=True)

    cell_position_8_state_new_snap_per_ff_session = Metric(col="cell_position_8_state_new_snap_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_8_state_new_chat_per_ff_session = Metric(col="cell_position_8_state_new_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_8_state_new_snap_and_chat_per_ff_session = Metric(col="cell_position_8_state_new_snap_and_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_8_state_read_snap_per_ff_session = Metric(col="cell_position_8_state_read_snap_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_8_state_read_chat_per_ff_session = Metric(col="cell_position_8_state_read_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_8_state_read_reaction_per_ff_session = Metric(col="cell_position_8_state_read_reaction_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_8_state_new_reaction_per_ff_session = Metric(col="cell_position_8_state_new_reaction_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_8_state_call_per_ff_session = Metric(col="cell_position_8_state_call_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_8_state_typing_per_ff_session = Metric(col="cell_position_8_state_typing_per_ff_session", dist="cont", daily=True, cumulative=True)

    cell_position_9_state_new_snap_per_ff_session = Metric(col="cell_position_9_state_new_snap_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_9_state_new_chat_per_ff_session = Metric(col="cell_position_9_state_new_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_9_state_new_snap_and_chat_per_ff_session = Metric(col="cell_position_9_state_new_snap_and_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_9_state_read_snap_per_ff_session = Metric(col="cell_position_9_state_read_snap_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_9_state_read_chat_per_ff_session = Metric(col="cell_position_9_state_read_chat_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_9_state_read_reaction_per_ff_session = Metric(col="cell_position_9_state_read_reaction_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_9_state_new_reaction_per_ff_session = Metric(col="cell_position_9_state_new_reaction_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_9_state_call_per_ff_session = Metric(col="cell_position_9_state_call_per_ff_session", dist="cont", daily=True, cumulative=True)
    cell_position_9_state_typing_per_ff_session = Metric(col="cell_position_9_state_typing_per_ff_session", dist="cont", daily=True, cumulative=True)

    sql="""
        SELECT 
            ghost_user_id,
            TIMESTAMP(event_date) as ts,
            ff_page_session_count,
            app_session_count as app_session_count_w_ff_page_open,

            SAFE_DIVIDE(cell_position_0_state_new_snap, ff_page_session_count) AS cell_position_0_state_new_snap_per_ff_session,
            SAFE_DIVIDE(cell_position_0_state_new_chat, ff_page_session_count) AS cell_position_0_state_new_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_0_state_new_snap_and_chat, ff_page_session_count) AS cell_position_0_state_new_snap_and_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_0_state_read_snap, ff_page_session_count) AS cell_position_0_state_read_snap_per_ff_session,
            SAFE_DIVIDE(cell_position_0_state_read_chat, ff_page_session_count) AS cell_position_0_state_read_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_0_state_read_reaction, ff_page_session_count) AS cell_position_0_state_read_reaction_per_ff_session,
            SAFE_DIVIDE(cell_position_0_state_new_reaction, ff_page_session_count) AS cell_position_0_state_new_reaction_per_ff_session,
            SAFE_DIVIDE(cell_position_0_state_call, ff_page_session_count) AS cell_position_0_state_call_per_ff_session,
            SAFE_DIVIDE(cell_position_0_state_typing, ff_page_session_count) AS cell_position_0_state_typing_per_ff_session,

            SAFE_DIVIDE(cell_position_1_state_new_snap, ff_page_session_count) AS cell_position_1_state_new_snap_per_ff_session,
            SAFE_DIVIDE(cell_position_1_state_new_chat, ff_page_session_count) AS cell_position_1_state_new_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_1_state_new_snap_and_chat, ff_page_session_count) AS cell_position_1_state_new_snap_and_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_1_state_read_snap, ff_page_session_count) AS cell_position_1_state_read_snap_per_ff_session,
            SAFE_DIVIDE(cell_position_1_state_read_chat, ff_page_session_count) AS cell_position_1_state_read_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_1_state_read_reaction, ff_page_session_count) AS cell_position_1_state_read_reaction_per_ff_session,
            SAFE_DIVIDE(cell_position_1_state_new_reaction, ff_page_session_count) AS cell_position_1_state_new_reaction_per_ff_session,
            SAFE_DIVIDE(cell_position_1_state_call, ff_page_session_count) AS cell_position_1_state_call_per_ff_session,
            SAFE_DIVIDE(cell_position_1_state_typing, ff_page_session_count) AS cell_position_1_state_typing_per_ff_session,

            SAFE_DIVIDE(cell_position_2_state_new_snap, ff_page_session_count) AS cell_position_2_state_new_snap_per_ff_session,
            SAFE_DIVIDE(cell_position_2_state_new_chat, ff_page_session_count) AS cell_position_2_state_new_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_2_state_new_snap_and_chat, ff_page_session_count) AS cell_position_2_state_new_snap_and_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_2_state_read_snap, ff_page_session_count) AS cell_position_2_state_read_snap_per_ff_session,
            SAFE_DIVIDE(cell_position_2_state_read_chat, ff_page_session_count) AS cell_position_2_state_read_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_2_state_read_reaction, ff_page_session_count) AS cell_position_2_state_read_reaction_per_ff_session,
            SAFE_DIVIDE(cell_position_2_state_new_reaction, ff_page_session_count) AS cell_position_2_state_new_reaction_per_ff_session,
            SAFE_DIVIDE(cell_position_2_state_call, ff_page_session_count) AS cell_position_2_state_call_per_ff_session,
            SAFE_DIVIDE(cell_position_2_state_typing, ff_page_session_count) AS cell_position_2_state_typing_per_ff_session,

            SAFE_DIVIDE(cell_position_3_state_new_snap, ff_page_session_count) AS cell_position_3_state_new_snap_per_ff_session,
            SAFE_DIVIDE(cell_position_3_state_new_chat, ff_page_session_count) AS cell_position_3_state_new_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_3_state_new_snap_and_chat, ff_page_session_count) AS cell_position_3_state_new_snap_and_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_3_state_read_snap, ff_page_session_count) AS cell_position_3_state_read_snap_per_ff_session,
            SAFE_DIVIDE(cell_position_3_state_read_chat, ff_page_session_count) AS cell_position_3_state_read_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_3_state_read_reaction, ff_page_session_count) AS cell_position_3_state_read_reaction_per_ff_session,
            SAFE_DIVIDE(cell_position_3_state_new_reaction, ff_page_session_count) AS cell_position_3_state_new_reaction_per_ff_session,
            SAFE_DIVIDE(cell_position_3_state_call, ff_page_session_count) AS cell_position_3_state_call_per_ff_session,
            SAFE_DIVIDE(cell_position_3_state_typing, ff_page_session_count) AS cell_position_3_state_typing_per_ff_session,

            SAFE_DIVIDE(cell_position_4_state_new_snap, ff_page_session_count) AS cell_position_4_state_new_snap_per_ff_session,
            SAFE_DIVIDE(cell_position_4_state_new_chat, ff_page_session_count) AS cell_position_4_state_new_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_4_state_new_snap_and_chat, ff_page_session_count) AS cell_position_4_state_new_snap_and_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_4_state_read_snap, ff_page_session_count) AS cell_position_4_state_read_snap_per_ff_session,
            SAFE_DIVIDE(cell_position_4_state_read_chat, ff_page_session_count) AS cell_position_4_state_read_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_4_state_read_reaction, ff_page_session_count) AS cell_position_4_state_read_reaction_per_ff_session,
            SAFE_DIVIDE(cell_position_4_state_new_reaction, ff_page_session_count) AS cell_position_4_state_new_reaction_per_ff_session,
            SAFE_DIVIDE(cell_position_4_state_call, ff_page_session_count) AS cell_position_4_state_call_per_ff_session,
            SAFE_DIVIDE(cell_position_4_state_typing, ff_page_session_count) AS cell_position_4_state_typing_per_ff_session,

            SAFE_DIVIDE(cell_position_5_state_new_snap, ff_page_session_count) AS cell_position_5_state_new_snap_per_ff_session,
            SAFE_DIVIDE(cell_position_5_state_new_chat, ff_page_session_count) AS cell_position_5_state_new_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_5_state_new_snap_and_chat, ff_page_session_count) AS cell_position_5_state_new_snap_and_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_5_state_read_snap, ff_page_session_count) AS cell_position_5_state_read_snap_per_ff_session,
            SAFE_DIVIDE(cell_position_5_state_read_chat, ff_page_session_count) AS cell_position_5_state_read_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_5_state_read_reaction, ff_page_session_count) AS cell_position_5_state_read_reaction_per_ff_session,
            SAFE_DIVIDE(cell_position_5_state_new_reaction, ff_page_session_count) AS cell_position_5_state_new_reaction_per_ff_session,
            SAFE_DIVIDE(cell_position_5_state_call, ff_page_session_count) AS cell_position_5_state_call_per_ff_session,
            SAFE_DIVIDE(cell_position_5_state_typing, ff_page_session_count) AS cell_position_5_state_typing_per_ff_session,

            SAFE_DIVIDE(cell_position_6_state_new_snap, ff_page_session_count) AS cell_position_6_state_new_snap_per_ff_session,
            SAFE_DIVIDE(cell_position_6_state_new_chat, ff_page_session_count) AS cell_position_6_state_new_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_6_state_new_snap_and_chat, ff_page_session_count) AS cell_position_6_state_new_snap_and_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_6_state_read_snap, ff_page_session_count) AS cell_position_6_state_read_snap_per_ff_session,
            SAFE_DIVIDE(cell_position_6_state_read_chat, ff_page_session_count) AS cell_position_6_state_read_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_6_state_read_reaction, ff_page_session_count) AS cell_position_6_state_read_reaction_per_ff_session,
            SAFE_DIVIDE(cell_position_6_state_new_reaction, ff_page_session_count) AS cell_position_6_state_new_reaction_per_ff_session,
            SAFE_DIVIDE(cell_position_6_state_call, ff_page_session_count) AS cell_position_6_state_call_per_ff_session,
            SAFE_DIVIDE(cell_position_6_state_typing, ff_page_session_count) AS cell_position_6_state_typing_per_ff_session,

            SAFE_DIVIDE(cell_position_7_state_new_snap, ff_page_session_count) AS cell_position_7_state_new_snap_per_ff_session,
            SAFE_DIVIDE(cell_position_7_state_new_chat, ff_page_session_count) AS cell_position_7_state_new_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_7_state_new_snap_and_chat, ff_page_session_count) AS cell_position_7_state_new_snap_and_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_7_state_read_snap, ff_page_session_count) AS cell_position_7_state_read_snap_per_ff_session,
            SAFE_DIVIDE(cell_position_7_state_read_chat, ff_page_session_count) AS cell_position_7_state_read_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_7_state_read_reaction, ff_page_session_count) AS cell_position_7_state_read_reaction_per_ff_session,
            SAFE_DIVIDE(cell_position_7_state_new_reaction, ff_page_session_count) AS cell_position_7_state_new_reaction_per_ff_session,
            SAFE_DIVIDE(cell_position_7_state_call, ff_page_session_count) AS cell_position_7_state_call_per_ff_session,
            SAFE_DIVIDE(cell_position_7_state_typing, ff_page_session_count) AS cell_position_7_state_typing_per_ff_session,

            SAFE_DIVIDE(cell_position_8_state_new_snap, ff_page_session_count) AS cell_position_8_state_new_snap_per_ff_session,
            SAFE_DIVIDE(cell_position_8_state_new_chat, ff_page_session_count) AS cell_position_8_state_new_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_8_state_new_snap_and_chat, ff_page_session_count) AS cell_position_8_state_new_snap_and_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_8_state_read_snap, ff_page_session_count) AS cell_position_8_state_read_snap_per_ff_session,
            SAFE_DIVIDE(cell_position_8_state_read_chat, ff_page_session_count) AS cell_position_8_state_read_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_8_state_read_reaction, ff_page_session_count) AS cell_position_8_state_read_reaction_per_ff_session,
            SAFE_DIVIDE(cell_position_8_state_new_reaction, ff_page_session_count) AS cell_position_8_state_new_reaction_per_ff_session,
            SAFE_DIVIDE(cell_position_8_state_call, ff_page_session_count) AS cell_position_8_state_call_per_ff_session,
            SAFE_DIVIDE(cell_position_8_state_typing, ff_page_session_count) AS cell_position_8_state_typing_per_ff_session,

            SAFE_DIVIDE(cell_position_9_state_new_snap, ff_page_session_count) AS cell_position_9_state_new_snap_per_ff_session,
            SAFE_DIVIDE(cell_position_9_state_new_chat, ff_page_session_count) AS cell_position_9_state_new_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_9_state_new_snap_and_chat, ff_page_session_count) AS cell_position_9_state_new_snap_and_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_9_state_read_snap, ff_page_session_count) AS cell_position_9_state_read_snap_per_ff_session,
            SAFE_DIVIDE(cell_position_9_state_read_chat, ff_page_session_count) AS cell_position_9_state_read_chat_per_ff_session,
            SAFE_DIVIDE(cell_position_9_state_read_reaction, ff_page_session_count) AS cell_position_9_state_read_reaction_per_ff_session,
            SAFE_DIVIDE(cell_position_9_state_new_reaction, ff_page_session_count) AS cell_position_9_state_new_reaction_per_ff_session,
            SAFE_DIVIDE(cell_position_9_state_call, ff_page_session_count) AS cell_position_9_state_call_per_ff_session,
            SAFE_DIVIDE(cell_position_9_state_typing, ff_page_session_count) AS cell_position_9_state_typing_per_ff_session,

        FROM
          `sc-analytics.report_messaging.friends_feed_page_open_user_level_20*`
    WHERE concat('20',_table_suffix) between '{start_date}' and '{end_date}'
        """
    
    def sql_callable(start_date, end_date):
#         start_date, end_date = start_date[-6:], end_date[-6:]
        return sql.format(start_date=start_date, end_date=end_date)  
    
    mt = MetricTable(
        sql = sql.format(start_date=start_date, end_date=end_date),
        sql_callable = sql_callable,
        metrics=[
                ff_page_session_count,
                app_session_count_w_ff_page_open,
                cell_position_0_state_new_snap_per_ff_session,
                cell_position_0_state_new_chat_per_ff_session,
                cell_position_0_state_new_snap_and_chat_per_ff_session,
                cell_position_0_state_read_snap_per_ff_session,
                cell_position_0_state_read_chat_per_ff_session,
                cell_position_0_state_read_reaction_per_ff_session,
                cell_position_0_state_new_reaction_per_ff_session,
                cell_position_0_state_call_per_ff_session,
                cell_position_0_state_typing_per_ff_session,

                cell_position_1_state_new_snap_per_ff_session,
                cell_position_1_state_new_chat_per_ff_session,
                cell_position_1_state_new_snap_and_chat_per_ff_session,
                cell_position_1_state_read_snap_per_ff_session,
                cell_position_1_state_read_chat_per_ff_session,
                cell_position_1_state_read_reaction_per_ff_session,
                cell_position_1_state_new_reaction_per_ff_session,
                cell_position_1_state_call_per_ff_session,
                cell_position_1_state_typing_per_ff_session,

                cell_position_2_state_new_snap_per_ff_session,
                cell_position_2_state_new_chat_per_ff_session,
                cell_position_2_state_new_snap_and_chat_per_ff_session,
                cell_position_2_state_read_snap_per_ff_session,
                cell_position_2_state_read_chat_per_ff_session,
                cell_position_2_state_read_reaction_per_ff_session,
                cell_position_2_state_new_reaction_per_ff_session,
                cell_position_2_state_call_per_ff_session,
                cell_position_2_state_typing_per_ff_session,

                cell_position_3_state_new_snap_per_ff_session,
                cell_position_3_state_new_chat_per_ff_session,
                cell_position_3_state_new_snap_and_chat_per_ff_session,
                cell_position_3_state_read_snap_per_ff_session,
                cell_position_3_state_read_chat_per_ff_session,
                cell_position_3_state_read_reaction_per_ff_session,
                cell_position_3_state_new_reaction_per_ff_session,
                cell_position_3_state_call_per_ff_session,
                cell_position_3_state_typing_per_ff_session,

                cell_position_4_state_new_snap_per_ff_session,
                cell_position_4_state_new_chat_per_ff_session,
                cell_position_4_state_new_snap_and_chat_per_ff_session,
                cell_position_4_state_read_snap_per_ff_session,
                cell_position_4_state_read_chat_per_ff_session,
                cell_position_4_state_read_reaction_per_ff_session,
                cell_position_4_state_new_reaction_per_ff_session,
                cell_position_4_state_call_per_ff_session,
                cell_position_4_state_typing_per_ff_session,

                cell_position_5_state_new_snap_per_ff_session,
                cell_position_5_state_new_chat_per_ff_session,
                cell_position_5_state_new_snap_and_chat_per_ff_session,
                cell_position_5_state_read_snap_per_ff_session,
                cell_position_5_state_read_chat_per_ff_session,
                cell_position_5_state_read_reaction_per_ff_session,
                cell_position_5_state_new_reaction_per_ff_session,
                cell_position_5_state_call_per_ff_session,
                cell_position_5_state_typing_per_ff_session,

                cell_position_6_state_new_snap_per_ff_session,
                cell_position_6_state_new_chat_per_ff_session,
                cell_position_6_state_new_snap_and_chat_per_ff_session,
                cell_position_6_state_read_snap_per_ff_session,
                cell_position_6_state_read_chat_per_ff_session,
                cell_position_6_state_read_reaction_per_ff_session,
                cell_position_6_state_new_reaction_per_ff_session,
                cell_position_6_state_call_per_ff_session,
                cell_position_6_state_typing_per_ff_session,

                cell_position_7_state_new_snap_per_ff_session,
                cell_position_7_state_new_chat_per_ff_session,
                cell_position_7_state_new_snap_and_chat_per_ff_session,
                cell_position_7_state_read_snap_per_ff_session,
                cell_position_7_state_read_chat_per_ff_session,
                cell_position_7_state_read_reaction_per_ff_session,
                cell_position_7_state_new_reaction_per_ff_session,
                cell_position_7_state_call_per_ff_session,
                cell_position_7_state_typing_per_ff_session,

                cell_position_8_state_new_snap_per_ff_session,
                cell_position_8_state_new_chat_per_ff_session,
                cell_position_8_state_new_snap_and_chat_per_ff_session,
                cell_position_8_state_read_snap_per_ff_session,
                cell_position_8_state_read_chat_per_ff_session,
                cell_position_8_state_read_reaction_per_ff_session,
                cell_position_8_state_new_reaction_per_ff_session,
                cell_position_8_state_call_per_ff_session,
                cell_position_8_state_typing_per_ff_session,

                cell_position_9_state_new_snap_per_ff_session,
                cell_position_9_state_new_chat_per_ff_session,
                cell_position_9_state_new_snap_and_chat_per_ff_session,
                cell_position_9_state_read_snap_per_ff_session,
                cell_position_9_state_read_chat_per_ff_session,
                cell_position_9_state_read_reaction_per_ff_session,
                cell_position_9_state_new_reaction_per_ff_session,
                cell_position_9_state_call_per_ff_session,
                cell_position_9_state_typing_per_ff_session
                ],
        name="friend_feed_state_cell_position_metrics",
        inner_join_with_mapping=False,
        bq_dialect="standard"
    )
    return mt



def friend_feed_page_view_metrics(start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    feed_page_view_session_count = Metric(col="feed_page_view_session_count", dist="cont", daily=True, cumulative=True)

    total_time_viewed_sec = Metric(col="total_time_viewed_sec", dist="cont", daily=True, cumulative=True)
    time_viewed_sec_per_ff_session = Metric(col='time_viewed_sec_per_ff_session', numerator='total_time_viewed_sec', denominator='feed_page_view_session_count', dist='ratio')
    feed_page_view_session_time_viewed_sec_0_1 = Metric(col="feed_page_view_session_time_viewed_sec_0_1", dist="cont", daily=True, cumulative=True)
    feed_page_view_session_time_viewed_sec_1_2 = Metric(col="feed_page_view_session_time_viewed_sec_1_2", dist="cont", daily=True, cumulative=True)
    feed_page_view_session_time_viewed_sec_2_5 = Metric(col="feed_page_view_session_time_viewed_sec_2_5", dist="cont", daily=True, cumulative=True)
    feed_page_view_session_time_viewed_sec_5_10 = Metric(col="feed_page_view_session_time_viewed_sec_5_10", dist="cont", daily=True, cumulative=True)
    feed_page_view_session_time_viewed_sec_10_30 = Metric(col="feed_page_view_session_time_viewed_sec_10_30", dist="cont", daily=True, cumulative=True)
    feed_page_view_session_time_viewed_sec_30_60 = Metric(col="feed_page_view_session_time_viewed_sec_30_60", dist="cont", daily=True, cumulative=True)
    feed_page_view_session_time_viewed_sec_60_300 = Metric(col="feed_page_view_session_time_viewed_sec_60_300", dist="cont", daily=True, cumulative=True)
    feed_page_view_session_time_viewed_sec_300_600 = Metric(col="feed_page_view_session_time_viewed_sec_300_600", dist="cont", daily=True, cumulative=True)
    feed_page_view_session_time_viewed_sec_600_plus = Metric(col="feed_page_view_session_time_viewed_sec_600_plus", dist="cont", daily=True, cumulative=True)
    time_viewed_sec_per_ff_session_0_1 = Metric(col='time_viewed_sec_per_ff_session_0_1', numerator='feed_page_view_session_time_viewed_sec_0_1', denominator='feed_page_view_session_count', dist='ratio')
    time_viewed_sec_per_ff_session_1_2 = Metric(col='time_viewed_sec_per_ff_session_1_2', numerator='feed_page_view_session_time_viewed_sec_1_2', denominator='feed_page_view_session_count', dist='ratio')
    time_viewed_sec_per_ff_session_2_5 = Metric(col='time_viewed_sec_per_ff_session_2_5', numerator='feed_page_view_session_time_viewed_sec_2_5', denominator='feed_page_view_session_count', dist='ratio')
    time_viewed_sec_per_ff_session_5_10 = Metric(col='time_viewed_sec_per_ff_session_5_10', numerator='feed_page_view_session_time_viewed_sec_5_10', denominator='feed_page_view_session_count', dist='ratio')
    time_viewed_sec_per_ff_session_10_30 = Metric(col='time_viewed_sec_per_ff_session_10_30', numerator='feed_page_view_session_time_viewed_sec_10_30', denominator='feed_page_view_session_count', dist='ratio')
    time_viewed_sec_per_ff_session_30_60 = Metric(col='time_viewed_sec_per_ff_session_30_60', numerator='feed_page_view_session_time_viewed_sec_30_60', denominator='feed_page_view_session_count', dist='ratio')
    time_viewed_sec_per_ff_session_60_300 = Metric(col='time_viewed_sec_per_ff_session_60_300', numerator='feed_page_view_session_time_viewed_sec_60_300', denominator='feed_page_view_session_count', dist='ratio')
    time_viewed_sec_per_ff_session_300_600 = Metric(col='time_viewed_sec_per_ff_session_300_600', numerator='feed_page_view_session_time_viewed_sec_300_600', denominator='feed_page_view_session_count', dist='ratio')
    time_viewed_sec_per_ff_session_600_plus = Metric(col='time_viewed_sec_per_ff_session_600_plus', numerator='feed_page_view_session_time_viewed_sec_600_plus', denominator='feed_page_view_session_count', dist='ratio')

    total_num_pull_to_refresh = Metric(col="total_num_pull_to_refresh", dist="cont", daily=True, cumulative=True)
    num_pull_to_refresh_per_ff_session = Metric(col='num_pull_to_refresh_per_ff_session', numerator='total_num_pull_to_refresh', denominator='feed_page_view_session_count', dist='ratio')
    feed_page_view_session_num_pull_to_refresh_0 = Metric(col="feed_page_view_session_num_pull_to_refresh_0", dist="cont", daily=True, cumulative=True)
    feed_page_view_session_num_pull_to_refresh_1 = Metric(col="feed_page_view_session_num_pull_to_refresh_1", dist="cont", daily=True, cumulative=True)
    feed_page_view_session_num_pull_to_refresh_2 = Metric(col="feed_page_view_session_num_pull_to_refresh_2", dist="cont", daily=True, cumulative=True)
    feed_page_view_session_num_pull_to_refresh_3_plus = Metric(col="feed_page_view_session_num_pull_to_refresh_3_plus", dist="cont", daily=True, cumulative=True)
    num_pull_to_refresh_per_ff_session_0 = Metric(col='num_pull_to_refresh_per_ff_session_0', numerator='feed_page_view_session_num_pull_to_refresh_0', denominator='feed_page_view_session_count', dist='ratio')
    num_pull_to_refresh_per_ff_session_1 = Metric(col='num_pull_to_refresh_per_ff_session_1', numerator='feed_page_view_session_num_pull_to_refresh_1', denominator='feed_page_view_session_count', dist='ratio')
    num_pull_to_refresh_per_ff_session_2 = Metric(col='num_pull_to_refresh_per_ff_session_2', numerator='feed_page_view_session_num_pull_to_refresh_2', denominator='feed_page_view_session_count', dist='ratio')
    num_pull_to_refresh_per_ff_session_3_plus = Metric(col='num_pull_to_refresh_per_ff_session_3_plus', numerator='feed_page_view_session_num_pull_to_refresh_3_plus', denominator='feed_page_view_session_count', dist='ratio')

    total_time_viewed_sec_after_p2r = Metric(col="total_time_viewed_sec_after_p2r", dist="cont", daily=True, cumulative=True)
    time_viewed_sec_after_p2r_per_ff_session = Metric(col='time_viewed_sec_after_p2r_per_ff_session', numerator='total_time_viewed_sec_after_p2r', denominator='feed_page_view_session_count', dist='ratio')
    feed_page_view_session_time_viewed_sec_after_p2r_0_2 = Metric(col="feed_page_view_session_time_viewed_sec_after_p2r_0_2", dist="cont", daily=True, cumulative=True)
    feed_page_view_session_time_viewed_sec_after_p2r_2_5 = Metric(col="feed_page_view_session_time_viewed_sec_after_p2r_2_5", dist="cont", daily=True, cumulative=True)
    feed_page_view_session_time_viewed_sec_after_p2r_5_10 = Metric(col="feed_page_view_session_time_viewed_sec_after_p2r_5_10", dist="cont", daily=True, cumulative=True)
    feed_page_view_session_time_viewed_sec_after_p2r_10_30 = Metric(col="feed_page_view_session_time_viewed_sec_after_p2r_10_30", dist="cont", daily=True, cumulative=True)
    feed_page_view_session_time_viewed_sec_after_p2r_30_60 = Metric(col="feed_page_view_session_time_viewed_sec_after_p2r_30_60", dist="cont", daily=True, cumulative=True)
    feed_page_view_session_time_viewed_sec_after_p2r_60_120 = Metric(col="feed_page_view_session_time_viewed_sec_after_p2r_60_120", dist="cont", daily=True, cumulative=True)
    feed_page_view_session_time_viewed_sec_after_p2r_120_600 = Metric(col="feed_page_view_session_time_viewed_sec_after_p2r_120_600", dist="cont", daily=True, cumulative=True)
    feed_page_view_session_time_viewed_sec_after_p2r_600_plus = Metric(col="feed_page_view_session_time_viewed_sec_after_p2r_600_plus", dist="cont", daily=True, cumulative=True)
    time_viewed_sec_after_p2r_per_ff_session_0_2 = Metric(col='time_viewed_sec_after_p2r_per_ff_session_0_2', numerator='feed_page_view_session_time_viewed_sec_after_p2r_0_2', denominator='feed_page_view_session_count', dist='ratio')
    time_viewed_sec_after_p2r_per_ff_session_2_5 = Metric(col='time_viewed_sec_after_p2r_per_ff_session_2_5', numerator='feed_page_view_session_time_viewed_sec_after_p2r_2_5', denominator='feed_page_view_session_count', dist='ratio')
    time_viewed_sec_after_p2r_per_ff_session_5_10 = Metric(col='time_viewed_sec_after_p2r_per_ff_session_5_10', numerator='feed_page_view_session_time_viewed_sec_after_p2r_5_10', denominator='feed_page_view_session_count', dist='ratio')
    time_viewed_sec_after_p2r_per_ff_session_10_30 = Metric(col='time_viewed_sec_after_p2r_per_ff_session_10_30', numerator='feed_page_view_session_time_viewed_sec_after_p2r_10_30', denominator='feed_page_view_session_count', dist='ratio')
    time_viewed_sec_after_p2r_per_ff_session_30_60 = Metric(col='time_viewed_sec_after_p2r_per_ff_session_30_60', numerator='feed_page_view_session_time_viewed_sec_after_p2r_30_60', denominator='feed_page_view_session_count', dist='ratio')
    time_viewed_sec_after_p2r_per_ff_session_60_120 = Metric(col='time_viewed_sec_after_p2r_per_ff_session_60_120', numerator='feed_page_view_session_time_viewed_sec_after_p2r_60_120', denominator='feed_page_view_session_count', dist='ratio')
    time_viewed_sec_after_p2r_per_ff_session_120_600 = Metric(col='time_viewed_sec_after_p2r_per_ff_session_120_600', numerator='feed_page_view_session_time_viewed_sec_after_p2r_120_600', denominator='feed_page_view_session_count', dist='ratio')
    time_viewed_sec_after_p2r_per_ff_session_600_plus = Metric(col='time_viewed_sec_after_p2r_per_ff_session_600_plus', numerator='feed_page_view_session_time_viewed_sec_after_p2r_600_plus', denominator='feed_page_view_session_count', dist='ratio')

    sql="""
        SELECT 
            ghost_user_id,
            TIMESTAMP(event_date) as ts,
            feed_page_view_session_count,

            feed_page_view_session_num_pull_to_refresh_0,
            feed_page_view_session_num_pull_to_refresh_1,
            feed_page_view_session_num_pull_to_refresh_2,
            feed_page_view_session_num_pull_to_refresh_3_plus,

            feed_page_view_session_time_viewed_sec_after_p2r_0_2,
            feed_page_view_session_time_viewed_sec_after_p2r_2_5,
            feed_page_view_session_time_viewed_sec_after_p2r_5_10,
            feed_page_view_session_time_viewed_sec_after_p2r_10_30,
            feed_page_view_session_time_viewed_sec_after_p2r_30_60,
            feed_page_view_session_time_viewed_sec_after_p2r_60_120,
            feed_page_view_session_time_viewed_sec_after_p2r_120_600,
            feed_page_view_session_time_viewed_sec_after_p2r_600_plus,

            feed_page_view_session_time_viewed_sec_0_1,
            feed_page_view_session_time_viewed_sec_1_2,
            feed_page_view_session_time_viewed_sec_2_5,
            feed_page_view_session_time_viewed_sec_5_10,
            feed_page_view_session_time_viewed_sec_10_30,
            feed_page_view_session_time_viewed_sec_30_60,
            feed_page_view_session_time_viewed_sec_60_300,
            feed_page_view_session_time_viewed_sec_300_600,
            feed_page_view_session_time_viewed_sec_600_plus,

            total_num_pull_to_refresh,
            total_time_viewed_sec_after_p2r,
            total_time_viewed_sec

        FROM
          `sc-analytics.report_messaging.feed_page_view_user_level_20*`
    WHERE concat('20',_table_suffix) between '{start_date}' and '{end_date}'
        """
    
    def sql_callable(start_date, end_date):
#         start_date, end_date = start_date[-6:], end_date[-6:]
        return sql.format(start_date=start_date, end_date=end_date) 

    mt = MetricTable(
        sql = sql.format(start_date=start_date, end_date=end_date),
        sql_callable = sql_callable,
        metrics=[
                feed_page_view_session_count, 
                total_time_viewed_sec, 
                time_viewed_sec_per_ff_session, 
                feed_page_view_session_time_viewed_sec_0_1, 
                feed_page_view_session_time_viewed_sec_1_2, 
                feed_page_view_session_time_viewed_sec_2_5, 
                feed_page_view_session_time_viewed_sec_5_10, 
                feed_page_view_session_time_viewed_sec_10_30, 
                feed_page_view_session_time_viewed_sec_30_60, 
                feed_page_view_session_time_viewed_sec_60_300, 
                feed_page_view_session_time_viewed_sec_300_600, 
                feed_page_view_session_time_viewed_sec_600_plus, 
                time_viewed_sec_per_ff_session_0_1, 
                time_viewed_sec_per_ff_session_1_2, 
                time_viewed_sec_per_ff_session_2_5, 
                time_viewed_sec_per_ff_session_5_10, 
                time_viewed_sec_per_ff_session_10_30, 
                time_viewed_sec_per_ff_session_30_60, 
                time_viewed_sec_per_ff_session_60_300, 
                time_viewed_sec_per_ff_session_300_600, 
                time_viewed_sec_per_ff_session_600_plus, 
                total_num_pull_to_refresh, 
                num_pull_to_refresh_per_ff_session, 
                feed_page_view_session_num_pull_to_refresh_0, 
                feed_page_view_session_num_pull_to_refresh_1, 
                feed_page_view_session_num_pull_to_refresh_2, 
                feed_page_view_session_num_pull_to_refresh_3_plus, 
                num_pull_to_refresh_per_ff_session_0, 
                num_pull_to_refresh_per_ff_session_1, 
                num_pull_to_refresh_per_ff_session_2, 
                num_pull_to_refresh_per_ff_session_3_plus, 
                total_time_viewed_sec_after_p2r, 
                time_viewed_sec_after_p2r_per_ff_session, 
                feed_page_view_session_time_viewed_sec_after_p2r_0_2, 
                feed_page_view_session_time_viewed_sec_after_p2r_2_5, 
                feed_page_view_session_time_viewed_sec_after_p2r_5_10, 
                feed_page_view_session_time_viewed_sec_after_p2r_10_30, 
                feed_page_view_session_time_viewed_sec_after_p2r_30_60, 
                feed_page_view_session_time_viewed_sec_after_p2r_60_120, 
                feed_page_view_session_time_viewed_sec_after_p2r_120_600, 
                feed_page_view_session_time_viewed_sec_after_p2r_600_plus, 
                time_viewed_sec_after_p2r_per_ff_session_0_2, 
                time_viewed_sec_after_p2r_per_ff_session_2_5, 
                time_viewed_sec_after_p2r_per_ff_session_5_10, 
                time_viewed_sec_after_p2r_per_ff_session_10_30, 
                time_viewed_sec_after_p2r_per_ff_session_30_60, 
                time_viewed_sec_after_p2r_per_ff_session_60_120, 
                time_viewed_sec_after_p2r_per_ff_session_120_600, 
                time_viewed_sec_after_p2r_per_ff_session_600_plus
                ],
        name="friend_feed_page_view_metrics",
        inner_join_with_mapping=False,
        bq_dialect="standard"
    )
    return mt


def friend_feed_ppv_metrics(start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    total_tab_session = Metric(col="total_tab_session", dist="cont", daily=True, cumulative=True)
    tab_session_friends_tab = Metric(col="tab_session_friends_tab", dist="cont", daily=True, cumulative=True)
    tab_session_friends_feed_tab = Metric(col="tab_session_friends_feed_tab", dist="cont", daily=True, cumulative=True)
    tab_session_stories_tab = Metric(col="tab_session_stories_tab", dist="cont", daily=True, cumulative=True)
    tab_session_story_friends_tab = Metric(col="tab_session_story_friends_tab", dist="cont", daily=True, cumulative=True)
    tab_session_camera_tab = Metric(col="tab_session_camera_tab", dist="cont", daily=True, cumulative=True)
    tab_session_snap_opera_tab = Metric(col="tab_session_snap_opera_tab", dist="cont", daily=True, cumulative=True)
    tab_session_map_tab = Metric(col="tab_session_map_tab", dist="cont", daily=True, cumulative=True)
    tab_session_spotlight_tab = Metric(col="tab_session_spotlight_tab", dist="cont", daily=True, cumulative=True)
    tab_session_friends_tab_active_day = Metric(col="tab_session_friends_tab_active_day", dist="cont", daily=True, cumulative=True)
    tab_session_friends_feed_tab_active_day = Metric(col="tab_session_friends_feed_tab_active_day", dist="cont", daily=True, cumulative=True)
    tab_session_stories_tab_active_day = Metric(col="tab_session_stories_tab_active_day", dist="cont", daily=True, cumulative=True)
    tab_session_story_friends_tab_active_day = Metric(col="tab_session_story_friends_tab_active_day", dist="cont", daily=True, cumulative=True)
    tab_session_camera_tab_active_day = Metric(col="tab_session_camera_tab_active_day", dist="cont", daily=True, cumulative=True)
    tab_session_snap_opera_tab_active_day = Metric(col="tab_session_snap_opera_tab_active_day", dist="cont", daily=True, cumulative=True)
    tab_session_map_tab_active_day = Metric(col="tab_session_map_tab_active_day", dist="cont", daily=True, cumulative=True)
    tab_session_spotlight_tab_active_day = Metric(col="tab_session_spotlight_tab_active_day", dist="cont", daily=True, cumulative=True)

    total_view_time_sec = Metric(col="total_view_time_sec", dist="cont", daily=True, cumulative=True)
    view_time_sec_friends_feed = Metric(col="view_time_sec_friends_feed", dist="cont", daily=True, cumulative=True)
    view_time_sec_chat = Metric(col="view_time_sec_chat", dist="cont", daily=True, cumulative=True)
    view_time_sec_call = Metric(col="view_time_sec_call", dist="cont", daily=True, cumulative=True)
    view_time_sec_friends_tab = Metric(col="view_time_sec_friends_tab", dist="cont", daily=True, cumulative=True)
    uu_w_0_1_chat_page_session = Metric(col="uu_w_0_1_chat_page_session", dist="cont", daily=True, cumulative=True)
    uu_w_1_5_chat_page_session = Metric(col="uu_w_1_5_chat_page_session", dist="cont", daily=True, cumulative=True)
    uu_w_5_10_chat_page_session = Metric(col="uu_w_5_10_chat_page_session", dist="cont", daily=True, cumulative=True)
    uu_w_10_15_chat_page_session = Metric(col="uu_w_10_15_chat_page_session", dist="cont", daily=True, cumulative=True)
    uu_w_15_20_chat_page_session = Metric(col="uu_w_15_20_chat_page_session", dist="cont", daily=True, cumulative=True)
    uu_w_20_plus_chat_page_session = Metric(col="uu_w_20_plus_chat_page_session", dist="cont", daily=True, cumulative=True)
    uu_w_0_1_friends_feed_session = Metric(col="uu_w_0_1_friends_feed_session", dist="cont", daily=True, cumulative=True)
    uu_w_1_5_friends_feed_session = Metric(col="uu_w_1_5_friends_feed_session", dist="cont", daily=True, cumulative=True)
    uu_w_5_plus_friends_feed_session = Metric(col="uu_w_5_plus_friends_feed_session", dist="cont", daily=True, cumulative=True)

    sql="""
        SELECT 
            ghost_user_id,
            TIMESTAMP(event_date) as ts,

            total_tab_session_count AS total_tab_session,
            tab_session_friends_tab AS tab_session_friends_tab,
            tab_session_friends_feed_tab AS tab_session_friends_feed_tab,
            tab_session_stories_tab AS tab_session_stories_tab,
            tab_session_story_friends_tab AS tab_session_story_friends_tab,
            tab_session_camera_tab AS tab_session_camera_tab,
            tab_session_snap_opera_tab AS tab_session_snap_opera_tab,
            tab_session_map_tab AS tab_session_map_tab,
            tab_session_spotlight_tab AS tab_session_spotlight_tab,

            IF(tab_session_friends_tab>0,1,0) AS tab_session_friends_tab_active_day,
            IF(tab_session_friends_feed_tab>0,1,0) AS tab_session_friends_feed_tab_active_day,
            IF(tab_session_stories_tab>0,1,0) AS tab_session_stories_tab_active_day,
            IF(tab_session_story_friends_tab>0,1,0) AS tab_session_story_friends_tab_active_day,
            IF(tab_session_camera_tab>0,1,0) AS tab_session_camera_tab_active_day,
            IF(tab_session_snap_opera_tab>0,1,0) AS tab_session_snap_opera_tab_active_day,
            IF(tab_session_map_tab>0,1,0) AS tab_session_map_tab_active_day,
            IF(tab_session_spotlight_tab>0,1,0) AS tab_session_spotlight_tab_active_day,

            total_view_time_sec,
            view_time_sec_friends_feed,
            view_time_sec_chat,
            view_time_sec_call,
            view_time_sec_friends_tab,

            IF(ppv_count_chat_0_1_sec>0,1,0) AS uu_w_0_1_chat_page_session,
            IF(ppv_count_chat_1_5_sec>0,1,0) AS uu_w_1_5_chat_page_session,
            IF(ppv_count_chat_5_10_sec>0,1,0) AS uu_w_5_10_chat_page_session,
            IF(ppv_count_chat_10_15_sec>0,1,0) AS uu_w_10_15_chat_page_session,
            IF(ppv_count_chat_15_20_sec>0,1,0) AS uu_w_15_20_chat_page_session,
            IF(ppv_count_chat_20_plus_sec>0,1,0) AS uu_w_20_plus_chat_page_session,

            IF(ppv_count_friends_feed_0_1_sec>0,1,0) AS uu_w_0_1_friends_feed_session,
            IF(ppv_count_friends_feed_1_5_sec>0,1,0) AS uu_w_1_5_friends_feed_session,
            IF(ppv_count_friends_feed_5_plus_sec>0,1,0) AS uu_w_5_plus_friends_feed_session,

        FROM
          `sc-analytics.report_messaging.page_page_view_user_level_20*`
    WHERE concat('20',_table_suffix) between '{start_date}' and '{end_date}'
        """
    
    def sql_callable(start_date, end_date):
#         start_date, end_date = start_date[-6:], end_date[-6:]
        return sql.format(start_date=start_date, end_date=end_date) 

    mt = MetricTable(
        sql = sql.format(start_date=start_date, end_date=end_date),
        sql_callable = sql_callable,
        metrics=[
                total_tab_session,
                tab_session_friends_tab,
                tab_session_friends_feed_tab,
                tab_session_stories_tab,
                tab_session_story_friends_tab,
                tab_session_camera_tab,
                tab_session_snap_opera_tab,
                tab_session_map_tab,
                tab_session_spotlight_tab,

                tab_session_friends_tab_active_day,
                tab_session_friends_feed_tab_active_day,
                tab_session_stories_tab_active_day,
                tab_session_story_friends_tab_active_day,
                tab_session_camera_tab_active_day,
                tab_session_snap_opera_tab_active_day,
                tab_session_map_tab_active_day,
                tab_session_spotlight_tab_active_day,

                total_view_time_sec,
                view_time_sec_friends_feed,
                view_time_sec_chat,
                view_time_sec_call,
                view_time_sec_friends_tab,
                uu_w_0_1_chat_page_session,
                uu_w_1_5_chat_page_session,
                uu_w_5_10_chat_page_session,
                uu_w_10_15_chat_page_session,
                uu_w_15_20_chat_page_session,
                uu_w_20_plus_chat_page_session,
                uu_w_0_1_friends_feed_session,
                uu_w_1_5_friends_feed_session,
                uu_w_5_plus_friends_feed_session
                ],
        name="friend_feed_ppv_metrics",
        inner_join_with_mapping=False,
        bq_dialect="standard"
    )
    return mt

def sponsored_snap_metrics(start_date, end_date):
    start_date, end_date = start_date[-6:], end_date[-6:]

    chat_create_cnt = Metric(col="chat_create_cnt", dist="cont", daily=True, cumulative=True)
    chat_create_cnt_uu = Metric(col="chat_create_cnt_uu", dist="cont", daily=True, cumulative=True)
    chat_send_cnt = Metric(col="chat_send_cnt", dist="cont", daily=True, cumulative=True)
    chat_send_cnt_uu = Metric(col="chat_send_cnt_uu", dist="cont", daily=True, cumulative=True)
    chat_save_cnt = Metric(col="chat_save_cnt", dist="cont", daily=True, cumulative=True)
    chat_save_cnt_uu = Metric(col="chat_save_cnt_uu", dist="cont", daily=True, cumulative=True)
    chat_view_cnt = Metric(col="chat_view_cnt", dist="cont", daily=True, cumulative=True)
    chat_view_cnt_uu = Metric(col="chat_view_cnt_uu", dist="cont", daily=True, cumulative=True)
    chat_send_text_cnt = Metric(col="chat_send_text_cnt", dist="cont", daily=True, cumulative=True)
    chat_send_text_cnt_uu = Metric(col="chat_send_text_cnt_uu", dist="cont", daily=True, cumulative=True)
    chat_send_note_cnt = Metric(col="chat_send_note_cnt", dist="cont", daily=True, cumulative=True)
    chat_send_note_cnt_uu = Metric(col="chat_send_note_cnt_uu", dist="cont", daily=True, cumulative=True)
    chat_send_media_cnt = Metric(col="chat_send_media_cnt", dist="cont", daily=True, cumulative=True)
    chat_send_media_cnt_uu = Metric(col="chat_send_media_cnt_uu", dist="cont", daily=True, cumulative=True)
    chat_send_sticker_cnt = Metric(col="chat_send_sticker_cnt", dist="cont", daily=True, cumulative=True)
    chat_send_sticker_cnt_uu = Metric(col="chat_send_sticker_cnt_uu", dist="cont", daily=True, cumulative=True)
    chat_conversation_clear_cnt = Metric(col="chat_conversation_clear_cnt", dist="cont", daily=True, cumulative=True)
    chat_conversation_clear_cnt_uu = Metric(col="chat_conversation_clear_cnt_uu", dist="cont", daily=True, cumulative=True)
    story_story_view_cnt = Metric(col="story_story_view_cnt", dist="cont", daily=True, cumulative=True)
    story_story_view_cnt_uu = Metric(col="story_story_view_cnt_uu", dist="cont", daily=True, cumulative=True)
    story_snap_view_cnt = Metric(col="story_snap_view_cnt", dist="cont", daily=True, cumulative=True)
    story_snap_view_cnt_uu = Metric(col="story_snap_view_cnt_uu", dist="cont", daily=True, cumulative=True)

    sql="""
            SELECT
            ghost_user_id,
            TIMESTAMP(DATE(event_date)) AS ts,
            SUM(chat_create_cnt) AS chat_create_cnt,
            IF(SUM(chat_create_cnt) > 0, 1, 0) AS chat_create_cnt_uu,
            SUM(chat_send_cnt) AS chat_send_cnt,
            IF(SUM(chat_send_cnt) > 0, 1, 0) AS chat_send_cnt_uu,
            SUM(chat_save_cnt) AS chat_save_cnt,
            IF(SUM(chat_save_cnt) > 0, 1, 0) AS chat_save_cnt_uu,
            SUM(chat_view_cnt) AS chat_view_cnt,
            IF(SUM(chat_view_cnt) > 0, 1, 0) AS chat_view_cnt_uu,
            SUM(chat_send_text_cnt) AS chat_send_text_cnt,
            IF(SUM(chat_send_text_cnt) > 0, 1, 0) AS chat_send_text_cnt_uu,
            SUM(chat_send_note_cnt) AS chat_send_note_cnt,
            IF(SUM(chat_send_note_cnt) > 0, 1, 0) AS chat_send_note_cnt_uu,
            SUM(chat_send_media_cnt) AS chat_send_media_cnt,
            IF(SUM(chat_send_media_cnt) > 0, 1, 0) AS chat_send_media_cnt_uu,
            SUM(chat_send_sticker_cnt) AS chat_send_sticker_cnt,
            IF(SUM(chat_send_sticker_cnt) > 0, 1, 0) AS chat_send_sticker_cnt_uu,
            SUM(chat_conversation_clear_cnt) AS chat_conversation_clear_cnt,
            IF(SUM(chat_conversation_clear_cnt) > 0, 1, 0) AS chat_conversation_clear_cnt_uu,
            SUM(story_story_view_cnt) AS story_story_view_cnt,
            IF(SUM(story_story_view_cnt) > 0, 1, 0) AS story_story_view_cnt_uu,
            SUM(story_snap_view_cnt) AS story_snap_view_cnt,
            IF(SUM(story_snap_view_cnt) > 0, 1, 0) AS story_snap_view_cnt_uu,
            FROM 
            `sc-analytics.report_growth.sponsored_snap_msg_engagement_user_20*`
            WHERE _table_suffix between '{start_date}' and '{end_date}'
            GROUP BY 1,2
            """
    
    def sql_callable(start_date, end_date):
        start_date, end_date = start_date[-6:], end_date[-6:]
        return sql.format(start_date=start_date, end_date=end_date)
    
    
    mt = MetricTable(
        sql=sql.format(start_date=start_date, end_date=end_date),
        sql_callable=sql_callable,
        metrics=[
                chat_create_cnt, 
                chat_create_cnt_uu, 
                chat_send_cnt, 
                chat_send_cnt_uu, 
                chat_save_cnt, 
                chat_save_cnt_uu, 
                chat_view_cnt, 
                chat_view_cnt_uu, 
                chat_send_text_cnt, 
                chat_send_text_cnt_uu, 
                chat_send_note_cnt, 
                chat_send_note_cnt_uu, 
                chat_send_media_cnt, 
                chat_send_media_cnt_uu, 
                chat_send_sticker_cnt, 
                chat_send_sticker_cnt_uu, 
                chat_conversation_clear_cnt, 
                chat_conversation_clear_cnt_uu, 
                story_story_view_cnt, 
                story_story_view_cnt_uu, 
                story_snap_view_cnt, 
                story_snap_view_cnt_uu, 
                ],
        name="sponsored_snap_metrics",
        inner_join_with_mapping=False,
        bq_dialect="standard"
    )
    return mt

#Unused

# def in_app_notification_chat_metrics(start_date, end_date):
#     start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
#     end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
#     source_table = """
#     `sc-analytics.report_growth.friends_feed_chat_user_level_2*`
#     WHERE concat('2',_table_suffix) between '{start}' and '{end}'
#     """.format(
#         start=start_date,
#         end=end_date,
#     )
#     chat_create_in_app_notification = Metric(col="chat_create_in_app_notification", dist="cont", daily=True, cumulative=True)
#     chat_create_in_app_notification_uu = Metric(col="chat_create_in_app_notification_uu", dist="cont", daily=True, cumulative=True)
#     chat_send_in_app_notification = Metric(col="chat_send_in_app_notification", dist="cont", daily=True, cumulative=True)
#     chat_send_in_app_notification_uu = Metric(col="chat_send_in_app_notification_uu", dist="cont", daily=True, cumulative=True)
#     chat_screenshot_in_app_notification = Metric(col="chat_screenshot_in_app_notification", dist="cont", daily=True, cumulative=True)
#     chat_screenshot_in_app_notification_uu = Metric(col="chat_screenshot_in_app_notification_uu", dist="cont", daily=True, cumulative=True)
#     chat_drawer_action_in_app_notification = Metric(col="chat_drawer_action_in_app_notification", dist="cont", daily=True, cumulative=True)
#     chat_drawer_action_in_app_notification_uu = Metric(col="chat_drawer_action_in_app_notification_uu", dist="cont", daily=True, cumulative=True)

#     mt = MetricTable(
#         sql="""
#         SELECT 
#             ghost_user_id,
#             ts AS ts, 
#             SUM(CHAT_CHAT_CREATE_IN_APP_NOTIFICATION) as chat_create_in_app_notification,
#             SUM(CHAT_CHAT_SEND_SOURCE_IN_APP_NOTIFICATION) as chat_send_in_app_notification,
#             SUM(chat_screenshot_in_app_notification) as chat_screenshot_in_app_notification,
#             SUM(chat_drawer_action_in_app_notification) as chat_drawer_action_in_app_notification,
#             SUM(CHAT_CHAT_CREATE_IN_APP_NOTIFICATION_UU) as chat_create_in_app_notification_uu,
#             SUM(CHAT_CHAT_SEND_SOURCE_IN_APP_NOTIFICATION_UU) as chat_send_in_app_notification_uu,
#             SUM(chat_screenshot_in_app_notification_uu) as chat_screenshot_in_app_notification_uu,
#             SUM(chat_drawer_action_in_app_notification_uu) as chat_drawer_action_in_app_notification_uu
#         FROM
#           {source_table}
#         GROUP BY 1,2
#         """.format(source_table=source_table),
#         metrics=[chat_create_in_app_notification, 
#                 chat_create_in_app_notification_uu,
#                 chat_send_in_app_notification,
#                 chat_send_in_app_notification_uu,
#                 chat_screenshot_in_app_notification,
#                 chat_screenshot_in_app_notification_uu,
#                 chat_drawer_action_in_app_notification,
#                 chat_drawer_action_in_app_notification_uu],
#         name="in_app_notification_chat_metrics",
#         inner_join_with_mapping=False,
#         bq_dialect="standard"
#     )
#     return mt

# def group_metrics(start_date, end_date):
#     """
#     Parameters
#     ----------
#     start_date
#     end_date
#     Returns
#     -------
#     mt: MetricTable
#     """
#     start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
#     end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
#     source_table = """
#     `sc-analytics.report_growth.chatv3_user_activity_2*`
#     WHERE concat('2',_table_suffix) between '{start}' and '{end}'
#     """.format(
#         start=start_date,
#         end=end_date,
#     )

#     GROUP_DIRECT_SNAP_SEND = Metric(
#         'GROUP_DIRECT_SNAP_SEND',
#         'Direct Snaps sent to Groups',
#         dist='cont',
#         daily=True,
#         cumulative=True,
#     )

#     GROUP_DIRECT_SNAP_SEND_USER = Metric(
#         'GROUP_DIRECT_SNAP_SEND_USER',
#         'Unique user with 1+ Direct Snaps sent to Groups',
#         dist='bin',
#         daily=True,
#         cumulative=True,
#     )
#     GROUP_DIRECT_SNAP_SEND_ACTIVE_DAY = Metric(
#         'GROUP_DIRECT_SNAP_SEND_ACTIVE_DAY',
#         'ACTIVE DAY with 1+ Direct Snaps sent to Groups',
#         dist='cont',
#         daily=True,
#         cumulative=True,
#     )
#     GROUP_DIRECT_SNAP_VIEW = Metric(
#         'GROUP_DIRECT_SNAP_VIEW',
#         'Direct Snaps viewed in Groups',
#         dist='cont',
#         daily=True,
#         cumulative=True,
#     )

#     GROUP_DIRECT_SNAP_VIEW_USER = Metric(
#         'GROUP_DIRECT_SNAP_VIEW_USER',
#         'Unique user with 1+ Direct Snaps viewed in Groups',
#         dist='bin',
#         daily=True,
#         cumulative=True,
#     )
#     GROUP_DIRECT_SNAP_VIEW_ACTIVE_DAY = Metric(
#         'GROUP_DIRECT_SNAP_VIEW_ACTIVE_DAY',
#         'ACTIVE DAY with 1+ Direct Snaps viewed in Groups',
#         dist='cont',
#         daily=True,
#         cumulative=True,
#     )
#     GROUP_CHAT_CHAT_SEND = Metric(
#         'GROUP_CHAT_CHAT_SEND',
#         'Chat sent to Groups',
#         dist='cont',
#         daily=True,
#         cumulative=True,
#     )

#     GROUP_CHAT_CHAT_SEND_USER = Metric(
#         'GROUP_CHAT_CHAT_SEND_USER',
#         'Unique user with 1+ Chat sent to Groups',
#         dist='bin',
#         daily=True,
#         cumulative=True,
#     )
#     GROUP_CHAT_CHAT_SEND_ACTIVE_DAY = Metric(
#         'GROUP_CHAT_CHAT_SEND_ACTIVE_DAY',
#         'ACTIVE DAY with 1+ Chat sent to Groups',
#         dist='cont',
#         daily=True,
#         cumulative=True,
#     )
#     GROUP_CHAT_CHAT_VIEW = Metric(
#         'GROUP_CHAT_CHAT_VIEW',
#         'Chat viewed in Groups',
#         dist='cont',
#         daily=True,
#         cumulative=True,
#     )

#     GROUP_CHAT_CHAT_VIEW_USER = Metric(
#         'GROUP_CHAT_CHAT_VIEW_USER',
#         'Unique user with 1+ Chat viewed in Groups',
#         dist='bin',
#         daily=True,
#         cumulative=True,
#     )
#     GROUP_CHAT_CHAT_VIEW_ACTIVE_DAY = Metric(
#         'GROUP_CHAT_CHAT_VIEW_ACTIVE_DAY',
#         'ACTIVE DAY with 1+ Chat viewed in Groups',
#         dist='cont',
#         daily=True,
#         cumulative=True,
#     )
#     GROUP_CREATE = Metric(
#         'GROUP_CREATE',
#         'Created Groups',
#         dist='cont',
#         daily=True,
#         cumulative=True,
#     )

#     GROUP_CREATE_USER = Metric(
#         'GROUP_CREATE_USER',
#         'Unique user with 1+ created Groups',
#         dist='bin',
#         daily=True,
#         cumulative=True,
#     )
#     GROUP_CREATE_ACTIVE_DAY = Metric(
#         'GROUP_CREATE_ACTIVE_DAY',
#         'ACTIVE DAY with 1+ created Groups',
#         dist='cont',
#         daily=True,
#         cumulative=True,
#     )
#     GROUP_USER_ADD = Metric(
#         'GROUP_USER_ADD',
#         'Add Friend To Group actions',
#         dist='cont',
#         daily=True,
#         cumulative=True,
#     )

#     GROUP_USER_ADD_USER = Metric(
#         'GROUP_USER_ADD_USER',
#         'Unique users with 1+ action of Add To Group',
#         dist='bin',
#         daily=True,
#         cumulative=True,
#     )
#     GROUP_USER_ADD_ACTIVE_DAY = Metric(
#         'GROUP_USER_ADD_ACTIVE_DAY',
#         'ACTIVE DAY with 1+ action of Add To Group',
#         dist='cont',
#         daily=True,
#         cumulative=True,
#     )
#     mt = MetricTable(
#         sql="""
#         SELECT
#         PARSE_TIMESTAMP('%Y%m%d', concat('2',_table_suffix)) as ts,
#         ghost_user_id,
#         IF(SUM(GROUP_DIRECT_SNAP_SEND)>0,1,0) AS GROUP_DIRECT_SNAP_SEND_USER,
#         IF(SUM(GROUP_DIRECT_SNAP_VIEW)>0,1,0) AS GROUP_DIRECT_SNAP_VIEW_USER,
#         IF(SUM(GROUP_CHAT_CHAT_SEND)>0,1,0) AS GROUP_CHAT_CHAT_SEND_USER,
#         IF(SUM(GROUP_CHAT_CHAT_VIEW)>0,1,0) AS GROUP_CHAT_CHAT_VIEW_USER,
#         IF(SUM(GROUP_USER_ADD)>0,1,0) AS GROUP_USER_ADD_USER,
#         IF(SUM(GROUP_CREATE)>0,1,0) AS GROUP_CREATE_USER,
#         IF(SUM(GROUP_DIRECT_SNAP_SEND)>0,1,0) AS GROUP_DIRECT_SNAP_SEND_ACTIVE_DAY,
#         IF(SUM(GROUP_DIRECT_SNAP_VIEW)>0,1,0) AS GROUP_DIRECT_SNAP_VIEW_ACTIVE_DAY,
#         IF(SUM(GROUP_CHAT_CHAT_SEND)>0,1,0) AS GROUP_CHAT_CHAT_SEND_ACTIVE_DAY,
#         IF(SUM(GROUP_CHAT_CHAT_VIEW)>0,1,0) AS GROUP_CHAT_CHAT_VIEW_ACTIVE_DAY,
#         IF(SUM(GROUP_USER_ADD)>0,1,0) AS GROUP_USER_ADD_ACTIVE_DAY,
#         IF(SUM(GROUP_CREATE)>0,1,0) AS GROUP_CREATE_ACTIVE_DAY,
#         SUM(GROUP_DIRECT_SNAP_SEND) GROUP_DIRECT_SNAP_SEND,
#         SUM(GROUP_DIRECT_SNAP_VIEW) GROUP_DIRECT_SNAP_VIEW,
#         SUM(GROUP_CHAT_CHAT_SEND) GROUP_CHAT_CHAT_SEND,
#         SUM(GROUP_CHAT_CHAT_VIEW) GROUP_CHAT_CHAT_VIEW,
#         SUM(GROUP_USER_ADD) GROUP_USER_ADD,
#         SUM(GROUP_CREATE) GROUP_CREATE,
#         FROM
#           {source_table}
#         GROUP BY 1,2
#         """.format(source_table=source_table),
#         metrics=[
#         GROUP_DIRECT_SNAP_SEND_USER, 
#         GROUP_DIRECT_SNAP_VIEW_USER, 
#         GROUP_CHAT_CHAT_SEND_USER, 
#         GROUP_CHAT_CHAT_VIEW_USER, 
#         GROUP_USER_ADD_USER, 
#         GROUP_CREATE_USER,
#         GROUP_DIRECT_SNAP_SEND_ACTIVE_DAY, 
#         GROUP_DIRECT_SNAP_VIEW_ACTIVE_DAY, 
#         GROUP_CHAT_CHAT_SEND_ACTIVE_DAY, 
#         GROUP_CHAT_CHAT_VIEW_ACTIVE_DAY, 
#         GROUP_USER_ADD_ACTIVE_DAY, 
#         GROUP_CREATE_ACTIVE_DAY, 
#         GROUP_DIRECT_SNAP_SEND, 
#         GROUP_DIRECT_SNAP_VIEW, 
#         GROUP_CHAT_CHAT_SEND, 
#         GROUP_CHAT_CHAT_VIEW, 
#         GROUP_USER_ADD, 
#         GROUP_CREATE, ],
#         name="group_metrics",
#         bq_dialect="standard"
#     )
#     return mt


# def ff_available_story_metrics(start_date, end_date):
#     """
#     Parameters
#     ----------
#     start_date
#     end_date
#     Returns
#     -------
#     mt: MetricTable
#     >>> ff_available_story_metrics('20170501', '20170503')
#     """
#     start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
#     end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
#     source_table = """
#         `sc-analytics.report_messaging.friend_feed_ready_events_*`
#     WHERE _table_suffix between '{start}' and '{end}'
#     """.format(
#         start=start_date,
#         end=end_date,
#     )

#     number_of_available_stories_on_synced_feed = Metric(
#         'number_of_available_stories_on_synced_feed',
#         dist='cont',
#         daily=True,
#         cumulative=True,
#     )

#     number_of_available_stories_on_cached_feed = Metric(
#         'number_of_available_stories_on_cached_feed',
#         dist='cont',
#         daily=True,
#         cumulative=True,
#     )

#     ff_entry_volume = Metric(
#         'ff_entry_volume',
#         dist='cont',
#         daily=True,
#         cumulative=True,
#     )

#     ff_entry_with_sync_complete = Metric(
#         'ff_entry_with_sync_complete',
#         dist='cont',
#         daily=True,
#         cumulative=True,
#     )

#     ff_entry_with_sync_incomplete = Metric(
#         'ff_entry_with_sync_incomplete',
#         dist='cont',
#         daily=True,
#         cumulative=True,
#     )

#     mt = MetricTable(
#         sql="""
#         SELECT
#         PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
#         ghost_user_id,
#         SUM(number_of_available_stories_on_synced_feed) AS number_of_available_stories_on_synced_feed,
#         SUM(number_of_available_stories_on_cached_feed) AS number_of_available_stories_on_cached_feed,
#         COUNT(1) AS ff_entry_volume,
#         SUM(IF(sync_time_ms<=sync_feed_entry_time_ms,1,0)) AS ff_entry_with_sync_complete,
#         SUM(IF(sync_time_ms>sync_feed_entry_time_ms,1,0)) AS ff_entry_with_sync_incomplete,        
#         FROM
#           {source_table}
#         WHERE event_name = 'FRIEND_FEED_READY'
#         GROUP BY 1, 2
#         """.format(source_table=source_table),
#         metrics=[number_of_available_stories_on_synced_feed, 
#             number_of_available_stories_on_cached_feed,
#             ff_entry_volume,
#             ff_entry_with_sync_complete,
#             ff_entry_with_sync_incomplete],
#         name="ff_available_story_metrics",
#         bq_dialect="standard"
#     )
#     return mt


# def chat_send_to_view_time_metrics(start_date, end_date):
#     """
#     Parameters
#     ----------
#     start_date
#     end_date
#     Returns
#     -------
#     mt: MetricTable
#     >>> chat_send_to_view_time_metrics('20170501', '20170503')
#     """
#     start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
#     end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
#     source_table = """
#     `sc-analytics.prod_analytics_chat.daily_events_*`
#     WHERE _table_suffix between '{start}' and '{end}'
#     """.format(
#         start=start_date,
#         end=end_date,
#     )

#     group_chat_send_to_view_time = Metric(
#         'group_chat_send_to_view_time',
#         'Chat send to view time on Group Chats',
#         dist='quantile',
#         daily=True,
#         cumulative=True,
#     )

#     non_group_chat_send_to_view_time = Metric(
#         'non_group_chat_send_to_view_time',
#         'Chat send to view time on Non-Group Chats',
#         dist='quantile',
#         daily=True,
#         cumulative=True,
#     )

#     mt = MetricTable(
#         sql="""
#         SELECT
#         PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
#         ghost_user_id,
#         IF(mischief_id IS NULL, ack_time_sec, NULL) non_group_chat_send_to_view_time,
#         IF(mischief_id IS NOT NULL, ack_time_sec, NULL) group_chat_send_to_view_time,
#         FROM
#           {source_table}
#         AND event_name = 'CHAT_CHAT_VIEW'
#         """.format(source_table=source_table),
#         metrics=[group_chat_send_to_view_time, non_group_chat_send_to_view_time], #, friends_feed_session_user],
#         name="chat_send_to_view_time_metrics",
#         quantile_metrics=True,
#         bq_dialect="standard"
#     )
#     return mt



