"""
Music Metrics
Contact: kjiang@
"""

from __future__ import division, print_function
from banjo.abtest.metric import (
    Metric, MetricTable, abtest_metrics_sql_helper
)
# from datetime import timedelta
import pandas as pd
import logging
from banjo.abtest.metric import POSITIVE, NEGATIVE

logger = logging.getLogger(__name__)

logger.warning("Music Metrics imported. Check the actual "
               "queries run to ensure correctness")

__all__ = [
    'music_overall',
    'music_licensed',
    'music_ugc',
    'music_spotlight_sounds',
    'music_request',
]

def music_overall(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    >>> music_overall('20170501', '20170503')
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    
    cont_metric_list = [
        'music_picker_open',
        'music_picker_track_playback',
        'music_pick',
        'music_picker_close',
        'music_picker_session_w_playback',
        'avg_music_picker_time_spent',
        'music_picker_open_preview',
        'music_pick_from_preview',
        'music_picker_open_camera',
        'music_pick_from_camera',
        'snap_create_w_music',
        'snap_preview_w_music',
        'snap_action_w_music',
        'snap_discard_w_music',
        'snap_preview_w_music_send_or_post',
        'snap_preview_w_music_send',
        'snap_preview_w_music_post',
        'snap_send_w_music',
        'snap_save_w_music',
        'direct_snap_view_w_music',
        'snap_w_music_card_view',
        'story_post_w_music',
        'spotlight_story_post_w_music',
        'story_view_w_music',
        'spotlight_story_view_w_music',
        'story_w_music_card_view',
        'context_music_unlock',
        'context_music_play',
        'music_topic_page_open',
        'avg_num_snaps_per_music_topic_page_view',
        'music_topic_page_view_w_0_snap',
        'music_topic_page_view_w_1_snap',
        'music_topic_page_view_w_2plus_snap',
        'music_topic_snap_action',
        'music_favorite_from_topic',
        'music_favorite_from_picker',
        'spotlight_music_context_unlock',
        'direct_snap_music_context_unlock',
        'friend_story_music_context_unlock'
    ]
    bin_metric_list = ['{}_uu'.format(m) for m in cont_metric_list]
    
    ratio_metric_list = [
        Metric(
            col='music_pick_conversion',
            numerator='music_pick',
            denominator='music_picker_open',
            dist='ratio',
        ),
        Metric(
            col='music_pick_conversion_in_camera',
            numerator='music_pick_from_camera',
            denominator='music_picker_open_camera',
            dist='ratio',
        ),
        Metric(
            col='music_pick_conversion_in_preview',
            numerator='music_pick_from_preview',
            denominator='music_picker_open_preview',
            dist='ratio',
        ),
    ]

    def sql_callable(start_date, end_date):
        sql = """
            SELECT
                timestamp(ds) AS ts,
                ghost_user_id,
                {cont_cols},

                {bin_cols},
            FROM `sc-analytics.report_growth.music_funnel_engagement_user_*`
            WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
            GROUP BY 1,2
        """.format(
            cont_cols=',\n                '.join(
                ['SUM({}) AS {}'.format(m, m)
                    for m in cont_metric_list]),
            bin_cols=',\n                '.join(
                ['IF(SUM({}) > 0, 1, 0) AS {}_uu'.format(m, m)
                    for m in cont_metric_list]),
            start=start_date,
            end=end_date
        )
        return sql

    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[Metric(col=m, dist='cont', cumulative=True, daily=True)
                        for m in cont_metric_list] +
            [r for r in ratio_metric_list] +
            [Metric(col=m, dist='bin', cumulative=True, daily=True)
                    for m in bin_metric_list],
        inner_join_with_mapping=False,
        name="music_overall",
        bq_dialect='standard',
    )
    return mt

def music_licensed(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    >>> music_licensed('20170501', '20170503')
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    
    cont_metric_list = [
        'licensed_music_pick',
        'licensed_music_pick_from_preview',
        'licensed_music_pick_from_camera',
        'snap_create_w_licensed_music',
        'snap_preview_w_licensed_music',
        'snap_action_w_licensed_music',
        'snap_discard_w_licensed_music',
        'snap_preview_w_licensed_music_send_or_post',
        'snap_preview_w_licensed_music_send',
        'snap_preview_w_licensed_music_post',
        'snap_send_w_licensed_music',
        'story_post_w_licensed_music',
        'spotlight_story_post_w_licensed_music',
        'snap_save_w_licensed_music',
        'direct_snap_view_w_licensed_music',
        'story_view_w_licensed_music',
        'spotlight_story_view_w_licensed_music',
    ]
    bin_metric_list = ['{}_uu'.format(m) for m in cont_metric_list]

    def sql_callable(start_date, end_date):
        sql = """
            SELECT
                timestamp(ds) AS ts,
                ghost_user_id,
                {cont_cols},

                {bin_cols},
            FROM `sc-analytics.report_growth.music_funnel_engagement_user_*`
            WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
            GROUP BY 1,2
        """.format(
            cont_cols=',\n                '.join(
                ['SUM({}) AS {}'.format(m, m)
                    for m in cont_metric_list]),
            bin_cols=',\n                '.join(
                ['IF(SUM({}) > 0, 1, 0) AS {}_uu'.format(m, m)
                    for m in cont_metric_list]),
            start=start_date,
            end=end_date
        )
        return sql

    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[Metric(col=m, dist='cont', cumulative=True, daily=True)
                        for m in cont_metric_list] +
            [Metric(col=m, dist='bin', cumulative=True, daily=True)
                    for m in bin_metric_list],
        inner_join_with_mapping=False,
        name="music_licensed",
        bq_dialect='standard',
    )
    return mt

def music_ugc(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    >>> music_ugc('20170501', '20170503')
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    
    cont_metric_list = [
        'music_ugc_record_start',
        'music_ugc_record_done',
        'music_ugc_save_attempt',
        'music_ugc_private_save_attempt',
        'music_ugc_public_save_attempt',
        'music_ugc_save_success',
        'music_ugc_save_fail',
        'music_ugc_save_fail_copyright',
        'music_ugc_save_fail_bad_title',
        'music_ugc_save_fail_unknown',
        'music_ugc_public_save_success',
        'music_ugc_private_save_success',
        'music_ugc_public_save_fail',
        'music_ugc_private_save_fail',
        'ugc_music_pick',
        'ugc_music_pick_from_preview',
        'ugc_music_pick_from_camera',
        'snap_create_w_ugc_music',
        'snap_preview_w_ugc_music',
        'snap_action_w_ugc_music',
        'snap_discard_w_ugc_music',
        'snap_preview_w_ugc_music_send_or_post',
        'snap_preview_w_ugc_music_send',
        'snap_preview_w_ugc_music_post',
        'snap_send_w_ugc_music',
        'story_post_w_ugc_music',
        'spotlight_story_post_w_ugc_music',
        'snap_save_w_ugc_music',
        'direct_snap_view_w_ugc_music',
        'story_view_w_ugc_music',
        'spotlight_story_view_w_ugc_music',
    ]
    bin_metric_list = ['{}_uu'.format(m) for m in cont_metric_list]

    ratio_metric_list = [
        Metric(
            col='pct_save_attempt',
            numerator='music_ugc_save_attempt',
            denominator='music_ugc_record_done',
            dist='ratio',
        ),
        Metric(
            col='pct_save_success',
            numerator='music_ugc_save_success',
            denominator='music_ugc_save_attempt',
            dist='ratio',
        ),
    ]

    def sql_callable(start_date, end_date):
        sql = """
            SELECT
                timestamp(ds) AS ts,
                ghost_user_id,
                {cont_cols},

                {bin_cols},
            FROM `sc-analytics.report_growth.music_funnel_engagement_user_*`
            WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
            GROUP BY 1,2
        """.format(
            cont_cols=',\n                '.join(
                ['SUM({}) AS {}'.format(m, m)
                    for m in cont_metric_list]),
            bin_cols=',\n                '.join(
                ['IF(SUM({}) > 0, 1, 0) AS {}_uu'.format(m, m)
                    for m in cont_metric_list]),
            start=start_date,
            end=end_date
        )
        return sql

    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[Metric(col=m, dist='cont', cumulative=True, daily=True)
                        for m in cont_metric_list] +
            [r for r in ratio_metric_list] +
            [Metric(col=m, dist='bin', cumulative=True, daily=True)
                    for m in bin_metric_list],
        inner_join_with_mapping=False,
        name="music_ugc",
        bq_dialect='standard',
    )
    return mt

def music_spotlight_sounds(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    >>> music_spotlight_sounds('20170501', '20170503')
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    
    cont_metric_list = [
        'spotlight_sound_pick',
        'spotlight_sound_pick_from_preview',
        'spotlight_sound_pick_from_camera',
        'snap_create_w_spotlight_sound',
        'snap_preview_w_spotlight_sound',
        'snap_action_w_spotlight_sound',
        'snap_discard_w_spotlight_sound',
        'snap_preview_w_spotlight_sound_send_or_post',
        'snap_preview_w_spotlight_sound_send',
        'snap_preview_w_spotlight_sound_post',
        'snap_send_w_spotlight_sound',
        'story_post_w_spotlight_sound',
        'spotlight_story_post_w_spotlight_sound',
        'snap_save_w_spotlight_sound',
        'direct_snap_view_w_spotlight_sound',
        'story_view_w_spotlight_sound',
        'spotlight_story_view_w_spotlight_sound',
    ]
    bin_metric_list = ['{}_uu'.format(m) for m in cont_metric_list]

    def sql_callable(start_date, end_date):
        sql = """
            SELECT
                timestamp(ds) AS ts,
                ghost_user_id,
                {cont_cols},

                {bin_cols},
            FROM `sc-analytics.report_growth.music_funnel_engagement_user_*`
            WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
            GROUP BY 1,2
        """.format(
            cont_cols=',\n                '.join(
                ['SUM({}) AS {}'.format(m, m)
                    for m in cont_metric_list]),
            bin_cols=',\n                '.join(
                ['IF(SUM({}) > 0, 1, 0) AS {}_uu'.format(m, m)
                    for m in cont_metric_list]),
            start=start_date,
            end=end_date
        )
        return sql

    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[Metric(col=m, dist='cont', cumulative=True, daily=True)
                        for m in cont_metric_list] +
            [Metric(col=m, dist='bin', cumulative=True, daily=True)
                    for m in bin_metric_list],
        inner_join_with_mapping=False,
        name="music_spotlight_sounds",
        bq_dialect='standard',
    )
    return mt


def music_pick_conversion_by_tabs(start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    cont_metric_list = ['feature_tab_open',
                        'playlist_tab_open',
                        'playlist_moods_featured_tab_open',                        
                        'playlist_moods_pretype_tab_open',                        
                        'playlist_grid_tab_open',                        
                        'search_tab_open',
                        'favorites_tab_open',
                        'recents_tab_open',
                        'artist_tab_open',

                        'feature_tab_picks',
                        'playlist_tab_picks',
                        'playlist_moods_featured_tab_picks',
                        'playlist_moods_pretype_tab_picks',
                        'playlist_grid_tab_picks',
                        'search_tab_picks',
                        'favorites_tab_picks',
                        'recents_tab_picks',
                        'artist_tab_picks'
                        ]

    ratio_metric_list = [
        Metric(
            col='music_pick_conversion_featured',
            numerator='feature_tab_picks',
            denominator='feature_tab_open',
            dist='ratio',
        ),
        Metric(
            col='music_pick_conversion_playlist',
            numerator='playlist_tab_picks',
            denominator='playlist_tab_open',
            dist='ratio',
        ),
        Metric(
            col='music_pick_conversion_playlist_moods_featured',
            numerator='playlist_moods_featured_tab_picks',
            denominator='playlist_moods_featured_tab_open',
            dist='ratio',
        ),
         Metric(
            col='music_pick_conversion_playlist_moods_pretype',
            numerator='playlist_moods_pretype_tab_picks',
            denominator='playlist_moods_pretype_tab_open',
            dist='ratio',
        ),
         Metric(
            col='music_pick_conversion_playlist_grid',
            numerator='playlist_grid_tab_picks',
            denominator='playlist_grid_tab_open',
            dist='ratio',
        ),
        Metric(
            col='music_pick_conversion_search',
            numerator='search_tab_picks',
            denominator='search_tab_open',
            dist='ratio',
        ),
        Metric(
            col='music_pick_conversion_favorite',
            numerator='favorites_tab_picks',
            denominator='favorites_tab_open',
            dist='ratio',
        ),
        Metric(
            col='music_pick_conversion_recents',
            numerator='recents_tab_picks',
            denominator='recents_tab_open',
            dist='ratio',
        ),
        Metric(
            col='music_pick_conversion_artist',
            numerator='artist_tab_picks',
            denominator='artist_tab_open',
            dist='ratio',
        ),
    ]

    def sql_callable(start_date, end_date):
        sql = """
                SELECT 
                    timestamp(ds) AS ts, 
                    ghost_user_id, 
                    sum(if(event_name = 'CREATIVE_TOOLS_PICKER_TAB_VIEW' and picker_tab = 'FeaturedHome:Default', 1, 0)) as feature_tab_open,
                    sum(if(event_name = 'CREATIVE_TOOLS_PICKER_TAB_VIEW' and picker_tab like 'PlaylistTracks%', 1, 0)) as playlist_tab_open,
                    sum(if(event_name = 'CREATIVE_TOOLS_PICKER_TAB_VIEW' and picker_tab like 'PlaylistTracks%' and picker_tab like '%:Moods_Featured:%', 1, 0)) as playlist_moods_featured_tab_open,
                    sum(if(event_name = 'CREATIVE_TOOLS_PICKER_TAB_VIEW' and picker_tab like 'PlaylistTracks%' and picker_tab like '%:Moods_Pretype:%', 1, 0)) as playlist_moods_pretype_tab_open,
                    sum(if(event_name = 'CREATIVE_TOOLS_PICKER_TAB_VIEW' and picker_tab like 'PlaylistTracks%' and picker_tab like '%:Grid:%', 1, 0)) as playlist_grid_tab_open,
                    sum(if(event_name = 'CREATIVE_TOOLS_PICKER_TAB_VIEW' and picker_tab in ('post_type_search', 'pre_type_search', 'search'), 1, 0)) as search_tab_open,
                    sum(if(event_name = 'CREATIVE_TOOLS_PICKER_TAB_VIEW' and picker_tab = 'favorites', 1, 0)) as favorites_tab_open,
                    sum(if(event_name = 'CREATIVE_TOOLS_PICKER_TAB_VIEW' and picker_tab = 'recents', 1, 0)) as recents_tab_open,
                    sum(if(event_name = 'CREATIVE_TOOLS_PICKER_TAB_VIEW' and lower(picker_tab) like 'artist%', 1, 0)) as artist_tab_open,
                    
                    sum(if(event_name = 'CREATIVE_TOOLS_PICKER_ITEM_PICK' and picker_tab = 'FeaturedHome:Default', 1, 0)) as feature_tab_picks,
                    sum(if(event_name = 'CREATIVE_TOOLS_PICKER_ITEM_PICK' and picker_tab like 'PlaylistTracks%', 1, 0)) as playlist_tab_picks,
                    sum(if(event_name = 'CREATIVE_TOOLS_PICKER_ITEM_PICK' and picker_tab like 'PlaylistTracks%' and picker_tab like '%:Moods_Featured:%', 1, 0)) as playlist_moods_featured_tab_picks,
                    sum(if(event_name = 'CREATIVE_TOOLS_PICKER_ITEM_PICK' and picker_tab like 'PlaylistTracks%' and picker_tab like '%:Moods_Pretype:%', 1, 0)) as playlist_moods_pretype_tab_picks,
                    sum(if(event_name = 'CREATIVE_TOOLS_PICKER_ITEM_PICK' and picker_tab like 'PlaylistTracks%' and picker_tab like '%:Grid:%', 1, 0)) as playlist_grid_tab_picks,
                    sum(if(event_name = 'CREATIVE_TOOLS_PICKER_ITEM_PICK' and picker_tab in ('post_type_search', 'pre_type_search', 'search'), 1, 0)) as search_tab_picks,
                    sum(if(event_name = 'CREATIVE_TOOLS_PICKER_ITEM_PICK' and picker_tab = 'favorites', 1, 0)) as favorites_tab_picks,
                    sum(if(event_name = 'CREATIVE_TOOLS_PICKER_ITEM_PICK' and picker_tab = 'recents', 1, 0)) as recents_tab_picks,
                    sum(if(event_name = 'CREATIVE_TOOLS_PICKER_ITEM_PICK' and lower(picker_tab) like 'artist%', 1, 0)) as artist_tab_picks,
                FROM `sc-analytics.report_growth.music_funnel_daily_events_*` 
                WHERE event_name in ('CREATIVE_TOOLS_PICKER_ITEM_PICK', 'CREATIVE_TOOLS_PICKER_TAB_VIEW')
                AND _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
                GROUP BY 1,2
            """.format(
            start=start_date,
            end=end_date
        )
        return sql

    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[Metric(m, m, dist='cont', cumulative=True, daily=True) for m in cont_metric_list] +
                [r for r in ratio_metric_list],
        inner_join_with_mapping=False,
        name="music_pick_conversion_by_tabs",
        bq_dialect='standard',
    )
    return mt


def music_picks_by_sections(start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    cont_metric_list = ['favorites_section_picks',
                        'recents_section_picks',
                        'my_sounds_section_picks',
                        ]

    def sql_callable(start_date, end_date):
        sql = """
                SELECT 
                    timestamp(ds) AS ts, 
                    ghost_user_id,
                    sum(if(event_name = 'CREATIVE_TOOLS_PICKER_ITEM_PICK' and section_id = 'favorites', 1, 0)) as favorites_section_picks,
                    sum(if(event_name = 'CREATIVE_TOOLS_PICKER_ITEM_PICK' and section_id = 'recents', 1, 0)) as recents_section_picks,
                    sum(if(event_name = 'CREATIVE_TOOLS_PICKER_ITEM_PICK' and section_id = 'my_sounds', 1, 0)) as my_sounds_section_picks,
                FROM `sc-analytics.report_growth.music_funnel_daily_events_*` 
                WHERE event_name in ('CREATIVE_TOOLS_PICKER_ITEM_PICK')
                AND _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
                GROUP BY 1,2
            """.format(
            start=start_date,
            end=end_date
        )
        return sql

    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[Metric(col=m, dist='cont', cumulative=True, daily=True)
                        for m in cont_metric_list],
        inner_join_with_mapping=False,
        name="music_picks_by_sections",
        bq_dialect='standard',
    )
    return mt

def music_picker_latency(start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    cont_metric_list = ['avg_picker_layout_load_latency',
                        'p99_picker_layout_load_latency',
                        'p90_picker_layout_load_latency',
                        'p50_picker_layout_load_latency', 
                        'avg_search_load_latency',
                        'p99_search_load_latency',
                        'p90_search_load_latency',
                        'p50_search_load_latency',
                        'avg_pretype_search_load_latency',
                        'p99_pretype_search_load_latency',
                        'p90_pretype_search_load_latency',
                        'p50_pretype_search_load_latency',
                        'avg_home_load_latency',
                        'p99_home_load_latency',
                        'p90_home_load_latency',
                        'p50_home_load_latency',
                        'avg_playlist_list_load_latency',
                        'p99_playlist_list_load_latency',
                        'p90_playlist_list_load_latency',
                        'p50_playlist_list_load_latency',
                        'avg_playlist_tracks_load_latency',
                        'p99_playlist_tracks_load_latency',
                        'p90_playlist_tracks_load_latency',
                        'p50_playlist_tracks_load_latency',
                        'avg_favorite_load_latency',
                        'p99_favorite_load_latency',
                        'p90_favorite_load_latency',
                        'p50_favorite_load_latency',
                        'avg_custom_sounds_load_latency',
                        'p99_custom_sounds_load_latency',
                        'p90_custom_sounds_load_latency',
                        'p50_custom_sounds_load_latency',
                        'avg_recents_load_latency',
                        'p99_recents_load_latency',
                        'p90_recents_load_latency',
                        'p50_recents_load_latency',
                        'avg_artist_load_latency',
                        'p99_artist_load_latency',
                        'p90_artist_load_latency',
                        'p50_artist_load_latency',
                        ]

    def sql_callable(start_date, end_date):
        sql = """
                SELECT 
                    timestamp(ds) AS ts, 
                    ghost_user_id,
                    
                    AVG(IF(music_page_type = 'PICKER_LAYOUT', page_load_latency_ms, NULL)) as avg_picker_layout_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'PICKER_LAYOUT', page_load_latency_ms, NULL), 100)[OFFSET(99)] AS p99_picker_layout_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'PICKER_LAYOUT', page_load_latency_ms, NULL), 100)[OFFSET(90)] AS p90_picker_layout_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'PICKER_LAYOUT', page_load_latency_ms, NULL), 100)[OFFSET(50)] AS p50_picker_layout_load_latency,
                    
                    AVG(IF(music_page_type = 'SEARCH', page_load_latency_ms, NULL)) as avg_search_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'SEARCH', page_load_latency_ms, NULL), 100)[OFFSET(99)] AS p99_search_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'SEARCH', page_load_latency_ms, NULL), 100)[OFFSET(90)] AS p90_search_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'SEARCH', page_load_latency_ms, NULL), 100)[OFFSET(50)] AS p50_search_load_latency,

                    AVG(IF(music_page_type = 'HOME', page_load_latency_ms, NULL)) as avg_home_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'HOME', page_load_latency_ms, NULL), 100)[OFFSET(99)] AS p99_home_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'HOME', page_load_latency_ms, NULL), 100)[OFFSET(90)] AS p90_home_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'HOME', page_load_latency_ms, NULL), 100)[OFFSET(50)] AS p50_home_load_latency,

                    AVG(IF(music_page_type = 'PLAYLIST_LIST', page_load_latency_ms, NULL)) as avg_playlist_list_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'PLAYLIST_LIST', page_load_latency_ms, NULL), 100)[OFFSET(99)] AS p99_playlist_list_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'PLAYLIST_LIST', page_load_latency_ms, NULL), 100)[OFFSET(90)] AS p90_playlist_list_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'PLAYLIST_LIST', page_load_latency_ms, NULL), 100)[OFFSET(50)] AS p50_playlist_list_load_latency,

                    AVG(IF(music_page_type = 'PLAYLIST_TRACKS', page_load_latency_ms, NULL)) as avg_playlist_tracks_load_latency,   
                    APPROX_QUANTILES(IF(music_page_type = 'PLAYLIST_TRACKS', page_load_latency_ms, NULL), 100)[OFFSET(99)] AS p99_playlist_tracks_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'PLAYLIST_TRACKS', page_load_latency_ms, NULL), 100)[OFFSET(90)] AS p90_playlist_tracks_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'PLAYLIST_TRACKS', page_load_latency_ms, NULL), 100)[OFFSET(50)] AS p50_playlist_tracks_load_latency,

                    AVG(IF(music_page_type = 'FAVORITE', page_load_latency_ms, NULL)) as avg_favorite_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'FAVORITE', page_load_latency_ms, NULL), 100)[OFFSET(99)] AS p99_favorite_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'FAVORITE', page_load_latency_ms, NULL), 100)[OFFSET(90)] AS p90_favorite_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'FAVORITE', page_load_latency_ms, NULL), 100)[OFFSET(50)] AS p50_favorite_load_latency,

                    AVG(IF(music_page_type = 'CUSTOM_SOUNDS', page_load_latency_ms, NULL)) as avg_custom_sounds_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'CUSTOM_SOUNDS', page_load_latency_ms, NULL), 100)[OFFSET(99)] AS p99_custom_sounds_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'CUSTOM_SOUNDS', page_load_latency_ms, NULL), 100)[OFFSET(90)] AS p90_custom_sounds_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'CUSTOM_SOUNDS', page_load_latency_ms, NULL), 100)[OFFSET(50)] AS p50_custom_sounds_load_latency,

                    AVG(IF(music_page_type = 'RECENTS', page_load_latency_ms, NULL)) as avg_recents_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'RECENTS', page_load_latency_ms, NULL), 100)[OFFSET(99)] AS p99_recents_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'RECENTS', page_load_latency_ms, NULL), 100)[OFFSET(90)] AS p90_recents_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'RECENTS', page_load_latency_ms, NULL), 100)[OFFSET(50)] AS p50_recents_load_latency,

                    AVG(IF(music_page_type = 'PRE_TYPE_SEARCH', page_load_latency_ms, NULL)) as avg_pretype_search_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'PRE_TYPE_SEARCH', page_load_latency_ms, NULL), 100)[OFFSET(99)] AS p99_pretype_search_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'PRE_TYPE_SEARCH', page_load_latency_ms, NULL), 100)[OFFSET(90)] AS p90_pretype_search_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'PRE_TYPE_SEARCH', page_load_latency_ms, NULL), 100)[OFFSET(50)] AS p50_pretype_search_load_latency,

                    AVG(IF(music_page_type = 'ARTIST', page_load_latency_ms, NULL)) as avg_artist_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'ARTIST', page_load_latency_ms, NULL), 100)[OFFSET(99)] AS p99_artist_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'ARTIST', page_load_latency_ms, NULL), 100)[OFFSET(90)] AS p90_artist_load_latency,
                    APPROX_QUANTILES(IF(music_page_type = 'ARTIST', page_load_latency_ms, NULL), 100)[OFFSET(50)] AS p50_artist_load_latency,
                
                FROM `sc-analytics.report_growth.music_funnel_daily_events_*` 
                WHERE event_name = 'MUSIC_PICKER_LOAD_LATENCY' 
                AND page_load_latency_ms > 0 
                # and is_cached = false
                AND _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
                GROUP BY 1,2
            """.format(
            start=start_date,
            end=end_date
        )
        return sql

    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[Metric(col=m, dist='cont', cumulative=True, daily=True, desired_direction=NEGATIVE)
                        for m in cont_metric_list],
        inner_join_with_mapping=False,
        name="music_picker_latency",
        bq_dialect='standard',
    )
    return mt

def music_recommendations(start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    cont_metric_list = ['recommendation_views',
                        'recommendation_picks',
                        'recommendation_removes',
                        'num_recommendations',
                        'num_recommendations_match',
                        'num_lenses',
                        ]
    
    ratio_metric_list = [
        Metric(
            col='music_pick_conversion_camera_recs',
            numerator='recommendation_picks',
            denominator='recommendation_views',
            dist='ratio',
        ),
         Metric(
            col='music_remove_conversion_camera_recs',
            numerator='recommendation_removes',
            denominator='recommendation_views',
            dist='ratio',
        ),
        Metric(
            col='music_recommendation_match_rate',
            numerator='num_recommendations_match',
            denominator='num_lenses',
            dist='ratio',
        ),
    ]

    def sql_callable(start_date, end_date):
        sql = """
                SELECT 
                    timestamp(ds) AS ts, 
                    ghost_user_id,
                    sum(safe_cast(if(event_name = 'MUSIC_RECOMMENDATION_VIEW', 1, 0) as int64)) as recommendation_views,
                    sum(safe_cast(if(event_name = 'MUSIC_RECOMMENDATION_PICK', 1, 0) as int64)) as recommendation_picks,
                    sum(safe_cast(if(event_name = 'MUSIC_RECOMMENDATION_REMOVE', 1, 0) as int64)) as recommendation_removes,
                    sum(safe_cast(if(num_recommendations<=100000,num_recommendations,null) as int64)) as num_recommendations, --exclude extreme values
                    sum(if(coalesce(num_lenses,0)>=0,num_lenses,0)) as num_lenses, --filter negative values
                    sum(num_recommendations_match) as num_recommendations_match,
                FROM `sc-analytics.report_growth.music_funnel_daily_events_*` 
                WHERE event_name IN (
                    'MUSIC_RECOMMENDATION_VIEW',
                    'MUSIC_RECOMMENDATION_PICK',
                    'MUSIC_RECOMMENDATION_REMOVE',
                    'MUSIC_RECOMMENDATION_RESPONSE'
                )
                AND _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
                GROUP BY 1,2
            """.format(
            start=start_date,
            end=end_date
        )
        return sql

    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[Metric(m, m, dist='cont', cumulative=True, daily=True) for m in cont_metric_list] +
                [r for r in ratio_metric_list],
        inner_join_with_mapping=False,
        name="music_recommendations",
        bq_dialect='standard',
    )
    return mt


def music_playlist(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    >>> music_playlist('20170501', '20170503')
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    cont_metric_list = [
        'snap_trending_pick_count',
        'snap_trending_snap_preview_count',
        'snap_trending_snap_send_count',
        'snap_trending_snap_post_count',
        'snap_trending_snap_save_count',
        'snap_trending_snap_preview_discard_count',
        'snap_trending_snap_preview_w_action_count',
        'snap_trending_spotlight_story_post_w_music',
        'love_pick_count',
        'love_snap_preview_count',
        'love_snap_send_count',
        'love_snap_post_count',
        'love_snap_save_count',
        'love_snap_preview_discard_count',
        'love_snap_preview_w_action_count',
        'love_spotlight_story_post_w_music',
        'viral_pick_count',
        'viral_snap_preview_count',
        'viral_snap_send_count',
        'viral_snap_post_count',
        'viral_snap_save_count',
        'viral_snap_preview_discard_count',
        'viral_snap_preview_w_action_count',
        'viral_spotlight_story_post_w_music',
        'hiphop_pick_count',
        'hiphop_snap_preview_count',
        'hiphop_snap_send_count',
        'hiphop_snap_post_count',
        'hiphop_snap_save_count',
        'hiphop_snap_preview_discard_count',
        'hiphop_snap_preview_w_action_count',
        'hiphop_spotlight_story_post_w_music',
        'latin_pick_count',
        'latin_snap_preview_count',
        'latin_snap_send_count',
        'latin_snap_post_count',
        'latin_snap_save_count',
        'latin_snap_preview_discard_count',
        'latin_snap_preview_w_action_count',
        'latin_spotlight_story_post_w_music',
        'mood_pick_count',
        'mood_snap_preview_count',
        'mood_snap_send_count',
        'mood_snap_post_count',
        'mood_snap_save_count',
        'mood_snap_preview_discard_count',
        'mood_snap_preview_w_action_count',
        'mood_spotlight_story_post_w_music',
        'sad_pick_count',
        'sad_snap_preview_count',
        'sad_snap_send_count',
        'sad_snap_post_count',
        'sad_snap_save_count',
        'sad_snap_preview_discard_count',
        'sad_snap_preview_w_action_count',
        'sad_spotlight_story_post_w_music',
        'happy_pick_count',
        'happy_snap_preview_count',
        'happy_snap_send_count',
        'happy_snap_post_count',
        'happy_snap_save_count',
        'happy_snap_preview_discard_count',
        'happy_snap_preview_w_action_count',
        'happy_spotlight_story_post_w_music',
        'funny_pick_count',
        'funny_snap_preview_count',
        'funny_snap_send_count',
        'funny_snap_post_count',
        'funny_snap_save_count',
        'funny_snap_preview_discard_count',
        'funny_snap_preview_w_action_count',
        'funny_spotlight_story_post_w_music',
        'yummy_pick_count',
        'yummy_snap_preview_count',
        'yummy_snap_send_count',
        'yummy_snap_post_count',
        'yummy_snap_save_count',
        'yummy_snap_preview_discard_count',
        'yummy_snap_preview_w_action_count',
        'yummy_spotlight_story_post_w_music',
        'featured_pick_count',
        'featured_snap_preview_count',
        'featured_snap_send_count',
        'featured_snap_post_count',
        'featured_snap_save_count',
        'featured_snap_preview_discard_count',
        'featured_snap_preview_w_action_count',
        'featured_spotlight_story_post_w_music',
        'search_pick_count',
        'search_snap_preview_count',
        'search_snap_send_count',
        'search_snap_post_count',
        'search_snap_save_count',
        'search_snap_preview_discard_count',
        'search_snap_preview_w_action_count',
        'search_spotlight_story_post_w_music',
        'favorites_pick_count',
        'favorites_snap_preview_count',
        'favorites_snap_send_count',
        'favorites_snap_post_count',
        'favorites_snap_save_count',
        'favorites_snap_preview_discard_count',
        'favorites_snap_preview_w_action_count',
        'favorites_spotlight_story_post_w_music',
        'bel_arabi_pick_count',
        'bel_arabi_snap_preview_count',
        'bel_arabi_snap_send_count',
        'bel_arabi_snap_post_count',
        'bel_arabi_snap_save_count',
        'bel_arabi_snap_preview_discard_count',
        'bel_arabi_snap_preview_w_action_count',
        'bel_arabi_spotlight_story_post_w_music',
        'recommended_pick_count',
        'recommended_snap_preview_count',
        'recommended_snap_send_count',
        'recommended_snap_post_count',
        'recommended_snap_save_count',
        'recommended_snap_preview_discard_count',
        'recommended_snap_preview_w_action_count',
        'recommended_spotlight_story_post_w_music',
        'new_releases_pick_count',
        'new_releases_snap_preview_count',
        'new_releases_snap_send_count',
        'new_releases_snap_post_count',
        'new_releases_snap_save_count',
        'new_releases_snap_preview_discard_count',
        'new_releases_snap_preview_w_action_count',
        'new_releases_spotlight_story_post_w_music',
        'grid_slot_zero_pick_count',
        'grid_slot_zero_snap_preview_count',
        'grid_slot_zero_snap_send_count',
        'grid_slot_zero_snap_post_count',
        'grid_slot_zero_snap_save_count',
        'grid_slot_zero_snap_preview_discard_count',
        'grid_slot_zero_snap_preview_w_action_count',
        'grid_slot_zero_spotlight_story_post_w_music',
        'grid_slot_one_pick_count',
        'grid_slot_one_snap_preview_count',
        'grid_slot_one_snap_send_count',
        'grid_slot_one_snap_post_count',
        'grid_slot_one_snap_save_count',
        'grid_slot_one_snap_preview_discard_count',
        'grid_slot_one_snap_preview_w_action_count',
        'grid_slot_one_spotlight_story_post_w_music',
        'grid_slot_two_pick_count',
        'grid_slot_two_snap_preview_count',
        'grid_slot_two_snap_send_count',
        'grid_slot_two_snap_post_count',
        'grid_slot_two_snap_save_count',
        'grid_slot_two_snap_preview_discard_count',
        'grid_slot_two_snap_preview_w_action_count',
        'grid_slot_two_spotlight_story_post_w_music',
        'grid_slot_three_pick_count',
        'grid_slot_three_snap_preview_count',
        'grid_slot_three_snap_send_count',
        'grid_slot_three_snap_post_count',
        'grid_slot_three_snap_save_count',
        'grid_slot_three_snap_preview_discard_count',
        'grid_slot_three_snap_preview_w_action_count',
        'grid_slot_three_spotlight_story_post_w_music',
        'grid_slot_four_pick_count',
        'grid_slot_four_snap_preview_count',
        'grid_slot_four_snap_send_count',
        'grid_slot_four_snap_post_count',
        'grid_slot_four_snap_save_count',
        'grid_slot_four_snap_preview_discard_count',
        'grid_slot_four_snap_preview_w_action_count',
        'grid_slot_four_spotlight_story_post_w_music',
        'grid_slot_five_pick_count',
        'grid_slot_five_snap_preview_count',
        'grid_slot_five_snap_send_count',
        'grid_slot_five_snap_post_count',
        'grid_slot_five_snap_save_count',
        'grid_slot_five_snap_preview_discard_count',
        'grid_slot_five_snap_preview_w_action_count',
        'grid_slot_five_spotlight_story_post_w_music',
        'popular_with_this_lens_pick_count',
        'popular_with_this_lens_snap_preview_count',
        'popular_with_this_lens_snap_send_count',
        'popular_with_this_lens_snap_post_count',
        'popular_with_this_lens_snap_save_count',
        'popular_with_this_lens_snap_preview_discard_count',
        'popular_with_this_lens_snap_preview_w_action_count',
        'popular_with_this_lens_spotlight_story_post_w_music',
    ]
    bin_metric_list = []

    ratio_metric_list = []

    def sql_callable(start_date, end_date):
        sql = """
        SELECT 
            TIMESTAMP(ds) AS ts,
            ghost_user_id,

            SUM(IF(ENDS_WITH(section, 'Grid:0'), pick_count, 0)) AS grid_slot_zero_pick_count,
            SUM(IF(ENDS_WITH(section, 'Grid:0'), snap_preview_count, 0)) AS grid_slot_zero_snap_preview_count,
            SUM(IF(ENDS_WITH(section, 'Grid:0'), snap_send_count, 0)) AS grid_slot_zero_snap_send_count,
            SUM(IF(ENDS_WITH(section, 'Grid:0'), snap_post_count, 0)) AS grid_slot_zero_snap_post_count,
            SUM(IF(ENDS_WITH(section, 'Grid:0'), snap_save_count, 0)) AS grid_slot_zero_snap_save_count,
            SUM(IF(ENDS_WITH(section, 'Grid:0'), snap_preview_discard_count, 0)) AS grid_slot_zero_snap_preview_discard_count,
            SUM(IF(ENDS_WITH(section, 'Grid:0'), snap_preview_w_action_count, 0)) AS grid_slot_zero_snap_preview_w_action_count,
            SUM(IF(ENDS_WITH(section, 'Grid:0'), spotlight_story_post_w_music, 0)) AS grid_slot_zero_spotlight_story_post_w_music,

            SUM(IF(ENDS_WITH(section, 'Grid:1'), pick_count, 0)) AS grid_slot_one_pick_count,
            SUM(IF(ENDS_WITH(section, 'Grid:1'), snap_preview_count, 0)) AS grid_slot_one_snap_preview_count,
            SUM(IF(ENDS_WITH(section, 'Grid:1'), snap_send_count, 0)) AS grid_slot_one_snap_send_count,
            SUM(IF(ENDS_WITH(section, 'Grid:1'), snap_post_count, 0)) AS grid_slot_one_snap_post_count,
            SUM(IF(ENDS_WITH(section, 'Grid:1'), snap_save_count, 0)) AS grid_slot_one_snap_save_count,
            SUM(IF(ENDS_WITH(section, 'Grid:1'), snap_preview_discard_count, 0)) AS grid_slot_one_snap_preview_discard_count,
            SUM(IF(ENDS_WITH(section, 'Grid:1'), snap_preview_w_action_count, 0)) AS grid_slot_one_snap_preview_w_action_count,
            SUM(IF(ENDS_WITH(section, 'Grid:1'), spotlight_story_post_w_music, 0)) AS grid_slot_one_spotlight_story_post_w_music,

            SUM(IF(ENDS_WITH(section, 'Grid:2'), pick_count, 0)) AS grid_slot_two_pick_count,
            SUM(IF(ENDS_WITH(section, 'Grid:2'), snap_preview_count, 0)) AS grid_slot_two_snap_preview_count,
            SUM(IF(ENDS_WITH(section, 'Grid:2'), snap_send_count, 0)) AS grid_slot_two_snap_send_count,
            SUM(IF(ENDS_WITH(section, 'Grid:2'), snap_post_count, 0)) AS grid_slot_two_snap_post_count,
            SUM(IF(ENDS_WITH(section, 'Grid:2'), snap_save_count, 0)) AS grid_slot_two_snap_save_count,
            SUM(IF(ENDS_WITH(section, 'Grid:2'), snap_preview_discard_count, 0)) AS grid_slot_two_snap_preview_discard_count,
            SUM(IF(ENDS_WITH(section, 'Grid:2'), snap_preview_w_action_count, 0)) AS grid_slot_two_snap_preview_w_action_count,
            SUM(IF(ENDS_WITH(section, 'Grid:2'), spotlight_story_post_w_music, 0)) AS grid_slot_two_spotlight_story_post_w_music,

            SUM(IF(ENDS_WITH(section, 'Grid:3'), pick_count, 0)) AS grid_slot_three_pick_count,
            SUM(IF(ENDS_WITH(section, 'Grid:3'), snap_preview_count, 0)) AS grid_slot_three_snap_preview_count,
            SUM(IF(ENDS_WITH(section, 'Grid:3'), snap_send_count, 0)) AS grid_slot_three_snap_send_count,
            SUM(IF(ENDS_WITH(section, 'Grid:3'), snap_post_count, 0)) AS grid_slot_three_snap_post_count,
            SUM(IF(ENDS_WITH(section, 'Grid:3'), snap_save_count, 0)) AS grid_slot_three_snap_save_count,
            SUM(IF(ENDS_WITH(section, 'Grid:3'), snap_preview_discard_count, 0)) AS grid_slot_three_snap_preview_discard_count,
            SUM(IF(ENDS_WITH(section, 'Grid:3'), snap_preview_w_action_count, 0)) AS grid_slot_three_snap_preview_w_action_count,
            SUM(IF(ENDS_WITH(section, 'Grid:3'), spotlight_story_post_w_music, 0)) AS grid_slot_three_spotlight_story_post_w_music,

            SUM(IF(ENDS_WITH(section, 'Grid:4'), pick_count, 0)) AS grid_slot_four_pick_count,
            SUM(IF(ENDS_WITH(section, 'Grid:4'), snap_preview_count, 0)) AS grid_slot_four_snap_preview_count,
            SUM(IF(ENDS_WITH(section, 'Grid:4'), snap_send_count, 0)) AS grid_slot_four_snap_send_count,
            SUM(IF(ENDS_WITH(section, 'Grid:4'), snap_post_count, 0)) AS grid_slot_four_snap_post_count,
            SUM(IF(ENDS_WITH(section, 'Grid:4'), snap_save_count, 0)) AS grid_slot_four_snap_save_count,
            SUM(IF(ENDS_WITH(section, 'Grid:4'), snap_preview_discard_count, 0)) AS grid_slot_four_snap_preview_discard_count,
            SUM(IF(ENDS_WITH(section, 'Grid:4'), snap_preview_w_action_count, 0)) AS grid_slot_four_snap_preview_w_action_count,
            SUM(IF(ENDS_WITH(section, 'Grid:4'), spotlight_story_post_w_music, 0)) AS grid_slot_four_spotlight_story_post_w_music,

            SUM(IF(ENDS_WITH(section, 'Grid:5'), pick_count, 0)) AS grid_slot_five_pick_count,
            SUM(IF(ENDS_WITH(section, 'Grid:5'), snap_preview_count, 0)) AS grid_slot_five_snap_preview_count,
            SUM(IF(ENDS_WITH(section, 'Grid:5'), snap_send_count, 0)) AS grid_slot_five_snap_send_count,
            SUM(IF(ENDS_WITH(section, 'Grid:5'), snap_post_count, 0)) AS grid_slot_five_snap_post_count,
            SUM(IF(ENDS_WITH(section, 'Grid:5'), snap_save_count, 0)) AS grid_slot_five_snap_save_count,
            SUM(IF(ENDS_WITH(section, 'Grid:5'), snap_preview_discard_count, 0)) AS grid_slot_five_snap_preview_discard_count,
            SUM(IF(ENDS_WITH(section, 'Grid:5'), snap_preview_w_action_count, 0)) AS grid_slot_five_snap_preview_w_action_count,
            SUM(IF(ENDS_WITH(section, 'Grid:5'), spotlight_story_post_w_music, 0)) AS grid_slot_five_spotlight_story_post_w_music,
            
            SUM(IF(STARTS_WITH(section, 'TrackList:Popular With This Lens'), pick_count, 0)) AS popular_with_this_lens_pick_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Popular With This Lens'), snap_preview_count, 0)) AS popular_with_this_lens_snap_preview_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Popular With This Lens'), snap_send_count, 0)) AS popular_with_this_lens_snap_send_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Popular With This Lens'), snap_post_count, 0)) AS popular_with_this_lens_snap_post_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Popular With This Lens'), snap_save_count, 0)) AS popular_with_this_lens_snap_save_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Popular With This Lens'), snap_preview_discard_count, 0)) AS popular_with_this_lens_snap_preview_discard_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Popular With This Lens'), snap_preview_w_action_count, 0)) AS popular_with_this_lens_snap_preview_w_action_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Popular With This Lens'), spotlight_story_post_w_music, 0)) AS popular_with_this_lens_spotlight_story_post_w_music,

            SUM(IF(STARTS_WITH(section, 'TrackList:Recommended'), pick_count, 0)) AS recommended_pick_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Recommended'), snap_preview_count, 0)) AS recommended_snap_preview_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Recommended'), snap_send_count, 0)) AS recommended_snap_send_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Recommended'), snap_post_count, 0)) AS recommended_snap_post_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Recommended'), snap_save_count, 0)) AS recommended_snap_save_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Recommended'), snap_preview_discard_count, 0)) AS recommended_snap_preview_discard_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Recommended'), snap_preview_w_action_count, 0)) AS recommended_snap_preview_w_action_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Recommended'), spotlight_story_post_w_music, 0)) AS recommended_spotlight_story_post_w_music,
            
            SUM(IF(STARTS_WITH(section, 'TrackList:Snap Trending'), pick_count, 0)) AS snap_trending_pick_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Snap Trending'), snap_preview_count, 0)) AS snap_trending_snap_preview_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Snap Trending'), snap_send_count, 0)) AS snap_trending_snap_send_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Snap Trending'), snap_post_count, 0)) AS snap_trending_snap_post_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Snap Trending'), snap_save_count, 0)) AS snap_trending_snap_save_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Snap Trending'), snap_preview_discard_count, 0)) AS snap_trending_snap_preview_discard_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Snap Trending'), snap_preview_w_action_count, 0)) AS snap_trending_snap_preview_w_action_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Snap Trending'), spotlight_story_post_w_music, 0)) AS snap_trending_spotlight_story_post_w_music,

            SUM(IF(STARTS_WITH(section, 'TrackList:Love'), pick_count, 0)) AS love_pick_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Love'), snap_preview_count, 0)) AS love_snap_preview_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Love'), snap_send_count, 0)) AS love_snap_send_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Love'), snap_post_count, 0)) AS love_snap_post_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Love'), snap_save_count, 0)) AS love_snap_save_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Love'), snap_preview_discard_count, 0)) AS love_snap_preview_discard_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Love'), snap_preview_w_action_count, 0)) AS love_snap_preview_w_action_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Love'), spotlight_story_post_w_music, 0)) AS love_spotlight_story_post_w_music,

            SUM(IF(STARTS_WITH(section, 'TrackList:Viral'), pick_count, 0)) AS viral_pick_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Viral'), snap_preview_count, 0)) AS viral_snap_preview_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Viral'), snap_send_count, 0)) AS viral_snap_send_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Viral'), snap_post_count, 0)) AS viral_snap_post_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Viral'), snap_save_count, 0)) AS viral_snap_save_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Viral'), snap_preview_discard_count, 0)) AS viral_snap_preview_discard_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Viral'), snap_preview_w_action_count, 0)) AS viral_snap_preview_w_action_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Viral'), spotlight_story_post_w_music, 0)) AS viral_spotlight_story_post_w_music,

            SUM(IF(STARTS_WITH(section, 'TrackList:Hip-Hop'), pick_count, 0)) AS hiphop_pick_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Hip-Hop'), snap_preview_count, 0)) AS hiphop_snap_preview_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Hip-Hop'), snap_send_count, 0)) AS hiphop_snap_send_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Hip-Hop'), snap_post_count, 0)) AS hiphop_snap_post_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Hip-Hop'), snap_save_count, 0)) AS hiphop_snap_save_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Hip-Hop'), snap_preview_discard_count, 0)) AS hiphop_snap_preview_discard_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Hip-Hop'), snap_preview_w_action_count, 0)) AS hiphop_snap_preview_w_action_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Hip-Hop'), spotlight_story_post_w_music, 0)) AS hiphop_spotlight_story_post_w_music,

            SUM(IF(STARTS_WITH(section, 'TrackList:Latin'), pick_count, 0)) AS latin_pick_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Latin'), snap_preview_count, 0)) AS latin_snap_preview_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Latin'), snap_send_count, 0)) AS latin_snap_send_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Latin'), snap_post_count, 0)) AS latin_snap_post_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Latin'), snap_save_count, 0)) AS latin_snap_save_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Latin'), snap_preview_discard_count, 0)) AS latin_snap_preview_discard_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Latin'), snap_preview_w_action_count, 0)) AS latin_snap_preview_w_action_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Latin'), spotlight_story_post_w_music, 0)) AS latin_spotlight_story_post_w_music,

            SUM(IF(STARTS_WITH(section, 'TrackList:Mood'), pick_count, 0)) AS mood_pick_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Mood'), snap_preview_count, 0)) AS mood_snap_preview_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Mood'), snap_send_count, 0)) AS mood_snap_send_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Mood'), snap_post_count, 0)) AS mood_snap_post_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Mood'), snap_save_count, 0)) AS mood_snap_save_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Mood'), snap_preview_discard_count, 0)) AS mood_snap_preview_discard_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Mood'), snap_preview_w_action_count, 0)) AS mood_snap_preview_w_action_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Mood'), spotlight_story_post_w_music, 0)) AS mood_spotlight_story_post_w_music,

            SUM(IF(STARTS_WITH(section, 'TrackList:Sad'), pick_count, 0)) AS sad_pick_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Sad'), snap_preview_count, 0)) AS sad_snap_preview_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Sad'), snap_send_count, 0)) AS sad_snap_send_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Sad'), snap_post_count, 0)) AS sad_snap_post_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Sad'), snap_save_count, 0)) AS sad_snap_save_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Sad'), snap_preview_discard_count, 0)) AS sad_snap_preview_discard_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Sad'), snap_preview_w_action_count, 0)) AS sad_snap_preview_w_action_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Sad'), spotlight_story_post_w_music, 0)) AS sad_spotlight_story_post_w_music,

            SUM(IF(STARTS_WITH(section, 'TrackList:Happy'), pick_count, 0)) AS happy_pick_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Happy'), snap_preview_count, 0)) AS happy_snap_preview_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Happy'), snap_send_count, 0)) AS happy_snap_send_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Happy'), snap_post_count, 0)) AS happy_snap_post_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Happy'), snap_save_count, 0)) AS happy_snap_save_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Happy'), snap_preview_discard_count, 0)) AS happy_snap_preview_discard_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Happy'), snap_preview_w_action_count, 0)) AS happy_snap_preview_w_action_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Happy'), spotlight_story_post_w_music, 0)) AS happy_spotlight_story_post_w_music,

            SUM(IF(STARTS_WITH(section, 'TrackList:Funny'), pick_count, 0)) AS funny_pick_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Funny'), snap_preview_count, 0)) AS funny_snap_preview_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Funny'), snap_send_count, 0)) AS funny_snap_send_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Funny'), snap_post_count, 0)) AS funny_snap_post_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Funny'), snap_save_count, 0)) AS funny_snap_save_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Funny'), snap_preview_discard_count, 0)) AS funny_snap_preview_discard_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Funny'), snap_preview_w_action_count, 0)) AS funny_snap_preview_w_action_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Funny'), spotlight_story_post_w_music, 0)) AS funny_spotlight_story_post_w_music,

            SUM(IF(STARTS_WITH(section, 'TrackList:Yummy'), pick_count, 0)) AS yummy_pick_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Yummy'), snap_preview_count, 0)) AS yummy_snap_preview_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Yummy'), snap_send_count, 0)) AS yummy_snap_send_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Yummy'), snap_post_count, 0)) AS yummy_snap_post_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Yummy'), snap_save_count, 0)) AS yummy_snap_save_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Yummy'), snap_preview_discard_count, 0)) AS yummy_snap_preview_discard_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Yummy'), snap_preview_w_action_count, 0)) AS yummy_snap_preview_w_action_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Yummy'), spotlight_story_post_w_music, 0)) AS yummy_spotlight_story_post_w_music,

            SUM(IF(picker_tab = 'featured', pick_count, 0)) AS featured_pick_count,
            SUM(IF(picker_tab = 'featured', snap_preview_count, 0)) AS featured_snap_preview_count,
            SUM(IF(picker_tab = 'featured', snap_send_count, 0)) AS featured_snap_send_count,
            SUM(IF(picker_tab = 'featured', snap_post_count, 0)) AS featured_snap_post_count,
            SUM(IF(picker_tab = 'featured', snap_save_count, 0)) AS featured_snap_save_count,
            SUM(IF(picker_tab = 'featured', snap_preview_discard_count, 0)) AS featured_snap_preview_discard_count,
            SUM(IF(picker_tab = 'featured', snap_preview_w_action_count, 0)) AS featured_snap_preview_w_action_count,
            SUM(IF(picker_tab = 'featured', spotlight_story_post_w_music, 0)) AS featured_spotlight_story_post_w_music,

            SUM(IF(picker_tab = 'search', pick_count, 0)) AS search_pick_count,
            SUM(IF(picker_tab = 'search', snap_preview_count, 0)) AS search_snap_preview_count,
            SUM(IF(picker_tab = 'search', snap_send_count, 0)) AS search_snap_send_count,
            SUM(IF(picker_tab = 'search', snap_post_count, 0)) AS search_snap_post_count,
            SUM(IF(picker_tab = 'search', snap_save_count, 0)) AS search_snap_save_count,
            SUM(IF(picker_tab = 'search', snap_preview_discard_count, 0)) AS search_snap_preview_discard_count,
            SUM(IF(picker_tab = 'search', snap_preview_w_action_count, 0)) AS search_snap_preview_w_action_count,
            SUM(IF(picker_tab = 'search', spotlight_story_post_w_music, 0)) AS search_spotlight_story_post_w_music,

            SUM(IF(picker_tab = 'favorites', pick_count, 0)) AS favorites_pick_count,
            SUM(IF(picker_tab = 'favorites', snap_preview_count, 0)) AS favorites_snap_preview_count,
            SUM(IF(picker_tab = 'favorites', snap_send_count, 0)) AS favorites_snap_send_count,
            SUM(IF(picker_tab = 'favorites', snap_post_count, 0)) AS favorites_snap_post_count,
            SUM(IF(picker_tab = 'favorites', snap_save_count, 0)) AS favorites_snap_save_count,
            SUM(IF(picker_tab = 'favorites', snap_preview_discard_count, 0)) AS favorites_snap_preview_discard_count,
            SUM(IF(picker_tab = 'favorites', snap_preview_w_action_count, 0)) AS favorites_snap_preview_w_action_count,
            SUM(IF(picker_tab = 'favorites', spotlight_story_post_w_music, 0)) AS favorites_spotlight_story_post_w_music,
            
            SUM(IF(STARTS_WITH(section, 'TrackList:Bel Arabi'), pick_count, 0)) AS bel_arabi_pick_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Bel Arabi'), snap_preview_count, 0)) AS bel_arabi_snap_preview_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Bel Arabi'), snap_send_count, 0)) AS bel_arabi_snap_send_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Bel Arabi'), snap_post_count, 0)) AS bel_arabi_snap_post_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Bel Arabi'), snap_save_count, 0)) AS bel_arabi_snap_save_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Bel Arabi'), snap_preview_discard_count, 0)) AS bel_arabi_snap_preview_discard_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Bel Arabi'), snap_preview_w_action_count, 0)) AS bel_arabi_snap_preview_w_action_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:Bel Arabi'), spotlight_story_post_w_music, 0)) AS bel_arabi_spotlight_story_post_w_music,
            
            SUM(IF(STARTS_WITH(section, 'TrackList:New Releases'), pick_count, 0)) AS new_releases_pick_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:New Releases'), snap_preview_count, 0)) AS new_releases_snap_preview_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:New Releases'), snap_send_count, 0)) AS new_releases_snap_send_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:New Releases'), snap_post_count, 0)) AS new_releases_snap_post_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:New Releases'), snap_save_count, 0)) AS new_releases_snap_save_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:New Releases'), snap_preview_discard_count, 0)) AS new_releases_snap_preview_discard_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:New Releases'), snap_preview_w_action_count, 0)) AS new_releases_snap_preview_w_action_count,
            SUM(IF(STARTS_WITH(section, 'TrackList:New Releases'), spotlight_story_post_w_music, 0)) AS new_releases_spotlight_story_post_w_music,
        FROM (
            SELECT
                ds,
                ghost_user_id,
                picker_session_id,
                music_track_id,
                source_page_type,
                ANY_VALUE(os_type) AS os_type,
                MAX(
                    CASE
                        WHEN lower(picker_tab) = 'search' THEN 'search'
                        WHEN lower(picker_tab) = 'favorites' THEN 'favorites'
                        WHEN SPLIT(picker_tab, ':')[SAFE_OFFSET(0)] IN ('1', 'FeaturedHome') THEN 'featured'
                        WHEN SPLIT(picker_tab, ':')[SAFE_OFFSET(0)] IN ('2', 'PlaylistTracks') THEN 'playlist'
                        WHEN SPLIT(picker_tab, ':')[SAFE_OFFSET(0)] IN ('3', 'MyCustomSounds') THEN 'custom_sound'
                        WHEN SPLIT(picker_tab, ':')[SAFE_OFFSET(0)] IS NULL THEN NULL
                        ELSE 'other'
                    END) AS picker_tab,
                MAX( 
                    CASE
                        WHEN SPLIT(picker_tab, ':')[SAFE_OFFSET(0)] IN ('2', 'PlaylistTracks') THEN IF(SAFE_CAST(section_id AS INT64) > 0, 'unknown', section_id)
                        WHEN SPLIT(picker_tab, ':')[SAFE_OFFSET(0)] IN ('3', 'MyCustomSounds') THEN 'custom_sound'
                        WHEN picker_tab IS NULL THEN NULL
                        ELSE IF(SAFE_CAST(section_id AS INT64) > 0, 'unknown', section_id)
                    END) AS section,

                COUNTIF(event_name = 'CREATIVE_TOOLS_PICKER_ITEM_PICK') AS pick_count,
                COUNTIF(event_name = 'DIRECT_SNAP_PREVIEW') AS snap_preview_count,

                COUNTIF(event_name = 'DIRECT_SNAP_SEND') AS snap_send_count,
                COUNTIF(event_name = 'STORY_SNAP_POST') AS snap_post_count,
                COUNTIF(event_name = 'DIRECT_SNAP_SAVE') AS snap_save_count,
                COUNTIF(
                    event_name = 'DIRECT_SNAP_PREVIEW'
                    AND NOT (
                        COALESCE(with_snap_send, false)
                        OR COALESCE(with_story_post, false)
                        OR COALESCE(with_gallery_save, false))) AS snap_preview_discard_count,
                COUNTIF(
                    event_name = 'DIRECT_SNAP_PREVIEW'
                    AND (
                        COALESCE(with_snap_send, false)
                        OR COALESCE(with_story_post, false)
                        OR COALESCE(with_gallery_save, false))) AS snap_preview_w_action_count,

                COUNTIF(event_name = 'STORY_SNAP_POST'
                        AND community_submission_variant IN ('BOTH', 'SPOTLIGHT')) AS spotlight_story_post_w_music,
            FROM `sc-analytics.report_growth.music_funnel_daily_events_*`
            WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
                AND event_name IN (
                'CREATIVE_TOOLS_PICKER_ITEM_PICK',
                'DIRECT_SNAP_PREVIEW',
                'DIRECT_SNAP_SEND',
                'STORY_SNAP_POST',
                'DIRECT_SNAP_SAVE'
            )
            GROUP BY 1,2,3,4,5
        )
        GROUP BY 1,2
        """.format(
            start=start_date,
            end=end_date,
        )

        return sql

    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[Metric(m, m, dist='cont', cumulative=True, daily=True) for m in cont_metric_list] +
                [Metric(m, m, dist='bin', cumulative=True, daily=True) for m in bin_metric_list] +
                [r for r in ratio_metric_list],
        inner_join_with_mapping=False,
        name="music_playlist",
        bq_dialect='standard',
    )
    return mt

def music_request(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    cont_metric_list = [
        'total_requests_with_exact_duplicate_tracks_top10',
        'unique_impression_ratio_top10'
    ]

    def sql_callable(start_date, end_date):
        sql = """
        WITH 
        -- Calculate unique impression ratio
        ImpressionRatio AS (
            SELECT
                ghost_user_id,
                COUNT(DISTINCT music_track_id) / COUNT(music_track_id) AS unique_impression_ratio
            FROM
                `creativetools.bento_training_music.music_picker_pick_action_view_logs_*` a
            JOIN `creativetools.bento_training_music.rfe_logs_*` b
                ON a.request_id = b.request_id AND a.music_track_id = b.item_id
            WHERE a._TABLE_SUFFIX BETWEEN '{start}' AND '{end}' AND b._TABLE_SUFFIX BETWEEN '{start}' AND '{end}' AND picker_tab = 'FeaturedHome:Default' AND b.item_position <= 10
            GROUP BY
                1
        ),
        -- Calculate exact duplicate track sets
        RequestTrackSets AS (
            SELECT
                ghost_user_id,
                a.request_id,
                ARRAY_TO_STRING(ARRAY_AGG(DISTINCT music_track_id ORDER BY music_track_id), '||') AS track_set_fingerprint
            FROM
                `creativetools.bento_training_music.music_picker_pick_action_view_logs_*` a
            JOIN `creativetools.bento_training_music.rfe_logs_*` b
                ON a.request_id = b.request_id AND a.music_track_id = b.item_id
            WHERE a._TABLE_SUFFIX BETWEEN '{start}' AND '{end}' AND b._TABLE_SUFFIX BETWEEN '{start}' AND '{end}' AND picker_tab = 'FeaturedHome:Default' AND b.item_position <= 10
            GROUP BY
                ghost_user_id,
                request_id
        ),
        DuplicateSetCounts AS (
            SELECT
                ghost_user_id,
                track_set_fingerprint,
                COUNT(request_id) AS num_requests_in_set
            FROM
                RequestTrackSets
            GROUP BY
                ghost_user_id,
                track_set_fingerprint
            HAVING
                COUNT(request_id) > 1
        ),
        ExactDuplicates AS (
            SELECT
                ghost_user_id,
                SUM(num_requests_in_set) AS total_requests_with_exact_duplicate_tracks
            FROM
                DuplicateSetCounts
            GROUP BY
                ghost_user_id
        ),
        -- Get all unique users from the date range
        AllUsers AS (
            SELECT 
                TIMESTAMP(DATE(client_ts)) AS ts,
                ghost_user_id
            FROM
                `creativetools.bento_training_music.music_picker_pick_action_view_logs_*`
            WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
            GROUP BY ALL
        )
        SELECT
            au.ts,
            au.ghost_user_id,
            COALESCE(AVG(ir.unique_impression_ratio), 0) AS unique_impression_ratio_top10,
            COALESCE(SUM(ed.total_requests_with_exact_duplicate_tracks), 0) AS total_requests_with_exact_duplicate_tracks_top10
        FROM
            AllUsers au
        LEFT JOIN
            ImpressionRatio ir ON au.ghost_user_id = ir.ghost_user_id
        LEFT JOIN
            ExactDuplicates ed ON au.ghost_user_id = ed.ghost_user_id
        GROUP BY
            1, 2
        """.format(
            start=start_date,
            end=end_date
        )
        return sql

    from banjo.abtest.metric import NEGATIVE
    
    # Define metrics with appropriate directions
    metrics = []
    negative_direction_metrics = ['total_requests_with_exact_duplicate_tracks_top10']

    for m in cont_metric_list:
        if m in negative_direction_metrics:
            metrics.append(Metric(col=m, dist='cont', cumulative=True, daily=True, desired_direction=NEGATIVE))
        else:
            metrics.append(Metric(col=m, dist='cont', cumulative=True, daily=True))
    
    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=metrics,
        inner_join_with_mapping=False,
        name="music_request",
        bq_dialect='standard',
    )
    return mt
