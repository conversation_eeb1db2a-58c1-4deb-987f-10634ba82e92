"""
Notification Center Metrics
"""

from __future__ import division, print_function
from banjo.abtest.metric import (
    Metric, MetricTable, abtest_metrics_sql_helper
)
from datetime import timedelta
import pandas as pd
from banjo.abtest.metric import POSITIVE, NEGATIVE

__all__ = [
    "notification_center_metrics", "public_profile_source_of_view_metrics"
]


def notification_center_metrics(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """

    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    all_tab_open_metrics = Metric(
        'all_tab_open_count'
        , 'all_tab_open_count'
        ,
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    achievement_tab_open_metrics = Metric(
        'achievements_tab_open_count'
        , 'achievements_tab_open_count'
        , 
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    comment_tab_open_metrics = Metric(
         'comments_tab_open_count'
        , 'comments_tab_open_count'
        , 
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    reply_tab_open_metrics = Metric(

        'replies_tab_open_count'
        , 'replies_tab_open_count'
        , 
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    professional_tab_open_metrics = Metric(
        'professional_tab_open_count'
        , 'professional_tab_open_count'
        , 
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    mention_tab_open_metrics = Metric(   
        'mentions_tab_open_count'
        , 'mentions_tab_open_count'
        , 
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    promotion_tab_open_metrics = Metric(
        'promotions_tab_open_count'
        , 'promotions_tab_open_count'
        ,
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    all_tab_open_uu_metrics = Metric(
        'all_tab_open_uu'
        , 'all_tab_open_uu'
        ,
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    achievement_tab_open_uu_metrics = Metric(
        'achievements_tab_open_uu'
        , 'achievements_tab_open_uu'
        , 
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    comment_tab_open_uu_metrics = Metric(
         'comments_tab_open_uu'
        , 'comments_tab_open_uu'
        , 
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    reply_tab_open_uu_metrics = Metric(

        'replies_tab_open_uu'
        , 'replies_tab_open_uu'
        , 
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    professional_tab_open_uu_metrics = Metric(
        'professional_tab_open_uu'
        , 'professional_tab_open_uu'
        , 
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    mention_tab_open_uu_metrics = Metric(   
        'mentions_tab_open_uu'
        , 'mentions_tab_open_uu'
        , 
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    promotion_tab_open_uu_metrics = Metric(
        'promotions_tab_open_uu'
        , 'promotions_tab_open_uu'
        ,
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    # voice_or_video_call_uu = Metric(
    #     'voice_or_video_call_uu',
    #     'Voice or Video Call Feedback Unique Users',
    #     dist='bin',
    #     daily=True,
    #     cumulative=True,
    #     desired_direction=NEGATIVE
    # )


    def sql_callable(start_date, end_date):
        sql = """
                        
            SELECT 
                TIMESTAMP(CONCAT(SUBSTR(_TABLE_SUFFIX, 1, 4), '-', SUBSTR(_TABLE_SUFFIX, 5, 2), '-', SUBSTR(_TABLE_SUFFIX, 7, 2))) AS ts,
                ghost_user_id,

                # tab open counts 
                COUNT(1) AS page_open_total,
                COUNTIF(page_type_specific="ACHIEVEMENTS") AS achievements_tab_open_count,
                COUNTIF(page_type_specific="COMMENTS") AS comments_tab_open_count,
                COUNTIF(page_type_specific="REPLIES") AS replies_tab_open_count,
                COUNTIF(page_type_specific="ALL_TAB") AS all_tab_open_count,
                COUNTIF(page_type_specific= "PROFESSIONAL") AS professional_tab_open_count,
                COUNTIF(page_type_specific= "MENTIONS") AS mentions_tab_open_count,
                COUNTIF(page_type_specific= "PROMOTIONS") AS promotions_tab_open_count,

                # tab open count UU 

                MAX(1) AS tab_open_uu, 
                MAX(CASE WHEN page_type_specific = "ACHIEVEMENTS" THEN 1 ELSE 0 END) AS achievements_tab_open_uu,
                MAX(CASE WHEN page_type_specific = "COMMENTS" THEN 1 ELSE 0 END) AS comments_tab_open_uu,
                MAX(CASE WHEN page_type_specific = "REPLIES" THEN 1 ELSE 0 END) AS replies_tab_open_uu,
                MAX(CASE WHEN page_type_specific = "ALL_TAB" THEN 1 ELSE 0 END) AS all_tab_open_uu,
                MAX(CASE WHEN page_type_specific = "PROFESSIONAL" THEN 1 ELSE 0 END) AS professional_tab_open_uu,
                MAX(CASE WHEN page_type_specific = "MENTIONS" THEN 1 ELSE 0 END) AS mentions_tab_open_uu,
                MAX(CASE WHEN page_type_specific = "PROMOTIONS" THEN 1 ELSE 0 END) AS promotions_tab_open_uu

            FROM `sc-analytics.prod_analytics_snappro.daily_events_*`
            WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
            AND event_name = "PUBLICPROFILE_MANAGE_PAGE_OPEN"
            GROUP BY 1,
                    2
        """.format(
            start=start_date,
            end=end_date
            )
        return sql

    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[
            all_tab_open_metrics,
            achievement_tab_open_metrics,
            comment_tab_open_metrics,
            reply_tab_open_metrics,
            professional_tab_open_metrics,
            mention_tab_open_metrics,
    
            all_tab_open_uu_metrics,
            achievement_tab_open_uu_metrics,
            comment_tab_open_uu_metrics,
            reply_tab_open_uu_metrics,
            professional_tab_open_uu_metrics,
            mention_tab_open_uu_metrics
        ],
        name="notification_center_metrics",
        #description="Metrics for Notification Center",
        bq_dialect='standard'
    )

    return mt


# -------
# add source of view metrics for public profile 
# -------

def public_profile_source_of_view_metrics(start_date, end_date):

    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    """

    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    public_profile_view = Metric(
        'public_profile_view',
        'public_profile_view',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    public_profile_view_from_add_friends = Metric(
        'public_profile_view_from_add_friends',
        'public_profile_view_from_add_friends',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    public_profile_view_from_profile = Metric(
        'public_profile_view_from_profile',
        'public_profile_view_from_profile',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    public_profile_view_from_context_menu = Metric(
        'public_profile_view_from_context_menu',
        'public_profile_view_from_context_menu',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    public_profile_view_from_spotlight_feed = Metric(
        'public_profile_view_from_spotlight_feed',
        'public_profile_view_from_spotlight_feed',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    public_profile_view_from_search = Metric(
        'public_profile_view_from_search',
        'public_profile_view_from_search',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    public_profile_view_from_unknown_source = Metric(
        'public_profile_view_from_unknown_source',
        'public_profile_view_from_unknown_source',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    public_profile_view_from_chat = Metric(
        'public_profile_view_from_chat',
        'public_profile_view_from_chat',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    public_profile_view_from_discover_feed = Metric(
        'public_profile_view_from_discover_feed',
        'public_profile_view_from_discover_feed',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    public_profile_view_from_public_profile_management = Metric(
        'public_profile_view_from_public_profile_management',
        'public_profile_view_from_public_profile_management',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    public_profile_view_from_external_source = Metric(
        'public_profile_view_from_external_source',
        'public_profile_view_from_external_source',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    public_profile_view_from_other_source = Metric(
        'public_profile_view_from_other_source',
        'public_profile_view_from_other_source',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    public_profile_view_uu = Metric(
        'public_profile_view_uu',
        'public_profile_view_uu',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    public_profile_view_from_add_friends_uu = Metric(
        'public_profile_view_from_add_friends_uu',
        'public_profile_view_from_add_friends_uu',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    public_profile_view_from_profile_uu = Metric(
        'public_profile_view_from_profile_uu',
        'public_profile_view_from_profile_uu',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    public_profile_view_from_context_menu_uu = Metric(
        'public_profile_view_from_context_menu_uu',
        'public_profile_view_from_context_menu_uu',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    public_profile_view_from_spotlight_feed_uu = Metric(
        'public_profile_view_from_spotlight_feed_uu',
        'public_profile_view_from_spotlight_feed_uu',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    public_profile_view_from_search_uu = Metric(
        'public_profile_view_from_search_uu',
        'public_profile_view_from_search_uu',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    public_profile_view_from_unknown_source_uu = Metric(
        'public_profile_view_from_unknown_source_uu',
        'public_profile_view_from_unknown_source_uu',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    public_profile_view_from_chat_uu = Metric(
        'public_profile_view_from_chat_uu',
        'public_profile_view_from_chat_uu',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    public_profile_view_from_discover_feed_uu = Metric(
        'public_profile_view_from_discover_feed_uu',
        'public_profile_view_from_discover_feed_uu',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    public_profile_view_from_public_profile_management_uu = Metric(
        'public_profile_view_from_public_profile_management_uu',
        'public_profile_view_from_public_profile_management_uu',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    public_profile_view_from_external_source_uu = Metric(
        'public_profile_view_from_external_source_uu',
        'public_profile_view_from_external_source_uu',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    public_profile_view_from_other_source_uu = Metric(
        'public_profile_view_from_other_source_uu',
        'public_profile_view_from_other_source_uu',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )



    def sql_callable(start_date, end_date):
        sql = """
                        
            SELECT 
                TIMESTAMP(CONCAT(SUBSTR(_TABLE_SUFFIX, 1, 4), '-', SUBSTR(_TABLE_SUFFIX, 5, 2), '-', SUBSTR(_TABLE_SUFFIX, 7, 2))) AS ts,
                ghost_user_id,

                    # pp open counts 
                    COUNT(1) AS public_profile_view,
                    COUNTIF(source_page = "ADD_FRIENDS") AS public_profile_view_from_add_friends,
                    COUNTIF(source_page = "PROFILE") AS public_profile_view_from_profile,
                    COUNTIF(source_page = "CONTEXT_MENU") AS public_profile_view_from_context_menu,
                    COUNTIF(source_page = "SPOTLIGHT_FEED") AS public_profile_view_from_spotlight_feed,
                    COUNTIF(source_page = "SEARCH") AS public_profile_view_from_search,
                    COUNTIF(source_page IS NULL) AS public_profile_view_from_unknown_source,
                    COUNTIF(source_page = "CHAT") AS public_profile_view_from_chat,
                    COUNTIF(source_page = "DISCOVER_FEED") AS public_profile_view_from_discover_feed,
                    COUNTIF(source_page = "PUBLIC_PROFILE_MANAGEMENT") AS public_profile_view_from_public_profile_management,
                    COUNTIF(source_page = "EXTERNAL") AS public_profile_view_from_external_source,
                    # count if source_page is not equal to any of the above 
                    COUNTIF(source_page NOT IN ("ADD_FRIENDS", "PROFILE", "CONTEXT_MENU", "SPOTLIGHT_FEED", "SEARCH", "CHAT", "DISCOVER_FEED", "PUBLIC_PROFILE_MANAGEMENT", "EXTERNAL") AND source_page IS NOT NULL) AS public_profile_view_from_other_source,

                    # pp open count UU
                    MAX(1) AS public_profile_view_uu,
                    MAX(CASE WHEN source_page = "ADD_FRIENDS" THEN 1 ELSE 0 END) AS public_profile_view_from_add_friends_uu,
                    MAX(CASE WHEN source_page = "PROFILE" THEN 1 ELSE 0 END) AS public_profile_view_from_profile_uu,
                    MAX(CASE WHEN source_page = "CONTEXT_MENU" THEN 1 ELSE 0 END) AS public_profile_view_from_context_menu_uu,
                    MAX(CASE WHEN source_page = "SPOTLIGHT_FEED" THEN 1 ELSE 0 END) AS public_profile_view_from_spotlight_feed_uu,
                    MAX(CASE WHEN source_page = "SEARCH" THEN 1 ELSE 0 END) AS public_profile_view_from_search_uu,
                    MAX(CASE WHEN source_page IS NULL THEN 1 ELSE 0 END) AS public_profile_view_from_unknown_source_uu,
                    MAX(CASE WHEN source_page = "CHAT" THEN 1 ELSE 0 END) AS public_profile_view_from_chat_uu,
                    MAX(CASE WHEN source_page = "DISCOVER_FEED" THEN 1 ELSE 0 END) AS public_profile_view_from_discover_feed_uu,
                    MAX(CASE WHEN source_page = "PUBLIC_PROFILE_MANAGEMENT" THEN 1 ELSE 0 END) AS public_profile_view_from_public_profile_management_uu,
                    MAX(CASE WHEN source_page = "EXTERNAL" THEN 1 ELSE 0 END) AS public_profile_view_from_external_source_uu,
                    # count if source_page is not equal to any of the above 
                    MAX(CASE WHEN source_page NOT IN ("ADD_FRIENDS", "PROFILE", "CONTEXT_MENU", "SPOTLIGHT_FEED", "SEARCH", "CHAT", "DISCOVER_FEED", "PUBLIC_PROFILE_MANAGEMENT", "EXTERNAL") AND source_page IS NOT NULL THEN 1 ELSE 0 END) AS public_profile_view_from_other_source_uu
                FROM `sc-analytics.prod_analytics_snappro.daily_events_*`
                WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'
                AND event_name = 'PUBLICPROFILE_PAGE_OPEN'
                GROUP BY 1,
                        2
            """.format(
                start=start_date,
                end=end_date
            )
        return sql

    mt = MetricTable(
        sql=None,
        sql_callable=sql_callable,
        metrics=[
                public_profile_view,
                public_profile_view_from_add_friends,
                public_profile_view_from_profile,
                public_profile_view_from_context_menu,
                public_profile_view_from_spotlight_feed,
                public_profile_view_from_search,
                public_profile_view_from_unknown_source,
                public_profile_view_from_chat,
                public_profile_view_from_discover_feed,
                public_profile_view_from_public_profile_management,
                public_profile_view_from_external_source,
                public_profile_view_from_other_source,
                public_profile_view_uu,
                public_profile_view_from_add_friends_uu,
                public_profile_view_from_profile_uu,
                public_profile_view_from_context_menu_uu,
                public_profile_view_from_spotlight_feed_uu,
                public_profile_view_from_search_uu,
                public_profile_view_from_unknown_source_uu,
                public_profile_view_from_chat_uu,
                public_profile_view_from_discover_feed_uu,
                public_profile_view_from_public_profile_management_uu,
                public_profile_view_from_external_source_uu,
                public_profile_view_from_other_source_uu
            ],
        name="notification_center_metrics",
        #description="Metrics for Notification Center",
        bq_dialect='standard'
    )

    return mt