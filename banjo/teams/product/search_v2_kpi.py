""" Search metrics
Contact: hwoo@
"""

from __future__ import division, print_function
from banjo.abtest.metric import (
    Metric, MetricTable, abtest_metrics_sql_helper
)
from datetime import timedelta
import datetime
import pandas as pd
import logging
from banjo.abtest.metric import POSITIVE, NEGATIVE


logger = logging.getLogger(__name__)

logger.warning("Please check the metric file for definition and calculation of each metrics.")

def spotlight_metrics(start_date, end_date):
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]


    fo_spotlight_story_cnt = Metric(
        'fo_spotlight_story_cnt',
        '(1st order) Spotlight Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    fo_spotlight_story_uu = Metric(
        'fo_spotlight_story_uu',
        '(1st order) Spotlight Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    topic_page_cnt = Metric(
        'topic_page_cnt',
        'Topic Page Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    topic_page_uu = Metric(
        'topic_page_uu',
        'Topic Page Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    so_spotlight_story_cnt = Metric(
        'so_spotlight_story_cnt',
        '(2nd order) Spotlight Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    so_spotlight_story_uu = Metric(
        'so_spotlight_story_uu',
        '(2nd order) Spotlight Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    fo_so_spotlight_story_cnt = Metric(
        'fo_so_spotlight_story_cnt',
        '(1st + 2nd order) Spotlight Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    fo_so_spotlight_story_uu = Metric(
        'fo_so_spotlight_story_uu',
        '(1st + 2nd order) Spotlight Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    mt = MetricTable(
        sql="""
        SELECT 
            fo.ts,
            fo.ghost_user_id,
            sum(fo.fo_spotlight_story_cnt) as fo_spotlight_story_cnt,
            if(sum(fo.fo_spotlight_story_cnt)>0, 1, 0) as fo_spotlight_story_uu,
            sum(fo.topic_page_cnt) as topic_page_cnt,
            if(sum(fo.topic_page_cnt)>0, 1, 0) as topic_page_uu,
            sum(so.so_spotlight_story_cnt) as so_spotlight_story_cnt,
            if(sum(so.so_spotlight_story_cnt)>0, 1, 0) as so_spotlight_story_uu,
            sum(fo.fo_spotlight_story_cnt+ifnull(so.so_spotlight_story_cnt,0)) as fo_so_spotlight_story_cnt,
            if(sum(fo.fo_spotlight_story_cnt+ifnull(so.so_spotlight_story_cnt,0))>0, 1, 0) as fo_so_spotlight_story_uu,
        FROM (
            SELECT
                event_date AS ts,
                ghost_user_id AS ghost_user_id,
                countif(action in ('OPEN_SPOTLIGHT_STORY_FROM_SEARCH_RESULT', 'OPEN_SPOTLIGHT_STORY_FROM_SEARCH_RESULT_PLAYLIST')) as fo_spotlight_story_cnt,
                countif(action='OPEN_TOPIC_PAGE') as topic_page_cnt,
            FROM `sc-analytics.report_search_v2.search_action_20*`
            WHERE
                _TABLE_SUFFIX >= '{start_trunc}' AND
                _TABLE_SUFFIX <= '{end_trunc}'
            GROUP BY 1, 2
        ) fo 
        LEFT OUTER JOIN (
            SELECT
                event_date AS ts,
                ghost_user_id AS ghost_user_id,
                sum(story_story_view_count) as so_spotlight_story_cnt,
            FROM `sc-analytics.report_search_v2.search_to_topic_open_20*`
            WHERE
                _TABLE_SUFFIX >= '{start_trunc}' AND
                _TABLE_SUFFIX <= '{end_trunc}'
            GROUP BY 1, 2
        ) so
            ON fo.ghost_user_id = so.ghost_user_id
            AND fo.ts = so.ts
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            fo_spotlight_story_cnt,
            fo_spotlight_story_uu,
            topic_page_cnt,
            topic_page_uu,
            so_spotlight_story_cnt,
            so_spotlight_story_uu,
            fo_so_spotlight_story_cnt,
            fo_so_spotlight_story_uu,
        ],
        name="spotlight_metrics",
        bq_dialect='standard'

    )
    return mt
    
def d7_friending(start_date, end_date):
    """
    D7 friending metrics
    """

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]


    close_plus_relationship_d7_cnt = Metric(
        'close_plus_relationship_d7_cnt',
        'All Add Friends: Close+ Relationship on D7 Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    afp_close_plus_relationship_d7 = Metric(
        'afp_close_plus_relationship_d7',
        'AddFrPg Add Friends: Close+ Relationship on D7 Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )


    univsrch_close_plus_relationship_d7 = Metric(
        'univsrch_close_plus_relationship_d7',
        'UnivSrch Add Friends: Close+ Relationship on D7 Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    mt = MetricTable(
        sql="""
        SELECT
          event_date AS ts,
          ghost_user_id AS ghost_user_id,
          sum(has_close_relationship_d7) as close_plus_relationship_d7_cnt,
          sum(case when source='ADD_FRIENDS_PAGE' then has_close_relationship_d7 end) as afp_close_plus_relationship_d7,
          sum(case when source='SEARCH_UNSPECIFIED' then has_close_relationship_d7 end) as univsrch_close_plus_relationship_d7,
        FROM `sc-analytics.report_search_v2.search_friend_request_all_20*` a
        WHERE
            a._TABLE_SUFFIX >= '{start_trunc}' AND
            a._TABLE_SUFFIX <= '{end_trunc}'
            AND search_result_section='ADD_A_FRIEND'
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            close_plus_relationship_d7_cnt,
            afp_close_plus_relationship_d7,
            univsrch_close_plus_relationship_d7,
        ],
        name="d7_friending",
        bq_dialect='standard'

    )
    return mt

def all_posttype_friending_volumes(start_date, end_date):
    metric_prefix='all_posttype'
    display_prefix='All Add Friends'
    filter_sql="AND search_result_section=\'ADD_A_FRIEND\'"
    includeReasonBreakdown=True 

    return friending_volumes(start_date, end_date, metric_prefix, display_prefix, filter_sql, includeReasonBreakdown)

def universal_posttype_friending_volumes(start_date, end_date):
    metric_prefix='univ_posttype'
    display_prefix='UnivSrch Add Friends'
    filter_sql="AND source=\'SEARCH_UNSPECIFIED\' AND search_result_section=\'ADD_A_FRIEND\'"
    includeReasonBreakdown=True 

    return friending_volumes(start_date, end_date, metric_prefix, display_prefix, filter_sql, includeReasonBreakdown)


def universal_pretype_posttype_friending_volumes(start_date, end_date):
    metric_prefix='univ_pre_post'
    display_prefix='UnivSrch'
    filter_sql="AND source=\'SEARCH_UNSPECIFIED\'"
    includeReasonBreakdown=False

    return friending_volumes(start_date, end_date, metric_prefix, display_prefix, filter_sql, includeReasonBreakdown)

def quick_add_friending_volumes(start_date, end_date):
    metric_prefix='quick_add'
    display_prefix='Quick Add'
    filter_sql="AND source=\'SEARCH_UNSPECIFIED\' AND search_result_section=\'QUICK_ADD\'"
    includeReasonBreakdown=False

    return friending_volumes(start_date, end_date, metric_prefix, display_prefix, filter_sql, includeReasonBreakdown)


def add_friends_page_posttype_friending_volumes(start_date, end_date):
    metric_prefix='afp_posttype'
    display_prefix='AddFrPg Add Friends'
    filter_sql="AND source=\'ADD_FRIENDS_PAGE\' AND search_result_section=\'ADD_A_FRIEND\'"
    includeReasonBreakdown=True 

    return friending_volumes(start_date, end_date, metric_prefix, display_prefix, filter_sql, includeReasonBreakdown)


def friending_volumes(start_date, end_date, metric_prefix, display_prefix, filter_sql, includeReasonBreakdown):
    """
    friending volumes
    generates the follow metrics: 

        '*_outgoing_cnt',
        '*_recip_cnt',
        '*_bidi_eng_cnt',
        '*_bidi_eng_not_block_delete_cnt',
        '*_recip_rate', 
        '*_bidi_eng_rate',
        '*_block_delete_of_bidi_eng_rate',
        '*_ignored_cnt',
        '*_blocked_cnt', 
        '*_deleted_cnt', 
        '*_reported_cnt',
        '*_neg_cnt', 
        '*_friend_add_mrr',
        '*_friend_recip_mrr',

        '*_displayname_outgoing_cnt',
        '*_displayname_recip_cnt',
        '*_displayname_bidi_eng_cnt',
        '*_displayname_bidi_eng_not_block_delete_cnt',
        '*_displayname_recip_rate', 
        '*_displayname_bidi_eng_rate',
        '*_displayname_block_delete_of_bidi_eng_rate',
        '*_username_outgoing_cnt',
        '*_username_recip_cnt',
        '*_username_bidi_eng_cnt',
        '*_username_bidi_eng_not_block_delete_cnt',
        '*_username_recip_rate', 
        '*_username_bidi_eng_rate',
        '*_username_block_delete_of_bidi_eng_rate',
        '*_mutual_outgoing_cnt',
        '*_mutual_recip_cnt',
        '*_mutual_bidi_eng_cnt',
        '*_mutual_bidi_eng_not_block_delete_cnt',
        '*_mutual_recip_rate', 
        '*_mutual_bidi_eng_rate',
        '*_mutual_block_delete_of_bidi_eng_rate',
        '*_cb_outgoing_cnt',
        '*_cb_recip_cnt',
        '*_cb_bidi_eng_cnt',
        '*_cb_bidi_eng_not_block_delete_cnt',
        '*_cb_recip_rate', 
        '*_cb_bidi_eng_rate',
        '*_cb_block_delete_of_bidi_eng_rate',
        '*_grp_outgoing_cnt',
        '*_grp_recip_cnt',
        '*_grp_bidi_eng_cnt',
        '*_grp_bidi_eng_not_block_delete_cnt',
        '*_grp_recip_rate', 
        '*_grp_bidi_eng_rate',
        '*_grp_block_delete_of_bidi_eng_rate',

    """
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    metric_metadata = [
        # metric name, metric display name, good direction
        ('outgoing_cnt', 'Outgoing Friend Request Count', POSITIVE),
        ('recip_cnt', 'Reciprocated Friend Request Count', POSITIVE),
        ('bidi_eng_cnt', 'Bidi Eng Friend Request Count', POSITIVE),
        ('bidi_eng_not_block_delete_cnt', 'Bidi Eng (Not BlockedDeleted) Count', POSITIVE),
        ('ignored_cnt', 'Friend Request Ignored by Recipient Count', NEGATIVE),
        ('blocked_cnt', 'Blocked by Recipient Count', NEGATIVE),
        ('deleted_cnt', 'Deleted by Recipient Count', NEGATIVE),
        ('reported_cnt', 'Reported by Recipient Count', NEGATIVE),
        ('neg_cnt', 'Any Negative Action by Recipient Count', NEGATIVE),
        ('block_delete_cnt', 'BlockedDeleted Count', NEGATIVE),

        ('friend_add_rr_sum', 'MRR numerator (Friend Adds Only)', POSITIVE),
        ('reciprocated_rr_sum', 'MRR numerator (Reciprocated Friend Requests Only)', POSITIVE),
    ]

    ratio_metric_metadata = [
        # format: numerator, denominator, metric_display_name, desired direction
        ('recip_rate', 'Reciprocation Rate', POSITIVE,
            'recip_cnt', 'outgoing_cnt'),
        ('bidi_eng_rate', 'Bidi Eng Rate', POSITIVE,
            'bidi_eng_cnt', 'outgoing_cnt'),
        ('block_delete_of_bidi_eng_rate', 'BlockedDeleted of Bidi Eng Rate', NEGATIVE,
            'block_delete_cnt', 'bidi_eng_cnt'),
        ('friend_add_mrr', 'MRR - Friend Adds Only', POSITIVE,
            'friend_add_rr_sum', 'outgoing_cnt'),
        ('friend_recip_mrr', 'MRR - Friend Reciprocations Only', POSITIVE,
            'reciprocated_rr_sum', 'recip_cnt'),
    ]

    if includeReasonBreakdown:
        metric_metadata += [
            ('displayname_outgoing_cnt', 'Outgoing Friend Request Count [DisplayName]', POSITIVE),
            ('displayname_recip_cnt', 'Reciprocated Friend Request Count [DisplayName]', POSITIVE),
            ('displayname_bidi_eng_cnt', 'Bidi Eng Friend Request Count [DisplayName]', POSITIVE),
            ('displayname_bidi_eng_not_block_delete_cnt', 'Bidi Eng (Not BlockedDeleted) Count [DisplayName]', POSITIVE),
            ('displayname_block_delete_cnt', 'BlockedDeleted Count [DisplayName]', NEGATIVE),
            ('username_outgoing_cnt', 'Outgoing Friend Request Count [Username]', POSITIVE),
            ('username_recip_cnt', 'Reciprocated Friend Request Count [Username]', POSITIVE),
            ('username_bidi_eng_cnt', 'Bidi Eng Friend Request Count [Username]', POSITIVE),
            ('username_bidi_eng_not_block_delete_cnt', 'Bidi Eng (Not BlockedDeleted) Count [Username]', POSITIVE),
            ('username_block_delete_cnt', 'BlockedDeleted Count [Username]', NEGATIVE),
            ('mutual_outgoing_cnt', 'Outgoing Friend Request Count [Mutual, YMK]', POSITIVE),
            ('mutual_recip_cnt', 'Reciprocated Friend Request Count [Mutual, YMK]', POSITIVE),
            ('mutual_bidi_eng_cnt', 'Bidi Eng Friend Request Count [Mutual, YMK]', POSITIVE),
            ('mutual_bidi_eng_not_block_delete_cnt', 'Bidi Eng (Not BlockedDeleted) Count [Mutual, YMK]', POSITIVE),
            ('mutual_block_delete_cnt', 'BlockedDeleted Count [Mutual, YMK]', NEGATIVE),
            ('cb_outgoing_cnt', 'Outgoing Friend Request Count [Contacts]', POSITIVE),
            ('cb_recip_cnt', 'Reciprocated Friend Request Count [Contacts]', POSITIVE),
            ('cb_bidi_eng_cnt', 'Bidi Eng Friend Request Count [Contacts]', POSITIVE),
            ('cb_bidi_eng_not_block_delete_cnt', 'Bidi Eng (Not BlockedDeleted) Count [Contacts]', POSITIVE),
            ('cb_block_delete_cnt', 'BlockedDeleted Count [Contacts]', NEGATIVE),
            ('grp_outgoing_cnt', 'Outgoing Friend Request Count [Groups]', POSITIVE),
            ('grp_recip_cnt', 'Reciprocated Friend Request Count [Groups]', POSITIVE),
            ('grp_bidi_eng_cnt', 'Bidi Eng Friend Request Count [Groups]', POSITIVE),
            ('grp_bidi_eng_not_block_delete_cnt', 'Bidi Eng (Not BlockedDeleted) Count [Groups]', POSITIVE),
            ('grp_block_delete_cnt', 'BlockedDeleted Count [Groups]', NEGATIVE),

        ]

        ratio_metric_metadata += [
            ('displayname_recip_rate', 'Reciprocation Rate [DisplayName]', POSITIVE,
                'displayname_recip_cnt', 'displayname_outgoing_cnt'),
            ('displayname_bidi_eng_rate','Bidi Eng Rate [DisplayName]', POSITIVE,
                 'displayname_bidi_eng_cnt', 'displayname_outgoing_cnt'),
            ('displayname_block_delete_of_bidi_eng_rate', 'BlockedDeleted of Bidi Eng Rate [DisplayName]', NEGATIVE,
                'displayname_block_delete_cnt', 'displayname_bidi_eng_cnt'),
            ('username_recip_rate', 'Reciprocation Rate [Username]', POSITIVE,
                'username_recip_cnt', 'username_outgoing_cnt'),
            ('username_bidi_eng_rate', 'Bidi Eng Rate [Username]', POSITIVE,
                'username_bidi_eng_cnt', 'username_outgoing_cnt'),
            ('username_block_delete_of_bidi_eng_rate', 'BlockedDeleted of Bidi Eng Rate [Username]', NEGATIVE,
                'username_block_delete_cnt', 'username_bidi_eng_cnt'),
            ('mutual_recip_rate', 'Reciprocation Rate [Mutual, YMK]', POSITIVE,
                'mutual_recip_cnt', 'mutual_outgoing_cnt'),
            ('mutual_bidi_eng_rate', 'Bidi Eng Rate [Mutual, YMK]', POSITIVE,
                'mutual_bidi_eng_cnt', 'mutual_outgoing_cnt'),
            ('mutual_block_delete_of_bidi_eng_rate', 'BlockedDeleted of Bidi Eng Rate [Mutual, YMK]', NEGATIVE,
                'mutual_block_delete_cnt', 'mutual_bidi_eng_cnt'),
            ('cb_recip_rate', 'Reciprocation Rate [Contacts]', POSITIVE,
                'cb_recip_cnt', 'cb_outgoing_cnt'),
            ('cb_bidi_eng_rate', 'Bidi Eng Rate [Contacts]', POSITIVE,
                'cb_bidi_eng_cnt', 'cb_outgoing_cnt', ),
            ('cb_block_delete_of_bidi_eng_rate', 'BlockedDeleted of Bidi Eng Rate [Contacts]', NEGATIVE,
                'cb_block_delete_cnt', 'cb_bidi_eng_cnt'),
            ('grp_recip_rate', 'Reciprocation Rate [Groups]', POSITIVE,
                'grp_recip_cnt', 'grp_outgoing_cnt'),
            ('grp_bidi_eng_rate', 'Bidi Eng Rate [Groups]', POSITIVE,
                'grp_bidi_eng_cnt', 'grp_outgoing_cnt', ),
            ('grp_block_delete_of_bidi_eng_rate', 'BlockedDeleted of Bidi Eng Rate [Groups]', NEGATIVE,
                'grp_block_delete_cnt', 'grp_bidi_eng_cnt'),
        ]

    metrics_list = []

    for (metric_name, display_name, desired_direction) in metric_metadata:
        curr_metric = Metric (
            col="{metric_prefix}_{metric_name}".format(metric_prefix=metric_prefix, metric_name=metric_name),
            name="{display_prefix}: {display_name}".format(display_prefix=display_prefix, display_name=display_name),
            dist='cont',
            daily=True,
            cumulative=True,
            desired_direction=desired_direction
        )
        metrics_list.append(curr_metric)


    for (metric_name, display_name, desired_direction, numerator, denominator) in ratio_metric_metadata:
        curr_metric = Metric(
            col="{metric_prefix}_{metric_name}".format(metric_prefix=metric_prefix, metric_name=metric_name),
            name="{display_prefix}: {display_name}".format(display_prefix=display_prefix, display_name=display_name),
            numerator="{metric_prefix}_{numerator}".format(metric_prefix=metric_prefix, numerator=numerator),
            denominator="{metric_prefix}_{denominator}".format(metric_prefix=metric_prefix, denominator=denominator),
            dist='ratio',
            desired_direction=desired_direction
        )
        metrics_list.append(curr_metric)
    

    mt = MetricTable(
        sql="""
        SELECT
            event_date AS ts,
            ghost_user_id AS ghost_user_id,

            sum(1) as {metric_prefix}_outgoing_cnt,
            sum(is_manual_reciprocated) as {metric_prefix}_recip_cnt,           
            sum(case when is_manual_reciprocated=1 then has_bidirectional_engagement_v2 end) as {metric_prefix}_bidi_eng_cnt,

            sum(is_explicit_ignored) as {metric_prefix}_ignored_cnt,
            sum(is_blocked) as {metric_prefix}_blocked_cnt,
            sum(is_deleted) as {metric_prefix}_deleted_cnt,
            sum(is_reported) as {metric_prefix}_reported_cnt,
            sum(if(is_explicit_ignored+is_blocked+is_deleted+is_reported>=1, 1, 0)) as {metric_prefix}_neg_cnt,
            sum(if(is_manual_reciprocated=1 and has_bidirectional_engagement_v2=1 and is_blocked+is_deleted=0, 1, 0)) as {metric_prefix}_bidi_eng_not_block_delete_cnt,
            sum(if(is_manual_reciprocated=1 and has_bidirectional_engagement_v2=1 and is_blocked+is_deleted>=1, 1, 0)) as {metric_prefix}_block_delete_cnt,


            sum(reciprocal_rank) as {metric_prefix}_friend_add_rr_sum,
            sum(case when is_manual_reciprocated=1 then reciprocal_rank end) as {metric_prefix}_reciprocated_rr_sum,

            sum(case when search_result_reason='DISPLAY_NAME' then 1 end) as {metric_prefix}_displayname_outgoing_cnt,
            sum(case when search_result_reason='DISPLAY_NAME' then is_manual_reciprocated end) as {metric_prefix}_displayname_recip_cnt,           
            sum(case when search_result_reason='DISPLAY_NAME' and is_manual_reciprocated=1 then has_bidirectional_engagement_v2 end) as {metric_prefix}_displayname_bidi_eng_cnt,
            sum(if(search_result_reason='DISPLAY_NAME' and is_manual_reciprocated=1 and has_bidirectional_engagement_v2=1 and is_blocked+is_deleted=0, 1, 0)) as {metric_prefix}_displayname_bidi_eng_not_block_delete_cnt,
            sum(if(search_result_reason='DISPLAY_NAME' and is_manual_reciprocated=1 and has_bidirectional_engagement_v2=1 and is_blocked+is_deleted>=1, 1, 0)) as {metric_prefix}_displayname_block_delete_cnt,

            sum(case when search_result_reason='USERNAME' then 1 end) as {metric_prefix}_username_outgoing_cnt,
            sum(case when search_result_reason='USERNAME' then is_manual_reciprocated end) as {metric_prefix}_username_recip_cnt,           
            sum(case when search_result_reason='USERNAME' and is_manual_reciprocated=1 then has_bidirectional_engagement_v2 end) as {metric_prefix}_username_bidi_eng_cnt,
            sum(if(search_result_reason='USERNAME' and is_manual_reciprocated=1 and has_bidirectional_engagement_v2=1 and is_blocked+is_deleted=0, 1, 0)) as {metric_prefix}_username_bidi_eng_not_block_delete_cnt,
            sum(if(search_result_reason='USERNAME' and is_manual_reciprocated=1 and has_bidirectional_engagement_v2=1 and is_blocked+is_deleted>=1, 1, 0)) as {metric_prefix}_username_block_delete_cnt,

            sum(case when (search_result_reason like '%MUTUAL_FRIEND%' or search_result_reason='YOU_MAY_KNOW') then 1 end) as {metric_prefix}_mutual_outgoing_cnt,
            sum(case when (search_result_reason like '%MUTUAL_FRIEND%' or search_result_reason='YOU_MAY_KNOW') then is_manual_reciprocated end) as {metric_prefix}_mutual_recip_cnt,           
            sum(case when (search_result_reason like '%MUTUAL_FRIEND%' or search_result_reason='YOU_MAY_KNOW') and is_manual_reciprocated=1 then has_bidirectional_engagement_v2 end) as {metric_prefix}_mutual_bidi_eng_cnt,
            sum(if((search_result_reason like '%MUTUAL_FRIEND%' or search_result_reason='YOU_MAY_KNOW') and is_manual_reciprocated=1 and has_bidirectional_engagement_v2=1 and is_blocked+is_deleted=0, 1, 0)) as {metric_prefix}_mutual_bidi_eng_not_block_delete_cnt,
            sum(if((search_result_reason like '%MUTUAL_FRIEND%' or search_result_reason='YOU_MAY_KNOW') and is_manual_reciprocated=1 and has_bidirectional_engagement_v2=1 and is_blocked+is_deleted>=1, 1, 0)) as {metric_prefix}_mutual_block_delete_cnt,

            sum(case when search_result_reason='CONTACT_BOOK' then 1 end) as {metric_prefix}_cb_outgoing_cnt,
            sum(case when search_result_reason='CONTACT_BOOK' then is_manual_reciprocated end) as {metric_prefix}_cb_recip_cnt,           
            sum(case when search_result_reason='CONTACT_BOOK' and is_manual_reciprocated=1 then has_bidirectional_engagement_v2 end) as {metric_prefix}_cb_bidi_eng_cnt,
            sum(if(search_result_reason='CONTACT_BOOK' and is_manual_reciprocated=1 and has_bidirectional_engagement_v2=1 and is_blocked+is_deleted=0, 1, 0)) as {metric_prefix}_cb_bidi_eng_not_block_delete_cnt,
            sum(if(search_result_reason='CONTACT_BOOK' and is_manual_reciprocated=1 and has_bidirectional_engagement_v2=1 and is_blocked+is_deleted>=1, 1, 0)) as {metric_prefix}_cb_block_delete_cnt,

            sum(case when search_result_reason LIKE '%GROUPS' then 1 end) as {metric_prefix}_grp_outgoing_cnt,
            sum(case when search_result_reason LIKE '%GROUPS' then is_manual_reciprocated end) as {metric_prefix}_grp_recip_cnt,           
            sum(case when search_result_reason LIKE '%GROUPS' and is_manual_reciprocated=1 then has_bidirectional_engagement_v2 end) as {metric_prefix}_grp_bidi_eng_cnt,
            sum(if(search_result_reason LIKE '%GROUPS' and is_manual_reciprocated=1 and has_bidirectional_engagement_v2=1 and is_blocked+is_deleted=0, 1, 0)) as {metric_prefix}_grp_bidi_eng_not_block_delete_cnt,
            sum(if(search_result_reason LIKE '%GROUPS' and is_manual_reciprocated=1 and has_bidirectional_engagement_v2=1 and is_blocked+is_deleted>=1, 1, 0)) as {metric_prefix}_grp_block_delete_cnt,
        FROM `sc-analytics.report_search_v2.search_friend_request_all_20*`
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND
            _TABLE_SUFFIX <= '{end_trunc}'
            {filter_sql}
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc,
            metric_prefix=metric_prefix,
            filter_sql=filter_sql),
        metrics=metrics_list,
        name="friending_volumes",
        bq_dialect='standard'

    )

    return mt

def add_friends_page_posttype_friending_volumes(start_date, end_date):
    metric_prefix='afp_posttype'
    display_prefix='AddFrPg Add Friends'
    filter_sql="AND source=\'ADD_FRIENDS_PAGE\' AND search_result_section=\'ADD_A_FRIEND\'"
    includeReasonBreakdown=True 

    return friending_volumes(start_date, end_date, metric_prefix, display_prefix, filter_sql, includeReasonBreakdown)

def friending_volumes_recipient(start_date, end_date):
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    metric_metadata = [
        ('Friend Request Count [0-14]', 'recipient_acct_age_0_14_outgoing_cnt', POSITIVE),
        ('Reciprocated Friend Request Count [0-14]', 'recipient_acct_age_0_14_recip_cnt', POSITIVE),
        ('Bidi Eng Friend Request Count [0-14]', 'recipient_acct_age_0_14_bidi_eng_cnt', POSITIVE),
        ('Any Negative Action Count [0-14]', 'recipient_acct_age_0_14_neg_cnt', NEGATIVE),

        ('Friend Request Count [15-30]', 'recipient_acct_age_15_30_outgoing_cnt', POSITIVE),
        ('Reciprocated Friend Request Count [15-30]', 'recipient_acct_age_15_30_recip_cnt', POSITIVE),
        ('Bidi Eng Friend Request Count [15-30]', 'recipient_acct_age_15_30_bidi_eng_cnt', POSITIVE),
        ('Any Negative Action Count [15-30]', 'recipient_acct_age_15_30_neg_cnt', NEGATIVE),

        ('Friend Request Count [31+]', 'recipient_acct_age_31_plus_outgoing_cnt', POSITIVE),
        ('Reciprocated Friend Request Count [31+]', 'recipient_acct_age_31_plus_recip_cnt', POSITIVE),
        ('Bidi Eng Friend Request Count [31+]', 'recipient_acct_age_31_plus_bidi_eng_cnt', POSITIVE),
        ('Any Negative Action Count [31+]', 'recipient_acct_age_31_plus_neg_cnt', NEGATIVE),


        ('Friend Request Count [0-5]', 'recipient_friend_status_0_5_outgoing_cnt', POSITIVE),
        ('Reciprocated Friend Request Count [0-5]', 'recipient_friend_status_0_5_recip_cnt', POSITIVE),
        ('Bidi Eng Friend Request Count [0-5]', 'recipient_friend_status_0_5_bidi_eng_cnt', POSITIVE),
        ('Any Negative Action Count [0-5]', 'recipient_friend_status_0_5_neg_cnt', NEGATIVE),

        ('Friend Request Count [6-30]', 'recipient_friend_status_6_30_outgoing_cnt', POSITIVE),
        ('Reciprocated Friend Request Count [6-30]', 'recipient_friend_status_6_30_recip_cnt', POSITIVE),
        ('Bidi Eng Friend Request Count [6-30]', 'recipient_friend_status_6_30_bidi_eng_cnt', POSITIVE),
        ('Any Negative Action Count [6-30]', 'recipient_friend_status_6_30_neg_cnt', NEGATIVE),

        ('Friend Request Count [31-100]', 'recipient_friend_status_31_100_outgoing_cnt', POSITIVE),
        ('Reciprocated Friend Request Count [31-100]', 'recipient_friend_status_31_100_recip_cnt', POSITIVE),
        ('Bidi Eng Friend Request Count [31-100]', 'recipient_friend_status_31_100_bidi_eng_cnt', POSITIVE),
        ('Any Negative Action Count [31-100]', 'recipient_friend_status_31_100_neg_cnt', NEGATIVE),

        ('Friend Request Count [101-200]', 'recipient_friend_status_101_200_outgoing_cnt', POSITIVE),
        ('Reciprocated Friend Request Count [101-200]', 'recipient_friend_status_101_200_recip_cnt', POSITIVE),
        ('Bidi Eng Friend Request Count [101-200]', 'recipient_friend_status_101_200_bidi_eng_cnt', POSITIVE),
        ('Any Negative Action Count [101-200]', 'recipient_friend_status_101_200_neg_cnt', NEGATIVE),

        ('Friend Request Count [201+]', 'recipient_friend_status_201_plus_outgoing_cnt', POSITIVE),
        ('Reciprocated Friend Request Count [201+]', 'recipient_friend_status_201_plus_recip_cnt', POSITIVE),
        ('Bidi Eng Friend Request Count [201+]', 'recipient_friend_status_201_plus_bidi_eng_cnt', POSITIVE),
        ('Any Negative Action Count [201+]', 'recipient_friend_status_201_plus_neg_cnt', NEGATIVE),

    ]

    ratio_metric_metadata = [
        # format: metric name, metric_display_name, desired direction, 
        #   numerator, denominator
        ('Reciprocation Rate [0-14]', 'recipient_acct_age_0_14_recip_rate', POSITIVE,
            'recipient_acct_age_0_14_recip_cnt', 'recipient_acct_age_0_14_outgoing_cnt'),
        ('Bidi Eng Rate [0-14]', 'recipient_acct_age_0_14_bidi_eng_rate', POSITIVE,
            'recipient_acct_age_0_14_bidi_eng_cnt', 'recipient_acct_age_0_14_outgoing_cnt'),
        ('Any Negative Action Rate [0-14]', 'recipient_acct_age_0_14_neg_rate', NEGATIVE,
            'recipient_acct_age_0_14_neg_cnt', 'recipient_acct_age_0_14_outgoing_cnt'),

        ('Reciprocation Rate [15-30]', 'recipient_acct_age_15_30_recip_rate', POSITIVE,
            'recipient_acct_age_15_30_recip_cnt', 'recipient_acct_age_15_30_outgoing_cnt'),
        ('Bidi Eng Rate [15-30]', 'recipient_acct_age_15_30_bidi_eng_rate', POSITIVE,
            'recipient_acct_age_15_30_bidi_eng_cnt', 'recipient_acct_age_15_30_outgoing_cnt'),
        ('Any Negative Action Rate [15-30]', 'recipient_acct_age_15_30_neg_rate', NEGATIVE,
            'recipient_acct_age_15_30_neg_cnt', 'recipient_acct_age_15_30_outgoing_cnt'),

        ('Reciprocation Rate [31+]', 'recipient_acct_age_31_plus_recip_rate', POSITIVE,
            'recipient_acct_age_31_plus_recip_cnt', 'recipient_acct_age_31_plus_outgoing_cnt'),
        ('Bidi Eng Rate [31+]', 'recipient_acct_age_31_plus_bidi_eng_rate', POSITIVE,
            'recipient_acct_age_31_plus_bidi_eng_cnt', 'recipient_acct_age_31_plus_outgoing_cnt'),
        ('Any Negative Action Rate [31+]', 'recipient_acct_age_31_plus_neg_rate', NEGATIVE,
            'recipient_acct_age_31_plus_neg_cnt', 'recipient_acct_age_31_plus_outgoing_cnt'),

        ('Reciprocation Rate [0-5]', 'recipient_friend_status_0_5_recip_rate', POSITIVE,
            'recipient_friend_status_0_5_recip_cnt', 'recipient_friend_status_0_5_outgoing_cnt'),
        ('Bidi Eng Rate [0-5]', 'recipient_friend_status_0_5_bidi_eng_rate', POSITIVE,
            'recipient_friend_status_0_5_bidi_eng_cnt', 'recipient_friend_status_0_5_outgoing_cnt'),
        ('Any Negative Action Rate [0-5]', 'recipient_friend_status_0_5_neg_rate', NEGATIVE,
            'recipient_friend_status_0_5_neg_cnt', 'recipient_friend_status_0_5_outgoing_cnt'),

        ('Reciprocation Rate [6-30]', 'recipient_friend_status_6_30_recip_rate', POSITIVE,
            'recipient_friend_status_6_30_recip_cnt', 'recipient_friend_status_6_30_outgoing_cnt'),
        ('Bidi Eng Rate [6-30]', 'recipient_friend_status_6_30_bidi_eng_rate', POSITIVE,
            'recipient_friend_status_6_30_bidi_eng_cnt', 'recipient_friend_status_6_30_outgoing_cnt'),
        ('Any Negative Action Rate [6-30]', 'recipient_friend_status_6_30_neg_rate', NEGATIVE,
            'recipient_friend_status_6_30_neg_cnt', 'recipient_friend_status_6_30_outgoing_cnt'),


        ('Reciprocation Rate [31-100]', 'recipient_friend_status_31_100_recip_rate', POSITIVE,
            'recipient_friend_status_31_100_recip_cnt', 'recipient_friend_status_31_100_outgoing_cnt'),
        ('Bidi Eng Rate [31-100]', 'recipient_friend_status_31_100_bidi_eng_rate', POSITIVE,
            'recipient_friend_status_31_100_bidi_eng_cnt', 'recipient_friend_status_31_100_outgoing_cnt'),
        ('Any Negative Action Rate [31-100]', 'recipient_friend_status_31_100_neg_rate', NEGATIVE,
            'recipient_friend_status_31_100_neg_cnt', 'recipient_friend_status_31_100_outgoing_cnt'),


        ('Reciprocation Rate [101-200]', 'recipient_friend_status_101_200_recip_rate', POSITIVE,
            'recipient_friend_status_101_200_recip_cnt', 'recipient_friend_status_101_200_outgoing_cnt'),
        ('Bidi Eng Rate [101-200]', 'recipient_friend_status_101_200_bidi_eng_rate', POSITIVE,
            'recipient_friend_status_101_200_bidi_eng_cnt', 'recipient_friend_status_101_200_outgoing_cnt'),
        ('Any Negative Action Rate [101-200]', 'recipient_friend_status_101_200_neg_rate', NEGATIVE,
            'recipient_friend_status_101_200_neg_cnt', 'recipient_friend_status_101_200_outgoing_cnt'),


        ('Reciprocation Rate [201+]', 'recipient_friend_status_201_plus_recip_rate', POSITIVE,
            'recipient_friend_status_201_plus_recip_cnt', 'recipient_friend_status_201_plus_outgoing_cnt'),
        ('Bidi Eng Rate [201+]', 'recipient_friend_status_201_plus_bidi_eng_rate', POSITIVE,
            'recipient_friend_status_201_plus_bidi_eng_cnt', 'recipient_friend_status_201_plus_outgoing_cnt'),
        ('Any Negative Action Rate [201+]', 'recipient_friend_status_201_plus_neg_rate', NEGATIVE,
            'recipient_friend_status_201_plus_neg_cnt', 'recipient_friend_status_201_plus_outgoing_cnt'),

    ]

    metrics_list=[]

    for (display_name, metric_name, desired_direction) in metric_metadata:
        curr_metric = Metric (
            metric_name,
            display_name,
            dist='cont',
            daily=True,
            cumulative=True,
            desired_direction=desired_direction
        )
        metrics_list.append(curr_metric)

    for (display_name, metric_name, desired_direction, numerator, denominator) in ratio_metric_metadata:
        curr_metric = Metric(
            col=metric_name,
            name=display_name,
            numerator=numerator,
            denominator=denominator,
            dist='ratio',
            desired_direction=desired_direction
        )
        metrics_list.append(curr_metric)

    mt = MetricTable(
        sql="""
        SELECT
            event_date AS ts,
            ghost_user_id AS ghost_user_id,

            countif(recipient_days_since_creation in ('0', '1-14')) as recipient_acct_age_0_14_outgoing_cnt,
            countif(recipient_days_since_creation in ('0', '1-14') and is_manual_reciprocated=1) as recipient_acct_age_0_14_recip_cnt,           
            countif(recipient_days_since_creation in ('0', '1-14') and is_manual_reciprocated=1 and has_bidirectional_engagement_v2=1) as recipient_acct_age_0_14_bidi_eng_cnt,
            countif(recipient_days_since_creation in ('0', '1-14') and is_explicit_ignored+is_blocked+is_deleted+is_reported>=1) as recipient_acct_age_0_14_neg_cnt,

            countif(recipient_days_since_creation='15-30') as recipient_acct_age_15_30_outgoing_cnt,
            countif(recipient_days_since_creation='15-30' and is_manual_reciprocated=1) as recipient_acct_age_15_30_recip_cnt,           
            countif(recipient_days_since_creation='15-30' and is_manual_reciprocated=1 and has_bidirectional_engagement_v2=1) as recipient_acct_age_15_30_bidi_eng_cnt,
            countif(recipient_days_since_creation='15-30' and is_explicit_ignored+is_blocked+is_deleted+is_reported>=1) as recipient_acct_age_15_30_neg_cnt,

            countif(recipient_days_since_creation in ('31-90', '91-365', '365+')) as recipient_acct_age_31_plus_outgoing_cnt,
            countif(recipient_days_since_creation in ('31-90', '91-365', '365+') and is_manual_reciprocated=1) as recipient_acct_age_31_plus_recip_cnt,           
            countif(recipient_days_since_creation in ('31-90', '91-365', '365+') and is_manual_reciprocated=1 and has_bidirectional_engagement_v2=1) as recipient_acct_age_31_plus_bidi_eng_cnt,
            countif(recipient_days_since_creation in ('31-90', '91-365', '365+') and is_explicit_ignored+is_blocked+is_deleted+is_reported>=1) as recipient_acct_age_31_plus_neg_cnt,
                  

            countif(recipient_bidirectional_friend_status='0-5') as recipient_friend_status_0_5_outgoing_cnt,
            countif(recipient_bidirectional_friend_status='0-5' and is_manual_reciprocated=1) as recipient_friend_status_0_5_recip_cnt,           
            countif(recipient_bidirectional_friend_status='0-5' and is_manual_reciprocated=1 and has_bidirectional_engagement_v2=1) as recipient_friend_status_0_5_bidi_eng_cnt,
            countif(recipient_bidirectional_friend_status='0-5' and is_explicit_ignored+is_blocked+is_deleted+is_reported>=1) as recipient_friend_status_0_5_neg_cnt,

            countif(recipient_bidirectional_friend_status='6-30') as recipient_friend_status_6_30_outgoing_cnt,
            countif(recipient_bidirectional_friend_status='6-30' and is_manual_reciprocated=1) as recipient_friend_status_6_30_recip_cnt,           
            countif(recipient_bidirectional_friend_status='6-30' and is_manual_reciprocated=1 and has_bidirectional_engagement_v2=1) as recipient_friend_status_6_30_bidi_eng_cnt,
            countif(recipient_bidirectional_friend_status='6-30' and is_explicit_ignored+is_blocked+is_deleted+is_reported>=1) as recipient_friend_status_6_30_neg_cnt,

            countif(recipient_bidirectional_friend_status='31-100') as recipient_friend_status_31_100_outgoing_cnt,
            countif(recipient_bidirectional_friend_status='31-100' and is_manual_reciprocated=1) as recipient_friend_status_31_100_recip_cnt,           
            countif(recipient_bidirectional_friend_status='31-100' and is_manual_reciprocated=1 and has_bidirectional_engagement_v2=1) as recipient_friend_status_31_100_bidi_eng_cnt,
            countif(recipient_bidirectional_friend_status='31-100' and is_explicit_ignored+is_blocked+is_deleted+is_reported>=1) as recipient_friend_status_31_100_neg_cnt,

            countif(recipient_bidirectional_friend_status='101-200') as recipient_friend_status_101_200_outgoing_cnt,
            countif(recipient_bidirectional_friend_status='101-200' and is_manual_reciprocated=1) as recipient_friend_status_101_200_recip_cnt,           
            countif(recipient_bidirectional_friend_status='101-200' and is_manual_reciprocated=1 and has_bidirectional_engagement_v2=1) as recipient_friend_status_101_200_bidi_eng_cnt,
            countif(recipient_bidirectional_friend_status='101-200' and is_explicit_ignored+is_blocked+is_deleted+is_reported>=1) as recipient_friend_status_101_200_neg_cnt,

            countif(recipient_bidirectional_friend_status='201+') as recipient_friend_status_201_plus_outgoing_cnt,
            countif(recipient_bidirectional_friend_status='201+' and is_manual_reciprocated=1) as recipient_friend_status_201_plus_recip_cnt,           
            countif(recipient_bidirectional_friend_status='201+' and is_manual_reciprocated=1 and has_bidirectional_engagement_v2=1) as recipient_friend_status_201_plus_bidi_eng_cnt,
            countif(recipient_bidirectional_friend_status='201+' and is_explicit_ignored+is_blocked+is_deleted+is_reported>=1) as recipient_friend_status_201_plus_neg_cnt,

        FROM `sc-analytics.report_search_v2.search_friend_request_all_20*`
        WHERE
            _TABLE_SUFFIX BETWEEN '{start_trunc}' AND '{end_trunc}'
            AND search_result_section='ADD_A_FRIEND'
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc),
        metrics=metrics_list,
        name="friending_volumes_recipient",
        bq_dialect='standard'

    )

    return mt

def item_mrr(start_date, end_date):
    
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    add_a_friend_rr = Metric(
        'add_a_friend_rr',
        'Add Friend: Reciprocal Rank',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    add_a_friend_mrr_count = Metric(
        'add_a_friend_mrr_count',
        'Add Friend: MRR Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    add_a_friend_mrr = Metric(
        col='add_a_friend_mrr',
        name='UnivSrch Add Friends: MRR - Any Action',
        numerator='add_a_friend_rr',
        denominator='add_a_friend_mrr_count',
        dist='ratio',
    )

    frgr_rr = Metric(
        'frgr_rr',
        'Friends & Groups: Reciprocal Rank',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    frgr_mrr_count = Metric(
        'frgr_mrr_count',
        'Friends & Groups: MRR Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    frgr_mrr = Metric(
        col='frgr_mrr',
        name='Friends & Groups: MRR',
        numerator='frgr_rr',
        denominator='frgr_mrr_count',
        dist='ratio',
    )

    sp_rr = Metric(
        'sp_rr',
        'Show & Publisher: Reciprocal Rank',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    sp_mrr_count = Metric(
        'sp_mrr_count',
        'Show & Publisher: MRR Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    sp_mrr = Metric(
        col='sp_mrr',
        name='Show & Publisher: MRR',
        numerator='sp_rr',
        denominator='sp_mrr_count',
        dist='ratio',
    )

    lens_rr = Metric(
        'lens_rr',
        'Lenses: Reciprocal Rank',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    lens_mrr_count = Metric(
        'lens_mrr_count',
        'Lenses: MRR Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    lens_mrr = Metric(
        col='lens_mrr',
        name='Lenses: MRR',
        numerator='lens_rr',
        denominator='lens_mrr_count',
        dist='ratio',
    )

    top_rr = Metric(
        'top_rr',
        'Top Results: Reciprocal Rank',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    top_mrr_count = Metric(
        'top_mrr_count',
        'Top Results: MRR Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    top_mrr = Metric(
        col='top_mrr',
        name='Top Results: MRR',
        numerator='top_rr',
        denominator='top_mrr_count',
        dist='ratio',
    )

    place_rr = Metric(
        'place_rr',
        'Places: Reciprocal Rank',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    place_mrr_count = Metric(
        'place_mrr_count',
        'Places: MRR Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    place_mrr = Metric(
        col='place_mrr',
        name='Places: MRR',
        numerator='place_rr',
        denominator='place_mrr_count',
        dist='ratio',
    )

    ee_rr = Metric(
        'ee_rr',
        'Editions & Episodes: Reciprocal Rank',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    ee_mrr_count = Metric(
        'ee_mrr_count',
        'Editions & Episodes: MRR Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    ee_mrr = Metric(
        col='ee_mrr',
        name='Editions & Episodes: MRR',
        numerator='ee_rr',
        denominator='ee_mrr_count',
        dist='ratio',
    )

    os_rr = Metric(
        'os_rr',
        'Popular Accts: Reciprocal Rank',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    os_mrr_count = Metric(
        'os_mrr_count',
        'Popular Accts: MRR Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    os_mrr = Metric(
        col='os_mrr',
        name='Popular Accts: MRR',
        numerator='os_rr',
        denominator='os_mrr_count',
        dist='ratio',
    )

    topics_rr = Metric(
        'topics_rr',
        'Topics: Reciprocal Rank',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    topics_mrr_count = Metric(
        'topics_mrr_count',
        'Topics: MRR Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    topics_mrr = Metric(
        col='topics_mrr',
        name='Topics: MRR',
        numerator='topics_rr',
        denominator='topics_mrr_count',
        dist='ratio',
    )


    mt = MetricTable(
    sql = """
        SELECT 
            event_date as ts,
            ghost_user_id,
            sum(CASE WHEN search_result_section = 'ADD_A_FRIEND' THEN 1/(1+cast(search_result_ranking_id as integer)) END) as add_a_friend_rr,
            sum(CASE WHEN search_result_section = 'ADD_A_FRIEND' THEN 1 END) add_a_friend_mrr_count,
            sum(CASE WHEN search_result_section = 'FRIENDS_AND_GROUPS' THEN 1/(1+cast(search_result_ranking_id as integer)) END) as frgr_rr,
            sum(CASE WHEN search_result_section = 'FRIENDS_AND_GROUPS' THEN 1 END) frgr_mrr_count,
            sum(CASE WHEN search_result_section = 'SHOWS_AND_PUBLISHERS' THEN 1/(1+cast(search_result_ranking_id as integer)) END) as sp_rr,
            sum(CASE WHEN search_result_section = 'SHOWS_AND_PUBLISHERS' THEN 1 END) sp_mrr_count,
            sum(CASE WHEN search_result_section = 'COMMUNITY_LENSES' THEN 1/(1+cast(search_result_ranking_id as integer)) END) as lens_rr,
            sum(CASE WHEN search_result_section = 'COMMUNITY_LENSES' THEN 1 END) lens_mrr_count,
            sum(CASE WHEN search_result_section = 'TOP_RESULTS' THEN 1/(1+cast(search_result_ranking_id as integer)) END) as top_rr,
            sum(CASE WHEN search_result_section = 'TOP_RESULTS' THEN 1 END) top_mrr_count,
            sum(CASE WHEN search_result_section = 'PLACES' THEN 1/(1+cast(search_result_ranking_id as integer)) END) as place_rr,
            sum(CASE WHEN search_result_section = 'PLACES' THEN 1 END) place_mrr_count,
            sum(CASE WHEN search_result_section = 'EDITIONS_AND_EPISODES' THEN 1/(1+cast(search_result_ranking_id as integer)) END) as ee_rr,
            sum(CASE WHEN search_result_section = 'EDITIONS_AND_EPISODES' THEN 1 END) ee_mrr_count,
            sum(CASE WHEN search_result_section = 'OFFICIAL_STORIES' THEN 1/(1+cast(search_result_ranking_id as integer)) END) as os_rr,
            sum(CASE WHEN search_result_section = 'OFFICIAL_STORIES' THEN 1 END) os_mrr_count,
            sum(CASE WHEN search_result_section = 'TOPICS' THEN 1/(1+cast(search_result_ranking_id as integer)) END) as topics_rr,
            sum(CASE WHEN search_result_section = 'TOPICS' THEN 1 END) topics_mrr_count,
        FROM `sc-analytics.report_search_v2.search_action_20*`
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND
            _TABLE_SUFFIX <= '{end_trunc}'
            AND search_result_section_index is not null 
            AND search_result_ranking_id is not null
            AND search_result_ranking_id <>''
            AND cast(search_result_ranking_id as integer)>=0
            AND cast(search_result_ranking_id as integer)<=49
            AND is_pretype='PostType'
            AND source='SEARCH_UNSPECIFIED'
        GROUP BY 1,2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            add_a_friend_rr,
            add_a_friend_mrr_count,
            add_a_friend_mrr,
            frgr_rr,
            frgr_mrr_count,
            frgr_mrr,   
            sp_rr,
            sp_mrr_count,
            sp_mrr,    
            lens_rr,
            lens_mrr_count,
            lens_mrr,   
            top_rr,
            top_mrr_count,
            top_mrr,       
            place_rr,
            place_mrr_count,
            place_mrr,   
            ee_rr,
            ee_mrr_count,
            ee_mrr,   
            os_rr,
            os_mrr_count,
            os_mrr,   
            topics_rr,
            topics_mrr_count,
            topics_mrr,  
        ],
        name='item_mrr',
        bq_dialect='standard'
    )

    return mt

def section_keystroke_metrics(start_date, end_date):

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    posttype_keystroke_count = Metric(
        'posttype_keystroke_count',
        "Posttype Keystroke Count",
        dist='cont',
        daily=True,
        cumulative=True,
    )

    posttype_action_count = Metric(
        'posttype_action_count',
        'Posttype Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    posttype_keystrokes_to_action = Metric(
        col='posttype_keystrokes_to_action',
        name="Posttype Keystrokes to Action",
        numerator='posttype_keystroke_count',
        denominator='posttype_action_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    fr_and_gr_keystroke_count = Metric(
        'fr_and_gr_keystroke_count',
        "Friends & Groups: Keystroke Count",
        dist='cont',
        daily=True,
        cumulative=True,
    )

    fr_and_gr_action_count = Metric(
        'fr_and_gr_action_count',
        "Friends & Groups: Action Count",
        dist='cont',
        daily=True,
        cumulative=True,
    )

    fr_and_gr_keystrokes_to_action = Metric(
        col='fr_and_gr_keystrokes_to_action',
        name="Friends & Groups: Keystrokes to Action",
        numerator='fr_and_gr_keystroke_count',
        denominator='fr_and_gr_action_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    show_and_pub_keystroke_count = Metric(
        'show_and_pub_keystroke_count',
        "Show & Publisher: Keystroke Count",
        dist='cont',
        daily=True,
        cumulative=True,
    )

    show_and_pub_action_count = Metric(
        'show_and_pub_action_count',
        "Show & Publisher: Action Count",
        dist='cont',
        daily=True,
        cumulative=True,
    )

    show_and_pub_keystrokes_to_action = Metric(
        col='show_and_pub_keystrokes_to_action',
        name="Show & Publisher: Keystrokes to Action",
        numerator='show_and_pub_keystroke_count',
        denominator='show_and_pub_action_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    add_a_friend_keystroke_count = Metric(
        'add_a_friend_keystroke_count',
        "Add Friend: Keystroke Count",
        dist='cont',
        daily=True,
        cumulative=True,
    )

    add_a_friend_action_count = Metric(
        'add_a_friend_action_count',
        "Add Friend: Action Count",
        dist='cont',
        daily=True,
        cumulative=True,
    )

    add_a_friend_keystrokes_to_action = Metric(
        col='add_a_friend_keystrokes_to_action',
        name="UnivSrch Add Friends: Keystrokes to Action",
        numerator='add_a_friend_keystroke_count',
        denominator='add_a_friend_action_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    lenses_keystroke_count = Metric(
        'lenses_keystroke_count',
        "Lenses: Keystroke Count",
        dist='cont',
        daily=True,
        cumulative=True,
    )

    lenses_action_count = Metric(
        'lenses_action_count',
        "Lenses: Action Count",
        dist='cont',
        daily=True,
        cumulative=True,
    )

    lenses_keystrokes_to_action = Metric(
        col='lenses_keystrokes_to_action',
        name="Lenses: Keystrokes to Action",
        numerator='lenses_keystroke_count',
        denominator='lenses_action_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    top_results_keystroke_count = Metric(
        'top_results_keystroke_count',
        "Top Results: Keystroke Count",
        dist='cont',
        daily=True,
        cumulative=True,
    )

    top_results_action_count = Metric(
        'top_results_action_count',
        "Top Results: Action Count",
        dist='cont',
        daily=True,
        cumulative=True,
    )

    top_results_keystrokes_to_action = Metric(
        col='top_results_keystrokes_to_action',
        name="Top Results: Keystrokes to Action",
        numerator='top_results_keystroke_count',
        denominator='top_results_action_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    places_keystroke_count = Metric(
        'places_keystroke_count',
        "Places: Keystroke Count",
        dist='cont',
        daily=True,
        cumulative=True,
    )

    places_action_count = Metric(
        'places_action_count',
        "Places: Action Count",
        dist='cont',
        daily=True,
        cumulative=True,
    )

    places_keystrokes_to_action = Metric(
        col='places_keystrokes_to_action',
        name="Places: Keystrokes to Action",
        numerator='places_keystroke_count',
        denominator='places_action_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    ed_and_ep_keystroke_count = Metric(
        'ed_and_ep_keystroke_count',
        "Editions & Episodes: Keystroke Count",
        dist='cont',
        daily=True,
        cumulative=True,
    )

    ed_and_ep_action_count = Metric(
        'ed_and_ep_action_count',
        "Editions & Episodes: Action Count",
        dist='cont',
        daily=True,
        cumulative=True,
    )

    ed_and_ep_keystrokes_to_action = Metric(
        col='ed_and_ep_keystrokes_to_action',
        name="Editions & Episodes: Keystrokes to Action",
        numerator='ed_and_ep_keystroke_count',
        denominator='ed_and_ep_action_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    official_stories_keystroke_count = Metric(
        'official_stories_keystroke_count',
        "Popular Accts: Keystroke Count",
        dist='cont',
        daily=True,
        cumulative=True,
    )

    official_stories_action_count = Metric(
        'official_stories_action_count',
        "Popular Accts: Action Count",
        dist='cont',
        daily=True,
        cumulative=True,
    )

    official_stories_keystrokes_to_action = Metric(
        col='official_stories_keystrokes_to_action',
        name="Popular Accts: Keystrokes to Action",
        numerator='official_stories_keystroke_count',
        denominator='official_stories_action_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    topics_keystroke_count = Metric(
        'topics_keystroke_count',
        "Topics: Keystroke Count",
        dist='cont',
        daily=True,
        cumulative=True,
    )

    topics_action_count = Metric(
        'topics_action_count',
        "Topics: Action Count",
        dist='cont',
        daily=True,
        cumulative=True,
    )

    topics_keystrokes_to_action = Metric(
        col='topics_keystrokes_to_action',
        name="Topics: Keystrokes to Action",
        numerator='topics_keystroke_count',
        denominator='topics_action_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    mt = MetricTable(
        sql="""
        SELECT 
          a.event_date as ts,
          a.ghost_user_id,
          SUM(1) AS posttype_action_count,
          SUM(CHAR_LENGTH(search_query_text)) AS posttype_keystroke_count,
          SUM(CASE WHEN a.search_result_section = 'FRIENDS_AND_GROUPS' THEN 1 ELSE NULL END) AS fr_and_gr_action_count,
          SUM(CASE WHEN a.search_result_section = 'FRIENDS_AND_GROUPS' THEN CHAR_LENGTH(search_query_text) ELSE NULL END) AS fr_and_gr_keystroke_count,
          SUM(CASE WHEN a.search_result_section = 'SHOWS_AND_PUBLISHERS' THEN 1 ELSE NULL END) AS show_and_pub_action_count,
          SUM(CASE WHEN a.search_result_section = 'SHOWS_AND_PUBLISHERS' THEN CHAR_LENGTH(search_query_text) ELSE NULL END) AS show_and_pub_keystroke_count,
          SUM(CASE WHEN a.search_result_section = 'ADD_A_FRIEND' THEN 1 ELSE NULL END) AS add_a_friend_action_count,
          SUM(CASE WHEN a.search_result_section = 'ADD_A_FRIEND' THEN CHAR_LENGTH(search_query_text) ELSE NULL END) AS add_a_friend_keystroke_count,
          SUM(CASE WHEN a.search_result_section = 'COMMUNITY_LENSES' THEN 1 ELSE NULL END) AS lenses_action_count,
          SUM(CASE WHEN a.search_result_section = 'COMMUNITY_LENSES' THEN CHAR_LENGTH(search_query_text) ELSE NULL END) AS lenses_keystroke_count,
          SUM(CASE WHEN a.search_result_section = 'TOP_RESULTS' THEN 1 ELSE NULL END) AS top_results_action_count,
          SUM(CASE WHEN a.search_result_section = 'TOP_RESULTS' THEN CHAR_LENGTH(search_query_text) ELSE NULL END) AS top_results_keystroke_count,
          SUM(CASE WHEN a.search_result_section = 'PLACES' THEN 1 ELSE NULL END) AS places_action_count,
          SUM(CASE WHEN a.search_result_section = 'PLACES' THEN CHAR_LENGTH(search_query_text) ELSE NULL END) AS places_keystroke_count,
          SUM(CASE WHEN a.search_result_section = 'EDITIONS_AND_EPISODES' THEN 1 ELSE NULL END) AS ed_and_ep_action_count,
          SUM(CASE WHEN a.search_result_section = 'EDITIONS_AND_EPISODES' THEN CHAR_LENGTH(search_query_text) ELSE NULL END) AS ed_and_ep_keystroke_count,
          SUM(CASE WHEN a.search_result_section = 'OFFICIAL_STORIES' THEN 1 ELSE NULL END) AS official_stories_action_count,
          SUM(CASE WHEN a.search_result_section = 'OFFICIAL_STORIES' THEN CHAR_LENGTH(search_query_text) ELSE NULL END) AS official_stories_keystroke_count,
          SUM(CASE WHEN a.search_result_section = 'TOPICS' THEN 1 ELSE NULL END) AS topics_action_count,
          SUM(CASE WHEN a.search_result_section = 'TOPICS' THEN CHAR_LENGTH(search_query_text) ELSE NULL END) AS topics_keystroke_count,
        FROM `sc-analytics.report_search_v2.search_action_20*` a
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND _TABLE_SUFFIX <= '{end_trunc}'
            AND source = 'SEARCH_UNSPECIFIED'
            AND is_pretype = 'PostType'
            AND CHAR_LENGTH(search_query_text) BETWEEN 1 AND 100
            AND search_result_identifier IS NOT NULL
        GROUP BY 1,2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[            
            posttype_action_count,
            posttype_keystroke_count,
            posttype_keystrokes_to_action,
            fr_and_gr_action_count,
            fr_and_gr_keystroke_count,
            fr_and_gr_keystrokes_to_action,
            show_and_pub_action_count,
            show_and_pub_keystroke_count,
            show_and_pub_keystrokes_to_action,
            add_a_friend_action_count,
            add_a_friend_keystroke_count,
            add_a_friend_keystrokes_to_action,
            lenses_action_count,
            lenses_keystroke_count,
            lenses_keystrokes_to_action,
            top_results_action_count,
            top_results_keystroke_count,
            top_results_keystrokes_to_action,
            places_action_count,
            places_keystroke_count,
            places_keystrokes_to_action,
            ed_and_ep_action_count,
            ed_and_ep_keystroke_count,
            ed_and_ep_keystrokes_to_action,
            official_stories_action_count,
            official_stories_keystroke_count,
            official_stories_keystrokes_to_action,
            topics_action_count,
            topics_keystroke_count,
            topics_keystrokes_to_action
        ],
        name="section_keystroke_metrics",
        bq_dialect='standard'

    )

    return mt


def keystroke_to_first_action_metrics(start_date, end_date):

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    key_stroke_to_first_posttype_creator_action = Metric(
        'key_stroke_to_first_posttype_creator_action',
        "Key Stroke to First Posttype Creator Action",
        dist='cont',
        daily=True,
        cumulative=True,
    )

    sessions_w_posttype_creator_action_as_first_action = Metric(
        'sessions_w_posttype_creator_action_as_first_action',
        'Sessions w Posttype Creator Action as First Action',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    avg_key_stroke_to_first_posttype_creator_action = Metric(
        col='avg_key_stroke_to_first_posttype_creator_action',
        name="Avg Key Stroke to First Posttype Creator Action",
        numerator='key_stroke_to_first_posttype_creator_action',
        denominator='sessions_w_posttype_creator_action_as_first_action',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    mt = MetricTable(
        sql="""
        SELECT 
          PARSE_TIMESTAMP('%Y%m%d',CONCAT('20',_TABLE_SUFFIX)) AS ts,
          ghost_user_id,
          COUNTIF(SPLIT(first_posttype_action_item_identifier, ':')[SAFE_ORDINAL(1)] = '2') AS sessions_w_posttype_creator_action_as_first_action,
          SUM(IF(SPLIT(first_posttype_action_item_identifier, ':')[SAFE_ORDINAL(1)] = '2', key_stroke_to_first_posttype_action,0)) AS key_stroke_to_first_posttype_creator_action,
        FROM `sc-analytics.report_search_v2.search_session_level_20*` 
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND _TABLE_SUFFIX <= '{end_trunc}'
            AND source = 'SEARCH_UNSPECIFIED'
            AND first_posttype_action_item_identifier IS NOT NULL
        GROUP BY 1,2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[            
            sessions_w_posttype_creator_action_as_first_action,
            key_stroke_to_first_posttype_creator_action,
            avg_key_stroke_to_first_posttype_creator_action,

        ],
        name="keystroke_to_first_action_metrics",
        bq_dialect='standard'

    )

    return mt


def section_click_thru_rates(start_date,end_date):
    """
    Calculates the following metrics:
        *_impression_cnt
        *_meaningful_action_cnt
        *_ctr
        *_press_view_more_cnt
        *_press_view_more_uu
    """

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]


    metadata = [
        # format: (metric_prefix, search_result_section, display name prefix, has press view more)

        # pretype sections
        ('topic_pill', 'QUERY_SUGGESTIONS', 'Topic Pill', False),
        ('ac', 'ACTIVITY_CENTER_PRETYPE', 'Activity Center', True),
        ('bf', 'BEST_FRIENDS', 'Best Friends', True),
        ('rs', 'RECENT_SEARCHES', 'Recents', False),
        ('qa', 'QUICK_ADD', 'Quick Add', True),
        ('stars_pre', 'STARS_PRETYPE', 'Stars', False),
        ('lens_pre', 'LENS_PRETYPE', 'Lens Pretype', False),
        ('places_pre', 'PLACES_PRETYPE', 'Places Pretype', False),
        ('shows_pre', 'PUBLISHERS_PRETYPE', 'Shows Pretype', False),
        ('tg', 'TOP_GROUPS', 'Top Groups', False),
        ('from_contacts_pre', 'FROM_CONTACTS_PRETYPE', 'Invite To Snapchat', True),

        # posttype sections
        ('fg', 'FRIENDS_AND_GROUPS', 'Friends & Groups', True),
        ('af', 'ADD_A_FRIEND', 'Add Friends', True),
        ('tr', 'TOP_RESULTS', 'Top Results', True),
        ('lens', 'COMMUNITY_LENSES', 'Lens', True),
        ('places', 'PLACES', 'Places', True),
        ('ee', 'EDITIONS_AND_EPISODES', 'Episodes', True),
        ('shows', 'SHOWS_AND_PUBLISHERS', 'Shows', True),
        ('topics', 'TOPICS', 'Topics', True),
        ('pop_acct', 'OFFICIAL_STORIES', 'Popular Accts', True),
        ('spotlight', 'SPOTLIGHT', 'Spotlight', True),
        ('query_suggestion_posttype', 'QUERY_SUGGESTIONS_POST_TYPE', 'Query Suggestions Posttype', False),
    ]

    impression_sql = ""
    action_sql = ""
    metrics_list = []

    for (prefix, search_result_section, display_name_prefix, has_press_view_more) in metadata:
        # construct SQL
        impression_sql += "SUM(CASE WHEN search_result_section='{search_result_section}' THEN num_impressions ELSE 0 END) as {prefix}_impression_cnt,\n".format(search_result_section=search_result_section, prefix=prefix)

        action_sql += "COUNTIF(search_result_identifier IS NOT NULL AND search_result_section='{search_result_section}') as {prefix}_meaningful_action_cnt,\n".format(search_result_section=search_result_section, prefix=prefix)

        if has_press_view_more:
            action_sql += "COUNTIF(action='PRESS_VIEW_MORE' AND search_result_section='{search_result_section}') as {prefix}_press_view_more_cnt,\n".format(search_result_section=search_result_section, prefix=prefix)
            action_sql += "IF(COUNTIF(action='PRESS_VIEW_MORE' AND search_result_section='{search_result_section}')>0, 1, 0) as {prefix}_press_view_more_uu,\n".format(search_result_section=search_result_section, prefix=prefix)


        # construct metrics
        impression_metric = Metric(
            "{prefix}_impression_cnt".format(prefix=prefix),
            "{display_name_prefix}: Impression Count".format(display_name_prefix=display_name_prefix),
            dist='cont',
            daily=True,
            cumulative=True,
        )
        metrics_list.append(impression_metric)

        meaningful_action_metric = Metric(
            "{prefix}_meaningful_action_cnt".format(prefix=prefix),
            "{display_name_prefix}: Meaningful Action Count".format(display_name_prefix=display_name_prefix),
            dist='cont',
            daily=True,
            cumulative=True,
        )
        metrics_list.append(meaningful_action_metric)

        ctr_metric = Metric(
            col="{prefix}_ctr".format(prefix=prefix),
            name="{display_name_prefix}: Click Thru Rate".format(display_name_prefix=display_name_prefix),
            numerator="{prefix}_meaningful_action_cnt".format(prefix=prefix),
            denominator="{prefix}_impression_cnt".format(prefix=prefix),
            dist='ratio'
        )
        metrics_list.append(ctr_metric)

        if has_press_view_more:
            press_view_more_metric = Metric(
                "{prefix}_press_view_more_cnt".format(prefix=prefix),
                "{display_name_prefix}: Press View More - Count".format(display_name_prefix=display_name_prefix),
                dist='cont',
                daily=True,
                cumulative=True,
                desired_direction=NEGATIVE
            )
            metrics_list.append(press_view_more_metric)

            press_view_more_uu_metric = Metric(
                "{prefix}_press_view_more_uu".format(prefix=prefix),
                "{display_name_prefix}: Press View More - UU".format(display_name_prefix=display_name_prefix),
                dist='bin',
                daily=True,
                cumulative=True,
                desired_direction=NEGATIVE
            )
            metrics_list.append(press_view_more_uu_metric)

    # friend adds CTR
    metrics_list.append(
        Metric(
            "af_friend_add_action_cnt",
            "Add Friends: Friend Adds Action Count",
            dist='cont',
            daily=True,
            cumulative=True,
        )
    )

    metrics_list.append(
        Metric(
            col="af_friend_add_ctr",
            name="Add Friends: CTR (Friend Adds Only)",
            numerator="af_friend_add_action_cnt",
            denominator="af_impression_cnt",
            dist='ratio'
        )
    )

    # pretype and posttype impressions
    metrics_list.append(
        Metric(
            'pretype_impression_cnt',
            "Pretype Impression Count",
            dist='cont',
            daily=True,
            cumulative=True,
        )
    )

    metrics_list.append(
        Metric(
            'posttype_impression_cnt',
            "Posttype Impression Count",
            dist='cont',
            daily=True,
            cumulative=True,
        )
    )

    # non carousel and  carousel impressions
    metrics_list.append(
        Metric(
            'tr_non_carousel_impression_cnt',
            'Top Results: Non-Carousel Impression Count',
            dist='cont',
            daily=True,
            cumulative=True,
        )
    )

    metrics_list.append(
        Metric(
            'tr_carousel_impression_cnt',
            'Top Results: Carousel Impression Count',
            dist='cont',
            daily=True,
            cumulative=True,
        )
    )

    metrics_list.append(
        Metric(
            'pop_acct_non_carousel_impression_cnt',
            'Popular Accts: Non-Carousel Impression Count',
            dist='cont',
            daily=True,
            cumulative=True,
        )
    )

    metrics_list.append(
        Metric(
            'pop_acct_carousel_impression_cnt',
            'Popular Accts: Carousel Impression Count',
            dist='cont',
            daily=True,
            cumulative=True,
        )
    )

    mt = MetricTable(
        sql="""
        SELECT 
            a.*,
            b.* EXCEPT(ts, ghost_user_id)
        FROM (
            -- impressions 
            SELECT
                event_date AS ts,
                _table_suffix,
                ghost_user_id,
                SUM(case when is_pretype is true then num_impressions else 0 end) pretype_impression_cnt,
                SUM(case when is_pretype is false then num_impressions else 0 end) as posttype_impression_cnt,
                SUM(case when search_result_section='TOP_RESULTS' then num_impressions - num_impressions_carousel else 0 end) as tr_non_carousel_impression_cnt,
                SUM(case when search_result_section='TOP_RESULTS' then num_impressions_carousel else 0 end) as tr_carousel_impression_cnt,
                SUM(case when search_result_section='OFFICIAL_STORIES' then num_impressions - num_impressions_carousel else 0 end) as pop_acct_non_carousel_impression_cnt,
                SUM(case when search_result_section='OFFICIAL_STORIES' then num_impressions_carousel else 0 end) as pop_acct_carousel_impression_cnt,
                {impression_sql}
            FROM `sc-analytics.report_search_v2.query_result_interactions_valid_20*`
            WHERE valid_query is true
            AND source='SEARCH_UNSPECIFIED'
            AND _TABLE_SUFFIX >= '{start_trunc}' AND _TABLE_SUFFIX <= '{end_trunc}'
            GROUP BY 1, 2, 3 ) a
        LEFT OUTER JOIN 
        (
            -- actions
            SELECT
                event_date AS ts,
                _table_suffix,
                ghost_user_id AS ghost_user_id,
                {action_sql}
                COUNTIF(action='ADD_FRIEND_FROM_SEARCH_RESULT' AND search_result_section='ADD_A_FRIEND') as af_friend_add_action_cnt,
            FROM `sc-analytics.report_search_v2.search_action_20*`
            WHERE
                _TABLE_SUFFIX >= '{start_trunc}' AND
                _TABLE_SUFFIX <= '{end_trunc}'
                AND source='SEARCH_UNSPECIFIED'
            GROUP BY 1, 2, 3 ) b
        ON a._table_suffix = b._table_suffix AND a.ghost_user_id = b.ghost_user_id
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc,
            impression_sql=impression_sql,
            action_sql=action_sql
            ),
        metrics=metrics_list,
        name="section_click_thru_rates_2",
        bq_dialect='standard'

    )
    return mt

def add_friends_click_thru_rates(start_date, end_date):
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]


    metadata=[
        # metric, name
        ('all_af_impression_cnt', 'All Add Friends: Impression Count'),
        ('all_af_impression_displayname_cnt', 'All Add Friends: Impression Cnt [DisplayName]'),
        ('all_af_impression_username_cnt', 'All Add Friends: Impression Cnt [Username]'),
        ('all_af_impression_mutual_cnt', 'All Add Friends: Impression Cnt [Mutual, YMK]'),
        ('all_af_impression_contacts_cnt', 'All Add Friends: Impression Cnt [Contacts]'),
        ('all_af_impression_groups_cnt', 'All Add Friends: Impression Cnt [Groups]'),
        ('all_af_meaningful_action_cnt', 'All Add Friends: Meaningful Action Count'),
        ('all_af_add_friend_cnt_1', 'All Add Friends: Add Friend Action Count'),

        ('univsrch_af_impression_displayname_cnt', 'UnivSrch Add Friends: Impression Cnt [DisplayName]'),
        ('univsrch_af_impression_username_cnt', 'UnivSrch Add Friends: Impression Cnt [Username]'),
        ('univsrch_af_impression_mutual_cnt', 'UnivSrch Add Friends: Impression Cnt [Mutual, YMK]'),
        ('univsrch_af_impression_contacts_cnt', 'UnivSrch Add Friends: Impression Cnt [Contacts]'),
        ('univsrch_af_impression_groups_cnt', 'UnivSrch Add Friends: Impression Cnt [Groups]'),

        ('afp_af_impression_cnt', 'AddFrPg Add Friends: Impression Count'),
        ('afp_af_impression_displayname_cnt', 'AddFrPg Add Friends: Impression Cnt [DisplayName]'),
        ('afp_af_impression_username_cnt', 'AddFrPg Add Friends: Impression Cnt [Username]'),
        ('afp_af_impression_mutual_cnt', 'AddFrPg Add Friends: Impression Cnt [Mutual, YMK]'),
        ('afp_af_impression_contacts_cnt', 'AddFrPg Add Friends: Impression Cnt [Contacts]'),
        ('afp_af_impression_groups_cnt', 'AddFrPg Add Friends: Impression Cnt [Groups]'),
        ('afp_af_meaningful_action_cnt', 'AddFrPg Add Friends: Meaningful Action Count'),
        ('afp_af_add_friend_cnt_1', 'AddFrPg Add Frinds: Add Friend Action Count'),
    ]

    ratio_metadata = [
        # metric, name, numerator, denominator
        ('all_af_ctr', 'All Add Friends: Click Thru Rate',
            'all_af_meaningful_action_cnt', 'all_af_impression_cnt'),
        ('all_af_add_friend_ctr', 'All Add Friends: CTR (Friend Adds Only)',
            'all_af_add_friend_cnt_1', 'all_af_impression_cnt'),
        ('afp_af_ctr', 'AddFrPg Add Friends: Click Thru Rate',
            'afp_af_meaningful_action_cnt', 'afp_af_impression_cnt'),
        ('afp_af_add_friend_ctr', 'AddFrPg Add Friends: CTR (Friend Adds Only)',
            'afp_af_add_friend_cnt_1', 'afp_af_impression_cnt'),
    ]

    metrics_list = []

    for (metric, name) in metadata:
        curr_metric = Metric(
            metric,
            name,
            dist='cont',
            daily=True,
            cumulative=True,
        )
        metrics_list.append(curr_metric)

    for (metric, name, numerator, denominator) in ratio_metadata:
        curr_metric = Metric (
            col=metric,
            name=name,
            numerator=numerator,
            denominator=denominator,
            dist='ratio'
        )
        metrics_list.append(curr_metric)

    mt = MetricTable(
        sql="""
        SELECT 
            a.*,
            b.* EXCEPT(ts, ghost_user_id)
        FROM (
            -- impressions 
            SELECT
                event_date AS ts,
                _table_suffix,
                ghost_user_id,
                SUM(num_impressions) as all_af_impression_cnt,
                SUM(num_add_friend_impressions_displayname) as all_af_impression_displayname_cnt,
                SUM(num_add_friend_impressions_username) as all_af_impression_username_cnt,
                SUM(num_add_friend_impressions_mutual) as all_af_impression_mutual_cnt,
                SUM(num_add_friend_impressions_contacts) as all_af_impression_contacts_cnt,
                SUM(num_add_friend_impressions_groups) as all_af_impression_groups_cnt,

                SUM(if(source='SEARCH_UNSPECIFIED', num_add_friend_impressions_displayname, 0)) as univsrch_af_impression_displayname_cnt,
                SUM(if(source='SEARCH_UNSPECIFIED', num_add_friend_impressions_username, 0)) as univsrch_af_impression_username_cnt,
                SUM(if(source='SEARCH_UNSPECIFIED', num_add_friend_impressions_mutual, 0)) as univsrch_af_impression_mutual_cnt,
                SUM(if(source='SEARCH_UNSPECIFIED', num_add_friend_impressions_contacts, 0)) as univsrch_af_impression_contacts_cnt,
                SUM(if(source='SEARCH_UNSPECIFIED', num_add_friend_impressions_groups, 0)) as univsrch_af_impression_groups_cnt,

                SUM(if(source='ADD_FRIENDS_PAGE', num_impressions, 0)) as afp_af_impression_cnt,
                SUM(if(source='ADD_FRIENDS_PAGE', num_add_friend_impressions_displayname, 0)) as afp_af_impression_displayname_cnt,
                SUM(if(source='ADD_FRIENDS_PAGE', num_add_friend_impressions_username, 0)) as afp_af_impression_username_cnt,
                SUM(if(source='ADD_FRIENDS_PAGE', num_add_friend_impressions_mutual, 0)) as afp_af_impression_mutual_cnt,
                SUM(if(source='ADD_FRIENDS_PAGE', num_add_friend_impressions_contacts, 0)) as afp_af_impression_contacts_cnt,
                SUM(if(source='ADD_FRIENDS_PAGE', num_add_friend_impressions_groups, 0)) as afp_af_impression_groups_cnt,
            FROM `sc-analytics.report_search_v2.query_result_interactions_valid_20*`
            WHERE valid_query is true
            AND search_result_section='ADD_A_FRIEND'
            AND _TABLE_SUFFIX BETWEEN '{start_trunc}' AND '{end_trunc}'
            GROUP BY 1, 2, 3 ) a
        LEFT OUTER JOIN 
        (
            -- actions
            SELECT
                event_date AS ts,
                _table_suffix,
                ghost_user_id AS ghost_user_id,
                countif(search_result_identifier is not null) as all_af_meaningful_action_cnt,
                countif(action='ADD_FRIEND_FROM_SEARCH_RESULT') as all_af_add_friend_cnt_1,

                countif(source='ADD_FRIENDS_PAGE' and search_result_identifier is not null) as afp_af_meaningful_action_cnt,
                countif(source='ADD_FRIENDS_PAGE' and action='ADD_FRIEND_FROM_SEARCH_RESULT') as afp_af_add_friend_cnt_1,
            FROM `sc-analytics.report_search_v2.search_action_20*`
            WHERE
                _TABLE_SUFFIX BETWEEN '{start_trunc}' AND '{end_trunc}'
                AND search_result_section='ADD_A_FRIEND'
            GROUP BY 1, 2, 3 ) b
        ON a._table_suffix = b._table_suffix AND a.ghost_user_id = b.ghost_user_id
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc,
            ),
        metrics=metrics_list,
        name="add_friends_click_thru_rates",
        bq_dialect='standard'

    )
    return mt

def friending_query_lengths(start_date, end_date):
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    all_count = Metric(
        'all_count',
        'All Add Friends: Query Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    all_final_query_length = Metric(
        'all_final_query_length',
        'All Add Friends: Final Query Length Sum',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    all_first_impressed_query_length = Metric(
        'all_first_impressed_query_length',
        'All Add Friends: First Impressed Prefix Query Length Sum',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    all_first_retrieved_query_length = Metric(
        'all_first_retrieved_query_length',
        'All Add Friends: First Retrieval Query Length Sum',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    all_final_avg_query_length = Metric(
        col='all_final_avg_query_length',
        name='All Add Friends: Final Query Length',
        numerator='all_final_query_length',
        denominator='all_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    all_first_impressed_avg_query_length = Metric(
        col='all_first_impressed_avg_query_length',
        name='All Add Friends: First Impressed Prefix Query Length',
        numerator='all_first_impressed_query_length',
        denominator='all_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    all_first_retrieved_avg_query_length = Metric(
        col='all_first_retrieved_avg_query_length',
        name='All Add Friends: First Retrieved Prefix Query Length',
        numerator='all_first_retrieved_query_length',
        denominator='all_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    dns_count = Metric(
        'dns_count',
        'All Add Friends: Query Count [DisplayName]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    dns_final_query_length = Metric(
        'dns_final_query_length',
        'All Add Friends: Final Query Length Sum [DisplayName]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    dns_first_impressed_query_length = Metric(
        'dns_first_impressed_query_length',
        'All Add Friends: First Impressed Prefix Query Length Sum [DisplayName]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    dns_first_retrieved_query_length = Metric(
        'dns_first_retrieved_query_length',
        'All Add Friends: First Retrieval Query Length Sum [DisplayName]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    dns_final_avg_query_length = Metric(
        col='dns_final_avg_query_length',
        name='All Add Friends: Final Query Length [DisplayName]',
        numerator='dns_final_query_length',
        denominator='dns_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    dns_first_impressed_avg_query_length = Metric(
        col='dns_first_impressed_avg_query_length',
        name='All Add Friends: First Impressed Prefix Query Length [DisplayName]',
        numerator='dns_first_impressed_query_length',
        denominator='dns_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    dns_first_retrieved_avg_query_length = Metric(
        col='dns_first_retrieved_avg_query_length',
        name='All Add Friends: First Retrieved Prefix Query Length [DisplayName]',
        numerator='dns_first_retrieved_query_length',
        denominator='dns_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    un_count = Metric(
        'un_count',
        'All Add Friends: Query Count [Username]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    un_final_query_length = Metric(
        'un_final_query_length',
        'All Add Friends: Final Query Length Sum [Username]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    un_first_impressed_query_length = Metric(
        'un_first_impressed_query_length',
        'All Add Friends: First Impressed Prefix Query Length Sum [Username]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    un_first_retrieved_query_length = Metric(
        'un_first_retrieved_query_length',
        'All Add Friends: First Retrieval Query Length Sum [Username]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    un_final_avg_query_length = Metric(
        col='un_final_avg_query_length',
        name='All Add Friends: Final Query Length [Username]',
        numerator='un_final_query_length',
        denominator='un_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    un_first_impressed_avg_query_length = Metric(
        col='un_first_impressed_avg_query_length',
        name='All Add Friends: First Impressed Prefix Query Length [Username]',
        numerator='un_first_impressed_query_length',
        denominator='un_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    un_first_retrieved_avg_query_length = Metric(
        col='un_first_retrieved_avg_query_length',
        name='All Add Friends: First Retrieved Prefix Query Length [Username]',
        numerator='un_first_retrieved_query_length',
        denominator='un_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    mut_count = Metric(
        'mut_count',
        'All Add Friends: Query Count [Mutual, YMK]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    mut_final_query_length = Metric(
        'mut_final_query_length',
        'All Add Friends: Final Query Length Sum [Mutual, YMK]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    mut_first_impressed_query_length = Metric(
        'mut_first_impressed_query_length',
        'All Add Friends: First Impressed Prefix Query Length Sum [Mutual, YMK]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    mut_first_retrieved_query_length = Metric(
        'mut_first_retrieved_query_length',
        'All Add Friends: First Retrieval Query Length Sum [Mutual, YMK]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    mut_final_avg_query_length = Metric(
        col='mut_final_avg_query_length',
        name='All Add Friends: Final Query Length [Mutual, YMK]',
        numerator='mut_final_query_length',
        denominator='mut_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    mut_first_impressed_avg_query_length = Metric(
        col='mut_first_impressed_avg_query_length',
        name='All Add Friends: First Impressed Prefix Query Length [Mutual, YMK]',
        numerator='mut_first_impressed_query_length',
        denominator='mut_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    mut_first_retrieved_avg_query_length = Metric(
        col='mut_first_retrieved_avg_query_length',
        name='All Add Friends: First Retrieved Prefix Query Length [Mutual, YMK]',
        numerator='mut_first_retrieved_query_length',
        denominator='mut_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    cb_count = Metric(
        'cb_count',
        'All Add Friends: Query Count [Contacts]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    cb_final_query_length = Metric(
        'cb_final_query_length',
        'All Add Friends: Final Query Length Sum [Contacts]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    cb_first_impressed_query_length = Metric(
        'cb_first_impressed_query_length',
        'All Add Friends: First Impressed Prefix Query Length Sum [Contacts]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    cb_first_retrieved_query_length = Metric(
        'cb_first_retrieved_query_length',
        'All Add Friends: First Retrieval Query Length Sum [Contacts]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    cb_final_avg_query_length = Metric(
        col='cb_final_avg_query_length',
        name='All Add Friends: Final Query Length [Contacts]',
        numerator='cb_final_query_length',
        denominator='cb_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    cb_first_impressed_avg_query_length = Metric(
        col='cb_first_impressed_avg_query_length',
        name='All Add Friends: First Impressed Prefix Query Length [Contacts]',
        numerator='cb_first_impressed_query_length',
        denominator='cb_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    cb_first_retrieved_avg_query_length = Metric(
        col='cb_first_retrieved_avg_query_length',
        name='All Add Friends: First Retrieved Prefix Query Length [Contacts]',
        numerator='cb_first_retrieved_query_length',
        denominator='cb_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    grp_count = Metric(
        'grp_count',
        'All Add Friends: Query Count [Group]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    grp_final_query_length = Metric(
        'grp_final_query_length',
        'All Add Friends: Final Query Length Sum [Groups]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    grp_first_impressed_query_length = Metric(
        'grp_first_impressed_query_length',
        'All Add Friends: First Impressed Prefix Query Length Sum [Groups]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    grp_first_retrieved_query_length = Metric(
        'grp_first_retrieved_query_length',
        'All Add Friends: First Retrieval Query Length Sum [Groups]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    grp_final_avg_query_length = Metric(
        col='grp_final_avg_query_length',
        name='All Add Friends: Final Query Length [Groups]',
        numerator='grp_final_query_length',
        denominator='grp_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    grp_first_impressed_avg_query_length = Metric(
        col='grp_first_impressed_avg_query_length',
        name='All Add Friends: First Impressed Prefix Query Length [Groups]',
        numerator='grp_first_impressed_query_length',
        denominator='grp_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    grp_first_retrieved_avg_query_length = Metric(
        col='grp_first_retrieved_avg_query_length',
        name='All Add Friends: First Retrieved Prefix Query Length [Groups]',
        numerator='grp_first_retrieved_query_length',
        denominator='grp_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )
    mt = MetricTable(
        sql="""
        SELECT
            event_date AS ts,
            ghost_user_id AS ghost_user_id,
            count(1) as all_count,
            sum(query_text_length) as all_final_query_length,
            sum(first_impressed_prefix_length) as all_first_impressed_query_length,
            sum(first_retrieved_prefix_length) as all_first_retrieved_query_length,

            count(case when search_result_reason='DISPLAY_NAME' then 1 end) as dns_count,
            sum(case when search_result_reason='DISPLAY_NAME' then query_text_length end) as dns_final_query_length,
            sum(case when search_result_reason='DISPLAY_NAME' then first_impressed_prefix_length end) as dns_first_impressed_query_length,
            sum(case when search_result_reason='DISPLAY_NAME' then first_retrieved_prefix_length end) as dns_first_retrieved_query_length,

            count(case when search_result_reason='USERNAME' then 1 end) as un_count,
            sum(case when search_result_reason='USERNAME' then query_text_length end) as un_final_query_length,
            sum(case when search_result_reason='USERNAME' then first_impressed_prefix_length end) as un_first_impressed_query_length,
            sum(case when search_result_reason='USERNAME' then first_retrieved_prefix_length end) as un_first_retrieved_query_length,

            count(case when search_result_reason like '%MUTUAL_FRIEND%' or search_result_reason='YOU_MAY_KNOW' then 1 end) as mut_count,
            sum(case when search_result_reason like '%MUTUAL_FRIEND%' or search_result_reason='YOU_MAY_KNOW' then query_text_length end) as mut_final_query_length,
            sum(case when search_result_reason like '%MUTUAL_FRIEND%' or search_result_reason='YOU_MAY_KNOW' then first_impressed_prefix_length end) as mut_first_impressed_query_length,
            sum(case when search_result_reason like '%MUTUAL_FRIEND%' or search_result_reason='YOU_MAY_KNOW' then first_retrieved_prefix_length end) as mut_first_retrieved_query_length,

            count(case when search_result_reason='CONTACT_BOOK' then 1 end) as cb_count,
            sum(case when search_result_reason='CONTACT_BOOK' then query_text_length end) as cb_final_query_length,
            sum(case when search_result_reason='CONTACT_BOOK' then first_impressed_prefix_length end) as cb_first_impressed_query_length,
            sum(case when search_result_reason='CONTACT_BOOK' then first_retrieved_prefix_length end) as cb_first_retrieved_query_length,

            count(case when search_result_reason like '%GROUPS' then 1 end) as grp_count,
            sum(case when search_result_reason like '%GROUPS' then query_text_length end) as grp_final_query_length,
            sum(case when search_result_reason like '%GROUPS' then first_impressed_prefix_length end) as grp_first_impressed_query_length,
            sum(case when search_result_reason like '%GROUPS' then first_retrieved_prefix_length end) as grp_first_retrieved_query_length,

        FROM `sc-analytics.report_search_v2.search_friend_request_all_20*`
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND
            _TABLE_SUFFIX <= '{end_trunc}'
            AND search_result_section='ADD_A_FRIEND'
            AND search_result_identifier is not null
            AND search_query_id < 30
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
                all_count,
                all_final_query_length,
                all_first_impressed_query_length,
                all_first_retrieved_query_length,
                all_final_avg_query_length,
                all_first_impressed_avg_query_length,
                all_first_retrieved_avg_query_length,
                dns_count,
                dns_final_query_length,
                dns_first_impressed_query_length,
                dns_first_retrieved_query_length,
                dns_final_avg_query_length,
                dns_first_impressed_avg_query_length,
                dns_first_retrieved_avg_query_length,
                un_count,
                un_final_query_length,
                un_first_impressed_query_length,
                un_first_retrieved_query_length,
                un_final_avg_query_length,
                un_first_impressed_avg_query_length,
                un_first_retrieved_avg_query_length,
                mut_count,
                mut_final_query_length,
                mut_first_impressed_query_length,
                mut_first_retrieved_query_length,
                mut_final_avg_query_length,
                mut_first_impressed_avg_query_length,
                mut_first_retrieved_avg_query_length,
                cb_count,
                cb_final_query_length,
                cb_first_impressed_query_length,
                cb_first_retrieved_query_length,
                cb_final_avg_query_length,
                cb_first_impressed_avg_query_length,
                cb_first_retrieved_avg_query_length,
                grp_count,
                grp_final_query_length,
                grp_first_impressed_query_length,
                grp_first_retrieved_query_length,
                grp_final_avg_query_length,
                grp_first_impressed_avg_query_length,
                grp_first_retrieved_avg_query_length,
                ],
        name="friending_query_lengths",
        bq_dialect='standard'

    )
    return mt


def perf_latency_metric(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    """

    first_section_latency = Metric(
        col='first_section_latency',
        name='First Section Latency',
        dist='quantile',
        desired_direction=NEGATIVE
    )

    first_section_warm_latency = Metric(
        col='first_section_warm_latency',
        name='First Section Warm Latency',
        dist='quantile',
        desired_direction=NEGATIVE
    )
    first_section_cold_latency = Metric(
        col='first_section_cold_latency',
        name='First Section Cold Latency',
        dist='quantile',
        desired_direction=NEGATIVE
    )

    root_view_latency = Metric(
        col='root_view_latency',
        name='Root View Latency',
        dist='quantile',
        desired_direction=NEGATIVE
    )

    header_latency = Metric(
        col='header_latency',
        name='Header Latency',
        dist='quantile',
        desired_direction=NEGATIVE
    )

    mt = MetricTable(
      sql=
      """
      SELECT 
             event_date AS ts,
             ghost_user_id,
             case when search_latency_type='UNIVERSAL_SEARCH_FIRST_SECTION_LATENCY' then search_latency_milliseconds else NULL end as first_section_latency,
             case when search_latency_type='UNIVERSAL_SEARCH_FIRST_SECTION_LATENCY' and app_startup_type='COLD' then search_latency_milliseconds else NULL end as first_section_cold_latency,
             case when search_latency_type='UNIVERSAL_SEARCH_FIRST_SECTION_LATENCY' and app_startup_type='WARM' then search_latency_milliseconds else NULL end as first_section_warm_latency,
             case when search_latency_type='UNIVERSAL_SEARCH_ROOT_VIEW_LATENCY' then search_latency_milliseconds else NULL end as root_view_latency,
             case when search_latency_type='UNIVERSAL_SEARCH_HEADER_LATENCY' then search_latency_milliseconds else NULL end as header_latency
      FROM `sc-analytics.report_search_v2.daily_search_latency_events_*`
      WHERE  _TABLE_SUFFIX between '{start}' and '{end}'
      AND source='SEARCH_UNSPECIFIED'
      """.format(
              start=start_date,
              end=end_date),
            metrics=[
                first_section_latency,
                first_section_cold_latency,
                first_section_warm_latency,
                root_view_latency,
                header_latency
                    ],
            quantile_metrics=True,
            name='perf_latency_metric',
            bq_dialect='standard'
            )
    return mt


def searchranking_action(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    >>> searchranking_action('20170501', '20170503')
    """

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    search_open_count = Metric(
        'search_open_count',
        'Search Session Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    search_open_uu = Metric(
        'search_open_uu',
        'Search UU',
        dist='bin',
        daily=True,
        cumulative=True
    )

    story_snap_view_count = Metric(
        'story_snap_view_count',
        'Story Snap View - Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    story_snap_view_uu = Metric(
        'story_snap_view_uu',
        'Story Snap View - UU',
        dist='bin',
        daily=True,
        cumulative=True
    )

    friend_request_count = Metric(
        'friend_request_count',
        'Friend Request - Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    friend_request_uu = Metric(
        'friend_request_uu',
        'Friend Request - UU',
        dist='bin',
        daily=True,
        cumulative=True
    )


    add_verified_count = Metric(
        'add_verified_count',
        'Subscribe - Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    add_verified_uu = Metric(
        'add_verified_uu',
        'Subscribe - UU',
        dist='bin',
        daily=True,
        cumulative=True
    )


    chat_create_count = Metric(
        'chat_create_count',
        'Chat Create - Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    chat_create_uu = Metric(
        'chat_create_uu',
        'Chat Create - UU',
        dist='bin',
        daily=True,
        cumulative=True
    )

    chat_send_count = Metric(
        'chat_send_count',
        'Chat Send - Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    chat_send_uu = Metric(
        'chat_send_uu',
        'Chat Send - UU',
        dist='bin',
        daily=True,
        cumulative=True
    )

    mini_profile_friend_profile_open_count = Metric(
        'mini_profile_friend_profile_open_count',
        'Friend Profile Open - Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    mini_profile_friend_profile_open_uu = Metric(
        'mini_profile_friend_profile_open_uu',
        'Friend Profile Open - UU',
        dist='bin',
        daily=True,
        cumulative=True
    )

    mini_profile_action_sheet_open_count = Metric(
        'mini_profile_action_sheet_open_count',
        'Action Sheet Open - Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    mini_profile_action_sheet_open_uu = Metric(
        'mini_profile_action_sheet_open_uu',
        'Action Sheet Open - UU',
        dist='bin',
        daily=True,
        cumulative=True
    )

    discover_article_open_count = Metric(
        'discover_article_open_count',
        'Discover Article Open - Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    discover_article_open_uu = Metric(
        'discover_article_open_uu',
        'Discover Article Open - UU',
        dist='bin',
        daily=True,
        cumulative=True
    )

    publisher_view_open_count = Metric(
        'publisher_view_open_count',
        'Publisher View Open - Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    publisher_view_open_uu = Metric(
        'publisher_view_open_uu',
        'Publisher View Open - UU',
        dist='bin',
        daily=True,
        cumulative=True
    )
    camera_open_count = Metric(
        'camera_open_count',
        'Open Snap View - Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    camera_open_uu = Metric(
        'camera_open_uu',
        'Open Snap View - UU',
        dist='bin',
        daily=True,
        cumulative=True
    )   
    
    unlock_lens_count = Metric(
        'unlock_lens_count',
        'Lens Open - Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    unlock_lens_uu = Metric(
        'unlock_lens_uu',
        'Lens Open - UU',
        dist='bin',
        daily=True,
        cumulative=True
    )    
      
    press_view_more_count = Metric(
        'press_view_more_count',
        'Press View More - Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    press_view_more_uu = Metric(
        'press_view_more_uu',
        'Press View More - UU',
        dist='bin',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )        
    
    topic_open_count = Metric(
        'topic_open_count',
        'Topic Open - Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    topic_open_uu = Metric(
        'topic_open_uu',
        'Topic Open - UU',
        dist='bin',
        daily=True,
        cumulative=True
    )     
    
    place_open_count = Metric(
        'place_open_count',
        'Place Open - Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    place_open_uu = Metric(
        'place_open_uu',
        'Place Open - UU',
        dist='bin',
        daily=True,
        cumulative=True
    )     
        
    place_story_view_count = Metric(
        'place_story_view_count',
        'Place Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    place_story_view_uu = Metric(
        'place_story_view_uu',
        'Place Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True
    )

    sql = """
        SELECT
            TIMESTAMP_SUB( event_date, INTERVAL 8 HOUR) AS ts,
            ghost_user_id,
            sum(search_page_enter_count) search_open_count,
            IF(sum(search_page_enter_count)>0,1,0) search_open_uu,
            sum(story_snap_view_total_count) as story_snap_view_count,
            IF(sum(story_snap_view_total_count)>0,1,0) story_snap_view_uu,
            sum(friend_request_sent_count) as friend_request_count,
            IF(sum(friend_request_sent_count)>0,1,0) friend_request_uu,
            sum(friend_request_subscribe_sent_count) as add_verified_count,
            IF(sum(friend_request_subscribe_sent_count)>0,1,0) add_verified_uu,
            sum(chat_create_count) chat_create_count,
            IF(sum(chat_create_count)>0,1,0) chat_create_uu,
            sum(chat_send_count) chat_send_count,
            IF(sum(chat_send_count)>0,1,0) chat_send_uu,

            sum(mini_profile_friend_profile_open_count) mini_profile_friend_profile_open_count,
            IF(sum(mini_profile_friend_profile_open_count)>0,1,0) mini_profile_friend_profile_open_uu,
            sum(mini_profile_action_sheet_open_count) mini_profile_action_sheet_open_count,
            IF(sum(mini_profile_action_sheet_open_count)>0,1,0) mini_profile_action_sheet_open_uu,

            sum(place_story_view_count) place_story_view_count,
            IF(sum(place_story_view_count)>0,1,0) place_story_view_uu,

            sum(discover_article_open_count) discover_article_open_count,
            IF(sum(discover_article_open_count)>0,1,0) discover_article_open_uu,
            sum(publisher_view_total_open_count) publisher_view_open_count,
            IF(sum(publisher_view_total_open_count)>0,1,0) publisher_view_open_uu,
            sum(camera_open_count) as camera_open_count,
            IF(sum(camera_open_count)>0,1,0) camera_open_uu,
            sum(unlock_lens_count) unlock_lens_count,
            IF(sum(unlock_lens_count)>0,1,0) unlock_lens_uu,
            sum(press_view_more_count) press_view_more_count,
            IF(sum(press_view_more_count)>0,1,0) press_view_more_uu,
            sum(topic_open_count) topic_open_count,
            IF(sum(topic_open_count)>0,1,0) topic_open_uu,
            sum(place_open_count) place_open_count,
            IF(sum(place_open_count)>0,1,0) place_open_uu
        FROM `sc-analytics.report_search_v2.search_dau_user_country_20*`
        WHERE _TABLE_SUFFIX >= '{start_trunc}' AND _TABLE_SUFFIX <= '{end_trunc}'
        GROUP BY 1,2
        """

    def sql_callable(start_date, end_date):
        start_trunc, end_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:], pd.to_datetime(end_date).strftime('%Y%m%d')[2:]
        return sql.format(start_trunc=start_trunc, end_trunc=end_trunc)

    mt = MetricTable(
        sql=sql.format(start_trunc=start_trunc, end_trunc=end_trunc),
        sql_callable=sql_callable,
        metrics=[
            search_open_count,
            search_open_uu,
            friend_request_count,
            friend_request_uu,
            add_verified_count,
            add_verified_uu,
            chat_create_count,
            chat_create_uu,
            chat_send_count,
            chat_send_uu,
            mini_profile_friend_profile_open_count,
            mini_profile_friend_profile_open_uu,
            mini_profile_action_sheet_open_count,
            mini_profile_action_sheet_open_uu,
            story_snap_view_count,
            story_snap_view_uu,
            unlock_lens_count,
            unlock_lens_uu,
            camera_open_count,
            camera_open_uu,
            place_open_count,
            place_open_uu,
            place_story_view_count,
            place_story_view_uu,
            topic_open_count,
            topic_open_uu,
            discover_article_open_count,
            discover_article_open_uu,
            publisher_view_open_count,
            publisher_view_open_uu,
            press_view_more_count,
            press_view_more_uu,
        ],
        name="searchranking_action",
        bq_dialect='standard'
    )

    return mt

def section_index_metric(start_date, end_date):
    """
    average section index
    """
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]


    total_action_count = Metric(
        'total_action_count',
        'Total Event Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    query_action_section_index = Metric(
        'query_action_section_index',
        'Query Action Section Index',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    first_section_action = Metric(
        'first_section_action',
        'First Section Action',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    first_section_first_result_action = Metric(
        'first_section_first_result_action',
        'First Section First Result Action',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    avg_section_index = Metric(
        col='avg_section_index',
        numerator='query_action_section_index',
        denominator='total_action_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    first_section_action_ratio = Metric(
        col='first_section_action_ratio',
        numerator='first_section_action',
        denominator='total_action_count',
        dist='ratio'
    )

    first_section_first_result_action_ratio = Metric(
        col='first_section_first_result_action_ratio',
        numerator='first_section_first_result_action',
        denominator='total_action_count',
        dist='ratio'
    )


    mt = MetricTable(
        sql="""
        SELECT
          event_date AS ts,
          ghost_user_id AS ghost_user_id,
          count(1) AS total_action_count,
          SUM(search_result_section_index) AS query_action_section_index,
          SUM(case when search_result_section_index=0 then 1 else 0 end) AS first_section_action,
          SUM(case when search_result_section_index=0 and search_result_ranking_id='0' then 1 else 0 end) first_section_first_result_action
        FROM `sc-analytics.report_search_v2.search_action_20*`
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND
            _TABLE_SUFFIX <= '{end_trunc}'
        AND  event_name='SEARCHRANKING_ACTION'
        AND    action not in ('END_SEARCH_VIEW','PRESS_VIEW_MORE','DISMISS_SEARCH_VIEW',
                'CLEAR_SEARCH_QUERY','SEARCH_BUTTON_CLICK','PRESS_VIEW_LESS','OPEN_SEARCH_VIEW',
                'BACK_TO_PREVIOUS_SEARCH')
        AND is_pretype='PostType'
        AND search_result_section not in ('FRIENDS_AND_GROUPS','MY_FRIENDS')
        AND source='SEARCH_UNSPECIFIED'
        AND search_result_section_index is not null 
        AND search_result_ranking_id is not null
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[total_action_count,
                 query_action_section_index,
                 avg_section_index,
                 first_section_action,
                 first_section_action_ratio,
                 first_section_first_result_action,
                 first_section_first_result_action_ratio
                 ],
        name="section_index_metric",
        bq_dialect='standard'

    )
    return mt



def searchranking_action_detailed(start_date, end_date):
    """
    Parameters
    ----------
    start_date
    end_date
    Returns
    -------
    mt: MetricTable
    >>> searchranking_action_detailed('20170501', '20170503')
    """
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]
    
    chat_send_1_1_count = Metric(
        'chat_send_1_1_count',
        'Total Chat Send (1-to-1) Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    chat_send_1_1_uu = Metric(
        'chat_send_1_1_uu',
        'Unique Chat Send (1-to-1) User',
        dist='bin',
        daily=True,
        cumulative=True
    )    



    chat_send_group_count = Metric(
        'chat_send_group_count',
        'Total Chat Send (Group) Count',
        dist='cont',
        daily=True,
        cumulative=True
    )

    chat_send_group_uu = Metric(
        'chat_send_group_uu',
        'Unique Chat Send (Group) User',
        dist='bin',
        daily=True,
        cumulative=True
    )    


    mt = MetricTable(
        sql="""
        SELECT
            TIMESTAMP_SUB( event_date, INTERVAL 8 HOUR) AS ts, 
            ghost_user_id,
            sum(friend_request_sent_count) as friend_request_sent_blizzard_count,
            IF(sum(friend_request_sent_count)>0,1,0) friend_request_sent_blizzard_uu,
            sum(friend_request_subscribe_sent_count) as friend_request_subscribe_count,
            IF(sum(friend_request_subscribe_sent_count)>0,1,0) friend_request_subscribe_uu,
            sum(chat_send_1_1_count) chat_send_1_1_count,
            IF(sum(chat_send_1_1_count)>0,1,0) chat_send_1_1_uu,
            sum(chat_send_group_count) chat_send_group_count,
            IF(sum(chat_send_group_count)>0,1,0) chat_send_group_uu,
        FROM `sc-analytics.report_search_v2.search_dau_user_country_20*`
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND
            _TABLE_SUFFIX <= '{end_trunc}'
        GROUP BY 
            1,2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            chat_send_1_1_count,
            chat_send_1_1_uu,
            chat_send_group_count,
            chat_send_group_uu
        ],
        name="searchranking_action_detailed",
        bq_dialect='standard'
    )

    return mt

def abandoned_session_ratio(start_date, end_date):
    """
    average quality sessions
    """
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    total_session_count = Metric(
        'total_session_count',
        'Total Session Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    abandoned_session_count = Metric(
        'abandoned_session_count',
        'Abandoned Sessions',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    posttype_abandonment_rate = Metric(
        col='posttype_abandonment_rate',
        name='PostType Abandonment Rate (Session-level)',
        numerator='abandoned_session_count',
        denominator='total_session_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )


    mt = MetricTable(
        sql="""
        SELECT 
            *, 
            total_session_count-non_abandoned_session_count as abandoned_session_count
        FROM(
            SELECT 
              event_date as ts,
              ghost_user_id,
              count(distinct search_session_id) as total_session_count,
              count(distinct case when num_meaningful_actions > 0 then search_session_id end) as non_abandoned_session_count,
            FROM `sc-analytics.report_search_v2.query_result_interactions_valid_20*`
            WHERE source='SEARCH_UNSPECIFIED'
              AND is_pretype=false
              AND _TABLE_SUFFIX >= '{start_trunc}' AND _TABLE_SUFFIX <= '{end_trunc}'
            GROUP BY 1,2
        )
        
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[abandoned_session_count,
                 total_session_count,
                 posttype_abandonment_rate
                 ],
        name="abandoned_session_ratio",
        bq_dialect='standard'
    )
    return mt


def abandoned_session_ratio_afp(start_date, end_date):
    """
    average quality sessions for Add Friend Page posttype
    """
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    total_afp_session_count = Metric(
        'total_afp_session_count',
        'Total AFP Session Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    abandoned_afp_session_count = Metric(
        'abandoned_afp_session_count',
        'Abandoned AFP Sessions',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    posttype_afp_abandonment_rate = Metric(
        col='posttype_afp_abandonment_rate',
        name='PostType Session Abandonment Rate (Add Friend Page)',
        numerator='abandoned_afp_session_count',
        denominator='total_afp_session_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )


    mt = MetricTable(
        sql="""
        SELECT 
            *, 
            total_afp_session_count-non_abandoned_afp_session_count as abandoned_afp_session_count
        FROM(
            SELECT 
              event_date as ts,
              ghost_user_id,
              count(distinct search_session_id) as total_afp_session_count,
              count(distinct case when num_meaningful_actions > 0 then search_session_id end) as non_abandoned_afp_session_count,
            FROM `sc-analytics.report_search_v2.query_result_interactions_valid_20*`
            WHERE source='ADD_FRIENDS_PAGE'
              AND is_pretype=false
              AND _TABLE_SUFFIX >= '{start_trunc}' AND _TABLE_SUFFIX <= '{end_trunc}'
            GROUP BY 1,2
        )
        
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[abandoned_afp_session_count,
                 total_afp_session_count,
                 posttype_afp_abandonment_rate
                 ],
        name="abandoned_session_ratio_afp",
        bq_dialect='standard'
    )
    return mt



def abandoned_query_ratio(start_date, end_date):
    """
    calculate query abandonment rate  with/without F&G impression
    """
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    total_valid_query_count = Metric(
        'total_valid_query_count',
        'Total Valid Query Count (length >=3)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    abandoned_query_count = Metric(
        'abandoned_query_count',
        'Abandoned Query count (length >=3)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    posttype_query_abandonment_rate = Metric(
        col='posttype_query_abandonment_rate',
        name='Query Abandonment Rate (length >=3)',
        numerator='abandoned_query_count',
        denominator='total_valid_query_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    non_FG_query_count = Metric(
        'non_FG_query_count',
        'Valid Query Count (No F&G Impression & length >=3)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    non_FG_abandoned_query_count = Metric(
        'non_FG_abandoned_query_count',
        'Abandoned Query Count (No F&G Impression & length >=3)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    non_FG_query_abandonment_rate = Metric(
        col='non_FG_query_abandonment_rate',
        name='Query Abandonment Rate (No F&G Impression & length >=3)',
        numerator='non_FG_abandoned_query_count',
        denominator='non_FG_query_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    FG_query_count = Metric(
        'FG_query_count',
        'Valid Query Count (Has F&G Impression & length >=3)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    FG_abandoned_query_count = Metric(
        'FG_abandoned_query_count',
        'Abandoned Query Count (Has F&G Impression & length >=3)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    FG_query_abandonment_rate = Metric(
        col='FG_query_abandonment_rate',
        name='Query Abandonment Rate (Has F&G Impression & length >=3)',
        numerator='FG_abandoned_query_count',
        denominator='FG_query_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    mt = MetricTable(
        sql="""
        
          SELECT ts
              , ghost_user_id
              , SUM(IF(has_friends_and_groups_impression = 0,1,0)) AS non_FG_query_count
              , SUM(IF(has_friends_and_groups_impression = 0,abandoned_query,0)) AS non_FG_abandoned_query_count
              , SUM(IF(has_friends_and_groups_impression = 1,1,0)) AS FG_query_count
              , SUM(IF(has_friends_and_groups_impression = 1, abandoned_query,0)) AS FG_abandoned_query_count
              , COUNT(*) AS total_valid_query_count
              , SUM(abandoned_query) AS  abandoned_query_count
          FROM (          
                SELECT event_date as ts
                  , ghost_user_id 
                  , search_session_id
                  , search_query_id
                  , LOWER(TRIM(search_query_text)) AS search_query_text
                  , CHAR_LENGTH(LOWER(TRIM(search_query_text))) AS query_length
                  , CASE WHEN SUM(num_meaningful_actions) = 0 THEN 1 ELSE 0 END abandoned_query
                  , CASE WHEN SUM(num_meaningful_actions) > 0 THEN 1 ELSE 0 END query_w_action
                  , CASE WHEN SUM(IF(search_result_section = 'FRIENDS_AND_GROUPS', has_impression,0)) > 0 THEN 1 ELSE 0 END AS has_friends_and_groups_impression
                  , CASE WHEN SUM(IF(search_result_section = 'ADD_A_FRIEND', has_impression,0)) > 0 THEN 1 ELSE 0 END AS has_add_friends_impression
                FROM `sc-analytics.report_search_v2.query_result_interactions_valid_short_20*`  -- at query + section level
                WHERE  _TABLE_SUFFIX >= '{start_trunc}' AND _TABLE_SUFFIX <= '{end_trunc}' 
                  AND valid_query is true
                  AND source = 'SEARCH_UNSPECIFIED'
                  AND search_query_text IS NOT NULL AND LOWER(TRIM(search_query_text)) <> ""
                  AND search_result_section IS NOT NULL AND search_result_section <> ""
                  AND is_pretype is false
                  AND CHAR_LENGTH(LOWER(TRIM(search_query_text))) >=3 -- query_length >=3
                GROUP BY 1,2,3,4,5,6
                )
          GROUP BY 1,2
        
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[total_valid_query_count,
                 abandoned_query_count,
                 posttype_query_abandonment_rate,
                 non_FG_query_count,
                 non_FG_abandoned_query_count,
                 non_FG_query_abandonment_rate,
                 FG_query_count,
                 FG_abandoned_query_count,
                 FG_query_abandonment_rate
                 ],
        name="abandoned_query_ratio",
        bq_dialect='standard'
    )
    return mt


def pre_post_breakdown(start_date, end_date):
    """
    average Section Interaction
    """
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]



    pretype_meaningful = Metric(
        'pretype_meaningful',
        'PreType Meaningful Action - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    posttype_meaningful = Metric(
        'posttype_meaningful',
        'PostType Meaningful Action - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    pretype_chat_create = Metric(
        'pretype_chat_create',
        'PreType Chat Create - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    posttype_chat_create = Metric(
        'posttype_chat_create',
        'PostType Chat Create - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    pretype_open_mini_profile = Metric(
        'pretype_open_mini_profile',
        'PreType Open Mini Profile - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    posttype_open_mini_profile = Metric(
        'posttype_open_mini_profile',
        'PostType Open Mini Profile - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    pretype_open_friend_profile = Metric(
        'pretype_open_friend_profile',
        'PreType Open Friend Profile - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    posttype_open_friend_profile = Metric(
        'posttype_open_friend_profile',
        'PostType Open Friend Profile - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    pretype_open_action_sheet = Metric(
        'pretype_open_action_sheet',
        'PreType Open Action Sheet - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    posttype_open_action_sheet = Metric(
        'posttype_open_action_sheet',
        'PostType Open Action Sheet - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    

    pretype_place_story_view = Metric(
        'pretype_place_story_view',
        'PreType Place Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    posttype_place_story_view = Metric(
        'posttype_place_story_view',
        'PostType Place Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )


    pretype_add_friend = Metric(
        'pretype_add_friend',
        'PreType Add Friend - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    posttype_add_friend = Metric(
        'posttype_add_friend',
        'PostType Add Friend - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    pretype_add_verified_user = Metric(
        'pretype_add_verified_user',
        'PreType Add Verified User - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    posttype_add_verified_user = Metric(
        'posttype_add_verified_user',
        'PostType Add Verified User - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    pretype_friend_story_view = Metric(
        'pretype_friend_story_view',
        'PreType Friend Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    posttype_friend_story_view = Metric(
        'posttype_friend_story_view',
        'PostType Friend Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    pretype_user_story_view = Metric(
        'pretype_user_story_view',
        'PreType User Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    posttype_user_story_view = Metric(
        'posttype_user_story_view',
        'PostType User Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )


    pretype_publisher_view = Metric(
        'pretype_publisher_view',
        'PreType Publisher View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    posttype_publisher_view = Metric(
        'posttype_publisher_view',
        'PostType Publisher View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    pretype_unlock_lens = Metric(
        'pretype_unlock_lens',
        'PreType Open Lens - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    posttype_unlock_lens = Metric(
        'posttype_unlock_lens',
        'PostType Open Lens - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    pretype_discover_article = Metric(
        'pretype_discover_article',
        'PreType Discover Article - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    posttype_discover_article = Metric(
        'posttype_discover_article',
        'PostType Discover Article - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    pretype_press_view_more = Metric(
        'pretype_press_view_more',
        'PreType Press View More - Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    posttype_press_view_more = Metric(
        'posttype_press_view_more',
        'PostType Press View More - Count',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    pretype_open_topic_page = Metric(
        'pretype_open_topic_page',
        'PreType Open Topic Page - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    posttype_open_topic_page = Metric(
        'posttype_open_topic_page',
        'PostType Open Topic Page - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    pretype_open_place_profile = Metric(
        'pretype_open_place_profile',
        'PreType Open Place Profile - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    posttype_open_place_profile = Metric(
        'posttype_open_place_profile',
        'PostType Open Place Profile - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )


    pretype_meaningful_uu = Metric(
        'pretype_meaningful_uu',
        'PreType Meaningful Action - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    posttype_meaningful_uu = Metric(
        'posttype_meaningful_uu',
        'PostType Meaningful Action - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    pretype_chat_create_uu = Metric(
        'pretype_chat_create_uu',
        'PreType Chat Create - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    posttype_chat_create_uu = Metric(
        'posttype_chat_create_uu',
        'PostType Chat Create - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    pretype_open_mini_profile_uu = Metric(
        'pretype_open_mini_profile_uu',
        'PreType Open Mini Profile - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    posttype_open_mini_profile_uu = Metric(
        'posttype_open_mini_profile_uu',
        'PostType Open Mini Profile - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    pretype_open_friend_profile_uu = Metric(
        'pretype_open_friend_profile_uu',
        'PreType Open Friend Profile - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    posttype_open_friend_profile_uu = Metric(
        'posttype_open_friend_profile_uu',
        'PostType Open Friend Profile - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )
    pretype_open_action_sheet_uu = Metric(
        'pretype_open_action_sheet_uu',
        'PreType Open Action Sheet - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    posttype_open_action_sheet_uu = Metric(
        'posttype_open_action_sheet_uu',
        'PostType Open Action Sheet - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )
    

    pretype_place_story_view_uu = Metric(
        'pretype_place_story_view_uu',
        'PreType Place Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )
    posttype_place_story_view_uu = Metric(
        'posttype_place_story_view_uu',
        'PostType Place Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    pretype_add_friend_uu = Metric(
        'pretype_add_friend_uu',
        'PreType Add Friend - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    posttype_add_friend_uu = Metric(
        'posttype_add_friend_uu',
        'PostType Add Friend - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )
    pretype_add_verified_user_uu = Metric(
        'pretype_add_verified_user_uu',
        'PreType Add Verified User - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    posttype_add_verified_user_uu = Metric(
        'posttype_add_verified_user_uu',
        'PostType Add Verified User - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    pretype_friend_story_view_uu = Metric(
        'pretype_friend_story_view_uu',
        'PreType Friend Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    posttype_friend_story_view_uu = Metric(
        'posttype_friend_story_view_uu',
        'PostType Friend Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )
    pretype_user_story_view_uu = Metric(
        'pretype_user_story_view_uu',
        'PreType User Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    posttype_user_story_view_uu = Metric(
        'posttype_user_story_view_uu',
        'PostType User Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )


    pretype_publisher_view_uu = Metric(
        'pretype_publisher_view_uu',
        'PreType Publisher View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    posttype_publisher_view_uu = Metric(
        'posttype_publisher_view_uu',
        'PostType Publisher View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    pretype_unlock_lens_uu = Metric(
        'pretype_unlock_lens_uu',
        'PreType Open Lens - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    posttype_unlock_lens_uu = Metric(
        'posttype_unlock_lens_uu',
        'PostType Open Lens - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    pretype_discover_article_uu = Metric(
        'pretype_discover_article_uu',
        'PreType Discover Article - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    posttype_discover_article_uu = Metric(
        'posttype_discover_article_uu',
        'PostType Discover Article - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )
    pretype_press_view_more_uu = Metric(
        'pretype_press_view_more_uu',
        'PreType Press View More - UU',
        dist='bin',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    posttype_press_view_more_uu = Metric(
        'posttype_press_view_more_uu',
        'PostType Press View More - UU',
        dist='bin',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    pretype_open_topic_page_uu = Metric(
        'pretype_open_topic_page_uu',
        'PreType Open Topic Page - UU',
        dist='bin',
        daily=True,
        cumulative=True
    )

    posttype_open_topic_page_uu = Metric(
        'posttype_open_topic_page_uu',
        'PostType Open Topic Page - UU',
        dist='bin',
        daily=True,
        cumulative=True
    )

    pretype_open_place_profile_uu = Metric(
        'pretype_open_place_profile_uu',
        'PreType Open Place Profile - UU',
        dist='bin',
        daily=True,
        cumulative=True
    )

    posttype_open_place_profile_uu = Metric(
        'posttype_open_place_profile_uu',
        'PostType Open Place Profile - UU',
        dist='bin',
        daily=True,
        cumulative=True
    )



    mt = MetricTable(
        sql="""
        SELECT
            event_date AS ts,
            ghost_user_id,

            SUM(case when is_pretype is true then num_meaningful_actions else 0 end) pretype_meaningful,
            SUM(case when is_pretype is false then num_meaningful_actions else 0 end) as posttype_meaningful,
            SUM(case when is_pretype is true then num_open_chat_view_from_search_result else 0 end) pretype_chat_create,
            SUM(case when is_pretype is false then num_open_chat_view_from_search_result else 0 end) as posttype_chat_create,
            SUM(case when is_pretype is true then num_open_mini_profile_view_from_search_result else 0 end) pretype_open_mini_profile,
            SUM(case when is_pretype is false then num_open_mini_profile_view_from_search_result else 0 end) as posttype_open_mini_profile,
            SUM(case when is_pretype is true then num_open_friend_profile_from_search_result else 0 end) pretype_open_friend_profile,
            SUM(case when is_pretype is false then num_open_friend_profile_from_search_result else 0 end) as posttype_open_friend_profile,
            SUM(case when is_pretype is true then num_open_action_sheet_from_search_result else 0 end) pretype_open_action_sheet,
            SUM(case when is_pretype is false then num_open_action_sheet_from_search_result else 0 end) as posttype_open_action_sheet,
            SUM(case when is_pretype is true then num_open_place_story_from_search_result else 0 end) pretype_place_story_view,
            SUM(case when is_pretype is false then num_open_place_story_from_search_result else 0 end) as posttype_place_story_view,
            SUM(case when is_pretype is true then num_open_topic_page_from_search_result else 0 end) pretype_open_topic_page,
            SUM(case when is_pretype is false then num_open_topic_page_from_search_result else 0 end) as posttype_open_topic_page,
            SUM(case when is_pretype is true then num_open_venue_profile_from_search_result else 0 end) pretype_open_place_profile,
            SUM(case when is_pretype is false then num_open_venue_profile_from_search_result else 0 end) as posttype_open_place_profile,
            SUM(case when is_pretype is true then num_add_friend_from_search_result else 0 end) pretype_add_friend,
            SUM(case when is_pretype is false then num_add_friend_from_search_result else 0 end) as posttype_add_friend,
            SUM(case when is_pretype is true then num_open_friend_story_from_search_result else 0 end) pretype_friend_story_view,
            SUM(case when is_pretype is false then num_open_friend_story_from_search_result else 0 end) as posttype_friend_story_view,
            SUM(case when is_pretype is true then num_open_publisher_view_from_search_result else 0 end) pretype_publisher_view,
            SUM(case when is_pretype is false then num_open_publisher_view_from_search_result else 0 end) as posttype_publisher_view,
            SUM(case when is_pretype is true then num_add_verified_user_from_search_result else 0 end) pretype_add_verified_user,
            SUM(case when is_pretype is false then num_add_verified_user_from_search_result else 0 end) as posttype_add_verified_user,
            SUM(case when is_pretype is true then num_open_user_story_from_search_result else 0 end) pretype_user_story_view,
            SUM(case when is_pretype is false then num_open_user_story_from_search_result else 0 end) as posttype_user_story_view,
            SUM(case when is_pretype is true then num_unlock_lens_from_search_result else 0 end) pretype_unlock_lens,
            SUM(case when is_pretype is false then num_unlock_lens_from_search_result else 0 end) as posttype_unlock_lens,
            SUM(case when is_pretype is true then num_open_discover_article_from_search_result else 0 end) pretype_discover_article,
            SUM(case when is_pretype is false then num_open_discover_article_from_search_result else 0 end) as posttype_discover_article,
            SUM(case when is_pretype is true then num_press_view_more_from_search_result else 0 end) pretype_press_view_more,
            SUM(case when is_pretype is false then num_press_view_more_from_search_result else 0 end) as posttype_press_view_more,

            IF(SUM(case when is_pretype is true then num_meaningful_actions else 0 end)>0,1,0) pretype_meaningful_uu,
            IF(SUM(case when is_pretype is false then num_meaningful_actions else 0 end)>0,1,0) as posttype_meaningful_uu,
            IF(SUM(case when is_pretype is true then num_open_chat_view_from_search_result else 0 end)>0,1,0) pretype_chat_create_uu,
            IF(SUM(case when is_pretype is false then num_open_chat_view_from_search_result else 0 end)>0,1,0) as posttype_chat_create_uu,
            IF(SUM(case when is_pretype is true then num_open_mini_profile_view_from_search_result else 0 end)>0,1,0) pretype_open_mini_profile_uu,
            IF(SUM(case when is_pretype is false then num_open_mini_profile_view_from_search_result else 0 end)>0,1,0) as posttype_open_mini_profile_uu,
            IF(SUM(case when is_pretype is true then num_open_friend_profile_from_search_result else 0 end)>0,1,0) pretype_open_friend_profile_uu,
            IF(SUM(case when is_pretype is false then num_open_friend_profile_from_search_result else 0 end)>0,1,0) as posttype_open_friend_profile_uu,
            IF(SUM(case when is_pretype is true then num_open_action_sheet_from_search_result else 0 end)>0,1,0) pretype_open_action_sheet_uu,
            IF(SUM(case when is_pretype is false then num_open_action_sheet_from_search_result else 0 end)>0,1,0) as posttype_open_action_sheet_uu,
            IF(SUM(case when is_pretype is true then num_open_place_story_from_search_result else 0 end)>0,1,0) pretype_place_story_view_uu,
            IF(SUM(case when is_pretype is false then num_open_place_story_from_search_result else 0 end)>0,1,0) as posttype_place_story_view_uu,
            IF(SUM(case when is_pretype is true then num_open_topic_page_from_search_result else 0 end)>0,1,0) pretype_open_topic_page_uu,
            IF(SUM(case when is_pretype is false then num_open_topic_page_from_search_result else 0 end)>0,1,0) as posttype_open_topic_page_uu,
            IF(SUM(case when is_pretype is true then num_open_venue_profile_from_search_result else 0 end)>0,1,0) pretype_open_place_profile_uu,
            IF(SUM(case when is_pretype is false then num_open_venue_profile_from_search_result else 0 end)>0,1,0) as posttype_open_place_profile_uu,
            IF(SUM(case when is_pretype is true then num_add_friend_from_search_result else 0 end)>0,1,0) pretype_add_friend_uu,
            IF(SUM(case when is_pretype is false then num_add_friend_from_search_result else 0 end)>0,1,0) as posttype_add_friend_uu,
            IF(SUM(case when is_pretype is true then num_open_friend_story_from_search_result else 0 end)>0,1,0) pretype_friend_story_view_uu,
            IF(SUM(case when is_pretype is false then num_open_friend_story_from_search_result else 0 end)>0,1,0) as posttype_friend_story_view_uu,
            IF(SUM(case when is_pretype is true then num_open_publisher_view_from_search_result else 0 end)>0,1,0) pretype_publisher_view_uu,
            IF(SUM(case when is_pretype is false then num_open_publisher_view_from_search_result else 0 end)>0,1,0) as posttype_publisher_view_uu,
            IF(SUM(case when is_pretype is true then num_add_verified_user_from_search_result else 0 end)>0,1,0) pretype_add_verified_user_uu,
            IF(SUM(case when is_pretype is false then num_add_verified_user_from_search_result else 0 end)>0,1,0) as posttype_add_verified_user_uu,
            IF(SUM(case when is_pretype is true then num_open_user_story_from_search_result else 0 end)>0,1,0) pretype_user_story_view_uu,
            IF(SUM(case when is_pretype is false then num_open_user_story_from_search_result else 0 end)>0,1,0) as posttype_user_story_view_uu,
            IF(SUM(case when is_pretype is true then num_unlock_lens_from_search_result else 0 end)>0,1,0) pretype_unlock_lens_uu,
            IF(SUM(case when is_pretype is false then num_unlock_lens_from_search_result else 0 end)>0,1,0) as posttype_unlock_lens_uu,
            IF(SUM(case when is_pretype is true then num_open_discover_article_from_search_result else 0 end)>0,1,0) pretype_discover_article_uu,
            IF(SUM(case when is_pretype is false then num_open_discover_article_from_search_result else 0 end)>0,1,0) as posttype_discover_article_uu,
            IF(SUM(case when is_pretype is true then num_press_view_more_from_search_result else 0 end)>0,1,0) pretype_press_view_more_uu,
            IF(SUM(case when is_pretype is false then num_press_view_more_from_search_result else 0 end)>0,1,0) as posttype_press_view_more_uu


        FROM `sc-analytics.report_search_v2.query_result_interactions_valid_20*`
        WHERE valid_query is true
        AND source='SEARCH_UNSPECIFIED'
        AND _TABLE_SUFFIX >= '{start_trunc}' AND _TABLE_SUFFIX <= '{end_trunc}'
        GROUP BY 
            1,
            2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            pretype_meaningful,
            posttype_meaningful,
            pretype_chat_create,
            posttype_chat_create,
            pretype_open_mini_profile,
            posttype_open_mini_profile,
            pretype_open_friend_profile,
            posttype_open_friend_profile,
            pretype_open_action_sheet,
            posttype_open_action_sheet,
            pretype_add_friend,
            posttype_add_friend,
            pretype_friend_story_view,
            posttype_friend_story_view,
            pretype_publisher_view,
            posttype_publisher_view,
            pretype_add_verified_user,
            posttype_add_verified_user,
            pretype_user_story_view,
            posttype_user_story_view,
            pretype_place_story_view,
            posttype_place_story_view,
            pretype_open_topic_page,
            posttype_open_topic_page,
            pretype_open_place_profile,
            posttype_open_place_profile,
            pretype_unlock_lens,
            posttype_unlock_lens,
            pretype_discover_article,
            posttype_discover_article,
            pretype_press_view_more,
            posttype_press_view_more,
            pretype_meaningful_uu,
            posttype_meaningful_uu,
            pretype_chat_create_uu,
            posttype_chat_create_uu,
            pretype_open_mini_profile_uu,
            posttype_open_mini_profile_uu,
            pretype_open_friend_profile_uu,
            posttype_open_friend_profile_uu,
            pretype_open_action_sheet_uu,
            posttype_open_action_sheet_uu,
            pretype_add_friend_uu,
            posttype_add_friend_uu,
            pretype_friend_story_view_uu,
            posttype_friend_story_view_uu,
            pretype_publisher_view_uu,
            posttype_publisher_view_uu,
            pretype_add_verified_user_uu,
            posttype_add_verified_user_uu,
            pretype_user_story_view_uu,
            posttype_user_story_view_uu,
            pretype_place_story_view_uu,
            posttype_place_story_view_uu,
            pretype_open_topic_page_uu,
            posttype_open_topic_page_uu,
            pretype_open_place_profile_uu,
            posttype_open_place_profile_uu,
            pretype_unlock_lens_uu,
            posttype_unlock_lens_uu,
            pretype_discover_article_uu,
            posttype_discover_article_uu,
            pretype_press_view_more_uu,
            posttype_press_view_more_uu

        ],
        name="pre_post_breakdown",
        bq_dialect='standard'
    )

    return mt

def press_view_more_section(start_date, end_date):
    """
    """
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]
    
    add_a_friend_cnt = Metric(
        'add_a_friend_cnt',
        'Press View More (Add a Friend)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    frd_group_cnt = Metric(
        'frd_group_cnt',
        'Press View More (Friends and Groups)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    quick_add_cnt = Metric(
        'quick_add_cnt',
        'Press View More (Quick Add)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    off_stories_cnt = Metric(
        'off_stories_cnt',
        'Press View More (Official Stories)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    best_friends_cnt = Metric(
        'best_friends_cnt',
        'Press View More (Best Friends)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    comm_lens_cnt = Metric(
        'comm_lens_cnt',
        'Press View More (Community Lenses)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    places_cnt = Metric(
        'places_cnt',
        'Press View More (Places)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    top_results_cnt = Metric(
        'top_results_cnt',
        'Press View More (Top Results)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    edi_epi_cnt = Metric(
        'edi_epi_cnt',
        'Press View More (Editions and Episodes)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    hn_cnt = Metric(
        'hn_cnt',
        'Press View More (Happening Now)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    topics_cnt = Metric(
        'topics_cnt',
        'Press View More (Topics)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    show_pub_cnt = Metric(
        'show_pub_cnt',
        'Press View More (Shows and Publishers)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    mt = MetricTable(
        sql="""
            SELECT
                event_date AS ts,
                ghost_user_id AS ghost_user_id,
                sum(case when search_result_section='ADD_A_FRIEND' then 1 else 0 end) add_a_friend_cnt,
                sum(case when search_result_section='FRIENDS_AND_GROUPS' then 1 else 0 end) frd_group_cnt,
                sum(case when search_result_section='QUICK_ADD' then 1 else 0 end) quick_add_cnt,
                sum(case when search_result_section='OFFICIAL_STORIES' then 1 else 0 end) off_stories_cnt,
                sum(case when search_result_section='BEST_FRIENDS' then 1 else 0 end) best_friends_cnt,
                sum(case when search_result_section='COMMUNITY_LENSES' then 1 else 0 end) comm_lens_cnt,
                sum(case when search_result_section='PLACES' then 1 else 0 end) places_cnt,
                sum(case when search_result_section='TOP_RESULTS' then 1 else 0 end) top_results_cnt,
                sum(case when search_result_section='EDITIONS_AND_EPISODES' then 1 else 0 end) edi_epi_cnt,
                sum(case when search_result_section='HAPPENING_NOW' then 1 else 0 end) hn_cnt,
                sum(case when search_result_section='TOPICS' then 1 else 0 end) topics_cnt,
                sum(case when search_result_section='SHOWS_AND_PUBLISHERS' then 1 else 0 end) show_pub_cnt
            FROM `sc-analytics.report_search_v2.search_action_20*`
            WHERE
                _TABLE_SUFFIX >= '{start_trunc}' AND _TABLE_SUFFIX <= '{end_trunc}'
            AND action='PRESS_VIEW_MORE'
            AND source='SEARCH_UNSPECIFIED'
            group by 1,2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            add_a_friend_cnt,
            frd_group_cnt,
            quick_add_cnt,
            off_stories_cnt,
            best_friends_cnt,
            comm_lens_cnt,
            places_cnt,
            top_results_cnt,
            edi_epi_cnt,
            hn_cnt,
            topics_cnt,
            show_pub_cnt   
        ],
        name="press_view_more_section",
        bq_dialect='standard'
   )

    return mt

def action_dau(start_date, end_date):
    """
    Quick Add Section Interaction
    """

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]



    search_dau = Metric(
        'search_dau',
        'Search Daily Active User',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    pretype_action_dau = Metric(
        'pretype_action_dau',
        'Pretype Daily Active User',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    posttype_action_dau = Metric(
        'posttype_action_dau',
        'Unique Posttype Daily Active User',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    pretype_action_dau_pct = Metric(
        col='pretype_action_dau_pct',
        name='% Search DAU with Pretype Meaningful Action',
        numerator='pretype_action_dau',
        denominator='search_dau',
        dist='ratio'
    )

    posttype_action_dau_pct = Metric(
        col='posttype_action_dau_pct',
        name='% Search DAU with Posttype Meaningful Action',
        numerator='posttype_action_dau',
        denominator='search_dau',
        dist='ratio'
    )

    pretype_session_cnt = Metric(
        'pretype_session_cnt',
        'Pretype Session Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    pretype_action_session_cnt = Metric(
        'pretype_action_session_cnt',
        'Pretype Session with Meaningful Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    posttype_session_cnt = Metric(
        'posttype_session_cnt',
        'Posttype Session Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    posttype_action_session_cnt = Metric(
        'posttype_action_session_cnt',
        'PostType Session with Meaningful Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )


    mt = MetricTable(
        sql="""
        SELECT 
            ts, ghost_user_id,
            pretype_session_cnt,
            pretype_action_session_cnt,
            posttype_session_cnt,
            posttype_action_session_cnt,

            1 as search_dau,
            case when pretype_action_dau>0 then 1 else 0 end as pretype_action_dau,
            case when posttype_action_dau>0 then 1 else 0 end as posttype_action_dau,

        FROM(        
            SELECT
              event_date AS ts,
              ghost_user_id AS ghost_user_id,

              count(distinct case when is_pretype is true then search_session_id else NULL end ) pretype_session_cnt,
              count(distinct case when is_pretype is true AND num_meaningful_actions>0 then search_session_id else NULL end ) pretype_action_session_cnt,
              count(distinct case when is_pretype is false then search_session_id else NULL end ) posttype_session_cnt,
              count(distinct case when is_pretype is false AND num_meaningful_actions>0 then search_session_id else NULL end ) posttype_action_session_cnt,

              sum(case when is_pretype is true AND num_meaningful_actions>0 THEN 1 ELSE NULL END) pretype_action_dau,
              sum(case when is_pretype is false AND num_meaningful_actions>0 THEN 1 ELSE NULL END) posttype_action_dau 

            FROM `sc-analytics.report_search_v2.query_result_interactions_valid_20*`
            WHERE
                _TABLE_SUFFIX >= '{start_trunc}' AND
                _TABLE_SUFFIX <= '{end_trunc}'
            AND source='SEARCH_UNSPECIFIED'
            GROUP BY 1, 2
        )
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            search_dau,
            pretype_action_dau,
            posttype_action_dau,
            pretype_action_dau_pct,
            posttype_action_dau_pct,
            pretype_session_cnt,
            pretype_action_session_cnt,
            posttype_session_cnt,
            posttype_action_session_cnt,
        ],
        name="action_dau",
        bq_dialect='standard'

    )
    return mt


def section_mrr(start_date, end_date):
    """
    average section index
    """
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]


    pretype_count = Metric(
        'pretype_count',
        'PreType Event Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    posttype_count = Metric(
        'posttype_count',
        'PostType Event Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    pretype_section_index = Metric(
        'pretype_section_index',
        'PreType Section Index',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    posttype_section_index = Metric(
        'posttype_section_index',
        'PostType Section Index',
        dist='cont',
        daily=True,
        cumulative=True,
    )


    pretype_section_mrr = Metric(
        col='pretype_section_mrr',
        numerator='pretype_section_index',
        denominator='pretype_count',
        dist='ratio'
    )

    posttype_section_mrr = Metric(
        col='posttype_section_mrr',
        numerator='posttype_section_index',
        denominator='posttype_count',
        dist='ratio'
    )

    event_count = Metric(
        'event_count',
        'Total Event Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    section_index = Metric(
        'section_index',
        'Section Index',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    section_mean_reciprocal_rank = Metric(
        col='section_mean_reciprocal_rank',
        numerator='section_index',
        denominator='event_count',
        dist='ratio'
    )

    fng_action_count = Metric(
        col='fng_action_count',
        name='F&G Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    fng_reciprocal_rank = Metric(
        col='fng_reciprocal_rank',
        name='F&G Reciprocal Rank',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    fng_section_mrr = Metric(
        col='fng_section_mrr',
        name='F&G Section-level MRR',
        numerator='fng_reciprocal_rank',
        denominator='fng_action_count',
        dist='ratio'
    )   

    af_action_count = Metric(
        col='af_action_count',
        name='AF Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    af_reciprocal_rank = Metric(
        col='af_reciprocal_rank',
        name='AF Reciprocal Rank',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    af_section_mrr = Metric(
        col='af_section_mrr',
        name='Add Friends Section-level MRR',
        numerator='af_reciprocal_rank',
        denominator='af_action_count',
        dist='ratio'
    )   

    pa_action_count = Metric(
        col='pa_action_count',
        name='PA Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    pa_reciprocal_rank = Metric(
        col='pa_reciprocal_rank',
        name='PA Reciprocal Rank',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    pa_section_mrr = Metric(
        col='pa_section_mrr',
        name='Popular Accounts Section-level MRR',
        numerator='pa_reciprocal_rank',
        denominator='pa_action_count',
        dist='ratio'
    )   

    tr_action_count = Metric(
        col='tr_action_count',
        name='TR Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    tr_reciprocal_rank = Metric(
        col='tr_reciprocal_rank',
        name='TR Reciprocal Rank',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    tr_section_mrr = Metric(
        col='tr_section_mrr',
        name='Top Results Section-level MRR',
        numerator='tr_reciprocal_rank',
        denominator='tr_action_count',
        dist='ratio'
    )   

    invite_action_count = Metric(
        col='invite_action_count',
        name='Invite Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    invite_reciprocal_rank = Metric(
        col='invite_reciprocal_rank',
        name='Invite Reciprocal Rank',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    invite_section_mrr = Metric(
        col='invite_section_mrr',
        name='Invites Section-level MRR',
        numerator='invite_reciprocal_rank',
        denominator='invite_action_count',
        dist='ratio'
    )   

    lens_action_count = Metric(
        col='lens_action_count',
        name='Lens Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    lens_reciprocal_rank = Metric(
        col='lens_reciprocal_rank',
        name='Lens Reciprocal Rank',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    lens_section_mrr = Metric(
        col='lens_section_mrr',
        name='Lenses Section-level MRR',
        numerator='lens_reciprocal_rank',
        denominator='lens_action_count',
        dist='ratio'
    )   

    place_action_count = Metric(
        col='place_action_count',
        name='Place Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    place_reciprocal_rank = Metric(
        col='place_reciprocal_rank',
        name='Place Reciprocal Rank',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    place_section_mrr = Metric(
        col='place_section_mrr',
        name='Places Section-level MRR',
        numerator='place_reciprocal_rank',
        denominator='place_action_count',
        dist='ratio'
    )   

    topic_action_count = Metric(
        col='topic_action_count',
        name='Topic Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    topic_reciprocal_rank = Metric(
        col='topic_reciprocal_rank',
        name='Topic Reciprocal Rank',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    topic_section_mrr = Metric(
        col='topic_section_mrr',
        name='Topics Section-level MRR',
        numerator='topic_reciprocal_rank',
        denominator='topic_action_count',
        dist='ratio'
    )   

    edition_action_count = Metric(
        col='edition_action_count',
        name='Edition Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    edition_reciprocal_rank = Metric(
        col='edition_reciprocal_rank',
        name='Edition Reciprocal Rank',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    edition_section_mrr = Metric(
        col='edition_section_mrr',
        name='Editions Section-level MRR',
        numerator='edition_reciprocal_rank',
        denominator='edition_action_count',
        dist='ratio'
    )   

    shows_action_count = Metric(
        col='shows_action_count',
        name='Shows Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    shows_reciprocal_rank = Metric(
        col='shows_reciprocal_rank',
        name='Shows Reciprocal Rank',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    shows_section_mrr = Metric(
        col='shows_section_mrr',
        name='Shows Section-level MRR',
        numerator='shows_reciprocal_rank',
        denominator='shows_action_count',
        dist='ratio'
    )   

    music_action_count = Metric(
        col='music_action_count',
        name='Music Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    music_reciprocal_rank = Metric(
        col='music_reciprocal_rank',
        name='Music Reciprocal Rank',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    music_section_mrr = Metric(
        col='music_section_mrr',
        name='Musics Section-level MRR',
        numerator='music_reciprocal_rank',
        denominator='music_action_count',
        dist='ratio'
    )   

    spotlight_action_count = Metric(
        col='spotlight_action_count',
        name='Spotlight Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    spotlight_reciprocal_rank = Metric(
        col='spotlight_reciprocal_rank',
        name='Spotlight Reciprocal Rank',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    spotlight_section_mrr = Metric(
        col='spotlight_section_mrr',
        name='Spotlight Section-level MRR',
        numerator='spotlight_reciprocal_rank',
        denominator='spotlight_action_count',
        dist='ratio'
    )   


    mt = MetricTable(
        sql="""

        WITH search_action AS (
            SELECT 
                  a.event_date,
                  a.ghost_user_id,
                  a.search_result_section,
                  a.search_result_section_index,
                  IF(q.query_suggestion_posttype_impressions > 0, 1,0) AS has_qs_impression
            FROM `sc-analytics.report_search_v2.search_action_20*` AS a 
            LEFT JOIN `sc-analytics.report_search_v2.search_session_query_level_20*`  AS q
              ON a._TABLE_SUFFIX = q._TABLE_SUFFIX
                AND a.ghost_user_id = q.ghost_user_id
                AND a.search_session_id = q.search_session_id
                AND a.search_query_id = q.search_query_id
            WHERE 
                a._TABLE_SUFFIX BETWEEN '{start_trunc}' AND '{end_trunc}'
                AND a.search_result_section_index is not null 
                AND a.search_result_ranking_id is not null
                AND a.search_result_ranking_id <>''
                AND cast(a.search_result_ranking_id as integer)>=0
                AND cast(a.search_result_ranking_id as integer)<=49
                AND a.is_pretype='PostType'
                AND a.source='SEARCH_UNSPECIFIED'
                AND q.source = 'SEARCH_UNSPECIFIED'

        )
        SELECT
            event_date AS ts,
            ghost_user_id AS ghost_user_id,
            count(1) AS event_count,
            sum(1/(1+search_result_section_index)) as section_index,
            sum(case when search_result_section in ('BEST_FRIENDS','RECENT_SEARCHES','QUICK_ADD','TOP_GROUPS','STARS_PRETYPE','LENS_PRETYPE','PUBLISHERS_PRETYPE','PLACES_PRETYPE','FROM_CONTACTS_PRETYPE') then 1 else 0 end) pretype_count,
            sum(case when search_result_section in ('FRIENDS_AND_GROUPS','ADD_A_FRIEND','COMMUNITY_LENSES','TOP_RESULTS','PLACES','EDITIONS_AND_EPISODES','TOPICS','SHOWS_AND_PUBLISHERS','FROM_CONTACTS','ADDED_ME','MUSIC_LICENSED','HERE_FOR_YOU','SPOTLIGHT','OFFICIAL_STORIES') then 1 else 0 end) posttype_count,

            sum(case when search_result_section in ('BEST_FRIENDS','RECENT_SEARCHES','QUICK_ADD','TOP_GROUPS','STARS_PRETYPE','LENS_PRETYPE','PUBLISHERS_PRETYPE','PLACES_PRETYPE','FROM_CONTACTS_PRETYPE') then (1/(1+search_result_section_index)) else 0 end) pretype_section_index,
            sum(case when search_result_section in ('FRIENDS_AND_GROUPS','ADD_A_FRIEND','COMMUNITY_LENSES','TOP_RESULTS','PLACES','EDITIONS_AND_EPISODES','TOPICS','SHOWS_AND_PUBLISHERS','FROM_CONTACTS','ADDED_ME','MUSIC_LICENSED','HERE_FOR_YOU','SPOTLIGHT','OFFICIAL_STORIES') then (1/(1+search_result_section_index - has_qs_impression)) else 0 end) posttype_section_index,

            sum(case when search_result_section = 'FRIENDS_AND_GROUPS' then 1 else 0 end) fng_action_count, 
            sum(case when search_result_section = 'FRIENDS_AND_GROUPS' then (1/(1+search_result_section_index - has_qs_impression)) else 0 end) fng_reciprocal_rank, 

            sum(case when search_result_section = 'ADD_A_FRIEND' then 1 else 0 end) af_action_count, 
            sum(case when search_result_section = 'ADD_A_FRIEND' then (1/(1+search_result_section_index - has_qs_impression)) else 0 end) af_reciprocal_rank, 

            sum(case when search_result_section = 'OFFICIAL_STORIES' then 1 else 0 end) pa_action_count, 
            sum(case when search_result_section = 'OFFICIAL_STORIES' then (1/(1+search_result_section_index - has_qs_impression)) else 0 end) pa_reciprocal_rank, 

            sum(case when search_result_section = 'TOP_RESULTS' then 1 else 0 end) tr_action_count, 
            sum(case when search_result_section = 'TOP_RESULTS' then (1/(1+search_result_section_index - has_qs_impression)) else 0 end) tr_reciprocal_rank, 

            sum(case when search_result_section = 'FROM_CONTACTS' then 1 else 0 end) invite_action_count, 
            sum(case when search_result_section = 'FROM_CONTACTS' then (1/(1+search_result_section_index - has_qs_impression)) else 0 end) invite_reciprocal_rank, 

            sum(case when search_result_section = 'COMMUNITY_LENSES' then 1 else 0 end) lens_action_count, 
            sum(case when search_result_section = 'COMMUNITY_LENSES' then (1/(1+search_result_section_index - has_qs_impression)) else 0 end) lens_reciprocal_rank, 

            sum(case when search_result_section = 'PLACES' then 1 else 0 end) place_action_count, 
            sum(case when search_result_section = 'PLACES' then (1/(1+search_result_section_index - has_qs_impression)) else 0 end) place_reciprocal_rank, 

            sum(case when search_result_section = 'TOPICS' then 1 else 0 end) topic_action_count, 
            sum(case when search_result_section = 'TOPICS' then (1/(1+search_result_section_index - has_qs_impression)) else 0 end) topic_reciprocal_rank, 

            sum(case when search_result_section = 'EDITIONS_AND_EPISODES' then 1 else 0 end) edition_action_count, 
            sum(case when search_result_section = 'EDITIONS_AND_EPISODES' then (1/(1+search_result_section_index - has_qs_impression)) else 0 end) edition_reciprocal_rank, 

            sum(case when search_result_section = 'SHOWS_AND_PUBLISHERS' then 1 else 0 end) shows_action_count, 
            sum(case when search_result_section = 'SHOWS_AND_PUBLISHERS' then (1/(1+search_result_section_index - has_qs_impression)) else 0 end) shows_reciprocal_rank, 

            sum(case when search_result_section = 'MUSIC_LICENSED' then 1 else 0 end) music_action_count, 
            sum(case when search_result_section = 'MUSIC_LICENSED' then (1/(1+search_result_section_index - has_qs_impression)) else 0 end) music_reciprocal_rank,     

            sum(case when search_result_section = 'SPOTLIGHT' then 1 else 0 end) spotlight_action_count, 
            sum(case when search_result_section = 'SPOTLIGHT' then (1/(1+search_result_section_index - has_qs_impression)) else 0 end) spotlight_reciprocal_rank,     

        FROM search_action
        WHERE 1+search_result_section_index - has_qs_impression >=1 -- Correcting section rank index for Query Suggestion impression
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[pretype_count,
                 posttype_count,
                 pretype_section_index,
                 posttype_section_index,
                 pretype_section_mrr,
                 posttype_section_mrr,
                 event_count,
                 section_index,
                 section_mean_reciprocal_rank,
                 fng_action_count,
                 fng_reciprocal_rank,   
                 fng_section_mrr,
                 af_action_count,
                 af_reciprocal_rank,
                 af_section_mrr,
                 pa_action_count,
                 pa_reciprocal_rank,
                 pa_section_mrr,
                 tr_action_count,
                 tr_reciprocal_rank,
                 tr_section_mrr,
                 invite_action_count,
                 invite_reciprocal_rank,
                 invite_section_mrr,
                 lens_action_count,
                 lens_reciprocal_rank,
                 lens_section_mrr,
                 place_action_count,
                 place_reciprocal_rank,
                 place_section_mrr,
                 topic_action_count,
                 topic_reciprocal_rank,
                 topic_section_mrr,
                 edition_action_count,
                 edition_reciprocal_rank,
                 edition_section_mrr,
                 shows_action_count,
                 shows_reciprocal_rank,
                 shows_section_mrr,
                 music_action_count,
                 music_reciprocal_rank,
                 music_section_mrr,
                 spotlight_action_count,
                 spotlight_reciprocal_rank,
                 spotlight_section_mrr
                 ],
        name="section_mrr",
        bq_dialect='standard'

    )
    return mt



def action_based_recall_at_one(start_date, end_date):
    """
    average section index
    """
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]


    af_action_count = Metric(
        col='af_action_count',
        name='AF Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    af_action_1st_section = Metric(
        col='af_action_1st_section',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    af_section_action_recall_at_one = Metric(
        col='af_section_action_recall_at_one',
        name='Add Friends Section Action Recall @ 1',
        numerator='af_action_1st_section',
        denominator='af_action_count',
        dist='ratio'
    )   

    pa_action_count = Metric(
        col='pa_action_count',
        name='PA Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    pa_action_1st_section = Metric(
        col='pa_action_1st_section',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    pa_section_action_recall_at_one = Metric(
        col='pa_section_action_recall_at_one',
        name='Popular Accounts Section Action Recall @ 1',
        numerator='pa_action_1st_section',
        denominator='pa_action_count',
        dist='ratio'
    )   
  

    invite_action_count = Metric(
        col='invite_action_count',
        name='Invite Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    invite_action_1st_section = Metric(
        col='invite_action_1st_section',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    invite_section_action_recall_at_one = Metric(
        col='invite_section_action_recall_at_one',
        name='Invites Section Action Recall @ 1',
        numerator='invite_action_1st_section',
        denominator='invite_action_count',
        dist='ratio'
    )   

    lens_action_count = Metric(
        col='lens_action_count',
        name='Lens Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    lens_action_1st_section = Metric(
        col='lens_action_1st_section',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    lens_section_action_recall_at_one = Metric(
        col='lens_section_action_recall_at_one',
        name='Lenses Section Action Recall @ 1',
        numerator='lens_action_1st_section',
        denominator='lens_action_count',
        dist='ratio'
    )   

    place_action_count = Metric(
        col='place_action_count',
        name='Place Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    place_action_1st_section = Metric(
        col='place_action_1st_section',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    place_section_action_recall_at_one = Metric(
        col='place_section_action_recall_at_one',
        name='Places Section Action Recall @ 1',
        numerator='place_action_1st_section',
        denominator='place_action_count',
        dist='ratio'
    )   

    topic_action_count = Metric(
        col='topic_action_count',
        name='Topic Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    topic_action_1st_section = Metric(
        col='topic_action_1st_section',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    topic_section_action_recall_at_one = Metric(
        col='topic_section_action_recall_at_one',
        name='Topics Section Action Recall @ 1',
        numerator='topic_action_1st_section',
        denominator='topic_action_count',
        dist='ratio'
    )   

    edition_action_count = Metric(
        col='edition_action_count',
        name='Edition Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    edition_action_1st_section = Metric(
        col='edition_action_1st_section',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    edition_section_action_recall_at_one = Metric(
        col='edition_section_action_recall_at_one',
        name='Editions Section Action Recall @ 1',
        numerator='edition_action_1st_section',
        denominator='edition_action_count',
        dist='ratio'
    )   

    shows_action_count = Metric(
        col='shows_action_count',
        name='Shows Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    shows_action_1st_section = Metric(
        col='shows_action_1st_section',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    shows_section_action_recall_at_one = Metric(
        col='shows_section_action_recall_at_one',
        name='Shows Section Action Recall @ 1',
        numerator='shows_action_1st_section',
        denominator='shows_action_count',
        dist='ratio'
    )   

    music_action_count = Metric(
        col='music_action_count',
        name='Music Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    music_action_1st_section = Metric(
        col='music_action_1st_section',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    music_section_action_recall_at_one = Metric(
        col='music_section_action_recall_at_one',
        name='Musics Section Action Recall @ 1',
        numerator='music_action_1st_section',
        denominator='music_action_count',
        dist='ratio'
    )   

    spotlight_action_count = Metric(
        col='spotlight_action_count',
        name='Spotlight Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    spotlight_action_1st_section = Metric(
        col='spotlight_action_1st_section',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    spotlight_section_action_recall_at_one = Metric(
        col='spotlight_section_action_recall_at_one',
        name='Spotlight Section Action Recall @ 1',
        numerator='spotlight_action_1st_section',
        denominator='spotlight_action_count',
        dist='ratio'
    )   


    mt = MetricTable(
        sql="""

        WITH search_action AS (
            SELECT 
                  a.event_date,
                  a.ghost_user_id,
                  a.search_result_section,
                  a.search_result_section_index,
                  IF(q.query_suggestion_posttype_impressions > 0, 1,0) AS has_qs_impression,
                  IF(q.top_results_impressions > 0, 1,0) AS has_tr_impression,
                  IF(q.friends_and_groups_impressions > 0, 1,0) AS has_fg_impression
            FROM `sc-analytics.report_search_v2.search_action_20*` AS a 
            LEFT JOIN `sc-analytics.report_search_v2.search_session_query_level_20*`  AS q
              ON a._TABLE_SUFFIX = q._TABLE_SUFFIX
                AND a.ghost_user_id = q.ghost_user_id
                AND a.search_session_id = q.search_session_id
                AND a.search_query_id = q.search_query_id
            WHERE 
                a._TABLE_SUFFIX BETWEEN '{start_trunc}' AND '{end_trunc}'
                AND a.search_result_section_index is not null 
                AND a.search_result_ranking_id is not null
                AND a.search_result_ranking_id <>''
                AND cast(a.search_result_ranking_id as integer)>=0
                AND cast(a.search_result_ranking_id as integer)<=49
                AND a.is_pretype='PostType'
                AND a.source='SEARCH_UNSPECIFIED'
                AND q.source = 'SEARCH_UNSPECIFIED'

        )
        SELECT
            event_date AS ts,
            ghost_user_id AS ghost_user_id,
            sum(case when search_result_section = 'ADD_A_FRIEND' then 1 else 0 end) af_action_count, 
            sum(case when search_result_section = 'ADD_A_FRIEND' and 1+search_result_section_index - has_qs_impression - has_tr_impression -  has_fg_impression = 1 then 1 else 0 end) af_action_1st_section, 

            sum(case when search_result_section = 'OFFICIAL_STORIES' then 1 else 0 end) pa_action_count, 
            sum(case when search_result_section = 'OFFICIAL_STORIES' and 1+search_result_section_index - has_qs_impression - has_tr_impression -  has_fg_impression = 1 then 1 else 0 end) pa_action_1st_section, 

            sum(case when search_result_section = 'FROM_CONTACTS' then 1 else 0 end) invite_action_count, 
            sum(case when search_result_section = 'FROM_CONTACTS' and 1+search_result_section_index - has_qs_impression - has_tr_impression -  has_fg_impression = 1 then 1 else 0 end) invite_action_1st_section, 

            sum(case when search_result_section = 'COMMUNITY_LENSES' then 1 else 0 end) lens_action_count, 
            sum(case when search_result_section = 'COMMUNITY_LENSES' and 1+search_result_section_index - has_qs_impression - has_tr_impression -  has_fg_impression = 1 then 1 else 0 end) lens_action_1st_section, 

            sum(case when search_result_section = 'PLACES' then 1 else 0 end) place_action_count, 
            sum(case when search_result_section = 'PLACES' and 1+search_result_section_index - has_qs_impression - has_tr_impression -  has_fg_impression = 1 then 1 else 0 end) place_action_1st_section, 

            sum(case when search_result_section = 'TOPICS' then 1 else 0 end) topic_action_count, 
            sum(case when search_result_section = 'TOPICS' and 1+search_result_section_index - has_qs_impression - has_tr_impression -  has_fg_impression = 1 then 1 else 0 end) topic_action_1st_section, 

            sum(case when search_result_section = 'EDITIONS_AND_EPISODES' then 1 else 0 end) edition_action_count, 
            sum(case when search_result_section = 'EDITIONS_AND_EPISODES' and 1+search_result_section_index - has_qs_impression - has_tr_impression -  has_fg_impression = 1 then 1 else 0 end) edition_action_1st_section, 

            sum(case when search_result_section = 'SHOWS_AND_PUBLISHERS' then 1 else 0 end) shows_action_count, 
            sum(case when search_result_section = 'SHOWS_AND_PUBLISHERS' and 1+search_result_section_index - has_qs_impression - has_tr_impression -  has_fg_impression = 1 then 1 else 0 end) shows_action_1st_section, 

            sum(case when search_result_section = 'MUSIC_LICENSED' then 1 else 0 end) music_action_count, 
            sum(case when search_result_section = 'MUSIC_LICENSED' and 1+search_result_section_index - has_qs_impression - has_tr_impression -  has_fg_impression = 1 then 1 else 0 end) music_action_1st_section,     

            sum(case when search_result_section = 'SPOTLIGHT' then 1 else 0 end) spotlight_action_count, 
            sum(case when search_result_section = 'SPOTLIGHT' and 1+search_result_section_index - has_qs_impression - has_tr_impression -  has_fg_impression = 1 then 1 else 0 end) spotlight_action_1st_section,    

        FROM search_action
        WHERE 1+search_result_section_index - has_qs_impression - has_tr_impression -  has_fg_impression >=1 --Correcting section rank index for Query Suggestion, Top Results and F&G impressions
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[af_action_count,
                 af_action_1st_section,
                 af_section_action_recall_at_one,
                 pa_action_count,
                 pa_action_1st_section,
                 pa_section_action_recall_at_one,
                 invite_action_count,
                 invite_action_1st_section,
                 invite_section_action_recall_at_one,
                 lens_action_count,
                 lens_action_1st_section,
                 lens_section_action_recall_at_one,
                 place_action_count,
                 place_action_1st_section,
                 place_section_action_recall_at_one,
                 topic_action_count,
                 topic_action_1st_section,
                 topic_section_action_recall_at_one,
                 edition_action_count,
                 edition_action_1st_section,
                 edition_section_action_recall_at_one,
                 shows_action_count,
                 shows_action_1st_section,
                 shows_section_action_recall_at_one,
                 music_action_count,
                 music_action_1st_section,
                 music_section_action_recall_at_one,
                 spotlight_action_count,
                 spotlight_action_1st_section,
                 spotlight_section_action_recall_at_one
                 ],
        name="action_based_recall_at_one",
        bq_dialect='standard'

    )
    return mt

def best_friends_action(start_date, end_date):
    """
    Best Friend Section Interaction
    """
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]


    bf_chat_view_cnt = Metric(
        'bf_chat_view_cnt',
        'Best Friends: Chat View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    bf_chat_view_uu = Metric(
        'bf_chat_view_uu',
        'Best Friends: Chat View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )


    bf_friend_story_cnt = Metric(
        'bf_friend_story_cnt',
        'Best Friends: Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    bf_friend_story_uu = Metric(
        'bf_friend_story_uu',
        'Best Friends: Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )


    bf_action_sheet_cnt = Metric(
        'bf_action_sheet_cnt',
        'Best Friends: Action Sheet Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    bf_action_sheet_uu = Metric(
        'bf_action_sheet_uu',
        'Best Friends: Action Sheet Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )


    bf_snap_view_cnt = Metric(
        'bf_snap_view_cnt',
        'Best Friends: Open Snap View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    bf_snap_view_uu = Metric(
        'bf_snap_view_uu',
        'Best Friends: Open Snap View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    bf_friend_profile_cnt = Metric(
        'bf_friend_profile_cnt',
        'Best Friends: Friend Profile Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    bf_friend_profile_uu = Metric(
        'bf_friend_profile_uu',
        'Best Friends: Friend Profile Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )


    mt = MetricTable(
        sql="""
        SELECT
          event_date AS ts,
          ghost_user_id AS ghost_user_id,

          sum(case when action='OPEN_CHAT_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) bf_chat_view_cnt,
          IF(sum(case when action='OPEN_CHAT_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) bf_chat_view_uu,

          sum(case when action='OPEN_FRIEND_STORY_FROM_SEARCH_RESULT' then 1 else 0 end) bf_friend_story_cnt,
          IF(sum(case when action='OPEN_FRIEND_STORY_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) bf_friend_story_uu,

          sum(case when action='OPEN_UNIFIED_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) bf_action_sheet_cnt,
          IF(sum(case when action='OPEN_UNIFIED_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0)  bf_action_sheet_uu,

          sum(case when action='OPEN_SNAP_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) bf_snap_view_cnt,
          IF(sum(case when action='OPEN_SNAP_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) bf_snap_view_uu,

          sum(case when action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) bf_friend_profile_cnt,
          IF(sum(case when action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) bf_friend_profile_uu

        FROM `sc-analytics.report_search_v2.search_action_20*`
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND
            _TABLE_SUFFIX <= '{end_trunc}'
        AND search_query_context not in ('MAPS_SCREEN_FRIEND_FINDER','EAGLE_SEARCH')
        AND source='SEARCH_UNSPECIFIED'
        AND search_result_section='BEST_FRIENDS'
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
                bf_chat_view_cnt,
                bf_chat_view_uu,
                bf_friend_story_cnt,
                bf_friend_story_uu,
                bf_action_sheet_cnt,
                bf_action_sheet_uu,
                bf_snap_view_cnt,
                bf_snap_view_uu,
                bf_friend_profile_cnt,
                bf_friend_profile_uu
        ],
        name="best_friends_action",
        bq_dialect='standard'

    )
    return mt


def recent_searches_action(start_date, end_date):
    """
    Recent Searches Section Interaction
    """

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]


    rs_friend_profile_cnt = Metric(
        'rs_friend_profile_cnt',
        'Recents: Friend Profile Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    rs_friend_profile_uu = Metric(
        'rs_friend_profile_uu',
        'Recents: Friend Profile Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    rs_clear_all_cnt = Metric(
        'rs_clear_all_cnt',
        'Recents: Clear All - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    rs_clear_all_uu = Metric(
        'rs_clear_all_uu',
        'Recents: Clear All - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )


    rs_chat_view_cnt = Metric(
        'rs_chat_view_cnt',
        'Recents: Chat View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    rs_chat_view_uu = Metric(
        'rs_chat_view_uu',
        'Recents: Chat View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    rs_user_story_cnt = Metric(
        'rs_user_story_cnt',
        'Recents: Creator Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    rs_user_story_uu = Metric(
        'rs_user_story_uu',
        'Recents: Creator Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )


    rs_friend_story_cnt = Metric(
        'rs_friend_story_cnt',
        'Recents: Friend Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    rs_friend_story_uu = Metric(
        'rs_friend_story_uu',
        'Recents: Friend Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    rs_action_sheet_cnt = Metric(
        'rs_action_sheet_cnt',
        'Recents: Action Sheet Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    rs_action_sheet_uu = Metric(
        'rs_action_sheet_uu',
        'Recents: Action Sheet Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    rs_add_friend_cnt = Metric(
        'rs_add_friend_cnt',
        'Recents: Add Friend - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    rs_unlock_lens_cnt = Metric(
        'rs_unlock_lens_cnt',
        'Recents: Unlock Lens - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    rs_add_friend_uu = Metric(
        'rs_add_friend_uu',
        'Recents: Add Friend - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    rs_unlock_lens_uu = Metric(
        'rs_unlock_lens_uu',
        'Recents: Unlock Lens - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    mt = MetricTable(
        sql="""
        SELECT
          event_date AS ts,
          ghost_user_id AS ghost_user_id,

          sum(case when action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) rs_friend_profile_cnt,
          IF(sum(case when action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) rs_friend_profile_uu,

          sum(case when action='OPEN_RECENTS_CLEAR_ALL_DIALOG' then 1 else 0 end) rs_clear_all_cnt,
          IF(sum(case when action='OPEN_RECENTS_CLEAR_ALL_DIALOG' then 1 else 0 end)>0,1,0) rs_clear_all_uu,

          sum(case when action='OPEN_CHAT_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) rs_chat_view_cnt,
          IF(sum(case when action='OPEN_CHAT_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) rs_chat_view_uu,

          sum(case when action='OPEN_USER_STORY_FROM_SEARCH_RESULT' then 1 else 0 end) rs_user_story_cnt,
          IF(sum(case when action='OPEN_USER_STORY_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) rs_user_story_uu,

          sum(case when action='OPEN_FRIEND_STORY_FROM_SEARCH_RESULT' then 1 else 0 end) rs_friend_story_cnt,
          IF(sum(case when action='OPEN_FRIEND_STORY_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) rs_friend_story_uu,

          sum(case when action='OPEN_UNIFIED_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) rs_action_sheet_cnt,
          IF(sum(case when action='OPEN_UNIFIED_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) rs_action_sheet_uu,

          sum(case when action='ADD_FRIEND_FROM_SEARCH_RESULT' then 1 else 0 end) rs_add_friend_cnt,
          IF(sum(case when action='ADD_FRIEND_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) rs_add_friend_uu,

          sum(case when action='UNLOCK_LENS' then 1 else 0 end) rs_unlock_lens_cnt,
          IF(sum(case when action='UNLOCK_LENS' then 1 else 0 end)>0,1,0) rs_unlock_lens_uu,

        FROM `sc-analytics.report_search_v2.search_action_20*`
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND
            _TABLE_SUFFIX <= '{end_trunc}'
        AND search_query_context not in ('MAPS_SCREEN_FRIEND_FINDER','EAGLE_SEARCH')
        AND source='SEARCH_UNSPECIFIED'
        AND search_result_section='RECENT_SEARCHES'
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            rs_friend_profile_cnt,
            rs_clear_all_cnt,
            rs_chat_view_cnt,
            rs_user_story_cnt,
            rs_friend_story_cnt,
            rs_action_sheet_cnt,
            rs_add_friend_cnt,
            rs_unlock_lens_cnt,
            rs_friend_profile_uu,
            rs_clear_all_uu,
            rs_chat_view_uu,
            rs_user_story_uu,
            rs_friend_story_uu,
            rs_action_sheet_uu,
            rs_add_friend_uu,
            rs_unlock_lens_uu,
        ],
        name="recent_searches_action",
        bq_dialect='standard'

    )
    return mt


def quick_add_action(start_date, end_date):
    """
    Quick Add Section Interaction
    """

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]


    qa_friend_profile_cnt = Metric(
        'qa_friend_profile_cnt',
        'Quick Add: Friend Profile Open Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )


    qa_add_friend_cnt = Metric(
        'qa_add_friend_cnt',
        'Quick Add: Add Friend Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    qa_action_sheet_cnt = Metric(
        'qa_action_sheet_cnt',
        'Quick Add: Action Sheet Open Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    qa_chat_view_cnt = Metric(
        'qa_chat_view_cnt',
        'Quick Add: Chat View Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )



    qa_friend_profile_uu = Metric(
        'qa_friend_profile_uu',
        'Quick Add: Unique Friend Profile Open User',
        dist='bin',
        daily=True,
        cumulative=True,
    )


    qa_add_friend_uu = Metric(
        'qa_add_friend_uu',
        'Quick Add: Unique Add Friend User',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    qa_action_sheet_uu = Metric(
        'qa_action_sheet_uu',
        'Quick Add: Unique Action Sheet Open User',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    qa_chat_view_uu = Metric(
        'qa_chat_view_uu',
        'Quick Add: Unique Chat View User',
        dist='bin',
        daily=True,
        cumulative=True,
    )


    mt = MetricTable(
        sql="""
        SELECT
          event_date AS ts,
          ghost_user_id AS ghost_user_id,

          sum(case when action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) qa_friend_profile_cnt,
          IF(sum(case when action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) qa_friend_profile_uu,

          sum(case when action='ADD_FRIEND_FROM_SEARCH_RESULT' then 1 else 0 end) qa_add_friend_cnt,
          IF(sum(case when action='ADD_FRIEND_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) qa_add_friend_uu,

          sum(case when action='OPEN_UNIFIED_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) qa_action_sheet_cnt,
          IF(sum(case when action='OPEN_UNIFIED_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) qa_action_sheet_uu,

          sum(case when action='OPEN_CHAT_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) qa_chat_view_cnt,
          IF(sum(case when action='OPEN_CHAT_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) qa_chat_view_uu

        FROM `sc-analytics.report_search_v2.search_action_20*`
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND
            _TABLE_SUFFIX <= '{end_trunc}'
        AND search_query_context not in ('MAPS_SCREEN_FRIEND_FINDER','EAGLE_SEARCH')
        AND source='SEARCH_UNSPECIFIED'
        AND search_result_section='QUICK_ADD'
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            qa_friend_profile_cnt,
            qa_add_friend_cnt,
            qa_action_sheet_cnt,
            qa_chat_view_cnt,
            qa_friend_profile_uu,
            qa_add_friend_uu,
            qa_action_sheet_uu,
            qa_chat_view_uu
        ],
        name="quick_add_action",
        bq_dialect='standard'

    )
    return mt


def friends_and_groups_action(start_date, end_date):
    """
    Quick Add Section Interaction
    """

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]


    faq_chat_view_cnt = Metric(
        'faq_chat_view_cnt',
        'Friends & Groups: Chat View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    faq_friend_story_cnt = Metric(
        'faq_friend_story_cnt',
        'Friends & Groups: Friend Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    faq_friend_profile_cnt = Metric(
        'faq_friend_profile_cnt',
        'Friends & Groups: Friend Profile Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    faq_action_sheet_cnt = Metric(
        'faq_action_sheet_cnt',
        'Friends & Groups: Action Sheet Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    faq_snap_view_cnt = Metric(
        'faq_snap_view_cnt',
        'Friends & Groups: Open Snap View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    faq_map_view_cnt = Metric(
        'faq_map_view_cnt',
        'Friends & Groups: Map View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )




    faq_chat_view_uu = Metric(
        'faq_chat_view_uu',
        'Friends & Groups: Chat View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    faq_friend_story_uu = Metric(
        'faq_friend_story_uu',
        'Friends & Groups: Friend Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    faq_friend_profile_uu = Metric(
        'faq_friend_profile_uu',
        'Friends & Groups: Friend Profile Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    faq_action_sheet_uu = Metric(
        'faq_action_sheet_uu',
        'Friends & Groups: Action Sheet Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    faq_snap_view_uu = Metric(
        'faq_snap_view_uu',
        'Friends & Groups: Open Snap View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    faq_map_view_uu = Metric(
        'faq_map_view_uu',
        'Friends & Groups: Map View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )


    mt = MetricTable(
        sql="""
        SELECT
          event_date AS ts,
          ghost_user_id AS ghost_user_id,

          sum(case when action='OPEN_CHAT_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) faq_chat_view_cnt,
          IF(sum(case when action='OPEN_CHAT_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) faq_chat_view_uu,

          sum(case when action='OPEN_FRIEND_STORY_FROM_SEARCH_RESULT' then 1 else 0 end) faq_friend_story_cnt,
          IF(sum(case when action='OPEN_FRIEND_STORY_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) faq_friend_story_uu,

          sum(case when action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) faq_friend_profile_cnt,
          IF(sum(case when action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) faq_friend_profile_uu,

          sum(case when action='OPEN_UNIFIED_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) faq_action_sheet_cnt,
          IF(sum(case when action='OPEN_UNIFIED_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) faq_action_sheet_uu,

          sum(case when action='OPEN_SNAP_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) faq_snap_view_cnt,
          IF(sum(case when action='OPEN_SNAP_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) faq_snap_view_uu,

          sum(case when action='OPEN_MAP_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) faq_map_view_cnt,
          IF(sum(case when action='OPEN_MAP_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) faq_map_view_uu

        FROM `sc-analytics.report_search_v2.search_action_20*`
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND
            _TABLE_SUFFIX <= '{end_trunc}'
        AND search_query_context not in ('MAPS_SCREEN_FRIEND_FINDER','EAGLE_SEARCH')
        AND source='SEARCH_UNSPECIFIED'
        AND search_result_section='FRIENDS_AND_GROUPS'
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            faq_chat_view_cnt,
            faq_friend_story_cnt,
            faq_friend_profile_cnt,
            faq_action_sheet_cnt,
            faq_snap_view_cnt,
            faq_map_view_cnt,
            faq_chat_view_uu,
            faq_friend_story_uu,
            faq_friend_profile_uu,
            faq_action_sheet_uu,
            faq_snap_view_uu,
            faq_map_view_uu

        ],
        name="friends_and_groups_action",
        bq_dialect='standard'

    )
    return mt



def add_friend_action(start_date, end_date):
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]


    af_add_friend_cnt = Metric(
        'af_add_friend_cnt',
        'Add Friends: Add Friend - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    af_friend_profile_cnt = Metric(
        'af_friend_profile_cnt',
        'Add Friends: Profile Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    af_chat_view_cnt = Metric(
        'af_chat_view_cnt',
        'Add Friends: Chat View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    af_action_sheet_cnt = Metric(
        'af_action_sheet_cnt',
        'Add Friends: Action Sheet Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )


    af_add_friend_uu = Metric(
        'af_add_friend_uu',
        'Add Friends: Add Friend - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    af_friend_profile_uu = Metric(
        'af_friend_profile_uu',
        'Add Friends: Profile Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    af_chat_view_uu = Metric(
        'af_chat_view_uu',
        'Add Friends: Chat View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    af_action_sheet_uu = Metric(
        'af_action_sheet_uu',
        'Add Friends: Action Sheet Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )


    mt = MetricTable(
        sql="""
        SELECT
          event_date AS ts,
          ghost_user_id AS ghost_user_id,

          sum(case when action='ADD_FRIEND_FROM_SEARCH_RESULT' then 1 else 0 end) af_add_friend_cnt,
          IF(sum(case when action='ADD_FRIEND_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) af_add_friend_uu,

          sum(case when action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) af_friend_profile_cnt,
          IF(sum(case when action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) af_friend_profile_uu,

          sum(case when action='OPEN_CHAT_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) af_chat_view_cnt,
          IF(sum(case when action='OPEN_CHAT_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) af_chat_view_uu,

          sum(case when action='OPEN_UNIFIED_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) af_action_sheet_cnt,
          IF(sum(case when action='OPEN_UNIFIED_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) af_action_sheet_uu

        FROM `sc-analytics.report_search_v2.search_action_20*`
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND
            _TABLE_SUFFIX <= '{end_trunc}'
        AND source='SEARCH_UNSPECIFIED'
        AND search_result_section='ADD_A_FRIEND'
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            af_add_friend_cnt,
            af_friend_profile_cnt,
            af_chat_view_cnt,
            af_action_sheet_cnt,
            af_add_friend_uu,
            af_friend_profile_uu,
            af_chat_view_uu,
            af_action_sheet_uu
        ],
        name="add_friend_action",
        bq_dialect='standard'

    )
    return mt


def add_friend_action_non_universal(start_date, end_date):
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]


    metadata = [
        ('all_af_add_friend_cnt', 'All Add Friends: Add Friend - Count'),
        ('all_af_add_friend_uu', 'All Add Friends: Add Friend - UU'),
        ('all_af_friend_profile_cnt', 'All Add Friends: Profile Open - Count'),
        ('all_af_friend_profile_uu', 'All Add Friends: Profile Open - UU'),
        ('all_af_chat_view_cnt', 'All Add Friends: Chat View - Count'),
        ('all_af_chat_view_uu', 'All Add Friends: Chat View - UU'),
        ('all_af_action_sheet_cnt', 'All Add Friends: Action Sheet Open - Count'),
        ('all_af_action_sheet_uu', 'All Add Friends: Action Sheet Open - UU'),


        ('afp_af_add_friend_cnt', 'AddFrPg Add Friends: Add Friend - Count'),
        ('afp_af_add_friend_uu', 'AddFrPg Add Friends: Add Friend - UU'),
        ('afp_af_friend_profile_cnt', 'AddFrPg Add Friends: Profile Open - Count'),
        ('afp_af_friend_profile_uu', 'AddFrPg Add Friends: Profile Open - UU'),
        ('afp_af_chat_view_cnt', 'AddFrPg Add Friends: Chat View - Count'),
        ('afp_af_chat_view_uu', 'AddFrPg Add Friends: Chat View - UU'),
        ('afp_af_action_sheet_cnt', 'AddFrPg Add Friends: Action Sheet Open - Count'),
        ('afp_af_action_sheet_uu', 'AddFrPg Add Friends: Action Sheet Open - UU'),
    ]


    metrics_list = []

    for (metric, name) in metadata:
        curr_metric = Metric(
            metric,
            name,
            dist='cont',
            daily=True,
            cumulative=True,
        )
        metrics_list.append(curr_metric)


    mt = MetricTable(
        sql="""
        SELECT
            event_date AS ts,
            ghost_user_id AS ghost_user_id,

            COUNTIF(action='ADD_FRIEND_FROM_SEARCH_RESULT') all_af_add_friend_cnt,
            IF(COUNTIF(action='ADD_FRIEND_FROM_SEARCH_RESULT')>0,1,0) all_af_add_friend_uu,

            COUNTIF(action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT') all_af_friend_profile_cnt,
            IF(COUNTIF(action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT')>0,1,0) all_af_friend_profile_uu,

            COUNTIF(action='OPEN_CHAT_VIEW_FROM_SEARCH_RESULT') all_af_chat_view_cnt,
            IF(COUNTIF(action='OPEN_CHAT_VIEW_FROM_SEARCH_RESULT')>0,1,0) all_af_chat_view_uu,

            COUNTIF(action='OPEN_UNIFIED_PROFILE_VIEW_FROM_SEARCH_RESULT') all_af_action_sheet_cnt,
            IF(COUNTIF(action='OPEN_UNIFIED_PROFILE_VIEW_FROM_SEARCH_RESULT')>0,1,0) all_af_action_sheet_uu,


            COUNTIF(source='ADD_FRIENDS_PAGE' and action='ADD_FRIEND_FROM_SEARCH_RESULT') afp_af_add_friend_cnt,
            IF(COUNTIF(source='ADD_FRIENDS_PAGE' and action='ADD_FRIEND_FROM_SEARCH_RESULT')>0,1,0) afp_af_add_friend_uu,

            COUNTIF(source='ADD_FRIENDS_PAGE' and action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT') afp_af_friend_profile_cnt,
            IF(COUNTIF(source='ADD_FRIENDS_PAGE' and action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT')>0,1,0) afp_af_friend_profile_uu,

            COUNTIF(source='ADD_FRIENDS_PAGE' and action='OPEN_CHAT_VIEW_FROM_SEARCH_RESULT') afp_af_chat_view_cnt,
            IF(COUNTIF(source='ADD_FRIENDS_PAGE' and action='OPEN_CHAT_VIEW_FROM_SEARCH_RESULT')>0,1,0) afp_af_chat_view_uu,

            COUNTIF(source='ADD_FRIENDS_PAGE' and action='OPEN_UNIFIED_PROFILE_VIEW_FROM_SEARCH_RESULT') afp_af_action_sheet_cnt,
            IF(COUNTIF(source='ADD_FRIENDS_PAGE' and action='OPEN_UNIFIED_PROFILE_VIEW_FROM_SEARCH_RESULT')>0,1,0) afp_af_action_sheet_uu,

        FROM `sc-analytics.report_search_v2.search_action_20*`
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND
            _TABLE_SUFFIX <= '{end_trunc}'
        AND search_result_section='ADD_A_FRIEND'
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=metrics_list,
        name="add_friend_action_non_universal",
        bq_dialect='standard'

    )
    return mt



def top_results_action(start_date, end_date):
    """
    Top Results Section Interaction
    """

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    tr_user_story_cnt = Metric(
        'tr_user_story_cnt',
        'Top Results: User Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    tr_user_saved_story_cnt = Metric(
        'tr_user_saved_story_cnt',
        'Top Results: User Saved Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    tr_open_discover_article_cnt = Metric(
        'tr_open_discover_article_cnt',
        'Top Results: Discover Article Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    tr_unlock_lens_cnt = Metric(
        'tr_unlock_lens_cnt',
        'Top Results: Unlock Lens - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    tr_friend_profile_cnt = Metric(
        'tr_friend_profile_cnt',
        'Top Results: Friend Profile Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )


    tr_user_story_uu = Metric(
        'tr_user_story_uu',
        'Top Results: User Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    tr_user_saved_story_uu = Metric(
        'tr_user_saved_story_uu',
        'Top Results: User Saved Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    tr_open_discover_article_uu = Metric(
        'tr_open_discover_article_uu',
        'Top Results: Discover Article Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )
    tr_unlock_lens_uu = Metric(
        'tr_unlock_lens_uu',
        'Top Results: Unlock Lens - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )
    tr_friend_profile_uu = Metric(
        'tr_friend_profile_uu',
        'Top Results: Friend Profile Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )


    tr_open_publisher_view_cnt = Metric(
        'tr_open_publisher_view_cnt',
        'Top Results: Publisher View Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    tr_topic_open_cnt = Metric(
        'tr_topic_open_cnt',
        'Top Results: Topic Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    tr_venue_open_cnt = Metric(
        'tr_venue_open_cnt',
        'Top Results: Venue Profile Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    tr_add_verified_cnt = Metric(
        'tr_add_verified_cnt',
        'Top Results: Add Verified User - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )


    tr_open_publisher_view_uu = Metric(
        'tr_open_publisher_view_uu',
        'Top Results: Publisher View Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )
    tr_topic_open_uu = Metric(
        'tr_topic_open_uu',
        'Top Results: Topic Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )
    tr_venue_open_uu = Metric(
        'tr_venue_open_uu',
        'Top Results: Venue Profile Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )
    tr_add_verified_uu = Metric(
        'tr_add_verified_uu',
        'Top Results: Add Verified User - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    mt = MetricTable(
        sql="""
        SELECT
          event_date AS ts,
          ghost_user_id AS ghost_user_id,

          sum(case when action='OPEN_USER_STORY_FROM_SEARCH_RESULT' then 1 else 0 end) tr_user_story_cnt,
          sum(case when action='OPEN_USER_SAVED_STORY_FROM_SEARCH_RESULT' then 1 else 0 end) tr_user_saved_story_cnt,
          sum(case when action='OPEN_DISCOVER_ARTICLE_FROM_SEARCH_RESULT' then 1 else 0 end) tr_open_discover_article_cnt,
          sum(case when action='UNLOCK_LENS' then 1 else 0 end) tr_unlock_lens_cnt,
          sum(case when action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) tr_friend_profile_cnt,

          sum(case when action='OPEN_PUBLISHER_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) tr_open_publisher_view_cnt,
          sum(case when action='OPEN_TOPIC_PAGE' then 1 else 0 end) tr_topic_open_cnt,
          sum(case when action='OPEN_VENUE_PROFILE' then 1 else 0 end) tr_venue_open_cnt,
          sum(case when action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' then 1 else 0 end) tr_add_verified_cnt,


          IF(sum(case when action='OPEN_USER_STORY_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) tr_user_story_uu,
          IF(SUM(case when action='OPEN_USER_SAVED_STORY_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) tr_user_saved_story_uu,
          IF(sum(case when action='OPEN_DISCOVER_ARTICLE_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) tr_open_discover_article_uu,
          IF(sum(case when action='UNLOCK_LENS' then 1 else 0 end)>0,1,0) tr_unlock_lens_uu,
          IF(sum(case when action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) tr_friend_profile_uu,

          IF(sum(case when action='OPEN_PUBLISHER_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) tr_open_publisher_view_uu,
          IF(sum(case when action='OPEN_TOPIC_PAGE' then 1 else 0 end)>0,1,0) tr_topic_open_uu,
          IF(sum(case when action='OPEN_VENUE_PROFILE' then 1 else 0 end)>0,1,0) tr_venue_open_uu,
          IF(sum(case when action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) tr_add_verified_uu


        FROM `sc-analytics.report_search_v2.search_action_20*`
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND
            _TABLE_SUFFIX <= '{end_trunc}'
        AND search_query_context not in ('MAPS_SCREEN_FRIEND_FINDER','EAGLE_SEARCH')
        AND source='SEARCH_UNSPECIFIED'
        AND search_result_section='TOP_RESULTS'
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            tr_user_story_cnt,
            tr_user_saved_story_cnt,
            tr_open_discover_article_cnt,
            tr_unlock_lens_cnt,
            tr_friend_profile_cnt,
            tr_user_story_uu,
            tr_user_saved_story_uu,
            tr_open_discover_article_uu,
            tr_unlock_lens_uu,
            tr_friend_profile_uu,

            tr_open_publisher_view_cnt,
            tr_topic_open_cnt,
            tr_venue_open_cnt,
            tr_add_verified_cnt,
            tr_open_publisher_view_uu,
            tr_topic_open_uu,
            tr_venue_open_uu,
            tr_add_verified_uu
        ],
        name="top_results_action",
        bq_dialect='standard'

    )
    return mt

def lens_action(start_date, end_date):
    """
    Top Results Section Interaction
    """

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]


    lens_unlock_lens_cnt = Metric(
        'lens_unlock_lens_cnt',
        'Lenses: Unlock Lens - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    lens_open_lens_info_cnt = Metric(
        'lens_open_lens_info_cnt',
        'Lenses: Lens Info Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    lens_unlock_lens_uu = Metric(
        'lens_unlock_lens_uu',
        'Lenses: Unlock Lens - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    lens_open_lens_info_uu = Metric(
        'lens_open_lens_info_uu',
        'Lenses: Lens Info Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    mt = MetricTable(
        sql="""
        SELECT
          event_date AS ts,
          ghost_user_id AS ghost_user_id,

          sum(case when action='UNLOCK_LENS' then 1 else 0 end) lens_unlock_lens_cnt,
          sum(case when action='OPEN_LENS_INFO_CARD' then 1 else 0 end) lens_open_lens_info_cnt,
          IF(sum(case when action='UNLOCK_LENS' then 1 else 0 end)>0,1,0) lens_unlock_lens_uu,
          IF(sum(case when action='OPEN_LENS_INFO_CARD' then 1 else 0 end)>0,1,0) lens_open_lens_info_uu

        FROM `sc-analytics.report_search_v2.search_action_20*`
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND
            _TABLE_SUFFIX <= '{end_trunc}'
        AND search_query_context not in ('MAPS_SCREEN_FRIEND_FINDER','EAGLE_SEARCH')
        AND source='SEARCH_UNSPECIFIED'
        AND search_result_section='COMMUNITY_LENSES'
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            lens_unlock_lens_cnt,
            lens_open_lens_info_cnt,
            lens_unlock_lens_uu,
            lens_open_lens_info_uu
        ],
        name="lens_action",
        bq_dialect='standard'

    )
    return mt

def lens_action_posttype(start_date, end_date):

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]


    lens_unlock_lens_cnt_universal = Metric(
        'lens_unlock_lens_cnt_universal',
        'UnivSrch Posttype: Unlock Lens - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    lens_open_lens_info_cnt_universal = Metric(
        'lens_open_lens_info_cnt_universal',
        'UnivSrch Posttype: Lens Info Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    lens_unlock_lens_uu_universal = Metric(
        'lens_unlock_lens_uu_universal',
        'UnivSrch Posttype: Unlock Lens - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    lens_open_lens_info_uu_universal = Metric(
        'lens_open_lens_info_uu_universal',
        'UnivSrch Posttype: Lens Info Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    lens_unlock_lens_cnt_le = Metric(
        'lens_unlock_lens_cnt_le',
        'LensExp Posttype: Unlock Lens - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    lens_open_lens_info_cnt_le = Metric(
        'lens_open_lens_info_cnt_le',
        'LensExp Posttype: Lens Info Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    lens_unlock_lens_uu_le = Metric(
        'lens_unlock_lens_uu_le',
        'LensExp Posttype: Unlock Lens - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    lens_open_lens_info_uu_le = Metric(
        'lens_open_lens_info_uu_le',
        'LensExp Posttype: Lens Info Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    mt = MetricTable(
        sql="""
        SELECT
          event_date AS ts,
          ghost_user_id AS ghost_user_id,

          sum(case when source='SEARCH_UNSPECIFIED' and action='UNLOCK_LENS' then 1 else 0 end) lens_unlock_lens_cnt_universal,
          sum(case when source='SEARCH_UNSPECIFIED' and action='OPEN_LENS_INFO_CARD' then 1 else 0 end) lens_open_lens_info_cnt_universal,
          IF(sum(case when source='SEARCH_UNSPECIFIED' and action='UNLOCK_LENS' then 1 else 0 end)>0,1,0) lens_unlock_lens_uu_universal,
          IF(sum(case when source='SEARCH_UNSPECIFIED' and action='OPEN_LENS_INFO_CARD' then 1 else 0 end)>0,1,0) lens_open_lens_info_uu_universal,

          sum(case when source='LE_SEARCH' and action='UNLOCK_LENS' then 1 else 0 end) lens_unlock_lens_cnt_le,
          sum(case when source='LE_SEARCH' and action='OPEN_LENS_INFO_CARD' then 1 else 0 end) lens_open_lens_info_cnt_le,
          IF(sum(case when source='LE_SEARCH' and action='UNLOCK_LENS' then 1 else 0 end)>0,1,0) lens_unlock_lens_uu_le,
          IF(sum(case when source='LE_SEARCH' and action='OPEN_LENS_INFO_CARD' then 1 else 0 end)>0,1,0) lens_open_lens_info_uu_le,


        FROM `sc-analytics.report_search_v2.search_action_20*`
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND
            _TABLE_SUFFIX <= '{end_trunc}'
        AND source in ('SEARCH_UNSPECIFIED', 'LE_SEARCH')
        AND search_result_section in ('COMMUNITY_LENSES', 'TOP_RESULTS')
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            lens_unlock_lens_cnt_universal,
            lens_open_lens_info_cnt_universal,
            lens_unlock_lens_uu_universal,
            lens_open_lens_info_uu_universal,
            lens_unlock_lens_cnt_le,
            lens_open_lens_info_cnt_le,
            lens_unlock_lens_uu_le,
            lens_open_lens_info_uu_le
        ],
        name="lens_action_posttype",
        bq_dialect='standard'

    )
    return mt

def lens_action_posttype_second_order(start_date, end_date):
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]


    has_snap_create_count_universal = Metric(
        'has_snap_create_count_universal',
        '(2nd order) UnivSrch Posttype: Snap Create - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    has_snap_save_count_universal = Metric(
        'has_snap_save_count_universal',
        '(2nd order) UnivSrch Posttype: Snap Save - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    has_snap_send_count_universal = Metric(
        'has_snap_send_count_universal',
        '(2nd order) UnivSrch Posttype: Snap Send - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    has_story_snap_post_count_universal = Metric(
        'has_story_snap_post_count_universal',
        '(2nd order) UnivSrch Posttype: Story Post - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    has_snap_save_send_post_count_universal = Metric(
        'has_snap_save_send_post_count_universal',
        '(2nd order) UnivSrch Posttype: Save/Send/Post - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    has_snap_create_count_le = Metric(
        'has_snap_create_count_le',
        '(2nd order) LensExp Posttype: Snap Create - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    has_snap_save_count_le = Metric(
        'has_snap_save_count_le',
        '(2nd order) LensExp Posttype: Snap Save - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    has_snap_send_count_le = Metric(
        'has_snap_send_count_le',
        '(2nd order) LensExp Posttype: Snap Send - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    has_story_snap_post_count_le = Metric(
        'has_story_snap_post_count_le',
        '(2nd order) LensExp Posttype: Story Post - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    has_snap_save_send_post_count_le = Metric(
        'has_snap_save_send_post_count_le',
        '(2nd order) LensExp Posttype: Snap Save/Send/Post - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    mt = MetricTable(
        sql="""
        SELECT
          PARSE_TIMESTAMP('%Y%m%d', concat('20', _TABLE_SUFFIX), 'UTC') AS ts,
          ghost_user_id AS ghost_user_id,

          sum(if(source='SEARCH_UNSPECIFIED', has_snap_create, 0)) has_snap_create_count_universal,
          sum(if(source='SEARCH_UNSPECIFIED', has_snap_save, 0)) has_snap_save_count_universal,
          sum(if(source='SEARCH_UNSPECIFIED', has_snap_send, 0)) has_snap_send_count_universal,
          sum(if(source='SEARCH_UNSPECIFIED', has_story_snap_post, 0)) has_story_snap_post_count_universal,
          sum(if(source='SEARCH_UNSPECIFIED' and (has_snap_save+has_snap_send+has_story_snap_post>0), 1, 0)) has_snap_save_send_post_count_universal,

          sum(if(source='LE_SEARCH', has_snap_create, 0)) has_snap_create_count_le,
          sum(if(source='LE_SEARCH', has_snap_save, 0)) has_snap_save_count_le,
          sum(if(source='LE_SEARCH', has_snap_send, 0)) has_snap_send_count_le,
          sum(if(source='LE_SEARCH', has_story_snap_post, 0)) has_story_snap_post_count_le,
          sum(if(source='LE_SEARCH' and (has_snap_save+has_snap_send+has_story_snap_post>0), 1, 0)) has_snap_save_send_post_count_le,


        FROM `sc-analytics.report_search_v2.search_to_lens_open_20*`
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND
            _TABLE_SUFFIX <= '{end_trunc}'
        AND search_result_section IN ('COMMUNITY_LENSES', 'TOP_RESULTS')
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            has_snap_create_count_universal,
            has_snap_save_count_universal,
            has_snap_send_count_universal,
            has_story_snap_post_count_universal,
            has_snap_save_send_post_count_universal,
            has_snap_create_count_le,
            has_snap_save_count_le,
            has_snap_send_count_le,
            has_story_snap_post_count_le,
            has_snap_save_send_post_count_le,
        ],
        name="lens_action_posttype_second_order",
        bq_dialect='standard'

    )
    return mt

def lens_action_pretype(start_date, end_date):
    """
    Lens Pretype Section Interaction
    """

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    lens_unlock_lens_cnt_pretype = Metric(
        'lens_unlock_lens_cnt_pretype',
        'Lenses: Unlock Lens - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )


    lens_open_lens_info_cnt_pretype = Metric(
        'lens_open_lens_info_cnt_pretype',
        'Lenses: Lens Info Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    lens_unlock_lens_uu_pretype = Metric(
        'lens_unlock_lens_uu_pretype',
        'Lenses: Unlock Lens - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )


    lens_open_lens_info_uu_pretype = Metric(
        'lens_open_lens_info_uu_pretype',
        'Lenses: Lens Info Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    mt = MetricTable(
        sql="""
        SELECT
          event_date AS ts,
          ghost_user_id AS ghost_user_id,

          sum(case when action='UNLOCK_LENS' then 1 else 0 end) lens_unlock_lens_cnt_pretype,
          sum(case when action='OPEN_LENS_INFO_CARD' then 1 else 0 end) lens_open_lens_info_cnt_pretype,
          IF(sum(case when action='UNLOCK_LENS' then 1 else 0 end)>0,1,0) lens_unlock_lens_uu_pretype,
          IF(sum(case when action='OPEN_LENS_INFO_CARD' then 1 else 0 end)>0,1,0) lens_open_lens_info_uu_pretype

        FROM `sc-analytics.report_search_v2.search_action_20*`
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND
            _TABLE_SUFFIX <= '{end_trunc}'
        AND search_query_context not in ('MAPS_SCREEN_FRIEND_FINDER','EAGLE_SEARCH')
        AND source='SEARCH_UNSPECIFIED'
        AND search_result_section='LENS_PRETYPE'
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            lens_unlock_lens_cnt_pretype,
            lens_open_lens_info_cnt_pretype,
            lens_unlock_lens_uu_pretype,
            lens_open_lens_info_uu_pretype
        ],
        name="lens_action_pretype",
        bq_dialect='standard'

    )
    return mt





def show_publisher_action(start_date, end_date):
    """
    Show Publisher Section Interaction
    """

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    sp_open_publisher_view_cnt = Metric(
        'sp_open_publisher_view_cnt',
        'Show & Publisher: Publisher View Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    sp_open_publisher_view_uu = Metric(
        'sp_open_publisher_view_uu',
        'Show & Publisher: Publisher View Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    mt = MetricTable(
        sql="""
        SELECT
          event_date AS ts,
          ghost_user_id AS ghost_user_id,
          sum(case when action='OPEN_PUBLISHER_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) sp_open_publisher_view_cnt,
          IF(sum(case when action='OPEN_PUBLISHER_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) sp_open_publisher_view_uu,
        FROM `sc-analytics.report_search_v2.search_action_20*`
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND
            _TABLE_SUFFIX <= '{end_trunc}'
        AND search_query_context not in ('MAPS_SCREEN_FRIEND_FINDER','EAGLE_SEARCH')
        AND source='SEARCH_UNSPECIFIED'
        AND search_result_section='SHOWS_AND_PUBLISHERS'
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            sp_open_publisher_view_cnt,
            sp_open_publisher_view_uu,
        ],
        name="show_publisher_action",
        bq_dialect='standard'

    )
    return mt


def show_publisher_action_pretype(start_date, end_date):
    """
    Show Publisher Section Interaction in Pretype
    """

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]


    sp_open_publisher_view_cnt_pretype = Metric(
        'sp_open_publisher_view_cnt_pretype',
        'Show & Publisher: Publisher View Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    sp_open_publisher_view_uu_pretype = Metric(
        'sp_open_publisher_view_uu_pretype',
        'Show & Publisher: Publisher View Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )


    mt = MetricTable(
        sql="""
        SELECT
          event_date AS ts,
          ghost_user_id AS ghost_user_id,
          sum(case when action='OPEN_PUBLISHER_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) sp_open_publisher_view_cnt_pretype,
          IF(sum(case when action='OPEN_PUBLISHER_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) sp_open_publisher_view_uu_pretype,
        FROM `sc-analytics.report_search_v2.search_action_20*`
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND
            _TABLE_SUFFIX <= '{end_trunc}'
        AND search_query_context not in ('MAPS_SCREEN_FRIEND_FINDER','EAGLE_SEARCH')
        AND source='SEARCH_UNSPECIFIED'
        AND search_result_section='PUBLISHERS_PRETYPE'
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            sp_open_publisher_view_cnt_pretype,
            sp_open_publisher_view_uu_pretype,
        ],
        name="show_publisher_action_pretype",
        bq_dialect='standard'

    )
    return mt





def places_action(start_date, end_date):
    """
    Places Section Interaction
    """

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]


    p_open_venue_cnt = Metric(
        'p_open_venue_cnt',
        'Places: Venue Profile Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    p_open_place_story_cnt = Metric(
        'p_open_place_story_cnt',
        'Places: Place Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    p_open_venue_uu = Metric(
        'p_open_venue_uu',
        'Places: Venue Profile Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    p_open_place_story_uu = Metric(
        'p_open_place_story_uu',
        'Places: Place Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )
    mt = MetricTable(
        sql="""
        SELECT
          event_date AS ts,
          ghost_user_id AS ghost_user_id,
          sum(case when action='OPEN_VENUE_PROFILE' then 1 else 0 end) p_open_venue_cnt,
          sum(case when action='OPEN_PLACE_STORY_FROM_SEARCH_RESULT' then 1 else 0 end) p_open_place_story_cnt,
          IF(sum(case when action='OPEN_VENUE_PROFILE' then 1 else 0 end)>0,1,0) p_open_venue_uu,
          IF(sum(case when action='OPEN_PLACE_STORY_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) p_open_place_story_uu,
        FROM `sc-analytics.report_search_v2.search_action_20*`
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND
            _TABLE_SUFFIX <= '{end_trunc}'
        AND search_query_context not in ('MAPS_SCREEN_FRIEND_FINDER','EAGLE_SEARCH')
        AND source='SEARCH_UNSPECIFIED'
        AND search_result_section='PLACES'
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            p_open_venue_cnt,
            p_open_place_story_cnt,
            p_open_venue_uu,
            p_open_place_story_uu
        ],
        name="places_action",
        bq_dialect='standard'

    )
    return mt



def places_action_pretype(start_date, end_date):
    """
    Places Pretype Section Interaction
    """

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    p_open_venue_cnt_pretype = Metric(
        'p_open_venue_cnt_pretype',
        'Places: Venue Profile Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    p_open_place_story_cnt_pretype = Metric(
        'p_open_place_story_cnt_pretype',
        'Places: Place Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    p_open_venue_uu_pretype = Metric(
        'p_open_venue_uu_pretype',
        'Places: Venue Profile Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    p_open_place_story_uu_pretype = Metric(
        'p_open_place_story_uu_pretype',
        'Places: Place Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )
    mt = MetricTable(
        sql="""
        SELECT
          event_date AS ts,
          ghost_user_id AS ghost_user_id,
          sum(case when action='OPEN_VENUE_PROFILE' then 1 else 0 end) p_open_venue_cnt_pretype,
          sum(case when action='OPEN_PLACE_STORY_FROM_SEARCH_RESULT' then 1 else 0 end) p_open_place_story_cnt_pretype,
          IF(sum(case when action='OPEN_VENUE_PROFILE' then 1 else 0 end)>0,1,0) p_open_venue_uu_pretype,
          IF(sum(case when action='OPEN_PLACE_STORY_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) p_open_place_story_uu_pretype,
        FROM `sc-analytics.report_search_v2.search_action_20*`
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND
            _TABLE_SUFFIX <= '{end_trunc}'
        AND search_query_context not in ('MAPS_SCREEN_FRIEND_FINDER','EAGLE_SEARCH')
        AND source='SEARCH_UNSPECIFIED'
        AND search_result_section='PLACES_PRETYPE'
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            p_open_venue_cnt_pretype,
            p_open_place_story_cnt_pretype,
            p_open_venue_uu_pretype,
            p_open_place_story_uu_pretype
        ],
        name="places_action_pretype",
        bq_dialect='standard'

    )
    return mt


def editions_episodes_action(start_date, end_date):
    """
    Editions Episodes Section Interaction
    """

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]


    ee_open_discover_article_cnt = Metric(
        'ee_open_discover_article_cnt',
        'Editions & Episodes: Discover Article Open Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    ee_open_publisher_view_cnt = Metric(
        'ee_open_publisher_view_cnt',
        'Editions & Episodes: Publisher View Open Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    ee_open_discover_article_uu = Metric(
        'ee_open_discover_article_uu',
        'Editions & Episodes: Unique Discover Article Open User',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    ee_open_publisher_view_uu = Metric(
        'ee_open_publisher_view_uu',
        'Editions & Episodes: Unique Publisher View Open User',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    mt = MetricTable(
        sql="""
        SELECT
          event_date AS ts,
          ghost_user_id AS ghost_user_id,
          sum(case when action='OPEN_DISCOVER_ARTICLE_FROM_SEARCH_RESULT' then 1 else 0 end) ee_open_discover_article_cnt,
          sum(case when action='OPEN_PUBLISHER_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) ee_open_publisher_view_cnt,
          IF(sum(case when action='OPEN_DISCOVER_ARTICLE_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) ee_open_discover_article_uu,
          IF(sum(case when action='OPEN_PUBLISHER_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) ee_open_publisher_view_uu
        FROM `sc-analytics.report_search_v2.search_action_20*`
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND
            _TABLE_SUFFIX <= '{end_trunc}'
        AND search_query_context not in ('MAPS_SCREEN_FRIEND_FINDER','EAGLE_SEARCH')
        AND source='SEARCH_UNSPECIFIED'
        AND search_result_section='EDITIONS_AND_EPISODES'
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            ee_open_discover_article_cnt,
            ee_open_publisher_view_cnt,
            ee_open_discover_article_uu,
            ee_open_publisher_view_uu
        ],
        name="editions_episodes_action",
        bq_dialect='standard'

    )
    return mt


def content_freshness_metrics(start_date, end_date):
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    search_spotlight_impression_0_1_days = Metric(
        'search_spotlight_impression_0_1_days',
        'Search Spotlight Impressions 0-1 days old',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    search_spotlight_impression_1_3_days = Metric(
        'search_spotlight_impression_1_3_days',
        'Search Spotlight Impressions 1-3 days old',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    search_spotlight_impression_3_7_days = Metric(
        'search_spotlight_impression_3_7_days',
        'Search Spotlight Impressions 3-7 days old',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    search_spotlight_impression_7_30_days = Metric(
        'search_spotlight_impression_7_30_days',
        'Search Spotlight Impressions 7-30 days old',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    search_spotlight_impression_30_90_days = Metric(
        'search_spotlight_impression_30_90_days',
        'Search Spotlight Impressions 30-90 days old',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    search_spotlight_impression_90_plus_days = Metric(
        'search_spotlight_impression_90_plus_days',
        'Search Spotlight Impressions 90+ days old',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    search_spotlight_impression_total = Metric(
        'search_spotlight_impression_total',
        'Total Search Spotlight Impressions',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    search_spotlight_impression_0_1_days_pct = Metric(
        'search_spotlight_impression_0_1_days_pct',
        '% of Search Spotlight Impressions 0-1 days old',
        numerator='search_spotlight_impression_0_1_days',
        denominator='search_spotlight_impression_total',
        dist='ratio',
    )

    search_spotlight_impression_1_3_days_pct = Metric(
        'search_spotlight_impression_1_3_days_pct',
        '% of Search Spotlight Impressions 1-3 days old',
        numerator='search_spotlight_impression_1_3_days',
        denominator='search_spotlight_impression_total',
        dist='ratio',
    )

    search_spotlight_impression_3_7_days_pct = Metric(
        'search_spotlight_impression_3_7_days_pct',
        '% of Search Spotlight Impressions 3-7 days old',
        numerator='search_spotlight_impression_3_7_days',
        denominator='search_spotlight_impression_total',
        dist='ratio',
    )

    search_spotlight_impression_7_30_days_pct = Metric(
        'search_spotlight_impression_7_30_days_pct',
        '% of Search Spotlight Impressions 7-30 days old',
        numerator='search_spotlight_impression_7_30_days',
        denominator='search_spotlight_impression_total',
        dist='ratio',
    )

    search_spotlight_impression_30_90_days_pct = Metric(
        'search_spotlight_impression_30_90_days_pct',
        '% of Search Spotlight Impressions 30-90 days old',
        numerator='search_spotlight_impression_30_90_days',
        denominator='search_spotlight_impression_total',
        dist='ratio',
    )

    search_spotlight_impression_90_plus_days_pct = Metric(
        'search_spotlight_impression_90_plus_days_pct',
        '% of Search Spotlight Impressions 90+ days old',
        numerator='search_spotlight_impression_90_plus_days',
        denominator='search_spotlight_impression_total',
        dist='ratio',
    )

    mt = MetricTable(
        sql="""
            SELECT
                ghost_user_id
                ,PARSE_TIMESTAMP('%Y%m%d',CONCAT('20',_TABLE_SUFFIX)) AS ts
                ,sum(if(days_since_creation >0 AND days_since_creation <=1, 1, 0)) 
                    as search_spotlight_impression_0_1_days
                ,sum(if(days_since_creation >1 AND days_since_creation <=3, 1, 0)) 
                    as search_spotlight_impression_1_3_days
                ,sum(if(days_since_creation >3 AND days_since_creation <=7, 1, 0)) 
                    as search_spotlight_impression_3_7_days
                ,sum(if(days_since_creation >7 AND days_since_creation <=30, 1, 0)) 
                    as search_spotlight_impression_7_30_days
                ,sum(if(days_since_creation >30 AND days_since_creation <=90, 1, 0)) 
                    as search_spotlight_impression_30_90_days
                ,sum(if(days_since_creation >90, 1, 0)) 
                    as search_spotlight_impression_90_plus_days
                ,count(*) as search_spotlight_impression_total
            FROM `sc-analytics.report_search_v2.search_spotlight_impression_20*`
            WHERE _TABLE_SUFFIX BETWEEN '{start_trunc}' AND '{end_trunc}' 
            GROUP BY 1, 2
            """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
        ),
        metrics=[
            search_spotlight_impression_0_1_days,
            search_spotlight_impression_0_1_days_pct,
            search_spotlight_impression_1_3_days,
            search_spotlight_impression_1_3_days_pct,
            search_spotlight_impression_3_7_days,
            search_spotlight_impression_3_7_days_pct,
            search_spotlight_impression_7_30_days,
            search_spotlight_impression_7_30_days_pct,
            search_spotlight_impression_30_90_days,
            search_spotlight_impression_30_90_days_pct,
            search_spotlight_impression_90_plus_days,
            search_spotlight_impression_90_plus_days_pct,
            search_spotlight_impression_total
        ],
        name="content_freshness_metrics",
        bq_dialect='standard'
    )

    return mt

def creator_metrics(start_date, end_date):

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    fo_creator_action_count = Metric(
        'fo_creator_action_count',
        'Creator Actions - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    fo_creator_profile_open_count = Metric(
        'fo_creator_profile_open_count',
        'Creator Profile Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    fo_creator_profile_open_uu = Metric(
        'fo_creator_profile_open_uu',
        'Creator Profile Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    fo_creator_story_open_count = Metric(
        'fo_creator_story_open_count',
        'Creator Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    fo_creator_story_open_uu = Metric(
        'fo_creator_story_open_uu',
        'Creator Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    fo_so_creator_story_open_count = Metric(
        'fo_so_creator_story_open_count',
        '(1st + 2nd order) Creator Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    fo_so_creator_story_open_uu = Metric(
        'fo_so_creator_story_open_uu',
        '(1st + 2nd order) Creator Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    fo_creator_subscribe_count = Metric(
        'fo_creator_subscribe_count',
        'Creator Subscribe - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    fo_creator_subscribe_uu = Metric(
        'fo_creator_subscribe_uu',
        'Creator Subscribe - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    fo_so_creator_subscribe_count = Metric(
        'fo_so_creator_subscribe_count',
        '(1st + 2nd order) Creator Subscribe - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    fo_so_creator_subscribe_uu = Metric(
        'fo_so_creator_subscribe_uu',
        '(1st + 2nd order) Creator Subscribe - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )


    mt = MetricTable(
        sql="""
            SELECT 
                first_order.ghost_user_id,
                first_order.ts,
                sum(first_order.action_count) as fo_creator_action_count,
                sum(first_order.profile_open_count) as fo_creator_profile_open_count,
                if(sum(first_order.profile_open_count)>0, 1, 0) as fo_creator_profile_open_uu,
                sum(first_order.story_open_count) as fo_creator_story_open_count,
                if(sum(first_order.story_open_count)>0, 1, 0) as fo_creator_story_open_uu,
                sum(first_order.story_open_count + ifnull(second_order.story_open_count,0)) as fo_so_creator_story_open_count, 
                if(sum(first_order.story_open_count + ifnull(second_order.story_open_count,0))>0, 1, 0) as fo_so_creator_story_open_uu,
                sum(first_order.subscribe_count) as fo_creator_subscribe_count,
                if(sum(first_order.subscribe_count)>0, 1, 0) as fo_creator_subscribe_uu,
                sum(first_order.subscribe_count + ifnull(second_order.subscribe_count, 0)) as fo_so_creator_subscribe_count,
                if(sum(first_order.subscribe_count + ifnull(second_order.subscribe_count,0))>0, 1, 0) as fo_so_creator_subscribe_uu,
            FROM (
                SELECT 
                    ghost_user_id, 
                    event_date as ts,
                    _TABLE_SUFFIX as table_suffix,
                    count(*) as action_count,
                    countif(action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT') profile_open_count,
                    countif(action='OPEN_USER_STORY_FROM_SEARCH_RESULT') story_open_count,
                    countif(action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT') subscribe_count,
                FROM `sc-analytics.report_search_v2.search_action_20*`
                WHERE _TABLE_SUFFIX BETWEEN '{start_trunc}' AND '{end_trunc}'
                    AND source='SEARCH_UNSPECIFIED'
                    AND (SPLIT(search_result_identifier, ':')[SAFE_ORDINAL(1)] = '2' 
                         OR search_result_section = 'SUBSCRIPTIONS')
                GROUP BY 1,2,3
            ) first_order 
            LEFT OUTER JOIN (
                SELECT 
                    ghost_user_id, 
                    event_date as ts,
                    _TABLE_SUFFIX as table_suffix,
                    sum(public_profile_story_view_count) as story_open_count,
                    sum(if(public_profile_subscribe_count>0, 1, 0)) as subscribe_count,
                FROM `sc-analytics.report_search_v2.search_to_creatorprofile_open_20*`
                WHERE _TABLE_SUFFIX BETWEEN '{start_trunc}' AND '{end_trunc}'
                    AND source='SEARCH_UNSPECIFIED'
                GROUP BY 1,2,3
            ) second_order
                ON first_order.ghost_user_id = second_order.ghost_user_id
                AND first_order.table_suffix = second_order.table_suffix
            GROUP BY 1,2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            fo_creator_action_count,
            fo_creator_profile_open_count,
            fo_creator_profile_open_uu,
            fo_creator_story_open_count,
            fo_creator_story_open_uu,
            fo_so_creator_story_open_count, 
            fo_so_creator_story_open_uu,
            fo_creator_subscribe_count,
            fo_creator_subscribe_uu,
            fo_so_creator_subscribe_count,
            fo_so_creator_subscribe_uu,
        ],
        name="creator_metrics",
        bq_dialect='standard'
    )

    return mt


def creator_posttype_metrics(start_date, end_date):

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    fo_creator_posttype_profile_open_count = Metric(
        'fo_creator_posttype_profile_open_count',
        'Posttype Creator Profile Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    fo_creator_posttype_profile_open_uu = Metric(
        'fo_creator_posttype_profile_open_uu',
        'Posttype Creator Profile Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    fo_creator_posttype_story_open_count = Metric(
        'fo_creator_posttype_story_open_count',
        'Posttype Creator Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    fo_creator_posttype_story_open_uu = Metric(
        'fo_creator_posttype_story_open_uu',
        'Posttype Creator Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    fo_creator_posttype_saved_story_open_count = Metric(
        'fo_creator_posttype_saved_story_open_count',
        'Posttype Creator Saved Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    fo_creator_posttype_saved_story_open_uu = Metric(
        'fo_creator_posttype_saved_story_open_uu',
        'Posttype Creator Saved Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    fo_so_creator_posttype_story_open_count = Metric(
        'fo_so_creator_posttype_story_open_count',
        '(1st + 2nd order) Posttype Creator Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    fo_so_creator_posttype_story_open_uu = Metric(
        'fo_so_creator_posttype_story_open_uu',
        '(1st + 2nd order) Posttype Creator Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    fo_creator_posttype_subscribe_count = Metric(
        'fo_creator_posttype_subscribe_count',
        'Posttype Creator Subscribe - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    fo_creator_posttype_subscribe_uu = Metric(
        'fo_creator_posttype_subscribe_uu',
        'Posttype Creator Subscribe - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    fo_so_creator_posttype_subscribe_count = Metric(
        'fo_so_creator_posttype_subscribe_count',
        '(1st + 2nd order) Posttype Creator Subscribe - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    fo_so_creator_posttype_subscribe_uu = Metric(
        'fo_so_creator_posttype_subscribe_uu',
        '(1st + 2nd order) Posttype Creator Subscribe - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )


    mt = MetricTable(
        sql="""
            SELECT 
                first_order.ghost_user_id,
                first_order.ts,
                sum(first_order.profile_open_count) as fo_creator_posttype_profile_open_count,
                if(sum(first_order.profile_open_count)>0, 1, 0) as fo_creator_posttype_profile_open_uu,
                sum(first_order.story_open_count) as fo_creator_posttype_story_open_count,
                if(sum(first_order.story_open_count)>0, 1, 0) as fo_creator_posttype_story_open_uu,
                sum(first_order.saved_story_open_count) as fo_creator_posttype_saved_story_open_count,
                if(sum(first_order.saved_story_open_count)>0, 1, 0) as fo_creator_posttype_saved_story_open_uu,
                sum(first_order.story_open_count + ifnull(second_order.story_open_count,0)) as fo_so_creator_posttype_story_open_count, 
                if(sum(first_order.story_open_count + ifnull(second_order.story_open_count,0))>0, 1, 0) as fo_so_creator_posttype_story_open_uu,
                sum(first_order.subscribe_count) as fo_creator_posttype_subscribe_count,
                if(sum(first_order.subscribe_count)>0, 1, 0) as fo_creator_posttype_subscribe_uu,
                sum(first_order.subscribe_count + ifnull(second_order.subscribe_count, 0)) as fo_so_creator_posttype_subscribe_count,
                if(sum(first_order.subscribe_count + ifnull(second_order.subscribe_count,0))>0, 1, 0) as fo_so_creator_posttype_subscribe_uu,
            FROM (
                SELECT 
                    ghost_user_id, 
                    event_date as ts,
                    _TABLE_SUFFIX as table_suffix,
                    countif(action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT') profile_open_count,
                    countif(action='OPEN_USER_SAVED_STORY_FROM_SEARCH_RESULT') saved_story_open_count,
                    countif(action='OPEN_USER_STORY_FROM_SEARCH_RESULT') story_open_count,
                    countif(action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT') subscribe_count,
                FROM `sc-analytics.report_search_v2.search_action_20*`
                WHERE _TABLE_SUFFIX BETWEEN '{start_trunc}' AND '{end_trunc}'
                    AND source='SEARCH_UNSPECIFIED'
                    AND search_result_section IN ('TOP_RESULTS', 'OFFICIAL_STORIES')
                    AND SPLIT(search_result_identifier, ':')[SAFE_ORDINAL(1)] = '2'
                GROUP BY 1,2,3
            ) first_order 
            LEFT OUTER JOIN (
                SELECT 
                    ghost_user_id, 
                    event_date as ts,
                    _TABLE_SUFFIX as table_suffix,
                    sum(public_profile_story_view_count) as story_open_count,
                    sum(if(public_profile_subscribe_count>0, 1, 0)) as subscribe_count,
                FROM `sc-analytics.report_search_v2.search_to_creatorprofile_open_20*`
                WHERE _TABLE_SUFFIX BETWEEN '{start_trunc}' AND '{end_trunc}'
                    AND source='SEARCH_UNSPECIFIED'
                    AND search_result_section IN ('TOP_RESULTS', 'OFFICIAL_STORIES')
                GROUP BY 1,2,3
            ) second_order
                ON first_order.ghost_user_id = second_order.ghost_user_id
                AND first_order.table_suffix = second_order.table_suffix
            GROUP BY 1,2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            fo_creator_posttype_profile_open_count,
            fo_creator_posttype_profile_open_uu,
            fo_creator_posttype_story_open_count,
            fo_creator_posttype_story_open_uu,
            fo_creator_posttype_saved_story_open_count,
            fo_creator_posttype_saved_story_open_uu,
            fo_so_creator_posttype_story_open_count, 
            fo_so_creator_posttype_story_open_uu,
            fo_creator_posttype_subscribe_count,
            fo_creator_posttype_subscribe_uu,
            fo_so_creator_posttype_subscribe_count,
            fo_so_creator_posttype_subscribe_uu,
        ],
        name="creator_posttype_metrics",
        bq_dialect='standard'
    )

    return mt


def official_stories_action(start_date, end_date):
    """
    Subscribe Section Interaction
    """

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    os_profile_open_count = Metric(
        'os_profile_open_count',
        'Popular Accts: Profile Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    os_profile_open_uu = Metric(
        'os_profile_open_uu',
        'Popular Accts: Profile Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    os_saved_story_open_count = Metric(
        'os_saved_story_open_count',
        'Popular Accts: Saved Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    os_saved_story_open_uu = Metric(
        'os_saved_story_open_uu',
        'Popular Accts: Saved Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    os_story_open_count = Metric(
        'os_story_open_count',
        'Popular Accts: Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    os_story_open_uu = Metric(
        'os_story_open_uu',
        'Popular Accts: Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    os_fo_so_story_open_count = Metric(
        'os_fo_so_story_open_count',
        'Popular Accts: (1st + 2nd order) Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    os_fo_so_story_open_uu = Metric(
        'os_fo_so_story_open_uu',
        'Popular Accts: (1st + 2nd order) Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    os_subscribe_count = Metric(
        'os_subscribe_count',
        'Popular Accts: Subscribe - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    os_subscribe_uu = Metric(
        'os_subscribe_uu',
        'Popular Accts: Subscribe - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    os_fo_so_subscribe_count = Metric(
        'os_fo_so_subscribe_count',
        'Popular Accts: (1st + 2nd order) Subscribe - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    os_fo_so_subscribe_uu = Metric(
        'os_fo_so_subscribe_uu',
        'Popular Accts: (1st + 2nd order)Subscribe - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    mt = MetricTable(
        sql="""
            SELECT 
                first_order.ghost_user_id,
                first_order.ts,
                sum(first_order.profile_open_count) as os_profile_open_count,
                if(sum(first_order.profile_open_count)>0, 1, 0) as os_profile_open_uu,
                sum(first_order.story_open_count) as os_story_open_count,
                if(sum(first_order.story_open_count)>0, 1, 0) as os_story_open_uu,
                sum(first_order.saved_story_open_count) as os_saved_story_open_count,
                if(sum(first_order.saved_story_open_count)>0, 1, 0) os_saved_story_open_uu,
                sum(first_order.story_open_count + ifnull(second_order.story_open_count,0)) as os_fo_so_story_open_count, 
                if(sum(first_order.story_open_count + ifnull(second_order.story_open_count,0))>0, 1, 0) as os_fo_so_story_open_uu,
                sum(first_order.subscribe_count) as os_subscribe_count,
                if(sum(first_order.subscribe_count)>0, 1, 0) as os_subscribe_uu,
                sum(first_order.subscribe_count + ifnull(second_order.subscribe_count, 0)) as os_fo_so_subscribe_count,
                if(sum(first_order.subscribe_count + ifnull(second_order.subscribe_count,0))>0, 1, 0) as os_fo_so_subscribe_uu,
            FROM (
                SELECT 
                    ghost_user_id, 
                    event_date as ts,
                    _TABLE_SUFFIX as table_suffix,
                    count(search_result_identifier) as meaningful_action_count,
                    countif(action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT') profile_open_count,
                    countif(action='OPEN_USER_STORY_FROM_SEARCH_RESULT') story_open_count,
                    countif(action='OPEN_USER_SAVED_STORY_FROM_SEARCH_RESULT') saved_story_open_count,
                    countif(action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT') subscribe_count,
                    countif(action='PRESS_VIEW_MORE') press_view_more_count,
                FROM `sc-analytics.report_search_v2.search_action_20*`
                WHERE _TABLE_SUFFIX BETWEEN '{start_trunc}' AND '{end_trunc}'
                    AND source='SEARCH_UNSPECIFIED'
                    AND search_result_section = 'OFFICIAL_STORIES'
                GROUP BY 1,2,3
            ) first_order 
            LEFT OUTER JOIN (
                SELECT 
                    ghost_user_id, 
                    event_date as ts,
                    _TABLE_SUFFIX as table_suffix,
                    sum(public_profile_story_view_count) as story_open_count,
                    sum(if(public_profile_subscribe_count>0, 1, 0)) as subscribe_count,
                FROM `sc-analytics.report_search_v2.search_to_creatorprofile_open_20*`
                WHERE _TABLE_SUFFIX BETWEEN '{start_trunc}' AND '{end_trunc}'
                    AND source='SEARCH_UNSPECIFIED'
                    AND search_result_section = 'OFFICIAL_STORIES'
                GROUP BY 1,2,3
            ) second_order
                ON first_order.ghost_user_id = second_order.ghost_user_id
                AND first_order.table_suffix = second_order.table_suffix
            GROUP BY 1,2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            os_profile_open_count,
            os_profile_open_uu,
            os_saved_story_open_count,
            os_saved_story_open_uu,
            os_story_open_count,
            os_story_open_uu,
            os_fo_so_story_open_count,
            os_fo_so_story_open_uu,
            os_subscribe_count,
            os_subscribe_uu,
            os_fo_so_subscribe_count,
            os_fo_so_subscribe_uu,
        ],
        name="subscribe_action_2",
        bq_dialect='standard'

    )
    return mt

def stars_pretype_action(start_date, end_date):
    """
    Stars Section Interaction in Pretype
    """

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    star_profile_open_count = Metric(
        'star_profile_open_count',
        'Stars: Profile Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    star_profile_open_uu = Metric(
        'star_profile_open_uu',
        'Stars: Profile Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    star_story_open_count = Metric(
        'star_story_open_count',
        'Stars: Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    star_story_open_uu = Metric(
        'star_story_open_uu',
        'Stars: Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    star_fo_so_story_open_count = Metric(
        'star_fo_so_story_open_count',
        'Stars: (1st + 2nd order) Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    star_fo_so_story_open_uu = Metric(
        'star_fo_so_story_open_uu',
        'Stars: (1st + 2nd order) Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    star_subscribe_count = Metric(
        'star_subscribe_count',
        'Stars: Subscribe - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    star_subscribe_uu = Metric(
        'star_subscribe_uu',
        'Stars: Subscribe - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    star_fo_so_subscribe_count = Metric(
        'star_fo_so_subscribe_count',
        'Stars: (1st + 2nd order) Subscribe - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    star_fo_so_subscribe_uu = Metric(
        'star_fo_so_subscribe_uu',
        'Stars: (1st + 2nd order)Subscribe - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )


    mt = MetricTable(
        sql="""
            SELECT 
                first_order.ghost_user_id,
                first_order.ts,
                sum(first_order.profile_open_count) as star_profile_open_count,
                if(sum(first_order.profile_open_count)>0, 1, 0) as star_profile_open_uu,
                sum(first_order.story_open_count) as star_story_open_count,
                if(sum(first_order.story_open_count)>0, 1, 0) as star_story_open_uu,
                sum(first_order.story_open_count + ifnull(second_order.story_open_count,0)) as star_fo_so_story_open_count, 
                if(sum(first_order.story_open_count + ifnull(second_order.story_open_count,0))>0, 1, 0) as star_fo_so_story_open_uu,
                sum(first_order.subscribe_count) as star_subscribe_count,
                if(sum(first_order.subscribe_count)>0, 1, 0) as star_subscribe_uu,
                sum(first_order.subscribe_count + ifnull(second_order.subscribe_count, 0)) as star_fo_so_subscribe_count,
                if(sum(first_order.subscribe_count + ifnull(second_order.subscribe_count,0))>0, 1, 0) as star_fo_so_subscribe_uu,
            FROM (
                SELECT 
                    ghost_user_id, 
                    event_date as ts,
                    _TABLE_SUFFIX as table_suffix,
                    countif(action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT') profile_open_count,
                    countif(action='OPEN_USER_STORY_FROM_SEARCH_RESULT') story_open_count,
                    countif(action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT') subscribe_count,
                FROM `sc-analytics.report_search_v2.search_action_20*`
                WHERE _TABLE_SUFFIX BETWEEN '{start_trunc}' AND '{end_trunc}'
                    AND source='SEARCH_UNSPECIFIED'
                    AND search_result_section = 'STARS_PRETYPE'
                GROUP BY 1,2,3
            ) first_order 
            LEFT OUTER JOIN (
                SELECT 
                    ghost_user_id, 
                    event_date as ts,
                    _TABLE_SUFFIX as table_suffix,
                    sum(public_profile_story_view_count) as story_open_count,
                    sum(if(public_profile_subscribe_count>0, 1, 0)) as subscribe_count,
                FROM `sc-analytics.report_search_v2.search_to_creatorprofile_open_20*`
                WHERE _TABLE_SUFFIX BETWEEN '{start_trunc}' AND '{end_trunc}'
                    AND source='SEARCH_UNSPECIFIED'
                    AND search_result_section = 'STARS_PRETYPE'
                GROUP BY 1,2,3
            ) second_order
                ON first_order.ghost_user_id = second_order.ghost_user_id
                AND first_order.table_suffix = second_order.table_suffix
            GROUP BY 1,2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            star_profile_open_count,
            star_profile_open_uu,
            star_story_open_count,
            star_story_open_uu,
            star_fo_so_story_open_count, 
            star_fo_so_story_open_uu,
            star_subscribe_count,
            star_subscribe_uu,
            star_fo_so_subscribe_count,
            star_fo_so_subscribe_uu,
        ],
        name="stars_action_pretype",
        bq_dialect='standard'

    )
    return mt

def posttype_creator_tier(start_date, end_date):
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]
            
    creator_open_profile_expert_count = Metric('creator_open_profile_expert_count', 'Posttype Creator Profile Open - Count [Expert]', dist='cont', daily=True, cumulative=True)
    creator_open_profile_expert_uu = Metric('creator_open_profile_expert_uu', 'Posttype Creator Profile Open - UU [Expert]', dist='bin', daily=True, cumulative=True)
    creator_view_story_expert_count = Metric('creator_view_story_expert_count', 'Posttype Creator Story View - Count [Expert]', dist='cont', daily=True, cumulative=True)
    creator_view_story_expert_uu = Metric('creator_view_story_expert_uu', 'Posttype Creator Story View - UU [Expert]', dist='bin', daily=True, cumulative=True)
    creator_subscribe_expert_count = Metric('creator_subscribe_expert_count', 'Posttype Creator Subscribe - Count [Expert]', dist='cont', daily=True, cumulative=True)
    creator_subscribe_expert_uu = Metric('creator_subscribe_expert_uu', 'Posttype Creator Subscribe - UU [Expert]', dist='bin', daily=True, cumulative=True)

    creator_open_profile_advanced_count = Metric('creator_open_profile_advanced_count', 'Posttype Creator Profile Open - Count [Advanced]', dist='cont', daily=True, cumulative=True)
    creator_open_profile_advanced_uu = Metric('creator_open_profile_advanced_uu', 'Posttype Creator Profile Open - UU [Advanced]', dist='bin', daily=True, cumulative=True)
    creator_view_story_advanced_count = Metric('creator_view_story_advanced_count', 'Posttype Creator Story View - Count [Advanced]', dist='cont', daily=True, cumulative=True)
    creator_view_story_advanced_uu = Metric('creator_view_story_advanced_uu', 'Posttype Creator View Story - UU [Advanced]', dist='bin', daily=True, cumulative=True)
    creator_subscribe_advanced_count = Metric('creator_subscribe_advanced_count', 'Posttype Creator Subscribe - Count [Advanced]', dist='cont', daily=True, cumulative=True)
    creator_subscribe_advanced_uu = Metric('creator_subscribe_advanced_uu', 'Posttype Creator Subscribe - UU [Advanced]', dist='bin', daily=True, cumulative=True)

    creator_open_profile_intermediate_count = Metric('creator_open_profile_intermediate_count', 'Posttype Creator Profile Open - Count [Intermediate]', dist='cont', daily=True, cumulative=True)
    creator_open_profile_intermediate_uu = Metric('creator_open_profile_intermediate_uu', 'Posttype Creator Profile Open - UU [Intermediate]', dist='bin', daily=True, cumulative=True)
    creator_view_story_intermediate_count = Metric('creator_view_story_intermediate_count', 'Posttype Creator Story View - Count [Intermediate]', dist='cont', daily=True, cumulative=True)
    creator_view_story_intermediate_uu = Metric('creator_view_story_intermediate_uu', 'Posttype Creator Story View - UU [Intermediate]', dist='bin', daily=True, cumulative=True)
    creator_subscribe_intermediate_count = Metric('creator_subscribe_intermediate_count', 'Posttype Creator Subscribe - Count [Intermediate]', dist='cont', daily=True, cumulative=True)
    creator_subscribe_intermediate_uu = Metric('creator_subscribe_intermediate_uu', 'Posttype Creator Subscribe - UU [Intermediate]', dist='bin', daily=True, cumulative=True)

    creator_open_profile_beginner_count = Metric('creator_open_profile_beginner_count', 'Posttype Creator Profile Open - Count [Elementary, Beginner]', dist='cont', daily=True, cumulative=True)
    creator_open_profile_beginner_uu = Metric('creator_open_profile_beginner_uu', 'Posttype Creator Profile Open - UU [Elementary, Beginner]', dist='bin', daily=True, cumulative=True)
    creator_view_story_beginner_count = Metric('creator_view_story_beginner_count', 'Posttype Creator Story View - Count [Elementary, Beginner]', dist='cont', daily=True, cumulative=True)
    creator_view_story_beginner_uu = Metric('creator_view_story_beginner_uu', 'Posttype Creator Story View Open - UU [Elementary, Beginner]', dist='bin', daily=True, cumulative=True)
    creator_subscribe_beginner_count = Metric('creator_subscribe_beginner_count', 'Posttype Creator Subscribe - Count [Elementary, Beginner]', dist='cont', daily=True, cumulative=True)
    creator_subscribe_beginner_uu = Metric('creator_subscribe_beginner_uu', 'Posttype Creator Subscribe - UU [Elementary, Beginner]', dist='bin', daily=True, cumulative=True)

    creator_open_profile_nopublicpost_count = Metric('creator_open_profile_nopublicpost_count', 'Posttype Creator Profile Open - Count [NoPublicPost, NoViewsReceived]', dist='cont', daily=True, cumulative=True)
    creator_open_profile_nopublicpost_uu = Metric('creator_open_profile_nopublicpost_uu', 'Posttype Creator Profile Open - UU [NoPublicPost, NoViewsReceived]', dist='bin', daily=True, cumulative=True)
    creator_view_story_nopublicpost_count = Metric('creator_view_story_nopublicpost_count', 'Posttype Creator Story View - Count [NoPublicPost, NoViewsReceived]', dist='cont', daily=True, cumulative=True)
    creator_view_story_nopublicpost_uu = Metric('creator_view_story_nopublicpost_uu', 'Posttype Creator Story View - UU [NoPublicPost, NoViewsReceived]', dist='bin', daily=True, cumulative=True)
    creator_subscribe_nopublicpost_count = Metric('creator_subscribe_nopublicpost_count', 'Posttype Creator Subscribe - Count [NoPublicPost, NoViewsReceived]', dist='cont', daily=True, cumulative=True)
    creator_subscribe_nopublicpost_uu = Metric('creator_subscribe_nopublicpost_uu', 'Posttype Creator Subscribe - UU [NoPublicPost, NoViewsReceived]', dist='bin', daily=True, cumulative=True)

    creator_open_profile_null_count = Metric('creator_open_profile_null_count', 'Posttype Creator Profile Open - Count [Null]', dist='cont', daily=True, cumulative=True)
    creator_open_profile_null_uu = Metric('creator_open_profile_null_uu', 'Posttype Creator Profile Open - UU [Null]', dist='bin', daily=True, cumulative=True)
    creator_view_story_null_count = Metric('creator_view_story_null_count', 'Posttype Creator Story View - Count [Null]', dist='cont', daily=True, cumulative=True)
    creator_view_story_null_uu = Metric('creator_view_story_null_uu', 'Posttype Creator Story View - UU [Null]', dist='bin', daily=True, cumulative=True)
    creator_subscribe_null_count = Metric('creator_subscribe_null_count', 'Posttype Creator Subscribe - Count [Null]', dist='cont', daily=True, cumulative=True)
    creator_subscribe_null_uu = Metric('creator_subscribe_null_uu', 'Posttype Creator Subscribe - UU [Null]', dist='bin', daily=True, cumulative=True)


    mt = MetricTable(
    sql = """
        SELECT 
            event_date as ts,
            a.ghost_user_id,
            
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier = 'Expert') as creator_open_profile_expert_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier = 'Expert')>0, 1, 0) as creator_open_profile_expert_uu,
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier = 'Advanced') as creator_open_profile_advanced_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier = 'Advanced')>0, 1, 0) as creator_open_profile_advanced_uu,
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier = 'Intermediate') as creator_open_profile_intermediate_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier = 'Intermediate')>0, 1, 0) as creator_open_profile_intermediate_uu,
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier IN ('Elementary', 'Beginner')) as creator_open_profile_beginner_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier IN ('Elementary', 'Beginner'))>0, 1, 0) as creator_open_profile_beginner_uu,
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier IN ('NoPublicPost', 'NoViewsReceived')) as creator_open_profile_nopublicpost_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier IN ('NoPublicPost', 'NoViewsReceived'))>0, 1, 0) as creator_open_profile_nopublicpost_uu,
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier IS NULL) as creator_open_profile_null_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier IS NULL)>0, 1, 0) as creator_open_profile_null_uu,

            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier = 'Expert') as creator_view_story_expert_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier = 'Expert')>0, 1, 0) as creator_view_story_expert_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier = 'Advanced') as creator_view_story_advanced_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier = 'Advanced')>0, 1, 0) as creator_view_story_advanced_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier = 'Intermediate') as creator_view_story_intermediate_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier = 'Intermediate')>0, 1, 0) as creator_view_story_intermediate_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier IN ('Elementary', 'Beginner')) as creator_view_story_beginner_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier IN ('Elementary', 'Beginner'))>0, 1, 0) as creator_view_story_beginner_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier IN ('NoPublicPost', 'NoViewsReceived')) as creator_view_story_nopublicpost_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier IN ('NoPublicPost', 'NoViewsReceived'))>0, 1, 0) as creator_view_story_nopublicpost_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier IS NULL) as creator_view_story_null_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier IS NULL)>0, 1, 0) as creator_view_story_null_uu,

            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier = 'Expert') as creator_subscribe_expert_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier = 'Expert')>0, 1, 0) as creator_subscribe_expert_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier = 'Advanced') as creator_subscribe_advanced_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier = 'Advanced')>0, 1, 0) as creator_subscribe_advanced_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier = 'Intermediate') as creator_subscribe_intermediate_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier = 'Intermediate')>0, 1, 0) as creator_subscribe_intermediate_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier IN ('Elementary', 'Beginner')) as creator_subscribe_beginner_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier IN ('Elementary', 'Beginner'))>0, 1, 0) as creator_subscribe_beginner_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier IN ('NoPublicPost', 'NoViewsReceived')) as creator_subscribe_nopublicpost_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier IN ('NoPublicPost', 'NoViewsReceived'))>0, 1, 0) as creator_subscribe_nopublicpost_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier IS NULL) as creator_subscribe_null_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier IS NULL)>0, 1, 0) as creator_subscribe_null_uu,

        FROM `sc-analytics.report_search_v2.search_action_20*` a
            LEFT OUTER JOIN `sc-analytics.report_creator.aggregated_creator_metrics_20*` b
                ON a._TABLE_SUFFIX = b._TABLE_SUFFIX AND REGEXP_EXTRACT(a.search_result_identifier, r"^\d+::(.+?)::.*$")=b.profile_id
        WHERE
            a._TABLE_SUFFIX BETWEEN '{start_trunc}' AND '{end_trunc}'
            AND REGEXP_EXTRACT(a.search_result_identifier, r"^(.+?):") = '2'
            AND is_pretype='PostType'
            AND source='SEARCH_UNSPECIFIED'
        GROUP BY 1,2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            creator_open_profile_expert_count,
            creator_open_profile_expert_uu,
            creator_view_story_expert_count,
            creator_view_story_expert_uu,
            creator_subscribe_expert_count,
            creator_subscribe_expert_uu,

            creator_open_profile_advanced_count,
            creator_open_profile_advanced_uu,
            creator_view_story_advanced_count,
            creator_view_story_advanced_uu,
            creator_subscribe_advanced_count,
            creator_subscribe_advanced_uu,

            creator_open_profile_intermediate_count,
            creator_open_profile_intermediate_uu,
            creator_view_story_intermediate_count,
            creator_view_story_intermediate_uu,
            creator_subscribe_intermediate_count,
            creator_subscribe_intermediate_uu,

            creator_open_profile_beginner_count,
            creator_open_profile_beginner_uu,
            creator_view_story_beginner_count,
            creator_view_story_beginner_uu,
            creator_subscribe_beginner_count,
            creator_subscribe_beginner_uu,

            creator_open_profile_nopublicpost_count,
            creator_open_profile_nopublicpost_uu,
            creator_view_story_nopublicpost_count,
            creator_view_story_nopublicpost_uu,
            creator_subscribe_nopublicpost_count,
            creator_subscribe_nopublicpost_uu,

            creator_open_profile_null_count,
            creator_open_profile_null_uu,
            creator_view_story_null_count,
            creator_view_story_null_uu,
            creator_subscribe_null_count,
            creator_subscribe_null_uu,
        ],
        name='posttype_creator_tier',
        bq_dialect='standard'
    )

    return mt

def posttype_real_creator_tier(start_date, end_date):
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    creator_open_profile_tier_unknown_count = Metric('creator_open_profile_tier_unknown_count',
                                               'Posttype Creator Profile Open - Count [Tier Unknown]', dist='cont',
                                               daily=True, cumulative=True)
    creator_open_profile_tier_unknown_uu = Metric('creator_open_profile_tier_unknown_uu',
                                            'Posttype Creator Profile Open - UU [Tier Unknown]', dist='bin', daily=True,
                                            cumulative=True)
    creator_view_story_tier_unknown_count = Metric('creator_view_story_tier_unknown_count',
                                             'Posttype Creator Story View - Count [Tier Unknown]', dist='cont', daily=True,
                                             cumulative=True)
    creator_view_story_tier_unknown_uu = Metric('creator_view_story_tier_unknown_uu', 'Posttype Creator Story View - UU [Tier Unknown]',
                                          dist='bin', daily=True, cumulative=True)
    creator_subscribe_tier_unknown_count = Metric('creator_subscribe_tier_unknown_count',
                                            'Posttype Creator Subscribe - Count [Tier Unknown]', dist='cont', daily=True,
                                            cumulative=True)
    creator_subscribe_tier_unknown_uu = Metric('creator_subscribe_tier_unknown_uu', 'Posttype Creator Subscribe - UU [Tier Unknown]',
                                         dist='bin', daily=True, cumulative=True)

    creator_open_profile_snapchatter_count = Metric('creator_open_profile_snapchatter_count',
                                                 'Posttype Creator Profile Open - Count [Snapchatter]', dist='cont',
                                                 daily=True, cumulative=True)
    creator_open_profile_snapchatter_uu = Metric('creator_open_profile_snapchatter_uu',
                                              'Posttype Creator Profile Open - UU [Snapchatter]', dist='bin', daily=True,
                                              cumulative=True)
    creator_view_story_snapchatter_count = Metric('creator_view_story_snapchatter_count',
                                               'Posttype Creator Story View - Count [Snapchatter]', dist='cont',
                                               daily=True, cumulative=True)
    creator_view_story_snapchatter_uu = Metric('creator_view_story_snapchatter_uu',
                                            'Posttype Creator View Story - UU [Snapchatter]', dist='bin', daily=True,
                                            cumulative=True)
    creator_subscribe_snapchatter_count = Metric('creator_subscribe_snapchatter_count',
                                              'Posttype Creator Subscribe - Count [Snapchatter]', dist='cont', daily=True,
                                              cumulative=True)
    creator_subscribe_snapchatter_uu = Metric('creator_subscribe_snapchatter_uu',
                                           'Posttype Creator Subscribe - UU [Snapchatter]', dist='bin', daily=True,
                                           cumulative=True)

    creator_open_profile_quality_count = Metric('creator_open_profile_quality_count',
                                                     'Posttype Creator Profile Open - Count [Quality]',
                                                     dist='cont', daily=True, cumulative=True)
    creator_open_profile_quality_uu = Metric('creator_open_profile_quality_uu',
                                                  'Posttype Creator Profile Open - UU [Quality]', dist='bin',
                                                  daily=True, cumulative=True)
    creator_view_story_quality_count = Metric('creator_view_story_quality_count',
                                                   'Posttype Creator Story View - Count [Quality]', dist='cont',
                                                   daily=True, cumulative=True)
    creator_view_story_quality_uu = Metric('creator_view_story_quality_uu',
                                                'Posttype Creator Story View - UU [Quality]', dist='bin',
                                                daily=True, cumulative=True)
    creator_subscribe_quality_count = Metric('creator_subscribe_quality_count',
                                                  'Posttype Creator Subscribe - Count [Quality]', dist='cont',
                                                  daily=True, cumulative=True)
    creator_subscribe_quality_uu = Metric('creator_subscribe_quality_uu',
                                               'Posttype Creator Subscribe - UU [Quality]', dist='bin', daily=True,
                                               cumulative=True)

    creator_open_profile_content_reposter_count = Metric('creator_open_profile_content_reposter_count',
                                                 'Posttype Creator Profile Open - Count [Content Reposter]',
                                                 dist='cont', daily=True, cumulative=True)
    creator_open_profile_content_reposter_uu = Metric('creator_open_profile_content_reposter_uu',
                                              'Posttype Creator Profile Open - UU [Content Reposter]', dist='bin',
                                              daily=True, cumulative=True)
    creator_view_story_content_reposter_count = Metric('creator_view_story_content_reposter_count',
                                               'Posttype Creator Story View - Count [Content Reposter]',
                                               dist='cont', daily=True, cumulative=True)
    creator_view_story_content_reposter_uu = Metric('creator_view_story_content_reposter_uu',
                                            'Posttype Creator Story View Open - UU [Content Reposter]', dist='bin',
                                            daily=True, cumulative=True)
    creator_subscribe_content_reposter_count = Metric('creator_subscribe_content_reposter_count',
                                              'Posttype Creator Subscribe - Count [Content Reposter]', dist='cont',
                                              daily=True, cumulative=True)
    creator_subscribe_content_reposter_uu = Metric('creator_subscribe_content_reposter_uu',
                                           'Posttype Creator Subscribe - UU [Content Reposter]', dist='bin',
                                           daily=True, cumulative=True)

    creator_open_profile_policy_violator_count = Metric('creator_open_profile_policy_violator_count',
                                                     'Posttype Creator Profile Open - Count [Policy Violator]',
                                                     dist='cont', daily=True, cumulative=True)
    creator_open_profile_policy_violator_uu = Metric('creator_open_profile_policy_violator_uu',
                                                  'Posttype Creator Profile Open - UU [Policy Violator]',
                                                  dist='bin', daily=True, cumulative=True)
    creator_view_story_policy_violator_count = Metric('creator_view_story_policy_violator_count',
                                                   'Posttype Creator Story View - Count [Policy Violator]',
                                                   dist='cont', daily=True, cumulative=True)
    creator_view_story_policy_violator_uu = Metric('creator_view_story_policy_violator_uu',
                                                'Posttype Creator Story View - UU [Policy Violator]',
                                                dist='bin', daily=True, cumulative=True)
    creator_subscribe_policy_violator_count = Metric('creator_subscribe_policy_violator_count',
                                                  'Posttype Creator Subscribe - Count [Policy Violator]',
                                                  dist='cont', daily=True, cumulative=True)
    creator_subscribe_policy_violator_uu = Metric('creator_subscribe_policy_violator_uu',
                                               'Posttype Creator Subscribe - UU [Policy Violator]',
                                               dist='bin', daily=True, cumulative=True)

    mt = MetricTable(
        sql="""
        SELECT 
            a.event_date as ts,
            a.ghost_user_id,

            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'TIER_UNKNOWN') as creator_open_profile_tier_unknown_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'TIER_UNKNOWN')>0, 1, 0) as creator_open_profile_tier_unknown_uu,
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'SNAPCHATTER') as creator_open_profile_snapchatter_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'SNAPCHATTER')>0, 1, 0) as creator_open_profile_snapchatter_uu,
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'QUALITY') as creator_open_profile_quality_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'QUALITY')>0, 1, 0) as creator_open_profile_quality_uu,
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'CONTENT_REPOSTER') as creator_open_profile_content_reposter_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'CONTENT_REPOSTER')>0, 1, 0) as creator_open_profile_content_reposter_uu,
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'POLICY_VIOLATOR') as creator_open_profile_policy_violator_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'POLICY_VIOLATOR')>0, 1, 0) as creator_open_profile_policy_violator_uu,

            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'TIER_UNKNOWN') as creator_view_story_tier_unknown_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'TIER_UNKNOWN')>0, 1, 0) as creator_view_story_tier_unknown_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'SNAPCHATTER') as creator_view_story_snapchatter_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'SNAPCHATTER')>0, 1, 0) as creator_view_story_snapchatter_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'QUALITY') as creator_view_story_quality_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'QUALITY')>0, 1, 0) as creator_view_story_quality_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'CONTENT_REPOSTER') as creator_view_story_content_reposter_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'CONTENT_REPOSTER')>0, 1, 0) as creator_view_story_content_reposter_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'POLICY_VIOLATOR') as creator_view_story_policy_violator_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'POLICY_VIOLATOR')>0, 1, 0) as creator_view_story_policy_violator_uu,

            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'TIER_UNKNOWN') as creator_subscribe_tier_unknown_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'TIER_UNKNOWN')>0, 1, 0) as creator_subscribe_tier_unknown_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'SNAPCHATTER') as creator_subscribe_snapchatter_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'SNAPCHATTER')>0, 1, 0) as creator_subscribe_snapchatter_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'QUALITY') as creator_subscribe_quality_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'QUALITY')>0, 1, 0) as creator_subscribe_quality_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'CONTENT_REPOSTER') as creator_subscribe_content_reposter_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'CONTENT_REPOSTER')>0, 1, 0) as creator_subscribe_content_reposter_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'POLICY_VIOLATOR') as creator_subscribe_policy_violator_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__tier = 'POLICY_VIOLATOR')>0, 1, 0) as creator_subscribe_policy_violator_uu,

        FROM `sc-analytics.report_search_v2.search_action_20*` a
            LEFT OUTER JOIN `sc-analytics.report_creator.creator_profile_20*` cp
              ON a._TABLE_SUFFIX = cp._TABLE_SUFFIX 
              AND REGEXP_EXTRACT(a.search_result_identifier, r"^\d+::(.+?)::.*$") = cp.BusinessProfileId
            LEFT OUTER JOIN `context-pii.creator_profile.creator_profile_v1_prod_20*` b
                on cp._TABLE_SUFFIX = b._TABLE_SUFFIX
                and cp.user_id = b.creator_user_id
        WHERE
            a._TABLE_SUFFIX BETWEEN '{start_trunc}' AND '{end_trunc}'
            AND REGEXP_EXTRACT(a.search_result_identifier, r"^(.+?):") = '2'
            AND is_pretype='PostType'
            AND source='SEARCH_UNSPECIFIED'
        GROUP BY 1,2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
        ),
        metrics=[
            creator_open_profile_tier_unknown_count,
            creator_open_profile_tier_unknown_uu,
            creator_view_story_tier_unknown_count,
            creator_view_story_tier_unknown_uu,
            creator_subscribe_tier_unknown_count,
            creator_subscribe_tier_unknown_uu,

            creator_open_profile_snapchatter_count,
            creator_open_profile_snapchatter_uu,
            creator_view_story_snapchatter_count,
            creator_view_story_snapchatter_uu,
            creator_subscribe_snapchatter_count,
            creator_subscribe_snapchatter_uu,

            creator_open_profile_quality_count,
            creator_open_profile_quality_uu,
            creator_view_story_quality_count,
            creator_view_story_quality_uu,
            creator_subscribe_quality_count,
            creator_subscribe_quality_uu,

            creator_open_profile_content_reposter_count,
            creator_open_profile_content_reposter_uu,
            creator_view_story_content_reposter_count,
            creator_view_story_content_reposter_uu,
            creator_subscribe_content_reposter_count,
            creator_subscribe_content_reposter_uu,

            creator_open_profile_policy_violator_count,
            creator_open_profile_policy_violator_uu,
            creator_view_story_policy_violator_count,
            creator_view_story_policy_violator_uu,
            creator_subscribe_policy_violator_count,
            creator_subscribe_policy_violator_uu,
        ],
        name='posttype_real_creator_tier',
        bq_dialect='standard'
    )

    return mt

def posttype_real_creator_subtier(start_date, end_date):
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    creator_open_profile_emerging_count = Metric('creator_open_profile_emerging_count',
                                               'Posttype Creator Profile Open - Count [Tier Emerging]', dist='cont',
                                               daily=True, cumulative=True)
    creator_open_profile_emerging_uu = Metric('creator_open_profile_emerging_uu',
                                            'Posttype Creator Profile Open - UU [Tier Emerging]', dist='bin', daily=True,
                                            cumulative=True)
    creator_view_story_emerging_count = Metric('creator_view_story_emerging_count',
                                             'Posttype Creator Story View - Count [Tier Emerging]', dist='cont', daily=True,
                                             cumulative=True)
    creator_view_story_emerging_uu = Metric('creator_view_story_emerging_uu', 'Posttype Creator Story View - UU [Tier Emerging]',
                                          dist='bin', daily=True, cumulative=True)
    creator_subscribe_emerging_count = Metric('creator_subscribe_emerging_count',
                                            'Posttype Creator Subscribe - Count [Tier Emerging]', dist='cont', daily=True,
                                            cumulative=True)
    creator_subscribe_emerging_uu = Metric('creator_subscribe_emerging_uu', 'Posttype Creator Subscribe - UU [Tier Emerging]',
                                         dist='bin', daily=True, cumulative=True)

    creator_open_profile_occasional_count = Metric('creator_open_profile_occasional_count',
                                                 'Posttype Creator Profile Open - Count [Occasional]', dist='cont',
                                                 daily=True, cumulative=True)
    creator_open_profile_occasional_uu = Metric('creator_open_profile_occasional_uu',
                                              'Posttype Creator Profile Open - UU [Occasional]', dist='bin', daily=True,
                                              cumulative=True)
    creator_view_story_occasional_count = Metric('creator_view_story_occasional_count',
                                               'Posttype Creator Story View - Count [Occasional]', dist='cont',
                                               daily=True, cumulative=True)
    creator_view_story_occasional_uu = Metric('creator_view_story_occasional_uu',
                                            'Posttype Creator View Story - UU [Occasional]', dist='bin', daily=True,
                                            cumulative=True)
    creator_subscribe_occasional_count = Metric('creator_subscribe_occasional_count',
                                              'Posttype Creator Subscribe - Count [Occasional]', dist='cont', daily=True,
                                              cumulative=True)
    creator_subscribe_occasional_uu = Metric('creator_subscribe_occasional_uu',
                                           'Posttype Creator Subscribe - UU [Occasional]', dist='bin', daily=True,
                                           cumulative=True)

    creator_open_profile_impersonator_count = Metric('creator_open_profile_impersonator_count',
                                                     'Posttype Creator Profile Open - Count [Impersonator]',
                                                     dist='cont', daily=True, cumulative=True)
    creator_open_profile_impersonator_uu = Metric('creator_open_profile_impersonator_uu',
                                                  'Posttype Creator Profile Open - UU [Impersonator]', dist='bin',
                                                  daily=True, cumulative=True)
    creator_view_story_impersonator_count = Metric('creator_view_story_impersonator_count',
                                                   'Posttype Creator Story View - Count [Impersonator]', dist='cont',
                                                   daily=True, cumulative=True)
    creator_view_story_impersonator_uu = Metric('creator_view_story_impersonator_uu',
                                                'Posttype Creator Story View - UU [Impersonator]', dist='bin',
                                                daily=True, cumulative=True)
    creator_subscribe_impersonator_count = Metric('creator_subscribe_impersonator_count',
                                                  'Posttype Creator Subscribe - Count [Impersonator]', dist='cont',
                                                  daily=True, cumulative=True)
    creator_subscribe_impersonator_uu = Metric('creator_subscribe_impersonator_uu',
                                               'Posttype Creator Subscribe - UU [Impersonator]', dist='bin', daily=True,
                                               cumulative=True)

    creator_open_profile_ic_verified_tier2_count = Metric('creator_open_profile_ic_verified_tier2_count',
                                                 'Posttype Creator Profile Open - Count [IC Cerified Tier 2]',
                                                 dist='cont', daily=True, cumulative=True)
    creator_open_profile_ic_verified_tier2_uu = Metric('creator_open_profile_ic_verified_tier2_uu',
                                              'Posttype Creator Profile Open - UU [IC Cerified Tier 2]', dist='bin',
                                              daily=True, cumulative=True)
    creator_view_story_ic_verified_tier2_count = Metric('creator_view_story_ic_verified_tier2_count',
                                               'Posttype Creator Story View - Count [IC Cerified Tier 2]',
                                               dist='cont', daily=True, cumulative=True)
    creator_view_story_ic_verified_tier2_uu = Metric('creator_view_story_ic_verified_tier2_uu',
                                            'Posttype Creator Story View Open - UU [IC Cerified Tier 2]', dist='bin',
                                            daily=True, cumulative=True)
    creator_subscribe_ic_verified_tier2_count = Metric('creator_subscribe_ic_verified_tier2_count',
                                              'Posttype Creator Subscribe - Count [IC Cerified Tier 2]', dist='cont',
                                              daily=True, cumulative=True)
    creator_subscribe_ic_verified_tier2_uu = Metric('creator_subscribe_ic_verified_tier2_uu',
                                           'Posttype Creator Subscribe - UU [IC Cerified Tier 2]', dist='bin',
                                           daily=True, cumulative=True)

    creator_open_profile_tt_verified_tier1_count = Metric('creator_open_profile_tt_verified_tier1_count',
                                                     'Posttype Creator Profile Open - Count [TT Verified Tier 1]',
                                                     dist='cont', daily=True, cumulative=True)
    creator_open_profile_tt_verified_tier1_uu = Metric('creator_open_profile_tt_verified_tier1_uu',
                                                  'Posttype Creator Profile Open - UU [TT Verified Tier 1]',
                                                  dist='bin', daily=True, cumulative=True)
    creator_view_story_tt_verified_tier1_count = Metric('creator_view_story_tt_verified_tier1_count',
                                                   'Posttype Creator Story View - Count [TT Verified Tier 1]',
                                                   dist='cont', daily=True, cumulative=True)
    creator_view_story_tt_verified_tier1_uu = Metric('creator_view_story_tt_verified_tier1_uu',
                                                'Posttype Creator Story View - UU [TT Verified Tier 1]',
                                                dist='bin', daily=True, cumulative=True)
    creator_subscribe_tt_verified_tier1_count = Metric('creator_subscribe_tt_verified_tier1_count',
                                                  'Posttype Creator Subscribe - Count [TT Verified Tier 1]',
                                                  dist='cont', daily=True, cumulative=True)
    creator_subscribe_tt_verified_tier1_uu = Metric('creator_subscribe_tt_verified_tier1_uu',
                                               'Posttype Creator Subscribe - UU [TT Verified Tier 1]',
                                               dist='bin', daily=True, cumulative=True)

    creator_open_profile_homegrown_count = Metric('creator_open_profile_homegrown_count',
                                                     'Posttype Creator Profile Open - Count [Homegrown]',
                                                     dist='cont', daily=True, cumulative=True)
    creator_open_profile_homegrown_uu = Metric('creator_open_profile_homegrown_uu',
                                                  'Posttype Creator Profile Open - UU [Homegrown]', dist='bin',
                                                  daily=True, cumulative=True)
    creator_view_story_homegrown_count = Metric('creator_view_story_homegrown_count',
                                                   'Posttype Creator Story View - Count [Homegrown]', dist='cont',
                                                   daily=True, cumulative=True)
    creator_view_story_homegrown_uu = Metric('creator_view_story_homegrown_uu',
                                                'Posttype Creator Story View - UU [Homegrown]', dist='bin',
                                                daily=True, cumulative=True)
    creator_subscribe_homegrown_count = Metric('creator_subscribe_homegrown_count',
                                                  'Posttype Creator Subscribe - Count [Homegrown]', dist='cont',
                                                  daily=True, cumulative=True)
    creator_subscribe_homegrown_uu = Metric('creator_subscribe_homegrown_uu',
                                               'Posttype Creator Subscribe - UU [Homegrown]', dist='bin', daily=True,
                                               cumulative=True)

    creator_open_profile_risk_high_count = Metric('creator_open_profile_risk_high_count',
                                                  'Posttype Creator Profile Open - Count [Risk High]',
                                                  dist='cont', daily=True, cumulative=True)
    creator_open_profile_risk_high_uu = Metric('creator_open_profile_risk_high_uu',
                                               'Posttype Creator Profile Open - UU [Risk High]', dist='bin',
                                               daily=True, cumulative=True)
    creator_view_story_risk_high_count = Metric('creator_view_story_risk_high_count',
                                                'Posttype Creator Story View - Count [Risk High]', dist='cont',
                                                daily=True, cumulative=True)
    creator_view_story_risk_high_uu = Metric('creator_view_story_risk_high_uu',
                                             'Posttype Creator Story View - UU [Risk High]', dist='bin',
                                             daily=True, cumulative=True)
    creator_subscribe_risk_high_count = Metric('creator_subscribe_risk_high_count',
                                               'Posttype Creator Subscribe - Count [Risk High]', dist='cont',
                                               daily=True, cumulative=True)
    creator_subscribe_risk_high_uu = Metric('creator_subscribe_risk_high_uu',
                                            'Posttype Creator Subscribe - UU [Risk High]', dist='bin', daily=True,
                                            cumulative=True)

    creator_open_profile_ic_verified_default_count = Metric('creator_open_profile_ic_verified_default_count',
                                                  'Posttype Creator Profile Open - Count [IC Verified Default]',
                                                  dist='cont', daily=True, cumulative=True)
    creator_open_profile_ic_verified_default_uu = Metric('creator_open_profile_ic_verified_default_uu',
                                               'Posttype Creator Profile Open - UU [IC Verified Default]', dist='bin',
                                               daily=True, cumulative=True)
    creator_view_story_ic_verified_default_count = Metric('creator_view_story_ic_verified_default_count',
                                                'Posttype Creator Story View - Count [IC Verified Default]', dist='cont',
                                                daily=True, cumulative=True)
    creator_view_story_ic_verified_default_uu = Metric('creator_view_story_ic_verified_default_uu',
                                             'Posttype Creator Story View - UU [IC Verified Default]', dist='bin',
                                             daily=True, cumulative=True)
    creator_subscribe_ic_verified_default_count = Metric('creator_subscribe_ic_verified_default_count',
                                               'Posttype Creator Subscribe - Count [IC Verified Default]', dist='cont',
                                               daily=True, cumulative=True)
    creator_subscribe_ic_verified_default_uu = Metric('creator_subscribe_ic_verified_default_uu',
                                            'Posttype Creator Subscribe - UU [IC Verified Default]', dist='bin', daily=True,
                                            cumulative=True)

    creator_open_profile_brand_count = Metric('creator_open_profile_brand_count',
                                                            'Posttype Creator Profile Open - Count [Brand]',
                                                            dist='cont', daily=True, cumulative=True)
    creator_open_profile_brand_uu = Metric('creator_open_profile_brand_uu',
                                                         'Posttype Creator Profile Open - UU [Brand]',
                                                         dist='bin',
                                                         daily=True, cumulative=True)
    creator_view_story_brand_count = Metric('creator_view_story_brand_count',
                                                          'Posttype Creator Story View - Count [Brand]',
                                                          dist='cont',
                                                          daily=True, cumulative=True)
    creator_view_story_brand_uu = Metric('creator_view_story_brand_uu',
                                                       'Posttype Creator Story View - UU [Brand]',
                                                       dist='bin',
                                                       daily=True, cumulative=True)
    creator_subscribe_brand_count = Metric('creator_subscribe_brand_count',
                                                         'Posttype Creator Subscribe - Count [Brand]',
                                                         dist='cont',
                                                         daily=True, cumulative=True)
    creator_subscribe_brand_uu = Metric('creator_subscribe_brand_uu',
                                                      'Posttype Creator Subscribe - UU [Brand]',
                                                      dist='bin', daily=True,
                                                      cumulative=True)

    creator_open_profile_avid_count = Metric('creator_open_profile_avid_count',
                                              'Posttype Creator Profile Open - Count [Avid]',
                                              dist='cont', daily=True, cumulative=True)
    creator_open_profile_avid_uu = Metric('creator_open_profile_avid_uu',
                                           'Posttype Creator Profile Open - UU [Avid]',
                                           dist='bin',
                                           daily=True, cumulative=True)
    creator_view_story_avid_count = Metric('creator_view_story_avid_count',
                                            'Posttype Creator Story View - Count [Avid]',
                                            dist='cont',
                                            daily=True, cumulative=True)
    creator_view_story_avid_uu = Metric('creator_view_story_avid_uu',
                                         'Posttype Creator Story View - UU [Avid]',
                                         dist='bin',
                                         daily=True, cumulative=True)
    creator_subscribe_avid_count = Metric('creator_subscribe_avid_count',
                                           'Posttype Creator Subscribe - Count [Avid]',
                                           dist='cont',
                                           daily=True, cumulative=True)
    creator_subscribe_avid_uu = Metric('creator_subscribe_avid_uu',
                                        'Posttype Creator Subscribe - UU [Avid]',
                                        dist='bin', daily=True,
                                        cumulative=True)

    creator_open_profile_ic_verified_tier1_count = Metric('creator_open_profile_ic_verified_tier1_count',
                                                          'Posttype Creator Profile Open - Count [IC Cerified Tier 1]',
                                                          dist='cont', daily=True, cumulative=True)
    creator_open_profile_ic_verified_tier1_uu = Metric('creator_open_profile_ic_verified_tier1_uu',
                                                       'Posttype Creator Profile Open - UU [IC Cerified Tier 1]',
                                                       dist='bin',
                                                       daily=True, cumulative=True)
    creator_view_story_ic_verified_tier1_count = Metric('creator_view_story_ic_verified_tier1_count',
                                                        'Posttype Creator Story View - Count [IC Cerified Tier 1]',
                                                        dist='cont', daily=True, cumulative=True)
    creator_view_story_ic_verified_tier1_uu = Metric('creator_view_story_ic_verified_tier1_uu',
                                                     'Posttype Creator Story View Open - UU [IC Cerified Tier 1]',
                                                     dist='bin',
                                                     daily=True, cumulative=True)
    creator_subscribe_ic_verified_tier1_count = Metric('creator_subscribe_ic_verified_tier1_count',
                                                       'Posttype Creator Subscribe - Count [IC Cerified Tier 1]',
                                                       dist='cont',
                                                       daily=True, cumulative=True)
    creator_subscribe_ic_verified_tier1_uu = Metric('creator_subscribe_ic_verified_tier1_uu',
                                                    'Posttype Creator Subscribe - UU [IC Cerified Tier 1]', dist='bin',
                                                    daily=True, cumulative=True)

    creator_open_profile_tt_verified_tier2_count = Metric('creator_open_profile_tt_verified_tier2_count',
                                                          'Posttype Creator Profile Open - Count [TT Verified Tier 2]',
                                                          dist='cont', daily=True, cumulative=True)
    creator_open_profile_tt_verified_tier2_uu = Metric('creator_open_profile_tt_verified_tier2_uu',
                                                       'Posttype Creator Profile Open - UU [TT Verified Tier 2]',
                                                       dist='bin', daily=True, cumulative=True)
    creator_view_story_tt_verified_tier2_count = Metric('creator_view_story_tt_verified_tier2_count',
                                                        'Posttype Creator Story View - Count [TT Verified Tier 2]',
                                                        dist='cont', daily=True, cumulative=True)
    creator_view_story_tt_verified_tier2_uu = Metric('creator_view_story_tt_verified_tier2_uu',
                                                     'Posttype Creator Story View - UU [TT Verified Tier 2]',
                                                     dist='bin', daily=True, cumulative=True)
    creator_subscribe_tt_verified_tier2_count = Metric('creator_subscribe_tt_verified_tier2_count',
                                                       'Posttype Creator Subscribe - Count [TT Verified Tier 2]',
                                                       dist='cont', daily=True, cumulative=True)
    creator_subscribe_tt_verified_tier2_uu = Metric('creator_subscribe_tt_verified_tier2_uu',
                                                    'Posttype Creator Subscribe - UU [TT Verified Tier 2]',
                                                    dist='bin', daily=True, cumulative=True)

    creator_open_profile_snap_star_count = Metric('creator_open_profile_snap_star_count',
                                             'Posttype Creator Profile Open - Count [Snap Star]',
                                             dist='cont', daily=True, cumulative=True)
    creator_open_profile_snap_star_uu = Metric('creator_open_profile_snap_star_uu',
                                          'Posttype Creator Profile Open - UU [Snap Star]',
                                          dist='bin',
                                          daily=True, cumulative=True)
    creator_view_story_snap_star_count = Metric('creator_view_story_snap_star_count',
                                           'Posttype Creator Story View - Count [Snap Star]',
                                           dist='cont',
                                           daily=True, cumulative=True)
    creator_view_story_snap_star_uu = Metric('creator_view_story_snap_star_uu',
                                        'Posttype Creator Story View - UU [Snap Star]',
                                        dist='bin',
                                        daily=True, cumulative=True)
    creator_subscribe_snap_star_count = Metric('creator_subscribe_snap_star_count',
                                          'Posttype Creator Subscribe - Count [Snap Star]',
                                          dist='cont',
                                          daily=True, cumulative=True)
    creator_subscribe_snap_star_uu = Metric('creator_subscribe_snap_star_uu',
                                       'Posttype Creator Subscribe - UU [Snap Star]',
                                       dist='bin', daily=True,
                                       cumulative=True)

    creator_open_profile_verified_count = Metric('creator_open_profile_verified_count',
                                                  'Posttype Creator Profile Open - Count [Verified]',
                                                  dist='cont', daily=True, cumulative=True)
    creator_open_profile_verified_uu = Metric('creator_open_profile_verified_uu',
                                               'Posttype Creator Profile Open - UU [Verified]',
                                               dist='bin',
                                               daily=True, cumulative=True)
    creator_view_story_verified_count = Metric('creator_view_story_verified_count',
                                                'Posttype Creator Story View - Count [Verified]',
                                                dist='cont',
                                                daily=True, cumulative=True)
    creator_view_story_verified_uu = Metric('creator_view_story_verified_uu',
                                             'Posttype Creator Story View - UU [Verified]',
                                             dist='bin',
                                             daily=True, cumulative=True)
    creator_subscribe_verified_count = Metric('creator_subscribe_verified_count',
                                               'Posttype Creator Subscribe - Count [Verified]',
                                               dist='cont',
                                               daily=True, cumulative=True)
    creator_subscribe_verified_uu = Metric('creator_subscribe_verified_uu',
                                            'Posttype Creator Subscribe - UU [Verified]',
                                            dist='bin', daily=True,
                                            cumulative=True)

    creator_open_profile_subtier_unknown_count = Metric('creator_open_profile_subtier_unknown_count',
                                                 'Posttype Creator Profile Open - Count [Subtier Unknown]',
                                                 dist='cont', daily=True, cumulative=True)
    creator_open_profile_subtier_unknown_uu = Metric('creator_open_profile_subtier_unknown_uu',
                                              'Posttype Creator Profile Open - UU [Subtier Unknown]',
                                              dist='bin',
                                              daily=True, cumulative=True)
    creator_view_story_subtier_unknown_count = Metric('creator_view_story_subtier_unknown_count',
                                               'Posttype Creator Story View - Count [Subtier Unknown]',
                                               dist='cont',
                                               daily=True, cumulative=True)
    creator_view_story_subtier_unknown_uu = Metric('creator_view_story_subtier_unknown_uu',
                                            'Posttype Creator Story View - UU [Subtier Unknown]',
                                            dist='bin',
                                            daily=True, cumulative=True)
    creator_subscribe_subtier_unknown_count = Metric('creator_subscribe_subtier_unknown_count',
                                              'Posttype Creator Subscribe - Count [Subtier Unknown]',
                                              dist='cont',
                                              daily=True, cumulative=True)
    creator_subscribe_subtier_unknown_uu = Metric('creator_subscribe_subtier_unknown_uu',
                                           'Posttype Creator Subscribe - UU [Subtier Unknown]',
                                           dist='bin', daily=True,
                                           cumulative=True)

    creator_open_profile_publisher_count = Metric('creator_open_profile_publisher_count',
                                                        'Posttype Creator Profile Open - Count [Publisher]',
                                                        dist='cont', daily=True, cumulative=True)
    creator_open_profile_publisher_uu = Metric('creator_open_profile_publisher_uu',
                                                     'Posttype Creator Profile Open - UU [Publisher]',
                                                     dist='bin',
                                                     daily=True, cumulative=True)
    creator_view_story_publisher_count = Metric('creator_view_story_publisher_count',
                                                      'Posttype Creator Story View - Count [Publisher]',
                                                      dist='cont',
                                                      daily=True, cumulative=True)
    creator_view_story_publisher_uu = Metric('creator_view_story_publisher_uu',
                                                   'Posttype Creator Story View - UU [Publisher]',
                                                   dist='bin',
                                                   daily=True, cumulative=True)
    creator_subscribe_publisher_count = Metric('creator_subscribe_publisher_count',
                                                     'Posttype Creator Subscribe - Count [Publisher]',
                                                     dist='cont',
                                                     daily=True, cumulative=True)
    creator_subscribe_publisher_uu = Metric('creator_subscribe_publisher_uu',
                                                  'Posttype Creator Subscribe - UU [Publisher]',
                                                  dist='bin', daily=True,
                                                  cumulative=True)

    creator_open_profile_content_aggregator_count = Metric('creator_open_profile_content_aggregator_count',
                                                  'Posttype Creator Profile Open - Count [Content Aggregator]',
                                                  dist='cont', daily=True, cumulative=True)
    creator_open_profile_content_aggregator_uu = Metric('creator_open_profile_content_aggregator_uu',
                                               'Posttype Creator Profile Open - UU [Content Aggregator]',
                                               dist='bin',
                                               daily=True, cumulative=True)
    creator_view_story_content_aggregator_count = Metric('creator_view_story_content_aggregator_count',
                                                'Posttype Creator Story View - Count [Content Aggregator]',
                                                dist='cont',
                                                daily=True, cumulative=True)
    creator_view_story_content_aggregator_uu = Metric('creator_view_story_content_aggregator_uu',
                                             'Posttype Creator Story View - UU [Content Aggregator]',
                                             dist='bin',
                                             daily=True, cumulative=True)
    creator_subscribe_content_aggregator_count = Metric('creator_subscribe_content_aggregator_count',
                                               'Posttype Creator Subscribe - Count [Content Aggregator]',
                                               dist='cont',
                                               daily=True, cumulative=True)
    creator_subscribe_content_aggregator_uu = Metric('creator_subscribe_content_aggregator_uu',
                                            'Posttype Creator Subscribe - UU [Content Aggregator]',
                                            dist='bin', daily=True,
                                            cumulative=True)

    mt = MetricTable(
        sql="""
        SELECT 
            a.event_date as ts,
            a.ghost_user_id,

            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'EMERGING') as creator_open_profile_emerging_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'EMERGING')>0, 1, 0) as creator_open_profile_emerging_uu,
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'OCCASIONAL') as creator_open_profile_occasional_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'OCCASIONAL')>0, 1, 0) as creator_open_profile_occasional_uu,
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'IMPERSONATOR') as creator_open_profile_impersonator_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'IMPERSONATOR')>0, 1, 0) as creator_open_profile_impersonator_uu,
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'IC_VERIFIED_TIER2') as creator_open_profile_ic_verified_tier2_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'IC_VERIFIED_TIER2')>0, 1, 0) as creator_open_profile_ic_verified_tier2_uu,
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'TT_VERIFIED_TIER1') as creator_open_profile_tt_verified_tier1_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'TT_VERIFIED_TIER1')>0, 1, 0) as creator_open_profile_tt_verified_tier1_uu,
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'IC_VERIFIED_TIER1') as creator_open_profile_ic_verified_tier1_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'IC_VERIFIED_TIER1')>0, 1, 0) as creator_open_profile_ic_verified_tier1_uu,
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'TT_VERIFIED_TIER2') as creator_open_profile_tt_verified_tier2_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'TT_VERIFIED_TIER2')>0, 1, 0) as creator_open_profile_tt_verified_tier2_uu,            
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'HOMEGROWN') as creator_open_profile_homegrown_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'HOMEGROWN')>0, 1, 0) as creator_open_profile_homegrown_uu,
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'RISK_HIGH') as creator_open_profile_risk_high_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'RISK_HIGH')>0, 1, 0) as creator_open_profile_risk_high_uu,
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'IC_VERIFIED_DEFAULT') as creator_open_profile_ic_verified_default_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'IC_VERIFIED_DEFAULT')>0, 1, 0) as creator_open_profile_ic_verified_default_uu,
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'BRAND') as creator_open_profile_brand_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'BRAND')>0, 1, 0) as creator_open_profile_brand_uu,
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'AVID') as creator_open_profile_avid_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'AVID')>0, 1, 0) as creator_open_profile_avid_uu,
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'SNAP_STAR') as creator_open_profile_snap_star_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'SNAP_STAR')>0, 1, 0) as creator_open_profile_snap_star_uu,
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'VERIFIED') as creator_open_profile_verified_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'VERIFIED')>0, 1, 0) as creator_open_profile_verified_uu,
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'SUBTIER_UNKNOWN') as creator_open_profile_subtier_unknown_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'SUBTIER_UNKNOWN')>0, 1, 0) as creator_open_profile_subtier_unknown_uu,
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'PUBLISHER') as creator_open_profile_publisher_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'PUBLISHER')>0, 1, 0) as creator_open_profile_publisher_uu,
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'CONTENT_AGGREGATOR') as creator_open_profile_content_aggregator_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'CONTENT_AGGREGATOR')>0, 1, 0) as creator_open_profile_content_aggregator_uu,

            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'EMERGING') as creator_view_story_emerging_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'EMERGING')>0, 1, 0) as creator_view_story_emerging_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'OCCASIONAL') as creator_view_story_occasional_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'OCCASIONAL')>0, 1, 0) as creator_view_story_occasional_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'IMPERSONATOR') as creator_view_story_impersonator_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'IMPERSONATOR')>0, 1, 0) as creator_view_story_impersonator_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'IC_VERIFIED_TIER2') as creator_view_story_ic_verified_tier2_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'IC_VERIFIED_TIER2')>0, 1, 0) as creator_view_story_ic_verified_tier2_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'TT_VERIFIED_TIER1') as creator_view_story_tt_verified_tier1_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'TT_VERIFIED_TIER1')>0, 1, 0) as creator_view_story_tt_verified_tier1_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'IC_VERIFIED_TIER1') as creator_view_story_ic_verified_tier1_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'IC_VERIFIED_TIER1')>0, 1, 0) as creator_view_story_ic_verified_tier1_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'TT_VERIFIED_TIER2') as creator_view_story_tt_verified_tier2_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'TT_VERIFIED_TIER2')>0, 1, 0) as creator_view_story_tt_verified_tier2_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'HOMEGROWN') as creator_view_story_homegrown_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'HOMEGROWN')>0, 1, 0) as creator_view_story_homegrown_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'RISK_HIGH') as creator_view_story_risk_high_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'RISK_HIGH')>0, 1, 0) as creator_view_story_risk_high_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'IC_VERIFIED_DEFAULT') as creator_view_story_ic_verified_default_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'IC_VERIFIED_DEFAULT')>0, 1, 0) as creator_view_story_ic_verified_default_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'BRAND') as creator_view_story_brand_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'BRAND')>0, 1, 0) as creator_view_story_brand_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'AVID') as creator_view_story_avid_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'AVID')>0, 1, 0) as creator_view_story_avid_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'SNAP_STAR') as creator_view_story_snap_star_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'SNAP_STAR')>0, 1, 0) as creator_view_story_snap_star_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'VERIFIED') as creator_view_story_verified_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'VERIFIED')>0, 1, 0) as creator_view_story_verified_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'SUBTIER_UNKNOWN') as creator_view_story_subtier_unknown_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'SUBTIER_UNKNOWN')>0, 1, 0) as creator_view_story_subtier_unknown_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'PUBLISHER') as creator_view_story_publisher_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'PUBLISHER')>0, 1, 0) as creator_view_story_publisher_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'CONTENT_AGGREGATOR') as creator_view_story_content_aggregator_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'CONTENT_AGGREGATOR')>0, 1, 0) as creator_view_story_content_aggregator_uu,

            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'EMERGING') as creator_subscribe_emerging_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'EMERGING')>0, 1, 0) as creator_subscribe_emerging_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'OCCASIONAL') as creator_subscribe_occasional_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'OCCASIONAL')>0, 1, 0) as creator_subscribe_occasional_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'IMPERSONATOR') as creator_subscribe_impersonator_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'IMPERSONATOR')>0, 1, 0) as creator_subscribe_impersonator_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'IC_VERIFIED_TIER2') as creator_subscribe_ic_verified_tier2_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'IC_VERIFIED_TIER2')>0, 1, 0) as creator_subscribe_ic_verified_tier2_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'TT_VERIFIED_TIER1') as creator_subscribe_tt_verified_tier1_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'TT_VERIFIED_TIER1')>0, 1, 0) as creator_subscribe_tt_verified_tier1_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'IC_VERIFIED_TIER1') as creator_subscribe_ic_verified_tier1_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'IC_VERIFIED_TIER1')>0, 1, 0) as creator_subscribe_ic_verified_tier1_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'TT_VERIFIED_TIER2') as creator_subscribe_tt_verified_tier2_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'TT_VERIFIED_TIER2')>0, 1, 0) as creator_subscribe_tt_verified_tier2_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'HOMEGROWN') as creator_subscribe_homegrown_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'HOMEGROWN')>0, 1, 0) as creator_subscribe_homegrown_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'RISK_HIGH') as creator_subscribe_risk_high_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'RISK_HIGH')>0, 1, 0) as creator_subscribe_risk_high_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'IC_VERIFIED_DEFAULT') as creator_subscribe_ic_verified_default_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'IC_VERIFIED_DEFAULT')>0, 1, 0) as creator_subscribe_ic_verified_default_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'BRAND') as creator_subscribe_brand_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'BRAND')>0, 1, 0) as creator_subscribe_brand_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'AVID') as creator_subscribe_avid_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'AVID')>0, 1, 0) as creator_subscribe_avid_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'SNAP_STAR') as creator_subscribe_snap_star_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'SNAP_STAR')>0, 1, 0) as creator_subscribe_snap_star_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'VERIFIED') as creator_subscribe_verified_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'VERIFIED')>0, 1, 0) as creator_subscribe_verified_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'SUBTIER_UNKNOWN') as creator_subscribe_subtier_unknown_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'SUBTIER_UNKNOWN')>0, 1, 0) as creator_subscribe_subtier_unknown_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'PUBLISHER') as creator_subscribe_publisher_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'PUBLISHER')>0, 1, 0) as creator_subscribe_publisher_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'CONTENT_AGGREGATOR') as creator_subscribe_content_aggregator_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__subtier = 'CONTENT_AGGREGATOR')>0, 1, 0) as creator_subscribe_content_aggregator_uu,

        FROM `sc-analytics.report_search_v2.search_action_20*` a
            LEFT OUTER JOIN `sc-analytics.report_creator.creator_profile_20*` cp
              ON a._TABLE_SUFFIX = cp._TABLE_SUFFIX 
              AND REGEXP_EXTRACT(a.search_result_identifier, r"^\d+::(.+?)::.*$") = cp.BusinessProfileId
            LEFT OUTER JOIN `context-pii.creator_profile.creator_profile_v1_prod_20*` b
                on cp._TABLE_SUFFIX = b._TABLE_SUFFIX
                and cp.user_id = b.creator_user_id
        WHERE
            a._TABLE_SUFFIX BETWEEN '{start_trunc}' AND '{end_trunc}'
            AND REGEXP_EXTRACT(a.search_result_identifier, r"^(.+?):") = '2'
            AND is_pretype='PostType'
            AND source='SEARCH_UNSPECIFIED'
        GROUP BY 1,2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
        ),
        metrics=[
            creator_open_profile_emerging_count,
            creator_open_profile_emerging_uu,
            creator_view_story_emerging_count,
            creator_view_story_emerging_uu,
            creator_subscribe_emerging_count,
            creator_subscribe_emerging_uu,

            creator_open_profile_occasional_count,
            creator_open_profile_occasional_uu,
            creator_view_story_occasional_count,
            creator_view_story_occasional_uu,
            creator_subscribe_occasional_count,
            creator_subscribe_occasional_uu,

            creator_open_profile_impersonator_count,
            creator_open_profile_impersonator_uu,
            creator_view_story_impersonator_count,
            creator_view_story_impersonator_uu,
            creator_subscribe_impersonator_count,
            creator_subscribe_impersonator_uu,

            creator_open_profile_ic_verified_tier2_count,
            creator_open_profile_ic_verified_tier2_uu,
            creator_view_story_ic_verified_tier2_count,
            creator_view_story_ic_verified_tier2_uu,
            creator_subscribe_ic_verified_tier2_count,
            creator_subscribe_ic_verified_tier2_uu,

            creator_open_profile_tt_verified_tier1_count,
            creator_open_profile_tt_verified_tier1_uu,
            creator_view_story_tt_verified_tier1_count,
            creator_view_story_tt_verified_tier1_uu,
            creator_subscribe_tt_verified_tier1_count,
            creator_subscribe_tt_verified_tier1_uu,

            creator_open_profile_ic_verified_tier1_count,
            creator_open_profile_ic_verified_tier1_uu,
            creator_view_story_ic_verified_tier1_count,
            creator_view_story_ic_verified_tier1_uu,
            creator_subscribe_ic_verified_tier1_count,
            creator_subscribe_ic_verified_tier1_uu,

            creator_open_profile_tt_verified_tier2_count,
            creator_open_profile_tt_verified_tier2_uu,
            creator_view_story_tt_verified_tier2_count,
            creator_view_story_tt_verified_tier2_uu,
            creator_subscribe_tt_verified_tier2_count,
            creator_subscribe_tt_verified_tier2_uu,

            creator_open_profile_homegrown_count,
            creator_open_profile_homegrown_uu,
            creator_view_story_homegrown_count,
            creator_view_story_homegrown_uu,
            creator_subscribe_homegrown_count,
            creator_subscribe_homegrown_uu,

            creator_open_profile_risk_high_count,
            creator_open_profile_risk_high_uu,
            creator_view_story_risk_high_count,
            creator_view_story_risk_high_uu,
            creator_subscribe_risk_high_count,
            creator_subscribe_risk_high_uu,

            creator_open_profile_ic_verified_default_count,
            creator_open_profile_ic_verified_default_uu,
            creator_view_story_ic_verified_default_count,
            creator_view_story_ic_verified_default_uu,
            creator_subscribe_ic_verified_default_count,
            creator_subscribe_ic_verified_default_uu,

            creator_open_profile_brand_count,
            creator_open_profile_brand_uu,
            creator_view_story_brand_count,
            creator_view_story_brand_uu,
            creator_subscribe_brand_count,
            creator_subscribe_brand_uu,

            creator_open_profile_avid_count,
            creator_open_profile_avid_uu,
            creator_view_story_avid_count,
            creator_view_story_avid_uu,
            creator_subscribe_avid_count,
            creator_subscribe_avid_uu,

            creator_open_profile_snap_star_count,
            creator_open_profile_snap_star_uu,
            creator_view_story_snap_star_count,
            creator_view_story_snap_star_uu,
            creator_subscribe_snap_star_count,
            creator_subscribe_snap_star_uu,

            creator_open_profile_verified_count,
            creator_open_profile_verified_uu,
            creator_view_story_verified_count,
            creator_view_story_verified_uu,
            creator_subscribe_verified_count,
            creator_subscribe_verified_uu,

            creator_open_profile_subtier_unknown_count,
            creator_open_profile_subtier_unknown_uu,
            creator_view_story_subtier_unknown_count,
            creator_view_story_subtier_unknown_uu,
            creator_subscribe_subtier_unknown_count,
            creator_subscribe_subtier_unknown_uu,

            creator_open_profile_publisher_count,
            creator_open_profile_publisher_uu,
            creator_view_story_publisher_count,
            creator_view_story_publisher_uu,
            creator_subscribe_publisher_count,
            creator_subscribe_publisher_uu,

            creator_open_profile_content_aggregator_count,
            creator_open_profile_content_aggregator_uu,
            creator_view_story_content_aggregator_count,
            creator_view_story_content_aggregator_uu,
            creator_subscribe_content_aggregator_count,
            creator_subscribe_content_aggregator_uu,
        ],
        name='posttype_real_creator_subtier',
        bq_dialect='standard'
    )

    return mt

def posttype_is_real_creator(start_date, end_date):
    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    creator_open_profile_is_real_creator_count = Metric('creator_open_profile_is_real_creator_count',
                                               'Posttype Creator Profile Open - Count [Is Real Creator]', dist='cont',
                                               daily=True, cumulative=True)
    creator_open_profile_is_real_creator_uu = Metric('creator_open_profile_is_real_creator_uu',
                                            'Posttype Creator Profile Open - UU [Is Real Creator]', dist='bin', daily=True,
                                            cumulative=True)
    creator_view_story_is_real_creator_count = Metric('creator_view_story_is_real_creator_count',
                                             'Posttype Creator Story View - Count [Is Real Creator]', dist='cont', daily=True,
                                             cumulative=True)
    creator_view_story_is_real_creator_uu = Metric('creator_view_story_is_real_creator_uu', 'Posttype Creator Story View - UU [Is Real Creator]',
                                          dist='bin', daily=True, cumulative=True)
    creator_subscribe_is_real_creator_count = Metric('creator_subscribe_is_real_creator_count',
                                            'Posttype Creator Subscribe - Count [Is Real Creator]', dist='cont', daily=True,
                                            cumulative=True)
    creator_subscribe_is_real_creator_uu = Metric('creator_subscribe_is_real_creator_uu', 'Posttype Creator Subscribe - UU [Is Real Creator]',
                                         dist='bin', daily=True, cumulative=True)

    creator_open_profile_is_not_real_creator_count = Metric('creator_open_profile_is_not_real_creator_count',
                                                 'Posttype Creator Profile Open - Count [Not Real Creator]', dist='cont',
                                                 daily=True, cumulative=True)
    creator_open_profile_is_not_real_creator_uu = Metric('creator_open_profile_is_not_real_creator_uu',
                                              'Posttype Creator Profile Open - UU [Not Real Creator]', dist='bin', daily=True,
                                              cumulative=True)
    creator_view_story_is_not_real_creator_count = Metric('creator_view_story_is_not_real_creator_count',
                                               'Posttype Creator Story View - Count [Not Real Creator]', dist='cont',
                                               daily=True, cumulative=True)
    creator_view_story_is_not_real_creator_uu = Metric('creator_view_story_is_not_real_creator_uu',
                                            'Posttype Creator View Story - UU [Not Real Creator]', dist='bin', daily=True,
                                            cumulative=True)
    creator_subscribe_is_not_real_creator_count = Metric('creator_subscribe_is_not_real_creator_count',
                                              'Posttype Creator Subscribe - Count [Not Real Creator]', dist='cont', daily=True,
                                              cumulative=True)
    creator_subscribe_is_not_real_creator_uu = Metric('creator_subscribe_is_not_real_creator_uu',
                                           'Posttype Creator Subscribe - UU [Not Real Creator]', dist='bin', daily=True,
                                           cumulative=True)

    mt = MetricTable(
        sql="""
        SELECT 
            a.event_date as ts,
            a.ghost_user_id,

            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__is_real_creator is True) as creator_open_profile_is_real_creator_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__is_real_creator is True)>0, 1, 0) as creator_open_profile_is_real_creator_uu,
            COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__is_real_creator is False) as creator_open_profile_is_not_real_creator_count,
            IF(COUNTIF(a.action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' AND b.creator_tier__is_real_creator is False)>0, 1, 0) as creator_open_profile_is_not_real_creator_uu,

            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__is_real_creator is True) as creator_view_story_is_real_creator_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__is_real_creator is True)>0, 1, 0) as creator_view_story_is_real_creator_uu,
            COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__is_real_creator is False) as creator_view_story_is_not_real_creator_count,
            IF(COUNTIF(a.action='OPEN_USER_STORY_FROM_SEARCH_RESULT' AND b.creator_tier__is_real_creator is False)>0, 1, 0) as creator_view_story_is_not_real_creator_uu,

            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__is_real_creator is True) as creator_subscribe_is_real_creator_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__is_real_creator is True)>0, 1, 0) as creator_subscribe_is_real_creator_uu,
            COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__is_real_creator is False) as creator_subscribe_is_not_real_creator_count,
            IF(COUNTIF(a.action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' AND b.creator_tier__is_real_creator is False)>0, 1, 0) as creator_subscribe_is_not_real_creator_uu,

        FROM `sc-analytics.report_search_v2.search_action_20*` a
            LEFT OUTER JOIN `sc-analytics.report_creator.creator_profile_20*` cp
              ON a._TABLE_SUFFIX = cp._TABLE_SUFFIX 
              AND REGEXP_EXTRACT(a.search_result_identifier, r"^\d+::(.+?)::.*$") = cp.BusinessProfileId
            LEFT OUTER JOIN `context-pii.creator_profile.creator_profile_v1_prod_20*` b
                on cp._TABLE_SUFFIX = b._TABLE_SUFFIX
                and cp.user_id = b.creator_user_id
        WHERE
            a._TABLE_SUFFIX BETWEEN '{start_trunc}' AND '{end_trunc}'
            AND REGEXP_EXTRACT(a.search_result_identifier, r"^(.+?):") = '2'
            AND is_pretype='PostType'
            AND source='SEARCH_UNSPECIFIED'
        GROUP BY 1,2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
        ),
        metrics=[
            creator_open_profile_is_real_creator_count,
            creator_open_profile_is_real_creator_uu,
            creator_view_story_is_real_creator_count,
            creator_view_story_is_real_creator_uu,
            creator_subscribe_is_real_creator_count,
            creator_subscribe_is_real_creator_uu,

            creator_open_profile_is_not_real_creator_count,
            creator_open_profile_is_not_real_creator_uu,
            creator_view_story_is_not_real_creator_count,
            creator_view_story_is_not_real_creator_uu,
            creator_subscribe_is_not_real_creator_count,
            creator_subscribe_is_not_real_creator_uu,
        ],
        name='posttype_is_real_creator',
        bq_dialect='standard'
    )

    return mt

def subscribe_action_public_tier(start_date, end_date):
    """
    Subscribe Section Interaction, broken down by verified 
    """

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]


    spt_event_cnt = Metric(
        'spt_event_cnt',
        'Popular Accts: Public Tier: Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    spt_user_story_cnt = Metric(
        'spt_user_story_cnt',
        'Popular Accts: Public Tier: Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )


    spt_friend_profile_cnt = Metric(
        'spt_friend_profile_cnt',
        'Popular Accts: Public Tier: Profile Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    spt_add_verified_cnt = Metric(
        'spt_add_verified_cnt',
        'Popular Accts: Public Tier: Subscribe To Account - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    spt_user_story_uu = Metric(
      'spt_user_story_uu',
        'Popular Accts: Public Tier: Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )


    spt_friend_profile_uu = Metric(
        'spt_friend_profile_uu',
        'Popular Accts: Public Tier: Profile Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    spt_add_verified_uu = Metric(
        'spt_add_verified_uu',
        'Popular Accts: Public Tier: Subscribe To Account - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )


    mt = MetricTable(
        sql="""
        SELECT
          event_date AS ts,
          ghost_user_id AS ghost_user_id,
          sum(1) spt_event_cnt,
          sum(case when action='OPEN_USER_STORY_FROM_SEARCH_RESULT' then 1 else 0 end) spt_user_story_cnt,
          sum(case when action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) spt_friend_profile_cnt,
          sum(case when action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' then 1 else 0 end) spt_add_verified_cnt,
          IF(sum(case when action='OPEN_USER_STORY_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) spt_user_story_uu,
          IF(sum(case when action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) spt_friend_profile_uu,
          IF(sum(case when action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) spt_add_verified_uu
        FROM `sc-analytics.report_search_v2.search_action_20*` a
            LEFT OUTER JOIN `feelinsonice-hrd.snap_pro.snap_pro_profiles_with_unlinked_20*` b
                ON a._TABLE_SUFFIX = b._TABLE_SUFFIX AND REGEXP_EXTRACT(a.search_result_identifier, r"^\d+::(.+?)::.*$")=b.businessprofileid 
        WHERE
            a._TABLE_SUFFIX >= '{start_trunc}' AND
            a._TABLE_SUFFIX <= '{end_trunc}'
            AND search_query_context not in ('MAPS_SCREEN_FRIEND_FINDER','EAGLE_SEARCH')
            AND source='SEARCH_UNSPECIFIED'
            AND search_result_section='OFFICIAL_STORIES'
            AND tier='TIER_PUBLIC'
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            spt_event_cnt,
            spt_user_story_cnt,
            spt_friend_profile_cnt,
            spt_add_verified_cnt,
            spt_user_story_uu,
            spt_friend_profile_uu,
            spt_add_verified_uu
        ],
        name="subscribe_action_account_type_breakdown",
        bq_dialect='standard'

    )
    return mt

def subscribe_action_official_tier(start_date, end_date):
    """
    Subscribe Section Interaction, broken down by verified 
    """

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]


    sot_event_cnt = Metric(
        'sot_event_cnt',
        'Popular Accts: Official Tier: Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    sot_user_story_cnt = Metric(
        'sot_user_story_cnt',
        'Popular Accts: Official Tier: Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )


    sot_friend_profile_cnt = Metric(
        'sot_friend_profile_cnt',
        'Popular Accts: Official Tier: Profile Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    sot_add_verified_cnt = Metric(
        'sot_add_verified_cnt',
        'Popular Accts: Official Tier: Subscribe To Account - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    sot_user_story_uu = Metric(
      'sot_user_story_uu',
        'Popular Accts: Official Tier: Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )


    sot_friend_profile_uu = Metric(
        'sot_friend_profile_uu',
        'Popular Accts: Official Tier: Profile Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    sot_add_verified_uu = Metric(
        'sot_add_verified_uu',
        'Popular Accts: Official Tier: Subscribe To Account - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    sotbc_event_cnt = Metric(
        'sotbc_event_cnt',
        'Popular Accts: Official Tier, Business Category: Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    sotbc_user_story_cnt = Metric(
        'sotbc_user_story_cnt',
        'Popular Accts: Official Tier, Business Category: Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )


    sotbc_friend_profile_cnt = Metric(
        'sotbc_friend_profile_cnt',
        'Popular Accts: Official Tier, Business Category: Profile Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    sotbc_add_verified_cnt = Metric(
        'sotbc_add_verified_cnt',
        'Popular Accts: Official Tier, Business Category: Subscribe To Account - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    sotbc_user_story_uu = Metric(
      'sotbc_user_story_uu',
        'Popular Accts: Official Tier, Business Category: Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )


    sotbc_friend_profile_uu = Metric(
        'sotbc_friend_profile_uu',
        'Popular Accts: Official Tier, Business Category: Profile Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    sotbc_add_verified_uu = Metric(
        'sotbc_add_verified_uu',
        'Popular Accts: Official Tier, Business Category: Subscribe To Account - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    mt = MetricTable(
        sql="""
        SELECT
            event_date AS ts,
            ghost_user_id AS ghost_user_id,
            sum(1) sot_event_cnt,
            sum(case when action='OPEN_USER_STORY_FROM_SEARCH_RESULT' then 1 else 0 end) sot_user_story_cnt,
            sum(case when action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) sot_friend_profile_cnt,
            sum(case when action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' then 1 else 0 end) sot_add_verified_cnt,
            IF(sum(case when action='OPEN_USER_STORY_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) sot_user_story_uu,
            IF(sum(case when action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) sot_friend_profile_uu,
            IF(sum(case when action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) sot_add_verified_uu,

            sum(case when Internalcategory='BUSINESS' then 1 end) sotbc_event_cnt,
            sum(case when Internalcategory='BUSINESS' and action='OPEN_USER_STORY_FROM_SEARCH_RESULT' then 1 else 0 end) sotbc_user_story_cnt,
            sum(case when Internalcategory='BUSINESS' and action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end) sotbc_friend_profile_cnt,
            sum(case when Internalcategory='BUSINESS' and action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' then 1 else 0 end) sotbc_add_verified_cnt,
            IF(sum(case when Internalcategory='BUSINESS' and action='OPEN_USER_STORY_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) sotbc_user_story_uu,
            IF(sum(case when Internalcategory='BUSINESS' and action='OPEN_FRIENDING_PROFILE_VIEW_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) sotbc_friend_profile_uu,
            IF(sum(case when Internalcategory='BUSINESS' and action='ADD_VERIFIED_USER_AS_FRIEND_FROM_SEARCH_RESULT' then 1 else 0 end)>0,1,0) sotbc_add_verified_uu

        FROM `sc-analytics.report_search_v2.search_action_20*` a
            LEFT OUTER JOIN `feelinsonice-hrd.snap_pro.snap_pro_profiles_with_unlinked_20*` b
                ON a._TABLE_SUFFIX = b._TABLE_SUFFIX AND REGEXP_EXTRACT(a.search_result_identifier, r"^\d+::(.+?)::.*$")=b.businessprofileid 
        WHERE
            a._TABLE_SUFFIX >= '{start_trunc}' AND
            a._TABLE_SUFFIX <= '{end_trunc}'
            AND search_query_context not in ('MAPS_SCREEN_FRIEND_FINDER','EAGLE_SEARCH')
            AND source='SEARCH_UNSPECIFIED'
            AND search_result_section='OFFICIAL_STORIES'
            AND tier='TIER_PUBLIC_OFFICIAL'
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            sot_event_cnt,
            sot_user_story_cnt,
            sot_friend_profile_cnt,
            sot_add_verified_cnt,
            sot_user_story_uu,
            sot_friend_profile_uu,
            sot_add_verified_uu,

            sotbc_event_cnt,
            sotbc_user_story_cnt,
            sotbc_friend_profile_cnt,
            sotbc_add_verified_cnt,
            sotbc_user_story_uu,
            sotbc_friend_profile_uu,
            sotbc_add_verified_uu,
        ],
        name="subscribe_action_account_type_breakdown",
        bq_dialect='standard'

    )
    return mt

def topics_action(start_date, end_date):
    """
    Places Section Interaction
    """

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    t_open_topic_cnt = Metric(
        't_open_topic_cnt',
        'Topics: Topic Open - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    t_open_topic_uu = Metric(
        't_open_topic_uu',
        'Topics: Topic Open - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    mt = MetricTable(
        sql="""
        SELECT
          event_date AS ts,
          ghost_user_id AS ghost_user_id,
          sum(case when action='OPEN_TOPIC_PAGE' then 1 else 0 end) t_open_topic_cnt,
          IF(sum(case when action='OPEN_TOPIC_PAGE' then 1 else 0 end)>0,1,0) t_open_topic_uu,
        FROM `sc-analytics.report_search_v2.search_action_20*`
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND
            _TABLE_SUFFIX <= '{end_trunc}'
        AND source='SEARCH_UNSPECIFIED'
        AND search_result_section='TOPICS'
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            t_open_topic_cnt,
            t_open_topic_uu,
        ],
        name="topics_action",
        bq_dialect='standard'

    )
    return mt

def spotlight_section_action(start_date, end_date):
    """
    Spotlight Section Action breakdown
    """

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    spotlight_open_story_cnt = Metric(
        'spotlight_open_story_cnt',
        'Spotlight: Non-Playlist Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    spotlight_open_story_uu = Metric(
        'spotlight_open_story_uu',
        'Spotlight: Non-Playlist Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    spotlight_open_story_from_playlist_cnt = Metric(
        'spotlight_open_story_from_playlist_cnt',
        'Spotlight: Playlist Story View - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    spotlight_open_story_from_playlist_uu = Metric(
        'spotlight_open_story_from_playlist_uu',
        'Spotlight: Playlist Story View - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    mt = MetricTable(
        sql="""
        SELECT
            event_date AS ts,
            ghost_user_id AS ghost_user_id,
            SUM(CASE WHEN action='OPEN_SPOTLIGHT_STORY_FROM_SEARCH_RESULT' THEN 1 ELSE 0 END) spotlight_open_story_cnt,
            IF(SUM(CASE WHEN action='OPEN_SPOTLIGHT_STORY_FROM_SEARCH_RESULT' THEN 1 ELSE 0 END)>0,1,0) spotlight_open_story_uu,
            SUM(CASE WHEN action='OPEN_SPOTLIGHT_STORY_FROM_SEARCH_RESULT_PLAYLIST' THEN 1 ELSE 0 END) spotlight_open_story_from_playlist_cnt,
            IF(SUM(CASE WHEN action='OPEN_SPOTLIGHT_STORY_FROM_SEARCH_RESULT_PLAYLIST' THEN 1 ELSE 0 END)>0,1,0) spotlight_open_story_from_playlist_uu,
        FROM `sc-analytics.report_search_v2.search_action_20*`
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND
            _TABLE_SUFFIX <= '{end_trunc}'
        AND source='SEARCH_UNSPECIFIED'
        AND search_result_section='SPOTLIGHT'
        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            spotlight_open_story_cnt,
            spotlight_open_story_uu,
            spotlight_open_story_from_playlist_cnt,
            spotlight_open_story_from_playlist_uu
        ],
        name="spotlight_section_action",
        bq_dialect='standard'

    )
    return mt




def spotlight_section_second_order_action(start_date, end_date):
    """
    Spotlight Section Second Order Action breakdown
    """

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    spotlight_story_view_time_second = Metric(
        'spotlight_story_view_time_second',
        'Spotlight: Story View Time (Second)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    spotlight_open_profile = Metric(
        'spotlight_open_profile',
        'Spotlight: Open Profile Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    spotlight_favorite = Metric(
        'spotlight_favorite',
        'Spotlight: Favorite Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    spotlight_subscribe = Metric(
        'spotlight_subscribe',
        'Spotlight: Subscribe Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    spotlight_open_replies = Metric(
        'spotlight_open_replies',
        'Spotlight: Open Reply Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    spotlight_send = Metric(
        'spotlight_send',
        'Spotlight: Send Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    spotlight_zero_second_story_view = Metric(
        'spotlight_zero_second_story_view',
        'Spotlight: Zero Second Story View Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    spotlight_2_plus_second_story_view = Metric(
        'spotlight_2_plus_second_story_view',
        'Spotlight: 2+ Seconds Story View Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    spotlight_block = Metric(
        'spotlight_block',
        'Spotlight Blocks from Search',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    spotlight_report = Metric(
        'spotlight_report',
        'Spotlight Reports from Search',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    spotlight_block_rate = Metric(
        'spotlight_block_rate',
        'Search Spotlight Blocks per Action',
        numerator='spotlight_block',
        denominator='total_spotlight_actions',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    spotlight_report_rate = Metric(
        'spotlight_report_rate',
        'Search Spotlight Reports per Action',
        numerator='spotlight_report',
        denominator='total_spotlight_actions',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    spotlight_total_actions = Metric(
        'total_spotlight_actions',
        'Total Spotlight Actions from Search',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    mt = MetricTable(
        sql="""
        SELECT
            PARSE_TIMESTAMP('%Y%m%d',CONCAT('20',_TABLE_SUFFIX)) AS ts,
            ghost_user_id AS ghost_user_id,
            SUM(spotlight_story_view_time_second) spotlight_story_view_time_second,
            SUM(spotlight_open_profile) spotlight_open_profile,
            SUM(spotlight_favorite) spotlight_favorite,
            SUM(spotlight_subscribe) spotlight_subscribe,
            SUM(spotlight_open_replies) spotlight_open_replies,
            SUM(spotlight_send) spotlight_send,
            SUM(spotlight_zero_second_story_view) spotlight_zero_second_story_view,
            SUM(spotlight_2_plus_second_story_view) spotlight_2_plus_second_story_view,
            SUM(coalesce(spotlight_block, 0)) as spotlight_block,
            SUM(coalesce(spotlight_report, 0)) as spotlight_report,
            count(*) as total_spotlight_actions
        FROM `sc-analytics.report_search_v2.search_to_spotlight_open_20*`
        WHERE
            _TABLE_SUFFIX >= '{start_trunc}' AND
            _TABLE_SUFFIX <= '{end_trunc}'

        GROUP BY 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[
            spotlight_story_view_time_second,
            spotlight_open_profile,
            spotlight_favorite,
            spotlight_subscribe,
            spotlight_open_replies,
            spotlight_send,
            spotlight_zero_second_story_view,
            spotlight_2_plus_second_story_view,
            spotlight_block,
            spotlight_report,
            spotlight_block_rate,
            spotlight_report_rate,
            spotlight_total_actions
        ],
        name="spotlight_section_second_order_action",
        bq_dialect='standard'

    )
    return mt



def music_picker(start_date, end_date):

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[2:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[2:]

    mp_impression_cnt = Metric(
        'mp_impression_cnt',
        'Music Picker: Impression Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    mp_meaningful_action_cnt = Metric(
        'mp_meaningful_action_cnt',
        'Music Picker: Meaningful Action Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    mp_ctr = Metric(
        col='mp_ctr',
        name='Music Picker: Click Thru Rate',
        numerator='mp_meaningful_action_cnt',
        denominator='mp_impression_cnt',
        dist='ratio',
    )

    mp_mrr_sum = Metric(
        'mp_mrr_sum',
        'Music Picker: MRR numerator',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    mp_mrr_cnt = Metric(
        'mp_mrr_cnt',
        'Music Picker: MRR denominator',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    mp_mrr = Metric(
        col='mp_mrr',
        name='Music Picker: MRR',
        numerator='mp_mrr_sum',
        denominator='mp_mrr_cnt',
        dist='ratio',
    )

    mp_preview_sound_cnt = Metric(
        'mp_preview_sound_cnt',
        'Music Picker: Preview Sound - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    mp_preview_sound_uu = Metric(
        'mp_preview_sound_uu',
        'Music Picker: Preview Sound - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    mp_select_sound_cnt = Metric(
        'mp_select_sound_cnt',
        'Music Picker: Select Sound - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    mp_select_sound_uu = Metric(
        'mp_select_sound_uu',
        'Music Picker: Select Sound - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    mp_tap_music_artist_cnt = Metric(
        'mp_tap_music_artist_cnt',
        'Music Picker: Tap Music Artist - Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    mp_tap_music_artist_uu = Metric(
        'mp_tap_music_artist_uu',
        'Music Picker: Tap Music Artist - UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    mt = MetricTable(
        sql="""
        SELECT 
            a.*,
            b.* EXCEPT(_table_suffix, ghost_user_id)
        FROM (
            -- impressions 
            SELECT
                event_date AS ts,
                _table_suffix,
                ghost_user_id,
                SUM(num_impressions) as mp_impression_cnt,
            FROM `sc-analytics.report_search_v2.query_result_interactions_valid_20*`
            WHERE valid_query is true
            AND source = 'MUSIC_PICKER'
            AND _TABLE_SUFFIX BETWEEN '{start_trunc}' AND '{end_trunc}'
            GROUP BY 1, 2, 3 ) a
        LEFT OUTER JOIN 
        (
            -- actions
            SELECT
                _table_suffix,
                ghost_user_id AS ghost_user_id,
                -- logging bug: search_result_identifier is null for 'TAP_MUSIC_ARTIST' 
                COUNTIF(search_result_identifier is not null OR action='TAP_MUSIC_ARTIST') AS mp_meaningful_action_cnt,
                COUNTIF(action='PREVIEW_SOUND') AS mp_preview_sound_cnt,
                MAX(IF(action='PREVIEW_SOUND', 1, 0)) AS mp_preview_sound_uu,
                COUNTIF(action='SELECT_SOUND') AS mp_select_sound_cnt,
                MAX(IF(action='SELECT_SOUND', 1, 0)) AS mp_select_sound_uu,
                COUNTIF(action='TAP_MUSIC_ARTIST') AS mp_tap_music_artist_cnt,
                MAX(IF(action='TAP_MUSIC_ARTIST', 1, 0)) AS mp_tap_music_artist_uu,
                SUM(1/(1+CAST(search_result_ranking_id as INTEGER))) AS mp_mrr_sum,
                COUNTIF(search_result_ranking_id IS NOT NULL) AS mp_mrr_cnt,
            FROM `sc-analytics.report_search_v2.search_action_20*`
            WHERE
                _TABLE_SUFFIX BETWEEN '{start_trunc}' AND '{end_trunc}'
                AND source = 'MUSIC_PICKER'
            GROUP BY 1, 2) b
        ON a._table_suffix = b._table_suffix AND a.ghost_user_id = b.ghost_user_id
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc,
            ),
        metrics=[
            mp_impression_cnt,
            mp_meaningful_action_cnt,
            mp_ctr,
            mp_mrr_sum,
            mp_mrr_cnt,
            mp_mrr,
            mp_preview_sound_cnt,
            mp_preview_sound_uu,
            mp_select_sound_cnt,
            mp_select_sound_uu,
            mp_tap_music_artist_cnt,
            mp_tap_music_artist_uu,
        ],
        name="music_picker",
        bq_dialect='standard'

    )
    return mt


def p_suggestive_v5_impression_quantile_metrics(start_date, end_date):

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[1:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[1:]

    psv5_impression_quantiles = Metric(
        'psv5_impressions',
        'Quantiles of pSuggestive v5 Rating of Search Impressions',
        dist='quantile',
        desired_direction=NEGATIVE
    )

    mt = MetricTable(
        sql="""
        SELECT TIMESTAMP(CONCAT('2', SUBSTR(_TABLE_SUFFIX, 1, 3), '-', SUBSTR(_TABLE_SUFFIX, 4, 2), '-', SUBSTR(_TABLE_SUFFIX, 6, 2))) AS ts,
               ghost_user_id,
               psv5 as psv5_impressions
        FROM `sc-analytics.report_search_v2.search_spotlight_impression_2*`
        WHERE _TABLE_SUFFIX BETWEEN '{start_trunc}' and '{end_trunc}'
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[psv5_impression_quantiles],
        quantile_metrics=True,
        name="p_suggestive_v5_impression_quantile_metrics",
        bq_dialect='standard'
    )
    return mt


def p_suggestive_v5_action_quantile_metrics(start_date, end_date):

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[1:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[1:]

    psv5_action_quantiles = Metric(
        'psv5_actions',
        'Quantiles of pSuggestive v5 Rating of Search Actions',
        dist='quantile',
        desired_direction=NEGATIVE
    )

    mt = MetricTable(
        sql="""
        SELECT TIMESTAMP(CONCAT('2', SUBSTR(_TABLE_SUFFIX, 1, 3), '-', SUBSTR(_TABLE_SUFFIX, 4, 2), '-', SUBSTR(_TABLE_SUFFIX, 6, 2))) AS ts,
               ghost_user_id,
               psv5 as psv5_actions
        FROM `sc-analytics.report_search_v2.search_to_spotlight_open_2*`
        WHERE _TABLE_SUFFIX BETWEEN '{start_trunc}' and '{end_trunc}'
            and search_result_section = 'SPOTLIGHT'
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[psv5_action_quantiles],
        quantile_metrics=True,
        name="p_suggestive_v5_action_quantile_metrics",
        bq_dialect='standard'
    )
    return mt


def p_suggestive_v5_impression_avg(start_date, end_date):

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[1:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[1:]

    psv5_sum = Metric(
        col='psv5_sum',
        dist='cont'
    )

    unique_content_impressions = Metric(
        col='unique_content_impressions',
        dist='cont'
    )

    mean_psv5_impression = Metric(
        'mean_psv5_impression',
        'Avg pSuggestive v5 Rating of Search Impressions',
        numerator='psv5_sum',
        denominator='unique_content_impressions',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    mt = MetricTable(
        sql="""
        SELECT TIMESTAMP(CONCAT('2', SUBSTR(_TABLE_SUFFIX, 1, 3), '-', SUBSTR(_TABLE_SUFFIX, 4, 2), '-', SUBSTR(_TABLE_SUFFIX, 6, 2))) AS ts,
               ghost_user_id,
               sum(psv5) as psv5_sum,
               count(psv5) as unique_content_impressions
        FROM `sc-analytics.report_search_v2.search_spotlight_impression_2*`
        WHERE _TABLE_SUFFIX BETWEEN '{start_trunc}' and '{end_trunc}'
        group by 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[mean_psv5_impression, psv5_sum, unique_content_impressions],
        name="p_suggestive_v5_impression_avg",
        bq_dialect='standard'
    )
    return mt


def p_suggestive_v5_action_avg(start_date, end_date):

    start_trunc = pd.to_datetime(start_date).strftime('%Y%m%d')[1:]
    end_trunc = pd.to_datetime(end_date).strftime('%Y%m%d')[1:]

    psv5_sum = Metric(
        col='psv5_sum',
        dist='cont'
    )

    unique_content_actions = Metric(
        col='unique_content_actions',
        dist='cont'
    )

    mean_psv5_action = Metric(
        'mean_psv5_action',
        'Avg pSuggestive v5 Rating of Search Actions',
        numerator='psv5_sum',
        denominator='unique_content_actions',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    mt = MetricTable(
        sql="""
        SELECT TIMESTAMP(CONCAT('2', SUBSTR(_TABLE_SUFFIX, 1, 3), '-', SUBSTR(_TABLE_SUFFIX, 4, 2), '-', SUBSTR(_TABLE_SUFFIX, 6, 2))) AS ts,
               ghost_user_id,
               sum(psv5) as psv5_sum,
               count(psv5) as unique_content_actions
        FROM `sc-analytics.report_search_v2.search_to_spotlight_open_2*`
        WHERE _TABLE_SUFFIX BETWEEN '{start_trunc}' and '{end_trunc}'
            and search_result_section = 'SPOTLIGHT'
        group by 1, 2
        """.format(
            start_trunc=start_trunc,
            end_trunc=end_trunc
            ),
        metrics=[mean_psv5_action, psv5_sum, unique_content_actions],
        name="p_suggestive_v5_action_avg",
        bq_dialect='standard'
    )
    return mt
