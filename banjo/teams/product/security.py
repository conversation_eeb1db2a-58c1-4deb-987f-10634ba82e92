"""
Security/AuthN/S&A Metrics
Contact: <EMAIL>
"""

from __future__ import division, print_function
from banjo.abtest.metric import (
    Metric, MetricTable, abtest_metrics_sql_helper
)
from banjo.utils.gbq import read_gbq
from datetime import timedelta
import datetime
import pandas as pd
import logging
from banjo.abtest.metric import POSITIVE, NEGATIVE
import re

logger = logging.getLogger(__name__)

logger.warning("Please check the metric file for definition and calculation of each metrics.")


__all__ = [
    "sign_up_server",
    "sign_up_server_rub",
    "sign_up_network_response",
    "sign_in_server",
    "account_recovery_client",
    "security_lock",
    "security_ads",
    "telephony_cost",
    "account_takeover",
    "engagement"
]


def sign_up_server(study_type, study_name, start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    unit_id = ""
    join_key = ""
    source_table = ""

    if study_type == "mobile_device_level_study":
        unit_id = "config_device_id"
        join_key = "config_device_id"
        source_table = """ (
            SELECT 
              event_date,
              usermap.config_device_id,
              usermap.client_id,
              event_name,
              registration_result
            FROM 
             `sc-portal.abtest_device_usermap_cumulative.{study_name}__{end_date}` usermap
            INNER JOIN (
              SELECT 
                PARSE_DATE('%Y%m%d', _table_suffix) as event_date,
                client_id,
                event_name,
                registration_result
              FROM 
                `sc-analytics.prod_analytics_user.daily_events_*`
              WHERE 
                _TABLE_SUFFIX BETWEEN "{start_date}" AND "{end_date}"
            ) daily_events
            ON
              usermap.client_id=daily_events.client_id
        )
        """.format(study_name=study_name, start_date=start_date, end_date=end_date)

    sign_up_username_password_unset = Metric('sign_up_username_password_unset', 'sign_up_username_password_unset', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_unset_uu = Metric('sign_up_username_password_unset_uu', 'sign_up_username_password_unset_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_success = Metric('sign_up_username_password_success', 'sign_up_username_password_success', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_up_username_password_success_uu = Metric('sign_up_username_password_success_uu', 'sign_up_username_password_success_uu', dist='bin', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_up_username_password_success_email_taken = Metric('sign_up_username_password_success_email_taken', 'sign_up_username_password_success_email_taken', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_up_username_password_success_email_taken_uu = Metric('sign_up_username_password_success_email_taken_uu', 'sign_up_username_password_success_email_taken_uu', dist='bin', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_up_username_password_unexpected_failure = Metric('sign_up_username_password_unexpected_failure', 'sign_up_username_password_unexpected_failure', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_unexpected_failure_uu = Metric('sign_up_username_password_unexpected_failure_uu', 'sign_up_username_password_unexpected_failure_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_invalid_argument = Metric('sign_up_username_password_invalid_argument', 'sign_up_username_password_invalid_argument', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_invalid_argument_uu = Metric('sign_up_username_password_invalid_argument_uu', 'sign_up_username_password_invalid_argument_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_app_version_upgrade = Metric('sign_up_username_password_app_version_upgrade', 'sign_up_username_password_app_version_upgrade', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_app_version_upgrade_uu = Metric('sign_up_username_password_app_version_upgrade_uu', 'sign_up_username_password_app_version_upgrade_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_blocked = Metric('sign_up_username_password_blocked', 'sign_up_username_password_blocked', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_blocked_uu = Metric('sign_up_username_password_blocked_uu', 'sign_up_username_password_blocked_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_ineligible_for_registration = Metric('sign_up_username_password_ineligible_for_registration', 'sign_up_username_password_ineligible_for_registration', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_ineligible_for_registration_uu = Metric('sign_up_username_password_ineligible_for_registration_uu', 'sign_up_username_password_ineligible_for_registration_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_denied = Metric('sign_up_username_password_denied', 'sign_up_username_password_denied', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_denied_uu = Metric('sign_up_username_password_denied_uu', 'sign_up_username_password_denied_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_failed_challenge = Metric('sign_up_username_password_failed_challenge', 'sign_up_username_password_failed_challenge', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_failed_challenge_uu = Metric('sign_up_username_password_failed_challenge_uu', 'sign_up_username_password_failed_challenge_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_weak_password = Metric('sign_up_username_password_weak_password', 'sign_up_username_password_weak_password', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_weak_password_uu = Metric('sign_up_username_password_weak_password_uu', 'sign_up_username_password_weak_password_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_username_taken = Metric('sign_up_username_password_username_taken', 'sign_up_username_password_username_taken', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_username_taken_uu = Metric('sign_up_username_password_username_taken_uu', 'sign_up_username_password_username_taken_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_email_taken = Metric('sign_up_username_password_email_taken', 'sign_up_username_password_email_taken', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_email_taken_uu = Metric('sign_up_username_password_email_taken_uu', 'sign_up_username_password_email_taken_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_permission_denied = Metric('sign_up_username_password_permission_denied', 'sign_up_username_password_permission_denied', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_permission_denied_uu = Metric('sign_up_username_password_permission_denied_uu', 'sign_up_username_password_permission_denied_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_abusive_username = Metric('sign_up_username_password_abusive_username', 'sign_up_username_password_abusive_username', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_abusive_username_uu = Metric('sign_up_username_password_abusive_username_uu', 'sign_up_username_password_abusive_username_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_challenge_safetynet = Metric('sign_up_username_password_challenge_safetynet', 'sign_up_username_password_challenge_safetynet', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_challenge_safetynet_uu = Metric('sign_up_username_password_challenge_safetynet_uu', 'sign_up_username_password_challenge_safetynet_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_challenge_play_integrity = Metric('sign_up_username_password_challenge_play_integrity', 'sign_up_username_password_challenge_play_integrity', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_challenge_play_integrity_uu = Metric('sign_up_username_password_challenge_play_integrity_uu', 'sign_up_username_password_challenge_play_integrity_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_challenge_key_attestation = Metric('sign_up_username_password_challenge_key_attestation', 'sign_up_username_password_challenge_key_attestation', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_challenge_key_attestation_uu = Metric('sign_up_username_password_challenge_key_attestation_uu', 'sign_up_username_password_challenge_key_attestation_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_challenge_app_attest = Metric('sign_up_username_password_challenge_app_attest', 'sign_up_username_password_challenge_app_attest', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_challenge_app_attest_uu = Metric('sign_up_username_password_challenge_app_attest_uu', 'sign_up_username_password_challenge_app_attest_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_challenge_captcha = Metric('sign_up_username_password_challenge_captcha', 'sign_up_username_password_challenge_captcha', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_challenge_captcha_uu = Metric('sign_up_username_password_challenge_captcha_uu', 'sign_up_username_password_challenge_captcha_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_challenged = Metric('sign_up_username_password_challenged', 'sign_up_username_password_challenged', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_challenged_uu = Metric('sign_up_username_password_challenged_uu', 'sign_up_username_password_challenged_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_rate_limited_ip = Metric('sign_up_username_password_rate_limited_ip', 'sign_up_username_password_rate_limited_ip', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_rate_limited_ip_uu = Metric('sign_up_username_password_rate_limited_ip_uu', 'sign_up_username_password_rate_limited_ip_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_rate_limited_persistent_attestation_device_id = Metric('sign_up_username_password_rate_limited_persistent_attestation_device_id', 'sign_up_username_password_rate_limited_persistent_attestation_device_id', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_username_password_rate_limited_persistent_attestation_device_id_uu = Metric('sign_up_username_password_rate_limited_persistent_attestation_device_id_uu', 'sign_up_username_password_rate_limited_persistent_attestation_device_id_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)

    non_cos_sign_up_username_password_unset = Metric('non_cos_sign_up_username_password_unset', 'non_cos_sign_up_username_password_unset', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_unset_uu = Metric('non_cos_sign_up_username_password_unset_uu', 'non_cos_sign_up_username_password_unset_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_success = Metric('non_cos_sign_up_username_password_success', 'non_cos_sign_up_username_password_success', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    non_cos_sign_up_username_password_success_uu = Metric('non_cos_sign_up_username_password_success_uu', 'non_cos_sign_up_username_password_success_uu', dist='bin', daily=True, cumulative=True, desired_direction=POSITIVE)
    non_cos_sign_up_username_password_success_email_taken = Metric('non_cos_sign_up_username_password_success_email_taken', 'non_cos_sign_up_username_password_success_email_taken', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    non_cos_sign_up_username_password_success_email_taken_uu = Metric('non_cos_sign_up_username_password_success_email_taken_uu', 'non_cos_sign_up_username_password_success_email_taken_uu', dist='bin', daily=True, cumulative=True, desired_direction=POSITIVE)
    non_cos_sign_up_username_password_unexpected_failure = Metric('non_cos_sign_up_username_password_unexpected_failure', 'non_cos_sign_up_username_password_unexpected_failure', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_unexpected_failure_uu = Metric('non_cos_sign_up_username_password_unexpected_failure_uu', 'non_cos_sign_up_username_password_unexpected_failure_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_invalid_argument = Metric('non_cos_sign_up_username_password_invalid_argument', 'non_cos_sign_up_username_password_invalid_argument', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_invalid_argument_uu = Metric('non_cos_sign_up_username_password_invalid_argument_uu', 'non_cos_sign_up_username_password_invalid_argument_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_app_version_upgrade = Metric('non_cos_sign_up_username_password_app_version_upgrade', 'non_cos_sign_up_username_password_app_version_upgrade', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_app_version_upgrade_uu = Metric('non_cos_sign_up_username_password_app_version_upgrade_uu', 'non_cos_sign_up_username_password_app_version_upgrade_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_blocked = Metric('non_cos_sign_up_username_password_blocked', 'non_cos_sign_up_username_password_blocked', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_blocked_uu = Metric('non_cos_sign_up_username_password_blocked_uu', 'non_cos_sign_up_username_password_blocked_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_ineligible_for_registration = Metric('non_cos_sign_up_username_password_ineligible_for_registration', 'non_cos_sign_up_username_password_ineligible_for_registration', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_ineligible_for_registration_uu = Metric('non_cos_sign_up_username_password_ineligible_for_registration_uu', 'non_cos_sign_up_username_password_ineligible_for_registration_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_denied = Metric('non_cos_sign_up_username_password_denied', 'non_cos_sign_up_username_password_denied', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_denied_uu = Metric('non_cos_sign_up_username_password_denied_uu', 'non_cos_sign_up_username_password_denied_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_failed_challenge = Metric('non_cos_sign_up_username_password_failed_challenge', 'non_cos_sign_up_username_password_failed_challenge', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_failed_challenge_uu = Metric('non_cos_sign_up_username_password_failed_challenge_uu', 'non_cos_sign_up_username_password_failed_challenge_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_weak_password = Metric('non_cos_sign_up_username_password_weak_password', 'non_cos_sign_up_username_password_weak_password', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_weak_password_uu = Metric('non_cos_sign_up_username_password_weak_password_uu', 'non_cos_sign_up_username_password_weak_password_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_username_taken = Metric('non_cos_sign_up_username_password_username_taken', 'non_cos_sign_up_username_password_username_taken', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_username_taken_uu = Metric('non_cos_sign_up_username_password_username_taken_uu', 'non_cos_sign_up_username_password_username_taken_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_email_taken = Metric('non_cos_sign_up_username_password_email_taken', 'non_cos_sign_up_username_password_email_taken', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_email_taken_uu = Metric('non_cos_sign_up_username_password_email_taken_uu', 'non_cos_sign_up_username_password_email_taken_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_permission_denied = Metric('non_cos_sign_up_username_password_permission_denied', 'non_cos_sign_up_username_password_permission_denied', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_permission_denied_uu = Metric('non_cos_sign_up_username_password_permission_denied_uu', 'non_cos_sign_up_username_password_permission_denied_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_abusive_username = Metric('non_cos_sign_up_username_password_abusive_username', 'non_cos_sign_up_username_password_abusive_username', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_abusive_username_uu = Metric('non_cos_sign_up_username_password_abusive_username_uu', 'non_cos_sign_up_username_password_abusive_username_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_challenge_safetynet = Metric('non_cos_sign_up_username_password_challenge_safetynet', 'non_cos_sign_up_username_password_challenge_safetynet', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_challenge_safetynet_uu = Metric('non_cos_sign_up_username_password_challenge_safetynet_uu', 'non_cos_sign_up_username_password_challenge_safetynet_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_challenge_play_integrity = Metric('non_cos_sign_up_username_password_challenge_play_integrity', 'non_cos_sign_up_username_password_challenge_play_integrity', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_challenge_play_integrity_uu = Metric('non_cos_sign_up_username_password_challenge_play_integrity_uu', 'non_cos_sign_up_username_password_challenge_play_integrity_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_challenge_key_attestation = Metric('non_cos_sign_up_username_password_challenge_key_attestation', 'non_cos_sign_up_username_password_challenge_key_attestation', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_challenge_key_attestation_uu = Metric('non_cos_sign_up_username_password_challenge_key_attestation_uu', 'non_cos_sign_up_username_password_challenge_key_attestation_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_challenge_app_attest = Metric('non_cos_sign_up_username_password_challenge_app_attest', 'non_cos_sign_up_username_password_challenge_app_attest', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_challenge_app_attest_uu = Metric('non_cos_sign_up_username_password_challenge_app_attest_uu', 'non_cos_sign_up_username_password_challenge_app_attest_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_challenge_captcha = Metric('non_cos_sign_up_username_password_challenge_captcha', 'non_cos_sign_up_username_password_challenge_captcha', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_challenge_captcha_uu = Metric('non_cos_sign_up_username_password_challenge_captcha_uu', 'non_cos_sign_up_username_password_challenge_captcha_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_challenged = Metric('non_cos_sign_up_username_password_challenged', 'non_cos_sign_up_username_password_challenged', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_challenged_uu = Metric('non_cos_sign_up_username_password_challenged_uu', 'non_cos_sign_up_username_password_challenged_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_rate_limited_ip = Metric('non_cos_sign_up_username_password_rate_limited_ip', 'non_cos_sign_up_username_password_rate_limited_ip', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_rate_limited_ip_uu = Metric('non_cos_sign_up_username_password_rate_limited_ip_uu', 'non_cos_sign_up_username_password_rate_limited_ip_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_rate_limited_persistent_attestation_device_id = Metric('non_cos_sign_up_username_password_rate_limited_persistent_attestation_device_id', 'non_cos_sign_up_username_password_rate_limited_persistent_attestation_device_id', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    non_cos_sign_up_username_password_rate_limited_persistent_attestation_device_id_uu = Metric('non_cos_sign_up_username_password_rate_limited_persistent_attestation_device_id_uu', 'non_cos_sign_up_username_password_rate_limited_persistent_attestation_device_id_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)

    cos_sign_up_username_password_unset = Metric('cos_sign_up_username_password_unset', 'cos_sign_up_username_password_unset', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_unset_uu = Metric('cos_sign_up_username_password_unset_uu', 'cos_sign_up_username_password_unset_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_success = Metric('cos_sign_up_username_password_success', 'cos_sign_up_username_password_success', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    cos_sign_up_username_password_success_uu = Metric('cos_sign_up_username_password_success_uu', 'cos_sign_up_username_password_success_uu', dist='bin', daily=True, cumulative=True, desired_direction=POSITIVE)
    cos_sign_up_username_password_success_email_taken = Metric('cos_sign_up_username_password_success_email_taken', 'cos_sign_up_username_password_success_email_taken', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    cos_sign_up_username_password_success_email_taken_uu = Metric('cos_sign_up_username_password_success_email_taken_uu', 'cos_sign_up_username_password_success_email_taken_uu', dist='bin', daily=True, cumulative=True, desired_direction=POSITIVE)
    cos_sign_up_username_password_unexpected_failure = Metric('cos_sign_up_username_password_unexpected_failure', 'cos_sign_up_username_password_unexpected_failure', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_unexpected_failure_uu = Metric('cos_sign_up_username_password_unexpected_failure_uu', 'cos_sign_up_username_password_unexpected_failure_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_invalid_argument = Metric('cos_sign_up_username_password_invalid_argument', 'cos_sign_up_username_password_invalid_argument', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_invalid_argument_uu = Metric('cos_sign_up_username_password_invalid_argument_uu', 'cos_sign_up_username_password_invalid_argument_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_app_version_upgrade = Metric('cos_sign_up_username_password_app_version_upgrade', 'cos_sign_up_username_password_app_version_upgrade', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_app_version_upgrade_uu = Metric('cos_sign_up_username_password_app_version_upgrade_uu', 'cos_sign_up_username_password_app_version_upgrade_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_blocked = Metric('cos_sign_up_username_password_blocked', 'cos_sign_up_username_password_blocked', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_blocked_uu = Metric('cos_sign_up_username_password_blocked_uu', 'cos_sign_up_username_password_blocked_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_ineligible_for_registration = Metric('cos_sign_up_username_password_ineligible_for_registration', 'cos_sign_up_username_password_ineligible_for_registration', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_ineligible_for_registration_uu = Metric('cos_sign_up_username_password_ineligible_for_registration_uu', 'cos_sign_up_username_password_ineligible_for_registration_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_denied = Metric('cos_sign_up_username_password_denied', 'cos_sign_up_username_password_denied', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_denied_uu = Metric('cos_sign_up_username_password_denied_uu', 'cos_sign_up_username_password_denied_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_failed_challenge = Metric('cos_sign_up_username_password_failed_challenge', 'cos_sign_up_username_password_failed_challenge', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_failed_challenge_uu = Metric('cos_sign_up_username_password_failed_challenge_uu', 'cos_sign_up_username_password_failed_challenge_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_weak_password = Metric('cos_sign_up_username_password_weak_password', 'cos_sign_up_username_password_weak_password', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_weak_password_uu = Metric('cos_sign_up_username_password_weak_password_uu', 'cos_sign_up_username_password_weak_password_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_username_taken = Metric('cos_sign_up_username_password_username_taken', 'cos_sign_up_username_password_username_taken', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_username_taken_uu = Metric('cos_sign_up_username_password_username_taken_uu', 'cos_sign_up_username_password_username_taken_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_email_taken = Metric('cos_sign_up_username_password_email_taken', 'cos_sign_up_username_password_email_taken', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_email_taken_uu = Metric('cos_sign_up_username_password_email_taken_uu', 'cos_sign_up_username_password_email_taken_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_permission_denied = Metric('cos_sign_up_username_password_permission_denied', 'cos_sign_up_username_password_permission_denied', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_permission_denied_uu = Metric('cos_sign_up_username_password_permission_denied_uu', 'cos_sign_up_username_password_permission_denied_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_abusive_username = Metric('cos_sign_up_username_password_abusive_username', 'cos_sign_up_username_password_abusive_username', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_abusive_username_uu = Metric('cos_sign_up_username_password_abusive_username_uu', 'cos_sign_up_username_password_abusive_username_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_challenge_safetynet = Metric('cos_sign_up_username_password_challenge_safetynet', 'cos_sign_up_username_password_challenge_safetynet', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_challenge_safetynet_uu = Metric('cos_sign_up_username_password_challenge_safetynet_uu', 'cos_sign_up_username_password_challenge_safetynet_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_challenge_play_integrity = Metric('cos_sign_up_username_password_challenge_play_integrity', 'cos_sign_up_username_password_challenge_play_integrity', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_challenge_play_integrity_uu = Metric('cos_sign_up_username_password_challenge_play_integrity_uu', 'cos_sign_up_username_password_challenge_play_integrity_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_challenge_key_attestation = Metric('cos_sign_up_username_password_challenge_key_attestation', 'cos_sign_up_username_password_challenge_key_attestation', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_challenge_key_attestation_uu = Metric('cos_sign_up_username_password_challenge_key_attestation_uu', 'cos_sign_up_username_password_challenge_key_attestation_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_challenge_app_attest = Metric('cos_sign_up_username_password_challenge_app_attest', 'cos_sign_up_username_password_challenge_app_attest', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_challenge_app_attest_uu = Metric('cos_sign_up_username_password_challenge_app_attest_uu', 'cos_sign_up_username_password_challenge_app_attest_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_challenge_captcha = Metric('cos_sign_up_username_password_challenge_captcha', 'cos_sign_up_username_password_challenge_captcha', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_challenge_captcha_uu = Metric('cos_sign_up_username_password_challenge_captcha_uu', 'cos_sign_up_username_password_challenge_captcha_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_challenged = Metric('cos_sign_up_username_password_challenged', 'cos_sign_up_username_password_challenged', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_challenged_uu = Metric('cos_sign_up_username_password_challenged_uu', 'cos_sign_up_username_password_challenged_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_rate_limited_ip = Metric('cos_sign_up_username_password_rate_limited_ip', 'cos_sign_up_username_password_rate_limited_ip', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_rate_limited_ip_uu = Metric('cos_sign_up_username_password_rate_limited_ip_uu', 'cos_sign_up_username_password_rate_limited_ip_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_rate_limited_persistent_attestation_device_id = Metric('cos_sign_up_username_password_rate_limited_persistent_attestation_device_id', 'cos_sign_up_username_password_rate_limited_persistent_attestation_device_id', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    cos_sign_up_username_password_rate_limited_persistent_attestation_device_id_uu = Metric('cos_sign_up_username_password_rate_limited_persistent_attestation_device_id_uu', 'cos_sign_up_username_password_rate_limited_persistent_attestation_device_id_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)

    if source_table != "":
        mt = MetricTable(
            sql=""" 
            SELECT
              TIMESTAMP(event_date) as ts,
              {unit_id},
              # COS and non-COS
              SUM(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_UNSET', 1, 0)) as sign_up_username_password_unset,
              MAX(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_UNSET', 1, 0)) as sign_up_username_password_unset_uu,
              SUM(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_SUCCESS', 1, 0)) as sign_up_username_password_success,
              MAX(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_SUCCESS', 1, 0)) as sign_up_username_password_success_uu,
              SUM(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_SUCCESS_EMAIL_TAKEN', 1, 0)) as sign_up_username_password_success_email_taken,
              MAX(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_SUCCESS_EMAIL_TAKEN', 1, 0)) as sign_up_username_password_success_email_taken_uu,
              SUM(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_UNEXPECTED_FAILURE', 1, 0)) as sign_up_username_password_unexpected_failure,
              MAX(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_UNEXPECTED_FAILURE', 1, 0)) as sign_up_username_password_unexpected_failure_uu,
              SUM(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_INVALID_ARGUMENT', 1, 0)) as sign_up_username_password_invalid_argument,
              MAX(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_INVALID_ARGUMENT', 1, 0)) as sign_up_username_password_invalid_argument_uu,
              SUM(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_APP_VERSION_UPGRADE', 1, 0)) as sign_up_username_password_app_version_upgrade,
              MAX(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_APP_VERSION_UPGRADE', 1, 0)) as sign_up_username_password_app_version_upgrade_uu,
              SUM(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_BLOCKED', 1, 0)) as sign_up_username_password_blocked,
              MAX(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_BLOCKED', 1, 0)) as sign_up_username_password_blocked_uu,
              SUM(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_INELIGIBLE_FOR_REGISTRATION', 1, 0)) as sign_up_username_password_ineligible_for_registration,
              MAX(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_INELIGIBLE_FOR_REGISTRATION', 1, 0)) as sign_up_username_password_ineligible_for_registration_uu,
              SUM(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_DENIED', 1, 0)) as sign_up_username_password_denied,
              MAX(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_DENIED', 1, 0)) as sign_up_username_password_denied_uu,
              SUM(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_FAILED_CHALLENGE', 1, 0)) as sign_up_username_password_failed_challenge,
              MAX(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_FAILED_CHALLENGE', 1, 0)) as sign_up_username_password_failed_challenge_uu,
              SUM(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_WEAK_PASSWORD', 1, 0)) as sign_up_username_password_weak_password,
              MAX(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_WEAK_PASSWORD', 1, 0)) as sign_up_username_password_weak_password_uu,
              SUM(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_USERNAME_TAKEN', 1, 0)) as sign_up_username_password_username_taken,
              MAX(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_USERNAME_TAKEN', 1, 0)) as sign_up_username_password_username_taken_uu,
              SUM(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_EMAIL_TAKEN', 1, 0)) as sign_up_username_password_email_taken,
              MAX(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_EMAIL_TAKEN', 1, 0)) as sign_up_username_password_email_taken_uu,
              SUM(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_PERMISSION_DENIED', 1, 0)) as sign_up_username_password_permission_denied,
              MAX(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_PERMISSION_DENIED', 1, 0)) as sign_up_username_password_permission_denied_uu,
              SUM(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_ABUSIVE_USERNAME', 1, 0)) as sign_up_username_password_abusive_username,
              MAX(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_ABUSIVE_USERNAME', 1, 0)) as sign_up_username_password_abusive_username_uu,
              SUM(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_CHALLENGE_SAFETYNET', 1, 0)) as sign_up_username_password_challenge_safetynet,
              MAX(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_CHALLENGE_SAFETYNET', 1, 0)) as sign_up_username_password_challenge_safetynet_uu,
              SUM(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_CHALLENGE_PLAY_INTEGRITY', 1, 0)) as sign_up_username_password_challenge_play_integrity,
              MAX(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_CHALLENGE_PLAY_INTEGRITY', 1, 0)) as sign_up_username_password_challenge_play_integrity_uu,
              SUM(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_CHALLENGE_KEY_ATTESTATION', 1, 0)) as sign_up_username_password_challenge_key_attestation,
              MAX(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_CHALLENGE_KEY_ATTESTATION', 1, 0)) as sign_up_username_password_challenge_key_attestation_uu,
              SUM(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_CHALLENGE_APP_ATTEST', 1, 0)) as sign_up_username_password_challenge_app_attest,
              MAX(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_CHALLENGE_APP_ATTEST', 1, 0)) as sign_up_username_password_challenge_app_attest_uu,
              SUM(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_CHALLENGE_CAPTCHA', 1, 0)) as sign_up_username_password_challenge_captcha,
              MAX(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_CHALLENGE_CAPTCHA', 1, 0)) as sign_up_username_password_challenge_captcha_uu,
              SUM(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_CHALLENGED', 1, 0)) as sign_up_username_password_challenged,
              MAX(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_CHALLENGED', 1, 0)) as sign_up_username_password_challenged_uu,
              SUM(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_RATE_LIMITED_IP', 1, 0)) as sign_up_username_password_rate_limited_ip,
              MAX(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_RATE_LIMITED_IP', 1, 0)) as sign_up_username_password_rate_limited_ip_uu,
              SUM(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_RATE_LIMITED_PERSISTENT_ATTESTATION_DEVICE_ID', 1, 0)) as sign_up_username_password_rate_limited_persistent_attestation_device_id,
              MAX(IF(event_name in ('REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT', 'APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT') AND registration_result='REGISTRATION_RESULT_RATE_LIMITED_PERSISTENT_ATTESTATION_DEVICE_ID', 1, 0)) as sign_up_username_password_rate_limited_persistent_attestation_device_id_uu,
              # Non-Cos
              SUM(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_UNSET', 1, 0)) as non_cos_sign_up_username_password_unset,
              MAX(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_UNSET', 1, 0)) as non_cos_sign_up_username_password_unset_uu,
              SUM(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_SUCCESS', 1, 0)) as non_cos_sign_up_username_password_success,
              MAX(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_SUCCESS', 1, 0)) as non_cos_sign_up_username_password_success_uu,
              SUM(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_SUCCESS_EMAIL_TAKEN', 1, 0)) as non_cos_sign_up_username_password_success_email_taken,
              MAX(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_SUCCESS_EMAIL_TAKEN', 1, 0)) as non_cos_sign_up_username_password_success_email_taken_uu,
              SUM(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_UNEXPECTED_FAILURE', 1, 0)) as non_cos_sign_up_username_password_unexpected_failure,
              MAX(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_UNEXPECTED_FAILURE', 1, 0)) as non_cos_sign_up_username_password_unexpected_failure_uu,
              SUM(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_INVALID_ARGUMENT', 1, 0)) as non_cos_sign_up_username_password_invalid_argument,
              MAX(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_INVALID_ARGUMENT', 1, 0)) as non_cos_sign_up_username_password_invalid_argument_uu,
              SUM(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_APP_VERSION_UPGRADE', 1, 0)) as non_cos_sign_up_username_password_app_version_upgrade,
              MAX(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_APP_VERSION_UPGRADE', 1, 0)) as non_cos_sign_up_username_password_app_version_upgrade_uu,
              SUM(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_BLOCKED', 1, 0)) as non_cos_sign_up_username_password_blocked,
              MAX(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_BLOCKED', 1, 0)) as non_cos_sign_up_username_password_blocked_uu,
              SUM(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_INELIGIBLE_FOR_REGISTRATION', 1, 0)) as non_cos_sign_up_username_password_ineligible_for_registration,
              MAX(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_INELIGIBLE_FOR_REGISTRATION', 1, 0)) as non_cos_sign_up_username_password_ineligible_for_registration_uu,
              SUM(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_DENIED', 1, 0)) as non_cos_sign_up_username_password_denied,
              MAX(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_DENIED', 1, 0)) as non_cos_sign_up_username_password_denied_uu,
              SUM(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_FAILED_CHALLENGE', 1, 0)) as non_cos_sign_up_username_password_failed_challenge,
              MAX(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_FAILED_CHALLENGE', 1, 0)) as non_cos_sign_up_username_password_failed_challenge_uu,
              SUM(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_WEAK_PASSWORD', 1, 0)) as non_cos_sign_up_username_password_weak_password,
              MAX(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_WEAK_PASSWORD', 1, 0)) as non_cos_sign_up_username_password_weak_password_uu,
              SUM(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_USERNAME_TAKEN', 1, 0)) as non_cos_sign_up_username_password_username_taken,
              MAX(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_USERNAME_TAKEN', 1, 0)) as non_cos_sign_up_username_password_username_taken_uu,
              SUM(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_EMAIL_TAKEN', 1, 0)) as non_cos_sign_up_username_password_email_taken,
              MAX(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_EMAIL_TAKEN', 1, 0)) as non_cos_sign_up_username_password_email_taken_uu,
              SUM(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_PERMISSION_DENIED', 1, 0)) as non_cos_sign_up_username_password_permission_denied,
              MAX(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_PERMISSION_DENIED', 1, 0)) as non_cos_sign_up_username_password_permission_denied_uu,
              SUM(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_ABUSIVE_USERNAME', 1, 0)) as non_cos_sign_up_username_password_abusive_username,
              MAX(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_ABUSIVE_USERNAME', 1, 0)) as non_cos_sign_up_username_password_abusive_username_uu,
              SUM(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_CHALLENGE_SAFETYNET', 1, 0)) as non_cos_sign_up_username_password_challenge_safetynet,
              MAX(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_CHALLENGE_SAFETYNET', 1, 0)) as non_cos_sign_up_username_password_challenge_safetynet_uu,
              SUM(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_CHALLENGE_PLAY_INTEGRITY', 1, 0)) as non_cos_sign_up_username_password_challenge_play_integrity,
              MAX(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_CHALLENGE_PLAY_INTEGRITY', 1, 0)) as non_cos_sign_up_username_password_challenge_play_integrity_uu,
              SUM(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_CHALLENGE_KEY_ATTESTATION', 1, 0)) as non_cos_sign_up_username_password_challenge_key_attestation,
              MAX(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_CHALLENGE_KEY_ATTESTATION', 1, 0)) as non_cos_sign_up_username_password_challenge_key_attestation_uu,
              SUM(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_CHALLENGE_APP_ATTEST', 1, 0)) as non_cos_sign_up_username_password_challenge_app_attest,
              MAX(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_CHALLENGE_APP_ATTEST', 1, 0)) as non_cos_sign_up_username_password_challenge_app_attest_uu,
              SUM(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_CHALLENGE_CAPTCHA', 1, 0)) as non_cos_sign_up_username_password_challenge_captcha,
              MAX(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_CHALLENGE_CAPTCHA', 1, 0)) as non_cos_sign_up_username_password_challenge_captcha_uu,
              SUM(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_CHALLENGED', 1, 0)) as non_cos_sign_up_username_password_challenged,
              MAX(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_CHALLENGED', 1, 0)) as non_cos_sign_up_username_password_challenged_uu,
              SUM(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_RATE_LIMITED_IP', 1, 0)) as non_cos_sign_up_username_password_rate_limited_ip,
              MAX(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_RATE_LIMITED_IP', 1, 0)) as non_cos_sign_up_username_password_rate_limited_ip_uu,
              SUM(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_RATE_LIMITED_PERSISTENT_ATTESTATION_DEVICE_ID', 1, 0)) as non_cos_sign_up_username_password_rate_limited_persistent_attestation_device_id,
              MAX(IF(event_name='REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT' AND registration_result='REGISTRATION_RESULT_RATE_LIMITED_PERSISTENT_ATTESTATION_DEVICE_ID', 1, 0)) as non_cos_sign_up_username_password_rate_limited_persistent_attestation_device_id_uu,
              # COS
              SUM(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_UNSET', 1, 0)) as cos_sign_up_username_password_unset,
              MAX(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_UNSET', 1, 0)) as cos_sign_up_username_password_unset_uu,
              SUM(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_SUCCESS', 1, 0)) as cos_sign_up_username_password_success,
              MAX(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_SUCCESS', 1, 0)) as cos_sign_up_username_password_success_uu,
              SUM(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_SUCCESS_EMAIL_TAKEN', 1, 0)) as cos_sign_up_username_password_success_email_taken,
              MAX(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_SUCCESS_EMAIL_TAKEN', 1, 0)) as cos_sign_up_username_password_success_email_taken_uu,
              SUM(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_UNEXPECTED_FAILURE', 1, 0)) as cos_sign_up_username_password_unexpected_failure,
              MAX(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_UNEXPECTED_FAILURE', 1, 0)) as cos_sign_up_username_password_unexpected_failure_uu,
              SUM(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_INVALID_ARGUMENT', 1, 0)) as cos_sign_up_username_password_invalid_argument,
              MAX(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_INVALID_ARGUMENT', 1, 0)) as cos_sign_up_username_password_invalid_argument_uu,
              SUM(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_APP_VERSION_UPGRADE', 1, 0)) as cos_sign_up_username_password_app_version_upgrade,
              MAX(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_APP_VERSION_UPGRADE', 1, 0)) as cos_sign_up_username_password_app_version_upgrade_uu,
              SUM(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_BLOCKED', 1, 0)) as cos_sign_up_username_password_blocked,
              MAX(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_BLOCKED', 1, 0)) as cos_sign_up_username_password_blocked_uu,
              SUM(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_INELIGIBLE_FOR_REGISTRATION', 1, 0)) as cos_sign_up_username_password_ineligible_for_registration,
              MAX(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_INELIGIBLE_FOR_REGISTRATION', 1, 0)) as cos_sign_up_username_password_ineligible_for_registration_uu,
              SUM(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_DENIED', 1, 0)) as cos_sign_up_username_password_denied,
              MAX(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_DENIED', 1, 0)) as cos_sign_up_username_password_denied_uu,
              SUM(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_FAILED_CHALLENGE', 1, 0)) as cos_sign_up_username_password_failed_challenge,
              MAX(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_FAILED_CHALLENGE', 1, 0)) as cos_sign_up_username_password_failed_challenge_uu,
              SUM(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_WEAK_PASSWORD', 1, 0)) as cos_sign_up_username_password_weak_password,
              MAX(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_WEAK_PASSWORD', 1, 0)) as cos_sign_up_username_password_weak_password_uu,
              SUM(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_USERNAME_TAKEN', 1, 0)) as cos_sign_up_username_password_username_taken,
              MAX(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_USERNAME_TAKEN', 1, 0)) as cos_sign_up_username_password_username_taken_uu,
              SUM(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_EMAIL_TAKEN', 1, 0)) as cos_sign_up_username_password_email_taken,
              MAX(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_EMAIL_TAKEN', 1, 0)) as cos_sign_up_username_password_email_taken_uu,
              SUM(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_PERMISSION_DENIED', 1, 0)) as cos_sign_up_username_password_permission_denied,
              MAX(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_PERMISSION_DENIED', 1, 0)) as cos_sign_up_username_password_permission_denied_uu,
              SUM(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_ABUSIVE_USERNAME', 1, 0)) as cos_sign_up_username_password_abusive_username,
              MAX(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_ABUSIVE_USERNAME', 1, 0)) as cos_sign_up_username_password_abusive_username_uu,
              SUM(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_CHALLENGE_SAFETYNET', 1, 0)) as cos_sign_up_username_password_challenge_safetynet,
              MAX(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_CHALLENGE_SAFETYNET', 1, 0)) as cos_sign_up_username_password_challenge_safetynet_uu,
              SUM(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_CHALLENGE_PLAY_INTEGRITY', 1, 0)) as cos_sign_up_username_password_challenge_play_integrity,
              MAX(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_CHALLENGE_PLAY_INTEGRITY', 1, 0)) as cos_sign_up_username_password_challenge_play_integrity_uu,
              SUM(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_CHALLENGE_KEY_ATTESTATION', 1, 0)) as cos_sign_up_username_password_challenge_key_attestation,
              MAX(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_CHALLENGE_KEY_ATTESTATION', 1, 0)) as cos_sign_up_username_password_challenge_key_attestation_uu,
              SUM(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_CHALLENGE_APP_ATTEST', 1, 0)) as cos_sign_up_username_password_challenge_app_attest,
              MAX(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_CHALLENGE_APP_ATTEST', 1, 0)) as cos_sign_up_username_password_challenge_app_attest_uu,
              SUM(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_CHALLENGE_CAPTCHA', 1, 0)) as cos_sign_up_username_password_challenge_captcha,
              MAX(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_CHALLENGE_CAPTCHA', 1, 0)) as cos_sign_up_username_password_challenge_captcha_uu,
              SUM(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_CHALLENGED', 1, 0)) as cos_sign_up_username_password_challenged,
              MAX(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_CHALLENGED', 1, 0)) as cos_sign_up_username_password_challenged_uu,
              SUM(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_RATE_LIMITED_IP', 1, 0)) as cos_sign_up_username_password_rate_limited_ip,
              MAX(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_RATE_LIMITED_IP', 1, 0)) as cos_sign_up_username_password_rate_limited_ip_uu,
              SUM(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_RATE_LIMITED_PERSISTENT_ATTESTATION_DEVICE_ID', 1, 0)) as cos_sign_up_username_password_rate_limited_persistent_attestation_device_id,
              MAX(IF(event_name='APP_REGISTER_ANSWER_CHALLENGE_ATTEMPT' AND registration_result='REGISTRATION_RESULT_RATE_LIMITED_PERSISTENT_ATTESTATION_DEVICE_ID', 1, 0)) as cos_sign_up_username_password_rate_limited_persistent_attestation_device_id_uu,
            FROM 
              {source_table}
            GROUP BY 1,2  
            """.format(unit_id=unit_id, source_table=source_table),

            metrics=[
                sign_up_username_password_unset,
                sign_up_username_password_unset_uu,
                sign_up_username_password_success,
                sign_up_username_password_success_uu,
                sign_up_username_password_success_email_taken,
                sign_up_username_password_success_email_taken_uu,
                sign_up_username_password_unexpected_failure,
                sign_up_username_password_unexpected_failure_uu,
                sign_up_username_password_invalid_argument,
                sign_up_username_password_invalid_argument_uu,
                sign_up_username_password_app_version_upgrade,
                sign_up_username_password_app_version_upgrade_uu,
                sign_up_username_password_blocked,
                sign_up_username_password_blocked_uu,
                sign_up_username_password_ineligible_for_registration,
                sign_up_username_password_ineligible_for_registration_uu,
                sign_up_username_password_denied,
                sign_up_username_password_denied_uu,
                sign_up_username_password_failed_challenge,
                sign_up_username_password_failed_challenge_uu,
                sign_up_username_password_weak_password,
                sign_up_username_password_weak_password_uu,
                sign_up_username_password_username_taken,
                sign_up_username_password_username_taken_uu,
                sign_up_username_password_email_taken,
                sign_up_username_password_email_taken_uu,
                sign_up_username_password_permission_denied,
                sign_up_username_password_permission_denied_uu,
                sign_up_username_password_abusive_username,
                sign_up_username_password_abusive_username_uu,
                sign_up_username_password_challenge_safetynet,
                sign_up_username_password_challenge_safetynet_uu,
                sign_up_username_password_challenge_play_integrity,
                sign_up_username_password_challenge_play_integrity_uu,
                sign_up_username_password_challenge_key_attestation,
                sign_up_username_password_challenge_key_attestation_uu,
                sign_up_username_password_challenge_app_attest,
                sign_up_username_password_challenge_app_attest_uu,
                sign_up_username_password_challenge_captcha,
                sign_up_username_password_challenge_captcha_uu,
                sign_up_username_password_challenged,
                sign_up_username_password_challenged_uu,
                sign_up_username_password_rate_limited_ip,
                sign_up_username_password_rate_limited_ip_uu,
                sign_up_username_password_rate_limited_persistent_attestation_device_id,
                sign_up_username_password_rate_limited_persistent_attestation_device_id_uu,

                non_cos_sign_up_username_password_unset,
                non_cos_sign_up_username_password_unset_uu,
                non_cos_sign_up_username_password_success,
                non_cos_sign_up_username_password_success_uu,
                non_cos_sign_up_username_password_success_email_taken,
                non_cos_sign_up_username_password_success_email_taken_uu,
                non_cos_sign_up_username_password_unexpected_failure,
                non_cos_sign_up_username_password_unexpected_failure_uu,
                non_cos_sign_up_username_password_invalid_argument,
                non_cos_sign_up_username_password_invalid_argument_uu,
                non_cos_sign_up_username_password_app_version_upgrade,
                non_cos_sign_up_username_password_app_version_upgrade_uu,
                non_cos_sign_up_username_password_blocked,
                non_cos_sign_up_username_password_blocked_uu,
                non_cos_sign_up_username_password_ineligible_for_registration,
                non_cos_sign_up_username_password_ineligible_for_registration_uu,
                non_cos_sign_up_username_password_denied,
                non_cos_sign_up_username_password_denied_uu,
                non_cos_sign_up_username_password_failed_challenge,
                non_cos_sign_up_username_password_failed_challenge_uu,
                non_cos_sign_up_username_password_weak_password,
                non_cos_sign_up_username_password_weak_password_uu,
                non_cos_sign_up_username_password_username_taken,
                non_cos_sign_up_username_password_username_taken_uu,
                non_cos_sign_up_username_password_email_taken,
                non_cos_sign_up_username_password_email_taken_uu,
                non_cos_sign_up_username_password_permission_denied,
                non_cos_sign_up_username_password_permission_denied_uu,
                non_cos_sign_up_username_password_abusive_username,
                non_cos_sign_up_username_password_abusive_username_uu,
                non_cos_sign_up_username_password_challenge_safetynet,
                non_cos_sign_up_username_password_challenge_safetynet_uu,
                non_cos_sign_up_username_password_challenge_play_integrity,
                non_cos_sign_up_username_password_challenge_play_integrity_uu,
                non_cos_sign_up_username_password_challenge_key_attestation,
                non_cos_sign_up_username_password_challenge_key_attestation_uu,
                non_cos_sign_up_username_password_challenge_app_attest,
                non_cos_sign_up_username_password_challenge_app_attest_uu,
                non_cos_sign_up_username_password_challenge_captcha,
                non_cos_sign_up_username_password_challenge_captcha_uu,
                non_cos_sign_up_username_password_challenged,
                non_cos_sign_up_username_password_challenged_uu,
                non_cos_sign_up_username_password_rate_limited_ip,
                non_cos_sign_up_username_password_rate_limited_ip_uu,
                non_cos_sign_up_username_password_rate_limited_persistent_attestation_device_id,
                non_cos_sign_up_username_password_rate_limited_persistent_attestation_device_id_uu,

                cos_sign_up_username_password_unset,
                cos_sign_up_username_password_unset_uu,
                cos_sign_up_username_password_success,
                cos_sign_up_username_password_success_uu,
                cos_sign_up_username_password_success_email_taken,
                cos_sign_up_username_password_success_email_taken_uu,
                cos_sign_up_username_password_unexpected_failure,
                cos_sign_up_username_password_unexpected_failure_uu,
                cos_sign_up_username_password_invalid_argument,
                cos_sign_up_username_password_invalid_argument_uu,
                cos_sign_up_username_password_app_version_upgrade,
                cos_sign_up_username_password_app_version_upgrade_uu,
                cos_sign_up_username_password_blocked,
                cos_sign_up_username_password_blocked_uu,
                cos_sign_up_username_password_ineligible_for_registration,
                cos_sign_up_username_password_ineligible_for_registration_uu,
                cos_sign_up_username_password_denied,
                cos_sign_up_username_password_denied_uu,
                cos_sign_up_username_password_failed_challenge,
                cos_sign_up_username_password_failed_challenge_uu,
                cos_sign_up_username_password_weak_password,
                cos_sign_up_username_password_weak_password_uu,
                cos_sign_up_username_password_username_taken,
                cos_sign_up_username_password_username_taken_uu,
                cos_sign_up_username_password_email_taken,
                cos_sign_up_username_password_email_taken_uu,
                cos_sign_up_username_password_permission_denied,
                cos_sign_up_username_password_permission_denied_uu,
                cos_sign_up_username_password_abusive_username,
                cos_sign_up_username_password_abusive_username_uu,
                cos_sign_up_username_password_challenge_safetynet,
                cos_sign_up_username_password_challenge_safetynet_uu,
                cos_sign_up_username_password_challenge_play_integrity,
                cos_sign_up_username_password_challenge_play_integrity_uu,
                cos_sign_up_username_password_challenge_key_attestation,
                cos_sign_up_username_password_challenge_key_attestation_uu,
                cos_sign_up_username_password_challenge_app_attest,
                cos_sign_up_username_password_challenge_app_attest_uu,
                cos_sign_up_username_password_challenge_captcha,
                cos_sign_up_username_password_challenge_captcha_uu,
                cos_sign_up_username_password_challenged,
                cos_sign_up_username_password_challenged_uu,
                cos_sign_up_username_password_rate_limited_ip,
                cos_sign_up_username_password_rate_limited_ip_uu,
                cos_sign_up_username_password_rate_limited_persistent_attestation_device_id,
                cos_sign_up_username_password_rate_limited_persistent_attestation_device_id_uu,
            ],
            name="sign_up_server",
            bq_dialect="standard",
            inner_join_with_mapping=False,
            join_key=join_key,
            unit_id=unit_id
        )
    else:
        mt=None
        logger.warning("Sign-Up Server Metrics are not available for this study type.")
    return mt


def sign_up_server_rub(study_type, study_name, start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    unit_id = ""
    join_key = ""
    source_table = ""

    if study_type == "mobile_device_level_study":
        unit_id = "config_device_id"
        join_key = "config_device_id"
        source_table = """ (
            SELECT 
              event_date,
              usermap.config_device_id,
              usermap.client_id,
              register_result,
              ads_defense_name,
              safetynet_status,
              key_attestation_status,
              play_integrity_status,
              app_attest_status
            FROM 
             `sc-portal.abtest_device_usermap_cumulative.{study_name}__{end_date}` usermap
            INNER JOIN (
              SELECT 
                DATE(TIMESTAMP_SUB(TIMESTAMP_MILLIS(timestamp), INTERVAL 8 HOUR)) as event_date,
                client_id,
                IFNULL(register_result, "UNKNOWN") AS register_result,
                ads_defense_name,
                safetynet_status,
                key_attestation_status,
                play_integrity_status,
                app_attest_status
              FROM 
                `sc-infosec-services.abuse_analytics_data_feed.register_user_basic_*`
              WHERE _table_suffix between '{start_date}' and FORMAT_DATE('%Y%m%d',DATE_ADD(PARSE_DATE('%Y%m%d', '{end_date}'), INTERVAL 1 DAY))
              AND DATE(TIMESTAMP_SUB(TIMESTAMP_MILLIS(timestamp), INTERVAL 8 HOUR)) BETWEEN parse_date('%Y%m%d','{start_date}') and parse_date('%Y%m%d','{end_date}')
            ) register_user_basic
            ON
              usermap.client_id=register_user_basic.client_id
        )
        """.format(study_name=study_name, start_date=start_date, end_date=end_date)

    sign_up_result_success = Metric('sign_up_result_success', 'sign_up_result_success', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_up_result_success_uu = Metric('sign_up_result_success_uu', 'sign_up_result_success_uu', dist='bin', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_up_result_blocked_by_ads = Metric('sign_up_result_blocked_by_ads', 'sign_up_result_blocked_by_ads', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_result_blocked_by_ads_uu = Metric('sign_up_result_blocked_by_ads_uu', 'sign_up_result_blocked_by_ads_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_result_unknown = Metric('sign_up_result_unknown', 'sign_up_result_unknown', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_result_unknown_uu = Metric('sign_up_result_unknown_uu', 'sign_up_result_unknown_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_result_username_taken = Metric('sign_up_result_username_taken', 'sign_up_result_username_taken', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_result_username_taken_uu = Metric('sign_up_result_username_taken_uu', 'sign_up_result_username_taken_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_result_android_request_safetynet = Metric('sign_up_result_android_request_safetynet', 'sign_up_result_android_request_safetynet', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_result_android_request_safetynet_uu = Metric('sign_up_result_android_request_safetynet_uu', 'sign_up_result_android_request_safetynet_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_result_invalid_username = Metric('sign_up_result_invalid_username', 'sign_up_result_invalid_username', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_result_invalid_username_uu = Metric('sign_up_result_invalid_username_uu', 'sign_up_result_invalid_username_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_result_unexpected_failure = Metric('sign_up_result_unexpected_failure', 'sign_up_result_unexpected_failure', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_result_unexpected_failure_uu = Metric('sign_up_result_unexpected_failure_uu', 'sign_up_result_unexpected_failure_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_result_too_many_retries = Metric('sign_up_result_too_many_retries', 'sign_up_result_too_many_retries', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_result_too_many_retries_uu = Metric('sign_up_result_too_many_retries_uu', 'sign_up_result_too_many_retries_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_result_invalid_birthdate = Metric('sign_up_result_invalid_birthdate', 'sign_up_result_invalid_birthdate', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_result_invalid_birthdate_uu = Metric('sign_up_result_invalid_birthdate_uu', 'sign_up_result_invalid_birthdate_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_result_invalid_password = Metric('sign_up_result_invalid_password', 'sign_up_result_invalid_password', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_result_invalid_password_uu = Metric('sign_up_result_invalid_password_uu', 'sign_up_result_invalid_password_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_result_weak_password = Metric('sign_up_result_weak_password', 'sign_up_result_weak_password', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_result_weak_password_uu = Metric('sign_up_result_weak_password_uu', 'sign_up_result_weak_password_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_result_invalid_display_name = Metric('sign_up_result_invalid_display_name', 'sign_up_result_invalid_display_name', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_result_invalid_display_name_uu = Metric('sign_up_result_invalid_display_name_uu', 'sign_up_result_invalid_display_name_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_result_app_version_upgrade = Metric('sign_up_result_app_version_upgrade', 'sign_up_result_app_version_upgrade', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_result_app_version_upgrade_uu = Metric('sign_up_result_app_version_upgrade_uu', 'sign_up_result_app_version_upgrade_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_unset = Metric('sign_up_blocked_by_ads_defense_unset', 'sign_up_blocked_by_ads_defense_unset', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_unset_uu = Metric('sign_up_blocked_by_ads_defense_unset_uu', 'sign_up_blocked_by_ads_defense_unset_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_ip_blocking = Metric('sign_up_blocked_by_ads_defense_ip_blocking', 'sign_up_blocked_by_ads_defense_ip_blocking', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_ip_blocking_uu = Metric('sign_up_blocked_by_ads_defense_ip_blocking_uu', 'sign_up_blocked_by_ads_defense_ip_blocking_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_device_id_rate_limiting = Metric('sign_up_blocked_by_ads_defense_device_id_rate_limiting', 'sign_up_blocked_by_ads_defense_device_id_rate_limiting', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_device_id_rate_limiting_uu = Metric('sign_up_blocked_by_ads_defense_device_id_rate_limiting_uu', 'sign_up_blocked_by_ads_defense_device_id_rate_limiting_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_attestation = Metric('sign_up_blocked_by_ads_defense_attestation', 'sign_up_blocked_by_ads_defense_attestation', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_attestation_uu = Metric('sign_up_blocked_by_ads_defense_attestation_uu', 'sign_up_blocked_by_ads_defense_attestation_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_user_locked = Metric('sign_up_blocked_by_ads_defense_user_locked', 'sign_up_blocked_by_ads_defense_user_locked', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_user_locked_uu = Metric('sign_up_blocked_by_ads_defense_user_locked_uu', 'sign_up_blocked_by_ads_defense_user_locked_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_odlv = Metric('sign_up_blocked_by_ads_defense_odlv', 'sign_up_blocked_by_ads_defense_odlv', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_odlv_uu = Metric('sign_up_blocked_by_ads_defense_odlv_uu', 'sign_up_blocked_by_ads_defense_odlv_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_device_blocking = Metric('sign_up_blocked_by_ads_defense_device_blocking', 'sign_up_blocked_by_ads_defense_device_blocking', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_device_blocking_uu = Metric('sign_up_blocked_by_ads_defense_device_blocking_uu', 'sign_up_blocked_by_ads_defense_device_blocking_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_high_device_user_association_blocking = Metric('sign_up_blocked_by_ads_defense_high_device_user_association_blocking', 'sign_up_blocked_by_ads_defense_high_device_user_association_blocking', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_high_device_user_association_blocking_uu = Metric('sign_up_blocked_by_ads_defense_high_device_user_association_blocking_uu', 'sign_up_blocked_by_ads_defense_high_device_user_association_blocking_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_email_reputation_blocking = Metric('sign_up_blocked_by_ads_defense_email_reputation_blocking', 'sign_up_blocked_by_ads_defense_email_reputation_blocking', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_email_reputation_blocking_uu = Metric('sign_up_blocked_by_ads_defense_email_reputation_blocking_uu', 'sign_up_blocked_by_ads_defense_email_reputation_blocking_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_device_check = Metric('sign_up_blocked_by_ads_defense_device_check', 'sign_up_blocked_by_ads_defense_device_check', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_device_check_uu = Metric('sign_up_blocked_by_ads_defense_device_check_uu', 'sign_up_blocked_by_ads_defense_device_check_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_vendor_integrity_check = Metric('sign_up_blocked_by_ads_defense_vendor_integrity_check', 'sign_up_blocked_by_ads_defense_vendor_integrity_check', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_vendor_integrity_check_uu = Metric('sign_up_blocked_by_ads_defense_vendor_integrity_check_uu', 'sign_up_blocked_by_ads_defense_vendor_integrity_check_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_vendor_integrity_retry = Metric('sign_up_blocked_by_ads_defense_vendor_integrity_retry', 'sign_up_blocked_by_ads_defense_vendor_integrity_retry', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_vendor_integrity_retry_uu = Metric('sign_up_blocked_by_ads_defense_vendor_integrity_retry_uu', 'sign_up_blocked_by_ads_defense_vendor_integrity_retry_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_async_email_validation = Metric('sign_up_blocked_by_ads_defense_async_email_validation', 'sign_up_blocked_by_ads_defense_async_email_validation', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_async_email_validation_uu = Metric('sign_up_blocked_by_ads_defense_async_email_validation_uu', 'sign_up_blocked_by_ads_defense_async_email_validation_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_android_mismatch_useragent = Metric('sign_up_blocked_by_ads_defense_android_mismatch_useragent', 'sign_up_blocked_by_ads_defense_android_mismatch_useragent', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_android_mismatch_useragent_uu = Metric('sign_up_blocked_by_ads_defense_android_mismatch_useragent_uu', 'sign_up_blocked_by_ads_defense_android_mismatch_useragent_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_phone_iar_challenge_requested = Metric('sign_up_blocked_by_ads_defense_phone_iar_challenge_requested', 'sign_up_blocked_by_ads_defense_phone_iar_challenge_requested', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_phone_iar_challenge_requested_uu = Metric('sign_up_blocked_by_ads_defense_phone_iar_challenge_requested_uu', 'sign_up_blocked_by_ads_defense_phone_iar_challenge_requested_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_client_integrity_nonce_rate_limiting = Metric('sign_up_blocked_by_ads_defense_client_integrity_nonce_rate_limiting', 'sign_up_blocked_by_ads_defense_client_integrity_nonce_rate_limiting', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_client_integrity_nonce_rate_limiting_uu = Metric('sign_up_blocked_by_ads_defense_client_integrity_nonce_rate_limiting_uu', 'sign_up_blocked_by_ads_defense_client_integrity_nonce_rate_limiting_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_invalid_useragent = Metric('sign_up_blocked_by_ads_defense_invalid_useragent', 'sign_up_blocked_by_ads_defense_invalid_useragent', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_invalid_useragent_uu = Metric('sign_up_blocked_by_ads_defense_invalid_useragent_uu', 'sign_up_blocked_by_ads_defense_invalid_useragent_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_invalid_input = Metric('sign_up_blocked_by_ads_defense_invalid_input', 'sign_up_blocked_by_ads_defense_invalid_input', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_invalid_input_uu = Metric('sign_up_blocked_by_ads_defense_invalid_input_uu', 'sign_up_blocked_by_ads_defense_invalid_input_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_invalid_email_domain = Metric('sign_up_blocked_by_ads_defense_invalid_email_domain', 'sign_up_blocked_by_ads_defense_invalid_email_domain', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_invalid_email_domain_uu = Metric('sign_up_blocked_by_ads_defense_invalid_email_domain_uu', 'sign_up_blocked_by_ads_defense_invalid_email_domain_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_web_bot_detection = Metric('sign_up_blocked_by_ads_defense_web_bot_detection', 'sign_up_blocked_by_ads_defense_web_bot_detection', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_web_bot_detection_uu = Metric('sign_up_blocked_by_ads_defense_web_bot_detection_uu', 'sign_up_blocked_by_ads_defense_web_bot_detection_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_web_registration_app_login = Metric('sign_up_blocked_by_ads_defense_web_registration_app_login', 'sign_up_blocked_by_ads_defense_web_registration_app_login', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_blocked_by_ads_defense_web_registration_app_login_uu = Metric('sign_up_blocked_by_ads_defense_web_registration_app_login_uu', 'sign_up_blocked_by_ads_defense_web_registration_app_login_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_client_attestation_timeout = Metric('sign_up_safetynet_status_client_attestation_timeout', 'sign_up_safetynet_status_client_attestation_timeout', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_client_attestation_timeout_uu = Metric('sign_up_safetynet_status_client_attestation_timeout_uu', 'sign_up_safetynet_status_client_attestation_timeout_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_android_attestation_missing = Metric('sign_up_safetynet_status_android_attestation_missing', 'sign_up_safetynet_status_android_attestation_missing', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_android_attestation_missing_uu = Metric('sign_up_safetynet_status_android_attestation_missing_uu', 'sign_up_safetynet_status_android_attestation_missing_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_android_attestation_hardware_integrity_supported_but_missing = Metric('sign_up_safetynet_status_android_attestation_hardware_integrity_supported_but_missing', 'sign_up_safetynet_status_android_attestation_hardware_integrity_supported_but_missing', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_android_attestation_hardware_integrity_supported_but_missing_uu = Metric('sign_up_safetynet_status_android_attestation_hardware_integrity_supported_but_missing_uu', 'sign_up_safetynet_status_android_attestation_hardware_integrity_supported_but_missing_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_server_hardware_integrity_supported_but_missing = Metric('sign_up_safetynet_status_server_hardware_integrity_supported_but_missing', 'sign_up_safetynet_status_server_hardware_integrity_supported_but_missing', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_server_hardware_integrity_supported_but_missing_uu = Metric('sign_up_safetynet_status_server_hardware_integrity_supported_but_missing_uu', 'sign_up_safetynet_status_server_hardware_integrity_supported_but_missing_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_internal_safetynet_error = Metric('sign_up_safetynet_status_internal_safetynet_error', 'sign_up_safetynet_status_internal_safetynet_error', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_internal_safetynet_error_uu = Metric('sign_up_safetynet_status_internal_safetynet_error_uu', 'sign_up_safetynet_status_internal_safetynet_error_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_vendor_attestation_missing = Metric('sign_up_safetynet_status_vendor_attestation_missing', 'sign_up_safetynet_status_vendor_attestation_missing', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_vendor_attestation_missing_uu = Metric('sign_up_safetynet_status_vendor_attestation_missing_uu', 'sign_up_safetynet_status_vendor_attestation_missing_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_server_timestamp_invalid = Metric('sign_up_safetynet_status_server_timestamp_invalid', 'sign_up_safetynet_status_server_timestamp_invalid', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_server_timestamp_invalid_uu = Metric('sign_up_safetynet_status_server_timestamp_invalid_uu', 'sign_up_safetynet_status_server_timestamp_invalid_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_android_attestation_expired = Metric('sign_up_safetynet_status_android_attestation_expired', 'sign_up_safetynet_status_android_attestation_expired', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_android_attestation_expired_uu = Metric('sign_up_safetynet_status_android_attestation_expired_uu', 'sign_up_safetynet_status_android_attestation_expired_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_success = Metric('sign_up_safetynet_status_success', 'sign_up_safetynet_status_success', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_up_safetynet_status_success_uu = Metric('sign_up_safetynet_status_success_uu', 'sign_up_safetynet_status_success_uu', dist='bin', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_up_safetynet_status_server_apk_certificate_mismatch = Metric('sign_up_safetynet_status_server_apk_certificate_mismatch', 'sign_up_safetynet_status_server_apk_certificate_mismatch', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_server_apk_certificate_mismatch_uu = Metric('sign_up_safetynet_status_server_apk_certificate_mismatch_uu', 'sign_up_safetynet_status_server_apk_certificate_mismatch_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_server_signature_validation_failure = Metric('sign_up_safetynet_status_server_signature_validation_failure', 'sign_up_safetynet_status_server_signature_validation_failure', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_server_signature_validation_failure_uu = Metric('sign_up_safetynet_status_server_signature_validation_failure_uu', 'sign_up_safetynet_status_server_signature_validation_failure_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_android_attestation_verify_io_exception = Metric('sign_up_safetynet_status_android_attestation_verify_io_exception', 'sign_up_safetynet_status_android_attestation_verify_io_exception', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_android_attestation_verify_io_exception_uu = Metric('sign_up_safetynet_status_android_attestation_verify_io_exception_uu', 'sign_up_safetynet_status_android_attestation_verify_io_exception_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_server_basic_integrity_failure = Metric('sign_up_safetynet_status_server_basic_integrity_failure', 'sign_up_safetynet_status_server_basic_integrity_failure', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_server_basic_integrity_failure_uu = Metric('sign_up_safetynet_status_server_basic_integrity_failure_uu', 'sign_up_safetynet_status_server_basic_integrity_failure_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_android_attestation_internal_error = Metric('sign_up_safetynet_status_android_attestation_internal_error', 'sign_up_safetynet_status_android_attestation_internal_error', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_android_attestation_internal_error_uu = Metric('sign_up_safetynet_status_android_attestation_internal_error_uu', 'sign_up_safetynet_status_android_attestation_internal_error_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_internal_client_error = Metric('sign_up_safetynet_status_internal_client_error', 'sign_up_safetynet_status_internal_client_error', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_internal_client_error_uu = Metric('sign_up_safetynet_status_internal_client_error_uu', 'sign_up_safetynet_status_internal_client_error_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_internal_client_error_non_retriable = Metric('sign_up_safetynet_status_internal_client_error_non_retriable', 'sign_up_safetynet_status_internal_client_error_non_retriable', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_internal_client_error_non_retriable_uu = Metric('sign_up_safetynet_status_internal_client_error_non_retriable_uu', 'sign_up_safetynet_status_internal_client_error_non_retriable_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_server_cts_profile_mismatch = Metric('sign_up_safetynet_status_server_cts_profile_mismatch', 'sign_up_safetynet_status_server_cts_profile_mismatch', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_server_cts_profile_mismatch_uu = Metric('sign_up_safetynet_status_server_cts_profile_mismatch_uu', 'sign_up_safetynet_status_server_cts_profile_mismatch_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_android_attestation_verify_success = Metric('sign_up_safetynet_status_android_attestation_verify_success', 'sign_up_safetynet_status_android_attestation_verify_success', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_up_safetynet_status_android_attestation_verify_success_uu = Metric('sign_up_safetynet_status_android_attestation_verify_success_uu', 'sign_up_safetynet_status_android_attestation_verify_success_uu', dist='bin', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_up_safetynet_status_android_attestation_sig_mismatch = Metric('sign_up_safetynet_status_android_attestation_sig_mismatch', 'sign_up_safetynet_status_android_attestation_sig_mismatch', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_android_attestation_sig_mismatch_uu = Metric('sign_up_safetynet_status_android_attestation_sig_mismatch_uu', 'sign_up_safetynet_status_android_attestation_sig_mismatch_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_android_attestation_cts_profile_not_match = Metric('sign_up_safetynet_status_android_attestation_cts_profile_not_match', 'sign_up_safetynet_status_android_attestation_cts_profile_not_match', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_safetynet_status_android_attestation_cts_profile_not_match_uu = Metric('sign_up_safetynet_status_android_attestation_cts_profile_not_match_uu', 'sign_up_safetynet_status_android_attestation_cts_profile_not_match_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_key_attestation_status_vendor_attestation_missing = Metric('sign_up_key_attestation_status_vendor_attestation_missing', 'sign_up_key_attestation_status_vendor_attestation_missing', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_key_attestation_status_vendor_attestation_missing_uu = Metric('sign_up_key_attestation_status_vendor_attestation_missing_uu', 'sign_up_key_attestation_status_vendor_attestation_missing_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_key_attestation_status_acceptable = Metric('sign_up_key_attestation_status_acceptable', 'sign_up_key_attestation_status_acceptable', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_up_key_attestation_status_acceptable_uu = Metric('sign_up_key_attestation_status_acceptable_uu', 'sign_up_key_attestation_status_acceptable_uu', dist='bin', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_up_key_attestation_status_remote_client_error = Metric('sign_up_key_attestation_status_remote_client_error', 'sign_up_key_attestation_status_remote_client_error', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_key_attestation_status_remote_client_error_uu = Metric('sign_up_key_attestation_status_remote_client_error_uu', 'sign_up_key_attestation_status_remote_client_error_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_key_attestation_status_unacceptable_root_certificate = Metric('sign_up_key_attestation_status_unacceptable_root_certificate', 'sign_up_key_attestation_status_unacceptable_root_certificate', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_key_attestation_status_unacceptable_root_certificate_uu = Metric('sign_up_key_attestation_status_unacceptable_root_certificate_uu', 'sign_up_key_attestation_status_unacceptable_root_certificate_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_key_attestation_status_unsupported_by_client = Metric('sign_up_key_attestation_status_unsupported_by_client', 'sign_up_key_attestation_status_unsupported_by_client', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_key_attestation_status_unsupported_by_client_uu = Metric('sign_up_key_attestation_status_unsupported_by_client_uu', 'sign_up_key_attestation_status_unsupported_by_client_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_key_attestation_status_insecure_remote_device = Metric('sign_up_key_attestation_status_insecure_remote_device', 'sign_up_key_attestation_status_insecure_remote_device', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_key_attestation_status_insecure_remote_device_uu = Metric('sign_up_key_attestation_status_insecure_remote_device_uu', 'sign_up_key_attestation_status_insecure_remote_device_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_key_attestation_status_invalid_certificate_chain = Metric('sign_up_key_attestation_status_invalid_certificate_chain', 'sign_up_key_attestation_status_invalid_certificate_chain', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_key_attestation_status_invalid_certificate_chain_uu = Metric('sign_up_key_attestation_status_invalid_certificate_chain_uu', 'sign_up_key_attestation_status_invalid_certificate_chain_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_key_attestation_status_unacceptable_application_id = Metric('sign_up_key_attestation_status_unacceptable_application_id', 'sign_up_key_attestation_status_unacceptable_application_id', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_key_attestation_status_unacceptable_application_id_uu = Metric('sign_up_key_attestation_status_unacceptable_application_id_uu', 'sign_up_key_attestation_status_unacceptable_application_id_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_play_integrity_status_vendor_attestation_missing = Metric('sign_up_play_integrity_status_vendor_attestation_missing', 'sign_up_play_integrity_status_vendor_attestation_missing', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_play_integrity_status_vendor_attestation_missing_uu = Metric('sign_up_play_integrity_status_vendor_attestation_missing_uu', 'sign_up_play_integrity_status_vendor_attestation_missing_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_play_integrity_status_success_valid_payload = Metric('sign_up_play_integrity_status_success_valid_payload', 'sign_up_play_integrity_status_success_valid_payload', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_up_play_integrity_status_success_valid_payload_uu = Metric('sign_up_play_integrity_status_success_valid_payload_uu', 'sign_up_play_integrity_status_success_valid_payload_uu', dist='bin', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_up_play_integrity_status_vendor_attestation_non_actionable_client_error = Metric('sign_up_play_integrity_status_vendor_attestation_non_actionable_client_error', 'sign_up_play_integrity_status_vendor_attestation_non_actionable_client_error', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_play_integrity_status_vendor_attestation_non_actionable_client_error_uu = Metric('sign_up_play_integrity_status_vendor_attestation_non_actionable_client_error_uu', 'sign_up_play_integrity_status_vendor_attestation_non_actionable_client_error_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_play_integrity_status_unacceptable_device_integrity_status = Metric('sign_up_play_integrity_status_unacceptable_device_integrity_status', 'sign_up_play_integrity_status_unacceptable_device_integrity_status', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_play_integrity_status_unacceptable_device_integrity_status_uu = Metric('sign_up_play_integrity_status_unacceptable_device_integrity_status_uu', 'sign_up_play_integrity_status_unacceptable_device_integrity_status_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_play_integrity_status_vendor_attestation_play_store_missing = Metric('sign_up_play_integrity_status_vendor_attestation_play_store_missing', 'sign_up_play_integrity_status_vendor_attestation_play_store_missing', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_play_integrity_status_vendor_attestation_play_store_missing_uu = Metric('sign_up_play_integrity_status_vendor_attestation_play_store_missing_uu', 'sign_up_play_integrity_status_vendor_attestation_play_store_missing_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_play_integrity_status_vendor_attestation_retriable_client_error = Metric('sign_up_play_integrity_status_vendor_attestation_retriable_client_error', 'sign_up_play_integrity_status_vendor_attestation_retriable_client_error', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_play_integrity_status_vendor_attestation_retriable_client_error_uu = Metric('sign_up_play_integrity_status_vendor_attestation_retriable_client_error_uu', 'sign_up_play_integrity_status_vendor_attestation_retriable_client_error_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_play_integrity_status_token_expired = Metric('sign_up_play_integrity_status_token_expired', 'sign_up_play_integrity_status_token_expired', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_play_integrity_status_token_expired_uu = Metric('sign_up_play_integrity_status_token_expired_uu', 'sign_up_play_integrity_status_token_expired_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_play_integrity_status_decryption_failure = Metric('sign_up_play_integrity_status_decryption_failure', 'sign_up_play_integrity_status_decryption_failure', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_play_integrity_status_decryption_failure_uu = Metric('sign_up_play_integrity_status_decryption_failure_uu', 'sign_up_play_integrity_status_decryption_failure_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_vendor_attestation_missing = Metric('sign_up_app_attest_status_vendor_attestation_missing', 'sign_up_app_attest_status_vendor_attestation_missing', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_vendor_attestation_missing_uu = Metric('sign_up_app_attest_status_vendor_attestation_missing_uu', 'sign_up_app_attest_status_vendor_attestation_missing_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_success = Metric('sign_up_app_attest_status_success', 'sign_up_app_attest_status_success', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_up_app_attest_status_success_uu = Metric('sign_up_app_attest_status_success_uu', 'sign_up_app_attest_status_success_uu', dist='bin', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_up_app_attest_status_client_error_key_generation_and_attestation_timed_out = Metric('sign_up_app_attest_status_client_error_key_generation_and_attestation_timed_out', 'sign_up_app_attest_status_client_error_key_generation_and_attestation_timed_out', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_client_error_key_generation_and_attestation_timed_out_uu = Metric('sign_up_app_attest_status_client_error_key_generation_and_attestation_timed_out_uu', 'sign_up_app_attest_status_client_error_key_generation_and_attestation_timed_out_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_client_error_unsupported_ios_13_or_below = Metric('sign_up_app_attest_status_client_error_unsupported_ios_13_or_below', 'sign_up_app_attest_status_client_error_unsupported_ios_13_or_below', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_client_error_unsupported_ios_13_or_below_uu = Metric('sign_up_app_attest_status_client_error_unsupported_ios_13_or_below_uu', 'sign_up_app_attest_status_client_error_unsupported_ios_13_or_below_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_client_error_server_unavailable = Metric('sign_up_app_attest_status_client_error_server_unavailable', 'sign_up_app_attest_status_client_error_server_unavailable', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_client_error_server_unavailable_uu = Metric('sign_up_app_attest_status_client_error_server_unavailable_uu', 'sign_up_app_attest_status_client_error_server_unavailable_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_assertion_signature_invalid = Metric('sign_up_app_attest_status_assertion_signature_invalid', 'sign_up_app_attest_status_assertion_signature_invalid', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_assertion_signature_invalid_uu = Metric('sign_up_app_attest_status_assertion_signature_invalid_uu', 'sign_up_app_attest_status_assertion_signature_invalid_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_client_error_key_generation_timed_out = Metric('sign_up_app_attest_status_client_error_key_generation_timed_out', 'sign_up_app_attest_status_client_error_key_generation_timed_out', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_client_error_key_generation_timed_out_uu = Metric('sign_up_app_attest_status_client_error_key_generation_timed_out_uu', 'sign_up_app_attest_status_client_error_key_generation_timed_out_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_client_error_invalid_key = Metric('sign_up_app_attest_status_client_error_invalid_key', 'sign_up_app_attest_status_client_error_invalid_key', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_client_error_invalid_key_uu = Metric('sign_up_app_attest_status_client_error_invalid_key_uu', 'sign_up_app_attest_status_client_error_invalid_key_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_client_error_unsupported_ios_14_or_above = Metric('sign_up_app_attest_status_client_error_unsupported_ios_14_or_above', 'sign_up_app_attest_status_client_error_unsupported_ios_14_or_above', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_client_error_unsupported_ios_14_or_above_uu = Metric('sign_up_app_attest_status_client_error_unsupported_ios_14_or_above_uu', 'sign_up_app_attest_status_client_error_unsupported_ios_14_or_above_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_client_error_unknown_system_failure = Metric('sign_up_app_attest_status_client_error_unknown_system_failure', 'sign_up_app_attest_status_client_error_unknown_system_failure', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_client_error_unknown_system_failure_uu = Metric('sign_up_app_attest_status_client_error_unknown_system_failure_uu', 'sign_up_app_attest_status_client_error_unknown_system_failure_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_client_error_key_attestation_timed_out = Metric('sign_up_app_attest_status_client_error_key_attestation_timed_out', 'sign_up_app_attest_status_client_error_key_attestation_timed_out', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_client_error_key_attestation_timed_out_uu = Metric('sign_up_app_attest_status_client_error_key_attestation_timed_out_uu', 'sign_up_app_attest_status_client_error_key_attestation_timed_out_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_client_error_invalid_input = Metric('sign_up_app_attest_status_client_error_invalid_input', 'sign_up_app_attest_status_client_error_invalid_input', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_client_error_invalid_input_uu = Metric('sign_up_app_attest_status_client_error_invalid_input_uu', 'sign_up_app_attest_status_client_error_invalid_input_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_attestation_auth_data_rp_id_mismatch = Metric('sign_up_app_attest_status_attestation_auth_data_rp_id_mismatch', 'sign_up_app_attest_status_attestation_auth_data_rp_id_mismatch', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_attestation_auth_data_rp_id_mismatch_uu = Metric('sign_up_app_attest_status_attestation_auth_data_rp_id_mismatch_uu', 'sign_up_app_attest_status_attestation_auth_data_rp_id_mismatch_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_client_error_assertion_timed_out = Metric('sign_up_app_attest_status_client_error_assertion_timed_out', 'sign_up_app_attest_status_client_error_assertion_timed_out', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_client_error_assertion_timed_out_uu = Metric('sign_up_app_attest_status_client_error_assertion_timed_out_uu', 'sign_up_app_attest_status_client_error_assertion_timed_out_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_attestation_cert_chain_invalid = Metric('sign_up_app_attest_status_attestation_cert_chain_invalid', 'sign_up_app_attest_status_attestation_cert_chain_invalid', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_attestation_cert_chain_invalid_uu = Metric('sign_up_app_attest_status_attestation_cert_chain_invalid_uu', 'sign_up_app_attest_status_attestation_cert_chain_invalid_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_attestation_key_id_mismatch = Metric('sign_up_app_attest_status_attestation_key_id_mismatch', 'sign_up_app_attest_status_attestation_key_id_mismatch', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_app_attest_status_attestation_key_id_mismatch_uu = Metric('sign_up_app_attest_status_attestation_key_id_mismatch_uu', 'sign_up_app_attest_status_attestation_key_id_mismatch_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)

    if source_table != "":
        mt = MetricTable(
            sql=""" 
            SELECT
              TIMESTAMP(event_date) as ts,
              {unit_id},
              SUM(IF(register_result='SUCCESS', 1, 0)) as sign_up_result_success,
              MAX(IF(register_result='SUCCESS', 1, 0)) as sign_up_result_success_uu,
              SUM(IF(register_result='BLOCKED_BY_ADS', 1, 0)) as sign_up_result_blocked_by_ads,
              MAX(IF(register_result='BLOCKED_BY_ADS', 1, 0)) as sign_up_result_blocked_by_ads_uu,
              SUM(IF(register_result='UNKNOWN', 1, 0)) as sign_up_result_unknown,
              MAX(IF(register_result='UNKNOWN', 1, 0)) as sign_up_result_unknown_uu,
              SUM(IF(register_result='USERNAME_TAKEN', 1, 0)) as sign_up_result_username_taken,
              MAX(IF(register_result='USERNAME_TAKEN', 1, 0)) as sign_up_result_username_taken_uu,
              SUM(IF(register_result='ANDROID_REQUEST_SAFETYNET', 1, 0)) as sign_up_result_android_request_safetynet,
              MAX(IF(register_result='ANDROID_REQUEST_SAFETYNET', 1, 0)) as sign_up_result_android_request_safetynet_uu,
              SUM(IF(register_result='INVALID_USERNAME', 1, 0)) as sign_up_result_invalid_username,
              MAX(IF(register_result='INVALID_USERNAME', 1, 0)) as sign_up_result_invalid_username_uu,
              SUM(IF(register_result='UNEXPECTED_FAILURE', 1, 0)) as sign_up_result_unexpected_failure,
              MAX(IF(register_result='UNEXPECTED_FAILURE', 1, 0)) as sign_up_result_unexpected_failure_uu,
              SUM(IF(register_result='TOO_MANY_RETRIES', 1, 0)) as sign_up_result_too_many_retries,
              MAX(IF(register_result='TOO_MANY_RETRIES', 1, 0)) as sign_up_result_too_many_retries_uu,
              SUM(IF(register_result='INVALID_BIRTHDATE', 1, 0)) as sign_up_result_invalid_birthdate,
              MAX(IF(register_result='INVALID_BIRTHDATE', 1, 0)) as sign_up_result_invalid_birthdate_uu,
              SUM(IF(register_result='INVALID_PASSWORD', 1, 0)) as sign_up_result_invalid_password,
              MAX(IF(register_result='INVALID_PASSWORD', 1, 0)) as sign_up_result_invalid_password_uu,
              SUM(IF(register_result='WEAK_PASSWORD', 1, 0)) as sign_up_result_weak_password,
              MAX(IF(register_result='WEAK_PASSWORD', 1, 0)) as sign_up_result_weak_password_uu,
              SUM(IF(register_result='INVALID_DISPLAY_NAME', 1, 0)) as sign_up_result_invalid_display_name,
              MAX(IF(register_result='INVALID_DISPLAY_NAME', 1, 0)) as sign_up_result_invalid_display_name_uu,
              SUM(IF(register_result='APP_VERSION_UPGRADE', 1, 0)) as sign_up_result_app_version_upgrade,
              MAX(IF(register_result='APP_VERSION_UPGRADE', 1, 0)) as sign_up_result_app_version_upgrade_uu,
              SUM(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=0, 1, 0)) as sign_up_blocked_by_ads_defense_unset,
              MAX(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=0, 1, 0)) as sign_up_blocked_by_ads_defense_unset_uu,
              SUM(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=1, 1, 0)) as sign_up_blocked_by_ads_defense_ip_blocking,
              MAX(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=1, 1, 0)) as sign_up_blocked_by_ads_defense_ip_blocking_uu,
              SUM(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=2, 1, 0)) as sign_up_blocked_by_ads_defense_device_id_rate_limiting,
              MAX(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=2, 1, 0)) as sign_up_blocked_by_ads_defense_device_id_rate_limiting_uu,
              SUM(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=3, 1, 0)) as sign_up_blocked_by_ads_defense_attestation,
              MAX(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=3, 1, 0)) as sign_up_blocked_by_ads_defense_attestation_uu,
              SUM(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=4, 1, 0)) as sign_up_blocked_by_ads_defense_user_locked,
              MAX(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=4, 1, 0)) as sign_up_blocked_by_ads_defense_user_locked_uu,
              SUM(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=5, 1, 0)) as sign_up_blocked_by_ads_defense_odlv,
              MAX(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=5, 1, 0)) as sign_up_blocked_by_ads_defense_odlv_uu,
              SUM(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=6, 1, 0)) as sign_up_blocked_by_ads_defense_device_blocking,
              MAX(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=6, 1, 0)) as sign_up_blocked_by_ads_defense_device_blocking_uu,
              SUM(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=7, 1, 0)) as sign_up_blocked_by_ads_defense_high_device_user_association_blocking,
              MAX(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=7, 1, 0)) as sign_up_blocked_by_ads_defense_high_device_user_association_blocking_uu,
              SUM(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=8, 1, 0)) as sign_up_blocked_by_ads_defense_email_reputation_blocking,
              MAX(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=8, 1, 0)) as sign_up_blocked_by_ads_defense_email_reputation_blocking_uu,
              SUM(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=9, 1, 0)) as sign_up_blocked_by_ads_defense_device_check,
              MAX(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=9, 1, 0)) as sign_up_blocked_by_ads_defense_device_check_uu,
              SUM(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=10, 1, 0)) as sign_up_blocked_by_ads_defense_vendor_integrity_check,
              MAX(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=10, 1, 0)) as sign_up_blocked_by_ads_defense_vendor_integrity_check_uu,
              SUM(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=11, 1, 0)) as sign_up_blocked_by_ads_defense_vendor_integrity_retry,
              MAX(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=11, 1, 0)) as sign_up_blocked_by_ads_defense_vendor_integrity_retry_uu,
              SUM(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=12, 1, 0)) as sign_up_blocked_by_ads_defense_async_email_validation,
              MAX(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=12, 1, 0)) as sign_up_blocked_by_ads_defense_async_email_validation_uu,
              SUM(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=13, 1, 0)) as sign_up_blocked_by_ads_defense_android_mismatch_useragent,
              MAX(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=13, 1, 0)) as sign_up_blocked_by_ads_defense_android_mismatch_useragent_uu,
              SUM(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=14, 1, 0)) as sign_up_blocked_by_ads_defense_phone_iar_challenge_requested,
              MAX(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=14, 1, 0)) as sign_up_blocked_by_ads_defense_phone_iar_challenge_requested_uu,
              SUM(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=15, 1, 0)) as sign_up_blocked_by_ads_defense_client_integrity_nonce_rate_limiting,
              MAX(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=15, 1, 0)) as sign_up_blocked_by_ads_defense_client_integrity_nonce_rate_limiting_uu,
              SUM(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=16, 1, 0)) as sign_up_blocked_by_ads_defense_invalid_useragent,
              MAX(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=16, 1, 0)) as sign_up_blocked_by_ads_defense_invalid_useragent_uu,
              SUM(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=17, 1, 0)) as sign_up_blocked_by_ads_defense_invalid_input,
              MAX(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=17, 1, 0)) as sign_up_blocked_by_ads_defense_invalid_input_uu,
              SUM(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=19, 1, 0)) as sign_up_blocked_by_ads_defense_invalid_email_domain,
              MAX(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=19, 1, 0)) as sign_up_blocked_by_ads_defense_invalid_email_domain_uu,
              SUM(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=20, 1, 0)) as sign_up_blocked_by_ads_defense_web_bot_detection,
              MAX(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=20, 1, 0)) as sign_up_blocked_by_ads_defense_web_bot_detection_uu,
              SUM(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=21, 1, 0)) as sign_up_blocked_by_ads_defense_web_registration_app_login,
              MAX(IF(register_result='BLOCKED_BY_ADS' AND ads_defense_name=21, 1, 0)) as sign_up_blocked_by_ads_defense_web_registration_app_login_uu,
              SUM(IF(safetynet_status LIKE '%CLIENT_ATTESTATION_TIMEOUT%', 1, 0)) as sign_up_safetynet_status_client_attestation_timeout,
              MAX(IF(safetynet_status LIKE '%CLIENT_ATTESTATION_TIMEOUT%', 1, 0)) as sign_up_safetynet_status_client_attestation_timeout_uu,
              SUM(IF(safetynet_status LIKE '%android_attestation_missing%', 1, 0)) as sign_up_safetynet_status_android_attestation_missing,
              MAX(IF(safetynet_status LIKE '%android_attestation_missing%', 1, 0)) as sign_up_safetynet_status_android_attestation_missing_uu,
              SUM(IF(safetynet_status LIKE '%android_attestation_hardware_integrity_supported_but_missing%', 1, 0)) as sign_up_safetynet_status_android_attestation_hardware_integrity_supported_but_missing,
              MAX(IF(safetynet_status LIKE '%android_attestation_hardware_integrity_supported_but_missing%', 1, 0)) as sign_up_safetynet_status_android_attestation_hardware_integrity_supported_but_missing_uu,
              SUM(IF(safetynet_status LIKE '%SERVER_HARDWARE_INTEGRITY_SUPPORTED_BUT_MISSING%', 1, 0)) as sign_up_safetynet_status_server_hardware_integrity_supported_but_missing,
              MAX(IF(safetynet_status LIKE '%SERVER_HARDWARE_INTEGRITY_SUPPORTED_BUT_MISSING%', 1, 0)) as sign_up_safetynet_status_server_hardware_integrity_supported_but_missing_uu,
              SUM(IF(safetynet_status LIKE '%INTERNAL_SAFETYNET_ERROR%', 1, 0)) as sign_up_safetynet_status_internal_safetynet_error,
              MAX(IF(safetynet_status LIKE '%INTERNAL_SAFETYNET_ERROR%', 1, 0)) as sign_up_safetynet_status_internal_safetynet_error_uu,
              SUM(IF(safetynet_status LIKE '%VENDOR_ATTESTATION_MISSING%', 1, 0)) as sign_up_safetynet_status_vendor_attestation_missing,
              MAX(IF(safetynet_status LIKE '%VENDOR_ATTESTATION_MISSING%', 1, 0)) as sign_up_safetynet_status_vendor_attestation_missing_uu,
              SUM(IF(safetynet_status LIKE '%SERVER_TIMESTAMP_INVALID%', 1, 0)) as sign_up_safetynet_status_server_timestamp_invalid,
              MAX(IF(safetynet_status LIKE '%SERVER_TIMESTAMP_INVALID%', 1, 0)) as sign_up_safetynet_status_server_timestamp_invalid_uu,
              SUM(IF(safetynet_status LIKE '%android_attestation_expired%', 1, 0)) as sign_up_safetynet_status_android_attestation_expired,
              MAX(IF(safetynet_status LIKE '%android_attestation_expired%', 1, 0)) as sign_up_safetynet_status_android_attestation_expired_uu,
              SUM(IF(safetynet_status LIKE '%SUCCESS%', 1, 0)) as sign_up_safetynet_status_success,
              MAX(IF(safetynet_status LIKE '%SUCCESS%', 1, 0)) as sign_up_safetynet_status_success_uu,
              SUM(IF(safetynet_status LIKE '%SERVER_APK_CERTIFICATE_MISMATCH%', 1, 0)) as sign_up_safetynet_status_server_apk_certificate_mismatch,
              MAX(IF(safetynet_status LIKE '%SERVER_APK_CERTIFICATE_MISMATCH%', 1, 0)) as sign_up_safetynet_status_server_apk_certificate_mismatch_uu,
              SUM(IF(safetynet_status LIKE '%SERVER_SIGNATURE_VALIDATION_FAILURE%', 1, 0)) as sign_up_safetynet_status_server_signature_validation_failure,
              MAX(IF(safetynet_status LIKE '%SERVER_SIGNATURE_VALIDATION_FAILURE%', 1, 0)) as sign_up_safetynet_status_server_signature_validation_failure_uu,
              SUM(IF(safetynet_status LIKE '%android_attestation_verify_io_exception%', 1, 0)) as sign_up_safetynet_status_android_attestation_verify_io_exception,
              MAX(IF(safetynet_status LIKE '%android_attestation_verify_io_exception%', 1, 0)) as sign_up_safetynet_status_android_attestation_verify_io_exception_uu,
              SUM(IF(safetynet_status LIKE '%SERVER_BASIC_INTEGRITY_FAILURE%', 1, 0)) as sign_up_safetynet_status_server_basic_integrity_failure,
              MAX(IF(safetynet_status LIKE '%SERVER_BASIC_INTEGRITY_FAILURE%', 1, 0)) as sign_up_safetynet_status_server_basic_integrity_failure_uu,
              SUM(IF(safetynet_status LIKE '%android_attestation_internal_error%', 1, 0)) as sign_up_safetynet_status_android_attestation_internal_error,
              MAX(IF(safetynet_status LIKE '%android_attestation_internal_error%', 1, 0)) as sign_up_safetynet_status_android_attestation_internal_error_uu,
              SUM(IF(safetynet_status LIKE '%INTERNAL_CLIENT_ERROR%', 1, 0)) as sign_up_safetynet_status_internal_client_error,
              MAX(IF(safetynet_status LIKE '%INTERNAL_CLIENT_ERROR%', 1, 0)) as sign_up_safetynet_status_internal_client_error_uu,
              SUM(IF(safetynet_status LIKE '%INTERNAL_CLIENT_ERROR_NON_RETRIABLE%', 1, 0)) as sign_up_safetynet_status_internal_client_error_non_retriable,
              MAX(IF(safetynet_status LIKE '%INTERNAL_CLIENT_ERROR_NON_RETRIABLE%', 1, 0)) as sign_up_safetynet_status_internal_client_error_non_retriable_uu,
              SUM(IF(safetynet_status LIKE '%SERVER_CTS_PROFILE_MISMATCH%', 1, 0)) as sign_up_safetynet_status_server_cts_profile_mismatch,
              MAX(IF(safetynet_status LIKE '%SERVER_CTS_PROFILE_MISMATCH%', 1, 0)) as sign_up_safetynet_status_server_cts_profile_mismatch_uu,
              SUM(IF(safetynet_status LIKE '%android_attestation_verify_success%', 1, 0)) as sign_up_safetynet_status_android_attestation_verify_success,
              MAX(IF(safetynet_status LIKE '%android_attestation_verify_success%', 1, 0)) as sign_up_safetynet_status_android_attestation_verify_success_uu,
              SUM(IF(safetynet_status LIKE '%android_attestation_sig_mismatch%', 1, 0)) as sign_up_safetynet_status_android_attestation_sig_mismatch,
              MAX(IF(safetynet_status LIKE '%android_attestation_sig_mismatch%', 1, 0)) as sign_up_safetynet_status_android_attestation_sig_mismatch_uu,
              SUM(IF(safetynet_status LIKE '%android_attestation_cts_profile_not_match%', 1, 0)) as sign_up_safetynet_status_android_attestation_cts_profile_not_match,
              MAX(IF(safetynet_status LIKE '%android_attestation_cts_profile_not_match%', 1, 0)) as sign_up_safetynet_status_android_attestation_cts_profile_not_match_uu,
              SUM(IF(key_attestation_status = 'VENDOR_ATTESTATION_MISSING', 1, 0)) as sign_up_key_attestation_status_vendor_attestation_missing,
              MAX(IF(key_attestation_status = 'VENDOR_ATTESTATION_MISSING', 1, 0)) as sign_up_key_attestation_status_vendor_attestation_missing_uu,
              SUM(IF(key_attestation_status = 'ACCEPTABLE', 1, 0)) as sign_up_key_attestation_status_acceptable,
              MAX(IF(key_attestation_status = 'ACCEPTABLE', 1, 0)) as sign_up_key_attestation_status_acceptable_uu,
              SUM(IF(key_attestation_status = 'REMOTE_CLIENT_ERROR', 1, 0)) as sign_up_key_attestation_status_remote_client_error,
              MAX(IF(key_attestation_status = 'REMOTE_CLIENT_ERROR', 1, 0)) as sign_up_key_attestation_status_remote_client_error_uu,
              SUM(IF(key_attestation_status = 'UNACCEPTABLE_ROOT_CERTIFICATE', 1, 0)) as sign_up_key_attestation_status_unacceptable_root_certificate,
              MAX(IF(key_attestation_status = 'UNACCEPTABLE_ROOT_CERTIFICATE', 1, 0)) as sign_up_key_attestation_status_unacceptable_root_certificate_uu,
              SUM(IF(key_attestation_status = 'UNSUPPORTED_BY_CLIENT', 1, 0)) as sign_up_key_attestation_status_unsupported_by_client,
              MAX(IF(key_attestation_status = 'UNSUPPORTED_BY_CLIENT', 1, 0)) as sign_up_key_attestation_status_unsupported_by_client_uu,
              SUM(IF(key_attestation_status = 'INSECURE_REMOTE_DEVICE', 1, 0)) as sign_up_key_attestation_status_insecure_remote_device,
              MAX(IF(key_attestation_status = 'INSECURE_REMOTE_DEVICE', 1, 0)) as sign_up_key_attestation_status_insecure_remote_device_uu,
              SUM(IF(key_attestation_status = 'INVALID_CERTIFICATE_CHAIN', 1, 0)) as sign_up_key_attestation_status_invalid_certificate_chain,
              MAX(IF(key_attestation_status = 'INVALID_CERTIFICATE_CHAIN', 1, 0)) as sign_up_key_attestation_status_invalid_certificate_chain_uu,
              SUM(IF(key_attestation_status = 'UNACCEPTABLE_APPLICATION_ID', 1, 0)) as sign_up_key_attestation_status_unacceptable_application_id,
              MAX(IF(key_attestation_status = 'UNACCEPTABLE_APPLICATION_ID', 1, 0)) as sign_up_key_attestation_status_unacceptable_application_id_uu,
              SUM(IF(play_integrity_status = 'VENDOR_ATTESTATION_MISSING', 1, 0)) as sign_up_play_integrity_status_vendor_attestation_missing,
              MAX(IF(play_integrity_status = 'VENDOR_ATTESTATION_MISSING', 1, 0)) as sign_up_play_integrity_status_vendor_attestation_missing_uu,
              SUM(IF(play_integrity_status = 'SUCCESS_VALID_PAYLOAD', 1, 0)) as sign_up_play_integrity_status_success_valid_payload,
              MAX(IF(play_integrity_status = 'SUCCESS_VALID_PAYLOAD', 1, 0)) as sign_up_play_integrity_status_success_valid_payload_uu,
              SUM(IF(play_integrity_status = 'VENDOR_ATTESTATION_NON_ACTIONABLE_CLIENT_ERROR', 1, 0)) as sign_up_play_integrity_status_vendor_attestation_non_actionable_client_error,
              MAX(IF(play_integrity_status = 'VENDOR_ATTESTATION_NON_ACTIONABLE_CLIENT_ERROR', 1, 0)) as sign_up_play_integrity_status_vendor_attestation_non_actionable_client_error_uu,
              SUM(IF(play_integrity_status = 'UNACCEPTABLE_DEVICE_INTEGRITY_STATUS', 1, 0)) as sign_up_play_integrity_status_unacceptable_device_integrity_status,
              MAX(IF(play_integrity_status = 'UNACCEPTABLE_DEVICE_INTEGRITY_STATUS', 1, 0)) as sign_up_play_integrity_status_unacceptable_device_integrity_status_uu,
              SUM(IF(play_integrity_status = 'VENDOR_ATTESTATION_PLAY_STORE_MISSING', 1, 0)) as sign_up_play_integrity_status_vendor_attestation_play_store_missing,
              MAX(IF(play_integrity_status = 'VENDOR_ATTESTATION_PLAY_STORE_MISSING', 1, 0)) as sign_up_play_integrity_status_vendor_attestation_play_store_missing_uu,
              SUM(IF(play_integrity_status = 'VENDOR_ATTESTATION_RETRIABLE_CLIENT_ERROR', 1, 0)) as sign_up_play_integrity_status_vendor_attestation_retriable_client_error,
              MAX(IF(play_integrity_status = 'VENDOR_ATTESTATION_RETRIABLE_CLIENT_ERROR', 1, 0)) as sign_up_play_integrity_status_vendor_attestation_retriable_client_error_uu,
              SUM(IF(play_integrity_status = 'TOKEN_EXPIRED', 1, 0)) as sign_up_play_integrity_status_token_expired,
              MAX(IF(play_integrity_status = 'TOKEN_EXPIRED', 1, 0)) as sign_up_play_integrity_status_token_expired_uu,
              SUM(IF(play_integrity_status = 'DECRYPTION_FAILURE', 1, 0)) as sign_up_play_integrity_status_decryption_failure,
              MAX(IF(play_integrity_status = 'DECRYPTION_FAILURE', 1, 0)) as sign_up_play_integrity_status_decryption_failure_uu,
              SUM(IF(app_attest_status = 'VENDOR_ATTESTATION_MISSING', 1, 0)) as sign_up_app_attest_status_vendor_attestation_missing,
              MAX(IF(app_attest_status = 'VENDOR_ATTESTATION_MISSING', 1, 0)) as sign_up_app_attest_status_vendor_attestation_missing_uu,
              SUM(IF(app_attest_status = 'SUCCESS', 1, 0)) as sign_up_app_attest_status_success,
              MAX(IF(app_attest_status = 'SUCCESS', 1, 0)) as sign_up_app_attest_status_success_uu,
              SUM(IF(app_attest_status = 'CLIENT_ERROR_KEY_GENERATION_AND_ATTESTATION_TIMED_OUT', 1, 0)) as sign_up_app_attest_status_client_error_key_generation_and_attestation_timed_out,
              MAX(IF(app_attest_status = 'CLIENT_ERROR_KEY_GENERATION_AND_ATTESTATION_TIMED_OUT', 1, 0)) as sign_up_app_attest_status_client_error_key_generation_and_attestation_timed_out_uu,
              SUM(IF(app_attest_status = 'CLIENT_ERROR_UNSUPPORTED_IOS_13_OR_BELOW', 1, 0)) as sign_up_app_attest_status_client_error_unsupported_ios_13_or_below,
              MAX(IF(app_attest_status = 'CLIENT_ERROR_UNSUPPORTED_IOS_13_OR_BELOW', 1, 0)) as sign_up_app_attest_status_client_error_unsupported_ios_13_or_below_uu,
              SUM(IF(app_attest_status = 'CLIENT_ERROR_SERVER_UNAVAILABLE', 1, 0)) as sign_up_app_attest_status_client_error_server_unavailable,
              MAX(IF(app_attest_status = 'CLIENT_ERROR_SERVER_UNAVAILABLE', 1, 0)) as sign_up_app_attest_status_client_error_server_unavailable_uu,
              SUM(IF(app_attest_status = 'ASSERTION_SIGNATURE_INVALID', 1, 0)) as sign_up_app_attest_status_assertion_signature_invalid,
              MAX(IF(app_attest_status = 'ASSERTION_SIGNATURE_INVALID', 1, 0)) as sign_up_app_attest_status_assertion_signature_invalid_uu,
              SUM(IF(app_attest_status = 'CLIENT_ERROR_KEY_GENERATION_TIMED_OUT', 1, 0)) as sign_up_app_attest_status_client_error_key_generation_timed_out,
              MAX(IF(app_attest_status = 'CLIENT_ERROR_KEY_GENERATION_TIMED_OUT', 1, 0)) as sign_up_app_attest_status_client_error_key_generation_timed_out_uu,
              SUM(IF(app_attest_status = 'CLIENT_ERROR_INVALID_KEY', 1, 0)) as sign_up_app_attest_status_client_error_invalid_key,
              MAX(IF(app_attest_status = 'CLIENT_ERROR_INVALID_KEY', 1, 0)) as sign_up_app_attest_status_client_error_invalid_key_uu,
              SUM(IF(app_attest_status = 'CLIENT_ERROR_UNSUPPORTED_IOS_14_OR_ABOVE', 1, 0)) as sign_up_app_attest_status_client_error_unsupported_ios_14_or_above,
              MAX(IF(app_attest_status = 'CLIENT_ERROR_UNSUPPORTED_IOS_14_OR_ABOVE', 1, 0)) as sign_up_app_attest_status_client_error_unsupported_ios_14_or_above_uu,
              SUM(IF(app_attest_status = 'CLIENT_ERROR_UNKNOWN_SYSTEM_FAILURE', 1, 0)) as sign_up_app_attest_status_client_error_unknown_system_failure,
              MAX(IF(app_attest_status = 'CLIENT_ERROR_UNKNOWN_SYSTEM_FAILURE', 1, 0)) as sign_up_app_attest_status_client_error_unknown_system_failure_uu,
              SUM(IF(app_attest_status = 'CLIENT_ERROR_KEY_ATTESTATION_TIMED_OUT', 1, 0)) as sign_up_app_attest_status_client_error_key_attestation_timed_out,
              MAX(IF(app_attest_status = 'CLIENT_ERROR_KEY_ATTESTATION_TIMED_OUT', 1, 0)) as sign_up_app_attest_status_client_error_key_attestation_timed_out_uu,
              SUM(IF(app_attest_status = 'CLIENT_ERROR_INVALID_INPUT', 1, 0)) as sign_up_app_attest_status_client_error_invalid_input,
              MAX(IF(app_attest_status = 'CLIENT_ERROR_INVALID_INPUT', 1, 0)) as sign_up_app_attest_status_client_error_invalid_input_uu,
              SUM(IF(app_attest_status = 'ATTESTATION_AUTH_DATA_RP_ID_MISMATCH', 1, 0)) as sign_up_app_attest_status_attestation_auth_data_rp_id_mismatch,
              MAX(IF(app_attest_status = 'ATTESTATION_AUTH_DATA_RP_ID_MISMATCH', 1, 0)) as sign_up_app_attest_status_attestation_auth_data_rp_id_mismatch_uu,
              SUM(IF(app_attest_status = 'CLIENT_ERROR_ASSERTION_TIMED_OUT', 1, 0)) as sign_up_app_attest_status_client_error_assertion_timed_out,
              MAX(IF(app_attest_status = 'CLIENT_ERROR_ASSERTION_TIMED_OUT', 1, 0)) as sign_up_app_attest_status_client_error_assertion_timed_out_uu,
              SUM(IF(app_attest_status = 'ATTESTATION_CERT_CHAIN_INVALID', 1, 0)) as sign_up_app_attest_status_attestation_cert_chain_invalid,
              MAX(IF(app_attest_status = 'ATTESTATION_CERT_CHAIN_INVALID', 1, 0)) as sign_up_app_attest_status_attestation_cert_chain_invalid_uu,
              SUM(IF(app_attest_status = 'ATTESTATION_KEY_ID_MISMATCH', 1, 0)) as sign_up_app_attest_status_attestation_key_id_mismatch,
              MAX(IF(app_attest_status = 'ATTESTATION_KEY_ID_MISMATCH', 1, 0)) as sign_up_app_attest_status_attestation_key_id_mismatch_uu,
            FROM 
              {source_table}
            GROUP BY 1,2  
            """.format(unit_id=unit_id, source_table=source_table),

            metrics=[
                sign_up_result_success,
                sign_up_result_success_uu,
                sign_up_result_blocked_by_ads,
                sign_up_result_blocked_by_ads_uu,
                sign_up_result_unknown,
                sign_up_result_unknown_uu,
                sign_up_result_username_taken,
                sign_up_result_username_taken_uu,
                sign_up_result_android_request_safetynet,
                sign_up_result_android_request_safetynet_uu,
                sign_up_result_invalid_username,
                sign_up_result_invalid_username_uu,
                sign_up_result_unexpected_failure,
                sign_up_result_unexpected_failure_uu,
                sign_up_result_too_many_retries,
                sign_up_result_too_many_retries_uu,
                sign_up_result_invalid_birthdate,
                sign_up_result_invalid_birthdate_uu,
                sign_up_result_invalid_password,
                sign_up_result_invalid_password_uu,
                sign_up_result_weak_password,
                sign_up_result_weak_password_uu,
                sign_up_result_invalid_display_name,
                sign_up_result_invalid_display_name_uu,
                sign_up_result_app_version_upgrade,
                sign_up_result_app_version_upgrade_uu,
                sign_up_blocked_by_ads_defense_unset,
                sign_up_blocked_by_ads_defense_unset_uu,
                sign_up_blocked_by_ads_defense_ip_blocking,
                sign_up_blocked_by_ads_defense_ip_blocking_uu,
                sign_up_blocked_by_ads_defense_device_id_rate_limiting,
                sign_up_blocked_by_ads_defense_device_id_rate_limiting_uu,
                sign_up_blocked_by_ads_defense_attestation,
                sign_up_blocked_by_ads_defense_attestation_uu,
                sign_up_blocked_by_ads_defense_user_locked,
                sign_up_blocked_by_ads_defense_user_locked_uu,
                sign_up_blocked_by_ads_defense_odlv,
                sign_up_blocked_by_ads_defense_odlv_uu,
                sign_up_blocked_by_ads_defense_device_blocking,
                sign_up_blocked_by_ads_defense_device_blocking_uu,
                sign_up_blocked_by_ads_defense_high_device_user_association_blocking,
                sign_up_blocked_by_ads_defense_high_device_user_association_blocking_uu,
                sign_up_blocked_by_ads_defense_email_reputation_blocking,
                sign_up_blocked_by_ads_defense_email_reputation_blocking_uu,
                sign_up_blocked_by_ads_defense_device_check,
                sign_up_blocked_by_ads_defense_device_check_uu,
                sign_up_blocked_by_ads_defense_vendor_integrity_check,
                sign_up_blocked_by_ads_defense_vendor_integrity_check_uu,
                sign_up_blocked_by_ads_defense_vendor_integrity_retry,
                sign_up_blocked_by_ads_defense_vendor_integrity_retry_uu,
                sign_up_blocked_by_ads_defense_async_email_validation,
                sign_up_blocked_by_ads_defense_async_email_validation_uu,
                sign_up_blocked_by_ads_defense_android_mismatch_useragent,
                sign_up_blocked_by_ads_defense_android_mismatch_useragent_uu,
                sign_up_blocked_by_ads_defense_phone_iar_challenge_requested,
                sign_up_blocked_by_ads_defense_phone_iar_challenge_requested_uu,
                sign_up_blocked_by_ads_defense_client_integrity_nonce_rate_limiting,
                sign_up_blocked_by_ads_defense_client_integrity_nonce_rate_limiting_uu,
                sign_up_blocked_by_ads_defense_invalid_useragent,
                sign_up_blocked_by_ads_defense_invalid_useragent_uu,
                sign_up_blocked_by_ads_defense_invalid_input,
                sign_up_blocked_by_ads_defense_invalid_input_uu,
                sign_up_blocked_by_ads_defense_invalid_email_domain,
                sign_up_blocked_by_ads_defense_invalid_email_domain_uu,
                sign_up_blocked_by_ads_defense_web_bot_detection,
                sign_up_blocked_by_ads_defense_web_bot_detection_uu,
                sign_up_blocked_by_ads_defense_web_registration_app_login,
                sign_up_blocked_by_ads_defense_web_registration_app_login_uu,
                sign_up_safetynet_status_client_attestation_timeout,
                sign_up_safetynet_status_client_attestation_timeout_uu,
                sign_up_safetynet_status_android_attestation_missing,
                sign_up_safetynet_status_android_attestation_missing_uu,
                sign_up_safetynet_status_android_attestation_hardware_integrity_supported_but_missing,
                sign_up_safetynet_status_android_attestation_hardware_integrity_supported_but_missing_uu,
                sign_up_safetynet_status_server_hardware_integrity_supported_but_missing,
                sign_up_safetynet_status_server_hardware_integrity_supported_but_missing_uu,
                sign_up_safetynet_status_internal_safetynet_error,
                sign_up_safetynet_status_internal_safetynet_error_uu,
                sign_up_safetynet_status_vendor_attestation_missing,
                sign_up_safetynet_status_vendor_attestation_missing_uu,
                sign_up_safetynet_status_server_timestamp_invalid,
                sign_up_safetynet_status_server_timestamp_invalid_uu,
                sign_up_safetynet_status_android_attestation_expired,
                sign_up_safetynet_status_android_attestation_expired_uu,
                sign_up_safetynet_status_success,
                sign_up_safetynet_status_success_uu,
                sign_up_safetynet_status_server_apk_certificate_mismatch,
                sign_up_safetynet_status_server_apk_certificate_mismatch_uu,
                sign_up_safetynet_status_server_signature_validation_failure,
                sign_up_safetynet_status_server_signature_validation_failure_uu,
                sign_up_safetynet_status_android_attestation_verify_io_exception,
                sign_up_safetynet_status_android_attestation_verify_io_exception_uu,
                sign_up_safetynet_status_server_basic_integrity_failure,
                sign_up_safetynet_status_server_basic_integrity_failure_uu,
                sign_up_safetynet_status_android_attestation_internal_error,
                sign_up_safetynet_status_android_attestation_internal_error_uu,
                sign_up_safetynet_status_internal_client_error,
                sign_up_safetynet_status_internal_client_error_uu,
                sign_up_safetynet_status_internal_client_error_non_retriable,
                sign_up_safetynet_status_internal_client_error_non_retriable_uu,
                sign_up_safetynet_status_server_cts_profile_mismatch,
                sign_up_safetynet_status_server_cts_profile_mismatch_uu,
                sign_up_safetynet_status_android_attestation_verify_success,
                sign_up_safetynet_status_android_attestation_verify_success_uu,
                sign_up_safetynet_status_android_attestation_sig_mismatch,
                sign_up_safetynet_status_android_attestation_sig_mismatch_uu,
                sign_up_safetynet_status_android_attestation_cts_profile_not_match,
                sign_up_safetynet_status_android_attestation_cts_profile_not_match_uu,
                sign_up_key_attestation_status_vendor_attestation_missing,
                sign_up_key_attestation_status_vendor_attestation_missing_uu,
                sign_up_key_attestation_status_acceptable,
                sign_up_key_attestation_status_acceptable_uu,
                sign_up_key_attestation_status_remote_client_error,
                sign_up_key_attestation_status_remote_client_error_uu,
                sign_up_key_attestation_status_unacceptable_root_certificate,
                sign_up_key_attestation_status_unacceptable_root_certificate_uu,
                sign_up_key_attestation_status_unsupported_by_client,
                sign_up_key_attestation_status_unsupported_by_client_uu,
                sign_up_key_attestation_status_insecure_remote_device,
                sign_up_key_attestation_status_insecure_remote_device_uu,
                sign_up_key_attestation_status_invalid_certificate_chain,
                sign_up_key_attestation_status_invalid_certificate_chain_uu,
                sign_up_key_attestation_status_unacceptable_application_id,
                sign_up_key_attestation_status_unacceptable_application_id_uu,
                sign_up_play_integrity_status_vendor_attestation_missing,
                sign_up_play_integrity_status_vendor_attestation_missing_uu,
                sign_up_play_integrity_status_success_valid_payload,
                sign_up_play_integrity_status_success_valid_payload_uu,
                sign_up_play_integrity_status_vendor_attestation_non_actionable_client_error,
                sign_up_play_integrity_status_vendor_attestation_non_actionable_client_error_uu,
                sign_up_play_integrity_status_unacceptable_device_integrity_status,
                sign_up_play_integrity_status_unacceptable_device_integrity_status_uu,
                sign_up_play_integrity_status_vendor_attestation_play_store_missing,
                sign_up_play_integrity_status_vendor_attestation_play_store_missing_uu,
                sign_up_play_integrity_status_vendor_attestation_retriable_client_error,
                sign_up_play_integrity_status_vendor_attestation_retriable_client_error_uu,
                sign_up_play_integrity_status_token_expired,
                sign_up_play_integrity_status_token_expired_uu,
                sign_up_play_integrity_status_decryption_failure,
                sign_up_play_integrity_status_decryption_failure_uu,
                sign_up_app_attest_status_vendor_attestation_missing,
                sign_up_app_attest_status_vendor_attestation_missing_uu,
                sign_up_app_attest_status_success,
                sign_up_app_attest_status_success_uu,
                sign_up_app_attest_status_client_error_key_generation_and_attestation_timed_out,
                sign_up_app_attest_status_client_error_key_generation_and_attestation_timed_out_uu,
                sign_up_app_attest_status_client_error_unsupported_ios_13_or_below,
                sign_up_app_attest_status_client_error_unsupported_ios_13_or_below_uu,
                sign_up_app_attest_status_client_error_server_unavailable,
                sign_up_app_attest_status_client_error_server_unavailable_uu,
                sign_up_app_attest_status_assertion_signature_invalid,
                sign_up_app_attest_status_assertion_signature_invalid_uu,
                sign_up_app_attest_status_client_error_key_generation_timed_out,
                sign_up_app_attest_status_client_error_key_generation_timed_out_uu,
                sign_up_app_attest_status_client_error_invalid_key,
                sign_up_app_attest_status_client_error_invalid_key_uu,
                sign_up_app_attest_status_client_error_unsupported_ios_14_or_above,
                sign_up_app_attest_status_client_error_unsupported_ios_14_or_above_uu,
                sign_up_app_attest_status_client_error_unknown_system_failure,
                sign_up_app_attest_status_client_error_unknown_system_failure_uu,
                sign_up_app_attest_status_client_error_key_attestation_timed_out,
                sign_up_app_attest_status_client_error_key_attestation_timed_out_uu,
                sign_up_app_attest_status_client_error_invalid_input,
                sign_up_app_attest_status_client_error_invalid_input_uu,
                sign_up_app_attest_status_attestation_auth_data_rp_id_mismatch,
                sign_up_app_attest_status_attestation_auth_data_rp_id_mismatch_uu,
                sign_up_app_attest_status_client_error_assertion_timed_out,
                sign_up_app_attest_status_client_error_assertion_timed_out_uu,
                sign_up_app_attest_status_attestation_cert_chain_invalid,
                sign_up_app_attest_status_attestation_cert_chain_invalid_uu,
                sign_up_app_attest_status_attestation_key_id_mismatch,
                sign_up_app_attest_status_attestation_key_id_mismatch_uu,
            ],
            name="sign_up_server_rub",
            bq_dialect="standard",
            inner_join_with_mapping=False,
            join_key=join_key,
            unit_id=unit_id
        )

    else:
        mt = None
        logger.warning("Sign-Up Server Metrics are not available for this study type.")
    return mt


def sign_up_network_response(study_type, study_name, start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    unit_id = ""
    join_key = ""
    source_table = ""

    if study_type == "mobile_device_level_study":
        unit_id = "config_device_id"
        join_key = "config_device_id"
        source_table = """ (
            SELECT 
              event_date,
              usermap.config_device_id,
              usermap.client_id,
              event_name,
              network_response_result
            FROM 
             `sc-portal.abtest_device_usermap_cumulative.{study_name}__{end_date}` usermap
            INNER JOIN (
              SELECT 
                PARSE_DATE('%Y%m%d', _table_suffix) as event_date,
                client_id,
                event_name,
                CASE endpoint	 WHEN "/loq/register_v2" THEN	
                  CASE	
                    WHEN grpc_status_code in (200) THEN 'SUCCESS'	
                    WHEN grpc_status_code in (430) THEN 'SAFETYNET_REQUESTED'	
                    WHEN grpc_status_code in (401, 403, 429) THEN 'SPAM_AND_ABUSE'	
                    WHEN grpc_status_code in (-3, -1, 0) THEN 'NETWORK_PROBLEM'	
                    ELSE 'OTHER' 
                    END	
                  ELSE	
                    CASE 
                      WHEN grpc_status_code in (-1, 0) THEN	
                        CASE	
                          WHEN proto_status_code in (1) THEN 'SUCCESS'	
                          WHEN proto_status_code in (2) THEN 'SAFETYNET_REQUESTED'	
                          WHEN proto_status_code in (12, 20) THEN 'SPAM_AND_ABUSE'	
                        ELSE 'OTHER'	
                        END	
                    ELSE 'NETWORK_PROBLEM'	
                    END	
                END as network_response_result,	
              FROM 
                `sc-analytics.prod_analytics_user.daily_events_*`
              WHERE 
                _TABLE_SUFFIX BETWEEN "{start_date}" AND "{end_date}"
                AND event_name = 'REGISTRATION_USER_NETWORK_RESPONSE'	
            ) daily_events
            ON
              usermap.client_id=daily_events.client_id
        )
        """.format(study_name=study_name, start_date=start_date, end_date=end_date)

    sign_up_network_response_result_other = Metric('sign_up_network_response_result_other', 'sign_up_network_response_result_other', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_network_response_result_other_uu = Metric('sign_up_network_response_result_other_uu', 'sign_up_network_response_result_other_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_network_response_result_success = Metric('sign_up_network_response_result_success', 'sign_up_network_response_result_success', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_up_network_response_result_success_uu = Metric('sign_up_network_response_result_success_uu', 'sign_up_network_response_result_success_uu', dist='bin', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_up_network_response_result_network_problem = Metric('sign_up_network_response_result_network_problem', 'sign_up_network_response_result_network_problem', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_network_response_result_network_problem_uu = Metric('sign_up_network_response_result_network_problem_uu', 'sign_up_network_response_result_network_problem_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_network_response_result_safetynet_requested = Metric('sign_up_network_response_result_safetynet_requested', 'sign_up_network_response_result_safetynet_requested', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_network_response_result_safetynet_requested_uu = Metric('sign_up_network_response_result_safetynet_requested_uu', 'sign_up_network_response_result_safetynet_requested_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_network_response_result_spam_and_abuse = Metric('sign_up_network_response_result_spam_and_abuse', 'sign_up_network_response_result_spam_and_abuse', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_up_network_response_result_spam_and_abuse_uu = Metric('sign_up_network_response_result_spam_and_abuse_uu', 'sign_up_network_response_result_spam_and_abuse_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)

    if source_table != "":
        mt = MetricTable(
            sql=""" 
            SELECT
              TIMESTAMP(event_date) as ts,
              {unit_id},
              SUM(IF(network_response_result = 'OTHER', 1, 0)) as sign_up_network_response_result_other,
              MAX(IF(network_response_result = 'OTHER', 1, 0)) as sign_up_network_response_result_other_uu,
              SUM(IF(network_response_result = 'SUCCESS', 1, 0)) as sign_up_network_response_result_success,
              MAX(IF(network_response_result = 'SUCCESS', 1, 0)) as sign_up_network_response_result_success_uu,
              SUM(IF(network_response_result = 'NETWORK_PROBLEM', 1, 0)) as sign_up_network_response_result_network_problem,
              MAX(IF(network_response_result = 'NETWORK_PROBLEM', 1, 0)) as sign_up_network_response_result_network_problem_uu,
              SUM(IF(network_response_result = 'SAFETYNET_REQUESTED', 1, 0)) as sign_up_network_response_result_safetynet_requested,
              MAX(IF(network_response_result = 'SAFETYNET_REQUESTED', 1, 0)) as sign_up_network_response_result_safetynet_requested_uu,
              SUM(IF(network_response_result = 'SPAM_AND_ABUSE', 1, 0)) as sign_up_network_response_result_spam_and_abuse,
              MAX(IF(network_response_result = 'SPAM_AND_ABUSE', 1, 0)) as sign_up_network_response_result_spam_and_abuse_uu,
            FROM 
              {source_table}
            GROUP BY 1,2  
            """.format(unit_id=unit_id, source_table=source_table),

            metrics=[
                sign_up_network_response_result_other,
                sign_up_network_response_result_other_uu,
                sign_up_network_response_result_success,
                sign_up_network_response_result_success_uu,
                sign_up_network_response_result_network_problem,
                sign_up_network_response_result_network_problem_uu,
                sign_up_network_response_result_safetynet_requested,
                sign_up_network_response_result_safetynet_requested_uu,
                sign_up_network_response_result_spam_and_abuse,
                sign_up_network_response_result_spam_and_abuse_uu,
            ],
            name="sign_up_network_response",
            bq_dialect="standard",
            inner_join_with_mapping=False,
            join_key=join_key,
            unit_id=unit_id
        )

    else:
        mt=None
        logger.warning("Sign-Up Network Response Metrics are not available for this study type.")
    return mt


def sign_in_server(study_type, study_name, start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    unit_id = ""
    join_key = ""
    source_table = ""

    if study_type == "mobile_device_level_study":
        unit_id = "config_device_id"
        join_key = "config_device_id"
        source_table = """ (
            SELECT 
              usermap.config_device_id,
              usermap.client_id,
              user_login.* except (client_id)
            FROM 
             `sc-portal.abtest_device_usermap_cumulative.{study_name}__{end_date}` usermap
            INNER JOIN (
            SELECT
              DATE(TIMESTAMP_SUB( TIMESTAMP_MILLIS(timestamp), INTERVAL 8 hour)) as event_date,
              client_id,
              authentication_session_id,
              ghost_id AS ghost_user_id,
              authentication_device_descriptor,
              login_type,
              login_status,
              acs,
              rule,
              CASE 
                WHEN MAX(case when rule like "%DENY%" then 1 else 0 end ) OVER (partition by ghost_id, authentication_session_id) = 1 THEN "critical_ato_risk" 
                WHEN MAX(case when rule like "CHALLENGE%" then 1 else 0 end ) OVER (partition by  ghost_id, authentication_session_id) = 1 THEN "high_ato_risk" 
                WHEN MAX(case when rule like 'ALLOW%' OR rule = 'ADS-SEEN_DEVICE' then 1 else 0 end ) OVER (partition by ghost_id, authentication_session_id) = 1 THEN "low_ato_risk" 
                WHEN MAX(case when rule is null and ads_defense_name=0 and login_type not in (8, 9, 10) and login_status='success' then 1 else 0 end ) OVER (partition by ghost_id, authentication_session_id) = 1 THEN "low_ato_risk" 
                ELSE "unknown_ato_risk" 
              END AS ato_risk_level,
              CASE WHEN servlet_path in ('/scas/web/login/v2', '/web/login_odlv', '/accounts/validate_tfa', '/web/login', '/snapchat.janus.api.WebLoginService/WebLogin') THEN "web" ELSE "mobile" END AS platform,
              web_continue_param,
              CASE 
                WHEN MAX(case when web_continue_param like "%web.snapchat.com%" then 1 else 0 end) OVER (partition by authentication_session_id) = 1 THEN "desktop_web"
                ELSE "other"
              END AS web_domain
            FROM
              `sc-infosec-services.abuse_analytics_data_feed.user_login_*`
            WHERE 
              _TABLE_SUFFIX BETWEEN "{start_date}" AND FORMAT_DATE("%Y%m%d", DATE_ADD(PARSE_DATE("%Y%m%d", "{end_date}"), INTERVAL 1 DAY))
              AND DATE(TIMESTAMP_SUB( TIMESTAMP_MILLIS(timestamp), INTERVAL 8 hour)) BETWEEN PARSE_DATE("%Y%m%d", "{start_date}") AND PARSE_DATE("%Y%m%d", "{end_date}") 
              AND lower(servlet_path) NOT LIKE "%register%"
              /*This is because risk is not evaluated for state 3, magic code and 1TL for app*/
              AND ((login_type not in (8, 9, 10)) or (login_type is null))
              AND ((acs != 4) or (acs is null))
            ) user_login
            ON  
              usermap.client_id = user_login.client_id
        )
        """.format(study_name=study_name, unit_id=unit_id, start_date=start_date, end_date=end_date)
        
    elif study_type == "user_level_study":
        unit_id = "ghost_user_id"
        join_key = "ghost_user_id"
        source_table = """
        (
        SELECT
          DATE(TIMESTAMP_SUB( TIMESTAMP_MILLIS(timestamp), INTERVAL 8 hour)) as event_date,
          client_id,
          authentication_session_id,
          ghost_id AS ghost_user_id,
          authentication_device_descriptor,
          login_type,
          login_status,
          acs,
          rule,
          CASE 
            WHEN MAX(case when rule like "%DENY%" then 1 else 0 end ) OVER (partition by ghost_id, authentication_session_id) = 1 THEN "critical_ato_risk" 
            WHEN MAX(case when rule like "CHALLENGE%" then 1 else 0 end ) OVER (partition by  ghost_id, authentication_session_id) = 1 THEN "high_ato_risk" 
            WHEN MAX(case when rule like 'ALLOW%' OR rule = 'ADS-SEEN_DEVICE' then 1 else 0 end ) OVER (partition by ghost_id, authentication_session_id) = 1 THEN "low_ato_risk" 
            WHEN MAX(case when rule is null and ads_defense_name=0 and login_type not in (8, 9, 10) and login_status='success' then 1 else 0 end ) OVER (partition by ghost_id, authentication_session_id) = 1 THEN "low_ato_risk" 
            ELSE "unknown_ato_risk" 
          END AS ato_risk_level,
          CASE WHEN servlet_path in ('/scas/web/login/v2', '/web/login_odlv', '/accounts/validate_tfa', '/web/login', '/snapchat.janus.api.WebLoginService/WebLogin') THEN "web" ELSE "mobile" END AS platform,
          web_continue_param,
          CASE 
            WHEN MAX(case when web_continue_param like "%web.snapchat.com%" then 1 else 0 end) OVER (partition by authentication_session_id) = 1 THEN "desktop_web"
            ELSE "other"
          END AS web_domain
        FROM
          `sc-infosec-services.abuse_analytics_data_feed.user_login_*`
        WHERE 
          _TABLE_SUFFIX BETWEEN "{start_date}" AND FORMAT_DATE("%Y%m%d", DATE_ADD(PARSE_DATE("%Y%m%d", "{end_date}"), INTERVAL 1 DAY))
          AND DATE(TIMESTAMP_SUB( TIMESTAMP_MILLIS(timestamp), INTERVAL 8 hour)) BETWEEN PARSE_DATE("%Y%m%d", "{start_date}") AND PARSE_DATE("%Y%m%d", "{end_date}") 
          AND lower(servlet_path) NOT LIKE "%register%"
          /*This is because risk is not evaluated for state 3, magic code and 1TL for app*/
          AND ((login_type not in (8, 9, 10)) or (login_type is null))
          AND ((acs != 4) or (acs is null))
        )
        """.format(study_name=study_name, unit_id=unit_id, start_date=start_date, end_date=end_date)

    else:
        unit_id = "web_client_id"
        join_key = "web_client_id"
        source_table = """
        (
        SELECT
          DATE(TIMESTAMP_SUB( TIMESTAMP_MILLIS(timestamp), INTERVAL 8 hour)) as event_date,
          client_id as web_client_id,
          authentication_session_id,
          ghost_id AS ghost_user_id,
          authentication_device_descriptor,
          login_type,
          login_status,
          acs,
          rule,
          CASE 
            WHEN MAX(case when rule like "%DENY%" then 1 else 0 end ) OVER (partition by ghost_id, authentication_session_id) = 1 THEN "critical_ato_risk" 
            WHEN MAX(case when rule like "CHALLENGE%" then 1 else 0 end ) OVER (partition by  ghost_id, authentication_session_id) = 1 THEN "high_ato_risk" 
            WHEN MAX(case when rule like 'ALLOW%' OR rule = 'ADS-SEEN_DEVICE' then 1 else 0 end ) OVER (partition by ghost_id, authentication_session_id) = 1 THEN "low_ato_risk" 
            WHEN MAX(case when rule is null and ads_defense_name=0 and login_type not in (8, 9, 10) and login_status='success' then 1 else 0 end ) OVER (partition by ghost_id, authentication_session_id) = 1 THEN "low_ato_risk" 
            ELSE "unknown_ato_risk" 
          END AS ato_risk_level,
          CASE WHEN servlet_path in ('/scas/web/login/v2', '/web/login_odlv', '/accounts/validate_tfa', '/web/login', '/snapchat.janus.api.WebLoginService/WebLogin') THEN "web" ELSE "mobile" END AS platform,
          web_continue_param,
          CASE 
            WHEN MAX(case when web_continue_param like "%web.snapchat.com%" then 1 else 0 end) OVER (partition by authentication_session_id) = 1 THEN "desktop_web"
            ELSE "other"
          END AS web_domain
        FROM
          `sc-infosec-services.abuse_analytics_data_feed.user_login_*`
        WHERE 
          _TABLE_SUFFIX BETWEEN "{start_date}" AND FORMAT_DATE("%Y%m%d", DATE_ADD(PARSE_DATE("%Y%m%d", "{end_date}"), INTERVAL 1 DAY))
          AND DATE(TIMESTAMP_SUB( TIMESTAMP_MILLIS(timestamp), INTERVAL 8 hour)) BETWEEN PARSE_DATE("%Y%m%d", "{start_date}") AND PARSE_DATE("%Y%m%d", "{end_date}") 
          AND lower(servlet_path) NOT LIKE "%register%"
          /*This is because risk is not evaluated for state 3, magic code and 1TL for app*/
          AND ((login_type not in (8, 9, 10)) or (login_type is null))
          AND ((acs != 4) or (acs is null))
        )
        """.format(study_name=study_name, unit_id=unit_id, start_date=start_date, end_date=end_date)

    sign_in_success_uu = Metric('sign_in_success_uu', 'sign_in_success_uu', dist='bin', daily=True, cumulative=True)
    sign_in_session = Metric('sign_in_session', 'sign_in_session', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_in_success_session = Metric('sign_in_success_session', 'sign_in_success_session', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_in_success_rate_session = Metric('sign_in_success_rate_session', 'sign_in_success_rate_session', numerator_metric=sign_in_success_session, denominator_metric=sign_in_session, dist='ratio', desired_direction=POSITIVE)
    sign_in_session_low_ato_risk = Metric('sign_in_session_low_ato_risk', 'sign_in_session_low_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_in_success_session_low_ato_risk = Metric('sign_in_success_session_low_ato_risk', 'sign_in_success_session_low_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_in_success_rate_session_low_ato_risk = Metric('sign_in_success_rate_session_low_ato_risk', 'sign_in_success_rate_session_low_ato_risk', numerator_metric=sign_in_success_session_low_ato_risk, denominator_metric=sign_in_session_low_ato_risk, dist='ratio', desired_direction=POSITIVE)
    sign_in_session_high_ato_risk = Metric('sign_in_session_high_ato_risk', 'sign_in_session_high_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_in_success_session_high_ato_risk = Metric('sign_in_success_session_high_ato_risk', 'sign_in_success_session_high_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_in_success_rate_session_high_ato_risk = Metric('sign_in_success_rate_session_high_ato_risk', 'sign_in_success_rate_session_high_ato_risk', numerator_metric=sign_in_success_session_high_ato_risk, denominator_metric=sign_in_session_high_ato_risk, dist='ratio', desired_direction=NEGATIVE)
    sign_in_session_critical_ato_risk = Metric('sign_in_session_critical_ato_risk', 'sign_in_session_critical_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_in_success_session_critical_ato_risk = Metric('sign_in_success_session_critical_ato_risk', 'sign_in_success_session_critical_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_in_success_rate_session_critical_ato_risk = Metric('sign_in_success_rate_session_critical_ato_risk', 'sign_in_success_rate_session_critical_ato_risk', numerator_metric=sign_in_success_session_critical_ato_risk, denominator_metric=sign_in_session_critical_ato_risk, dist='ratio', desired_direction=NEGATIVE)
    sign_in_account = Metric('sign_in_account', 'sign_in_account', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_in_success_account = Metric('sign_in_success_account', 'sign_in_success_account', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_in_success_rate_account = Metric('sign_in_success_rate_account', 'sign_in_success_rate_account', numerator_metric=sign_in_success_account, denominator_metric=sign_in_account, dist='ratio', desired_direction=POSITIVE)
    sign_in_account_low_ato_risk = Metric('sign_in_account_low_ato_risk', 'sign_in_account_low_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_in_success_account_low_ato_risk = Metric('sign_in_success_account_low_ato_risk', 'sign_in_success_account_low_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_in_success_rate_account_low_ato_risk = Metric('sign_in_success_rate_account_low_ato_risk', 'sign_in_success_rate_account_low_ato_risk', numerator_metric=sign_in_success_account_low_ato_risk, denominator_metric=sign_in_account_low_ato_risk, dist='ratio', desired_direction=POSITIVE)
    sign_in_account_high_ato_risk = Metric('sign_in_account_high_ato_risk', 'sign_in_account_high_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_in_success_account_high_ato_risk = Metric('sign_in_success_account_high_ato_risk', 'sign_in_success_account_high_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_in_success_rate_account_high_ato_risk = Metric('sign_in_success_rate_account_high_ato_risk', 'sign_in_success_rate_account_high_ato_risk', numerator_metric=sign_in_success_account_high_ato_risk, denominator_metric=sign_in_account_high_ato_risk, dist='ratio', desired_direction=NEGATIVE)
    sign_in_account_critical_ato_risk = Metric('sign_in_account_critical_ato_risk', 'sign_in_account_critical_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_in_success_account_critical_ato_risk = Metric('sign_in_success_account_critical_ato_risk', 'sign_in_success_account_critical_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_in_success_rate_account_critical_ato_risk = Metric('sign_in_success_rate_account_critical_ato_risk', 'sign_in_success_rate_account_critical_ato_risk', numerator_metric=sign_in_success_account_critical_ato_risk, denominator_metric=sign_in_account_critical_ato_risk, dist='ratio', desired_direction=NEGATIVE)
    sign_in_device = Metric('sign_in_device', 'sign_in_device', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_in_success_device = Metric('sign_in_success_device', 'sign_in_success_device', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_in_success_rate_device = Metric('sign_in_success_rate_device', 'sign_in_success_rate_device', numerator_metric=sign_in_success_device, denominator_metric=sign_in_device, dist='ratio', desired_direction=POSITIVE)
    sign_in_device_low_ato_risk = Metric('sign_in_device_low_ato_risk', 'sign_in_device_low_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_in_success_device_low_ato_risk = Metric('sign_in_success_device_low_ato_risk', 'sign_in_success_device_low_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    sign_in_success_rate_device_low_ato_risk = Metric('sign_in_success_rate_device_low_ato_risk', 'sign_in_success_rate_device_low_ato_risk', numerator_metric=sign_in_success_device_low_ato_risk, denominator_metric=sign_in_device_low_ato_risk, dist='ratio', desired_direction=POSITIVE)
    sign_in_device_high_ato_risk = Metric('sign_in_device_high_ato_risk', 'sign_in_device_high_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_in_success_device_high_ato_risk = Metric('sign_in_success_device_high_ato_risk', 'sign_in_success_device_high_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_in_success_rate_device_high_ato_risk = Metric('sign_in_success_rate_device_high_ato_risk', 'sign_in_success_rate_device_high_ato_risk', numerator_metric=sign_in_success_device_high_ato_risk, denominator_metric=sign_in_device_high_ato_risk, dist='ratio', desired_direction=NEGATIVE)
    sign_in_device_critical_ato_risk = Metric('sign_in_device_critical_ato_risk', 'sign_in_device_critical_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_in_success_device_critical_ato_risk = Metric('sign_in_success_device_critical_ato_risk', 'sign_in_success_device_critical_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sign_in_success_rate_device_critical_ato_risk = Metric('sign_in_success_rate_device_critical_ato_risk', 'sign_in_success_rate_device_critical_ato_risk', numerator_metric=sign_in_success_device_critical_ato_risk, denominator_metric=sign_in_device_critical_ato_risk, dist='ratio', desired_direction=NEGATIVE)
    mobile_sign_in_session = Metric('mobile_sign_in_session', 'mobile_sign_in_session', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    mobile_sign_in_success_session = Metric('mobile_sign_in_success_session', 'mobile_sign_in_success_session', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    mobile_sign_in_success_rate_session = Metric('mobile_sign_in_success_rate_session', 'mobile_sign_in_success_rate_session', numerator_metric=mobile_sign_in_success_session, denominator_metric=mobile_sign_in_session, dist='ratio', desired_direction=POSITIVE)
    mobile_sign_in_session_low_ato_risk = Metric('mobile_sign_in_session_low_ato_risk', 'mobile_sign_in_session_low_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    mobile_sign_in_success_session_low_ato_risk = Metric('mobile_sign_in_success_session_low_ato_risk', 'mobile_sign_in_success_session_low_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    mobile_sign_in_success_rate_session_low_ato_risk = Metric('mobile_sign_in_success_rate_session_low_ato_risk', 'mobile_sign_in_success_rate_session_low_ato_risk', numerator_metric=mobile_sign_in_success_session_low_ato_risk, denominator_metric=mobile_sign_in_session_low_ato_risk, dist='ratio', desired_direction=POSITIVE)
    mobile_sign_in_session_high_ato_risk = Metric('mobile_sign_in_session_high_ato_risk', 'mobile_sign_in_session_high_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    mobile_sign_in_success_session_high_ato_risk = Metric('mobile_sign_in_success_session_high_ato_risk', 'mobile_sign_in_success_session_high_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    mobile_sign_in_success_rate_session_high_ato_risk = Metric('mobile_sign_in_success_rate_session_high_ato_risk', 'mobile_sign_in_success_rate_session_high_ato_risk', numerator_metric=mobile_sign_in_success_session_high_ato_risk, denominator_metric=mobile_sign_in_session_high_ato_risk, dist='ratio', desired_direction=NEGATIVE)
    mobile_sign_in_session_critical_ato_risk = Metric('mobile_sign_in_session_critical_ato_risk', 'mobile_sign_in_session_critical_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    mobile_sign_in_success_session_critical_ato_risk = Metric('mobile_sign_in_success_session_critical_ato_risk', 'mobile_sign_in_success_session_critical_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    mobile_sign_in_success_rate_session_critical_ato_risk = Metric('mobile_sign_in_success_rate_session_critical_ato_risk', 'mobile_sign_in_success_rate_session_critical_ato_risk', numerator_metric=mobile_sign_in_success_session_critical_ato_risk, denominator_metric=mobile_sign_in_session_critical_ato_risk, dist='ratio', desired_direction=NEGATIVE)
    mobile_sign_in_account = Metric('mobile_sign_in_account', 'mobile_sign_in_account', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    mobile_sign_in_success_account = Metric('mobile_sign_in_success_account', 'mobile_sign_in_success_account', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    mobile_sign_in_success_rate_account = Metric('mobile_sign_in_success_rate_account', 'mobile_sign_in_success_rate_account', numerator_metric=mobile_sign_in_success_account, denominator_metric=mobile_sign_in_account, dist='ratio', desired_direction=POSITIVE)
    mobile_sign_in_account_low_ato_risk = Metric('mobile_sign_in_account_low_ato_risk', 'mobile_sign_in_account_low_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    mobile_sign_in_success_account_low_ato_risk = Metric('mobile_sign_in_success_account_low_ato_risk', 'mobile_sign_in_success_account_low_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    mobile_sign_in_success_rate_account_low_ato_risk = Metric('mobile_sign_in_success_rate_account_low_ato_risk', 'mobile_sign_in_success_rate_account_low_ato_risk', numerator_metric=mobile_sign_in_success_account_low_ato_risk, denominator_metric=mobile_sign_in_account_low_ato_risk, dist='ratio', desired_direction=POSITIVE)
    mobile_sign_in_account_high_ato_risk = Metric('mobile_sign_in_account_high_ato_risk', 'mobile_sign_in_account_high_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    mobile_sign_in_success_account_high_ato_risk = Metric('mobile_sign_in_success_account_high_ato_risk', 'mobile_sign_in_success_account_high_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    mobile_sign_in_success_rate_account_high_ato_risk = Metric('mobile_sign_in_success_rate_account_high_ato_risk', 'mobile_sign_in_success_rate_account_high_ato_risk', numerator_metric=mobile_sign_in_success_account_high_ato_risk, denominator_metric=mobile_sign_in_account_high_ato_risk, dist='ratio', desired_direction=NEGATIVE)
    mobile_sign_in_account_critical_ato_risk = Metric('mobile_sign_in_account_critical_ato_risk', 'mobile_sign_in_account_critical_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    mobile_sign_in_success_account_critical_ato_risk = Metric('mobile_sign_in_success_account_critical_ato_risk', 'mobile_sign_in_success_account_critical_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    mobile_sign_in_success_rate_account_critical_ato_risk = Metric('mobile_sign_in_success_rate_account_critical_ato_risk', 'mobile_sign_in_success_rate_account_critical_ato_risk', numerator_metric=mobile_sign_in_success_account_critical_ato_risk, denominator_metric=mobile_sign_in_account_critical_ato_risk, dist='ratio', desired_direction=NEGATIVE)
    mobile_sign_in_device = Metric('mobile_sign_in_device', 'mobile_sign_in_device', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    mobile_sign_in_success_device = Metric('mobile_sign_in_success_device', 'mobile_sign_in_success_device', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    mobile_sign_in_success_rate_device = Metric('mobile_sign_in_success_rate_device', 'mobile_sign_in_success_rate_device', numerator_metric=mobile_sign_in_success_device, denominator_metric=mobile_sign_in_device, dist='ratio', desired_direction=POSITIVE)
    mobile_sign_in_device_low_ato_risk = Metric('mobile_sign_in_device_low_ato_risk', 'mobile_sign_in_device_low_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    mobile_sign_in_success_device_low_ato_risk = Metric('mobile_sign_in_success_device_low_ato_risk', 'mobile_sign_in_success_device_low_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    mobile_sign_in_success_rate_device_low_ato_risk = Metric('mobile_sign_in_success_rate_device_low_ato_risk', 'mobile_sign_in_success_rate_device_low_ato_risk', numerator_metric=mobile_sign_in_success_device_low_ato_risk, denominator_metric=mobile_sign_in_device_low_ato_risk, dist='ratio', desired_direction=POSITIVE)
    mobile_sign_in_device_high_ato_risk = Metric('mobile_sign_in_device_high_ato_risk', 'mobile_sign_in_device_high_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    mobile_sign_in_success_device_high_ato_risk = Metric('mobile_sign_in_success_device_high_ato_risk', 'mobile_sign_in_success_device_high_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    mobile_sign_in_success_rate_device_high_ato_risk = Metric('mobile_sign_in_success_rate_device_high_ato_risk', 'mobile_sign_in_success_rate_device_high_ato_risk', numerator_metric=mobile_sign_in_success_device_high_ato_risk, denominator_metric=mobile_sign_in_device_high_ato_risk, dist='ratio', desired_direction=NEGATIVE)
    mobile_sign_in_device_critical_ato_risk = Metric('mobile_sign_in_device_critical_ato_risk', 'mobile_sign_in_device_critical_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    mobile_sign_in_success_device_critical_ato_risk = Metric('mobile_sign_in_success_device_critical_ato_risk', 'mobile_sign_in_success_device_critical_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    mobile_sign_in_success_rate_device_critical_ato_risk = Metric('mobile_sign_in_success_rate_device_critical_ato_risk', 'mobile_sign_in_success_rate_device_critical_ato_risk', numerator_metric=mobile_sign_in_success_device_critical_ato_risk, denominator_metric=mobile_sign_in_device_critical_ato_risk, dist='ratio', desired_direction=NEGATIVE)
    web_sign_in_session = Metric('web_sign_in_session', 'web_sign_in_session', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    web_sign_in_success_session = Metric('web_sign_in_success_session', 'web_sign_in_success_session', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    web_sign_in_success_rate_session = Metric('web_sign_in_success_rate_session', 'web_sign_in_success_rate_session', numerator_metric=web_sign_in_success_session, denominator_metric=web_sign_in_session, dist='ratio', desired_direction=POSITIVE)
    web_sign_in_session_low_ato_risk = Metric('web_sign_in_session_low_ato_risk', 'web_sign_in_session_low_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    web_sign_in_success_session_low_ato_risk = Metric('web_sign_in_success_session_low_ato_risk', 'web_sign_in_success_session_low_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    web_sign_in_success_rate_session_low_ato_risk = Metric('web_sign_in_success_rate_session_low_ato_risk', 'web_sign_in_success_rate_session_low_ato_risk', numerator_metric=web_sign_in_success_session_low_ato_risk, denominator_metric=web_sign_in_session_low_ato_risk, dist='ratio', desired_direction=POSITIVE)
    web_sign_in_session_high_ato_risk = Metric('web_sign_in_session_high_ato_risk', 'web_sign_in_session_high_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    web_sign_in_success_session_high_ato_risk = Metric('web_sign_in_success_session_high_ato_risk', 'web_sign_in_success_session_high_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    web_sign_in_success_rate_session_high_ato_risk = Metric('web_sign_in_success_rate_session_high_ato_risk', 'web_sign_in_success_rate_session_high_ato_risk', numerator_metric=web_sign_in_success_session_high_ato_risk, denominator_metric=web_sign_in_session_high_ato_risk, dist='ratio', desired_direction=NEGATIVE)
    web_sign_in_session_critical_ato_risk = Metric('web_sign_in_session_critical_ato_risk', 'web_sign_in_session_critical_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    web_sign_in_success_session_critical_ato_risk = Metric('web_sign_in_success_session_critical_ato_risk', 'web_sign_in_success_session_critical_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    web_sign_in_success_rate_session_critical_ato_risk = Metric('web_sign_in_success_rate_session_critical_ato_risk', 'web_sign_in_success_rate_session_critical_ato_risk', numerator_metric=web_sign_in_success_session_critical_ato_risk, denominator_metric=web_sign_in_session_critical_ato_risk, dist='ratio', desired_direction=NEGATIVE)
    web_sign_in_account = Metric('web_sign_in_account', 'web_sign_in_account', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    web_sign_in_success_account = Metric('web_sign_in_success_account', 'web_sign_in_success_account', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    web_sign_in_success_rate_account = Metric('web_sign_in_success_rate_account', 'web_sign_in_success_rate_account', numerator_metric=web_sign_in_success_account, denominator_metric=web_sign_in_account, dist='ratio', desired_direction=POSITIVE)
    web_sign_in_account_low_ato_risk = Metric('web_sign_in_account_low_ato_risk', 'web_sign_in_account_low_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    web_sign_in_success_account_low_ato_risk = Metric('web_sign_in_success_account_low_ato_risk', 'web_sign_in_success_account_low_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    web_sign_in_success_rate_account_low_ato_risk = Metric('web_sign_in_success_rate_account_low_ato_risk', 'web_sign_in_success_rate_account_low_ato_risk', numerator_metric=web_sign_in_success_account_low_ato_risk, denominator_metric=web_sign_in_account_low_ato_risk, dist='ratio', desired_direction=POSITIVE)
    web_sign_in_account_high_ato_risk = Metric('web_sign_in_account_high_ato_risk', 'web_sign_in_account_high_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    web_sign_in_success_account_high_ato_risk = Metric('web_sign_in_success_account_high_ato_risk', 'web_sign_in_success_account_high_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    web_sign_in_success_rate_account_high_ato_risk = Metric('web_sign_in_success_rate_account_high_ato_risk', 'web_sign_in_success_rate_account_high_ato_risk', numerator_metric=web_sign_in_success_account_high_ato_risk, denominator_metric=web_sign_in_account_high_ato_risk, dist='ratio', desired_direction=NEGATIVE)
    web_sign_in_account_critical_ato_risk = Metric('web_sign_in_account_critical_ato_risk', 'web_sign_in_account_critical_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    web_sign_in_success_account_critical_ato_risk = Metric('web_sign_in_success_account_critical_ato_risk', 'web_sign_in_success_account_critical_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    web_sign_in_success_rate_account_critical_ato_risk = Metric('web_sign_in_success_rate_account_critical_ato_risk', 'web_sign_in_success_rate_account_critical_ato_risk', numerator_metric=web_sign_in_success_account_critical_ato_risk, denominator_metric=web_sign_in_account_critical_ato_risk, dist='ratio', desired_direction=NEGATIVE)
    web_sign_in_device = Metric('web_sign_in_device', 'web_sign_in_device', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    web_sign_in_success_device = Metric('web_sign_in_success_device', 'web_sign_in_success_device', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    web_sign_in_success_rate_device = Metric('web_sign_in_success_rate_device', 'web_sign_in_success_rate_device', numerator_metric=web_sign_in_success_device, denominator_metric=web_sign_in_device, dist='ratio', desired_direction=POSITIVE)
    web_sign_in_device_low_ato_risk = Metric('web_sign_in_device_low_ato_risk', 'web_sign_in_device_low_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    web_sign_in_success_device_low_ato_risk = Metric('web_sign_in_success_device_low_ato_risk', 'web_sign_in_success_device_low_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    web_sign_in_success_rate_device_low_ato_risk = Metric('web_sign_in_success_rate_device_low_ato_risk', 'web_sign_in_success_rate_device_low_ato_risk', numerator_metric=web_sign_in_success_device_low_ato_risk, denominator_metric=web_sign_in_device_low_ato_risk, dist='ratio', desired_direction=POSITIVE)
    web_sign_in_device_high_ato_risk = Metric('web_sign_in_device_high_ato_risk', 'web_sign_in_device_high_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    web_sign_in_success_device_high_ato_risk = Metric('web_sign_in_success_device_high_ato_risk', 'web_sign_in_success_device_high_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    web_sign_in_success_rate_device_high_ato_risk = Metric('web_sign_in_success_rate_device_high_ato_risk', 'web_sign_in_success_rate_device_high_ato_risk', numerator_metric=web_sign_in_success_device_high_ato_risk, denominator_metric=web_sign_in_device_high_ato_risk, dist='ratio', desired_direction=NEGATIVE)
    web_sign_in_device_critical_ato_risk = Metric('web_sign_in_device_critical_ato_risk', 'web_sign_in_device_critical_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    web_sign_in_success_device_critical_ato_risk = Metric('web_sign_in_success_device_critical_ato_risk', 'web_sign_in_success_device_critical_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    web_sign_in_success_rate_device_critical_ato_risk = Metric('web_sign_in_success_rate_device_critical_ato_risk', 'web_sign_in_success_rate_device_critical_ato_risk', numerator_metric=web_sign_in_success_device_critical_ato_risk, denominator_metric=web_sign_in_device_critical_ato_risk, dist='ratio', desired_direction=NEGATIVE)
    desktop_web_sign_in_session = Metric('desktop_web_sign_in_session', 'desktop_web_sign_in_session', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    desktop_web_sign_in_success_session = Metric('desktop_web_sign_in_success_session', 'desktop_web_sign_in_success_session', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    desktop_web_sign_in_success_rate_session = Metric('desktop_web_sign_in_success_rate_session', 'desktop_web_sign_in_success_rate_session', numerator_metric=desktop_web_sign_in_success_session, denominator_metric=desktop_web_sign_in_session, dist='ratio', desired_direction=POSITIVE)
    desktop_web_sign_in_session_low_ato_risk = Metric('desktop_web_sign_in_session_low_ato_risk', 'desktop_web_sign_in_session_low_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    desktop_web_sign_in_success_session_low_ato_risk = Metric('desktop_web_sign_in_success_session_low_ato_risk', 'desktop_web_sign_in_success_session_low_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    desktop_web_sign_in_success_rate_session_low_ato_risk = Metric('desktop_web_sign_in_success_rate_session_low_ato_risk', 'desktop_web_sign_in_success_rate_session_low_ato_risk', numerator_metric=desktop_web_sign_in_success_session_low_ato_risk, denominator_metric=desktop_web_sign_in_session_low_ato_risk, dist='ratio', desired_direction=POSITIVE)
    desktop_web_sign_in_session_high_ato_risk = Metric('desktop_web_sign_in_session_high_ato_risk', 'desktop_web_sign_in_session_high_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    desktop_web_sign_in_success_session_high_ato_risk = Metric('desktop_web_sign_in_success_session_high_ato_risk', 'desktop_web_sign_in_success_session_high_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    desktop_web_sign_in_success_rate_session_high_ato_risk = Metric('desktop_web_sign_in_success_rate_session_high_ato_risk', 'desktop_web_sign_in_success_rate_session_high_ato_risk', numerator_metric=desktop_web_sign_in_success_session_high_ato_risk, denominator_metric=desktop_web_sign_in_session_high_ato_risk, dist='ratio', desired_direction=NEGATIVE)
    desktop_web_sign_in_session_critical_ato_risk = Metric('desktop_web_sign_in_session_critical_ato_risk', 'desktop_web_sign_in_session_critical_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    desktop_web_sign_in_success_session_critical_ato_risk = Metric('desktop_web_sign_in_success_session_critical_ato_risk', 'desktop_web_sign_in_success_session_critical_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    desktop_web_sign_in_success_rate_session_critical_ato_risk = Metric('desktop_web_sign_in_success_rate_session_critical_ato_risk', 'desktop_web_sign_in_success_rate_session_critical_ato_risk', numerator_metric=desktop_web_sign_in_success_session_critical_ato_risk, denominator_metric=desktop_web_sign_in_session_critical_ato_risk, dist='ratio', desired_direction=NEGATIVE)
    desktop_web_sign_in_account = Metric('desktop_web_sign_in_account', 'desktop_web_sign_in_account', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    desktop_web_sign_in_success_account = Metric('desktop_web_sign_in_success_account', 'desktop_web_sign_in_success_account', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    desktop_web_sign_in_success_rate_account = Metric('desktop_web_sign_in_success_rate_account', 'desktop_web_sign_in_success_rate_account', numerator_metric=desktop_web_sign_in_success_account, denominator_metric=desktop_web_sign_in_account, dist='ratio', desired_direction=POSITIVE)
    desktop_web_sign_in_account_low_ato_risk = Metric('desktop_web_sign_in_account_low_ato_risk', 'desktop_web_sign_in_account_low_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    desktop_web_sign_in_success_account_low_ato_risk = Metric('desktop_web_sign_in_success_account_low_ato_risk', 'desktop_web_sign_in_success_account_low_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    desktop_web_sign_in_success_rate_account_low_ato_risk = Metric('desktop_web_sign_in_success_rate_account_low_ato_risk', 'desktop_web_sign_in_success_rate_account_low_ato_risk', numerator_metric=desktop_web_sign_in_success_account_low_ato_risk, denominator_metric=desktop_web_sign_in_account_low_ato_risk, dist='ratio', desired_direction=POSITIVE)
    desktop_web_sign_in_account_high_ato_risk = Metric('desktop_web_sign_in_account_high_ato_risk', 'desktop_web_sign_in_account_high_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    desktop_web_sign_in_success_account_high_ato_risk = Metric('desktop_web_sign_in_success_account_high_ato_risk', 'desktop_web_sign_in_success_account_high_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    desktop_web_sign_in_success_rate_account_high_ato_risk = Metric('desktop_web_sign_in_success_rate_account_high_ato_risk', 'desktop_web_sign_in_success_rate_account_high_ato_risk', numerator_metric=desktop_web_sign_in_success_account_high_ato_risk, denominator_metric=desktop_web_sign_in_account_high_ato_risk, dist='ratio', desired_direction=NEGATIVE)
    desktop_web_sign_in_account_critical_ato_risk = Metric('desktop_web_sign_in_account_critical_ato_risk', 'desktop_web_sign_in_account_critical_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    desktop_web_sign_in_success_account_critical_ato_risk = Metric('desktop_web_sign_in_success_account_critical_ato_risk', 'desktop_web_sign_in_success_account_critical_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    desktop_web_sign_in_success_rate_account_critical_ato_risk = Metric('desktop_web_sign_in_success_rate_account_critical_ato_risk', 'desktop_web_sign_in_success_rate_account_critical_ato_risk', numerator_metric=desktop_web_sign_in_success_account_critical_ato_risk, denominator_metric=desktop_web_sign_in_account_critical_ato_risk, dist='ratio', desired_direction=NEGATIVE)
    desktop_web_sign_in_device = Metric('desktop_web_sign_in_device', 'desktop_web_sign_in_device', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    desktop_web_sign_in_success_device = Metric('desktop_web_sign_in_success_device', 'desktop_web_sign_in_success_device', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    desktop_web_sign_in_success_rate_device = Metric('desktop_web_sign_in_success_rate_device', 'desktop_web_sign_in_success_rate_device', numerator_metric=desktop_web_sign_in_success_device, denominator_metric=desktop_web_sign_in_device, dist='ratio', desired_direction=POSITIVE)
    desktop_web_sign_in_device_low_ato_risk = Metric('desktop_web_sign_in_device_low_ato_risk', 'desktop_web_sign_in_device_low_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    desktop_web_sign_in_success_device_low_ato_risk = Metric('desktop_web_sign_in_success_device_low_ato_risk', 'desktop_web_sign_in_success_device_low_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
    desktop_web_sign_in_success_rate_device_low_ato_risk = Metric('desktop_web_sign_in_success_rate_device_low_ato_risk', 'desktop_web_sign_in_success_rate_device_low_ato_risk', numerator_metric=desktop_web_sign_in_success_device_low_ato_risk, denominator_metric=desktop_web_sign_in_device_low_ato_risk, dist='ratio', desired_direction=POSITIVE)
    desktop_web_sign_in_device_high_ato_risk = Metric('desktop_web_sign_in_device_high_ato_risk', 'desktop_web_sign_in_device_high_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    desktop_web_sign_in_success_device_high_ato_risk = Metric('desktop_web_sign_in_success_device_high_ato_risk', 'desktop_web_sign_in_success_device_high_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    desktop_web_sign_in_success_rate_device_high_ato_risk = Metric('desktop_web_sign_in_success_rate_device_high_ato_risk', 'desktop_web_sign_in_success_rate_device_high_ato_risk', numerator_metric=desktop_web_sign_in_success_device_high_ato_risk, denominator_metric=desktop_web_sign_in_device_high_ato_risk, dist='ratio', desired_direction=NEGATIVE)
    desktop_web_sign_in_device_critical_ato_risk = Metric('desktop_web_sign_in_device_critical_ato_risk', 'desktop_web_sign_in_device_critical_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    desktop_web_sign_in_success_device_critical_ato_risk = Metric('desktop_web_sign_in_success_device_critical_ato_risk', 'desktop_web_sign_in_success_device_critical_ato_risk', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    desktop_web_sign_in_success_rate_device_critical_ato_risk = Metric('desktop_web_sign_in_success_rate_device_critical_ato_risk', 'desktop_web_sign_in_success_rate_device_critical_ato_risk', numerator_metric=desktop_web_sign_in_success_device_critical_ato_risk, denominator_metric=desktop_web_sign_in_device_critical_ato_risk, dist='ratio', desired_direction=NEGATIVE)


    mt = MetricTable(
        sql="""
        SELECT
          TIMESTAMP(event_date) as ts,
          {unit_id},
          MAX(IF(login_status="success" AND ghost_user_id IS NOT NULL, 1, 0)) sign_in_success_uu,
          COUNT(DISTINCT authentication_session_id) AS sign_in_session,
          COUNT(DISTINCT CASE WHEN login_status='success' THEN authentication_session_id END) AS sign_in_success_session,
          COUNT(DISTINCT CASE WHEN ato_risk_level='low_ato_risk' THEN authentication_session_id END) AS sign_in_session_low_ato_risk,
          COUNT(DISTINCT CASE WHEN ato_risk_level='low_ato_risk' AND login_status='success' THEN authentication_session_id END) AS sign_in_success_session_low_ato_risk,
          COUNT(DISTINCT CASE WHEN ato_risk_level='high_ato_risk' THEN authentication_session_id END) AS sign_in_session_high_ato_risk,
          COUNT(DISTINCT CASE WHEN ato_risk_level='high_ato_risk' AND login_status='success' THEN authentication_session_id END) AS sign_in_success_session_high_ato_risk,
          COUNT(DISTINCT CASE WHEN ato_risk_level='critical_ato_risk' THEN authentication_session_id END) AS sign_in_session_critical_ato_risk,
          COUNT(DISTINCT CASE WHEN ato_risk_level='critical_ato_risk' AND login_status='success' THEN authentication_session_id END) AS sign_in_success_session_critical_ato_risk,
          COUNT(DISTINCT ghost_user_id) AS sign_in_account,
          COUNT(DISTINCT CASE WHEN login_status='success' THEN ghost_user_id END) AS sign_in_success_account,
          COUNT(DISTINCT CASE WHEN ato_risk_level='low_ato_risk' THEN ghost_user_id END) AS sign_in_account_low_ato_risk,
          COUNT(DISTINCT CASE WHEN ato_risk_level='low_ato_risk' AND login_status='success' THEN ghost_user_id END) AS sign_in_success_account_low_ato_risk,
          COUNT(DISTINCT CASE WHEN ato_risk_level='high_ato_risk' THEN ghost_user_id END) AS sign_in_account_high_ato_risk,
          COUNT(DISTINCT CASE WHEN ato_risk_level='high_ato_risk' AND login_status='success' THEN ghost_user_id END) AS sign_in_success_account_high_ato_risk,
          COUNT(DISTINCT CASE WHEN ato_risk_level='critical_ato_risk' THEN ghost_user_id END) AS sign_in_account_critical_ato_risk,
          COUNT(DISTINCT CASE WHEN ato_risk_level='critical_ato_risk' AND login_status='success' THEN ghost_user_id END) AS sign_in_success_account_critical_ato_risk,
          COUNT(DISTINCT authentication_device_descriptor) AS sign_in_device,
          COUNT(DISTINCT CASE WHEN login_status='success' THEN authentication_device_descriptor END) AS sign_in_success_device,
          COUNT(DISTINCT CASE WHEN ato_risk_level='low_ato_risk' THEN authentication_device_descriptor END) AS sign_in_device_low_ato_risk,
          COUNT(DISTINCT CASE WHEN ato_risk_level='low_ato_risk' AND login_status='success' THEN authentication_device_descriptor END) AS sign_in_success_device_low_ato_risk,
          COUNT(DISTINCT CASE WHEN ato_risk_level='high_ato_risk' THEN authentication_device_descriptor END) AS sign_in_device_high_ato_risk,
          COUNT(DISTINCT CASE WHEN ato_risk_level='high_ato_risk' AND login_status='success' THEN authentication_device_descriptor END) AS sign_in_success_device_high_ato_risk,
          COUNT(DISTINCT CASE WHEN ato_risk_level='critical_ato_risk' THEN authentication_device_descriptor END) AS sign_in_device_critical_ato_risk,
          COUNT(DISTINCT CASE WHEN ato_risk_level='critical_ato_risk' AND login_status='success' THEN authentication_device_descriptor END) AS sign_in_success_device_critical_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='mobile' THEN authentication_session_id END) AS mobile_sign_in_session,
          COUNT(DISTINCT CASE WHEN platform='mobile' AND login_status='success' THEN authentication_session_id END) AS mobile_sign_in_success_session,
          COUNT(DISTINCT CASE WHEN platform='mobile' AND ato_risk_level='low_ato_risk' THEN authentication_session_id END) AS mobile_sign_in_session_low_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='mobile' AND ato_risk_level='low_ato_risk' AND login_status='success' THEN authentication_session_id END) AS mobile_sign_in_success_session_low_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='mobile' AND ato_risk_level='high_ato_risk' THEN authentication_session_id END) AS mobile_sign_in_session_high_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='mobile' AND ato_risk_level='high_ato_risk' AND login_status='success' THEN authentication_session_id END) AS mobile_sign_in_success_session_high_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='mobile' AND ato_risk_level='critical_ato_risk' THEN authentication_session_id END) AS mobile_sign_in_session_critical_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='mobile' AND ato_risk_level='critical_ato_risk' AND login_status='success' THEN authentication_session_id END) AS mobile_sign_in_success_session_critical_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='mobile' THEN ghost_user_id END) AS mobile_sign_in_account,
          COUNT(DISTINCT CASE WHEN platform='mobile' AND login_status='success' THEN ghost_user_id END) AS mobile_sign_in_success_account,
          COUNT(DISTINCT CASE WHEN platform='mobile' AND ato_risk_level='low_ato_risk' THEN ghost_user_id END) AS mobile_sign_in_account_low_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='mobile' AND ato_risk_level='low_ato_risk' AND login_status='success' THEN ghost_user_id END) AS mobile_sign_in_success_account_low_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='mobile' AND ato_risk_level='high_ato_risk' THEN ghost_user_id END) AS mobile_sign_in_account_high_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='mobile' AND ato_risk_level='high_ato_risk' AND login_status='success' THEN ghost_user_id END) AS mobile_sign_in_success_account_high_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='mobile' AND ato_risk_level='critical_ato_risk' THEN ghost_user_id END) AS mobile_sign_in_account_critical_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='mobile' AND ato_risk_level='critical_ato_risk' AND login_status='success' THEN ghost_user_id END) AS mobile_sign_in_success_account_critical_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='mobile' THEN authentication_device_descriptor END) AS mobile_sign_in_device,
          COUNT(DISTINCT CASE WHEN platform='mobile' AND login_status='success' THEN authentication_device_descriptor END) AS mobile_sign_in_success_device,
          COUNT(DISTINCT CASE WHEN platform='mobile' AND ato_risk_level='low_ato_risk' THEN authentication_device_descriptor END) AS mobile_sign_in_device_low_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='mobile' AND ato_risk_level='low_ato_risk' AND login_status='success' THEN authentication_device_descriptor END) AS mobile_sign_in_success_device_low_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='mobile' AND ato_risk_level='high_ato_risk' THEN authentication_device_descriptor END) AS mobile_sign_in_device_high_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='mobile' AND ato_risk_level='high_ato_risk' AND login_status='success' THEN authentication_device_descriptor END) AS mobile_sign_in_success_device_high_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='mobile' AND ato_risk_level='critical_ato_risk' THEN authentication_device_descriptor END) AS mobile_sign_in_device_critical_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='mobile' AND ato_risk_level='critical_ato_risk' AND login_status='success' THEN authentication_device_descriptor END) AS mobile_sign_in_success_device_critical_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' THEN authentication_session_id END) AS web_sign_in_session,
          COUNT(DISTINCT CASE WHEN platform='web' AND login_status='success' THEN authentication_session_id END) AS web_sign_in_success_session,
          COUNT(DISTINCT CASE WHEN platform='web' AND ato_risk_level='low_ato_risk' THEN authentication_session_id END) AS web_sign_in_session_low_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND ato_risk_level='low_ato_risk' AND login_status='success' THEN authentication_session_id END) AS web_sign_in_success_session_low_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND ato_risk_level='high_ato_risk' THEN authentication_session_id END) AS web_sign_in_session_high_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND ato_risk_level='high_ato_risk' AND login_status='success' THEN authentication_session_id END) AS web_sign_in_success_session_high_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND ato_risk_level='critical_ato_risk' THEN authentication_session_id END) AS web_sign_in_session_critical_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND ato_risk_level='critical_ato_risk' AND login_status='success' THEN authentication_session_id END) AS web_sign_in_success_session_critical_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' THEN ghost_user_id END) AS web_sign_in_account,
          COUNT(DISTINCT CASE WHEN platform='web' AND login_status='success' THEN ghost_user_id END) AS web_sign_in_success_account,
          COUNT(DISTINCT CASE WHEN platform='web' AND ato_risk_level='low_ato_risk' THEN ghost_user_id END) AS web_sign_in_account_low_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND ato_risk_level='low_ato_risk' AND login_status='success' THEN ghost_user_id END) AS web_sign_in_success_account_low_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND ato_risk_level='high_ato_risk' THEN ghost_user_id END) AS web_sign_in_account_high_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND ato_risk_level='high_ato_risk' AND login_status='success' THEN ghost_user_id END) AS web_sign_in_success_account_high_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND ato_risk_level='critical_ato_risk' THEN ghost_user_id END) AS web_sign_in_account_critical_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND ato_risk_level='critical_ato_risk' AND login_status='success' THEN ghost_user_id END) AS web_sign_in_success_account_critical_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' THEN authentication_device_descriptor END) AS web_sign_in_device,
          COUNT(DISTINCT CASE WHEN platform='web' AND login_status='success' THEN authentication_device_descriptor END) AS web_sign_in_success_device,
          COUNT(DISTINCT CASE WHEN platform='web' AND ato_risk_level='low_ato_risk' THEN authentication_device_descriptor END) AS web_sign_in_device_low_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND ato_risk_level='low_ato_risk' AND login_status='success' THEN authentication_device_descriptor END) AS web_sign_in_success_device_low_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND ato_risk_level='high_ato_risk' THEN authentication_device_descriptor END) AS web_sign_in_device_high_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND ato_risk_level='high_ato_risk' AND login_status='success' THEN authentication_device_descriptor END) AS web_sign_in_success_device_high_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND ato_risk_level='critical_ato_risk' THEN authentication_device_descriptor END) AS web_sign_in_device_critical_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND ato_risk_level='critical_ato_risk' AND login_status='success' THEN authentication_device_descriptor END) AS web_sign_in_success_device_critical_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND web_domain='desktop_web' THEN authentication_session_id END) AS desktop_web_sign_in_session,
          COUNT(DISTINCT CASE WHEN platform='web' AND web_domain='desktop_web' AND login_status='success' THEN authentication_session_id END) AS desktop_web_sign_in_success_session,
          COUNT(DISTINCT CASE WHEN platform='web' AND web_domain='desktop_web' AND ato_risk_level='low_ato_risk' THEN authentication_session_id END) AS desktop_web_sign_in_session_low_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND web_domain='desktop_web' AND ato_risk_level='low_ato_risk' AND login_status='success' THEN authentication_session_id END) AS desktop_web_sign_in_success_session_low_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND web_domain='desktop_web' AND ato_risk_level='high_ato_risk' THEN authentication_session_id END) AS desktop_web_sign_in_session_high_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND web_domain='desktop_web' AND ato_risk_level='high_ato_risk' AND login_status='success' THEN authentication_session_id END) AS desktop_web_sign_in_success_session_high_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND web_domain='desktop_web' AND ato_risk_level='critical_ato_risk' THEN authentication_session_id END) AS desktop_web_sign_in_session_critical_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND web_domain='desktop_web' AND ato_risk_level='critical_ato_risk' AND login_status='success' THEN authentication_session_id END) AS desktop_web_sign_in_success_session_critical_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND web_domain='desktop_web' THEN ghost_user_id END) AS desktop_web_sign_in_account,
          COUNT(DISTINCT CASE WHEN platform='web' AND web_domain='desktop_web' AND login_status='success' THEN ghost_user_id END) AS desktop_web_sign_in_success_account,
          COUNT(DISTINCT CASE WHEN platform='web' AND web_domain='desktop_web' AND ato_risk_level='low_ato_risk' THEN ghost_user_id END) AS desktop_web_sign_in_account_low_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND web_domain='desktop_web' AND ato_risk_level='low_ato_risk' AND login_status='success' THEN ghost_user_id END) AS desktop_web_sign_in_success_account_low_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND web_domain='desktop_web' AND ato_risk_level='high_ato_risk' THEN ghost_user_id END) AS desktop_web_sign_in_account_high_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND web_domain='desktop_web' AND ato_risk_level='high_ato_risk' AND login_status='success' THEN ghost_user_id END) AS desktop_web_sign_in_success_account_high_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND web_domain='desktop_web' AND ato_risk_level='critical_ato_risk' THEN ghost_user_id END) AS desktop_web_sign_in_account_critical_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND web_domain='desktop_web' AND ato_risk_level='critical_ato_risk' AND login_status='success' THEN ghost_user_id END) AS desktop_web_sign_in_success_account_critical_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND web_domain='desktop_web' THEN authentication_device_descriptor END) AS desktop_web_sign_in_device,
          COUNT(DISTINCT CASE WHEN platform='web' AND web_domain='desktop_web' AND login_status='success' THEN authentication_device_descriptor END) AS desktop_web_sign_in_success_device,
          COUNT(DISTINCT CASE WHEN platform='web' AND web_domain='desktop_web' AND ato_risk_level='low_ato_risk' THEN authentication_device_descriptor END) AS desktop_web_sign_in_device_low_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND web_domain='desktop_web' AND ato_risk_level='low_ato_risk' AND login_status='success' THEN authentication_device_descriptor END) AS desktop_web_sign_in_success_device_low_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND web_domain='desktop_web' AND ato_risk_level='high_ato_risk' THEN authentication_device_descriptor END) AS desktop_web_sign_in_device_high_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND web_domain='desktop_web' AND ato_risk_level='high_ato_risk' AND login_status='success' THEN authentication_device_descriptor END) AS desktop_web_sign_in_success_device_high_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND web_domain='desktop_web' AND ato_risk_level='critical_ato_risk' THEN authentication_device_descriptor END) AS desktop_web_sign_in_device_critical_ato_risk,
          COUNT(DISTINCT CASE WHEN platform='web' AND web_domain='desktop_web' AND ato_risk_level='critical_ato_risk' AND login_status='success' THEN authentication_device_descriptor END) AS desktop_web_sign_in_success_device_critical_ato_risk,
        FROM
          {source_table} 
        GROUP BY 1,2
        """.format(unit_id=unit_id, source_table=source_table),

        metrics = [
            sign_in_success_uu,
            sign_in_success_session,
            sign_in_session,
            sign_in_success_rate_session,
            sign_in_success_session_low_ato_risk,
            sign_in_session_low_ato_risk,
            sign_in_success_rate_session_low_ato_risk,
            sign_in_success_session_high_ato_risk,
            sign_in_session_high_ato_risk,
            sign_in_success_rate_session_high_ato_risk,
            sign_in_success_session_critical_ato_risk,
            sign_in_session_critical_ato_risk,
            sign_in_success_rate_session_critical_ato_risk,
            sign_in_success_account,
            sign_in_account,
            sign_in_success_rate_account,
            sign_in_success_account_low_ato_risk,
            sign_in_account_low_ato_risk,
            sign_in_success_rate_account_low_ato_risk,
            sign_in_success_account_high_ato_risk,
            sign_in_account_high_ato_risk,
            sign_in_success_rate_account_high_ato_risk,
            sign_in_success_account_critical_ato_risk,
            sign_in_account_critical_ato_risk,
            sign_in_success_rate_account_critical_ato_risk,
            sign_in_success_device,
            sign_in_device,
            sign_in_success_rate_device,
            sign_in_success_device_low_ato_risk,
            sign_in_device_low_ato_risk,
            sign_in_success_rate_device_low_ato_risk,
            sign_in_success_device_high_ato_risk,
            sign_in_device_high_ato_risk,
            sign_in_success_rate_device_high_ato_risk,
            sign_in_success_device_critical_ato_risk,
            sign_in_device_critical_ato_risk,
            sign_in_success_rate_device_critical_ato_risk,
            mobile_sign_in_success_session,
            mobile_sign_in_session,
            mobile_sign_in_success_rate_session,
            mobile_sign_in_success_session_low_ato_risk,
            mobile_sign_in_session_low_ato_risk,
            mobile_sign_in_success_rate_session_low_ato_risk,
            mobile_sign_in_success_session_high_ato_risk,
            mobile_sign_in_session_high_ato_risk,
            mobile_sign_in_success_rate_session_high_ato_risk,
            mobile_sign_in_success_session_critical_ato_risk,
            mobile_sign_in_session_critical_ato_risk,
            mobile_sign_in_success_rate_session_critical_ato_risk,
            mobile_sign_in_success_account,
            mobile_sign_in_account,
            mobile_sign_in_success_rate_account,
            mobile_sign_in_success_account_low_ato_risk,
            mobile_sign_in_account_low_ato_risk,
            mobile_sign_in_success_rate_account_low_ato_risk,
            mobile_sign_in_success_account_high_ato_risk,
            mobile_sign_in_account_high_ato_risk,
            mobile_sign_in_success_rate_account_high_ato_risk,
            mobile_sign_in_success_account_critical_ato_risk,
            mobile_sign_in_account_critical_ato_risk,
            mobile_sign_in_success_rate_account_critical_ato_risk,
            mobile_sign_in_success_device,
            mobile_sign_in_device,
            mobile_sign_in_success_rate_device,
            mobile_sign_in_success_device_low_ato_risk,
            mobile_sign_in_device_low_ato_risk,
            mobile_sign_in_success_rate_device_low_ato_risk,
            mobile_sign_in_success_device_high_ato_risk,
            mobile_sign_in_device_high_ato_risk,
            mobile_sign_in_success_rate_device_high_ato_risk,
            mobile_sign_in_success_device_critical_ato_risk,
            mobile_sign_in_device_critical_ato_risk,
            mobile_sign_in_success_rate_device_critical_ato_risk,
            web_sign_in_success_session,
            web_sign_in_session,
            web_sign_in_success_rate_session,
            web_sign_in_success_session_low_ato_risk,
            web_sign_in_session_low_ato_risk,
            web_sign_in_success_rate_session_low_ato_risk,
            web_sign_in_success_session_high_ato_risk,
            web_sign_in_session_high_ato_risk,
            web_sign_in_success_rate_session_high_ato_risk,
            web_sign_in_success_session_critical_ato_risk,
            web_sign_in_session_critical_ato_risk,
            web_sign_in_success_rate_session_critical_ato_risk,
            web_sign_in_success_account,
            web_sign_in_account,
            web_sign_in_success_rate_account,
            web_sign_in_success_account_low_ato_risk,
            web_sign_in_account_low_ato_risk,
            web_sign_in_success_rate_account_low_ato_risk,
            web_sign_in_success_account_high_ato_risk,
            web_sign_in_account_high_ato_risk,
            web_sign_in_success_rate_account_high_ato_risk,
            web_sign_in_success_account_critical_ato_risk,
            web_sign_in_account_critical_ato_risk,
            web_sign_in_success_rate_account_critical_ato_risk,
            web_sign_in_success_device,
            web_sign_in_device,
            web_sign_in_success_rate_device,
            web_sign_in_success_device_low_ato_risk,
            web_sign_in_device_low_ato_risk,
            web_sign_in_success_rate_device_low_ato_risk,
            web_sign_in_success_device_high_ato_risk,
            web_sign_in_device_high_ato_risk,
            web_sign_in_success_rate_device_high_ato_risk,
            web_sign_in_success_device_critical_ato_risk,
            web_sign_in_device_critical_ato_risk,
            web_sign_in_success_rate_device_critical_ato_risk,
            desktop_web_sign_in_success_session,
            desktop_web_sign_in_session,
            desktop_web_sign_in_success_rate_session,
            desktop_web_sign_in_success_session_low_ato_risk,
            desktop_web_sign_in_session_low_ato_risk,
            desktop_web_sign_in_success_rate_session_low_ato_risk,
            desktop_web_sign_in_success_session_high_ato_risk,
            desktop_web_sign_in_session_high_ato_risk,
            desktop_web_sign_in_success_rate_session_high_ato_risk,
            desktop_web_sign_in_success_session_critical_ato_risk,
            desktop_web_sign_in_session_critical_ato_risk,
            desktop_web_sign_in_success_rate_session_critical_ato_risk,
            desktop_web_sign_in_success_account,
            desktop_web_sign_in_account,
            desktop_web_sign_in_success_rate_account,
            desktop_web_sign_in_success_account_low_ato_risk,
            desktop_web_sign_in_account_low_ato_risk,
            desktop_web_sign_in_success_rate_account_low_ato_risk,
            desktop_web_sign_in_success_account_high_ato_risk,
            desktop_web_sign_in_account_high_ato_risk,
            desktop_web_sign_in_success_rate_account_high_ato_risk,
            desktop_web_sign_in_success_account_critical_ato_risk,
            desktop_web_sign_in_account_critical_ato_risk,
            desktop_web_sign_in_success_rate_account_critical_ato_risk,
            desktop_web_sign_in_success_device,
            desktop_web_sign_in_device,
            desktop_web_sign_in_success_rate_device,
            desktop_web_sign_in_success_device_low_ato_risk,
            desktop_web_sign_in_device_low_ato_risk,
            desktop_web_sign_in_success_rate_device_low_ato_risk,
            desktop_web_sign_in_success_device_high_ato_risk,
            desktop_web_sign_in_device_high_ato_risk,
            desktop_web_sign_in_success_rate_device_high_ato_risk,
            desktop_web_sign_in_success_device_critical_ato_risk,
            desktop_web_sign_in_device_critical_ato_risk,
            desktop_web_sign_in_success_rate_device_critical_ato_risk,
        ],
        name="sign_in_server",
        bq_dialect="standard",
        inner_join_with_mapping=False,
        join_key=join_key,
        unit_id=unit_id
    )

    return mt


def account_recovery_client(study_type, study_name, start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    unit_id = ""
    join_key = ""
    source_table = ""

    if study_type == "mobile_device_level_study":
        unit_id = "config_device_id"
        join_key = "config_device_id"
        source_table = """
          `sc-portal.abtest_device_usermap_cumulative.{study_name}__{end_date}` usermap
        LEFT JOIN 
          `sc-analytics.report_growth.signup_login_user_level_2*` signup_login_user_level
        ON
          usermap.client_id=signup_login_user_level.client_id
        LEFT JOIN (
          SELECT
            client_id,
            MAX(IF(locked_account.ghost_id IS NOT NULL, 1, 0)) AS with_security_lock
          FROM
            `sc-analytics.report_growth.client_ghost_user_mapping_*` client_ghost_user_mapping
          LEFT JOIN (
            SELECT 
              ghost_id
            FROM
              `sc-infosec-services.abuse_analytics_data_feed.locked_user_*`
            WHERE 
              _table_suffix between '{start_date}' and FORMAT_DATE('%Y%m%d',DATE_ADD(PARSE_DATE('%Y%m%d', '{end_date}'), INTERVAL 1 DAY))
              AND DATE(TIMESTAMP_SUB(TIMESTAMP_MILLIS(timestamp), INTERVAL 8 HOUR)) BETWEEN parse_date('%Y%m%d','{start_date}') and parse_date('%Y%m%d','{end_date}')
              AND punishment = 1
            GROUP BY 1          
          ) locked_account
          ON
            client_ghost_user_mapping.ghost_user_id=locked_account.ghost_id
          WHERE
            _TABLE_SUFFIX BETWEEN '{start_date}' AND '{end_date}' 
          GROUP BY 1
        ) locked_client
        ON
          signup_login_user_level.client_id=locked_client.client_id
        WHERE
          CONCAT('2', _TABLE_SUFFIX) BETWEEN '{start_date}' AND '{end_date}' 
        """.format(study_name=study_name, unit_id=unit_id, source_table=source_table, start_date=start_date, end_date=end_date)
    # elif study_type == "user_level_study":
    if source_table != "":
        reset_password = Metric('reset_password', 'reset_password', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
        reset_password_uu = Metric('reset_password_uu', 'reset_password_uu', dist='bin', daily=True, cumulative=True, desired_direction=POSITIVE)
        reset_password_success = Metric('reset_password_success', 'reset_password_success', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
        reset_password_success_uu = Metric('reset_password_success_uu', 'reset_password_success_uu', dist='bin', daily=True, cumulative=True, desired_direction=POSITIVE)
        reset_password_locked_account = Metric('reset_password_locked_account', 'reset_password_locked_account', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
        reset_password_non_locked_account = Metric('reset_password_non_locked_account', 'reset_password_non_locked_account', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
        reset_password_success_locked_account = Metric('reset_password_success_locked_account', 'reset_password_success_locked_account', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
        reset_password_success_non_locked_account = Metric('reset_password_success_non_locked_account', 'reset_password_success_non_locked_account', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
        reset_password_uu_locked_account = Metric('reset_password_uu_locked_account', 'reset_password_uu_locked_account', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
        reset_password_uu_non_locked_account = Metric('reset_password_uu_non_locked_account', 'reset_password_uu_non_locked_account', dist='bin', daily=True, cumulative=True, desired_direction=POSITIVE)
        reset_password_success_uu_locked_account = Metric('reset_password_success_uu_locked_account', 'reset_password_success_uu_locked_account', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
        reset_password_success_uu_non_locked_account = Metric('reset_password_success_uu_non_locked_account', 'reset_password_success_uu_non_locked_account', dist='bin', daily=True, cumulative=True, desired_direction=POSITIVE)

        mt = MetricTable(
            sql="""
                    SELECT
                      PARSE_TIMESTAMP('%Y%m%d', CONCAT('2', _TABLE_SUFFIX)) as ts,
                      {unit_id}, 
                      SUM(reset_pwd) AS reset_password,
                      MAX(IF(reset_pwd_uu=1, 1, 0)) AS reset_password_uu,
                      SUM(reset_pwd_success) AS reset_password_success,
                      MAX(IF(reset_pwd_success_uu=1, 1, 0)) AS reset_password_success_uu,
                      
                      IF( MAX(IFNULL(locked_client.with_security_lock, 0))=1, SUM(reset_pwd), 0) AS reset_password_locked_account,
                      IF( MAX(IFNULL(locked_client.with_security_lock, 0))=0, SUM(reset_pwd), 0) AS reset_password_non_locked_account,
                      IF( MAX(IFNULL(locked_client.with_security_lock, 0))=1, SUM(reset_pwd_success), 0) AS reset_password_success_locked_account,
                      IF( MAX(IFNULL(locked_client.with_security_lock, 0))=0, SUM(reset_pwd_success), 0) AS reset_password_success_non_locked_account,      
                      
                      IF( MAX(IFNULL(locked_client.with_security_lock, 0))=1 AND MAX(reset_pwd_uu)=1, 1, 0) AS reset_password_uu_locked_account,
                      IF( MAX(IFNULL(locked_client.with_security_lock, 0))=0 AND MAX(reset_pwd_uu)=1, 1, 0) AS reset_password_uu_non_locked_account,
                      IF( MAX(IFNULL(locked_client.with_security_lock, 0))=1 AND MAX(reset_pwd_success_uu)=1, 1, 0) AS reset_password_success_uu_locked_account,
                      IF( MAX(IFNULL(locked_client.with_security_lock, 0))=0 AND MAX(reset_pwd_success_uu)=1, 1, 0) AS reset_password_success_uu_non_locked_account,                 
                      
                    FROM 
                      {source_table}
                    GROUP BY 1,2
                    """.format(unit_id=unit_id, source_table=source_table),

            metrics=[
                reset_password,
                reset_password_uu,
                reset_password_success,
                reset_password_success_uu,

                reset_password_locked_account,
                reset_password_non_locked_account,
                reset_password_success_locked_account,
                reset_password_success_non_locked_account,

                reset_password_uu_locked_account,
                reset_password_uu_non_locked_account,
                reset_password_success_uu_locked_account,
                reset_password_success_uu_non_locked_account,
            ],
            name="account_recovery_client",
            bq_dialect="standard",
            inner_join_with_mapping=False,
            join_key=join_key,
            unit_id=unit_id
        )
    else:
        logger.warning("Account Recovery Client Metrics are not available for this study type.")
        mt = None

    return mt


def security_lock(study_type, study_name, start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    unit_id = ""
    join_key = ""
    source_table = ""
    lock_reason_sql_statement = ""
    lock_reason_metric_object_list = []

    # This function creates sql statement and metric object for UU metric for each lock reason
    def create_sql_and_metric_object_for_lock_reason(lock_reason_list):
        metric_list = []
        metric_object_list = []
        sql_statement = ""
        for reason in lock_reason_list:
            processed_reason = re.sub(r"[^\w]", "_", reason)
            # print( """MAX(IF(ghost_user_id IS NOT NULL AND description = "{reason}", 1, 0)) AS security_{reason}_lock_uu,""".format(reason=reason))
            sql_statement += """MAX(IF(ghost_user_id IS NOT NULL AND description = "{reason}", 1, 0)) AS `security_{processed_reason}_lock_uu`,\n""".format(reason=reason, processed_reason=processed_reason)
            metric_list.append("security_{processed_reason}_lock_uu".format(processed_reason=processed_reason))
        for metric in metric_list:
            metric_object = Metric(metric, metric, dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
            metric_object_list.append(metric_object)

        return sql_statement, metric_object_list

    if study_type == "mobile_device_level_study":
        unit_id = "config_device_id"
        join_key = "config_device_id"
        source_table = """ (
        SELECT 
          event_date,
          config_device_id,
          usermap.client_id,
          ghost_user_id,
          description,
        FROM
          `sc-portal.abtest_device_usermap_cumulative.{study_name}__{end_date}` usermap
        INNER JOIN (
          SELECT
            event_date,
            client_id,
            ghost_id AS ghost_user_id,
            description
          FROM
            `sc-analytics.report_growth.client_ghost_user_mapping_*` client_ghost_user_mapping
          INNER JOIN (
            SELECT 
              DATE(TIMESTAMP_SUB(TIMESTAMP_MILLIS(timestamp), INTERVAL 8 HOUR)) as event_date,
              ghost_id,
              description
            FROM
              `sc-infosec-services.abuse_analytics_data_feed.locked_user_*`
            WHERE 
              _table_suffix between '{start_date}' and FORMAT_DATE('%Y%m%d',DATE_ADD(PARSE_DATE('%Y%m%d', '{end_date}'), INTERVAL 1 DAY))
              AND DATE(TIMESTAMP_SUB(TIMESTAMP_MILLIS(timestamp), INTERVAL 8 HOUR)) BETWEEN parse_date('%Y%m%d','{start_date}') and parse_date('%Y%m%d','{end_date}')
              AND punishment = 1
            GROUP BY 1,2,3         
          ) locked_account
          ON
            client_ghost_user_mapping.ghost_user_id=locked_account.ghost_id
          WHERE
            _TABLE_SUFFIX BETWEEN '{start_date}' AND '{end_date}'  /*use mapping table of the entire study period*/
          GROUP BY 1,2,3,4
        ) locked_client
        ON
          usermap.client_id=locked_client.client_id
        )
        """.format(study_name=study_name, unit_id=unit_id, start_date=start_date, end_date=end_date)

        top_lock_reason_sql = """
                             SELECT description, COUNT(DISTINCT {unit_id}) AS num_accounts
                             FROM {source_table}
                             GROUP BY 1 
                             ORDER BY 2 DESC
                             """.format(unit_id=unit_id, source_table=source_table)
        #print(top_lock_reason_sql)
        df = read_gbq(top_lock_reason_sql, project_id='sc-bq-gcs-billingonly',  dialect='standard')
        top_lock_reason_list = df.head(20).description.tolist()
        lock_reason_sql_statement, lock_reason_metric_object_list = create_sql_and_metric_object_for_lock_reason(top_lock_reason_list)

    elif study_type == "user_level_study":
        unit_id = "ghost_user_id"
        join_key = "ghost_user_id"
        source_table = """ (
          SELECT 
            DATE(TIMESTAMP_SUB(TIMESTAMP_MILLIS(timestamp), INTERVAL 8 HOUR)) as event_date,
            ghost_id as ghost_user_id,
            description
          FROM
            `sc-infosec-services.abuse_analytics_data_feed.locked_user_*`
          WHERE 
            _table_suffix between '{start_date}' and FORMAT_DATE('%Y%m%d',DATE_ADD(PARSE_DATE('%Y%m%d', '{end_date}'), INTERVAL 1 DAY))
            AND DATE(TIMESTAMP_SUB(TIMESTAMP_MILLIS(timestamp), INTERVAL 8 HOUR)) BETWEEN parse_date('%Y%m%d','{start_date}') and parse_date('%Y%m%d','{end_date}')
            AND punishment = 1
          GROUP BY 1,2,3        
        )
        """.format(start_date=start_date, end_date=end_date)

        top_lock_reason_sql = """
                             SELECT description, COUNT(DISTINCT {unit_id}) AS num_accounts
                             FROM {source_table}
                             GROUP BY 1 
                             ORDER BY 2 DESC
                             """.format(unit_id=unit_id, source_table=source_table)
        #print(top_lock_reason_sql)
        df = read_gbq(top_lock_reason_sql, project_id='sc-bq-gcs-billingonly', dialect='standard')
        top_lock_reason_list = df.head(20).description.tolist()
        lock_reason_sql_statement, lock_reason_metric_object_list = create_sql_and_metric_object_for_lock_reason(top_lock_reason_list)

    #print(lock_reason_sql_statement)
    if source_table != "":
        security_lock_account_count = Metric('security_lock_account_count', 'security_lock_account_count', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
        security_lock_uu = Metric('security_lock_uu', 'security_lock_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
        security_perm_lock_uu = Metric('security_perm_lock_uu', 'security_perm_lock_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
        security_temp_lock_uu = Metric('security_temp_lock_uu', 'security_temp_lock_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)

        mt = MetricTable(
            sql="""
                    SELECT
                      TIMESTAMP(event_date) as ts,
                      {unit_id}, 
                      COUNT(DISTINCT ghost_user_id) security_lock_account_count,
                      MAX(IF(ghost_user_id IS NOT NULL, 1, 0)) AS security_lock_uu,
                      MAX(IF(ghost_user_id IS NOT NULL AND description LIKE "%perm%", 1, 0))  AS security_perm_lock_uu,
                      MAX(IF(ghost_user_id IS NOT NULL AND description NOT LIKE "%perm%", 1, 0))  AS security_temp_lock_uu,
                      {lock_reason_sql_statement}  
                    FROM 
                      {source_table}
                    GROUP BY 1,2
                    """.format(unit_id=unit_id, source_table=source_table, lock_reason_sql_statement=lock_reason_sql_statement),

            metrics=[
                security_lock_account_count,
                security_lock_uu,
                security_perm_lock_uu,
                security_temp_lock_uu,
            ] + lock_reason_metric_object_list,
            name="security_lock",
            bq_dialect="standard",
            inner_join_with_mapping=False,
            join_key=join_key,
            unit_id=unit_id
        )
    else:
        logger.warning("Security Lock Metrics are not available for this study type.")
        mt = None

    return mt


def security_ads(study_type, study_name, start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    unit_id = ""
    join_key = ""
    source_table = ""

    if study_type == "mobile_device_level_study":
        unit_id = "config_device_id"
        join_key = "config_device_id"
        source_table = """ (
            SELECT
              event_date,
              usermap.config_device_id,
              usermap.client_id,
              context_message,
            FROM
             `sc-portal.abtest_device_usermap_cumulative.{study_name}__{end_date}` usermap
            INNER JOIN (
              SELECT
                DATE(TIMESTAMP_SUB( log_timestamp_ms_partition_col, INTERVAL 8 hour)) as event_date,
                http_request.client_id_str as client_id, 
                context_message,
              FROM
                `abuse-decision.audit_log.AbuseDecisionEvent`
              WHERE
                DATE(log_timestamp_ms_partition_col) BETWEEN PARSE_DATE("%Y%m%d", "{start_date}") AND DATE_ADD(PARSE_DATE("%Y%m%d", "{end_date}"), INTERVAL 1 DAY)
                AND DATE(TIMESTAMP_SUB( log_timestamp_ms_partition_col, INTERVAL 8 hour)) BETWEEN PARSE_DATE("%Y%m%d", "{start_date}") AND PARSE_DATE("%Y%m%d", "{end_date}") 
            ) ads
            ON
              usermap.client_id=ads.client_id
        )
        """.format(study_name=study_name, start_date=start_date, end_date=end_date)

    security_ads_phone_iar = Metric('security_ads_phone_iar', 'security_ads_phone_iar', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    security_ads_phone_iar_uu = Metric('security_ads_phone_iar_uu', 'security_ads_phone_iar_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    security_ads_email_iar = Metric('security_ads_email_iar', 'security_ads_email_iar', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    security_ads_email_iar_uu = Metric('security_ads_email_iar_uu', 'security_ads_email_iar_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)

    if source_table != "":
        mt = MetricTable(
            sql="""
            SELECT
              TIMESTAMP(event_date) as ts,
              {unit_id},
              SUM(IF(context_message LIKE 'IAR%PHONE%', 1, 0)) as security_ads_phone_iar,
              MAX(IF(context_message LIKE 'IAR%PHONE%', 1, 0)) as security_ads_phone_iar_uu,
              SUM(IF(context_message LIKE 'IAR%EMAIL%', 1, 0)) as security_ads_email_iar,
              MAX(IF(context_message LIKE 'IAR%EMAIL%', 1, 0)) as security_ads_email_iar_uu,
            FROM
              {source_table}
            GROUP BY 1,2
            """.format(unit_id=unit_id, source_table=source_table),

            metrics=[
                security_ads_phone_iar,
                security_ads_phone_iar_uu,
                security_ads_email_iar,
                security_ads_email_iar_uu,
            ],
            name="security_ads",
            bq_dialect="standard",
            inner_join_with_mapping=False,
            join_key=join_key,
            unit_id=unit_id
        )

    else:
        mt = None
        logger.warning("Security ADS Metrics are not available for this study type.")
    return mt


def account_takeover(study_type, study_name, start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    unit_id = ""
    join_key = ""
    source_table = ""

    if study_type == "user_level_study":
        unit_id = "ghost_user_id"
        join_key = "ghost_user_id"
        source_table = """ (
        SELECT
          DISTINCT DATE(ticket_distinct.created_at) as event_date,
          ghost_id as ghost_user_id,
          ticket_id,
          ticket_distinct.status, 
          child_json, 
          ticket_distinct.created_at,
          category
        FROM
          `sc-analytics.report_zendesk.ticket_distinct` ticket_distinct
        INNER JOIN 
          `sc-analytics.report_zendesk.ticket_events` ticket_events
        ON
          ticket_distinct.id=ticket_events.ticket_id
        INNER JOIN 
          `sc-mjolnir.enigma.user_map_{end_date}` user_map
        ON
          JSON_EXTRACT_SCALAR(child_json,'$.custom_ticket_fields.********')=user_map.user_name
        WHERE
          LOWER(category) = 'ca-hacked-account'
          AND status IN ('closed','solved')
          AND FORMAT_DATE("%Y%m%d",DATE(ticket_distinct.created_at)) BETWEEN '{start_date}' AND '{end_date}'
          AND child_json LIKE '%********%'
        )
        """.format(study_name=study_name, start_date=start_date, end_date=end_date)

    solved_as_hacked_ticket = Metric(
        'solved_as_hacked_ticket',
        'solved_as_hacked_ticket',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    solved_as_hacked_ticket_uu = Metric(
        'solved_as_hacked_ticket_uu',
        'solved_as_hacked_ticket_uu',
        dist='bin',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    if source_table != "":

        mt = MetricTable(
            sql="""
                SELECT 
                  TIMESTAMP(event_date) as ts,
                  {unit_id}, 
                  COUNT(DISTINCT ticket_id) as solved_as_hacked_ticket,
                  MAX(IF(ticket_id IS NOT NULL, 1, 0)) as solved_as_hacked_ticket_uu
                FROM 
                  {source_table}
                GROUP BY 1,2
                """.format(unit_id=unit_id, source_table=source_table),

            metrics=[
                solved_as_hacked_ticket,
                solved_as_hacked_ticket_uu,
            ],
            name="account_takeover",
            bq_dialect="standard",
            inner_join_with_mapping=False,
            join_key=join_key,
            unit_id=unit_id
        )
    else:
        mt = None
        logger.warning("Account Takeover Metrics are not available for this study type.")

    return mt


def telephony_cost(study_type, study_name, start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    unit_id = ""
    join_key = ""
    source_table = ""

    if study_type == "mobile_device_level_study":
        unit_id = "config_device_id"
        join_key = "config_device_id"

        telephony_cost = Metric(
            'telephony_cost',
            'telephony_cost',
            dist='cont',
            daily=True,
            cumulative=True,
            desired_direction=NEGATIVE
        )

        mt = MetricTable(
            sql=""" SELECT 
                      t2.ts,
                      config_device_id,
                      sum(cost) as telephony_cost
                    FROM 
                      `sc-portal.abtest_device_usermap_cumulative.{study_name}__{end_date}` t1
                    LEFT JOIN (
                    SELECT
                      PARSE_TIMESTAMP('%Y%m%d', avgcost.date) as ts,
                      status.client_id,
                      status.telephony_session_id,
                      status.country AS country,
                      status.provider AS provider,
                      avg_cost,
                      reqs AS reqs,
                      reqs * avg_cost AS cost,
                      conversions AS conversions,
                      sms_reqs,
                      flash_reqs,
                      voice_reqs
                    FROM (
                      SELECT
                        date,
                        provider,
                        country,
                        SUM(IF(provider = "NEXMO", euro_to_dollar * costs, costs))/ IF(SUM(reqs)>0, SUM(reqs), 1) AS avg_cost
                      FROM (
                        SELECT
                          _TABLE_SUFFIX AS date,
                          provider,
                          phone_number_country_code AS country,
                          delivery_currency AS currency,
                          SUM(CAST(price AS float64)) AS costs,
                          COUNT(1) AS reqs
                        FROM
                          `sc-analytics.prod_analytics_ops_user.daily_events_*`
                        WHERE
                          phone_number_country_code IS NOT NULL
                          AND provider IS NOT NULL
                          AND price IS NOT NULL
                          AND _TABLE_SUFFIX BETWEEN "{start_date}" AND "{end_date}"
                        GROUP BY
                          provider,
                          currency,
                          date,
                          country) costz
                      JOIN (
                        SELECT
                          _TABLE_SUFFIX AS euro_date,
                          AVG(CAST(conversion_rate AS float64)) AS euro_to_dollar,
                        FROM
                          `sc-analytics.prod_analytics_ops_user.daily_events_*`
                        WHERE
                          from_currency = "EUR"
                          AND to_currency = "USD"
                          AND _TABLE_SUFFIX BETWEEN "{start_date}" AND "{end_date}"
                        GROUP BY
                          euro_date ) AS currency_rate
                      ON
                        date = euro_date
                      GROUP BY
                        date, provider, country ) AS avgcost
                    INNER JOIN (
                      SELECT
                        _table_suffix as date,
                        client_id,
                        telephony_session_id,
                        provider,
                        phone_number_country_code AS country,
                        SUM(IF(status = "requested", 1, 0)) AS reqs,
                        SUM(IF(status = "requested" AND delivery_strategy = "SMS", 1, 0)) AS sms_reqs,
                        SUM( IF (status = "requested" AND delivery_strategy = "FLASH", 1, 0)) AS flash_reqs,
                        SUM(IF(status = "requested" AND delivery_strategy = "VOICE", 1, 0)) AS voice_reqs,
                        MAX(IF(status = "conversion", 1, 0)) AS conversions
                      FROM
                        `sc-analytics.prod_analytics_ops_user.daily_events_*`
                      WHERE
                        country IS NOT NULL
                        AND provider IS NOT NULL
                        #AND feature = "PASSWORD_RESET"
                        AND _TABLE_SUFFIX BETWEEN "{start_date}" AND "{end_date}"
                      GROUP BY
                        _table_suffix,
                        provider,
                        country,
                        client_id,
                        telephony_session_id ) AS status
                    ON
                      avgcost.provider = status.provider
                      AND avgcost.country = status.country
                      AND avgcost.date=status.date
                    WHERE
                      reqs >= 0 
                    ) t2
                  ON t1.client_id=t2.client_id
                  GROUP BY 1,2
            """.format(study_name=study_name, start_date=start_date, end_date=end_date),

            metrics=[
                telephony_cost
            ],
            name="telephony_cost_metrics",
            bq_dialect="standard",
            join_key=join_key,
            unit_id=unit_id
        )
    else:
        logger.warning("Telephony Cost Metrics are not available for this study type.")
        mt = None

    return mt

def engagement(study_type, study_name, start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    unit_id = ""
    join_key = ""
    source_table = ""

    if study_type == "mobile_device_level_study":
        unit_id = "config_device_id"
        join_key = "config_device_id"
        source_table = """ (
        SELECT
          event_date,
          config_device_id,
          usermap.client_id,
          ghost_user_id,
          APP_APPLICATION_OPEN_UU,
          APP_APPLICATION_OPEN_UU_NOT_LOCKED_NOT_REPORTED,
          APP_APPLICATION_OPEN_UU_LOCKED_OR_REPORTED,
          D0_APP_APPLICATION_OPEN_UU,
          D0_APP_APPLICATION_OPEN_UU_NOT_LOCKED_NOT_REPORTED,
          D0_APP_APPLICATION_OPEN_UU_LOCKED_OR_REPORTED
        FROM
          `sc-portal.abtest_device_usermap_cumulative.{study_name}__{end_date}` usermap
        INNER JOIN (
          SELECT
            event_date,
            client_id,
            client_ghost_user_mapping.ghost_user_id,
            APP_APPLICATION_OPEN_UU,
            CASE WHEN APP_APPLICATION_OPEN_UU=1 AND lock.ghost_user_id IS NULL AND report.ghost_user_id IS NULL THEN 1 else 0 END AS APP_APPLICATION_OPEN_UU_NOT_LOCKED_NOT_REPORTED,
            CASE WHEN APP_APPLICATION_OPEN_UU=1 AND (lock.ghost_user_id IS NOT NULL OR report.ghost_user_id IS NOT NULL) THEN 1 else 0 END AS APP_APPLICATION_OPEN_UU_LOCKED_OR_REPORTED,
            D0_APP_APPLICATION_OPEN_UU,
            CASE WHEN D0_APP_APPLICATION_OPEN_UU=1 AND lock.ghost_user_id IS NULL AND report.ghost_user_id IS NULL THEN 1 else 0 END AS D0_APP_APPLICATION_OPEN_UU_NOT_LOCKED_NOT_REPORTED,
            CASE WHEN D0_APP_APPLICATION_OPEN_UU=1 AND (lock.ghost_user_id IS NOT NULL OR report.ghost_user_id IS NOT NULL) THEN 1 else 0 END AS D0_APP_APPLICATION_OPEN_UU_LOCKED_OR_REPORTED,
          FROM
            `sc-analytics.report_growth.client_ghost_user_mapping_*` client_ghost_user_mapping
          LEFT JOIN (
            SELECT
              DATE(event_date) AS event_date,
              ghost_user_id,
              APP_APPLICATION_OPEN_UU,
              D0_APP_APPLICATION_OPEN_UU,
            FROM
              `sc-analytics.report_app.dau_user_country_2*`
            WHERE
              CONCAT("2",_table_suffix) between '{start_date}' and '{end_date}'
          ) duc
          ON
            parse_date("%Y%m%d", client_ghost_user_mapping._table_suffix)=duc.event_date
            AND client_ghost_user_mapping.ghost_user_id=duc.ghost_user_id
          LEFT JOIN (
          SELECT
            ghost_id as ghost_user_id
          FROM
            `sc-infosec-services.abuse_analytics_data_feed.locked_user_*`
          WHERE
             # extend end date to make sure each account has at least 7-day lookback window
            _table_suffix between '{start_date}' and FORMAT_DATE('%Y%m%d',DATE_ADD(PARSE_DATE('%Y%m%d', '{end_date}'), INTERVAL 7 DAY))
            AND DATE(TIMESTAMP_SUB(TIMESTAMP_MILLIS(timestamp), INTERVAL 8 HOUR)) BETWEEN parse_date('%Y%m%d','{start_date}') and date_add(parse_date('%Y%m%d','{end_date}'), interval 6 day)
            AND punishment = 1
            GROUP BY 1
          ) lock
          ON
            client_ghost_user_mapping.ghost_user_id=lock.ghost_user_id
          LEFT JOIN (
          SELECT
            reportee_ghost_id as ghost_user_id
          FROM
            `sc-infosec-services.abuse_analytics_data_feed.in_app_report_*`
          WHERE
             # extend end date to make sure each account has at least 7-day lookback window
            _table_suffix between '{start_date}' and FORMAT_DATE('%Y%m%d',DATE_ADD(PARSE_DATE('%Y%m%d', '{end_date}'), INTERVAL 7 DAY))
            AND DATE(TIMESTAMP_SUB(TIMESTAMP_MILLIS(timestamp), INTERVAL 8 HOUR)) BETWEEN parse_date('%Y%m%d','{start_date}') and date_add(parse_date('%Y%m%d','{end_date}'), interval 6 day)
            GROUP BY 1
          ) report
          ON
            client_ghost_user_mapping.ghost_user_id=report.ghost_user_id
          WHERE
            _TABLE_SUFFIX BETWEEN '{start_date}' AND '{end_date}'  /*use mapping table of the entire study period*/
        ) duc_client
        ON
          usermap.client_id=duc_client.client_id
          and timestamp(concat(event_date, " 23:59:59")) >= timestamp_sub(timestamp_millis(cast(initial_timestamp as int64)), interval 8 hour)
        )
        """.format(study_name=study_name,start_date=start_date, end_date=end_date)

    elif study_type=="user_level_study":
        unit_id = "ghost_user_id"
        join_key = "ghost_user_id"
        source_table = """ (
        SELECT
          DATE(event_date) AS event_date,
          duc.ghost_user_id,
          APP_APPLICATION_OPEN_UU,
          CASE WHEN APP_APPLICATION_OPEN_UU=1 AND lock.ghost_user_id IS NULL AND report.ghost_user_id IS NULL THEN 1 else 0 END AS APP_APPLICATION_OPEN_UU_NOT_LOCKED_NOT_REPORTED,
          CASE WHEN APP_APPLICATION_OPEN_UU=1 AND (lock.ghost_user_id IS NOT NULL OR report.ghost_user_id IS NOT NULL) THEN 1 else 0 END AS APP_APPLICATION_OPEN_UU_LOCKED_OR_REPORTED,
          D0_APP_APPLICATION_OPEN_UU,
          CASE WHEN D0_APP_APPLICATION_OPEN_UU=1 AND lock.ghost_user_id IS NULL AND report.ghost_user_id IS NULL THEN 1 else 0 END AS D0_APP_APPLICATION_OPEN_UU_NOT_LOCKED_NOT_REPORTED,
          CASE WHEN D0_APP_APPLICATION_OPEN_UU=1 AND (lock.ghost_user_id IS NOT NULL OR report.ghost_user_id IS NOT NULL) THEN 1 else 0 END AS D0_APP_APPLICATION_OPEN_UU_LOCKED_OR_REPORTED,
        FROM
          `sc-analytics.report_app.dau_user_country_2*` duc     
        LEFT JOIN (
        SELECT 
          ghost_id as ghost_user_id
        FROM
          `sc-infosec-services.abuse_analytics_data_feed.locked_user_*`
        WHERE 
           # extend end date to make sure each account has at least 7-day lookback window
          _table_suffix between '{start_date}' and FORMAT_DATE('%Y%m%d',DATE_ADD(PARSE_DATE('%Y%m%d', '{end_date}'), INTERVAL 7 DAY))
          AND DATE(TIMESTAMP_SUB(TIMESTAMP_MILLIS(timestamp), INTERVAL 8 HOUR)) BETWEEN parse_date('%Y%m%d','{start_date}') and date_add(parse_date('%Y%m%d','{end_date}'), interval 6 day)
          AND punishment = 1
          GROUP BY 1
        ) lock
        ON
          duc.ghost_user_id=lock.ghost_user_id
        LEFT JOIN (
        SELECT 
          reportee_ghost_id as ghost_user_id
        FROM
          `sc-infosec-services.abuse_analytics_data_feed.in_app_report_*`
        WHERE 
           # extend end date to make sure each account has at least 7-day lookback window
          _table_suffix between '{start_date}' and FORMAT_DATE('%Y%m%d',DATE_ADD(PARSE_DATE('%Y%m%d', '{end_date}'), INTERVAL 7 DAY))
          AND DATE(TIMESTAMP_SUB(TIMESTAMP_MILLIS(timestamp), INTERVAL 8 HOUR)) BETWEEN parse_date('%Y%m%d','{start_date}') and date_add(parse_date('%Y%m%d','{end_date}'), interval 6 day)
          GROUP BY 1
        ) report
        ON
          duc.ghost_user_id=report.ghost_user_id
        WHERE 
          CONCAT("2",_table_suffix) between '{start_date}' and '{end_date}'  
        )
        """.format(start_date=start_date, end_date=end_date)

    if source_table != "":
        app_open_active_days = Metric('app_open_active_days', 'app_open_active_days', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
        app_open_active_days_not_locked_not_reported = Metric('app_open_active_days_not_locked_not_reported', 'app_open_active_days_not_locked_not_reported', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
        app_open_active_days_locked_or_reported = Metric('app_open_active_days_locked_or_reported', 'app_open_active_days_locked_or_reported', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
        d0_app_application_open_uu = Metric('d0_app_application_open_uu', 'd0_app_application_open_uu', dist='bin', daily=True, cumulative=True, desired_direction=POSITIVE)
        d0_app_application_open_uu_not_locked_not_reported = Metric('d0_app_application_open_uu_not_locked_not_reported', 'd0_app_application_open_uu_not_locked_not_reported', dist='bin', daily=True, cumulative=True, desired_direction=POSITIVE)
        d0_app_application_open_uu_locked_or_reported = Metric('d0_app_application_open_uu_locked_or_reported', 'd0_app_application_open_uu_locked_or_reported', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)

        mt = MetricTable(
            sql="""
                    SELECT
                      TIMESTAMP(event_date) as ts,
                      {unit_id}, 
                      IFNULL(MAX(APP_APPLICATION_OPEN_UU),0) AS app_open_active_days,
                      IFNULL(MAX(APP_APPLICATION_OPEN_UU_NOT_LOCKED_NOT_REPORTED),0) AS app_open_active_days_not_locked_not_reported,
                      IFNULL(MAX(APP_APPLICATION_OPEN_UU_LOCKED_OR_REPORTED),0) AS app_open_active_days_locked_or_reported,
                      IFNULL(MAX(D0_APP_APPLICATION_OPEN_UU),0) AS d0_app_application_open_uu,
                      IFNULL(MAX(D0_APP_APPLICATION_OPEN_UU_NOT_LOCKED_NOT_REPORTED),0) AS d0_app_application_open_uu_not_locked_not_reported,
                      IFNULL(MAX(D0_APP_APPLICATION_OPEN_UU_LOCKED_OR_REPORTED),0) AS d0_app_application_open_uu_locked_or_reported,
                    FROM 
                      {source_table}
                    GROUP BY 1,2
                    """.format(unit_id=unit_id, source_table=source_table),

            metrics=[
                app_open_active_days,
                app_open_active_days_not_locked_not_reported,
                app_open_active_days_locked_or_reported,
                d0_app_application_open_uu,
                d0_app_application_open_uu_not_locked_not_reported,
                d0_app_application_open_uu_locked_or_reported
            ],
            name="engagement",
            bq_dialect="standard",
            inner_join_with_mapping=False,
            join_key=join_key,
            unit_id=unit_id
        )
    else:
        logger.warning("Engagement Metrics are not available for this study type.")
        mt = None

    return mt

def friending(study_type, study_name, start_date, end_date):
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    unit_id = ""
    join_key = ""
    source_table = ""
    
    if study_type=="user_level_study":
        unit_id = "ghost_user_id"
        join_key = "ghost_user_id"
        source_table = """ (
        SELECT
          DATE(event_date) AS event_date,
          sender_ghost_id AS ghost_user_id,
          recipient_ghost_id,
          sender_was_locked, sender_was_locked_d1, sender_was_locked_d2, sender_was_locked_d3,
          was_reciprocated_by_recipient, was_reciprocated_by_recipient_d1, was_reciprocated_by_recipient_d2, was_reciprocated_by_recipient_d3,
          was_negatively_actioned_by_recipient, was_negatively_actioned_by_recipient_d1, was_negatively_actioned_by_recipient_d2, was_negatively_actioned_by_recipient_d3,
          was_actioned, was_actioned_d1, was_actioned_d2, was_actioned_d3
        FROM
          `sc-analytics.report_safety.safety_friending_funnel_user_pair_2*`
        WHERE 
          CONCAT("2",_table_suffix) between '{start_date}' and '{end_date}' 
        )
        """.format(start_date=start_date, end_date=end_date)
    
    if source_table != "":
        outbound_friend_request_count = Metric('outbound_friend_request_count', 'outbound_friend_request_count', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
        outbound_friend_request_count_sender_locked_by_d3 = Metric('outbound_friend_request_count_sender_locked_by_d3', 'outbound_friend_request_count_sender_locked_by_d3', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
        outbound_friend_request_count_sender_not_locked_by_d3 = Metric('outbound_friend_request_count_sender_not_locked_by_d3', 'outbound_friend_request_count_sender_not_locked_by_d3', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
        outbound_friend_request_count_reciprocated_by_recipient_by_d3 = Metric('outbound_friend_request_count_reciprocated_by_recipient_by_d3', 'outbound_friend_request_count_reciprocated_by_recipient_by_d3', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
        outbound_friend_request_count_reciprocated_and_not_negatively_actioned_by_recipient_by_d3 = Metric('outbound_friend_request_count_reciprocated_and_not_negatively_actioned_by_recipient_by_d3', 'outbound_friend_request_count_reciprocated_and_not_negatively_actioned_by_recipient_by_d3', dist='cont', daily=True, cumulative=True, desired_direction=POSITIVE)
        outbound_friend_request_count_negatively_actioned_by_recipient_by_d3 = Metric('outbound_friend_request_count_negatively_actioned_by_recipient_by_d3', 'outbound_friend_request_count_negatively_actioned_by_recipient_by_d3', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
        outbound_friend_request_count_pending_by_d3 = Metric('outbound_friend_request_count_pending_by_d3', 'outbound_friend_request_count_pending_by_d3', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
        
        mt = MetricTable(
                sql="""
                    SELECT
                      TIMESTAMP(event_date) as ts,
                      {unit_id}, 
                      COUNT(DISTINCT recipient_ghost_id) AS outbound_friend_request_count,
                      COUNT(DISTINCT CASE WHEN GREATEST(sender_was_locked, sender_was_locked_d1, sender_was_locked_d2, sender_was_locked_d3)=1 THEN recipient_ghost_id END) AS outbound_friend_request_count_sender_locked_by_d3,
            
                      COUNT(DISTINCT CASE WHEN GREATEST(sender_was_locked, sender_was_locked_d1, sender_was_locked_d2, sender_was_locked_d3)=0 THEN recipient_ghost_id END) outbound_friend_request_count_sender_not_locked_by_d3,
            
                      COUNT(DISTINCT CASE WHEN GREATEST(sender_was_locked, sender_was_locked_d1, sender_was_locked_d2, sender_was_locked_d3)=0 AND GREATEST(was_reciprocated_by_recipient, was_reciprocated_by_recipient_d1, was_reciprocated_by_recipient_d2, was_reciprocated_by_recipient_d3)=1 THEN recipient_ghost_id END) AS outbound_friend_request_count_reciprocated_by_recipient_by_d3,

                      COUNT(DISTINCT CASE WHEN GREATEST(sender_was_locked, sender_was_locked_d1, sender_was_locked_d2, sender_was_locked_d3)=0 AND GREATEST(was_reciprocated_by_recipient, was_reciprocated_by_recipient_d1, was_reciprocated_by_recipient_d2, was_reciprocated_by_recipient_d3)=1 AND GREATEST(was_negatively_actioned_by_recipient, was_negatively_actioned_by_recipient_d1, was_negatively_actioned_by_recipient_d2, was_negatively_actioned_by_recipient_d3)=0 THEN recipient_ghost_id END) AS outbound_friend_request_count_reciprocated_and_not_negatively_actioned_by_recipient_by_d3,
            
                      COUNT(DISTINCT CASE WHEN GREATEST(sender_was_locked, sender_was_locked_d1, sender_was_locked_d2, sender_was_locked_d3)=0 AND GREATEST(was_negatively_actioned_by_recipient, was_negatively_actioned_by_recipient_d1, was_negatively_actioned_by_recipient_d2, was_negatively_actioned_by_recipient_d3)=1 THEN recipient_ghost_id END) AS outbound_friend_request_count_negatively_actioned_by_recipient_by_d3,
            
                      COUNT(DISTINCT CASE WHEN GREATEST(sender_was_locked, sender_was_locked_d1, sender_was_locked_d2, sender_was_locked_d3)=0 AND GREATEST(was_actioned, was_actioned_d1, was_actioned_d2, was_actioned_d3)=0 THEN recipient_ghost_id END) AS outbound_friend_request_count_pending_by_d3,
                    FROM 
                      {source_table}
                    GROUP BY 1,2
                    """.format(unit_id=unit_id, source_table=source_table),
    
                metrics=[
                    outbound_friend_request_count,
                    outbound_friend_request_count_sender_locked_by_d3,
                    outbound_friend_request_count_sender_not_locked_by_d3,
                    outbound_friend_request_count_reciprocated_by_recipient_by_d3,
                    outbound_friend_request_count_reciprocated_and_not_negatively_actioned_by_recipient_by_d3,
                    outbound_friend_request_count_negatively_actioned_by_recipient_by_d3,
                    outbound_friend_request_count_pending_by_d3,
                ],
                name="friending",
                bq_dialect="standard",
                inner_join_with_mapping=False,
                join_key=join_key,
                unit_id=unit_id
        )
    
    else:
        logger.warning("Friending Metrics are not available for this study type: {study_type}.".format(study_type))
        mt = None
    return mt
