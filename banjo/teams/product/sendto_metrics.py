"""
SendTo and Off-Platform Sharing Metrics
Contact: <EMAIL>, <EMAIL>
"""

from __future__ import division, print_function
from banjo.abtest.metric import (
    Metric, MetricTable, abtest_metrics_sql_helper
)
from datetime import timedelta
import datetime
import pandas as pd
import logging
from banjo.abtest.metric import POSITIVE, NEGATIVE

logger = logging.getLogger(__name__)

logger.warning("Please check the metric file for definition and calculation of each metrics.")

__all__ = [
    # SendTo available, seen, selection
    "sendto_sections_available",
    "sendto_sections_seen",
    "sendto_object_types_seen",
    "sendto_camera_snap_send",
    "sendto_recipients_by_section",
    "sendto_recipients_by_section_cancelled",
    "snap_recipient_by_source",
    "sendto_session_by_source",
    "sendto_select_all_usage",
    # SendTo session time
    "sendto_session_time",
    "sendto_time_per_cell_selected",
    # SendTo receiver-side metrics
    "snap_viewer",
    "snap_viewer_engagement_change",
    "bidirectional_communication_breakdown",
    "sendto_snap_send_recipient_breakdown",
    "sendto_snap_send_relationship_breakdown",
    # SendTo Recents ranking
    "recents_ranking_snapchatter_item_lvl_metrics",
    "recents_ranking_snapchatter_session_lvl_metrics",
    "recents_ranking_group_item_lvl_metrics",
    "recents_ranking_group_session_lvl_metrics",
    # SendTo Search ranking
    "search_ranking_item_lvl_metrics",
    # SendTo performacne
    # "sendto_slow_scroll",
    "sendto_render_latency",
    "sendto_render_latency_percentile",
    "sendto_data_ready_view_model_ready",
    # SendTo Candidate-Side DAU Delta
    "sendto_candidate_side_dau_delta",
    # OPS
    "off_platform_share",
    "off_platform_share_session_time_quantile",
    "off_platform_share_operation_quantile",
    "context_menu_action",
    # Invite
    "social_sms_action",
    "social_sms_invite_funnel_daily",
    "invite_contact_impression_action",
    "twilio_total_cost",
    "twilio_total_cost_per_dnu",
    # OPS & Invite receiver-side metrics
    "link_receiver_link_app_open",
    "link_receiver_deep_link_lifecycle",
    "link_receiver_link_web_page_view",
    "dnu_from_ops_ip",
    "indirect_org_dnu_attrib_1d",
    # Other
    "dnu_correspondent_dnu",
]

def sendto_sections_available(start_date, end_date):
    """
    Recipient cell available per session
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    source_table = """
    `sc-analytics.report_growth.page_sendto_session_end_by_user_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    session_count = Metric(
        'session_count',
        'Section Available Session Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    best_friends_available = Metric(
        'best_friends_available',
        'Best Friends Available',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    recents_available = Metric(
        'recents_available',
        'Recents Available',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    list_available = Metric(
        'list_available',
        'List Available',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    contextual_list_available = Metric(
        'contextual_list_available',
        'Contextual List Available',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    contextual_list_new_friends_available = Metric(
        'contextual_list_new_friends_available',
        'Contextual List (New Friends) Available',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    contextual_list_nearby_available = Metric(
        'contextual_list_nearby_available',
        'Contextual List (Nearby) Available',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    contextual_list_birthday_available = Metric(
        'contextual_list_birthday_available',
        'Contextual List (Birthday) Available',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    contextual_list_contacts_available = Metric(
        'contextual_list_contacts_available',
        'Contextual List (Contacts) Available',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snap_back_available = Metric(
        'snap_back_available',
        'Snap Back Available',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snap_stars_available = Metric(
        'snap_stars_available',
        'Snap Stars Available',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    friends_available = Metric(
        'friends_available',
        'Friends Available',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    groups_available = Metric(
        'groups_available',
        'Groups (Section) Available',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    top_groups_available = Metric(
        'top_groups_available',
        'Top Groups Available',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    suggested_available = Metric(
        'suggested_available',
        'Suggested Available',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    realtime_available = Metric(
        'realtime_available',
        'Realtime Available',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    new_groups_available = Metric(
        'new_groups_available',
        'New Groups Available',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    search_available = Metric(
        'search_available',
        'Search Available',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    quick_add_available = Metric(
        'quick_add_available',
        'Quick Add Available',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    stories_available = Metric(
        'stories_available',
        'Stories Available',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    spotlight_available = Metric(
        'spotlight_available',
        'Spotlight Available',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    last_recipients_available = Metric(
        'last_recipients_available',
        'Last Recipients Available',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    best_friends_available_per_session = Metric(
        col='best_friends_available_per_session',
        numerator='best_friends_available',
        denominator='session_count',
        dist='ratio'
    )

    recents_available_per_session = Metric(
        col='recents_available_per_session',
        numerator='recents_available',
        denominator='session_count',
        dist='ratio'
    )

    list_available_per_session = Metric(
        col='list_available_per_session',
        numerator='list_available',
        denominator='session_count',
        dist='ratio'
    )

    contextual_list_available_per_session = Metric(
        col='contextual_list_available_per_session',
        numerator='contextual_list_available',
        denominator='session_count',
        dist='ratio'
    )

    contextual_list_new_friends_available_per_session = Metric(
        col='contextual_list_new_friends_available_per_session',
        numerator='contextual_list_new_friends_available',
        denominator='session_count',
        dist='ratio'
    )

    contextual_list_nearby_available_per_session = Metric(
        col='contextual_list_nearby_available_per_session',
        numerator='contextual_list_nearby_available',
        denominator='session_count',
        dist='ratio'
    )

    contextual_list_birthday_available_per_session = Metric(
        col='contextual_list_birthday_available_per_session',
        numerator='contextual_list_birthday_available',
        denominator='session_count',
        dist='ratio'
    )

    contextual_list_contacts_available_per_session = Metric(
        col='contextual_list_contacts_available_per_session',
        numerator='contextual_list_contacts_available',
        denominator='session_count',
        dist='ratio'
    )

    snap_back_available_per_session = Metric(
        col='snap_back_available_per_session',
        numerator='snap_back_available',
        denominator='session_count',
        dist='ratio'
    )

    snap_stars_available_per_session = Metric(
        col='snap_stars_available_per_session',
        numerator='snap_stars_available',
        denominator='session_count',
        dist='ratio'
    )

    friends_available_per_session = Metric(
        col='friends_available_per_session',
        numerator='friends_available',
        denominator='session_count',
        dist='ratio'
    )

    groups_available_per_session = Metric(
        col='groups_available_per_session',
        numerator='groups_available',
        denominator='session_count',
        dist='ratio'
    )

    top_groups_available_per_session = Metric(
        col='top_groups_available_per_session',
        numerator='top_groups_available',
        denominator='session_count',
        dist='ratio'
    )

    suggested_available_per_session = Metric(
        col='suggested_available_per_session',
        numerator='suggested_available',
        denominator='session_count',
        dist='ratio'
    )

    realtime_available_per_session = Metric(
        col='realtime_available_per_session',
        numerator='realtime_available',
        denominator='session_count',
        dist='ratio'
    )

    new_groups_available_per_session = Metric(
        col='new_groups_available_per_session',
        numerator='new_groups_available',
        denominator='session_count',
        dist='ratio'
    )

    search_available_per_session = Metric(
        col='search_available_per_session',
        numerator='search_available',
        denominator='session_count',
        dist='ratio'
    )

    quick_add_available_per_session = Metric(
        col='quick_add_available_per_session',
        numerator='quick_add_available',
        denominator='session_count',
        dist='ratio'
    )

    stories_available_per_session = Metric(
        col='stories_available_per_session',
        numerator='stories_available',
        denominator='session_count',
        dist='ratio'
    )

    spotlight_available_per_session = Metric(
        col='spotlight_available_per_session',
        numerator='spotlight_available',
        denominator='session_count',
        dist='ratio'
    )

    last_recipients_available_per_session = Metric(
        col='last_recipients_available_per_session',
        numerator='last_recipients_available',
        denominator='session_count',
        dist='ratio'
    )

    mt = MetricTable(
        sql="""
        SELECT
          PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
          ghost_user_id,
          SUM(sessions_count) AS session_count,
          SUM(friends_available)AS friends_available,
          SUM(suggested_available)AS suggested_available,
          SUM(best_friends_available)AS best_friends_available,
          SUM(search_available)AS search_available,
          SUM(groups_available)AS groups_available,
          SUM(top_groups_available)AS top_groups_available,
          SUM(new_groups_available)AS new_groups_available,
          SUM(recents_available)AS recents_available,
          SUM(list_available)AS list_available,
          SUM(contextual_list_available)AS contextual_list_available,
          SUM(contextual_list_new_friends_available) AS contextual_list_new_friends_available,
          SUM(contextual_list_nearby_available) AS contextual_list_nearby_available,
          SUM(contextual_list_birthday_available) AS contextual_list_birthday_available,
          SUM(contextual_list_contacts_available) AS contextual_list_contacts_available,
          SUM(snap_back_available)AS snap_back_available,
          SUM(snap_stars_available)AS snap_stars_available,
          SUM(realtime_available)AS realtime_available,
          SUM(stories_available)AS stories_available,
          SUM(spotlight_available)AS spotlight_available,
          SUM(quick_add_available)AS quick_add_available,
          SUM(last_recipients_available)AS last_recipients_available,
        FROM
          {source_table}
        GROUP BY 1,2
        """.format(source_table=source_table),
        metrics=[
                session_count,
                best_friends_available,
                friends_available,
                recents_available,
                list_available,
                contextual_list_available,
                contextual_list_new_friends_available,
                contextual_list_nearby_available,
                contextual_list_birthday_available,
                contextual_list_contacts_available,                
                search_available,
                snap_back_available,
                snap_stars_available,
                groups_available,
                top_groups_available,
                #suggested_available,
                #realtime_available,
                new_groups_available,
                last_recipients_available,
                quick_add_available,
                stories_available,
                spotlight_available,

                best_friends_available_per_session,
                friends_available_per_session,
                recents_available_per_session,
                list_available_per_session,
                contextual_list_available_per_session,
                contextual_list_new_friends_available_per_session,
                contextual_list_nearby_available_per_session,
                contextual_list_birthday_available_per_session,
                contextual_list_contacts_available_per_session,                
                snap_back_available_per_session,
                snap_stars_available_per_session,
                groups_available_per_session,
                top_groups_available_per_session,
                #suggested_available_per_session,
                #realtime_available_per_session,
                new_groups_available_per_session,
                search_available_per_session,
                quick_add_available_per_session,
                last_recipients_available_per_session,
                stories_available_per_session,
                spotlight_available_per_session,
                ],
        name="sendto_sections_available",
        bq_dialect="standard"
    )
    return mt

def sendto_sections_seen(start_date, end_date):
    """
    Recipient cell seen per session.
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    source_table = """
    `sc-analytics.report_growth.page_sendto_session_end_by_user_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    session_count = Metric(
        'session_count',
        'Section Seen Session Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    best_friends_seen = Metric(
        'best_friends_seen',
        'Best Friends Seen',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    recents_seen = Metric(
        'recents_seen',
        'Recents Seen',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    list_seen = Metric(
        'list_seen',
        'List Seen',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    contextual_list_seen = Metric(
        'contextual_list_seen',
        'Contextual List Seen',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snap_back_seen = Metric(
        'snap_back_seen',
        'Snap Back Seen',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snap_stars_seen = Metric(
        'snap_stars_seen',
        'Snap Stars Seen',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    friends_seen = Metric(
        'friends_seen',
        'Friends Seen',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    groups_seen = Metric(
        'groups_seen',
        'Groups (Section) Seen',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    top_groups_seen = Metric(
        'top_groups_seen',
        'Top Groups Seen',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    suggested_seen = Metric(
        'suggested_seen',
        'Suggested Seen',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    realtime_seen = Metric(
        'realtime_seen',
        'Realtime Seen',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    new_groups_seen = Metric(
        'new_groups_seen',
        'New Groups Seen',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    search_seen = Metric(
        'search_seen',
        'Search Seen',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    stories_seen = Metric(
        'stories_seen',
        'Stories Seen',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    spotlight_seen = Metric(
        'spotlight_seen',
        'Spotlight Seen',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    share_sheet_seen = Metric(
        'share_sheet_seen',
        'Share Sheet Seen',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    last_recipients_seen = Metric(
        'last_recipients_seen',
        'Last Recipients Seen',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    best_friends_seen_per_session = Metric(
        col='best_friends_seen_per_session',
        numerator='best_friends_seen',
        denominator='session_count',
        dist='ratio'
    )

    recents_seen_per_session = Metric(
        col='recents_seen_per_session',
        numerator='recents_seen',
        denominator='session_count',
        dist='ratio'
    )

    list_seen_per_session = Metric(
        col='list_seen_per_session',
        numerator='list_seen',
        denominator='session_count',
        dist='ratio'
    )

    contextual_list_seen_per_session = Metric(
        col='contextual_list_seen_per_session',
        numerator='contextual_list_seen',
        denominator='session_count',
        dist='ratio'
    )

    snap_back_seen_per_session = Metric(
        col='snap_back_seen_per_session',
        numerator='snap_back_seen',
        denominator='session_count',
        dist='ratio'
    )

    snap_stars_seen_per_session = Metric(
        col='snap_stars_seen_per_session',
        numerator='snap_stars_seen',
        denominator='session_count',
        dist='ratio'
    )

    friends_seen_per_session = Metric(
        col='friends_seen_per_session',
        numerator='friends_seen',
        denominator='session_count',
        dist='ratio'
    )

    groups_seen_per_session = Metric(
        col='groups_seen_per_session',
        numerator='groups_seen',
        denominator='session_count',
        dist='ratio'
    )

    top_groups_seen_per_session = Metric(
        col='top_groups_seen_per_session',
        numerator='top_groups_seen',
        denominator='session_count',
        dist='ratio'
    )

    suggested_seen_per_session = Metric(
        col='suggested_seen_per_session',
        numerator='suggested_seen',
        denominator='session_count',
        dist='ratio'
    )

    realtime_seen_per_session = Metric(
        col='realtime_seen_per_session',
        numerator='realtime_seen',
        denominator='session_count',
        dist='ratio'
    )

    new_groups_seen_per_session = Metric(
        col='new_groups_seen_per_session',
        numerator='new_groups_seen',
        denominator='session_count',
        dist='ratio'
    )

    search_seen_per_session = Metric(
        col='search_seen_per_session',
        numerator='search_seen',
        denominator='session_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    stories_seen_per_session = Metric(
        col='stories_seen_per_session',
        numerator='stories_seen',
        denominator='session_count',
        dist='ratio'
    )

    spotlight_seen_per_session = Metric(
        col='spotlight_seen_per_session',
        numerator='spotlight_seen',
        denominator='session_count',
        dist='ratio'
    )

    share_sheet_seen_per_session = Metric(
        col='share_sheet_seen_per_session',
        numerator='share_sheet_seen',
        denominator='session_count',
        dist='ratio'
    )

    last_recipients_seen_per_session = Metric(
        col='last_recipients_seen_per_session',
        numerator='last_recipients_seen',
        denominator='session_count',
        dist='ratio'
    )

    mt = MetricTable(
        sql="""
        SELECT
          PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
          ghost_user_id,
          SUM(sessions_count) AS session_count,
          SUM(friends_seen)AS friends_seen,
          SUM(suggested_seen)AS suggested_seen,
          SUM(best_friends_seen)AS best_friends_seen,
          SUM(search_seen)AS search_seen,
          SUM(groups_seen)AS groups_seen,
          SUM(top_groups_seen)AS top_groups_seen,
          SUM(new_groups_seen)AS new_groups_seen,
          SUM(recents_seen)AS recents_seen,
          SUM(list_seen)AS list_seen,
          SUM(contextual_list_seen)AS contextual_list_seen,
          SUM(snap_back_seen)AS snap_back_seen,
          SUM(snap_stars_seen)AS snap_stars_seen,
          SUM(realtime_seen)AS realtime_seen,
          SUM(stories_seen)AS stories_seen,
          SUM(spotlight_seen)AS spotlight_seen,
          SUM(share_sheet_available_sessions_count)AS share_sheet_seen,
          SUM(last_recipients_seen)AS last_recipients_seen,
        FROM
          {source_table}
        GROUP BY 1,2
        """.format(source_table=source_table),
        metrics=[
                session_count,
                best_friends_seen,
                friends_seen,
                recents_seen,
                list_seen,
                contextual_list_seen,
                search_seen,
                snap_back_seen,
                snap_stars_seen,
                groups_seen,
                top_groups_seen,
                #suggested_seen,
                #realtime_seen,
                new_groups_seen,
                last_recipients_seen,
                stories_seen,
                spotlight_seen,
                share_sheet_seen,

                best_friends_seen_per_session,
                friends_seen_per_session,
                recents_seen_per_session,
                list_seen_per_session,
                contextual_list_seen_per_session,
                snap_back_seen_per_session,
                snap_stars_seen_per_session,
                groups_seen_per_session,
                top_groups_seen_per_session,
                #suggested_seen_per_session,
                #realtime_seen_per_session,
                new_groups_seen_per_session,
                search_seen_per_session,
                last_recipients_seen_per_session,
                stories_seen_per_session,
                spotlight_seen_per_session,
                share_sheet_seen_per_session
                ],
        name="sendto_sections_seen",
        bq_dialect="standard"
    )
    return mt

def sendto_object_types_seen(start_date, end_date):
    """
    Objects seen per session by type
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    source_table = """
    `sc-analytics.report_growth.sendto_impression_action_session_level_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    n_group_impressed = Metric(
        'n_group_impressed',
        'Group Objects Seen',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    n_snapchatter_impressed = Metric(
        'n_snapchatter_impressed',
        'Snapchatter Objects Seen',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    n_contact_non_snapchatter_impressed = Metric(
        'n_contact_non_snapchatter_impressed',
        'Contact Objects Seen',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    mt = MetricTable(
        sql="""
        SELECT
          PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
          ghost_user_id,
          SUM(n_group_impressed) AS n_group_impressed,
          SUM(n_snapchatter_impressed)AS n_snapchatter_impressed,
          SUM(n_contact_non_snapchatter_impressed)AS n_contact_non_snapchatter_impressed,
        FROM
          {source_table}
        GROUP BY 1,2
        """.format(source_table=source_table),
        metrics=[
            n_group_impressed,
            n_snapchatter_impressed,
            n_contact_non_snapchatter_impressed
                ],
        name="sendto_object_types_seen",
        bq_dialect="standard"
    )
    return mt

def sendto_camera_snap_send(start_date, end_date):
    """
    DIRECT_SNAP_SEND from 'CAMERA', 'GALLERY', 'DISCOVER','CAMERA_ROLL', including duplicates if multiple snaps are sent to the same users
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    source_table = """
    `sc-analytics.report_growth.direct_snap_send_by_user_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    total_snap_send = Metric(
        'total_snap_send',
        'SendTo Camera Total Snap Send',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    total_snap_send_active_day = Metric(
        'sendto_snap_active_day',
        'SendTo Camera Snap Send Active Day',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    total_snap_send_1_1_recipient_only = Metric(
        'total_snap_send_1_1_recipient_only',
        'SendTo Camera Total Snap Send (1-1 Recipient Only)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    total_snap_send_group_only = Metric(
        'total_snap_send_group_only',
        'SendTo Camera Total Snap Send (Group Recipient Only)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    total_snap_send_1_1_recipient_and_group = Metric(
        'total_snap_send_1_1_recipient_and_group',
        'SendTo Camera Total Snap Send (Both 1-1 and Group Recipients)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    total_snap_sender = Metric(
        'sendto_snap_sender',
        'SendTo Camera Snap Sender',
        dist='bin',
        daily=True,
        cumulative=True
    )

    num_of_1_1_snap_recipients = Metric(
        'num_of_1_1_snap_recipients',
        'SendTo Camera 1 to 1 Snap Recipients',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    num_of_1_1_recipients_per_snap = Metric(
        col='sendto_camera_1_to_1_snap_recipients_per_snap',
        numerator='num_of_1_1_snap_recipients',
        denominator='total_snap_send',
        dist='ratio'
    )

    num_of_unique_recipients = Metric(
        'num_of_unique_recipients',
        'SendTo Camera Unique Recipients',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    num_of_unique_recipients_per_snap = Metric(
        col='sendto_camera_unique_recipients_per_snap',
        numerator='num_of_unique_recipients',
        denominator='total_snap_send',
        dist='ratio'
    )

    num_of_non_unique_recipients = Metric(
        'num_of_non_unique_recipients',
        'SendTo Camera Non-Unique Recipients',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    num_of_non_unique_recipients_per_snap = Metric(
        col='sendto_camera_non_unique_recipients_per_snap',
        numerator='num_of_non_unique_recipients',
        denominator='total_snap_send',
        dist='ratio'
    )

    num_of_group_unique_recipients = Metric(
        'num_of_group_unique_recipients',
        'SendTo Camera Group Unique Recipients',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    num_of_group_unique_recipients_per_snap = Metric(
        col='sendto_camera_group_unique_recipients_per_snap',
        numerator='num_of_group_unique_recipients',
        denominator='total_snap_send',
        dist='ratio'
    )

    num_of_group_non_unique_recipients = Metric(
        'num_of_group_non_unique_recipients',
        'SendTo Camera Group Non-Unique Recipients',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    num_of_group_non_unique_recipients_per_snap = Metric(
        col='sendto_camera_group_non_unique_recipients_per_snap',
        numerator='num_of_group_non_unique_recipients',
        denominator='total_snap_send',
        dist='ratio'
    )

    num_of_groups = Metric(
        'num_of_groups',
        'SendTo Camera Groups',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    num_of_groups_per_snap = Metric(
        col='sendto_camera_groups_per_snap',
        numerator='num_of_groups',
        denominator='total_snap_send',
        dist='ratio'
    )

    num_of_non_unique_recipients_cap_25000_ = Metric(
        'num_of_non_unique_recipients_cap_25000_',
        'SendTo Camera Non-Unique Recipients (cap @25k)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    num_of_non_unique_recipients_cap_30000_ = Metric(
        'num_of_non_unique_recipients_cap_30000_',
        'SendTo Camera Non-Unique Recipients (cap @30k)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    num_of_non_unique_recipients_cap_35000_ = Metric(
        'num_of_non_unique_recipients_cap_35000_',
        'SendTo Camera Non-Unique Recipients (cap @35k)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    num_of_unique_recipients_cap_25000_ = Metric(
        'num_of_unique_recipients_cap_25000_',
        'SendTo Camera Unique Recipients (cap @25k)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    num_of_unique_recipients_cap_30000_ = Metric(
        'num_of_unique_recipients_cap_30000_',
        'SendTo Camera Unique Recipients (cap @30k)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    num_of_unique_recipients_cap_35000_ = Metric(
        'num_of_unique_recipients_cap_35000_',
        'SendTo Camera Unique Recipients (cap @35k)',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    camera_user_recipient_following_count = Metric(
        'camera_user_recipient_following_count',
        'SendTo Camera Recipients (Following)',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    camera_user_recipient_follower_count = Metric(
        'camera_user_recipient_follower_count',
        'SendTo Camera Recipients (Follower)',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    camera_user_recipient_friend_count = Metric(
        'camera_user_recipient_friend_count',
        'SendTo Camera Recipients (Friend)',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    camera_user_recipient_non_friend_count = Metric(
        'camera_user_recipient_non_friend_count',
        'SendTo Camera Recipients (Non-friend)',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    camera_user_recipient_snap_star_count = Metric(
        'camera_user_recipient_snap_star_count',
        'SendTo Camera Recipients (Snap Star)',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    camera_user_recipient_public_account_count = Metric(
        'camera_user_recipient_public_account_count',
        'SendTo Camera Recipients (Public Account)',
        dist='cont',
        daily=True,
        cumulative=True,
    )


    mt = MetricTable(
        sql="""
        SELECT
            PARSE_TIMESTAMP('%Y%m%d', _table_suffix) AS ts,
            ghost_user_id AS ghost_user_id,
            MAX(1) AS sendto_snap_sender,
            MAX(1) AS sendto_snap_active_day,
            SUM(num_of_direct_snaps) AS total_snap_send,
            SUM(if(num_of_1_1_snap_recipients > 0 and num_of_group_unique_recipients = 0, num_of_direct_snaps, 0)) AS total_snap_send_1_1_recipient_only,
            SUM(num_of_1_1_snap_recipients) AS num_of_1_1_snap_recipients,
            SUM(if(num_of_1_1_snap_recipients = 0 and num_of_group_unique_recipients > 0, num_of_direct_snaps, 0)) AS total_snap_send_group_only,
            SUM(if(num_of_1_1_snap_recipients > 0 and num_of_group_unique_recipients > 0, num_of_direct_snaps, 0)) AS total_snap_send_1_1_recipient_and_group,
            SUM(num_of_groups_selected) AS num_of_groups,
            SUM(num_of_group_unique_recipients) AS num_of_group_unique_recipients,
            SUM(num_of_group_non_unique_recipients) AS num_of_group_non_unique_recipients,
            SUM(num_of_non_unique_recipients) AS num_of_non_unique_recipients,
            SUM(num_of_unique_recipients) AS num_of_unique_recipients,
            IF(SUM(num_of_non_unique_recipients) > 25000, 25000, SUM(num_of_non_unique_recipients)) AS num_of_non_unique_recipients_cap_25000_,
            IF(SUM(num_of_non_unique_recipients) > 30000, 30000, SUM(num_of_non_unique_recipients)) AS num_of_non_unique_recipients_cap_30000_,
            IF(SUM(num_of_non_unique_recipients) > 35000, 35000, SUM(num_of_non_unique_recipients)) AS num_of_non_unique_recipients_cap_35000_,
            IF(SUM(num_of_unique_recipients) > 25000, 25000, SUM(num_of_unique_recipients)) AS num_of_unique_recipients_cap_25000_,
            IF(SUM(num_of_unique_recipients) > 30000, 30000, SUM(num_of_unique_recipients)) AS num_of_unique_recipients_cap_30000_,
            IF(SUM(num_of_unique_recipients) > 35000, 35000, SUM(num_of_unique_recipients)) AS num_of_unique_recipients_cap_35000_,
            SUM(user_recipient_following_count) AS camera_user_recipient_following_count,
            SUM(user_recipient_follower_count) AS camera_user_recipient_follower_count,
            SUM(user_recipient_friend_count) AS camera_user_recipient_friend_count,
            SUM(user_recipient_non_friend_count) AS camera_user_recipient_non_friend_count,
            SUM(user_recipient_snap_star_count) AS camera_user_recipient_snap_star_count,
            SUM(user_recipient_public_account_count) AS camera_user_recipient_public_account_count
        FROM
            {source_table}
        AND source IN ('CAMERA', 'GALLERY', 'DISCOVER', 'CAMERA_ROLL')
        GROUP BY
            1, 2
        """.format(source_table=source_table),
        metrics=[
        total_snap_send,
        total_snap_send_active_day,
        total_snap_sender,
        total_snap_send_1_1_recipient_only,
        total_snap_send_group_only,
        total_snap_send_1_1_recipient_and_group,
        num_of_1_1_snap_recipients,
        num_of_1_1_recipients_per_snap,
        num_of_unique_recipients,
        num_of_unique_recipients_per_snap,
        num_of_unique_recipients_cap_25000_,
        num_of_unique_recipients_cap_30000_,
        num_of_unique_recipients_cap_35000_,
        num_of_non_unique_recipients,
        num_of_non_unique_recipients_per_snap,
        num_of_non_unique_recipients_cap_25000_,
        num_of_non_unique_recipients_cap_30000_,
        num_of_non_unique_recipients_cap_35000_,
        num_of_groups,
        num_of_groups_per_snap,
        num_of_group_unique_recipients,
        num_of_group_unique_recipients_per_snap,
        num_of_group_non_unique_recipients,
        num_of_group_non_unique_recipients_per_snap,
        camera_user_recipient_following_count,
        camera_user_recipient_follower_count,
        camera_user_recipient_friend_count,
        camera_user_recipient_non_friend_count,
        camera_user_recipient_snap_star_count,
        camera_user_recipient_public_account_count,
         ],
        name="sendto_camera_snap_send",
        bq_dialect="standard"
    )
    return mt

def sendto_recipients_by_section(start_date, end_date):
    """
    Recipient cell selected by section. Selecting a group is counted as 1.
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    source_table = """
    `sc-analytics.report_growth.page_sendto_session_end_by_user_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    snaps_sent_to_best_friends_section = Metric(
        'snaps_sent_to_best_friends_section',
        'Recipients Selected From Best Friends',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snaps_sent_to_friends_section = Metric(
        'snaps_sent_to_friends_section',
        'Recipients Selected From Friends',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snaps_sent_to_stories_section = Metric(
        'snaps_sent_to_stories_section',
        'Recipients Selected From Stories',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snaps_sent_to_spotlight_section = Metric(
        'snaps_sent_to_spotlight_section',
        'Recipients Selected From Spotlight',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snaps_sent_to_share_sheet_section = Metric(
        'snaps_sent_to_share_sheet_section',
        'Recipients Selected From Share Sheet',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    snaps_sent_to_groups_section = Metric(
        'snaps_sent_to_groups_section',
        'Recipients Selected From Groups',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snaps_sent_to_top_groups_section = Metric(
        'snaps_sent_to_top_groups_section',
        'Recipients Selected From Top Groups',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snaps_sent_to_recents_section = Metric(
        'snaps_sent_to_recents_section',
        'Recipients Selected From Recents',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snaps_sent_to_list_section = Metric(
        'snaps_sent_to_list_section',
        'Recipients Selected From List',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snaps_sent_to_contextual_list_section = Metric(
        'snaps_sent_to_contextual_list_section',
        'Recipients Selected From Contextual List',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snaps_sent_to_snap_back_section = Metric(
        'snaps_sent_to_snap_back_section',
        'Recipients Selected From Snap Back',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snaps_sent_to_snap_stars_section = Metric(
        'snaps_sent_to_snap_stars_section',
        'Recipients Selected From Snap Stars',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snaps_sent_to_suggested_section = Metric(
        'snaps_sent_to_suggested_section',
        'Recipients Selected From Suggested',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snaps_sent_to_search_section = Metric(
        'snaps_sent_to_search_section',
        'Recipients Selected From Search',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snaps_sent_to_realtime_section = Metric(
        'snaps_sent_to_realtime_section',
        'Recipients Selected From Realtime',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snaps_sent_to_last_recipients_section = Metric(
        'snaps_sent_to_last_recipients_section',
        'Recipients Selected From Last Recipients',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snaps_sent_to_new_groups_section = Metric(
        'snaps_sent_to_new_groups_section',
        'Recipients Selected From New Groups',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snaps_sent_to_quick_add_section = Metric(
        'snaps_sent_to_quick_add_section',
        'Recipients Selected From Quick Add',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snaps_sent_to_all_section = Metric(
        'snaps_sent_to_all_section',
        'Recipients Selected From All Section',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    section_session_count = Metric(
        'section_session_count',
        'Session Count (SUCCESS)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    search_selected_ratio = Metric(
        col='recipients_selected_from_search_per_session',
        numerator='snaps_sent_to_search_section',
        denominator='section_session_count',
        dist='ratio'
    )

    suggested_selected_ratio = Metric(
        col='recipients_selected_from_suggested_per_session',
        numerator='snaps_sent_to_suggested_section',
        denominator='section_session_count',
        dist='ratio'
    )

    best_friends_selected_ratio = Metric(
        col='recipients_selected_from_best_friends_per_session',
        numerator='snaps_sent_to_best_friends_section',
        denominator='section_session_count',
        dist='ratio'
    )

    friends_selected_ratio = Metric(
        col='recipients_selected_from_friends_per_session',
        numerator='snaps_sent_to_friends_section',
        denominator='section_session_count',
        dist='ratio'
    )

    group_selected_ratio = Metric(
        col='recipients_selected_from_groups_per_session',
        numerator='snaps_sent_to_groups_section',
        denominator='section_session_count',
        dist='ratio'
    )

    top_group_selected_ratio = Metric(
        col='recipients_selected_from_top_groups_per_session',
        numerator='snaps_sent_to_top_groups_section',
        denominator='section_session_count',
        dist='ratio'
    )

    recents_selected_ratio = Metric(
        col='recipients_selected_from_recents_per_session',
        numerator='snaps_sent_to_recents_section',
        denominator='section_session_count',
        dist='ratio'
    )

    list_selected_ratio = Metric(
        col='recipients_selected_from_list_per_session',
        numerator='snaps_sent_to_list_section',
        denominator='section_session_count',
        dist='ratio'
    )

    contextual_list_selected_ratio = Metric(
        col='recipients_selected_from_contextual_list_per_session',
        numerator='snaps_sent_to_contextual_list_section',
        denominator='section_session_count',
        dist='ratio'
    )

    snap_back_selected_ratio = Metric(
        col='recipients_selected_from_snap_back_per_session',
        numerator='snaps_sent_to_snap_back_section',
        denominator='section_session_count',
        dist='ratio'
    )

    snap_stars_selected_ratio = Metric(
        col='recipients_selected_from_snap_stars_per_session',
        numerator='snaps_sent_to_snap_stars_section',
        denominator='section_session_count',
        dist='ratio'
    )

    realtime_selected_ratio = Metric(
        col='recipients_selected_from_realtime_per_session',
        numerator='snaps_sent_to_realtime_section',
        denominator='section_session_count',
        dist='ratio'
    )

    last_recipients_selected_ratio = Metric(
        col='recipients_selected_from_last_recipients_per_session',
        numerator='snaps_sent_to_last_recipients_section',
        denominator='section_session_count',
        dist='ratio'
    )

    new_groups_selected_ratio = Metric(
        col='recipients_selected_from_new_groups_per_session',
        numerator='snaps_sent_to_new_groups_section',
        denominator='section_session_count',
        dist='ratio'
    )

    quick_add_selected_ratio = Metric(
        col='recipients_selected_from_quick_add_per_session',
        numerator='snaps_sent_to_quick_add_section',
        denominator='section_session_count',
        dist='ratio'
    )

    stories_selected_ratio = Metric(
        col='recipients_selected_from_stories_per_session',
        numerator='snaps_sent_to_stories_section',
        denominator='section_session_count',
        dist='ratio'
    )

    spotlight_selected_ratio = Metric(
        col='recipients_selected_from_spotlight_per_session',
        numerator='snaps_sent_to_spotlight_section',
        denominator='section_session_count',
        dist='ratio'
    )

    share_sheet_selected_ratio = Metric(
        col='recipients_selected_from_share_sheet_per_session',
        numerator='snaps_sent_to_share_sheet_section',
        denominator='section_session_count',
        dist='ratio'
    )

    all_section_selected_ratio = Metric(
        col='recipients_selected_from_all_section_per_session',
        numerator='snaps_sent_to_all_section',
        denominator='section_session_count',
        dist='ratio'
    )

    mt = MetricTable(
        sql="""
        SELECT
          PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
          ghost_user_id,
          sum(sessions_count) as section_session_count,
          sum(new_friends_selected) as snaps_sent_to_new_friends_section,
          sum(stories_selected) as snaps_sent_to_stories_section,
          sum(spotlight_selected) as snaps_sent_to_spotlight_section,
          sum(share_sheet_selected) as snaps_sent_to_share_sheet_section,
          sum(friends_selected) as snaps_sent_to_friends_section,
          sum(suggested_selected) as snaps_sent_to_suggested_section,
          sum(quick_add_selected) as snaps_sent_to_quick_add_section,
          sum(best_friends_selected) as snaps_sent_to_best_friends_section,
          sum(search_selected) as snaps_sent_to_search_section,
          sum(groups_selected) as snaps_sent_to_groups_section,
          sum(groups_selected) as snaps_sent_to_top_groups_section,
          sum(new_groups_selected) as snaps_sent_to_new_groups_section,
          sum(recents_selected) as snaps_sent_to_recents_section,
          sum(list_selected) as snaps_sent_to_list_section,
          sum(contextual_list_selected) as snaps_sent_to_contextual_list_section,
          sum(snap_stars_selected) as snaps_sent_to_snap_stars_section,
          sum(snap_back_selected) as snaps_sent_to_snap_back_section,
          sum(realtime_selected) as snaps_sent_to_realtime_section,
          sum(last_recipients_selected) as snaps_sent_to_last_recipients_section,
          sum(COALESCE(friends_selected,0)
            +COALESCE(recents_selected,0)
            +COALESCE(best_friends_selected,0)
            +COALESCE(search_selected,0)
            +COALESCE(groups_selected,0)
            +COALESCE(top_groups_selected,0)
            +COALESCE(list_selected,0)
            +COALESCE(contextual_list_selected,0)
            +COALESCE(snap_stars_selected,0)
            +COALESCE(snap_back_selected,0)
            +COALESCE(last_recipients_selected,0)
            +COALESCE(new_groups_selected,0)) as snaps_sent_to_all_section
        FROM
          {source_table}
        and status = 'SUCCESS'
        GROUP BY 1,2
        """.format(source_table=source_table),
        metrics=[
                snaps_sent_to_best_friends_section,
                #snaps_sent_to_suggested_section,
                snaps_sent_to_groups_section,
                snaps_sent_to_top_groups_section,
                snaps_sent_to_friends_section,
                snaps_sent_to_recents_section,
                snaps_sent_to_list_section,
                snaps_sent_to_contextual_list_section,
                snaps_sent_to_snap_back_section,
                snaps_sent_to_snap_stars_section,
                snaps_sent_to_search_section,
                #snaps_sent_to_realtime_section,
                snaps_sent_to_last_recipients_section,
                snaps_sent_to_new_groups_section,
                snaps_sent_to_quick_add_section,
                snaps_sent_to_all_section,
                snaps_sent_to_stories_section,
                snaps_sent_to_spotlight_section,
                snaps_sent_to_share_sheet_section,

                section_session_count,
                search_selected_ratio,
                #suggested_selected_ratio,
                best_friends_selected_ratio,
                friends_selected_ratio,
                group_selected_ratio,
                top_group_selected_ratio,
                recents_selected_ratio,
                list_selected_ratio,
                contextual_list_selected_ratio,
                snap_back_selected_ratio,
                snap_stars_selected_ratio,
                #realtime_selected_ratio,
                last_recipients_selected_ratio,
                new_groups_selected_ratio,
                quick_add_selected_ratio,
                all_section_selected_ratio,
                stories_selected_ratio,
                spotlight_selected_ratio,
                share_sheet_selected_ratio
                ],
        name="sendto_recipients_by_section",
        bq_dialect="standard"
    )
    return mt

def sendto_recipients_by_section_cancelled(start_date, end_date):
    """
    Recipient cell selected by section. Selecting a group is counted as 1.
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    source_table = """
    `sc-analytics.report_growth.page_sendto_session_end_by_user_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    snaps_sent_to_best_friends_section_c = Metric(
        'snaps_sent_to_best_friends_section_c',
        'Recipients Selected From Best Friends (CANCELLED)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )
    snaps_sent_to_stories_section_c = Metric(
        'snaps_sent_to_stories_section_c',
        'Recipients Selected From Stories (CANCELLED)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )
    snaps_sent_to_spotlight_section_c = Metric(
        'snaps_sent_to_spotlight_section_c',
        'Recipients Selected From Spotlight (CANCELLED)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )
    snaps_sent_to_share_sheet_section_c = Metric(
        'snaps_sent_to_share_sheet_section_c',
        'Recipients Selected From Share Sheet (CANCELLED)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )
    snaps_sent_to_friends_section_c = Metric(
        'snaps_sent_to_friends_section_c',
        'Recipients Selected From Friends (CANCELLED)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    snaps_sent_to_groups_section_c = Metric(
        'snaps_sent_to_groups_section_c',
        'Recipients Selected From Groups (CANCELLED)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    snaps_sent_to_top_groups_section_c = Metric(
        'snaps_sent_to_top_groups_section_c',
        'Recipients Selected From Top Groups (CANCELLED)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    snaps_sent_to_recents_section_c = Metric(
        'snaps_sent_to_recents_section_c',
        'Recipients Selected From Recents (CANCELLED)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    snaps_sent_to_suggested_section_c = Metric(
        'snaps_sent_to_suggested_section_c',
        'Recipients Selected From Suggested (CANCELLED)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    snaps_sent_to_search_section_c = Metric(
        'snaps_sent_to_search_section_c',
        'Recipients Selected From Search (CANCELLED)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    snaps_sent_to_realtime_section_c = Metric(
        'snaps_sent_to_realtime_section_c',
        'Recipients Selected From Realtime (CANCELLED)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    snaps_sent_to_last_recipients_section_c = Metric(
        'snaps_sent_to_last_recipients_section_c',
        'Recipients Selected From Last Recipients (CANCELLED)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    snaps_sent_to_list_section_c = Metric(
        'snaps_sent_to_list_section_c',
        'Recipients Selected From List (CANCELLED)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    snaps_sent_to_contextual_list_section_c = Metric(
        'snaps_sent_to_contextual_list_section_c',
        'Recipients Selected From Contextual List (CANCELLED)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    snaps_sent_to_snap_stars_section_c = Metric(
        'snaps_sent_to_snap_stars_section_c',
        'Recipients Selected From Snap Stars (CANCELLED)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    snaps_sent_to_snap_back_section_c = Metric(
        'snaps_sent_to_snap_back_section_c',
        'Recipients Selected From Snap Back (CANCELLED)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    snaps_sent_to_new_groups_section_c = Metric(
        'snaps_sent_to_new_groups_section_c',
        'Recipients Selected From New Groups (CANCELLED)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    snaps_sent_to_all_section_c = Metric(
        'snaps_sent_to_all_section_c',
        'Recipients Selected From All Section (CANCELLED)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    section_session_count_c = Metric(
        'section_session_count_c',
        'Session Count (CANCELLED)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    search_selected_ratio_c = Metric(
        col='recipients_selected_from_search_per_session_cancelled',
        numerator='snaps_sent_to_search_section_c',
        denominator='section_session_count_c',
        dist='ratio', 
        desired_direction=NEGATIVE
    )

    suggested_selected_ratio_c = Metric(
        col='recipients_selected_from_suggested_per_session_cancelled',
        numerator='snaps_sent_to_suggested_section_c',
        denominator='section_session_count_c',
        dist='ratio', 
        desired_direction=NEGATIVE
    )

    best_friends_selected_ratio_c = Metric(
        col='recipients_selected_from_best_friends_per_session_cancelled',
        numerator='snaps_sent_to_best_friends_section_c',
        denominator='section_session_count_c',
        dist='ratio', 
        desired_direction=NEGATIVE
    )

    friends_selected_ratio_c = Metric(
        col='recipients_selected_from_friends_per_session_cancelled',
        numerator='snaps_sent_to_friends_section_c',
        denominator='section_session_count_c',
        dist='ratio', 
        desired_direction=NEGATIVE
    )

    group_selected_ratio_c = Metric(
        col='recipients_selected_from_groups_per_session_cancelled',
        numerator='snaps_sent_to_groups_section_c',
        denominator='section_session_count_c',
        dist='ratio', 
        desired_direction=NEGATIVE
    )

    top_group_selected_ratio_c = Metric(
        col='recipients_selected_from_top_groups_per_session_cancelled',
        numerator='snaps_sent_to_top_groups_section_c',
        denominator='section_session_count_c',
        dist='ratio', 
        desired_direction=NEGATIVE
    )

    recents_selected_ratio_c = Metric(
        col='recipients_selected_from_recents_per_session_cancelled',
        numerator='snaps_sent_to_recents_section_c',
        denominator='section_session_count_c',
        dist='ratio', 
        desired_direction=NEGATIVE
    )

    realtime_selected_ratio_c = Metric(
        col='recipients_selected_from_realtime_per_session_cancelled',
        numerator='snaps_sent_to_realtime_section_c',
        denominator='section_session_count_c',
        dist='ratio', 
        desired_direction=NEGATIVE
    )

    last_recipients_selected_ratio_c = Metric(
        col='recipients_selected_from_last_recipients_per_session_cancelled',
        numerator='snaps_sent_to_last_recipients_section_c',
        denominator='section_session_count_c',
        dist='ratio', 
        desired_direction=NEGATIVE
    )

    list_selected_ratio_c = Metric(
        col='recipients_selected_from_list_per_session_cancelled',
        numerator='snaps_sent_to_list_section_c',
        denominator='section_session_count_c',
        dist='ratio', 
        desired_direction=NEGATIVE
    )

    contextual_list_selected_ratio_c = Metric(
        col='recipients_selected_from_contextual_list_per_session_cancelled',
        numerator='snaps_sent_to_contextual_list_section_c',
        denominator='section_session_count_c',
        dist='ratio', 
        desired_direction=NEGATIVE
    )

    snap_stars_selected_ratio_c = Metric(
        col='recipients_selected_from_snap_stars_per_session_cancelled',
        numerator='snaps_sent_to_snap_stars_section_c',
        denominator='section_session_count_c',
        dist='ratio', 
        desired_direction=NEGATIVE
    )

    snap_back_selected_ratio_c = Metric(
        col='recipients_selected_from_snap_back_per_session_cancelled',
        numerator='snaps_sent_to_snap_back_section_c',
        denominator='section_session_count_c',
        dist='ratio', 
        desired_direction=NEGATIVE
    )

    new_groups_selected_ratio_c = Metric(
        col='recipients_selected_from_new_groups_per_session_cancelled',
        numerator='snaps_sent_to_new_groups_section_c',
        denominator='section_session_count_c',
        dist='ratio', 
        desired_direction=NEGATIVE
    )

    stories_selected_ratio_c = Metric(
        col='recipients_selected_from_stories_per_session_cancelled',
        numerator='snaps_sent_to_stories_section_c',
        denominator='section_session_count_c',
        dist='ratio', 
        desired_direction=NEGATIVE
    )

    spotlight_selected_ratio_c = Metric(
        col='recipients_selected_from_spotlight_per_session_cancelled',
        numerator='snaps_sent_to_spotlight_section_c',
        denominator='section_session_count_c',
        dist='ratio', 
        desired_direction=NEGATIVE
    )

    share_sheet_selected_ratio_c = Metric(
        col='recipients_selected_from_share_sheet_per_session_cancelled',
        numerator='snaps_sent_to_share_sheet_section_c',
        denominator='section_session_count_c',
        dist='ratio', 
        desired_direction=NEGATIVE
    )

    all_section_selected_ratio_c = Metric(
        col='recipients_selected_from_all_section_per_session_cancelled',
        numerator='snaps_sent_to_all_section_c',
        denominator='section_session_count_c',
        dist='ratio', 
        desired_direction=NEGATIVE
    )

    mt = MetricTable(
        sql="""
        SELECT
          PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
          ghost_user_id,
          sum(sessions_count) as section_session_count_c,
          sum(new_friends_selected) as snaps_sent_to_new_friends_section_c,
          sum(stories_selected) as snaps_sent_to_stories_section_c,
          sum(spotlight_selected) as snaps_sent_to_spotlight_section_c,
          sum(share_sheet_selected) as snaps_sent_to_share_sheet_section_c,
          sum(friends_selected) as snaps_sent_to_friends_section_c,
          sum(suggested_selected) as snaps_sent_to_suggested_section_c,
          sum(quick_add_selected) as snaps_sent_to_quick_add_section_c,
          sum(best_friends_selected) as snaps_sent_to_best_friends_section_c,
          sum(search_selected) as snaps_sent_to_search_section_c,
          sum(groups_selected) as snaps_sent_to_groups_section_c,
          sum(top_groups_selected) as snaps_sent_to_top_groups_section_c,
          sum(recents_selected) as snaps_sent_to_recents_section_c,
          sum(list_selected) as snaps_sent_to_list_section_c,
          sum(contextual_list_selected) as snaps_sent_to_contextual_list_section_c,
          sum(snap_stars_selected) as snaps_sent_to_snap_stars_section_c,
          sum(snap_back_selected) as snaps_sent_to_snap_back_section_c,
          sum(new_groups_selected) as snaps_sent_to_new_groups_section_c,
          sum(realtime_selected) as snaps_sent_to_realtime_section_c,
          sum(last_recipients_selected) as snaps_sent_to_last_recipients_section_c,
          sum(COALESCE(friends_selected,0)
              +COALESCE(recents_selected,0)
              +COALESCE(best_friends_selected,0)
              +COALESCE(search_selected,0)
              +COALESCE(groups_selected,0)
              +COALESCE(top_groups_selected,0)
              +COALESCE(last_recipients_selected,0)
              +COALESCE(list_selected,0)
              +COALESCE(contextual_list_selected,0)
              +COALESCE(snap_stars_selected,0)
              +COALESCE(snap_back_selected,0)
              +COALESCE(new_groups_selected,0)) as snaps_sent_to_all_section_c
        FROM
          {source_table}
        and status = 'CANCELLED'
        GROUP BY 1,2
        """.format(source_table=source_table),
        metrics=[
                snaps_sent_to_best_friends_section_c,
                #snaps_sent_to_suggested_section_c,
                snaps_sent_to_groups_section_c,
                snaps_sent_to_top_groups_section_c,
                snaps_sent_to_friends_section_c,
                snaps_sent_to_recents_section_c,
                snaps_sent_to_search_section_c,
                #snaps_sent_to_realtime_section_c,
                snaps_sent_to_last_recipients_section_c,
                snaps_sent_to_list_section_c,
                snaps_sent_to_contextual_list_section_c,
                snaps_sent_to_snap_stars_section_c,
                snaps_sent_to_snap_back_section_c,
                snaps_sent_to_new_groups_section_c,
                snaps_sent_to_all_section_c,
                snaps_sent_to_stories_section_c,
                snaps_sent_to_spotlight_section_c,
                snaps_sent_to_share_sheet_section_c,

                section_session_count_c,
                search_selected_ratio_c,
                #suggested_selected_ratio_c,
                best_friends_selected_ratio_c,
                friends_selected_ratio_c,
                group_selected_ratio_c,
                top_group_selected_ratio_c,
                recents_selected_ratio_c,
                #realtime_selected_ratio_c,
                last_recipients_selected_ratio_c,
                list_selected_ratio_c,
                contextual_list_selected_ratio_c,
                snap_stars_selected_ratio_c,
                snap_back_selected_ratio_c,
                new_groups_selected_ratio_c,
                all_section_selected_ratio_c,
                stories_selected_ratio_c,
                spotlight_selected_ratio_c,
                share_sheet_selected_ratio_c
                ],
        name="sendto_recipients_by_section_cancelled",
        bq_dialect="standard"
    )
    return mt

def snap_recipient_by_source(start_date, end_date):
    """
    Snap and snap recipients by sources.
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    source_table = """
    `sc-analytics.report_growth.direct_snap_send_by_user_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )


    snaps_total = Metric(
        'snap_send_total',
        'Snaps Send From All Sources',
        dist='cont',
        daily=True,
        cumulative=True
    )

    unique_recipients_total = Metric(
        'unique_recipients_total',
        'Unique Recipients From All Sources',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    unique_recipients_per_snap_total = Metric(
        col='unique_recipients_from_all_sources_per_snap',
        numerator='unique_recipients_total',
        denominator='snap_send_total',
        dist='ratio'
    )

    non_unique_recipients_total = Metric(
        'non_unique_recipients_total',
        'Non-Unique Recipients From All Sources',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    non_unique_recipients_per_snap_total = Metric(
        col='non_unique_recipients_from_all_sources_per_snap',
        numerator='non_unique_recipients_total',
        denominator='snap_send_total',
        dist='ratio'
    )

    recipients_from_camera = Metric(
        'recipients_from_camera',
        'Recipients From Camera',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snaps_from_camera = Metric(
        'snaps_from_camera',
        'Snaps Send From Camera',
        dist='cont',
        daily=True,
        cumulative=True
    )

    recipients_per_snap_from_camera = Metric(
        col='recipients_from_camera_per_snap',
        numerator='recipients_from_camera',
        denominator='snaps_from_camera',
        dist='ratio'
    )

    recipients_from_gallery = Metric(
        'recipients_from_gallery',
        'Recipients From Gallery',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snaps_from_gallery = Metric(
        'snaps_from_gallery',
        'Snaps Send From Gallery',
        dist='cont',
        daily=True,
        cumulative=True
    )

    recipients_per_snap_from_gallery = Metric(
        col='recipients_from_gallery_per_snap',
        numerator='recipients_from_gallery',
        denominator='snaps_from_gallery',
        dist='ratio'
    )

    recipients_from_feed = Metric(
        'recipients_from_feed',
        'Recipients From Feed DTTR',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snaps_from_feed = Metric(
        'snaps_from_feed',
        'Snaps Send From Feed DTTR',
        dist='cont',
        daily=True,
        cumulative=True
    )

    recipients_per_snap_from_feed = Metric(
        col='recipients_from_feed_dttr_per_snap',
        numerator='recipients_from_feed',
        denominator='snaps_from_feed',
        dist='ratio'
    )

    recipients_from_feed_reply = Metric(
        'recipients_from_feed_reply',
        'Recipients From Reply',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snaps_from_feed_reply = Metric(
        'snaps_from_feed_reply',
        'Snaps Send From Reply',
        dist='cont',
        daily=True,
        cumulative=True
    )

    recipients_per_snap_from_feed_reply = Metric(
        col='recipients_from_reply_per_snap',
        numerator='recipients_from_feed_reply',
        denominator='snaps_from_feed_reply',
        dist='ratio'
    )

    recipients_from_chat = Metric(
        'recipients_from_chat',
        'Recipients From Chat',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snaps_from_chat = Metric(
        'snaps_from_chat',
        'Snaps Send From Chat',
        dist='cont',
        daily=True,
        cumulative=True
    )

    recipients_per_snap_from_chat = Metric(
        col='recipients_from_chat_per_snap',
        numerator='recipients_from_chat',
        denominator='snaps_from_chat',
        dist='ratio'
    )

    recipients_from_camera_roll = Metric(
        'recipients_from_camera_roll',
        'Recipients From Camera Roll',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snaps_from_camera_roll = Metric(
        'snaps_from_camera_roll',
        'Snaps Send From Camera Roll',
        dist='cont',
        daily=True,
        cumulative=True
    )

    recipients_per_snap_from_camera_roll = Metric(
        col='recipients_from_camera_roll_per_snap',
        numerator='recipients_from_camera_roll',
        denominator='snaps_from_camera_roll',
        dist='ratio'
    )

    recipients_from_mini_profile = Metric(
        'recipients_from_mini_profile',
        'Recipients From Mini Profile',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snaps_from_mini_profile = Metric(
        'snaps_from_mini_profile',
        'Snaps Send From Mini Profile',
        dist='cont',
        daily=True,
        cumulative=True
    )

    recipients_per_snap_from_mini_profile = Metric(
        col='recipients_from_mini_profile_per_snap',
        numerator='recipients_from_mini_profile',
        denominator='snaps_from_mini_profile',
        dist='ratio'
    )

    recipients_from_others = Metric(
        'recipients_from_others',
        'Recipients From Others',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snaps_from_others = Metric(
        'snaps_from_others',
        'Snaps Send From Others',
        dist='cont',
        daily=True,
        cumulative=True
    )

    recipients_per_snap_from_others = Metric(
        col='recipients_from_others_per_snap',
        numerator='recipients_from_others',
        denominator='snaps_from_others',
        dist='ratio'
    )

    non_unique_recipients_0_5 = Metric(
        'non_unique_recipients_0_5',
        'Non-Unique Recipients of Users with Daily 1:1 Recipients [0, 5]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    non_unique_recipients_6_20 = Metric(
        'non_unique_recipients_6_20',
        'Non-Unique Recipients of Users with Daily 1:1 Recipients [6, 20]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    non_unique_recipients_21_50 = Metric(
        'non_unique_recipients_21_50',
        'Non-Unique Recipients of Users with Daily 1:1 Recipients [21, 50]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    non_unique_recipients_51_200 = Metric(
        'non_unique_recipients_51_200',
        'Non-Unique Recipients of Users with Daily 1:1 Recipients [51, 200]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    non_unique_recipients_200_2000 = Metric(
        'non_unique_recipients_200_2000',
        'Non-Unique Recipients of Users with Daily 1:1 Recipients [201, 2k]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    non_unique_recipients_2000_25000 = Metric(
        'non_unique_recipients_2000_25000',
        'Non-Unique Recipients of Users with Daily 1:1 Recipients (2k, 25k]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    non_unique_recipients_25000_ = Metric(
        'non_unique_recipients_25000_',
        'Non-Unique Recipients of Users with Daily 1:1 Recipients (25k,]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snap_recipients_0_5_active_days = Metric(
        'snap_recipients_0_5_active_days',
        'Active Days of Users with Daily 1:1 Recipients [0, 5]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snap_recipients_6_20_active_days = Metric(
        'snap_recipients_6_20_active_days',
        'Active Days of Users with Daily 1:1 Recipients [6, 20]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snap_recipients_21_50_active_days = Metric(
        'snap_recipients_21_50_active_days',
        'Active Days of Users with Daily 1:1 Recipients [21, 50]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snap_recipients_51_200_active_days = Metric(
        'snap_recipients_51_200_active_days',
        'Active Days of Users with Daily 1:1 Recipients [51, 200]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snap_recipients_200_2000_active_days = Metric(
        'snap_recipients_200_2000_active_days',
        'Active Days of Users with Daily 1:1 Recipients [201, 2k]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snap_recipients_2000_25000_active_days = Metric(
        'snap_recipients_2000_25000_active_days',
        'Active Days of Users with Daily 1:1 Recipients (2k, 25k]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snap_recipients_25000_active_days = Metric(
        'snap_recipients_25000_active_days',
        'Active Days of Users with Daily 1:1 Recipients (25k,]',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    snap_recipients_0_5_uu = Metric(
        'snap_recipients_0_5_uu',
        'UU with Daily 1:1 Recipients [0, 5]',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    snap_recipients_6_20_uu = Metric(
        'snap_recipients_6_20_uu',
        'UU with Daily 1:1 Recipients [6, 20]',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    snap_recipients_21_50_uu = Metric(
        'snap_recipients_21_50_uu',
        'UU with Daily 1:1 Recipients [21, 50]',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    snap_recipients_51_200_uu = Metric(
        'snap_recipients_51_200_uu',
        'UU with Daily 1:1 Recipients [51, 200]',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    snap_recipients_200_2000_uu = Metric(
        'snap_recipients_200_2000_uu',
        'UU with Daily 1:1 Recipients [201, 2k]',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    snap_recipients_2000_25000_uu = Metric(
        'snap_recipients_2000_25000_uu',
        'UU with Daily 1:1 Recipients (2k, 25k]',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    snap_recipients_25000_uu = Metric(
        'snap_recipients_25000_uu',
        'UU with Daily 1:1 Recipients (25k,]',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    num_of_non_unique_recipients_cap_20000 = Metric(
        'num_of_non_unique_recipients_cap_20000',
        'Non-Unique Recipients from All Sources (cap @20k)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    num_of_non_unique_recipients_cap_25000 = Metric(
        'num_of_non_unique_recipients_cap_25000',
        'Non-Unique Recipients from All Sources (cap @25k)',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    num_of_non_unique_recipients_cap_30000 = Metric(
        'num_of_non_unique_recipients_cap_30000',
        'Non-Unique Recipients from All Sources (cap @30k)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    user_recipient_following_count = Metric(
        'user_recipient_following_count',
        'Recipients (Following)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    user_recipient_follower_count = Metric(
        'user_recipient_follower_count',
        'Recipients (Follower)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    user_recipient_friend_count = Metric(
        'user_recipient_friend_count',
        'Recipients (Friend)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    user_recipient_non_friend_count = Metric(
        'user_recipient_non_friend_count',
        'Recipients (Non-Friend)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    user_recipient_snap_star_count = Metric(
        'user_recipient_snap_star_count',
        'Recipients (Snap Star)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    user_recipient_public_account_count = Metric(
        'user_recipient_public_account_count',
        'Recipients (Public Account)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    user_recipient_following_uu = Metric(
        'user_recipient_following_uu',
        'UU with Recipients (Following)',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    user_recipient_follower_uu = Metric(
        'user_recipient_follower_uu',
        'UU with Recipients (Follower)',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    user_recipient_friend_uu = Metric(
        'user_recipient_friend_uu',
        'UU with Recipients (Friend)',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    user_recipient_non_friend_uu = Metric(
        'user_recipient_non_friend_uu',
        'UU with Recipients (Non-Friends)',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    user_recipient_snap_star_uu = Metric(
        'user_recipient_snap_star_uu',
        'UU with Recipients (Snap Star)',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    user_recipient_public_account_uu = Metric(
        'user_recipient_public_account_uu',
        'UU with Recipients (Public Account)',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    mt = MetricTable(
        sql="""
        SELECT
          PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
          ghost_user_id AS ghost_user_id,
          SUM(num_of_non_unique_recipients) AS non_unique_recipients_total,
          SUM(num_of_unique_recipients) AS unique_recipients_total,
          SUM(num_of_direct_snaps) AS snap_send_total,
          SUM(CASE WHEN source = 'CAMERA' THEN num_of_unique_recipients ELSE 0 END) AS recipients_from_camera,
          SUM(CASE WHEN source = 'CAMERA' THEN num_of_direct_snaps ELSE 0 END) AS snaps_from_camera,
          SUM(CASE WHEN source = 'GALLERY' THEN num_of_unique_recipients ELSE 0 END) AS recipients_from_gallery,
          SUM(CASE WHEN source = 'GALLERY' THEN num_of_direct_snaps ELSE 0 END) AS snaps_from_gallery,
          SUM(CASE WHEN source = 'FEED' THEN num_of_unique_recipients ELSE 0 END) AS recipients_from_feed,
          SUM(CASE WHEN source = 'FEED' THEN num_of_direct_snaps ELSE 0 END) AS snaps_from_feed,
          SUM(CASE WHEN source = 'FEED_SNAP_REPLY' THEN num_of_unique_recipients ELSE 0 END) AS recipients_from_feed_reply,
          SUM(CASE WHEN source = 'FEED_SNAP_REPLY' THEN num_of_direct_snaps ELSE 0 END) AS snaps_from_feed_reply,
          SUM(CASE WHEN source = 'IN_CHAT' THEN num_of_unique_recipients ELSE 0 END) AS recipients_from_chat,
          SUM(CASE WHEN source = 'IN_CHAT' THEN num_of_direct_snaps ELSE 0 END) AS snaps_from_chat,
          SUM(CASE WHEN source = 'CAMERA_ROLL' THEN num_of_unique_recipients ELSE 0 END) AS recipients_from_camera_roll,
          SUM(CASE WHEN source = 'CAMERA_ROLL' THEN num_of_direct_snaps ELSE 0 END) AS snaps_from_camera_roll,
          SUM(CASE WHEN source = 'MINI_PROFILE' THEN num_of_unique_recipients ELSE 0 END) AS recipients_from_mini_profile,
          SUM(CASE WHEN source = 'MINI_PROFILE' THEN num_of_direct_snaps ELSE 0 END) AS snaps_from_mini_profile,
          SUM(CASE WHEN coalesce(source, 'OTHERS') not IN ('CAMERA',  'FEED',  'FEED_SNAP_REPLY',  'MINI_PROFILE',  'IN_CHAT',  'CAMERA_ROLL') THEN num_of_unique_recipients ELSE 0 END) AS recipients_from_others,
          SUM(CASE WHEN coalesce(source, 'OTHERS') not IN ('CAMERA',  'FEED',  'FEED_SNAP_REPLY',  'MINI_PROFILE',  'IN_CHAT',  'CAMERA_ROLL') THEN num_of_direct_snaps ELSE 0 END) AS snaps_from_others,


          if(SUM(num_of_1_1_snap_recipients) BETWEEN 0 and 5, SUM(num_of_non_unique_recipients), 0) AS non_unique_recipients_0_5,
          if(SUM(num_of_1_1_snap_recipients) BETWEEN 6 and 20, SUM(num_of_non_unique_recipients), 0) AS non_unique_recipients_6_20,
          if(SUM(num_of_1_1_snap_recipients) BETWEEN 21 and 50, SUM(num_of_non_unique_recipients), 0) AS non_unique_recipients_21_50,
          if(SUM(num_of_1_1_snap_recipients) BETWEEN 51 and 200, SUM(num_of_non_unique_recipients), 0) AS non_unique_recipients_51_200,
          if(SUM(num_of_1_1_snap_recipients) BETWEEN 201 and 2000, SUM(num_of_non_unique_recipients), 0) AS non_unique_recipients_200_2000,
          if(SUM(num_of_1_1_snap_recipients) BETWEEN 2001 and 25000, SUM(num_of_non_unique_recipients), 0) AS non_unique_recipients_2000_25000,
          if(SUM(num_of_1_1_snap_recipients) BETWEEN 25001 and 10000000000, SUM(num_of_non_unique_recipients), 0) AS non_unique_recipients_25000_,

          if(SUM(num_of_1_1_snap_recipients) BETWEEN 0 and 5, 1, 0) AS snap_recipients_0_5_active_days,
          if(SUM(num_of_1_1_snap_recipients) BETWEEN 6 and 20, 1, 0) AS snap_recipients_6_20_active_days,
          if(SUM(num_of_1_1_snap_recipients) BETWEEN 21 and 50, 1, 0) AS snap_recipients_21_50_active_days,
          if(SUM(num_of_1_1_snap_recipients) BETWEEN 51 and 200, 1, 0) AS snap_recipients_51_200_active_days,
          if(SUM(num_of_1_1_snap_recipients) BETWEEN 201 and 2000, 1, 0) AS snap_recipients_200_2000_active_days,
          if(SUM(num_of_1_1_snap_recipients) BETWEEN 2001 and 25000, 1, 0) AS snap_recipients_2000_25000_active_days,
          if(SUM(num_of_1_1_snap_recipients) BETWEEN 25001 and 10000000000, 1, 0) AS snap_recipients_25000_active_days,

          if(SUM(num_of_1_1_snap_recipients) BETWEEN 0 and 5, 1, 0) AS snap_recipients_0_5_uu,
          if(SUM(num_of_1_1_snap_recipients) BETWEEN 6 and 20, 1, 0) AS snap_recipients_6_20_uu,
          if(SUM(num_of_1_1_snap_recipients) BETWEEN 21 and 50, 1, 0) AS snap_recipients_21_50_uu,
          if(SUM(num_of_1_1_snap_recipients) BETWEEN 51 and 200, 1, 0) AS snap_recipients_51_200_uu,
          if(SUM(num_of_1_1_snap_recipients) BETWEEN 201 and 2000, 1, 0) AS snap_recipients_200_2000_uu,
          if(SUM(num_of_1_1_snap_recipients) BETWEEN 2001 and 25000, 1, 0) AS snap_recipients_2000_25000_uu,
          if(SUM(num_of_1_1_snap_recipients) BETWEEN 25001 and 10000000000, 1, 0) AS snap_recipients_25000_uu,

          if(SUM(num_of_non_unique_recipients)>20000 , 20000, SUM(num_of_non_unique_recipients)) AS num_of_non_unique_recipients_cap_20000,
          if(SUM(num_of_non_unique_recipients)>25000 , 25000, SUM(num_of_non_unique_recipients)) AS num_of_non_unique_recipients_cap_25000,
          if(SUM(num_of_non_unique_recipients)>30000 , 30000, SUM(num_of_non_unique_recipients)) AS num_of_non_unique_recipients_cap_30000,

          SUM(user_recipient_following_count) AS user_recipient_following_count,
          SUM(user_recipient_follower_count) AS user_recipient_follower_count,
          SUM(user_recipient_friend_count) AS user_recipient_friend_count,
          SUM(user_recipient_non_friend_count) AS user_recipient_non_friend_count,
          SUM(user_recipient_snap_star_count) AS user_recipient_snap_star_count,
          SUM(user_recipient_public_account_count) AS user_recipient_public_account_count,

          if(SUM(user_recipient_following_count)>0,1,0) AS user_recipient_following_uu,
          if(SUM(user_recipient_follower_count)>0,1,0) AS user_recipient_follower_uu,
          if(SUM(user_recipient_friend_count)>0,1,0) AS user_recipient_friend_uu,
          if(SUM(user_recipient_non_friend_count)>0,1,0) AS user_recipient_non_friend_uu,
          if(SUM(user_recipient_snap_star_count)>0,1,0) AS user_recipient_snap_star_uu,
          if(SUM(user_recipient_public_account_count)>0,1,0) AS user_recipient_public_account_uu,

          FROM 
          (
            select 
            _table_suffix,
            ghost_user_id,
            source, 
            CAST(num_of_non_unique_recipients AS FLOAT64) AS num_of_non_unique_recipients,
            CAST(num_of_unique_recipients AS FLOAT64) AS num_of_unique_recipients,
            CAST(num_of_direct_snaps AS FLOAT64) AS num_of_direct_snaps,
            CAST(num_of_1_1_snap_recipients AS FLOAT64) AS num_of_1_1_snap_recipients,
            CAST(user_recipient_following_count AS FLOAT64) AS user_recipient_following_count,
            CAST(user_recipient_follower_count AS FLOAT64) AS user_recipient_follower_count,
            CAST(user_recipient_friend_count AS FLOAT64) AS user_recipient_friend_count,
            CAST(user_recipient_non_friend_count AS FLOAT64) AS user_recipient_non_friend_count,
            CAST(user_recipient_snap_star_count AS FLOAT64) AS user_recipient_snap_star_count,
            CAST(user_recipient_public_account_count AS FLOAT64) AS user_recipient_public_account_count,
            from {source_table}
          )
          GROUP BY 1, 2
        """.format(source_table=source_table),
        metrics=[
                snaps_total,
                non_unique_recipients_total,
                non_unique_recipients_per_snap_total,
                unique_recipients_total,
                unique_recipients_per_snap_total,
                recipients_from_camera,
                snaps_from_camera,
                recipients_per_snap_from_camera,
                recipients_from_gallery,
                snaps_from_gallery,
                recipients_per_snap_from_gallery,
                recipients_from_feed,
                snaps_from_feed,
                recipients_per_snap_from_feed,
                recipients_from_feed_reply,
                snaps_from_feed_reply,
                recipients_per_snap_from_feed_reply,
                recipients_from_chat,
                snaps_from_chat,
                recipients_per_snap_from_chat,
                #recipients_from_camera_roll,
                #snaps_from_camera_roll,
                #recipients_per_snap_from_camera_roll,
                recipients_from_mini_profile,
                snaps_from_mini_profile,
                recipients_per_snap_from_mini_profile,
                recipients_from_others,
                snaps_from_others,
                recipients_per_snap_from_others,

                user_recipient_following_count,
                user_recipient_follower_count,
                user_recipient_friend_count,
                user_recipient_non_friend_count,
                user_recipient_snap_star_count,
                user_recipient_public_account_count,

                user_recipient_following_uu,
                user_recipient_follower_uu,
                user_recipient_friend_uu,
                user_recipient_non_friend_uu,
                user_recipient_snap_star_uu,
                user_recipient_public_account_uu,

                num_of_non_unique_recipients_cap_20000,
                num_of_non_unique_recipients_cap_25000,
                num_of_non_unique_recipients_cap_30000,

                non_unique_recipients_0_5,
                non_unique_recipients_6_20,
                non_unique_recipients_21_50,
                non_unique_recipients_51_200,
                non_unique_recipients_200_2000,
                non_unique_recipients_2000_25000,
                non_unique_recipients_25000_,

                snap_recipients_0_5_active_days,
                snap_recipients_6_20_active_days,
                snap_recipients_21_50_active_days,
                snap_recipients_51_200_active_days,
                snap_recipients_200_2000_active_days,
                snap_recipients_2000_25000_active_days,
                snap_recipients_25000_active_days,

                snap_recipients_0_5_uu,
                snap_recipients_6_20_uu,
                snap_recipients_21_50_uu,
                snap_recipients_51_200_uu,
                snap_recipients_200_2000_uu,
                snap_recipients_2000_25000_uu,
                snap_recipients_25000_uu,

                ],
        name="snap_recipient_by_source",
        bq_dialect="standard"
    )
    return mt

def sendto_session_by_source(start_date, end_date):
    """
    Successful SendTo sessions by sources.
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    source_table = """
    `sc-analytics.report_growth.page_sendto_session_end_by_user_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    CAMERA_success_sessions_count = Metric( 'CAMERA_success_sessions_count',   'SendTo Success Sessions CAMERA',  dist='cont',   daily=True, cumulative=True,  )
    GALLERY_success_sessions_count  = Metric( 'GALLERY_success_sessions_count',  'SendTo Success Sessions GALLERY',  dist='cont',   daily=True, cumulative=True,  )
    FEED_success_sessions_count = Metric( 'FEED_success_sessions_count',   'SendTo Success Sessions FEED',  dist='cont',   daily=True, cumulative=True,  )
    CAMERA_ROLL_success_sessions_count  = Metric( 'CAMERA_ROLL_success_sessions_count',  'SendTo Success Sessions CAMERA ROLL',  dist='cont',   daily=True, cumulative=True,  )
    IN_CHAT_success_sessions_count  = Metric( 'IN_CHAT_success_sessions_count',  'SendTo Success Sessions CHAT',  dist='cont',   daily=True, cumulative=True,  )
    SHARE_success_sessions_count  = Metric( 'SHARE_success_sessions_count',  'SendTo Success Sessions SHARE',  dist='cont',   daily=True, cumulative=True,  )
    DISCOVER_success_sessions_count = Metric( 'DISCOVER_success_sessions_count',   'SendTo Success Sessions DISCOVER',  dist='cont',   daily=True, cumulative=True,  )
    FEED_SNAP_REPLY_success_sessions_count  = Metric( 'FEED_SNAP_REPLY_success_sessions_count',  'SendTo Success Sessions FEED REPLY',  dist='cont',   daily=True, cumulative=True,  )
    OTHERS_success_sessions_count = Metric( 'OTHERS_success_sessions_count',   'SendTo Success Sessions OTHERS',  dist='cont',   daily=True, cumulative=True,  )

    CAMERA_success_sessions_UU = Metric( 'CAMERA_success_sessions_UU',   'SendTo Success UU CAMERA',  dist='bin',   daily=True, cumulative=True,  )
    GALLERY_success_sessions_UU  = Metric( 'GALLERY_success_sessions_UU',  'SendTo Success UU GALLERY',  dist='bin',   daily=True, cumulative=True,  )
    FEED_success_sessions_UU = Metric( 'FEED_success_sessions_UU',   'SendTo Success UU FEED',  dist='bin',   daily=True, cumulative=True,  )
    CAMERA_ROLL_success_sessions_UU  = Metric( 'CAMERA_ROLL_success_sessions_UU',  'SendTo Success UU CAMERA ROLL',  dist='bin',   daily=True, cumulative=True,  )
    IN_CHAT_success_sessions_UU  = Metric( 'IN_CHAT_success_sessions_UU',  'SendTo Success UU CHAT',  dist='bin',   daily=True, cumulative=True,  )
    SHARE_success_sessions_UU  = Metric( 'SHARE_success_sessions_UU',  'SendTo Success UU SHARE',  dist='bin',   daily=True, cumulative=True,  )
    DISCOVER_success_sessions_UU = Metric( 'DISCOVER_success_sessions_UU',   'SendTo Success UU DISCOVER',  dist='bin',   daily=True, cumulative=True,  )
    FEED_SNAP_REPLY_success_sessions_UU  = Metric( 'FEED_SNAP_REPLY_success_sessions_UU',  'SendTo Success UU FEED REPLY',  dist='bin',   daily=True, cumulative=True,  )
    OTHERS_success_sessions_UU = Metric( 'OTHERS_success_sessions_UU',   'SendTo Success UU OTHERS',  dist='bin',   daily=True, cumulative=True,  )

    CAMERA_success_sessions_active_day = Metric( 'CAMERA_success_sessions_active_day',   'SendTo Success Active Day CAMERA',  dist='cont',   daily=True, cumulative=True,  )
    GALLERY_success_sessions_active_day  = Metric( 'GALLERY_success_sessions_active_day',  'SendTo Success Active Day GALLERY',  dist='cont',   daily=True, cumulative=True,  )
    FEED_success_sessions_active_day = Metric( 'FEED_success_sessions_active_day',   'SendTo Success Active Day FEED',  dist='cont',   daily=True, cumulative=True,  )
    CAMERA_ROLL_success_sessions_active_day  = Metric( 'CAMERA_ROLL_success_sessions_active_day',  'SendTo Success Active Day CAMERA ROLL',  dist='cont',   daily=True, cumulative=True,  )
    IN_CHAT_success_sessions_active_day  = Metric( 'IN_CHAT_success_sessions_active_day',  'SendTo Success Active Day CHAT',  dist='cont',   daily=True, cumulative=True,  )
    SHARE_success_sessions_active_day  = Metric( 'SHARE_success_sessions_active_day',  'SendTo Success Active Day SHARE',  dist='cont',   daily=True, cumulative=True,  )
    DISCOVER_success_sessions_active_day = Metric( 'DISCOVER_success_sessions_active_day',   'SendTo Success Active Day DISCOVER',  dist='cont',   daily=True, cumulative=True,  )
    FEED_SNAP_REPLY_success_sessions_active_day  = Metric( 'FEED_SNAP_REPLY_success_sessions_active_day',  'SendTo Success Active Day FEED REPLY',  dist='cont',   daily=True, cumulative=True,  )
    OTHERS_success_sessions_active_day = Metric( 'OTHERS_success_sessions_active_day',   'SendTo Success Active Day OTHERS',  dist='cont',   daily=True, cumulative=True,  )


    mt = MetricTable(
        sql="""
        SELECT
          PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
          ghost_user_id AS ghost_user_id,
          SUM(IF(source = 'CAMERA',sessions_count,0)) AS CAMERA_success_sessions_count,
          SUM(IF(source = 'GALLERY',sessions_count,0)) AS GALLERY_success_sessions_count,
          SUM(IF(source = 'FEED',sessions_count,0)) AS FEED_success_sessions_count,
          SUM(IF(source = 'CAMERA_ROLL',sessions_count,0)) AS CAMERA_ROLL_success_sessions_count,
          SUM(IF(source = 'IN_CHAT',sessions_count,0)) AS IN_CHAT_success_sessions_count,
          SUM(IF(source = 'SHARE',sessions_count,0)) AS SHARE_success_sessions_count,
          SUM(IF(source = 'DISCOVER',sessions_count,0)) AS DISCOVER_success_sessions_count,
          SUM(IF(source = 'FEED_SNAP_REPLY',sessions_count,0)) AS FEED_SNAP_REPLY_success_sessions_count,
          SUM(IF(COALESCE(source,'OTHERS') NOT IN ('CAMERA','GALLERY','FEED','CAMERA_ROLL','IN_CHAT','SHARE','DISCOVER','FEED_SNAP_REPLY') ,sessions_count,0))
          AS OTHERS_success_sessions_count,

          MAX(IF(source = 'CAMERA',1,0)) AS CAMERA_success_sessions_UU,
          MAX(IF(source = 'GALLERY',1,0)) AS GALLERY_success_sessions_UU,
          MAX(IF(source = 'FEED',1,0)) AS FEED_success_sessions_UU,
          MAX(IF(source = 'CAMERA_ROLL',1,0)) AS CAMERA_ROLL_success_sessions_UU,
          MAX(IF(source = 'IN_CHAT',1,0)) AS IN_CHAT_success_sessions_UU,
          MAX(IF(source = 'SHARE',1,0)) AS SHARE_success_sessions_UU,
          MAX(IF(source = 'DISCOVER',1,0)) AS DISCOVER_success_sessions_UU,
          MAX(IF(source = 'FEED_SNAP_REPLY',1,0)) AS FEED_SNAP_REPLY_success_sessions_UU,
          MAX(IF(COALESCE(source,'OTHERS') NOT IN ('CAMERA','GALLERY','FEED','CAMERA_ROLL','IN_CHAT','SHARE','DISCOVER','FEED_SNAP_REPLY') ,1,0))
          AS OTHERS_success_sessions_UU,

          MAX(IF(source = 'CAMERA',1,0)) AS CAMERA_success_sessions_active_day,
          MAX(IF(source = 'GALLERY',1,0)) AS GALLERY_success_sessions_active_day,
          MAX(IF(source = 'FEED',1,0)) AS FEED_success_sessions_active_day,
          MAX(IF(source = 'CAMERA_ROLL',1,0)) AS CAMERA_ROLL_success_sessions_active_day,
          MAX(IF(source = 'IN_CHAT',1,0)) AS IN_CHAT_success_sessions_active_day,
          MAX(IF(source = 'SHARE',1,0)) AS SHARE_success_sessions_active_day,
          MAX(IF(source = 'DISCOVER',1,0)) AS DISCOVER_success_sessions_active_day,
          MAX(IF(source = 'FEED_SNAP_REPLY',1,0)) AS FEED_SNAP_REPLY_success_sessions_active_day,
          MAX(IF(COALESCE(source,'OTHERS') NOT IN ('CAMERA','GALLERY','FEED','CAMERA_ROLL','IN_CHAT','SHARE','DISCOVER','FEED_SNAP_REPLY') ,1,0))
          AS OTHERS_success_sessions_active_day,

          FROM {source_table}
          and
          status = 'SUCCESS'
          GROUP BY
          1,2
        """.format(source_table=source_table),
        metrics=[
                CAMERA_success_sessions_count,
                GALLERY_success_sessions_count,
                FEED_success_sessions_count,
                CAMERA_ROLL_success_sessions_count,
                IN_CHAT_success_sessions_count,
                SHARE_success_sessions_count,
                DISCOVER_success_sessions_count,
                FEED_SNAP_REPLY_success_sessions_count,
                OTHERS_success_sessions_count,

                CAMERA_success_sessions_UU,
                GALLERY_success_sessions_UU,
                FEED_success_sessions_UU,
                CAMERA_ROLL_success_sessions_UU,
                IN_CHAT_success_sessions_UU,
                SHARE_success_sessions_UU,
                DISCOVER_success_sessions_UU,
                FEED_SNAP_REPLY_success_sessions_UU,
                OTHERS_success_sessions_UU,

                CAMERA_success_sessions_active_day,
                GALLERY_success_sessions_active_day,
                FEED_success_sessions_active_day,
                CAMERA_ROLL_success_sessions_active_day,
                IN_CHAT_success_sessions_active_day,
                SHARE_success_sessions_active_day,
                DISCOVER_success_sessions_active_day,
                FEED_SNAP_REPLY_success_sessions_active_day,
                OTHERS_success_sessions_active_day
                ],
        name="sendto_session_by_source",
        bq_dialect="standard"
    )
    return mt

def sendto_select_all_usage(start_date, end_date):
    """
    SendTo Select All button usage
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    source_table = """
    `sc-analytics.report_growth.page_sendto_session_end_by_user_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    page_sendto_lists_select_all_action_count = Metric(
        'page_sendto_lists_select_all_action_count',
        'Select All Action Count (Shortcut Lists)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    page_sendto_best_friends_select_all_action_count = Metric(
        'page_sendto_best_friends_select_all_action_count',
        'Select All Action Count (Best Friends)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    page_sendto_best_friends_deselect_all_action_count = Metric(
        'page_sendto_best_friends_deselect_all_action_count',
        'Select All Deselect Action Count (Best Friends)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    page_sendto_best_friends_deselect_from_select_all_action_count = Metric(
        'page_sendto_best_friends_deselect_from_select_all_action_count',
        'Individual Deselect From Select All Action Count (Best Friends)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    mt = MetricTable(
        sql="""
        SELECT
          PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
          ghost_user_id,
          SUM(lists_select_all_count) AS page_sendto_lists_select_all_action_count,
          SUM(best_friends_select_all_action_count) AS page_sendto_best_friends_select_all_action_count,
          SUM(best_friends_deselect_all_action_count) AS page_sendto_best_friends_deselect_all_action_count,
          SUM(if(best_friends_select_all_action_count > 0, best_friends_available - best_friends_selected, 0)) as page_sendto_best_friends_deselect_from_select_all_action_count,
        FROM
          {source_table}
        GROUP BY 1,2
        """.format(source_table=source_table),
        metrics=[
            page_sendto_lists_select_all_action_count,
            page_sendto_best_friends_select_all_action_count,
            page_sendto_best_friends_deselect_all_action_count,
            page_sendto_best_friends_deselect_from_select_all_action_count,
                ],
        name="sendto_select_all_usage",
        bq_dialect="standard"
    )
    return mt

def sendto_session_time(start_date, end_date):
    """
    Session Time per SendTo session.
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    source_table = """
    `sc-analytics.report_growth.page_sendto_session_end_by_user_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    session_time = Metric(
        'session_time',
        'Session Time',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    time_session_count = Metric(
        'time_session_count',
        'Time Session Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    time_per_session_session_weighted = Metric(
        col='time_per_session',
        numerator='session_time',
        denominator='time_session_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    success_session_time = Metric(
        'success_session_time',
        'Success Session Time',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    success_time_session_count = Metric(
        'success_time_session_count',
        'Success Time Session Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    time_per_success_session = Metric(
        col='time_per_success_session',
        numerator='success_session_time',
        denominator='success_time_session_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    success_per_session = Metric(
        col='time_session_success_rate',
        numerator='success_time_session_count',
        denominator='time_session_count',
        dist='ratio'
    )

    sessions_active_day = Metric(
        'sessions_active_day',
        'Session Active Day',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    sessions_UU = Metric(
        'sessions_UU',
        'Session UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    success_sessions_active_day = Metric(
        'success_sessions_active_day',
        'Success Session Active Day',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    success_sessions_UU = Metric(
        'success_sessions_UU',
        'Success Session UU',
        dist='bin',
        daily=True,
        cumulative=True,
    )

    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        ghost_user_id AS ghost_user_id,
        MAX(1) AS sessions_active_day,
        MAX(1) AS sessions_UU,
        MAX(IF(status = 'SUCCESS',1,0)) AS success_sessions_active_day,
        MAX(IF(status = 'SUCCESS',1,0)) AS success_sessions_UU,

        SUM(sessions_count) AS time_session_count,
        SUM(sendto_session_time_sec) AS session_time,
        SUM(IF(status = 'SUCCESS',sessions_count,0)) AS success_time_session_count,
        SUM(IF(status = 'SUCCESS',sendto_session_time_sec,0)) AS success_session_time

        FROM {source_table}
        GROUP BY ts, ghost_user_id
        """.format(source_table=source_table,
                  ),
        metrics=[
                sessions_UU,
                sessions_active_day,
                time_session_count,
                session_time,
                time_per_session_session_weighted,
                success_sessions_UU,
                success_sessions_active_day,
                success_time_session_count,
                success_session_time,
                time_per_success_session,
                success_per_session
                ],
        name="sendto_session_time",
        bq_dialect="standard"
    )
    return mt

def sendto_time_per_cell_selected(start_date, end_date):
    """
    Time per recipient cell selected. A group is considered 1 cell.
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    source_table = """
    `sc-analytics.report_growth.stg_usr_sessionized_sendto_time_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    total_cell_selected = Metric(
        'cell_selected',
        'Recipient Cells Selected (Users + Groups)',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    total_selected_time = Metric(
        'selected_time',
        'Recipient Cells Selected Time',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    time_per_cell = Metric(
        col='time_per_recipient_cell_selected',
        numerator='selected_time',
        denominator='cell_selected',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    total_recipients_selected = Metric(
        'num_of_unique_recipients_selected',
        'Unique Recipients Selected',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    time_per_recipients = Metric(
        col='time_per_unique_recipients_selected',
        numerator='selected_time',
        denominator='num_of_unique_recipients_selected',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    bidirectional_communication_sendto_session_time_sec = Metric(
        'bidirectional_communication_sendto_session_time_sec',
        'Bidirect Comm Selected Time',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    bidirectional_communication_selected = Metric(
        'bidirectional_communication_selected',
        'Bidirect Comm Selected',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    time_per_bidirectional_communication = Metric(
        col='time_per_bidirect_comm_selected',
        numerator='bidirectional_communication_sendto_session_time_sec',
        denominator='bidirectional_communication_selected',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    mt = MetricTable(
        sql="""
        SELECT
          PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
          ghost_user_id AS ghost_user_id,
          SUM(num_of_unique_recipients) as num_of_unique_recipients_selected,
          SUM(COALESCE(num_of_1_1_snap_recipients,0)) + SUM(COALESCE(num_of_groups_selected,0)) as cell_selected,
          SUM(sendto_session_time_sec) as selected_time,
          SUM(bidirectional_communication_sendto_session_time_sec) as bidirectional_communication_sendto_session_time_sec,
          SUM(COALESCE(bidirectional_communication_friend,0)) + SUM(COALESCE(bidirectional_communication_group,0)) as bidirectional_communication_selected,
        FROM
          {source_table}
        and source IN ('CAMERA')
        GROUP BY 1,2
        """.format(source_table=source_table
                  ),
        metrics=[total_cell_selected,
                 total_selected_time,
                 time_per_cell,
                 total_recipients_selected,
                 time_per_recipients,
                 bidirectional_communication_sendto_session_time_sec,
                 bidirectional_communication_selected,
                 time_per_bidirectional_communication
                 ],
        name="sendto_time_per_cell_selected",
        bq_dialect="standard"
    )
    return mt

def snap_viewer(start_date, end_date):
    """
    Snap viewers per sender
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    source_table = """
    `sc-analytics.report_growth.snap_sender_viewer_summary_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    receiver_side_snap_viewer = Metric(
        'receiver_side_snap_viewer',
        'Receiver-side SendTo Source Snap Viewer',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    receiver_side_snap_view = Metric(
        'receiver_side_snap_view',
        'Receiver-side SendTo Source Snap View',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    receiver_side_inactive_1_day_snap_viewer = Metric(
        'receiver_side_inactive_1_day_snap_viewer',
        'Receiver-side SendTo Source Snap Viewer (Inactive 1D+)',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    receiver_side_inactive_4_day_snap_viewer = Metric(
        'receiver_side_inactive_4_day_snap_viewer',
        'Receiver-side SendTo Source Snap Viewer (Inactive 4D+)',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    receiver_side_inactive_7_day_snap_viewer = Metric(
        'receiver_side_inactive_7_day_snap_viewer',
        'Receiver-side SendTo Source Snap Viewer (Inactive 7D+)',
        dist='cont',
        daily=True,
        cumulative=True,
    )


    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        ghost_user_id,
        SUM(sendto_source_snap_viewer) as receiver_side_snap_viewer,
        SUM(viewer_sendto_source_snap_viewed) AS receiver_side_snap_view,
        SUM(IF(viewer_inactive_day_bucket in ('C 1-3','D 4-6','E 7-13','F 14-29','G 30+'),sendto_source_snap_viewer,0)) as receiver_side_inactive_1_day_snap_viewer,
        SUM(IF(viewer_inactive_day_bucket in ('D 4-6','E 7-13','F 14-29','G 30+'),sendto_source_snap_viewer,0)) as receiver_side_inactive_4_day_snap_viewer,
        SUM(IF(viewer_inactive_day_bucket in ('E 7-13','F 14-29','G 30+'),sendto_source_snap_viewer,0)) as receiver_side_inactive_7_day_snap_viewer,
        FROM {source_table}
        GROUP BY 1,2
        """.format(source_table=source_table),
        metrics=[
                receiver_side_snap_viewer,
                receiver_side_snap_view,
                receiver_side_inactive_1_day_snap_viewer,
                receiver_side_inactive_4_day_snap_viewer,
                receiver_side_inactive_7_day_snap_viewer,
                ],
        name="snap_viewer",
        bq_dialect="standard"
    )
    return mt

def snap_viewer_engagement_change(start_date, end_date):
    """
    Snap viewers with engagement changes per sender
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    source_table = """
    `sc-analytics.report_growth.snap_viewer_engagement_*`
    WHERE _table_suffix between '{start}' and '{end}'
    and is_from_send_to
    """.format(
        start=start_date,
        end=end_date,
    )

    receiver_side_snap_viewer_bdc_increase = Metric(
        'receiver_side_snap_viewer_bdc_increase',
        'Receiver-side SendTo Source Snap Viewer BDC Increase',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    receiver_side_snap_viewer_bdc_increase_from_1 = Metric(
        'receiver_side_snap_viewer_bdc_increase_from_1',
        'Receiver-side SendTo Source Snap Viewer BDC Increase From 1',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    receiver_side_snap_viewer_bdc_increase_to_1 = Metric(
        'receiver_side_snap_viewer_bdc_increase_to_1',
        'Receiver-side SendTo Source Snap Viewer BDC Increase To 1',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    receiver_side_snap_viewer_bdc_decrease_to_0 = Metric(
        'receiver_side_snap_viewer_bdc_decrease_to_0',
        'Receiver-side SendTo Source Snap Viewer BDC Decrease To 0',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )
    
    receiver_side_snap_viewer_streak_increase = Metric(
        'receiver_side_snap_viewer_streak_increase',
        'Receiver-side SendTo Source Snap Viewer Streak Increase',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    receiver_side_snap_viewer_streak_increase_from_1 = Metric(
        'receiver_side_snap_viewer_streak_increase_from_1',
        'Receiver-side SendTo Source Snap Viewer Streak Increase From 1',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    receiver_side_snap_viewer_streak_increase_to_1 = Metric(
        'receiver_side_snap_viewer_streak_increase_to_1',
        'Receiver-side SendTo Source Snap Viewer Streak Increase To 1',
        dist='cont',
        daily=True,
        cumulative=True,
    )
    
    receiver_side_snap_viewer_streak_decrease_to_0 = Metric(
        'receiver_side_snap_viewer_streak_decrease_to_0',
        'Receiver-side SendTo Source Snap Viewer Streak Decrease To 0',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )
    
    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        send_ghost_user_id as ghost_user_id,
        count(distinct if(viewer_n_bdc_friend > viewer_n_bdc_friend_1md, viewer_ghost_user_id, NULL)) as receiver_side_snap_viewer_bdc_increase,
        count(distinct if(viewer_n_bdc_friend > viewer_n_bdc_friend_1md and viewer_n_bdc_friend_1md = 1, viewer_ghost_user_id, NULL)) as receiver_side_snap_viewer_bdc_increase_from_1,
        count(distinct if(viewer_n_bdc_friend > viewer_n_bdc_friend_1md and viewer_n_bdc_friend = 1, viewer_ghost_user_id, NULL)) as receiver_side_snap_viewer_bdc_increase_to_1,
        count(distinct if(viewer_n_bdc_friend < viewer_n_bdc_friend_1md and viewer_n_bdc_friend = 0, viewer_ghost_user_id, NULL)) as receiver_side_snap_viewer_bdc_decrease_to_0,
        count(distinct if(viewer_n_streak_friend > viewer_n_streak_friend_1md, viewer_ghost_user_id, NULL)) as receiver_side_snap_viewer_streak_increase,
        count(distinct if(viewer_n_streak_friend > viewer_n_streak_friend_1md and viewer_n_streak_friend_1md = 1, viewer_ghost_user_id, NULL)) as receiver_side_snap_viewer_streak_increase_from_1,
        count(distinct if(viewer_n_streak_friend > viewer_n_streak_friend_1md and viewer_n_streak_friend = 1, viewer_ghost_user_id, NULL)) as receiver_side_snap_viewer_streak_increase_to_1,
        count(distinct if(viewer_n_streak_friend < viewer_n_streak_friend_1md and viewer_n_streak_friend = 0, viewer_ghost_user_id, NULL)) as receiver_side_snap_viewer_streak_decrease_to_0,
        FROM {source_table}
        GROUP BY 1,2
        """.format(source_table=source_table),
        metrics=[
                receiver_side_snap_viewer_bdc_increase,
                receiver_side_snap_viewer_bdc_increase_from_1,
                receiver_side_snap_viewer_bdc_increase_to_1,
                receiver_side_snap_viewer_bdc_decrease_to_0,
                receiver_side_snap_viewer_streak_increase,
                receiver_side_snap_viewer_streak_increase_from_1,
                receiver_side_snap_viewer_streak_increase_to_1,
                receiver_side_snap_viewer_streak_decrease_to_0,
                ],
        name="snap_viewer_engagement_change",
        bq_dialect="standard"
    )
    return mt

def bidirectional_communication_breakdown(start_date, end_date):
    """
    Bidirectional communication count breakdowns
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    source_table = """
    `sc-analytics.report_growth.bidirectional_communication_breakdown_user_level_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    send_communication_count_1to1 = Metric('send_communication_count_1to1', 'send_communication_count_1to1', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_count_1to1 = Metric('bidirectional_communication_count_1to1', 'bidirectional_communication_count_1to1', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_ratio = Metric(col='bidirectional_communication_count_1to1_ratio', numerator='bidirectional_communication_count_1to1', denominator='send_communication_count_1to1', dist='ratio')
    bidirectional_communication_count_group = Metric('bidirectional_communication_count_group', 'bidirectional_communication_count_group', dist='cont', daily=True, cumulative=True)
    in_group_bidirectional_communication_count = Metric('in_group_bidirectional_communication_count', 'in_group_bidirectional_communication_count', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_and_in_group = Metric('bidirectional_communication_count_1to1_and_in_group', 'bidirectional_communication_count_1to1_and_in_group', dist='cont', daily=True, cumulative=True)

    bidirectional_communication_count_streak_3_7 = Metric('bidirectional_communication_count_streak_3_7', 'bidirectional_communication_count_streak_3_7', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_count_streak_8_30 = Metric('bidirectional_communication_count_streak_8_30', 'bidirectional_communication_count_streak_8_30', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_count_streak_31_ = Metric('bidirectional_communication_count_streak_31_', 'bidirectional_communication_count_streak_31_', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_count_streak_null = Metric('bidirectional_communication_count_streak_null', 'bidirectional_communication_count_streak_null', dist='cont', daily=True, cumulative=True)

    bidirectional_communication_count_friend_days_0_1 = Metric('bidirectional_communication_count_friend_days_0_1', 'bidirectional_communication_count_friend_days_0_1', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_count_friend_days_2_7 = Metric('bidirectional_communication_count_friend_days_2_7', 'bidirectional_communication_count_friend_days_2_7', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_count_friend_days_8_30 = Metric('bidirectional_communication_count_friend_days_8_30', 'bidirectional_communication_count_friend_days_8_30', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_count_friend_days_31_ = Metric('bidirectional_communication_count_friend_days_31_', 'bidirectional_communication_count_friend_days_31_', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_count_friend_days_null = Metric('bidirectional_communication_count_friend_days_null', 'bidirectional_communication_count_friend_days_null', dist='cont', daily=True, cumulative=True)

    bidirectional_communication_count_1to1_l7equals1 = Metric('bidirectional_communication_count_1to1_l7equals1', 'bidirectional_communication_count_1to1_bdc_l7equals1', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7_2plus = Metric('bidirectional_communication_count_1to1_l7_2plus', 'bidirectional_communication_count_1to1_bdc_l7_2plus', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7_3plus = Metric('bidirectional_communication_count_1to1_l7_3plus', 'bidirectional_communication_count_1to1_bdc_l7_3plus', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7_4plus = Metric('bidirectional_communication_count_1to1_l7_4plus', 'bidirectional_communication_count_1to1_bdc_l7_4plus', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7_5plus = Metric('bidirectional_communication_count_1to1_l7_5plus', 'bidirectional_communication_count_1to1_bdc_l7_5plus', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7_6plus = Metric('bidirectional_communication_count_1to1_l7_6plus', 'bidirectional_communication_count_1to1_bdc_l7_6plus', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7_7plus = Metric('bidirectional_communication_count_1to1_l7_7plus', 'bidirectional_communication_count_1to1_bdc_l7_7plus', dist='cont', daily=True, cumulative=True)

    bidirectional_communication_count_1to1_snap_and_chat = Metric('bidirectional_communication_count_1to1_snap_and_chat', 'bidirectional_communication_count_1to1_snap_and_chat', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_chat_only = Metric('bidirectional_communication_count_1to1_chat_only', 'bidirectional_communication_count_1to1_chat_only', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_snap_only = Metric('bidirectional_communication_count_1to1_snap_only', 'bidirectional_communication_count_1to1_snap_only', dist='cont', daily=True, cumulative=True)

    bidirectional_communication_count_1to1_l7equals1_CAMERA = Metric('bidirectional_communication_count_1to1_l7equals1_CAMERA', 'bidirectional_communication_count_1to1_l7equals1_CAMERA', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7equals1_CONTEXT_STORY_REPLY = Metric('bidirectional_communication_count_1to1_l7equals1_CONTEXT_STORY_REPLY', 'bidirectional_communication_count_1to1_l7equals1_CONTEXT_STORY_REPLY', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7equals1_FEED = Metric('bidirectional_communication_count_1to1_l7equals1_FEED', 'bidirectional_communication_count_1to1_l7equals1_FEED', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7equals1_CHAT = Metric('bidirectional_communication_count_1to1_l7equals1_CHAT', 'bidirectional_communication_count_1to1_l7equals1_CHAT', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7equals1_SEARCH = Metric('bidirectional_communication_count_1to1_l7equals1_SEARCH', 'bidirectional_communication_count_1to1_l7equals1_SEARCH', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7equals1_FEED_SNAP_REPLY = Metric('bidirectional_communication_count_1to1_l7equals1_FEED_SNAP_REPLY', 'bidirectional_communication_count_1to1_l7equals1_FEED_SNAP_REPLY', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7equals1_ADD_FRIENDS_PAGE = Metric('bidirectional_communication_count_1to1_l7equals1_ADD_FRIENDS_PAGE', 'bidirectional_communication_count_1to1_l7equals1_ADD_FRIENDS_PAGE', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7equals1_GALLERY = Metric('bidirectional_communication_count_1to1_l7equals1_GALLERY', 'bidirectional_communication_count_1to1_l7equals1_GALLERY', dist='cont', daily=True, cumulative=True)

    send_communication_count_1to1_uu = Metric('send_communication_count_1to1_uu', 'send_communication_uu_1to1', dist='bin', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_uu = Metric('bidirectional_communication_count_1to1_uu', 'bidirectional_communication_uu_1to1', dist='bin', daily=True, cumulative=True)
    bidirectional_communication_count_group_uu = Metric('bidirectional_communication_count_group_uu', 'bidirectional_communication_uu_group', dist='bin', daily=True, cumulative=True)
    in_group_bidirectional_communication_count_uu = Metric('in_group_bidirectional_communication_count_uu', 'in_group_bidirectional_communication_uu', dist='bin', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_and_in_group_uu = Metric('bidirectional_communication_count_1to1_and_in_group_uu', 'bidirectional_communication_uu_1to1_and_in_group', dist='bin', daily=True, cumulative=True)

    bidirectional_communication_count_streak_3_7_uu = Metric('bidirectional_communication_count_streak_3_7_uu', 'bidirectional_communication_uu_streak_3_7', dist='bin', daily=True, cumulative=True)
    bidirectional_communication_count_streak_8_30_uu = Metric('bidirectional_communication_count_streak_8_30_uu', 'bidirectional_communication_uu_streak_8_30', dist='bin', daily=True, cumulative=True)
    bidirectional_communication_count_streak_31__uu = Metric('bidirectional_communication_count_streak_31__uu', 'bidirectional_communication_uu_streak_31_', dist='bin', daily=True, cumulative=True)
    bidirectional_communication_count_streak_null_uu = Metric('bidirectional_communication_count_streak_null_uu', 'bidirectional_communication_uu_streak_null', dist='bin', daily=True, cumulative=True)

    bidirectional_communication_count_friend_days_0_1_uu = Metric('bidirectional_communication_count_friend_days_0_1_uu', 'bidirectional_communication_uu_friend_days_0_1', dist='bin', daily=True, cumulative=True)
    bidirectional_communication_count_friend_days_2_7_uu = Metric('bidirectional_communication_count_friend_days_2_7_uu', 'bidirectional_communication_uu_friend_days_2_7', dist='bin', daily=True, cumulative=True)
    bidirectional_communication_count_friend_days_8_30_uu = Metric('bidirectional_communication_count_friend_days_8_30_uu', 'bidirectional_communication_uu_friend_days_8_30', dist='bin', daily=True, cumulative=True)
    bidirectional_communication_count_friend_days_31__uu = Metric('bidirectional_communication_count_friend_days_31__uu', 'bidirectional_communication_uu_friend_days_31_', dist='bin', daily=True, cumulative=True)
    bidirectional_communication_count_friend_days_null_uu = Metric('bidirectional_communication_count_friend_days_null_uu', 'bidirectional_communication_uu_friend_days_null', dist='bin', daily=True, cumulative=True)

    bidirectional_communication_count_1to1_l7equals1_uu = Metric('bidirectional_communication_count_1to1_l7equals1_uu', 'bidirectional_communication_uu_1to1_l7equals1', dist='bin', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7_2plus_uu = Metric('bidirectional_communication_count_1to1_l7_2plus_uu', 'bidirectional_communication_uu_1to1_l7_2plus', dist='bin', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7_3plus_uu = Metric('bidirectional_communication_count_1to1_l7_3plus_uu', 'bidirectional_communication_uu_1to1_l7_3plus', dist='bin', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7_4plus_uu = Metric('bidirectional_communication_count_1to1_l7_4plus_uu', 'bidirectional_communication_uu_1to1_l7_4plus', dist='bin', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7_5plus_uu = Metric('bidirectional_communication_count_1to1_l7_5plus_uu', 'bidirectional_communication_uu_1to1_l7_5plus', dist='bin', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7_6plus_uu = Metric('bidirectional_communication_count_1to1_l7_6plus_uu', 'bidirectional_communication_uu_1to1_l7_6plus', dist='bin', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7_7plus_uu = Metric('bidirectional_communication_count_1to1_l7_7plus_uu', 'bidirectional_communication_uu_1to1_l7_7plus', dist='bin', daily=True, cumulative=True)

    bidirectional_communication_count_1to1_snap_and_chat_uu = Metric('bidirectional_communication_count_1to1_snap_and_chat_uu', 'bidirectional_communication_uu_1to1_snap_and_chat', dist='bin', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_chat_only_uu = Metric('bidirectional_communication_count_1to1_chat_only_uu', 'bidirectional_communication_uu_1to1_chat_only', dist='bin', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_snap_only_uu = Metric('bidirectional_communication_count_1to1_snap_only_uu', 'bidirectional_communication_uu_1to1_snap_only', dist='bin', daily=True, cumulative=True)

    bidirectional_communication_count_1to1_l7equals1_CAMERA_uu = Metric('bidirectional_communication_count_1to1_l7equals1_CAMERA_uu', 'bidirectional_communication_uu_1to1_l7equals1_CAMERA', dist='bin', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7equals1_CONTEXT_STORY_REPLY_uu = Metric('bidirectional_communication_count_1to1_l7equals1_CONTEXT_STORY_REPLY_uu', 'bidirectional_communication_uu_1to1_l7equals1_CONTEXT_STORY_REPLY', dist='bin', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7equals1_FEED_uu = Metric('bidirectional_communication_count_1to1_l7equals1_FEED_uu', 'bidirectional_communication_uu_1to1_l7equals1_FEED', dist='bin', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7equals1_CHAT_uu = Metric('bidirectional_communication_count_1to1_l7equals1_CHAT_uu', 'bidirectional_communication_uu_1to1_l7equals1_CHAT', dist='bin', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7equals1_SEARCH_uu = Metric('bidirectional_communication_count_1to1_l7equals1_SEARCH_uu', 'bidirectional_communication_uu_1to1_l7equals1_SEARCH', dist='bin', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7equals1_FEED_SNAP_REPLY_uu = Metric('bidirectional_communication_count_1to1_l7equals1_FEED_SNAP_REPLY_uu', 'bidirectional_communication_uu_1to1_l7equals1_FEED_SNAP_REPLY', dist='bin', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7equals1_ADD_FRIENDS_PAGE_uu = Metric('bidirectional_communication_count_1to1_l7equals1_ADD_FRIENDS_PAGE_uu', 'bidirectional_communication_uu_1to1_l7equals1_ADD_FRIENDS_PAGE', dist='bin', daily=True, cumulative=True)
    bidirectional_communication_count_1to1_l7equals1_GALLERY_uu = Metric('bidirectional_communication_count_1to1_l7equals1_GALLERY_uu', 'bidirectional_communication_uu_1to1_l7equals1_GALLERY', dist='bin', daily=True, cumulative=True)
    
    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) AS ts,
        ghost_user_id,

        SUM(IF(correspondent_is_group=FALSE, send_communication_count, 0)) AS send_communication_count_1to1,
        SUM(IF(correspondent_is_group=FALSE, bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1,
        SUM(IF(correspondent_is_group, bidirectional_communication_count, 0)) AS bidirectional_communication_count_group,
        SUM(in_group_bidirectional_communication_count) AS in_group_bidirectional_communication_count,
        COALESCE(SUM(IF(correspondent_is_group=FALSE, bidirectional_communication_count, 0)), 0) + COALESCE(SUM(in_group_bidirectional_communication_count), 0) AS bidirectional_communication_count_1to1_and_in_group,

        SUM(IF(current_streak_count="(a)3-7", bidirectional_communication_count, 0)) AS bidirectional_communication_count_streak_3_7,
        SUM(IF(current_streak_count="(b)8-30", bidirectional_communication_count, 0)) AS bidirectional_communication_count_streak_8_30,
        SUM(IF(current_streak_count="(c)31+", bidirectional_communication_count, 0)) AS bidirectional_communication_count_streak_31_,
        SUM(IF(current_streak_count IS NULL AND correspondent_is_group=FALSE, bidirectional_communication_count, 0)) AS bidirectional_communication_count_streak_null,

        SUM(IF(friend_age_days="(a)0-1", bidirectional_communication_count, 0)) AS bidirectional_communication_count_friend_days_0_1,
        SUM(IF(friend_age_days="(b)2-7", bidirectional_communication_count, 0)) AS bidirectional_communication_count_friend_days_2_7,
        SUM(IF(friend_age_days="(c)8-30", bidirectional_communication_count, 0)) AS bidirectional_communication_count_friend_days_8_30,
        SUM(IF(friend_age_days="(d)31+", bidirectional_communication_count, 0)) AS bidirectional_communication_count_friend_days_31_,
        SUM(IF(friend_age_days IS NULL AND correspondent_is_group=FALSE, bidirectional_communication_count, 0)) AS bidirectional_communication_count_friend_days_null,

        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7=1, bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7equals1,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7>=2, bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7_2plus,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7>=3, bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7_3plus,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7>=4, bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7_4plus,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7>=5, bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7_5plus,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7>=6, bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7_6plus,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7>=7, bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7_7plus,

        SUM(IF(correspondent_is_group=FALSE AND has_snap AND has_chat, bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_snap_and_chat,
        SUM(IF(correspondent_is_group=FALSE AND NOT has_snap AND has_chat, bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_chat_only,
        SUM(IF(correspondent_is_group=FALSE AND has_snap AND NOT has_chat, bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_snap_only,

        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7=1 AND first_communication_source='CAMERA', bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7equals1_CAMERA,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7=1 AND first_communication_source='CONTEXT_STORY_REPLY', bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7equals1_CONTEXT_STORY_REPLY,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7=1 AND first_communication_source='FEED', bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7equals1_FEED,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7=1 AND first_communication_source IN ('CHAT', 'IN_CHAT'), bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7equals1_CHAT,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7=1 AND first_communication_source='SEARCH_UNSPECIFIED', bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7equals1_SEARCH,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7=1 AND first_communication_source='FEED_SNAP_REPLY', bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7equals1_FEED_SNAP_REPLY,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7=1 AND first_communication_source='ADD_FRIENDS_PAGE', bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7equals1_ADD_FRIENDS_PAGE,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7=1 AND first_communication_source='GALLERY', bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7equals1_GALLERY,

        SUM(IF(correspondent_is_group=FALSE, send_communication_count, 0)) AS send_communication_count_1to1_uu,
        SUM(IF(correspondent_is_group=FALSE, bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_uu,
        SUM(IF(correspondent_is_group, bidirectional_communication_count, 0)) AS bidirectional_communication_count_group_uu,
        SUM(in_group_bidirectional_communication_count) AS in_group_bidirectional_communication_count_uu,
        COALESCE(SUM(IF(correspondent_is_group=FALSE, bidirectional_communication_count, 0)), 0) + COALESCE(SUM(in_group_bidirectional_communication_count), 0) AS bidirectional_communication_count_1to1_and_in_group_uu,

        SUM(IF(current_streak_count="(a)3-7", bidirectional_communication_count, 0)) AS bidirectional_communication_count_streak_3_7_uu,
        SUM(IF(current_streak_count="(b)8-30", bidirectional_communication_count, 0)) AS bidirectional_communication_count_streak_8_30_uu,
        SUM(IF(current_streak_count="(c)31+", bidirectional_communication_count, 0)) AS bidirectional_communication_count_streak_31__uu,
        SUM(IF(current_streak_count IS NULL AND correspondent_is_group=FALSE, bidirectional_communication_count, 0)) AS bidirectional_communication_count_streak_null_uu,

        SUM(IF(friend_age_days="(a)0-1", bidirectional_communication_count, 0)) AS bidirectional_communication_count_friend_days_0_1_uu,
        SUM(IF(friend_age_days="(b)2-7", bidirectional_communication_count, 0)) AS bidirectional_communication_count_friend_days_2_7_uu,
        SUM(IF(friend_age_days="(c)8-30", bidirectional_communication_count, 0)) AS bidirectional_communication_count_friend_days_8_30_uu,
        SUM(IF(friend_age_days="(d)31+", bidirectional_communication_count, 0)) AS bidirectional_communication_count_friend_days_31__uu,
        SUM(IF(friend_age_days IS NULL AND correspondent_is_group=FALSE, bidirectional_communication_count, 0)) AS bidirectional_communication_count_friend_days_null_uu,

        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7=1, bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7equals1_uu,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7>=2, bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7_2plus_uu,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7>=3, bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7_3plus_uu,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7>=4, bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7_4plus_uu,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7>=5, bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7_5plus_uu,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7>=6, bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7_6plus_uu,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7>=7, bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7_7plus_uu,

        SUM(IF(correspondent_is_group=FALSE AND has_snap AND has_chat, bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_snap_and_chat_uu,
        SUM(IF(correspondent_is_group=FALSE AND NOT has_snap AND has_chat, bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_chat_only_uu,
        SUM(IF(correspondent_is_group=FALSE AND has_snap AND NOT has_chat, bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_snap_only_uu,

        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7=1 AND first_communication_source='CAMERA', bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7equals1_CAMERA_uu,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7=1 AND first_communication_source='CONTEXT_STORY_REPLY', bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7equals1_CONTEXT_STORY_REPLY_uu,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7=1 AND first_communication_source='FEED', bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7equals1_FEED_uu,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7=1 AND first_communication_source IN ('CHAT', 'IN_CHAT'), bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7equals1_CHAT_uu,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7=1 AND first_communication_source='SEARCH_UNSPECIFIED', bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7equals1_SEARCH_uu,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7=1 AND first_communication_source='FEED_SNAP_REPLY', bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7equals1_FEED_SNAP_REPLY_uu,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7=1 AND first_communication_source='ADD_FRIENDS_PAGE', bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7equals1_ADD_FRIENDS_PAGE_uu,
        SUM(IF(correspondent_is_group=FALSE AND bidirectional_communication_l7=1 AND first_communication_source='GALLERY', bidirectional_communication_count, 0)) AS bidirectional_communication_count_1to1_l7equals1_GALLERY_uu
        FROM
          {source_table}
        GROUP BY 1, 2
        """.format(source_table=source_table),

        metrics=[
        send_communication_count_1to1,
        bidirectional_communication_count_1to1,
        bidirectional_communication_count_1to1_ratio,
        bidirectional_communication_count_group,
        in_group_bidirectional_communication_count,
        bidirectional_communication_count_1to1_and_in_group,

        bidirectional_communication_count_streak_3_7,
        bidirectional_communication_count_streak_8_30,
        bidirectional_communication_count_streak_31_,
        bidirectional_communication_count_streak_null,

        bidirectional_communication_count_friend_days_0_1,
        bidirectional_communication_count_friend_days_2_7,
        bidirectional_communication_count_friend_days_8_30,
        bidirectional_communication_count_friend_days_31_,
        bidirectional_communication_count_friend_days_null,

        bidirectional_communication_count_1to1_l7equals1,
        bidirectional_communication_count_1to1_l7_2plus,
        bidirectional_communication_count_1to1_l7_3plus,
        bidirectional_communication_count_1to1_l7_4plus,
        bidirectional_communication_count_1to1_l7_5plus,
        bidirectional_communication_count_1to1_l7_6plus,
        bidirectional_communication_count_1to1_l7_7plus,

        bidirectional_communication_count_1to1_snap_and_chat,
        bidirectional_communication_count_1to1_chat_only,
        bidirectional_communication_count_1to1_snap_only,

        bidirectional_communication_count_1to1_l7equals1_CAMERA,
        bidirectional_communication_count_1to1_l7equals1_CONTEXT_STORY_REPLY,
        bidirectional_communication_count_1to1_l7equals1_FEED,
        bidirectional_communication_count_1to1_l7equals1_CHAT,
        bidirectional_communication_count_1to1_l7equals1_SEARCH,
        bidirectional_communication_count_1to1_l7equals1_FEED_SNAP_REPLY,
        bidirectional_communication_count_1to1_l7equals1_ADD_FRIENDS_PAGE,
        bidirectional_communication_count_1to1_l7equals1_GALLERY,

        send_communication_count_1to1_uu,
        bidirectional_communication_count_1to1_uu,
        bidirectional_communication_count_group_uu,
        in_group_bidirectional_communication_count_uu,
        bidirectional_communication_count_1to1_and_in_group_uu,

        bidirectional_communication_count_streak_3_7_uu,
        bidirectional_communication_count_streak_8_30_uu,
        bidirectional_communication_count_streak_31__uu,
        bidirectional_communication_count_streak_null_uu,
        bidirectional_communication_count_friend_days_0_1_uu,
        bidirectional_communication_count_friend_days_2_7_uu,
        bidirectional_communication_count_friend_days_8_30_uu,
        bidirectional_communication_count_friend_days_31__uu,
        bidirectional_communication_count_friend_days_null_uu,
        bidirectional_communication_count_1to1_l7equals1_uu,
        bidirectional_communication_count_1to1_l7_2plus_uu,
        bidirectional_communication_count_1to1_l7_3plus_uu,
        bidirectional_communication_count_1to1_l7_4plus_uu,
        bidirectional_communication_count_1to1_l7_5plus_uu,
        bidirectional_communication_count_1to1_l7_6plus_uu,
        bidirectional_communication_count_1to1_l7_7plus_uu,
        bidirectional_communication_count_1to1_snap_and_chat_uu,
        bidirectional_communication_count_1to1_chat_only_uu,
        bidirectional_communication_count_1to1_snap_only_uu,
        bidirectional_communication_count_1to1_l7equals1_CAMERA_uu,
        bidirectional_communication_count_1to1_l7equals1_CONTEXT_STORY_REPLY_uu,
        bidirectional_communication_count_1to1_l7equals1_FEED_uu,
        bidirectional_communication_count_1to1_l7equals1_CHAT_uu,
        bidirectional_communication_count_1to1_l7equals1_SEARCH_uu,
        bidirectional_communication_count_1to1_l7equals1_FEED_SNAP_REPLY_uu,
        bidirectional_communication_count_1to1_l7equals1_ADD_FRIENDS_PAGE_uu,
        bidirectional_communication_count_1to1_l7equals1_GALLERY_uu
        ],
        name="bidirectional_communication_breakdown",
        bq_dialect="standard"
    )
    return mt

def sendto_snap_send_recipient_breakdown(start_date, end_date):
    """
    SendTo snap send breakdowns
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    source_table = """
    `sc-analytics.report_growth.stg_usr_sessionized_sendto_time_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    send_communication_friend = Metric('send_communication_friend', 'sendto_recipient_friend', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_friend = Metric('bidirectional_communication_friend', 'sendto_recipient_bdc_friend', dist='cont', daily=True, cumulative=True,)
    send_communication_group = Metric('send_communication_group', 'sendto_recipient_group', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_group = Metric('bidirectional_communication_group', 'sendto_recipient_bdc_group', dist='cont', daily=True, cumulative=True,)

    # Breakdown by BDC L-ness
    bidirectional_communication_friend_bdc_l7_1 = Metric('bidirectional_communication_friend_bdc_l7_1', 'sendto_recipient_bdc_friend_bdc_l7_1', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_friend_bdc_l7_2 = Metric('bidirectional_communication_friend_bdc_l7_2', 'sendto_recipient_bdc_friend_bdc_l7_2', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_friend_bdc_l7_3 = Metric('bidirectional_communication_friend_bdc_l7_3', 'sendto_recipient_bdc_friend_bdc_l7_3', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_friend_bdc_l7_4 = Metric('bidirectional_communication_friend_bdc_l7_4', 'sendto_recipient_bdc_friend_bdc_l7_4', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_friend_bdc_l7_5 = Metric('bidirectional_communication_friend_bdc_l7_5', 'sendto_recipient_bdc_friend_bdc_l7_5', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_friend_bdc_l7_6 = Metric('bidirectional_communication_friend_bdc_l7_6', 'sendto_recipient_bdc_friend_bdc_l7_6', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_friend_bdc_l7_7 = Metric('bidirectional_communication_friend_bdc_l7_7', 'sendto_recipient_bdc_friend_bdc_l7_7', dist='cont', daily=True, cumulative=True,)

    # Breakdown by BDC L-ness (l7_xplus metrics)
    bidirectional_communication_friend_bdc_l7_6plus = Metric('bidirectional_communication_friend_bdc_l7_6plus', 'sendto_recipient_bdc_friend_bdc_l7_6plus', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_friend_bdc_l7_5plus = Metric('bidirectional_communication_friend_bdc_l7_5plus', 'sendto_recipient_bdc_friend_bdc_l7_5plus', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_friend_bdc_l7_4plus = Metric('bidirectional_communication_friend_bdc_l7_4plus', 'sendto_recipient_bdc_friend_bdc_l7_4plus', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_friend_bdc_l7_3plus = Metric('bidirectional_communication_friend_bdc_l7_3plus', 'sendto_recipient_bdc_friend_bdc_l7_3plus', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_friend_bdc_l7_2plus = Metric('bidirectional_communication_friend_bdc_l7_2plus', 'sendto_recipient_bdc_friend_bdc_l7_2plus', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_friend_bdc_l7_1plus = Metric('bidirectional_communication_friend_bdc_l7_1plus', 'sendto_recipient_bdc_friend_bdc_l7_1plus', dist='cont', daily=True, cumulative=True,)

    # Breakdown by receiver app L-ness
    bidirectional_communication_correspondent_app_l7_1 = Metric('bidirectional_communication_correspondent_app_l7_1', 'sendto_recipient_bdc_friend_app_l7_1', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_correspondent_app_l7_2 = Metric('bidirectional_communication_correspondent_app_l7_2', 'sendto_recipient_bdc_friend_app_l7_2', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_correspondent_app_l7_3 = Metric('bidirectional_communication_correspondent_app_l7_3', 'sendto_recipient_bdc_friend_app_l7_3', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_correspondent_app_l7_4 = Metric('bidirectional_communication_correspondent_app_l7_4', 'sendto_recipient_bdc_friend_app_l7_4', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_correspondent_app_l7_5 = Metric('bidirectional_communication_correspondent_app_l7_5', 'sendto_recipient_bdc_friend_app_l7_5', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_correspondent_app_l7_6 = Metric('bidirectional_communication_correspondent_app_l7_6', 'sendto_recipient_bdc_friend_app_l7_6', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_correspondent_app_l7_7 = Metric('bidirectional_communication_correspondent_app_l7_7', 'sendto_recipient_bdc_friend_app_l7_7', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_correspondent_app_l7_increase_dbd = Metric('bidirectional_communication_correspondent_app_l7_increase_dbd', 'sendto_recipient_bdc_friend_app_l7_increase_dbd', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_correspondent_app_l7_increase_dbd_from0 = Metric('bidirectional_communication_correspondent_app_l7_increase_dbd_from0', 'bidirectional_communication_correspondent_app_l7_increase_dbd_from0', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_correspondent_app_l7_increase_dbd_from3minus = Metric('bidirectional_communication_correspondent_app_l7_increase_dbd_from3minus', 'bidirectional_communication_correspondent_app_l7_increase_dbd_from3minus', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_correspondent_app_l7_increase_dbd_from7minus = Metric('bidirectional_communication_correspondent_app_l7_increase_dbd_from7minus', 'bidirectional_communication_correspondent_app_l7_increase_dbd_from7minus', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_correspondent_app_l7_increase_dbd_to1plus = Metric('bidirectional_communication_correspondent_app_l7_increase_dbd_to1plus', 'bidirectional_communication_correspondent_app_l7_increase_dbd_to1plus', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_correspondent_app_l7_increase_dbd_to3plus = Metric('bidirectional_communication_correspondent_app_l7_increase_dbd_to3plus', 'bidirectional_communication_correspondent_app_l7_increase_dbd_to3plus', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_correspondent_app_l7_increase_dbd_to7plus = Metric('bidirectional_communication_correspondent_app_l7_increase_dbd_to7plus', 'bidirectional_communication_correspondent_app_l7_increase_dbd_to7plus', dist='cont', daily=True, cumulative=True)

    # Breakdown by receiver app L-ness (l7_xplus metrics)
    bidirectional_communication_correspondent_app_l7_6plus = Metric('bidirectional_communication_correspondent_app_l7_6plus', 'sendto_recipient_bdc_friend_app_l7_6plus', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_correspondent_app_l7_5plus = Metric('bidirectional_communication_correspondent_app_l7_5plus', 'sendto_recipient_bdc_friend_app_l7_5plus', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_correspondent_app_l7_4plus = Metric('bidirectional_communication_correspondent_app_l7_4plus', 'sendto_recipient_bdc_friend_app_l7_4plus', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_correspondent_app_l7_3plus = Metric('bidirectional_communication_correspondent_app_l7_3plus', 'sendto_recipient_bdc_friend_app_l7_3plus', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_correspondent_app_l7_2plus = Metric('bidirectional_communication_correspondent_app_l7_2plus', 'sendto_recipient_bdc_friend_app_l7_2plus', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_correspondent_app_l7_1plus = Metric('bidirectional_communication_correspondent_app_l7_1plus', 'sendto_recipient_bdc_friend_app_l7_1plus', dist='cont', daily=True, cumulative=True,)

    # Breakdown by receiver daily BDC count
    bidirectional_communication_correspondent_daily_1_bdc = Metric('bidirectional_communication_correspondent_daily_1_bdc', 'sendto_recipient_bdc_friend_daily_1_minus_bdc', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_correspondent_daily_3_bdc = Metric('bidirectional_communication_correspondent_daily_3_bdc', 'sendto_recipient_bdc_friend_daily_3_minus_bdc', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_correspondent_daily_bdc_increase_dbd = Metric('bidirectional_communication_correspondent_daily_bdc_increase_dbd', 'sendto_recipient_bdc_friend_daily_bdc_increase_dbd', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_correspondent_daily_bdc_increase_dbd_from0 = Metric('bidirectional_communication_correspondent_daily_bdc_increase_dbd_from0', 'bidirectional_communication_correspondent_daily_bdc_increase_dbd_from0', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_correspondent_daily_bdc_increase_dbd_from1minus = Metric('bidirectional_communication_correspondent_daily_bdc_increase_dbd_from1minus', 'bidirectional_communication_correspondent_daily_bdc_increase_dbd_from1minus', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_correspondent_daily_bdc_increase_dbd_from3minus = Metric('bidirectional_communication_correspondent_daily_bdc_increase_dbd_from3minus', 'bidirectional_communication_correspondent_daily_bdc_increase_dbd_from3minus', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_correspondent_daily_bdc_increase_dbd_to1plus = Metric('bidirectional_communication_correspondent_daily_bdc_increase_dbd_to1plus', 'bidirectional_communication_correspondent_daily_bdc_increase_dbd_to1plus', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_correspondent_daily_bdc_increase_dbd_to3plus = Metric('bidirectional_communication_correspondent_daily_bdc_increase_dbd_to3plus', 'bidirectional_communication_correspondent_daily_bdc_increase_dbd_to3plus', dist='cont', daily=True, cumulative=True)

    # Breakdown by receiver inactive status
    # "_per_session" means metrics are aggregated from session-level data, the suffix is used to differentiate from the sendto_snap_send_relationship_breakdown group
    bidirectional_communication_correspondent_new_user_per_session = Metric('bidirectional_communication_correspondent_new_user_per_session', 'sendto_recipient_bdc_friend_new_user', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_correspondent_inactive_0_day_per_session = Metric('bidirectional_communication_correspondent_inactive_0_day_per_session', 'sendto_recipient_bdc_friend_inactive_0_day', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_correspondent_inactive_1_plus_day_per_session = Metric('bidirectional_communication_correspondent_inactive_1_plus_day_per_session', 'sendto_recipient_bdc_friend_inactive_1_plus_day', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_correspondent_inactive_7_plus_day_per_session = Metric('bidirectional_communication_correspondent_inactive_7_plus_day_per_session', 'sendto_recipient_bdc_friend_inactive_7_plus_day', dist='cont', daily=True, cumulative=True,)
    bidirectional_communication_correspondent_inactive_30_plus_day_per_session = Metric('bidirectional_communication_correspondent_inactive_30_plus_day_per_session', 'sendto_recipient_bdc_friend_inactive_30_plus_day', dist='cont', daily=True, cumulative=True,)

    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        ghost_user_id,
        SUM(send_communication_friend) AS send_communication_friend,
        SUM(bidirectional_communication_friend) AS bidirectional_communication_friend,
        SUM(send_communication_group) AS send_communication_group,
        SUM(bidirectional_communication_group) AS bidirectional_communication_group,

        SUM(bidirectional_communication_friend_bdc_l7_1) AS bidirectional_communication_friend_bdc_l7_1,
        SUM(bidirectional_communication_friend_bdc_l7_2) AS bidirectional_communication_friend_bdc_l7_2,
        SUM(bidirectional_communication_friend_bdc_l7_3) AS bidirectional_communication_friend_bdc_l7_3,
        SUM(bidirectional_communication_friend_bdc_l7_4) AS bidirectional_communication_friend_bdc_l7_4,
        SUM(bidirectional_communication_friend_bdc_l7_5) AS bidirectional_communication_friend_bdc_l7_5,
        SUM(bidirectional_communication_friend_bdc_l7_6) AS bidirectional_communication_friend_bdc_l7_6,
        SUM(bidirectional_communication_friend_bdc_l7_7) AS bidirectional_communication_friend_bdc_l7_7,

        SUM(bidirectional_communication_friend_bdc_l7_6 + bidirectional_communication_friend_bdc_l7_7) AS bidirectional_communication_friend_bdc_l7_6plus,
        SUM(bidirectional_communication_friend_bdc_l7_5 + bidirectional_communication_friend_bdc_l7_6 + bidirectional_communication_friend_bdc_l7_7) AS bidirectional_communication_friend_bdc_l7_5plus,
        SUM(bidirectional_communication_friend_bdc_l7_4 + bidirectional_communication_friend_bdc_l7_5 + bidirectional_communication_friend_bdc_l7_6 + bidirectional_communication_friend_bdc_l7_7) AS bidirectional_communication_friend_bdc_l7_4plus,
        SUM(bidirectional_communication_friend_bdc_l7_3 + bidirectional_communication_friend_bdc_l7_4 + bidirectional_communication_friend_bdc_l7_5 + bidirectional_communication_friend_bdc_l7_6 + bidirectional_communication_friend_bdc_l7_7) AS bidirectional_communication_friend_bdc_l7_3plus,
        SUM(bidirectional_communication_friend_bdc_l7_2 + bidirectional_communication_friend_bdc_l7_3 + bidirectional_communication_friend_bdc_l7_4 + bidirectional_communication_friend_bdc_l7_5 + bidirectional_communication_friend_bdc_l7_6 + bidirectional_communication_friend_bdc_l7_7) AS bidirectional_communication_friend_bdc_l7_2plus,
        SUM(bidirectional_communication_friend_bdc_l7_1 + bidirectional_communication_friend_bdc_l7_2 + bidirectional_communication_friend_bdc_l7_3 + bidirectional_communication_friend_bdc_l7_4 + bidirectional_communication_friend_bdc_l7_5 + bidirectional_communication_friend_bdc_l7_6 + bidirectional_communication_friend_bdc_l7_7) AS bidirectional_communication_friend_bdc_l7_1plus,        

        SUM(bidirectional_communication_correspondent_app_l7_1) AS bidirectional_communication_correspondent_app_l7_1,
        SUM(bidirectional_communication_correspondent_app_l7_2) AS bidirectional_communication_correspondent_app_l7_2,
        SUM(bidirectional_communication_correspondent_app_l7_3) AS bidirectional_communication_correspondent_app_l7_3,
        SUM(bidirectional_communication_correspondent_app_l7_4) AS bidirectional_communication_correspondent_app_l7_4,
        SUM(bidirectional_communication_correspondent_app_l7_5) AS bidirectional_communication_correspondent_app_l7_5,
        SUM(bidirectional_communication_correspondent_app_l7_6) AS bidirectional_communication_correspondent_app_l7_6,
        SUM(bidirectional_communication_correspondent_app_l7_7) AS bidirectional_communication_correspondent_app_l7_7,
        SUM(bidirectional_communication_correspondent_app_l7_increase_dbd) AS bidirectional_communication_correspondent_app_l7_increase_dbd,
        SUM(bidirectional_communication_correspondent_app_l7_increase_dbd_from0) AS bidirectional_communication_correspondent_app_l7_increase_dbd_from0,
        SUM(bidirectional_communication_correspondent_app_l7_increase_dbd_from3minus) AS bidirectional_communication_correspondent_app_l7_increase_dbd_from3minus,
        SUM(bidirectional_communication_correspondent_app_l7_increase_dbd_from7minus) AS bidirectional_communication_correspondent_app_l7_increase_dbd_from7minus,
        SUM(bidirectional_communication_correspondent_app_l7_increase_dbd_to1plus) AS bidirectional_communication_correspondent_app_l7_increase_dbd_to1plus,
        SUM(bidirectional_communication_correspondent_app_l7_increase_dbd_to3plus) AS bidirectional_communication_correspondent_app_l7_increase_dbd_to3plus,
        SUM(bidirectional_communication_correspondent_app_l7_increase_dbd_to7plus) AS bidirectional_communication_correspondent_app_l7_increase_dbd_to7plus,        

        SUM(bidirectional_communication_correspondent_app_l7_6 + bidirectional_communication_correspondent_app_l7_7) AS bidirectional_communication_correspondent_app_l7_6plus,
        SUM(bidirectional_communication_correspondent_app_l7_5 + bidirectional_communication_correspondent_app_l7_6 + bidirectional_communication_correspondent_app_l7_7) AS bidirectional_communication_correspondent_app_l7_5plus,
        SUM(bidirectional_communication_correspondent_app_l7_4 + bidirectional_communication_correspondent_app_l7_5 + bidirectional_communication_correspondent_app_l7_6 + bidirectional_communication_correspondent_app_l7_7) AS bidirectional_communication_correspondent_app_l7_4plus,
        SUM(bidirectional_communication_correspondent_app_l7_3 + bidirectional_communication_correspondent_app_l7_4 + bidirectional_communication_correspondent_app_l7_5 + bidirectional_communication_correspondent_app_l7_6 + bidirectional_communication_correspondent_app_l7_7) AS bidirectional_communication_correspondent_app_l7_3plus,
        SUM(bidirectional_communication_correspondent_app_l7_2 + bidirectional_communication_correspondent_app_l7_3 + bidirectional_communication_correspondent_app_l7_4 + bidirectional_communication_correspondent_app_l7_5 + bidirectional_communication_correspondent_app_l7_6 + bidirectional_communication_correspondent_app_l7_7) AS bidirectional_communication_correspondent_app_l7_2plus,
        SUM(bidirectional_communication_correspondent_app_l7_1 + bidirectional_communication_correspondent_app_l7_2 + bidirectional_communication_correspondent_app_l7_3 + bidirectional_communication_correspondent_app_l7_4 + bidirectional_communication_correspondent_app_l7_5 + bidirectional_communication_correspondent_app_l7_6 + bidirectional_communication_correspondent_app_l7_7) AS bidirectional_communication_correspondent_app_l7_1plus,

        SUM(bidirectional_communication_correspondent_daily_1_bdc) AS bidirectional_communication_correspondent_daily_1_bdc,
        SUM(bidirectional_communication_correspondent_daily_3_bdc) AS bidirectional_communication_correspondent_daily_3_bdc,
        SUM(bidirectional_communication_correspondent_daily_bdc_increase_dbd) AS bidirectional_communication_correspondent_daily_bdc_increase_dbd,
        SUM(bidirectional_communication_correspondent_daily_bdc_increase_dbd_from0) AS bidirectional_communication_correspondent_daily_bdc_increase_dbd_from0,
        SUM(bidirectional_communication_correspondent_daily_bdc_increase_dbd_from1minus) AS bidirectional_communication_correspondent_daily_bdc_increase_dbd_from1minus,
        SUM(bidirectional_communication_correspondent_daily_bdc_increase_dbd_from3minus) AS bidirectional_communication_correspondent_daily_bdc_increase_dbd_from3minus,
        SUM(bidirectional_communication_correspondent_daily_bdc_increase_dbd_to1plus) AS bidirectional_communication_correspondent_daily_bdc_increase_dbd_to1plus,
        SUM(bidirectional_communication_correspondent_daily_bdc_increase_dbd_to3plus) AS bidirectional_communication_correspondent_daily_bdc_increase_dbd_to3plus,


        sum(bidirectional_communication_correspondent_new_user) AS bidirectional_communication_correspondent_new_user_per_session,
        sum(bidirectional_communication_correspondent_inactive_0_day) AS bidirectional_communication_correspondent_inactive_0_day_per_session,
        sum(bidirectional_communication_correspondent_inactive_1_plus_day) AS bidirectional_communication_correspondent_inactive_1_plus_day_per_session,
        sum(bidirectional_communication_correspondent_inactive_7_plus_day) AS bidirectional_communication_correspondent_inactive_7_plus_day_per_session,
        sum(bidirectional_communication_correspondent_inactive_30_plus_day) AS bidirectional_communication_correspondent_inactive_30_plus_day_per_session,

        FROM {source_table}
        GROUP BY 1,2
        """.format(source_table=source_table),

        metrics=[
        send_communication_friend,
        send_communication_group,
        bidirectional_communication_group,
        bidirectional_communication_friend,
        bidirectional_communication_friend_bdc_l7_1,
        bidirectional_communication_friend_bdc_l7_2,
        bidirectional_communication_friend_bdc_l7_3,
        bidirectional_communication_friend_bdc_l7_4,
        bidirectional_communication_friend_bdc_l7_5,
        bidirectional_communication_friend_bdc_l7_6,
        bidirectional_communication_friend_bdc_l7_7,
        bidirectional_communication_friend_bdc_l7_6plus,
        bidirectional_communication_friend_bdc_l7_5plus,
        bidirectional_communication_friend_bdc_l7_4plus,
        bidirectional_communication_friend_bdc_l7_3plus,
        bidirectional_communication_friend_bdc_l7_2plus,
        bidirectional_communication_friend_bdc_l7_1plus,
        bidirectional_communication_correspondent_app_l7_1,
        bidirectional_communication_correspondent_app_l7_2,
        bidirectional_communication_correspondent_app_l7_3,
        bidirectional_communication_correspondent_app_l7_4,
        bidirectional_communication_correspondent_app_l7_5,
        bidirectional_communication_correspondent_app_l7_6,
        bidirectional_communication_correspondent_app_l7_7,
        bidirectional_communication_correspondent_app_l7_increase_dbd,
        bidirectional_communication_correspondent_app_l7_increase_dbd_from0,
        bidirectional_communication_correspondent_app_l7_increase_dbd_from3minus,
        bidirectional_communication_correspondent_app_l7_increase_dbd_from7minus,
        bidirectional_communication_correspondent_app_l7_increase_dbd_to1plus,
        bidirectional_communication_correspondent_app_l7_increase_dbd_to3plus,
        bidirectional_communication_correspondent_app_l7_increase_dbd_to7plus,        
        bidirectional_communication_correspondent_app_l7_6plus,
        bidirectional_communication_correspondent_app_l7_5plus,
        bidirectional_communication_correspondent_app_l7_4plus,
        bidirectional_communication_correspondent_app_l7_3plus,
        bidirectional_communication_correspondent_app_l7_2plus,
        bidirectional_communication_correspondent_app_l7_1plus,        
        bidirectional_communication_correspondent_daily_1_bdc,
        bidirectional_communication_correspondent_daily_3_bdc,
        bidirectional_communication_correspondent_daily_bdc_increase_dbd,
        bidirectional_communication_correspondent_daily_bdc_increase_dbd_from0,
        bidirectional_communication_correspondent_daily_bdc_increase_dbd_from1minus,
        bidirectional_communication_correspondent_daily_bdc_increase_dbd_from3minus,
        bidirectional_communication_correspondent_daily_bdc_increase_dbd_to1plus,
        bidirectional_communication_correspondent_daily_bdc_increase_dbd_to3plus,
        bidirectional_communication_correspondent_new_user_per_session,
        bidirectional_communication_correspondent_inactive_0_day_per_session,
        bidirectional_communication_correspondent_inactive_1_plus_day_per_session,
        bidirectional_communication_correspondent_inactive_7_plus_day_per_session,
        bidirectional_communication_correspondent_inactive_30_plus_day_per_session,
        ],
        name="sendto_snap_send_recipient_breakdown",
        bq_dialect="standard"
    )
    return mt

def sendto_snap_send_relationship_breakdown(start_date, end_date):
    """
    SendTo snap send relationship (correspondent) breakdowns
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    source_table = """
    `sc-analytics.report_growth.stg_usr_send_communication_from_sendto_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    send_communication = Metric('send_communication', 'send_communication_correspondent', dist='cont', daily=True, cumulative=True)
    send_communication_correspondent_dau_user = Metric('send_communication_correspondent_dau_user', 'send_communication_correspondent_dau_user', dist='cont', daily=True, cumulative=True)
    send_communication_correspondent_non_dau_user = Metric('send_communication_correspondent_non_dau_user', 'send_communication_correspondent_non_dau_user', dist='cont', daily=True, cumulative=True)
    send_communication_correspondent_new_user = Metric('send_communication_correspondent_new_user', 'send_communication_correspondent_new_user', dist='cont', daily=True, cumulative=True)
    send_communication_correspondent_inactive_0_day = Metric('send_communication_correspondent_inactive_0_day', 'send_communication_correspondent_inactive_0_day', dist='cont', daily=True, cumulative=True)
    send_communication_correspondent_inactive_1_plus_day = Metric('send_communication_correspondent_inactive_1_plus_day', 'send_communication_correspondent_inactive_1_plus_day', dist='cont', daily=True, cumulative=True)
    send_communication_correspondent_inactive_3_plus_day = Metric('send_communication_correspondent_inactive_3_plus_day', 'send_communication_correspondent_inactive_3_plus_day', dist='cont', daily=True, cumulative=True)
    send_communication_correspondent_inactive_7_plus_day = Metric('send_communication_correspondent_inactive_7_plus_day', 'send_communication_correspondent_inactive_7_plus_day', dist='cont', daily=True, cumulative=True)
    send_communication_correspondent_inactive_30_plus_day = Metric('send_communication_correspondent_inactive_30_plus_day', 'send_communication_correspondent_inactive_30_plus_day', dist='cont', daily=True, cumulative=True)

    send_communication_from_sendto = Metric('send_communication_from_sendto', 'send_communication_from_sendto_correspondent', dist='cont', daily=True, cumulative=True)
    send_communication_from_sendto_correspondent_dau_user = Metric('send_communication_from_sendto_correspondent_dau_user', 'send_communication_from_sendto_correspondent_dau_user', dist='cont', daily=True, cumulative=True)
    send_communication_from_sendto_correspondent_non_dau_user = Metric('send_communication_from_sendto_correspondent_non_dau_user', 'send_communication_from_sendto_correspondent_non_dau_user', dist='cont', daily=True, cumulative=True)
    send_communication_from_sendto_correspondent_new_user = Metric('send_communication_from_sendto_correspondent_new_user', 'send_communication_from_sendto_correspondent_new_user', dist='cont', daily=True, cumulative=True)
    send_communication_from_sendto_correspondent_inactive_0_day = Metric('send_communication_from_sendto_correspondent_inactive_0_day', 'send_communication_from_sendto_correspondent_inactive_0_day', dist='cont', daily=True, cumulative=True)
    send_communication_from_sendto_correspondent_inactive_1_plus_day = Metric('send_communication_from_sendto_correspondent_inactive_1_plus_day', 'send_communication_from_sendto_correspondent_inactive_1_plus_day', dist='cont', daily=True, cumulative=True)
    send_communication_from_sendto_correspondent_inactive_3_plus_day = Metric('send_communication_from_sendto_correspondent_inactive_3_plus_day', 'send_communication_from_sendto_correspondent_inactive_3_plus_day', dist='cont', daily=True, cumulative=True)
    send_communication_from_sendto_correspondent_inactive_7_plus_day = Metric('send_communication_from_sendto_correspondent_inactive_7_plus_day', 'send_communication_from_sendto_correspondent_inactive_7_plus_day', dist='cont', daily=True, cumulative=True)
    send_communication_from_sendto_correspondent_inactive_30_plus_day = Metric('send_communication_from_sendto_correspondent_inactive_30_plus_day', 'send_communication_from_sendto_correspondent_inactive_30_plus_day', dist='cont', daily=True, cumulative=True)

    # "_per_user" means metrics are aggregated from user-level data, the suffix is used to differentiate from the sendto_snap_send_recipient_breakdown group
    bidirectional_communication_per_user = Metric('bidirectional_communication_per_user', 'bidirectional_communication_correspondent', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_correspondent_new_user_per_user = Metric('bidirectional_communication_correspondent_new_user_per_user', 'bidirectional_communication_correspondent_new_user', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_correspondent_inactive_0_day_per_user = Metric('bidirectional_communication_correspondent_inactive_0_day_per_user', 'bidirectional_communication_correspondent_inactive_0_day', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_correspondent_inactive_1_plus_day_per_user = Metric('bidirectional_communication_correspondent_inactive_1_plus_day_per_user', 'bidirectional_communication_correspondent_inactive_1_plus_day', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_correspondent_inactive_3_plus_day_per_user = Metric('bidirectional_communication_correspondent_inactive_3_plus_day_per_user', 'bidirectional_communication_correspondent_inactive_3_plus_day', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_correspondent_inactive_7_plus_day_per_user = Metric('bidirectional_communication_correspondent_inactive_7_plus_day_per_user', 'bidirectional_communication_correspondent_inactive_7_plus_day', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_correspondent_inactive_30_plus_day_per_user = Metric('bidirectional_communication_correspondent_inactive_30_plus_day_per_user', 'bidirectional_communication_correspondent_inactive_30_plus_day', dist='cont', daily=True, cumulative=True)

    bidirectional_communication_from_sendto = Metric('bidirectional_communication_from_sendto', 'bidirectional_communication_from_sendto_correspondent', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_from_sendto_correspondent_new_user = Metric('bidirectional_communication_from_sendto_correspondent_new_user', 'bidirectional_communication_from_sendto_correspondent_new_user', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_from_sendto_correspondent_inactive_0_day = Metric('bidirectional_communication_from_sendto_correspondent_inactive_0_day', 'bidirectional_communication_from_sendto_correspondent_inactive_0_day', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_from_sendto_correspondent_inactive_1_plus_day = Metric('bidirectional_communication_from_sendto_correspondent_inactive_1_plus_day', 'bidirectional_communication_from_sendto_correspondent_inactive_1_plus_day', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_from_sendto_correspondent_inactive_3_plus_day = Metric('bidirectional_communication_from_sendto_correspondent_inactive_3_plus_day', 'bidirectional_communication_from_sendto_correspondent_inactive_3_plus_day', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_from_sendto_correspondent_inactive_7_plus_day = Metric('bidirectional_communication_from_sendto_correspondent_inactive_7_plus_day', 'bidirectional_communication_from_sendto_correspondent_inactive_7_plus_day', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_from_sendto_correspondent_inactive_30_plus_day = Metric('bidirectional_communication_from_sendto_correspondent_inactive_30_plus_day', 'bidirectional_communication_from_sendto_correspondent_inactive_30_plus_day', dist='cont', daily=True, cumulative=True)

    send_communication_correspondent_l_7_equals_0_user = Metric('send_communication_correspondent_l_7_equals_0_user', 'send_communication_correspondent_l_7_equals_0_user', dist='cont', daily=True, cumulative=True)
    send_communication_correspondent_l_7_equals_1_user = Metric('send_communication_correspondent_l_7_equals_1_user', 'send_communication_correspondent_l_7_equals_1_user', dist='cont', daily=True, cumulative=True)
    send_communication_correspondent_l_7_equals_2_user = Metric('send_communication_correspondent_l_7_equals_2_user', 'send_communication_correspondent_l_7_equals_2_user', dist='cont', daily=True, cumulative=True)
    send_communication_correspondent_l_7_equals_3_user = Metric('send_communication_correspondent_l_7_equals_3_user', 'send_communication_correspondent_l_7_equals_3_user', dist='cont', daily=True, cumulative=True)
    send_communication_correspondent_l_7_equals_4_user = Metric('send_communication_correspondent_l_7_equals_4_user', 'send_communication_correspondent_l_7_equals_4_user', dist='cont', daily=True, cumulative=True)
    send_communication_correspondent_l_7_equals_5_user = Metric('send_communication_correspondent_l_7_equals_5_user', 'send_communication_correspondent_l_7_equals_5_user', dist='cont', daily=True, cumulative=True)
    send_communication_correspondent_l_7_equals_6_user = Metric('send_communication_correspondent_l_7_equals_6_user', 'send_communication_correspondent_l_7_equals_6_user', dist='cont', daily=True, cumulative=True)
    send_communication_correspondent_l_7_equals_7_user = Metric('send_communication_correspondent_l_7_equals_7_user', 'send_communication_correspondent_l_7_equals_7_user', dist='cont', daily=True, cumulative=True)

    send_communication_to_bdc_l7_equals_0_relationship = Metric('send_communication_to_bdc_l7_equals_0_relationship', 'send_communication_to_bdc_l7_equals_0_relationship', dist='cont', daily=True, cumulative=True)
    send_communication_to_bdc_l7_equals_1_relationship = Metric('send_communication_to_bdc_l7_equals_1_relationship', 'send_communication_to_bdc_l7_equals_1_relationship', dist='cont', daily=True, cumulative=True)
    send_communication_to_bdc_l7_equals_2_relationship = Metric('send_communication_to_bdc_l7_equals_2_relationship', 'send_communication_to_bdc_l7_equals_2_relationship', dist='cont', daily=True, cumulative=True)
    send_communication_to_bdc_l7_equals_3_relationship = Metric('send_communication_to_bdc_l7_equals_3_relationship', 'send_communication_to_bdc_l7_equals_3_relationship', dist='cont', daily=True, cumulative=True)
    send_communication_to_bdc_l7_equals_4_relationship = Metric('send_communication_to_bdc_l7_equals_4_relationship', 'send_communication_to_bdc_l7_equals_4_relationship', dist='cont', daily=True, cumulative=True)
    send_communication_to_bdc_l7_equals_5_relationship = Metric('send_communication_to_bdc_l7_equals_5_relationship', 'send_communication_to_bdc_l7_equals_5_relationship', dist='cont', daily=True, cumulative=True)
    send_communication_to_bdc_l7_equals_6_relationship = Metric('send_communication_to_bdc_l7_equals_6_relationship', 'send_communication_to_bdc_l7_equals_6_relationship', dist='cont', daily=True, cumulative=True)
    send_communication_to_bdc_l7_equals_7_relationship = Metric('send_communication_to_bdc_l7_equals_7_relationship', 'send_communication_to_bdc_l7_equals_7_relationship', dist='cont', daily=True, cumulative=True)

    send_communication_from_sendto_correspondent_l_7_equals_0_user = Metric('send_communication_from_sendto_correspondent_l_7_equals_0_user', 'send_communication_from_sendto_correspondent_l_7_equals_0_user', dist='cont', daily=True, cumulative=True)
    send_communication_from_sendto_correspondent_l_7_equals_1_user = Metric('send_communication_from_sendto_correspondent_l_7_equals_1_user', 'send_communication_from_sendto_correspondent_l_7_equals_1_user', dist='cont', daily=True, cumulative=True)
    send_communication_from_sendto_correspondent_l_7_equals_2_user = Metric('send_communication_from_sendto_correspondent_l_7_equals_2_user', 'send_communication_from_sendto_correspondent_l_7_equals_2_user', dist='cont', daily=True, cumulative=True)
    send_communication_from_sendto_correspondent_l_7_equals_3_user = Metric('send_communication_from_sendto_correspondent_l_7_equals_3_user', 'send_communication_from_sendto_correspondent_l_7_equals_3_user', dist='cont', daily=True, cumulative=True)
    send_communication_from_sendto_correspondent_l_7_equals_4_user = Metric('send_communication_from_sendto_correspondent_l_7_equals_4_user', 'send_communication_from_sendto_correspondent_l_7_equals_4_user', dist='cont', daily=True, cumulative=True)
    send_communication_from_sendto_correspondent_l_7_equals_5_user = Metric('send_communication_from_sendto_correspondent_l_7_equals_5_user', 'send_communication_from_sendto_correspondent_l_7_equals_5_user', dist='cont', daily=True, cumulative=True)
    send_communication_from_sendto_correspondent_l_7_equals_6_user = Metric('send_communication_from_sendto_correspondent_l_7_equals_6_user', 'send_communication_from_sendto_correspondent_l_7_equals_6_user', dist='cont', daily=True, cumulative=True)
    send_communication_from_sendto_correspondent_l_7_equals_7_user = Metric('send_communication_from_sendto_correspondent_l_7_equals_7_user', 'send_communication_from_sendto_correspondent_l_7_equals_7_user', dist='cont', daily=True, cumulative=True)

    send_communication_from_sendto_to_bdc_l7_equals_0_relationship = Metric('send_communication_from_sendto_to_bdc_l7_equals_0_relationship', 'send_communication_from_sendto_to_bdc_l7_equals_0_relationship', dist='cont', daily=True, cumulative=True)
    send_communication_from_sendto_to_bdc_l7_equals_1_relationship = Metric('send_communication_from_sendto_to_bdc_l7_equals_1_relationship', 'send_communication_from_sendto_to_bdc_l7_equals_1_relationship', dist='cont', daily=True, cumulative=True)
    send_communication_from_sendto_to_bdc_l7_equals_2_relationship = Metric('send_communication_from_sendto_to_bdc_l7_equals_2_relationship', 'send_communication_from_sendto_to_bdc_l7_equals_2_relationship', dist='cont', daily=True, cumulative=True)
    send_communication_from_sendto_to_bdc_l7_equals_3_relationship = Metric('send_communication_from_sendto_to_bdc_l7_equals_3_relationship', 'send_communication_from_sendto_to_bdc_l7_equals_3_relationship', dist='cont', daily=True, cumulative=True)
    send_communication_from_sendto_to_bdc_l7_equals_4_relationship = Metric('send_communication_from_sendto_to_bdc_l7_equals_4_relationship', 'send_communication_from_sendto_to_bdc_l7_equals_4_relationship', dist='cont', daily=True, cumulative=True)
    send_communication_from_sendto_to_bdc_l7_equals_5_relationship = Metric('send_communication_from_sendto_to_bdc_l7_equals_5_relationship', 'send_communication_from_sendto_to_bdc_l7_equals_5_relationship', dist='cont', daily=True, cumulative=True)
    send_communication_from_sendto_to_bdc_l7_equals_6_relationship = Metric('send_communication_from_sendto_to_bdc_l7_equals_6_relationship', 'send_communication_from_sendto_to_bdc_l7_equals_6_relationship', dist='cont', daily=True, cumulative=True)
    send_communication_from_sendto_to_bdc_l7_equals_7_relationship = Metric('send_communication_from_sendto_to_bdc_l7_equals_7_relationship', 'send_communication_from_sendto_to_bdc_l7_equals_7_relationship', dist='cont', daily=True, cumulative=True)
    
    # bidirectional_communication_correspondent_l_7_equals_0_user = Metric('bidirectional_communication_correspondent_l_7_equals_0_user', 'bidirectional_communication_correspondent_l_7_equals_0_user', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_correspondent_l_7_equals_1_user = Metric('bidirectional_communication_correspondent_l_7_equals_1_user', 'bidirectional_communication_correspondent_l_7_equals_1_user', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_correspondent_l_7_equals_2_user = Metric('bidirectional_communication_correspondent_l_7_equals_2_user', 'bidirectional_communication_correspondent_l_7_equals_2_user', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_correspondent_l_7_equals_3_user = Metric('bidirectional_communication_correspondent_l_7_equals_3_user', 'bidirectional_communication_correspondent_l_7_equals_3_user', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_correspondent_l_7_equals_4_user = Metric('bidirectional_communication_correspondent_l_7_equals_4_user', 'bidirectional_communication_correspondent_l_7_equals_4_user', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_correspondent_l_7_equals_5_user = Metric('bidirectional_communication_correspondent_l_7_equals_5_user', 'bidirectional_communication_correspondent_l_7_equals_5_user', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_correspondent_l_7_equals_6_user = Metric('bidirectional_communication_correspondent_l_7_equals_6_user', 'bidirectional_communication_correspondent_l_7_equals_6_user', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_correspondent_l_7_equals_7_user = Metric('bidirectional_communication_correspondent_l_7_equals_7_user', 'bidirectional_communication_correspondent_l_7_equals_7_user', dist='cont', daily=True, cumulative=True)

    # bidirectional_communication_to_bdc_l7_equals_0_relationship = Metric('bidirectional_communication_to_bdc_l7_equals_0_relationship', 'bidirectional_communication_to_bdc_l7_equals_0_relationship', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_to_bdc_l7_equals_1_relationship = Metric('bidirectional_communication_to_bdc_l7_equals_1_relationship', 'bidirectional_communication_to_bdc_l7_equals_1_relationship', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_to_bdc_l7_equals_2_relationship = Metric('bidirectional_communication_to_bdc_l7_equals_2_relationship', 'bidirectional_communication_to_bdc_l7_equals_2_relationship', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_to_bdc_l7_equals_3_relationship = Metric('bidirectional_communication_to_bdc_l7_equals_3_relationship', 'bidirectional_communication_to_bdc_l7_equals_3_relationship', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_to_bdc_l7_equals_4_relationship = Metric('bidirectional_communication_to_bdc_l7_equals_4_relationship', 'bidirectional_communication_to_bdc_l7_equals_4_relationship', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_to_bdc_l7_equals_5_relationship = Metric('bidirectional_communication_to_bdc_l7_equals_5_relationship', 'bidirectional_communication_to_bdc_l7_equals_5_relationship', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_to_bdc_l7_equals_6_relationship = Metric('bidirectional_communication_to_bdc_l7_equals_6_relationship', 'bidirectional_communication_to_bdc_l7_equals_6_relationship', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_to_bdc_l7_equals_7_relationship = Metric('bidirectional_communication_to_bdc_l7_equals_7_relationship', 'bidirectional_communication_to_bdc_l7_equals_7_relationship', dist='cont', daily=True, cumulative=True)
              

    #bidirectional_communication_from_sendto_correspondent_l_7_equals_0_user = Metric('bidirectional_communication_from_sendto_correspondent_l_7_equals_0_user', 'bidirectional_communication_from_sendto_correspondent_l_7_equals_0_user', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_from_sendto_correspondent_l_7_equals_1_user = Metric('bidirectional_communication_from_sendto_correspondent_l_7_equals_1_user', 'bidirectional_communication_from_sendto_correspondent_l_7_equals_1_user', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_from_sendto_correspondent_l_7_equals_2_user = Metric('bidirectional_communication_from_sendto_correspondent_l_7_equals_2_user', 'bidirectional_communication_from_sendto_correspondent_l_7_equals_2_user', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_from_sendto_correspondent_l_7_equals_3_user = Metric('bidirectional_communication_from_sendto_correspondent_l_7_equals_3_user', 'bidirectional_communication_from_sendto_correspondent_l_7_equals_3_user', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_from_sendto_correspondent_l_7_equals_4_user = Metric('bidirectional_communication_from_sendto_correspondent_l_7_equals_4_user', 'bidirectional_communication_from_sendto_correspondent_l_7_equals_4_user', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_from_sendto_correspondent_l_7_equals_5_user = Metric('bidirectional_communication_from_sendto_correspondent_l_7_equals_5_user', 'bidirectional_communication_from_sendto_correspondent_l_7_equals_5_user', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_from_sendto_correspondent_l_7_equals_6_user = Metric('bidirectional_communication_from_sendto_correspondent_l_7_equals_6_user', 'bidirectional_communication_from_sendto_correspondent_l_7_equals_6_user', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_from_sendto_correspondent_l_7_equals_7_user = Metric('bidirectional_communication_from_sendto_correspondent_l_7_equals_7_user', 'bidirectional_communication_from_sendto_correspondent_l_7_equals_7_user', dist='cont', daily=True, cumulative=True)

    #bidirectional_communication_from_sendto_to_bdc_l7_equals_0_relationship = Metric('bidirectional_communication_from_sendto_to_bdc_l7_equals_0_relationship', 'bidirectional_communication_from_sendto_to_bdc_l7_equals_0_relationship', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_from_sendto_to_bdc_l7_equals_1_relationship = Metric('bidirectional_communication_from_sendto_to_bdc_l7_equals_1_relationship', 'bidirectional_communication_from_sendto_to_bdc_l7_equals_1_relationship', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_from_sendto_to_bdc_l7_equals_2_relationship = Metric('bidirectional_communication_from_sendto_to_bdc_l7_equals_2_relationship', 'bidirectional_communication_from_sendto_to_bdc_l7_equals_2_relationship', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_from_sendto_to_bdc_l7_equals_3_relationship = Metric('bidirectional_communication_from_sendto_to_bdc_l7_equals_3_relationship', 'bidirectional_communication_from_sendto_to_bdc_l7_equals_3_relationship', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_from_sendto_to_bdc_l7_equals_4_relationship = Metric('bidirectional_communication_from_sendto_to_bdc_l7_equals_4_relationship', 'bidirectional_communication_from_sendto_to_bdc_l7_equals_4_relationship', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_from_sendto_to_bdc_l7_equals_5_relationship = Metric('bidirectional_communication_from_sendto_to_bdc_l7_equals_5_relationship', 'bidirectional_communication_from_sendto_to_bdc_l7_equals_5_relationship', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_from_sendto_to_bdc_l7_equals_6_relationship = Metric('bidirectional_communication_from_sendto_to_bdc_l7_equals_6_relationship', 'bidirectional_communication_from_sendto_to_bdc_l7_equals_6_relationship', dist='cont', daily=True, cumulative=True)
    bidirectional_communication_from_sendto_to_bdc_l7_equals_7_relationship = Metric('bidirectional_communication_from_sendto_to_bdc_l7_equals_7_relationship', 'bidirectional_communication_from_sendto_to_bdc_l7_equals_7_relationship', dist='cont', daily=True, cumulative=True)             
    mt = MetricTable(
        sql="""
    SELECT
    PARSE_TIMESTAMP('%Y%m%d', _table_suffix) AS ts,
    send_ghost_user_id AS ghost_user_id,
    send_communication,
    send_communication_correspondent_dau_user,
    send_communication_correspondent_non_dau_user,
    send_communication_correspondent_new_user,
    send_communication_correspondent_inactive_0_day,
    send_communication_correspondent_inactive_1_plus_day,
    send_communication_correspondent_inactive_3_plus_day,
    send_communication_correspondent_inactive_7_plus_day,
    send_communication_correspondent_inactive_30_plus_day,
    send_communication_correspondent_l_7_equals_0_user,
    send_communication_correspondent_l_7_equals_1_user,
    send_communication_correspondent_l_7_equals_2_user,
    send_communication_correspondent_l_7_equals_3_user,
    send_communication_correspondent_l_7_equals_4_user,
    send_communication_correspondent_l_7_equals_5_user,
    send_communication_correspondent_l_7_equals_6_user,
    send_communication_correspondent_l_7_equals_7_user,
    send_communication_to_bdc_l7_equals_0_relationship,
    send_communication_to_bdc_l7_equals_1_relationship,
    send_communication_to_bdc_l7_equals_2_relationship,
    send_communication_to_bdc_l7_equals_3_relationship,
    send_communication_to_bdc_l7_equals_4_relationship,
    send_communication_to_bdc_l7_equals_5_relationship,
    send_communication_to_bdc_l7_equals_6_relationship,
    send_communication_to_bdc_l7_equals_7_relationship,
    send_communication_from_sendto,
    send_communication_from_sendto_correspondent_dau_user,
    send_communication_from_sendto_correspondent_non_dau_user,
    send_communication_from_sendto_correspondent_new_user,
    send_communication_from_sendto_correspondent_inactive_0_day,
    send_communication_from_sendto_correspondent_inactive_1_plus_day,
    send_communication_from_sendto_correspondent_inactive_3_plus_day,
    send_communication_from_sendto_correspondent_inactive_7_plus_day,
    send_communication_from_sendto_correspondent_inactive_30_plus_day,
    send_communication_from_sendto_correspondent_l_7_equals_0_user,
    send_communication_from_sendto_correspondent_l_7_equals_1_user,
    send_communication_from_sendto_correspondent_l_7_equals_2_user,
    send_communication_from_sendto_correspondent_l_7_equals_3_user,
    send_communication_from_sendto_correspondent_l_7_equals_4_user,
    send_communication_from_sendto_correspondent_l_7_equals_5_user,
    send_communication_from_sendto_correspondent_l_7_equals_6_user,
    send_communication_from_sendto_correspondent_l_7_equals_7_user,
    send_communication_from_sendto_to_bdc_l7_equals_0_relationship,
    send_communication_from_sendto_to_bdc_l7_equals_1_relationship,
    send_communication_from_sendto_to_bdc_l7_equals_2_relationship,
    send_communication_from_sendto_to_bdc_l7_equals_3_relationship,
    send_communication_from_sendto_to_bdc_l7_equals_4_relationship,
    send_communication_from_sendto_to_bdc_l7_equals_5_relationship,
    send_communication_from_sendto_to_bdc_l7_equals_6_relationship,
    send_communication_from_sendto_to_bdc_l7_equals_7_relationship,
    bidirectional_communication AS bidirectional_communication_per_user,
    bidirectional_communication_correspondent_new_user AS bidirectional_communication_correspondent_new_user_per_user,
    bidirectional_communication_correspondent_inactive_0_day AS bidirectional_communication_correspondent_inactive_0_day_per_user,
    bidirectional_communication_correspondent_inactive_1_plus_day AS bidirectional_communication_correspondent_inactive_1_plus_day_per_user,
    bidirectional_communication_correspondent_inactive_3_plus_day AS bidirectional_communication_correspondent_inactive_3_plus_day_per_user,
    bidirectional_communication_correspondent_inactive_7_plus_day AS bidirectional_communication_correspondent_inactive_7_plus_day_per_user,
    bidirectional_communication_correspondent_inactive_30_plus_day AS bidirectional_communication_correspondent_inactive_30_plus_day_per_user,
    #bidirectional_communication_correspondent_l_7_equals_0_user,
    bidirectional_communication_correspondent_l_7_equals_1_user,
    bidirectional_communication_correspondent_l_7_equals_2_user,
    bidirectional_communication_correspondent_l_7_equals_3_user,
    bidirectional_communication_correspondent_l_7_equals_4_user,
    bidirectional_communication_correspondent_l_7_equals_5_user,
    bidirectional_communication_correspondent_l_7_equals_6_user,
    bidirectional_communication_correspondent_l_7_equals_7_user,
    #bidirectional_communication_to_bdc_l7_equals_0_relationship,
    bidirectional_communication_to_bdc_l7_equals_1_relationship,
    bidirectional_communication_to_bdc_l7_equals_2_relationship,
    bidirectional_communication_to_bdc_l7_equals_3_relationship,
    bidirectional_communication_to_bdc_l7_equals_4_relationship,
    bidirectional_communication_to_bdc_l7_equals_5_relationship,
    bidirectional_communication_to_bdc_l7_equals_6_relationship,
    bidirectional_communication_to_bdc_l7_equals_7_relationship,
    bidirectional_communication_from_sendto,
    bidirectional_communication_from_sendto_correspondent_new_user,
    bidirectional_communication_from_sendto_correspondent_inactive_0_day,
    bidirectional_communication_from_sendto_correspondent_inactive_1_plus_day,
    bidirectional_communication_from_sendto_correspondent_inactive_3_plus_day,
    bidirectional_communication_from_sendto_correspondent_inactive_7_plus_day,
    bidirectional_communication_from_sendto_correspondent_inactive_30_plus_day,
    #bidirectional_communication_from_sendto_correspondent_l_7_equals_0_user,
    bidirectional_communication_from_sendto_correspondent_l_7_equals_1_user,
    bidirectional_communication_from_sendto_correspondent_l_7_equals_2_user,
    bidirectional_communication_from_sendto_correspondent_l_7_equals_3_user,
    bidirectional_communication_from_sendto_correspondent_l_7_equals_4_user,
    bidirectional_communication_from_sendto_correspondent_l_7_equals_5_user,
    bidirectional_communication_from_sendto_correspondent_l_7_equals_6_user,
    bidirectional_communication_from_sendto_correspondent_l_7_equals_7_user,
    #bidirectional_communication_from_sendto_to_bdc_l7_equals_0_relationship,
    bidirectional_communication_from_sendto_to_bdc_l7_equals_1_relationship,
    bidirectional_communication_from_sendto_to_bdc_l7_equals_2_relationship,
    bidirectional_communication_from_sendto_to_bdc_l7_equals_3_relationship,
    bidirectional_communication_from_sendto_to_bdc_l7_equals_4_relationship,
    bidirectional_communication_from_sendto_to_bdc_l7_equals_5_relationship,
    bidirectional_communication_from_sendto_to_bdc_l7_equals_6_relationship,
    bidirectional_communication_from_sendto_to_bdc_l7_equals_7_relationship,
    FROM
        {source_table}
    """.format(source_table=source_table),
        
    metrics=[
        send_communication,
        send_communication_correspondent_dau_user,
        send_communication_correspondent_non_dau_user, 
        send_communication_correspondent_new_user,
        send_communication_correspondent_inactive_0_day,
        send_communication_correspondent_inactive_1_plus_day,
        send_communication_correspondent_inactive_3_plus_day,
        send_communication_correspondent_inactive_7_plus_day,
        send_communication_correspondent_inactive_30_plus_day,

        send_communication_correspondent_l_7_equals_0_user,
        send_communication_correspondent_l_7_equals_1_user,
        send_communication_correspondent_l_7_equals_2_user,
        send_communication_correspondent_l_7_equals_3_user,
        send_communication_correspondent_l_7_equals_4_user,
        send_communication_correspondent_l_7_equals_5_user,
        send_communication_correspondent_l_7_equals_6_user,
        send_communication_correspondent_l_7_equals_7_user,

        send_communication_to_bdc_l7_equals_0_relationship,
        send_communication_to_bdc_l7_equals_1_relationship,
        send_communication_to_bdc_l7_equals_2_relationship,
        send_communication_to_bdc_l7_equals_3_relationship,
        send_communication_to_bdc_l7_equals_4_relationship,
        send_communication_to_bdc_l7_equals_5_relationship,
        send_communication_to_bdc_l7_equals_6_relationship,
        send_communication_to_bdc_l7_equals_7_relationship,

        send_communication_from_sendto,
        send_communication_from_sendto_correspondent_dau_user,
        send_communication_from_sendto_correspondent_non_dau_user, 
        send_communication_from_sendto_correspondent_new_user,
        send_communication_from_sendto_correspondent_inactive_0_day,
        send_communication_from_sendto_correspondent_inactive_1_plus_day,
        send_communication_from_sendto_correspondent_inactive_3_plus_day,
        send_communication_from_sendto_correspondent_inactive_7_plus_day,
        send_communication_from_sendto_correspondent_inactive_30_plus_day,

        send_communication_from_sendto_correspondent_l_7_equals_0_user,
        send_communication_from_sendto_correspondent_l_7_equals_1_user,
        send_communication_from_sendto_correspondent_l_7_equals_2_user,
        send_communication_from_sendto_correspondent_l_7_equals_3_user,
        send_communication_from_sendto_correspondent_l_7_equals_4_user,
        send_communication_from_sendto_correspondent_l_7_equals_5_user,
        send_communication_from_sendto_correspondent_l_7_equals_6_user,
        send_communication_from_sendto_correspondent_l_7_equals_7_user,

        send_communication_from_sendto_to_bdc_l7_equals_0_relationship,
        send_communication_from_sendto_to_bdc_l7_equals_1_relationship,
        send_communication_from_sendto_to_bdc_l7_equals_2_relationship,
        send_communication_from_sendto_to_bdc_l7_equals_3_relationship,
        send_communication_from_sendto_to_bdc_l7_equals_4_relationship,
        send_communication_from_sendto_to_bdc_l7_equals_5_relationship,
        send_communication_from_sendto_to_bdc_l7_equals_6_relationship,
        send_communication_from_sendto_to_bdc_l7_equals_7_relationship,

        bidirectional_communication_per_user,
        bidirectional_communication_correspondent_new_user_per_user,
        bidirectional_communication_correspondent_inactive_0_day_per_user,
        bidirectional_communication_correspondent_inactive_1_plus_day_per_user,
        bidirectional_communication_correspondent_inactive_3_plus_day_per_user,
        bidirectional_communication_correspondent_inactive_7_plus_day_per_user,
        bidirectional_communication_correspondent_inactive_30_plus_day_per_user,

        #bidirectional_communication_correspondent_l_7_equals_0_user,
        bidirectional_communication_correspondent_l_7_equals_1_user,
        bidirectional_communication_correspondent_l_7_equals_2_user,
        bidirectional_communication_correspondent_l_7_equals_3_user,
        bidirectional_communication_correspondent_l_7_equals_4_user,
        bidirectional_communication_correspondent_l_7_equals_5_user,
        bidirectional_communication_correspondent_l_7_equals_6_user,
        bidirectional_communication_correspondent_l_7_equals_7_user,

        #bidirectional_communication_to_bdc_l7_equals_0_relationship,
        bidirectional_communication_to_bdc_l7_equals_1_relationship,
        bidirectional_communication_to_bdc_l7_equals_2_relationship,
        bidirectional_communication_to_bdc_l7_equals_3_relationship,
        bidirectional_communication_to_bdc_l7_equals_4_relationship,
        bidirectional_communication_to_bdc_l7_equals_5_relationship,
        bidirectional_communication_to_bdc_l7_equals_6_relationship,
        bidirectional_communication_to_bdc_l7_equals_7_relationship,

        bidirectional_communication_from_sendto,
        bidirectional_communication_from_sendto_correspondent_new_user,
        bidirectional_communication_from_sendto_correspondent_inactive_0_day,
        bidirectional_communication_from_sendto_correspondent_inactive_1_plus_day,
        bidirectional_communication_from_sendto_correspondent_inactive_3_plus_day,
        bidirectional_communication_from_sendto_correspondent_inactive_7_plus_day,
        bidirectional_communication_from_sendto_correspondent_inactive_30_plus_day,

        #bidirectional_communication_from_sendto_correspondent_l_7_equals_0_user,
        bidirectional_communication_from_sendto_correspondent_l_7_equals_1_user,
        bidirectional_communication_from_sendto_correspondent_l_7_equals_2_user,
        bidirectional_communication_from_sendto_correspondent_l_7_equals_3_user,
        bidirectional_communication_from_sendto_correspondent_l_7_equals_4_user,
        bidirectional_communication_from_sendto_correspondent_l_7_equals_5_user,
        bidirectional_communication_from_sendto_correspondent_l_7_equals_6_user,
        bidirectional_communication_from_sendto_correspondent_l_7_equals_7_user,

        #bidirectional_communication_from_sendto_to_bdc_l7_equals_0_relationship,
        bidirectional_communication_from_sendto_to_bdc_l7_equals_1_relationship,
        bidirectional_communication_from_sendto_to_bdc_l7_equals_2_relationship,
        bidirectional_communication_from_sendto_to_bdc_l7_equals_3_relationship,
        bidirectional_communication_from_sendto_to_bdc_l7_equals_4_relationship,
        bidirectional_communication_from_sendto_to_bdc_l7_equals_5_relationship,
        bidirectional_communication_from_sendto_to_bdc_l7_equals_6_relationship,
        bidirectional_communication_from_sendto_to_bdc_l7_equals_7_relationship,                
        ],
        name="sendto_snap_send_relationship_breakdown",
        bq_dialect="standard"
    )
    return mt

def recents_ranking_snapchatter_item_lvl_metrics(start_date, end_date):
    """
    Item-level metrics for evaluating Snapchatter Recents ranking
    """

    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
    `sc-analytics.report_growth.sendto_session_lvl_recent_snapchatter_ir_metrics_2*`
    WHERE CONCAT('2', _table_suffix) between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    n_recents_snapchatter_impressed = Metric('n_recents_snapchatter_impressed', 'n_recents_snapchatter_impressed', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_impressed_top5 = Metric('n_recents_snapchatter_impressed_top5', 'n_recents_snapchatter_impressed_top5', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_impressed_top8 = Metric('n_recents_snapchatter_impressed_top8', 'n_recents_snapchatter_impressed_top8', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_impressed_top10 = Metric('n_recents_snapchatter_impressed_top10', 'n_recents_snapchatter_impressed_top10', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_impressed_top20 = Metric('n_recents_snapchatter_impressed_top20', 'n_recents_snapchatter_impressed_top20', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_impressed_inactive = Metric('n_recents_snapchatter_impressed_inactive', 'n_recents_snapchatter_impressed_inactive', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_impressed_inactive_top5 = Metric('n_recents_snapchatter_impressed_inactive_top5', 'n_recents_snapchatter_impressed_inactive_top5', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_impressed_inactive_top8 = Metric('n_recents_snapchatter_impressed_inactive_top8', 'n_recents_snapchatter_impressed_inactive_top8', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_impressed_inactive_top10 = Metric('n_recents_snapchatter_impressed_inactive_top10', 'n_recents_snapchatter_impressed_inactive_top10', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_impressed_inactive_top20 = Metric('n_recents_snapchatter_impressed_inactive_top20', 'n_recents_snapchatter_impressed_inactive_top20', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_impressed_ria = Metric('n_recents_snapchatter_impressed_ria', 'n_recents_snapchatter_impressed_ria', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_impressed_ria_top5 = Metric('n_recents_snapchatter_impressed_ria_top5', 'n_recents_snapchatter_impressed_ria_top5', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_impressed_ria_top8 = Metric('n_recents_snapchatter_impressed_ria_top8', 'n_recents_snapchatter_impressed_ria_top8', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_impressed_ria_top10 = Metric('n_recents_snapchatter_impressed_ria_top10', 'n_recents_snapchatter_impressed_ria_top10', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_impressed_ria_top20 = Metric('n_recents_snapchatter_impressed_ria_top20', 'n_recents_snapchatter_impressed_ria_top20', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_impressed_streak_expiring = Metric('n_recents_snapchatter_impressed_streak_expiring', 'n_recents_snapchatter_impressed_streak_expiring', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_impressed_streak_expiring_top5 = Metric('n_recents_snapchatter_impressed_streak_expiring_top5', 'n_recents_snapchatter_impressed_streak_expiring_top5', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_impressed_streak_expiring_top8 = Metric('n_recents_snapchatter_impressed_streak_expiring_top8', 'n_recents_snapchatter_impressed_streak_expiring_top8', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_impressed_streak_expiring_top10 = Metric('n_recents_snapchatter_impressed_streak_expiring_top10', 'n_recents_snapchatter_impressed_streak_expiring_top10', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_impressed_streak_expiring_top20 = Metric('n_recents_snapchatter_impressed_streak_expiring_top20', 'n_recents_snapchatter_impressed_streak_expiring_top20', dist='cont', daily=True, cumulative=True,)    

    n_recents_snapchatter_selected = Metric('n_recents_snapchatter_selected', 'n_recents_snapchatter_selected', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_selected_top5 = Metric('n_recents_snapchatter_selected_top5', 'n_recents_snapchatter_selected_top5', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_selected_top8 = Metric('n_recents_snapchatter_selected_top8', 'n_recents_snapchatter_selected_top8', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_selected_top10 = Metric('n_recents_snapchatter_selected_top10', 'n_recents_snapchatter_selected_top10', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_selected_top20 = Metric('n_recents_snapchatter_selected_top20', 'n_recents_snapchatter_selected_top20', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_selected_inactive = Metric('n_recents_snapchatter_selected_inactive', 'n_recents_snapchatter_selected_inactive', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_selected_inactive_top5 = Metric('n_recents_snapchatter_selected_inactive_top5', 'n_recents_snapchatter_selected_inactive_top5', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_selected_inactive_top8 = Metric('n_recents_snapchatter_selected_inactive_top8', 'n_recents_snapchatter_selected_inactive_top8', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_selected_inactive_top10 = Metric('n_recents_snapchatter_selected_inactive_top10', 'n_recents_snapchatter_selected_inactive_top10', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_selected_inactive_top20 = Metric('n_recents_snapchatter_selected_inactive_top20', 'n_recents_snapchatter_selected_inactive_top20', dist='cont', daily=True, cumulative=True,)
    n_recents_snapchatter_selected_ria = Metric('n_recents_snapchatter_selected_ria', 'n_recents_snapchatter_selected_ria', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_selected_ria_top5 = Metric('n_recents_snapchatter_selected_ria_top5', 'n_recents_snapchatter_selected_ria_top5', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_selected_ria_top8 = Metric('n_recents_snapchatter_selected_ria_top8', 'n_recents_snapchatter_selected_ria_top8', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_selected_ria_top10 = Metric('n_recents_snapchatter_selected_ria_top10', 'n_recents_snapchatter_selected_ria_top10', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_selected_ria_top20 = Metric('n_recents_snapchatter_selected_ria_top20', 'n_recents_snapchatter_selected_ria_top20', dist='cont', daily=True, cumulative=True,)
    n_recents_snapchatter_selected_streak_expiring = Metric('n_recents_snapchatter_selected_streak_expiring', 'n_recents_snapchatter_selected_streak_expiring', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_selected_streak_expiring_top5 = Metric('n_recents_snapchatter_selected_streak_expiring_top5', 'n_recents_snapchatter_selected_streak_expiring_top5', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_selected_streak_expiring_top8 = Metric('n_recents_snapchatter_selected_streak_expiring_top8', 'n_recents_snapchatter_selected_streak_expiring_top8', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_selected_streak_expiring_top10 = Metric('n_recents_snapchatter_selected_streak_expiring_top10', 'n_recents_snapchatter_selected_streak_expiring_top10', dist='cont', daily=True, cumulative=True,)    
    n_recents_snapchatter_selected_streak_expiring_top20 = Metric('n_recents_snapchatter_selected_streak_expiring_top20', 'n_recents_snapchatter_selected_streak_expiring_top20', dist='cont', daily=True, cumulative=True,)

    recents_snapchatter_ctr = Metric(col='recents_snapchatter_ctr', numerator='n_recents_snapchatter_selected', denominator='n_recents_snapchatter_impressed', dist='ratio')
    recents_snapchatter_ctr_top5 = Metric(col='recents_snapchatter_ctr_top5', numerator='n_recents_snapchatter_selected_top5', denominator='n_recents_snapchatter_impressed_top5', dist='ratio')
    recents_snapchatter_ctr_top8 = Metric(col='recents_snapchatter_ctr_top8', numerator='n_recents_snapchatter_selected_top8', denominator='n_recents_snapchatter_impressed_top8', dist='ratio')
    recents_snapchatter_ctr_top10 = Metric(col='recents_snapchatter_ctr_top10', numerator='n_recents_snapchatter_selected_top10', denominator='n_recents_snapchatter_impressed_top10', dist='ratio')
    recents_snapchatter_ctr_top20 = Metric(col='recents_snapchatter_ctr_top20', numerator='n_recents_snapchatter_selected_top20', denominator='n_recents_snapchatter_impressed_top20', dist='ratio')
    recents_snapchatter_ctr_inactive = Metric(col='recents_snapchatter_ctr_inactive', numerator='n_recents_snapchatter_selected_inactive', denominator='n_recents_snapchatter_impressed_inactive', dist='ratio')
    recents_snapchatter_ctr_inactive_top5 = Metric(col='recents_snapchatter_ctr_inactive_top5', numerator='n_recents_snapchatter_selected_inactive_top5', denominator='n_recents_snapchatter_impressed_inactive_top5', dist='ratio')
    recents_snapchatter_ctr_inactive_top8 = Metric(col='recents_snapchatter_ctr_inactive_top8', numerator='n_recents_snapchatter_selected_inactive_top8', denominator='n_recents_snapchatter_impressed_inactive_top8', dist='ratio')
    recents_snapchatter_ctr_inactive_top10 = Metric(col='recents_snapchatter_ctr_inactive_top10', numerator='n_recents_snapchatter_selected_inactive_top10', denominator='n_recents_snapchatter_impressed_inactive_top10', dist='ratio')
    recents_snapchatter_ctr_inactive_top20 = Metric(col='recents_snapchatter_ctr_inactive_top20', numerator='n_recents_snapchatter_selected_inactive_top20', denominator='n_recents_snapchatter_impressed_inactive_top20', dist='ratio')
    recents_snapchatter_ctr_ria = Metric(col='recents_snapchatter_ctr_ria', numerator='n_recents_snapchatter_selected_ria', denominator='n_recents_snapchatter_impressed_ria', dist='ratio')
    recents_snapchatter_ctr_ria_top5 = Metric(col='recents_snapchatter_ctr_ria_top5', numerator='n_recents_snapchatter_selected_ria_top5', denominator='n_recents_snapchatter_impressed_ria_top5', dist='ratio')
    recents_snapchatter_ctr_ria_top8 = Metric(col='recents_snapchatter_ctr_ria_top8', numerator='n_recents_snapchatter_selected_ria_top8', denominator='n_recents_snapchatter_impressed_ria_top8', dist='ratio')
    recents_snapchatter_ctr_ria_top10 = Metric(col='recents_snapchatter_ctr_ria_top10', numerator='n_recents_snapchatter_selected_ria_top10', denominator='n_recents_snapchatter_impressed_ria_top10', dist='ratio')
    recents_snapchatter_ctr_ria_top20 = Metric(col='recents_snapchatter_ctr_ria_top20', numerator='n_recents_snapchatter_selected_ria_top20', denominator='n_recents_snapchatter_impressed_ria_top20', dist='ratio')
    recents_snapchatter_ctr_streak_expiring = Metric(col='recents_snapchatter_ctr_streak_expiring', numerator='n_recents_snapchatter_selected_streak_expiring', denominator='n_recents_snapchatter_impressed_streak_expiring', dist='ratio')
    recents_snapchatter_ctr_streak_expiring_top5 = Metric(col='recents_snapchatter_ctr_streak_expiring_top5', numerator='n_recents_snapchatter_selected_streak_expiring_top5', denominator='n_recents_snapchatter_impressed_streak_expiring_top5', dist='ratio')
    recents_snapchatter_ctr_streak_expiring_top8 = Metric(col='recents_snapchatter_ctr_streak_expiring_top8', numerator='n_recents_snapchatter_selected_streak_expiring_top8', denominator='n_recents_snapchatter_impressed_streak_expiring_top8', dist='ratio')
    recents_snapchatter_ctr_streak_expiring_top10 = Metric(col='recents_snapchatter_ctr_streak_expiring_top10', numerator='n_recents_snapchatter_selected_streak_expiring_top10', denominator='n_recents_snapchatter_impressed_streak_expiring_top10', dist='ratio')
    recents_snapchatter_ctr_streak_expiring_top20 = Metric(col='recents_snapchatter_ctr_streak_expiring_top20', numerator='n_recents_snapchatter_selected_streak_expiring_top20', denominator='n_recents_snapchatter_impressed_streak_expiring_top20', dist='ratio')

    mt = MetricTable(
        sql="""
        SELECT distinct
        PARSE_TIMESTAMP('%Y%m%d', CONCAT('2', _table_suffix)) as ts,
        ghost_user_id,
        sum(n_result_impressed) as n_recents_snapchatter_impressed,
        sum(n_result_impressed_top5) as n_recents_snapchatter_impressed_top5,
        sum(n_result_impressed_top8) as n_recents_snapchatter_impressed_top8,
        sum(n_result_impressed_top10) as n_recents_snapchatter_impressed_top10,
        sum(n_result_impressed_top20) as n_recents_snapchatter_impressed_top20,
        sum(n_result_impressed_inactive) as n_recents_snapchatter_impressed_inactive,
        sum(n_result_impressed_inactive_top5) as n_recents_snapchatter_impressed_inactive_top5,
        sum(n_result_impressed_inactive_top8) as n_recents_snapchatter_impressed_inactive_top8,
        sum(n_result_impressed_inactive_top10) as n_recents_snapchatter_impressed_inactive_top10,
        sum(n_result_impressed_inactive_top20) as n_recents_snapchatter_impressed_inactive_top20,
        sum(n_result_impressed_ria) as n_recents_snapchatter_impressed_ria,
        sum(n_result_impressed_ria_top5) as n_recents_snapchatter_impressed_ria_top5,
        sum(n_result_impressed_ria_top8) as n_recents_snapchatter_impressed_ria_top8,
        sum(n_result_impressed_ria_top10) as n_recents_snapchatter_impressed_ria_top10,
        sum(n_result_impressed_ria_top20) as n_recents_snapchatter_impressed_ria_top20,
        sum(n_result_impressed_streak_expiring) as n_recents_snapchatter_impressed_streak_expiring,
        sum(n_result_impressed_streak_expiring_top5) as n_recents_snapchatter_impressed_streak_expiring_top5,
        sum(n_result_impressed_streak_expiring_top8) as n_recents_snapchatter_impressed_streak_expiring_top8,
        sum(n_result_impressed_streak_expiring_top10) as n_recents_snapchatter_impressed_streak_expiring_top10,
        sum(n_result_impressed_streak_expiring_top20) as n_recents_snapchatter_impressed_streak_expiring_top20,

        sum(n_result_selected) as n_recents_snapchatter_selected,
        sum(n_result_selected_top5) as n_recents_snapchatter_selected_top5,
        sum(n_result_selected_top8) as n_recents_snapchatter_selected_top8,
        sum(n_result_selected_top10) as n_recents_snapchatter_selected_top10,
        sum(n_result_selected_top20) as n_recents_snapchatter_selected_top20,
        sum(n_result_selected_inactive) as n_recents_snapchatter_selected_inactive,
        sum(n_result_selected_inactive_top5) as n_recents_snapchatter_selected_inactive_top5,
        sum(n_result_selected_inactive_top8) as n_recents_snapchatter_selected_inactive_top8,
        sum(n_result_selected_inactive_top10) as n_recents_snapchatter_selected_inactive_top10,
        sum(n_result_selected_inactive_top20) as n_recents_snapchatter_selected_inactive_top20,
        sum(n_result_selected_ria) as n_recents_snapchatter_selected_ria,
        sum(n_result_selected_ria_top5) as n_recents_snapchatter_selected_ria_top5,
        sum(n_result_selected_ria_top8) as n_recents_snapchatter_selected_ria_top8,
        sum(n_result_selected_ria_top10) as n_recents_snapchatter_selected_ria_top10,
        sum(n_result_selected_ria_top20) as n_recents_snapchatter_selected_ria_top20,
        sum(n_result_selected_streak_expiring) as n_recents_snapchatter_selected_streak_expiring,
        sum(n_result_selected_streak_expiring_top5) as n_recents_snapchatter_selected_streak_expiring_top5,
        sum(n_result_selected_streak_expiring_top8) as n_recents_snapchatter_selected_streak_expiring_top8,
        sum(n_result_selected_streak_expiring_top10) as n_recents_snapchatter_selected_streak_expiring_top10,
        sum(n_result_selected_streak_expiring_top20) as n_recents_snapchatter_selected_streak_expiring_top20,
 
        from {source_table}
        group by 1, 2
        """.format(source_table=source_table),

        metrics=[
            n_recents_snapchatter_impressed, 
            n_recents_snapchatter_impressed_top5, 
            n_recents_snapchatter_impressed_top8, 
            n_recents_snapchatter_impressed_top10, 
            n_recents_snapchatter_impressed_top20, 
            n_recents_snapchatter_selected, 
            n_recents_snapchatter_selected_top5, 
            n_recents_snapchatter_selected_top8, 
            n_recents_snapchatter_selected_top10, 
            n_recents_snapchatter_selected_top20, 
            recents_snapchatter_ctr, 
            recents_snapchatter_ctr_top5, 
            recents_snapchatter_ctr_top8, 
            recents_snapchatter_ctr_top10, 
            recents_snapchatter_ctr_top20, 

            n_recents_snapchatter_impressed_inactive, 
            n_recents_snapchatter_impressed_inactive_top5, 
            n_recents_snapchatter_impressed_inactive_top8, 
            n_recents_snapchatter_impressed_inactive_top10, 
            n_recents_snapchatter_impressed_inactive_top20, 
            n_recents_snapchatter_selected_inactive, 
            n_recents_snapchatter_selected_inactive_top5, 
            n_recents_snapchatter_selected_inactive_top8, 
            n_recents_snapchatter_selected_inactive_top10, 
            n_recents_snapchatter_selected_inactive_top20,
            recents_snapchatter_ctr_inactive, 
            recents_snapchatter_ctr_inactive_top5, 
            recents_snapchatter_ctr_inactive_top8, 
            recents_snapchatter_ctr_inactive_top10, 
            recents_snapchatter_ctr_inactive_top20,

            n_recents_snapchatter_impressed_ria, 
            n_recents_snapchatter_impressed_ria_top5, 
            n_recents_snapchatter_impressed_ria_top8, 
            n_recents_snapchatter_impressed_ria_top10, 
            n_recents_snapchatter_impressed_ria_top20, 
            n_recents_snapchatter_selected_ria, 
            n_recents_snapchatter_selected_ria_top5, 
            n_recents_snapchatter_selected_ria_top8, 
            n_recents_snapchatter_selected_ria_top10, 
            n_recents_snapchatter_selected_ria_top20,
            recents_snapchatter_ctr_ria, 
            recents_snapchatter_ctr_ria_top5, 
            recents_snapchatter_ctr_ria_top8, 
            recents_snapchatter_ctr_ria_top10, 
            recents_snapchatter_ctr_ria_top20,                                        
            n_recents_snapchatter_impressed_streak_expiring, 
            n_recents_snapchatter_impressed_streak_expiring_top5, 
            n_recents_snapchatter_impressed_streak_expiring_top8, 
            n_recents_snapchatter_impressed_streak_expiring_top10, 
            n_recents_snapchatter_impressed_streak_expiring_top20, 
            n_recents_snapchatter_selected_streak_expiring, 
            n_recents_snapchatter_selected_streak_expiring_top5, 
            n_recents_snapchatter_selected_streak_expiring_top8, 
            n_recents_snapchatter_selected_streak_expiring_top10, 
            n_recents_snapchatter_selected_streak_expiring_top20,
            recents_snapchatter_ctr_streak_expiring, 
            recents_snapchatter_ctr_streak_expiring_top5, 
            recents_snapchatter_ctr_streak_expiring_top8, 
            recents_snapchatter_ctr_streak_expiring_top10, 
            recents_snapchatter_ctr_streak_expiring_top20
        ],
        name="recents_ranking_snapchatter_item_lvl_metrics",
        bq_dialect="standard"
    )
    return mt

def recents_ranking_snapchatter_session_lvl_metrics(start_date, end_date):
    """
    Session-level metrics for evaluating Snapchatter Recents ranking
    """

    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
    `sc-analytics.report_growth.sendto_session_lvl_recent_snapchatter_ir_metrics_2*`
    WHERE CONCAT('2', _table_suffix) between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    n_session_with_snapchatter_impression = Metric('n_session_with_snapchatter_impression', 'n_session_with_snapchatter_impression', dist='cont', daily=True, cumulative=True,)

    n_session_with_snapchatter_selection = Metric('n_session_with_snapchatter_selection', 'n_session_with_snapchatter_selection', dist='cont', daily=True, cumulative=True,)
    pct_session_with_snapchatter_selection = Metric(col='pct_session_with_snapchatter_selection', numerator='n_session_with_snapchatter_selection', denominator='n_session_with_snapchatter_impression', dist='ratio')
    
    n_session_with_inactive_snapchatter_impression = Metric('n_session_with_inactive_snapchatter_impression', 'n_session_with_inactive_snapchatter_impression', dist='cont', daily=True, cumulative=True,)
    pct_session_with_inactive_snapchatter_impression = Metric(col='pct_session_with_inactive_snapchatter_impression', numerator='n_session_with_inactive_snapchatter_impression', denominator='n_session_with_snapchatter_impression', dist='ratio')
    n_session_with_inactive_snapchatter_impression_top5 = Metric('n_session_with_inactive_snapchatter_impression_top5', 'n_session_with_inactive_snapchatter_impression_top5', dist='cont', daily=True, cumulative=True,)
    pct_session_with_inactive_snapchatter_impression_top5 = Metric(col='pct_session_with_inactive_snapchatter_impression_top5', numerator='n_session_with_inactive_snapchatter_impression_top5', denominator='n_session_with_snapchatter_impression', dist='ratio')
    n_session_with_inactive_snapchatter_impression_top8 = Metric('n_session_with_inactive_snapchatter_impression_top8', 'n_session_with_inactive_snapchatter_impression_top8', dist='cont', daily=True, cumulative=True,)
    pct_session_with_inactive_snapchatter_impression_top8 = Metric(col='pct_session_with_inactive_snapchatter_impression_top8', numerator='n_session_with_inactive_snapchatter_impression_top8', denominator='n_session_with_snapchatter_impression', dist='ratio')
    n_session_with_inactive_snapchatter_impression_top10 = Metric('n_session_with_inactive_snapchatter_impression_top10', 'n_session_with_inactive_snapchatter_impression_top10', dist='cont', daily=True, cumulative=True,)
    pct_session_with_inactive_snapchatter_impression_top10 = Metric(col='pct_session_with_inactive_snapchatter_impression_top10', numerator='n_session_with_inactive_snapchatter_impression_top10', denominator='n_session_with_snapchatter_impression', dist='ratio')
    n_session_with_inactive_snapchatter_impression_top20 = Metric('n_session_with_inactive_snapchatter_impression_top20', 'n_session_with_inactive_snapchatter_impression_top20', dist='cont', daily=True, cumulative=True,)
    pct_session_with_inactive_snapchatter_impression_top20 = Metric(col='pct_session_with_inactive_snapchatter_impression_top20', numerator='n_session_with_inactive_snapchatter_impression_top20', denominator='n_session_with_snapchatter_impression', dist='ratio')
    
    n_session_with_inactive_snapchatter_selection = Metric('n_session_with_inactive_snapchatter_selection', 'n_session_with_inactive_snapchatter_selection', dist='cont', daily=True, cumulative=True,)    
    pct_session_with_inactive_snapchatter_selection = Metric(col='pct_session_with_inactive_snapchatter_selection', numerator='n_session_with_inactive_snapchatter_selection', denominator='n_session_with_snapchatter_impression', dist='ratio')
    n_session_with_inactive_snapchatter_selection_top5 = Metric('n_session_with_inactive_snapchatter_selection_top5', 'n_session_with_inactive_snapchatter_selection_top5', dist='cont', daily=True, cumulative=True,)
    pct_session_with_inactive_snapchatter_selection_top5 = Metric(col='pct_session_with_inactive_snapchatter_selection_top5', numerator='n_session_with_inactive_snapchatter_selection_top5', denominator='n_session_with_snapchatter_impression', dist='ratio')
    n_session_with_inactive_snapchatter_selection_top8 = Metric('n_session_with_inactive_snapchatter_selection_top8', 'n_session_with_inactive_snapchatter_selection_top8', dist='cont', daily=True, cumulative=True,)
    pct_session_with_inactive_snapchatter_selection_top8 = Metric(col='pct_session_with_inactive_snapchatter_selection_top8', numerator='n_session_with_inactive_snapchatter_selection_top8', denominator='n_session_with_snapchatter_impression', dist='ratio')
    n_session_with_inactive_snapchatter_selection_top10 = Metric('n_session_with_inactive_snapchatter_selection_top10', 'n_session_with_inactive_snapchatter_selection_top10', dist='cont', daily=True, cumulative=True,)
    pct_session_with_inactive_snapchatter_selection_top10 = Metric(col='pct_session_with_inactive_snapchatter_selection_top10', numerator='n_session_with_inactive_snapchatter_selection_top10', denominator='n_session_with_snapchatter_impression', dist='ratio')
    n_session_with_inactive_snapchatter_selection_top20 = Metric('n_session_with_inactive_snapchatter_selection_top20', 'n_session_with_inactive_snapchatter_selection_top20', dist='cont', daily=True, cumulative=True,)
    pct_session_with_inactive_snapchatter_selection_top20 = Metric(col='pct_session_with_inactive_snapchatter_selection_top20', numerator='n_session_with_inactive_snapchatter_selection_top20', denominator='n_session_with_snapchatter_impression', dist='ratio')    
    
    n_session_with_ria_snapchatter_impression = Metric('n_session_with_ria_snapchatter_impression', 'n_session_with_ria_snapchatter_impression', dist='cont', daily=True, cumulative=True,)
    pct_session_with_ria_snapchatter_impression = Metric(col='pct_session_with_ria_snapchatter_impression', numerator='n_session_with_ria_snapchatter_impression', denominator='n_session_with_snapchatter_impression', dist='ratio')
    n_session_with_ria_snapchatter_impression_top5 = Metric('n_session_with_ria_snapchatter_impression_top5', 'n_session_with_ria_snapchatter_impression_top5', dist='cont', daily=True, cumulative=True,)
    pct_session_with_ria_snapchatter_impression_top5 = Metric(col='pct_session_with_ria_snapchatter_impression_top5', numerator='n_session_with_ria_snapchatter_impression_top5', denominator='n_session_with_snapchatter_impression', dist='ratio')
    n_session_with_ria_snapchatter_impression_top8 = Metric('n_session_with_ria_snapchatter_impression_top8', 'n_session_with_ria_snapchatter_impression_top8', dist='cont', daily=True, cumulative=True,)
    pct_session_with_ria_snapchatter_impression_top8 = Metric(col='pct_session_with_ria_snapchatter_impression_top8', numerator='n_session_with_ria_snapchatter_impression_top8', denominator='n_session_with_snapchatter_impression', dist='ratio')
    n_session_with_ria_snapchatter_impression_top10 = Metric('n_session_with_ria_snapchatter_impression_top10', 'n_session_with_ria_snapchatter_impression_top10', dist='cont', daily=True, cumulative=True,)
    pct_session_with_ria_snapchatter_impression_top10 = Metric(col='pct_session_with_ria_snapchatter_impression_top10', numerator='n_session_with_ria_snapchatter_impression_top10', denominator='n_session_with_snapchatter_impression', dist='ratio')
    n_session_with_ria_snapchatter_impression_top20 = Metric('n_session_with_ria_snapchatter_impression_top20', 'n_session_with_ria_snapchatter_impression_top20', dist='cont', daily=True, cumulative=True,)
    pct_session_with_ria_snapchatter_impression_top20 = Metric(col='pct_session_with_ria_snapchatter_impression_top20', numerator='n_session_with_ria_snapchatter_impression_top20', denominator='n_session_with_snapchatter_impression', dist='ratio')

    n_session_with_ria_snapchatter_selection = Metric('n_session_with_ria_snapchatter_selection', 'n_session_with_ria_snapchatter_selection', dist='cont', daily=True, cumulative=True,)    
    pct_session_with_ria_snapchatter_selection = Metric(col='pct_session_with_ria_snapchatter_selection', numerator='n_session_with_ria_snapchatter_selection', denominator='n_session_with_snapchatter_impression', dist='ratio')
    n_session_with_ria_snapchatter_selection_top5 = Metric('n_session_with_ria_snapchatter_selection_top5', 'n_session_with_ria_snapchatter_selection_top5', dist='cont', daily=True, cumulative=True,)
    pct_session_with_ria_snapchatter_selection_top5 = Metric(col='pct_session_with_ria_snapchatter_selection_top5', numerator='n_session_with_ria_snapchatter_selection_top5', denominator='n_session_with_snapchatter_impression', dist='ratio')
    n_session_with_ria_snapchatter_selection_top8 = Metric('n_session_with_ria_snapchatter_selection_top8', 'n_session_with_ria_snapchatter_selection_top8', dist='cont', daily=True, cumulative=True,)
    pct_session_with_ria_snapchatter_selection_top8 = Metric(col='pct_session_with_ria_snapchatter_selection_top8', numerator='n_session_with_ria_snapchatter_selection_top8', denominator='n_session_with_snapchatter_impression', dist='ratio')
    n_session_with_ria_snapchatter_selection_top10 = Metric('n_session_with_ria_snapchatter_selection_top10', 'n_session_with_ria_snapchatter_selection_top10', dist='cont', daily=True, cumulative=True,)
    pct_session_with_ria_snapchatter_selection_top10 = Metric(col='pct_session_with_ria_snapchatter_selection_top10', numerator='n_session_with_ria_snapchatter_selection_top10', denominator='n_session_with_snapchatter_impression', dist='ratio')
    n_session_with_ria_snapchatter_selection_top20 = Metric('n_session_with_ria_snapchatter_selection_top20', 'n_session_with_ria_snapchatter_selection_top20', dist='cont', daily=True, cumulative=True,)
    pct_session_with_ria_snapchatter_selection_top20 = Metric(col='pct_session_with_ria_snapchatter_selection_top20', numerator='n_session_with_ria_snapchatter_selection_top20', denominator='n_session_with_snapchatter_impression', dist='ratio')    

    n_session_with_streak_expiring_snapchatter_impression = Metric('n_session_with_streak_expiring_snapchatter_impression', 'n_session_with_streak_expiring_snapchatter_impression', dist='cont', daily=True, cumulative=True,)
    pct_session_with_streak_expiring_snapchatter_impression = Metric(col='pct_session_with_streak_expiring_snapchatter_impression', numerator='n_session_with_streak_expiring_snapchatter_impression', denominator='n_session_with_snapchatter_impression', dist='ratio')
    n_session_with_streak_expiring_snapchatter_impression_top5 = Metric('n_session_with_streak_expiring_snapchatter_impression_top5', 'n_session_with_streak_expiring_snapchatter_impression_top5', dist='cont', daily=True, cumulative=True,)
    pct_session_with_streak_expiring_snapchatter_impression_top5 = Metric(col='pct_session_with_streak_expiring_snapchatter_impression_top5', numerator='n_session_with_streak_expiring_snapchatter_impression_top5', denominator='n_session_with_snapchatter_impression', dist='ratio')
    n_session_with_streak_expiring_snapchatter_impression_top8 = Metric('n_session_with_streak_expiring_snapchatter_impression_top8', 'n_session_with_streak_expiring_snapchatter_impression_top8', dist='cont', daily=True, cumulative=True,)
    pct_session_with_streak_expiring_snapchatter_impression_top8 = Metric(col='pct_session_with_streak_expiring_snapchatter_impression_top8', numerator='n_session_with_streak_expiring_snapchatter_impression_top8', denominator='n_session_with_snapchatter_impression', dist='ratio')
    n_session_with_streak_expiring_snapchatter_impression_top10 = Metric('n_session_with_streak_expiring_snapchatter_impression_top10', 'n_session_with_streak_expiring_snapchatter_impression_top10', dist='cont', daily=True, cumulative=True,)
    pct_session_with_streak_expiring_snapchatter_impression_top10 = Metric(col='pct_session_with_streak_expiring_snapchatter_impression_top10', numerator='n_session_with_streak_expiring_snapchatter_impression_top10', denominator='n_session_with_snapchatter_impression', dist='ratio')
    n_session_with_streak_expiring_snapchatter_impression_top20 = Metric('n_session_with_streak_expiring_snapchatter_impression_top20', 'n_session_with_streak_expiring_snapchatter_impression_top20', dist='cont', daily=True, cumulative=True,)
    pct_session_with_streak_expiring_snapchatter_impression_top20 = Metric(col='pct_session_with_streak_expiring_snapchatter_impression_top20', numerator='n_session_with_streak_expiring_snapchatter_impression_top20', denominator='n_session_with_snapchatter_impression', dist='ratio')

    n_session_with_streak_expiring_snapchatter_selection = Metric('n_session_with_streak_expiring_snapchatter_selection', 'n_session_with_streak_expiring_snapchatter_selection', dist='cont', daily=True, cumulative=True,)    
    pct_session_with_streak_expiring_snapchatter_selection = Metric(col='pct_session_with_streak_expiring_snapchatter_selection', numerator='n_session_with_streak_expiring_snapchatter_selection', denominator='n_session_with_snapchatter_impression', dist='ratio')
    n_session_with_streak_expiring_snapchatter_selection_top5 = Metric('n_session_with_streak_expiring_snapchatter_selection_top5', 'n_session_with_streak_expiring_snapchatter_selection_top5', dist='cont', daily=True, cumulative=True,)
    pct_session_with_streak_expiring_snapchatter_selection_top5 = Metric(col='pct_session_with_streak_expiring_snapchatter_selection_top5', numerator='n_session_with_streak_expiring_snapchatter_selection_top5', denominator='n_session_with_snapchatter_impression', dist='ratio')
    n_session_with_streak_expiring_snapchatter_selection_top8 = Metric('n_session_with_streak_expiring_snapchatter_selection_top8', 'n_session_with_streak_expiring_snapchatter_selection_top8', dist='cont', daily=True, cumulative=True,)
    pct_session_with_streak_expiring_snapchatter_selection_top8 = Metric(col='pct_session_with_streak_expiring_snapchatter_selection_top8', numerator='n_session_with_streak_expiring_snapchatter_selection_top8', denominator='n_session_with_snapchatter_impression', dist='ratio')
    n_session_with_streak_expiring_snapchatter_selection_top10 = Metric('n_session_with_streak_expiring_snapchatter_selection_top10', 'n_session_with_streak_expiring_snapchatter_selection_top10', dist='cont', daily=True, cumulative=True,)
    pct_session_with_streak_expiring_snapchatter_selection_top10 = Metric(col='pct_session_with_streak_expiring_snapchatter_selection_top10', numerator='n_session_with_streak_expiring_snapchatter_selection_top10', denominator='n_session_with_snapchatter_impression', dist='ratio')
    n_session_with_streak_expiring_snapchatter_selection_top20 = Metric('n_session_with_streak_expiring_snapchatter_selection_top20', 'n_session_with_streak_expiring_snapchatter_selection_top20', dist='cont', daily=True, cumulative=True,)
    pct_session_with_streak_expiring_snapchatter_selection_top20 = Metric(col='pct_session_with_streak_expiring_snapchatter_selection_top20', numerator='n_session_with_streak_expiring_snapchatter_selection_top20', denominator='n_session_with_snapchatter_impression', dist='ratio')    

    mt = MetricTable(
        sql="""
        SELECT distinct
        PARSE_TIMESTAMP('%Y%m%d', CONCAT('2', _table_suffix)) as ts,
        ghost_user_id,
        SUM(1) as n_session_with_snapchatter_impression,
        SUM(IF(n_result_selected > 0, 1, 0)) as n_session_with_snapchatter_selection,

        SUM(IF(n_result_impressed_inactive > 0, 1, 0)) as n_session_with_inactive_snapchatter_impression,
        SUM(IF(n_result_impressed_inactive > 0 and n_result_impressed_inactive_top5 > 0, 1, 0)) as n_session_with_inactive_snapchatter_impression_top5,
        SUM(IF(n_result_impressed_inactive > 0 and n_result_impressed_inactive_top8 > 0, 1, 0)) as n_session_with_inactive_snapchatter_impression_top8,
        SUM(IF(n_result_impressed_inactive > 0 and n_result_impressed_inactive_top10 > 0, 1, 0)) as n_session_with_inactive_snapchatter_impression_top10,
        SUM(IF(n_result_impressed_inactive > 0 and n_result_impressed_inactive_top20 > 0, 1, 0)) as n_session_with_inactive_snapchatter_impression_top20,

        SUM(IF(n_result_selected_inactive > 0, 1, 0)) as n_session_with_inactive_snapchatter_selection,
        SUM(IF(n_result_selected_inactive > 0 and n_result_selected_inactive_top5 > 0, 1, 0)) as n_session_with_inactive_snapchatter_selection_top5,
        SUM(IF(n_result_selected_inactive > 0 and n_result_selected_inactive_top8 > 0, 1, 0)) as n_session_with_inactive_snapchatter_selection_top8,
        SUM(IF(n_result_selected_inactive > 0 and n_result_selected_inactive_top10 > 0, 1, 0)) as n_session_with_inactive_snapchatter_selection_top10,
        SUM(IF(n_result_selected_inactive > 0 and n_result_selected_inactive_top20 > 0, 1, 0)) as n_session_with_inactive_snapchatter_selection_top20,

        SUM(IF(n_result_impressed_ria > 0, 1, 0)) as n_session_with_ria_snapchatter_impression,
        SUM(IF(n_result_impressed_ria > 0 and n_result_impressed_ria_top5 > 0, 1, 0)) as n_session_with_ria_snapchatter_impression_top5,
        SUM(IF(n_result_impressed_ria > 0 and n_result_impressed_ria_top8 > 0, 1, 0)) as n_session_with_ria_snapchatter_impression_top8,
        SUM(IF(n_result_impressed_ria > 0 and n_result_impressed_ria_top10 > 0, 1, 0)) as n_session_with_ria_snapchatter_impression_top10,
        SUM(IF(n_result_impressed_ria > 0 and n_result_impressed_ria_top20 > 0, 1, 0)) as n_session_with_ria_snapchatter_impression_top20,

        SUM(IF(n_result_selected_ria > 0, 1, 0)) as n_session_with_ria_snapchatter_selection,
        SUM(IF(n_result_selected_ria > 0 and n_result_selected_ria_top5 > 0, 1, 0)) as n_session_with_ria_snapchatter_selection_top5,
        SUM(IF(n_result_selected_ria > 0 and n_result_selected_ria_top8 > 0, 1, 0)) as n_session_with_ria_snapchatter_selection_top8,
        SUM(IF(n_result_selected_ria > 0 and n_result_selected_ria_top10 > 0, 1, 0)) as n_session_with_ria_snapchatter_selection_top10,
        SUM(IF(n_result_selected_ria > 0 and n_result_selected_ria_top20 > 0, 1, 0)) as n_session_with_ria_snapchatter_selection_top20,

        SUM(IF(n_result_impressed_streak_expiring > 0, 1, 0)) as n_session_with_streak_expiring_snapchatter_impression,
        SUM(IF(n_result_impressed_streak_expiring > 0 and n_result_impressed_streak_expiring_top5 > 0, 1, 0)) as n_session_with_streak_expiring_snapchatter_impression_top5,
        SUM(IF(n_result_impressed_streak_expiring > 0 and n_result_impressed_streak_expiring_top8 > 0, 1, 0)) as n_session_with_streak_expiring_snapchatter_impression_top8,
        SUM(IF(n_result_impressed_streak_expiring > 0 and n_result_impressed_streak_expiring_top10 > 0, 1, 0)) as n_session_with_streak_expiring_snapchatter_impression_top10,
        SUM(IF(n_result_impressed_streak_expiring > 0 and n_result_impressed_streak_expiring_top20 > 0, 1, 0)) as n_session_with_streak_expiring_snapchatter_impression_top20,

        SUM(IF(n_result_selected_streak_expiring > 0, 1, 0)) as n_session_with_streak_expiring_snapchatter_selection,
        SUM(IF(n_result_selected_streak_expiring > 0 and n_result_selected_streak_expiring_top5 > 0, 1, 0)) as n_session_with_streak_expiring_snapchatter_selection_top5,
        SUM(IF(n_result_selected_streak_expiring > 0 and n_result_selected_streak_expiring_top8 > 0, 1, 0)) as n_session_with_streak_expiring_snapchatter_selection_top8,
        SUM(IF(n_result_selected_streak_expiring > 0 and n_result_selected_streak_expiring_top10 > 0, 1, 0)) as n_session_with_streak_expiring_snapchatter_selection_top10,
        SUM(IF(n_result_selected_streak_expiring > 0 and n_result_selected_streak_expiring_top20 > 0, 1, 0)) as n_session_with_streak_expiring_snapchatter_selection_top20,

        from {source_table}
        group by 1, 2
        """.format(source_table=source_table),

        metrics=[
                n_session_with_snapchatter_impression,
                n_session_with_snapchatter_selection,
                pct_session_with_snapchatter_selection,
                n_session_with_inactive_snapchatter_impression,
                pct_session_with_inactive_snapchatter_impression,
                n_session_with_inactive_snapchatter_impression_top5,
                pct_session_with_inactive_snapchatter_impression_top5,
                n_session_with_inactive_snapchatter_impression_top8,
                pct_session_with_inactive_snapchatter_impression_top8,
                n_session_with_inactive_snapchatter_impression_top10,
                pct_session_with_inactive_snapchatter_impression_top10,
                n_session_with_inactive_snapchatter_impression_top20,
                pct_session_with_inactive_snapchatter_impression_top20,
                n_session_with_inactive_snapchatter_selection,
                pct_session_with_inactive_snapchatter_selection,
                n_session_with_inactive_snapchatter_selection_top5,
                pct_session_with_inactive_snapchatter_selection_top5,
                n_session_with_inactive_snapchatter_selection_top8,
                pct_session_with_inactive_snapchatter_selection_top8,
                n_session_with_inactive_snapchatter_selection_top10,
                pct_session_with_inactive_snapchatter_selection_top10,
                n_session_with_inactive_snapchatter_selection_top20,
                pct_session_with_inactive_snapchatter_selection_top20, 
                n_session_with_ria_snapchatter_impression,
                pct_session_with_ria_snapchatter_impression,
                n_session_with_ria_snapchatter_impression_top5,
                pct_session_with_ria_snapchatter_impression_top5,
                n_session_with_ria_snapchatter_impression_top8,
                pct_session_with_ria_snapchatter_impression_top8,
                n_session_with_ria_snapchatter_impression_top10,
                pct_session_with_ria_snapchatter_impression_top10,
                n_session_with_ria_snapchatter_impression_top20,
                pct_session_with_ria_snapchatter_impression_top20,
                n_session_with_ria_snapchatter_selection,
                pct_session_with_ria_snapchatter_selection,
                n_session_with_ria_snapchatter_selection_top5,
                pct_session_with_ria_snapchatter_selection_top5,
                n_session_with_ria_snapchatter_selection_top8,
                pct_session_with_ria_snapchatter_selection_top8,
                n_session_with_ria_snapchatter_selection_top10,
                pct_session_with_ria_snapchatter_selection_top10,
                n_session_with_ria_snapchatter_selection_top20,
                pct_session_with_ria_snapchatter_selection_top20, 
                n_session_with_streak_expiring_snapchatter_impression,
                pct_session_with_streak_expiring_snapchatter_impression,
                n_session_with_streak_expiring_snapchatter_impression_top5,
                pct_session_with_streak_expiring_snapchatter_impression_top5,
                n_session_with_streak_expiring_snapchatter_impression_top8,
                pct_session_with_streak_expiring_snapchatter_impression_top8,
                n_session_with_streak_expiring_snapchatter_impression_top10,
                pct_session_with_streak_expiring_snapchatter_impression_top10,
                n_session_with_streak_expiring_snapchatter_impression_top20,
                pct_session_with_streak_expiring_snapchatter_impression_top20,
                n_session_with_streak_expiring_snapchatter_selection,
                pct_session_with_streak_expiring_snapchatter_selection,
                n_session_with_streak_expiring_snapchatter_selection_top5,
                pct_session_with_streak_expiring_snapchatter_selection_top5,
                n_session_with_streak_expiring_snapchatter_selection_top8,
                pct_session_with_streak_expiring_snapchatter_selection_top8,
                n_session_with_streak_expiring_snapchatter_selection_top10,
                pct_session_with_streak_expiring_snapchatter_selection_top10,
                n_session_with_streak_expiring_snapchatter_selection_top20,
                pct_session_with_streak_expiring_snapchatter_selection_top20
            ],
        name="recents_ranking_snapchatter_session_lvl_metrics",
        bq_dialect="standard"
    )
    return mt

def recents_ranking_group_item_lvl_metrics(start_date, end_date):
    """
    Item-level metrics for evaluating Group Recents ranking
    """

    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
    `sc-analytics.report_growth.sendto_session_lvl_recent_group_ir_metrics_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    n_recents_group_impressed = Metric('n_recents_group_impressed', 'n_recents_group_impressed', dist='cont', daily=True, cumulative=True,)    
    n_recents_group_impressed_top5 = Metric('n_recents_group_impressed_top5', 'n_recents_group_impressed_top5', dist='cont', daily=True, cumulative=True,)    
    n_recents_group_impressed_top8 = Metric('n_recents_group_impressed_top8', 'n_recents_group_impressed_top8', dist='cont', daily=True, cumulative=True,)    
    n_recents_group_impressed_top10 = Metric('n_recents_group_impressed_top10', 'n_recents_group_impressed_top10', dist='cont', daily=True, cumulative=True,)    
    n_recents_group_impressed_top20 = Metric('n_recents_group_impressed_top20', 'n_recents_group_impressed_top20', dist='cont', daily=True, cumulative=True,)    
    n_recents_group_selected = Metric('n_recents_group_selected', 'n_recents_group_selected', dist='cont', daily=True, cumulative=True,)    
    n_recents_group_selected_top5 = Metric('n_recents_group_selected_top5', 'n_recents_group_selected_top5', dist='cont', daily=True, cumulative=True,)    
    n_recents_group_selected_top8 = Metric('n_recents_group_selected_top8', 'n_recents_group_selected_top8', dist='cont', daily=True, cumulative=True,)    
    n_recents_group_selected_top10 = Metric('n_recents_group_selected_top10', 'n_recents_group_selected_top10', dist='cont', daily=True, cumulative=True,)    
    n_recents_group_selected_top20 = Metric('n_recents_group_selected_top20', 'n_recents_group_selected_top20', dist='cont', daily=True, cumulative=True,)    

    recents_group_ctr = Metric(col='recents_group_ctr', numerator='n_recents_group_selected', denominator='n_recents_group_impressed', dist='ratio')
    recents_group_ctr_top5 = Metric(col='recents_group_ctr_top5', numerator='n_recents_group_selected_top5', denominator='n_recents_group_impressed_top5', dist='ratio')
    recents_group_ctr_top8 = Metric(col='recents_group_ctr_top8', numerator='n_recents_group_selected_top8', denominator='n_recents_group_impressed_top8', dist='ratio')
    recents_group_ctr_top10 = Metric(col='recents_group_ctr_top10', numerator='n_recents_group_selected_top10', denominator='n_recents_group_impressed_top10', dist='ratio')
    recents_group_ctr_top20 = Metric(col='recents_group_ctr_top20', numerator='n_recents_group_selected_top20', denominator='n_recents_group_impressed_top20', dist='ratio')
    
    mt = MetricTable(
        sql="""
        SELECT distinct
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        ghost_user_id,
        sum(n_result_impressed) as n_recents_group_impressed,
        sum(n_result_impressed_top5) as n_recents_group_impressed_top5,
        sum(n_result_impressed_top8) as n_recents_group_impressed_top8,
        sum(n_result_impressed_top10) as n_recents_group_impressed_top10,
        sum(n_result_impressed_top20) as n_recents_group_impressed_top20,
        sum(n_result_selected) as n_recents_group_selected,
        sum(n_result_selected_top5) as n_recents_group_selected_top5,
        sum(n_result_selected_top8) as n_recents_group_selected_top8,
        sum(n_result_selected_top10) as n_recents_group_selected_top10,
        sum(n_result_selected_top20) as n_recents_group_selected_top20,
        from {source_table}
        group by 1, 2
        """.format(source_table=source_table),

        metrics=[
            n_recents_group_impressed, 
            n_recents_group_impressed_top5, 
            n_recents_group_impressed_top8, 
            n_recents_group_impressed_top10, 
            n_recents_group_impressed_top20, 
            n_recents_group_selected, 
            n_recents_group_selected_top5, 
            n_recents_group_selected_top8, 
            n_recents_group_selected_top10, 
            n_recents_group_selected_top20, 
            recents_group_ctr, 
            recents_group_ctr_top5, 
            recents_group_ctr_top8, 
            recents_group_ctr_top10, 
            recents_group_ctr_top20, 
                ],
        name="recents_ranking_group_item_lvl_metrics",
        bq_dialect="standard"
    )
    return mt

def recents_ranking_group_session_lvl_metrics(start_date, end_date):
    """
    Session-level metrics for evaluating Group Recents ranking
    """

    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
    `sc-analytics.report_growth.sendto_session_lvl_recent_group_ir_metrics_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    n_session_with_group_impression = Metric('n_session_with_group_impression', 'n_session_with_group_impression', dist='cont', daily=True, cumulative=True,)

    n_session_with_group_selection = Metric('n_session_with_group_selection', 'n_session_with_group_selection', dist='cont', daily=True, cumulative=True,)
    pct_session_with_group_selection = Metric(col='pct_session_with_group_selection', numerator='n_session_with_group_selection', denominator='n_session_with_group_impression', dist='ratio')
    
    mt = MetricTable(
        sql="""
        SELECT distinct
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        ghost_user_id,
        count(distinct send_to_session_id) as n_session_with_group_impression,
        count(distinct if(n_result_selected > 0, send_to_session_id, NULL)) as n_session_with_group_selection,
        from {source_table}
        group by 1, 2
        """.format(source_table=source_table),

        metrics=[
                n_session_with_group_impression,
                n_session_with_group_selection,
                pct_session_with_group_selection,
                ],
        name="recents_ranking_group_session_lvl_metrics",
        bq_dialect="standard"
    )
    return mt

def search_ranking_item_lvl_metrics(start_date, end_date):
    """
    Item-level metrics for evaluating Search ranking
    """

    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
    `sc-analytics.report_growth.sendto_session_lvl_search_ir_metrics_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    n_search_result_impressed = Metric('n_search_result_impressed', 'n_search_result_impressed', dist='cont', daily=True, cumulative=True)    
    n_search_result_impressed_top5 = Metric('n_search_result_impressed_top5', 'n_search_result_impressed_top5', dist='cont', daily=True, cumulative=True)    
    n_search_result_impressed_top8 = Metric('n_search_result_impressed_top8', 'n_search_result_impressed_top8', dist='cont', daily=True, cumulative=True)    
    n_search_result_impressed_top10 = Metric('n_search_result_impressed_top10', 'n_search_result_impressed_top10', dist='cont', daily=True, cumulative=True)    
    n_search_result_impressed_top20 = Metric('n_search_result_impressed_top20', 'n_search_result_impressed_top20', dist='cont', daily=True, cumulative=True)    
    n_search_result_selected = Metric('n_search_result_selected', 'n_search_result_selected', dist='cont', daily=True, cumulative=True)    
    n_search_result_selected_top5 = Metric('n_search_result_selected_top5', 'n_search_result_selected_top5', dist='cont', daily=True, cumulative=True)    
    n_search_result_selected_top8 = Metric('n_search_result_selected_top8', 'n_search_result_selected_top8', dist='cont', daily=True, cumulative=True)    
    n_search_result_selected_top10 = Metric('n_search_result_selected_top10', 'n_search_result_selected_top10', dist='cont', daily=True, cumulative=True)    
    n_search_result_selected_top20 = Metric('n_search_result_selected_top20', 'n_search_result_selected_top20', dist='cont', daily=True, cumulative=True)    

    search_result_ctr = Metric(col='search_result_ctr', numerator='n_search_result_selected', denominator='n_search_result_impressed', dist='ratio')
    search_result_ctr_top5 = Metric(col='search_result_ctr_top5', numerator='n_search_result_selected_top5', denominator='n_search_result_impressed_top5', dist='ratio')
    search_result_ctr_top8 = Metric(col='search_result_ctr_top8', numerator='n_search_result_selected_top8', denominator='n_search_result_impressed_top8', dist='ratio')
    search_result_ctr_top10 = Metric(col='search_result_ctr_top10', numerator='n_search_result_selected_top10', denominator='n_search_result_impressed_top10', dist='ratio')
    search_result_ctr_top20 = Metric(col='search_result_ctr_top20', numerator='n_search_result_selected_top20', denominator='n_search_result_impressed_top20', dist='ratio')    

    n_search_snapchatter_impressed = Metric('n_search_snapchatter_impressed', 'n_search_snapchatter_impressed', dist='cont', daily=True, cumulative=True,)    
    n_search_snapchatter_impressed_top5 = Metric('n_search_snapchatter_impressed_top5', 'n_search_snapchatter_impressed_top5', dist='cont', daily=True, cumulative=True,)    
    n_search_snapchatter_impressed_top8 = Metric('n_search_snapchatter_impressed_top8', 'n_search_snapchatter_impressed_top8', dist='cont', daily=True, cumulative=True,)    
    n_search_snapchatter_impressed_top10 = Metric('n_search_snapchatter_impressed_top10', 'n_search_snapchatter_impressed_top10', dist='cont', daily=True, cumulative=True,)    
    n_search_snapchatter_impressed_top20 = Metric('n_search_snapchatter_impressed_top20', 'n_search_snapchatter_impressed_top20', dist='cont', daily=True, cumulative=True,)    
    n_search_snapchatter_selected = Metric('n_search_snapchatter_selected', 'n_search_snapchatter_selected', dist='cont', daily=True, cumulative=True,)    
    n_search_snapchatter_selected_top5 = Metric('n_search_snapchatter_selected_top5', 'n_search_snapchatter_selected_top5', dist='cont', daily=True, cumulative=True,)    
    n_search_snapchatter_selected_top8 = Metric('n_search_snapchatter_selected_top8', 'n_search_snapchatter_selected_top8', dist='cont', daily=True, cumulative=True,)    
    n_search_snapchatter_selected_top10 = Metric('n_search_snapchatter_selected_top10', 'n_search_snapchatter_selected_top10', dist='cont', daily=True, cumulative=True,)    
    n_search_snapchatter_selected_top20 = Metric('n_search_snapchatter_selected_top20', 'n_search_snapchatter_selected_top20', dist='cont', daily=True, cumulative=True,)    

    search_snapchatter_ctr = Metric(col='search_snapchatter_ctr', numerator='n_search_snapchatter_selected', denominator='n_search_snapchatter_impressed', dist='ratio')
    search_snapchatter_ctr_top5 = Metric(col='search_snapchatter_ctr_top5', numerator='n_search_snapchatter_selected_top5', denominator='n_search_snapchatter_impressed_top5', dist='ratio')
    search_snapchatter_ctr_top8 = Metric(col='search_snapchatter_ctr_top8', numerator='n_search_snapchatter_selected_top8', denominator='n_search_snapchatter_impressed_top8', dist='ratio')
    search_snapchatter_ctr_top10 = Metric(col='search_snapchatter_ctr_top10', numerator='n_search_snapchatter_selected_top10', denominator='n_search_snapchatter_impressed_top10', dist='ratio')
    search_snapchatter_ctr_top20 = Metric(col='search_snapchatter_ctr_top20', numerator='n_search_snapchatter_selected_top20', denominator='n_search_snapchatter_impressed_top20', dist='ratio')
    
    n_search_group_impressed = Metric('n_search_group_impressed', 'n_search_group_impressed', dist='cont', daily=True, cumulative=True)    
    n_search_group_impressed_top5 = Metric('n_search_group_impressed_top5', 'n_search_group_impressed_top5', dist='cont', daily=True, cumulative=True)    
    n_search_group_impressed_top8 = Metric('n_search_group_impressed_top8', 'n_search_group_impressed_top8', dist='cont', daily=True, cumulative=True)    
    n_search_group_impressed_top10 = Metric('n_search_group_impressed_top10', 'n_search_group_impressed_top10', dist='cont', daily=True, cumulative=True)    
    n_search_group_impressed_top20 = Metric('n_search_group_impressed_top20', 'n_search_group_impressed_top20', dist='cont', daily=True, cumulative=True)    
    n_search_group_selected = Metric('n_search_group_selected', 'n_search_group_selected', dist='cont', daily=True, cumulative=True)    
    n_search_group_selected_top5 = Metric('n_search_group_selected_top5', 'n_search_group_selected_top5', dist='cont', daily=True, cumulative=True)    
    n_search_group_selected_top8 = Metric('n_search_group_selected_top8', 'n_search_group_selected_top8', dist='cont', daily=True, cumulative=True)    
    n_search_group_selected_top10 = Metric('n_search_group_selected_top10', 'n_search_group_selected_top10', dist='cont', daily=True, cumulative=True)    
    n_search_group_selected_top20 = Metric('n_search_group_selected_top20', 'n_search_group_selected_top20', dist='cont', daily=True, cumulative=True)    

    search_group_ctr = Metric(col='search_group_ctr', numerator='n_search_group_selected', denominator='n_search_group_impressed', dist='ratio')
    search_group_ctr_top5 = Metric(col='search_group_ctr_top5', numerator='n_search_group_selected_top5', denominator='n_search_group_impressed_top5', dist='ratio')
    search_group_ctr_top8 = Metric(col='search_group_ctr_top8', numerator='n_search_group_selected_top8', denominator='n_search_group_impressed_top8', dist='ratio')
    search_group_ctr_top10 = Metric(col='search_group_ctr_top10', numerator='n_search_group_selected_top10', denominator='n_search_group_impressed_top10', dist='ratio')
    search_group_ctr_top20 = Metric(col='search_group_ctr_top20', numerator='n_search_group_selected_top20', denominator='n_search_group_impressed_top20', dist='ratio')

    n_search_contact_impressed = Metric('n_search_contact_impressed', 'n_search_contact_impressed', dist='cont', daily=True, cumulative=True)    
    n_search_contact_impressed_top5 = Metric('n_search_contact_impressed_top5', 'n_search_contact_impressed_top5', dist='cont', daily=True, cumulative=True)    
    n_search_contact_impressed_top8 = Metric('n_search_contact_impressed_top8', 'n_search_contact_impressed_top8', dist='cont', daily=True, cumulative=True)    
    n_search_contact_impressed_top10 = Metric('n_search_contact_impressed_top10', 'n_search_contact_impressed_top10', dist='cont', daily=True, cumulative=True)    
    n_search_contact_impressed_top20 = Metric('n_search_contact_impressed_top20', 'n_search_contact_impressed_top20', dist='cont', daily=True, cumulative=True)    
    n_search_contact_selected = Metric('n_search_contact_selected', 'n_search_contact_selected', dist='cont', daily=True, cumulative=True)    
    n_search_contact_selected_top5 = Metric('n_search_contact_selected_top5', 'n_search_contact_selected_top5', dist='cont', daily=True, cumulative=True)    
    n_search_contact_selected_top8 = Metric('n_search_contact_selected_top8', 'n_search_contact_selected_top8', dist='cont', daily=True, cumulative=True)    
    n_search_contact_selected_top10 = Metric('n_search_contact_selected_top10', 'n_search_contact_selected_top10', dist='cont', daily=True, cumulative=True)    
    n_search_contact_selected_top20 = Metric('n_search_contact_selected_top20', 'n_search_contact_selected_top20', dist='cont', daily=True, cumulative=True)    

    search_contact_ctr = Metric(col='search_contact_ctr', numerator='n_search_contact_selected', denominator='n_search_contact_impressed', dist='ratio')
    search_contact_ctr_top5 = Metric(col='search_contact_ctr_top5', numerator='n_search_contact_selected_top5', denominator='n_search_contact_impressed_top5', dist='ratio')
    search_contact_ctr_top8 = Metric(col='search_contact_ctr_top8', numerator='n_search_contact_selected_top8', denominator='n_search_contact_impressed_top8', dist='ratio')
    search_contact_ctr_top10 = Metric(col='search_contact_ctr_top10', numerator='n_search_contact_selected_top10', denominator='n_search_contact_impressed_top10', dist='ratio')
    search_contact_ctr_top20 = Metric(col='search_contact_ctr_top20', numerator='n_search_contact_selected_top20', denominator='n_search_contact_impressed_top20', dist='ratio')
    
    mt = MetricTable(
        sql="""
        SELECT distinct
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        ghost_user_id,
        
        sum(n_result_impressed) as n_search_result_impressed,
        sum(n_result_impressed_top5) as n_search_result_impressed_top5,
        sum(n_result_impressed_top8) as n_search_result_impressed_top8,
        sum(n_result_impressed_top10) as n_search_result_impressed_top10,
        sum(n_result_impressed_top20) as n_search_result_impressed_top20,
        sum(n_result_selected) as n_search_result_selected,
        sum(n_result_selected_top5) as n_search_result_selected_top5,
        sum(n_result_selected_top8) as n_search_result_selected_top8,
        sum(n_result_selected_top10) as n_search_result_selected_top10,
        sum(n_result_selected_top20) as n_search_result_selected_top20,

        sum(n_snapchatter_impressed) as n_search_snapchatter_impressed,
        sum(n_snapchatter_impressed_top5) as n_search_snapchatter_impressed_top5,
        sum(n_snapchatter_impressed_top8) as n_search_snapchatter_impressed_top8,
        sum(n_snapchatter_impressed_top10) as n_search_snapchatter_impressed_top10,
        sum(n_snapchatter_impressed_top20) as n_search_snapchatter_impressed_top20,
        sum(n_snapchatter_selected) as n_search_snapchatter_selected,
        sum(n_snapchatter_selected_top5) as n_search_snapchatter_selected_top5,
        sum(n_snapchatter_selected_top8) as n_search_snapchatter_selected_top8,
        sum(n_snapchatter_selected_top10) as n_search_snapchatter_selected_top10,
        sum(n_snapchatter_selected_top20) as n_search_snapchatter_selected_top20,

        sum(n_group_impressed) as n_search_group_impressed,
        sum(n_group_impressed_top5) as n_search_group_impressed_top5,
        sum(n_group_impressed_top8) as n_search_group_impressed_top8,
        sum(n_group_impressed_top10) as n_search_group_impressed_top10,
        sum(n_group_impressed_top20) as n_search_group_impressed_top20,
        sum(n_group_selected) as n_search_group_selected,
        sum(n_group_selected_top5) as n_search_group_selected_top5,
        sum(n_group_selected_top8) as n_search_group_selected_top8,
        sum(n_group_selected_top10) as n_search_group_selected_top10,
        sum(n_group_selected_top20) as n_search_group_selected_top20,

        sum(n_contact_impressed) as n_search_contact_impressed,
        sum(n_contact_impressed_top5) as n_search_contact_impressed_top5,
        sum(n_contact_impressed_top8) as n_search_contact_impressed_top8,
        sum(n_contact_impressed_top10) as n_search_contact_impressed_top10,
        sum(n_contact_impressed_top20) as n_search_contact_impressed_top20,
        sum(n_contact_selected) as n_search_contact_selected,
        sum(n_contact_selected_top5) as n_search_contact_selected_top5,
        sum(n_contact_selected_top8) as n_search_contact_selected_top8,
        sum(n_contact_selected_top10) as n_search_contact_selected_top10,
        sum(n_contact_selected_top20) as n_search_contact_selected_top20,
        from {source_table}
        group by 1, 2        
        """.format(source_table=source_table),

        metrics=[
            # Result Metrics
            n_search_result_impressed, 
            n_search_result_impressed_top5, 
            n_search_result_impressed_top8, 
            n_search_result_impressed_top10, 
            n_search_result_impressed_top20, 
            n_search_result_selected, 
            n_search_result_selected_top5, 
            n_search_result_selected_top8, 
            n_search_result_selected_top10, 
            n_search_result_selected_top20, 
            search_result_ctr, 
            search_result_ctr_top5, 
            search_result_ctr_top8, 
            search_result_ctr_top10, 
            search_result_ctr_top20,

            # Snapchatter Metrics
            n_search_snapchatter_impressed, 
            n_search_snapchatter_impressed_top5, 
            n_search_snapchatter_impressed_top8, 
            n_search_snapchatter_impressed_top10, 
            n_search_snapchatter_impressed_top20, 
            n_search_snapchatter_selected, 
            n_search_snapchatter_selected_top5, 
            n_search_snapchatter_selected_top8, 
            n_search_snapchatter_selected_top10, 
            n_search_snapchatter_selected_top20, 
            search_snapchatter_ctr, 
            search_snapchatter_ctr_top5, 
            search_snapchatter_ctr_top8, 
            search_snapchatter_ctr_top10, 
            search_snapchatter_ctr_top20,

            # Group Metrics
            n_search_group_impressed, 
            n_search_group_impressed_top5, 
            n_search_group_impressed_top8, 
            n_search_group_impressed_top10, 
            n_search_group_impressed_top20, 
            n_search_group_selected, 
            n_search_group_selected_top5, 
            n_search_group_selected_top8, 
            n_search_group_selected_top10, 
            n_search_group_selected_top20, 
            search_group_ctr, 
            search_group_ctr_top5, 
            search_group_ctr_top8, 
            search_group_ctr_top10, 
            search_group_ctr_top20,

            # Contact Metrics
            n_search_contact_impressed, 
            n_search_contact_impressed_top5, 
            n_search_contact_impressed_top8, 
            n_search_contact_impressed_top10, 
            n_search_contact_impressed_top20, 
            n_search_contact_selected, 
            n_search_contact_selected_top5, 
            n_search_contact_selected_top8, 
            n_search_contact_selected_top10, 
            n_search_contact_selected_top20, 
            search_contact_ctr, 
            search_contact_ctr_top5, 
            search_contact_ctr_top8, 
            search_contact_ctr_top10, 
            search_contact_ctr_top20,            
                ],
        name="search_ranking_item_lvl_metrics",
        bq_dialect="standard"
    )
    return mt
    
# def sendto_slow_scroll(start_date, end_date):
#     """
#     SendTo slow scroll
#     """
#     start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
#     end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

#     source_table = """
#     `sc-portal.quest.app_app_bad_frame_user_2*`
#     WHERE concat('2',_TABLE_SUFFIX) between '{start}' and '{end}'
#     """.format(
#         start=start_date,
#         end=end_date,
#     )

#     app_sendto_scroll = Metric(
#         'app_sendto_scroll',
#         'app_sendto_scroll',
#         dist='cont',
#         daily=True,
#         cumulative=True,
#         desired_direction=NEGATIVE
#     )
#     app_sendto_slow_scroll = Metric(
#         'app_sendto_slow_scroll',
#         'app_sendto_slow_scroll',
#         dist='cont',
#         daily=True,
#         cumulative=True,
#         desired_direction=NEGATIVE
#     )


#     app_sendto_slow_scroll_ratio = Metric(
#         col='app_sendto_slow_scroll_ratio',
#         numerator='app_sendto_slow_scroll',
#         denominator='app_sendto_scroll',
#         dist='ratio',
#         desired_direction=NEGATIVE

#     )

#     mt = MetricTable(
#         sql="""
#         SELECT
#           PARSE_TIMESTAMP('%Y%m%d', concat('2',_TABLE_SUFFIX)) as ts,
#           ghost_user_id,
#           SUM(app_sendto_scroll) AS app_sendto_scroll,
#           SUM(app_sendto_slow_scroll) AS app_sendto_slow_scroll,
#           FROM {source_table}
#           GROUP BY 1,2
#         """.format(source_table=source_table),
#         metrics=[
#                 app_sendto_scroll,
#                 app_sendto_slow_scroll,
#                 app_sendto_slow_scroll_ratio,
#                 ],
#         name="sendto_slow_scroll",
#         bq_dialect="standard"

#     )
#     return mt

def sendto_render_latency(start_date, end_date):
    """
    SendTo render latency
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    source_table = """
    `sc-analytics.report_camera.send_to_latency_raw_events_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    # max_rendered_latency
    rendered_latency_time = Metric(
        'rendered_latency_time',
        'SendTo Latency Rendered Time',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=NEGATIVE
    )

    rendered_latency_count = Metric(
        'rendered_latency_count',
        'SendTo Latency Rendered Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    time_per_rendered = Metric(
        col='Latency Time Per Rendered',
        numerator='rendered_latency_time',
        denominator='rendered_latency_count',
        dist='ratio',
        desired_direction=NEGATIVE
    )

    send_to_init_count = Metric(
        'send_to_init_count',
        'SendTo Init Latency Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    send_to_view_created_count = Metric(
        'send_to_view_created_count',
        'SendTo View Created Latency Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    send_to_page_loaded_count = Metric(
        'send_to_page_loaded_count',
        'SendTo Page Loaded Latency Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )

    send_to_rendered_sections_count = Metric(
        'send_to_rendered_sections_count',
        'SendTo Rendered-Sections Latency Count',
        dist='cont',
        daily=True,
        cumulative=True,
    )


    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        ghost_user_id,
        sum(if(max_rendered_latency>0,1,0)) as rendered_latency_count,
        sum(if(max_rendered_latency>0, max_rendered_latency,0)) as rendered_latency_time,
        sum(if(splits_SEND_TO_INIT>0,1,0))as send_to_init_count,
        sum(if(splits_SEND_TO_VIEW_CREATED>0,1,0))as send_to_view_created_count,
        sum(if(splits_SEND_TO_PAGE_LOADED>0,1,0))as send_to_page_loaded_count,
        sum(if(rendered_sections is not null,1,0))as send_to_rendered_sections_count,

        FROM {source_table}
        GROUP BY ts, ghost_user_id
        """.format(source_table=source_table,
                  ),
        metrics=[
                rendered_latency_count,
                rendered_latency_time,
                send_to_init_count,
                send_to_view_created_count,
                send_to_page_loaded_count,
                send_to_rendered_sections_count
                ],
        name="sendto_render_latency",
        bq_dialect="standard"
    )
    return mt

def sendto_render_latency_percentile(start_date, end_date):
    """
    Percentiles of SendTo render latency
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    source_table = """
    `sc-analytics.report_camera.send_to_latency_raw_events_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    # max_rendered_latency
    rendered_latency_time_percentile = Metric(
       'rendered_latency_time_percentile',
       'Percentiles of SendTo Latency Rendered Time',
       dist='quantile',
       desired_direction=NEGATIVE
    )

    WARM_rendered_latency_time_percentile = Metric(
       'WARM_rendered_latency_time_percentile',
       'Percentiles of SendTo Latency Rendered Time (WARM)',
       dist='quantile',
       desired_direction=NEGATIVE
    )

    COLD_rendered_latency_time_percentile = Metric(
       'COLD_rendered_latency_time_percentile',
       'Percentiles of SendTo Latency Rendered Time (COLD)',
       dist='quantile',
       desired_direction=NEGATIVE
    )

    send_to_page_loaded_time_percentile = Metric(
       'send_to_page_loaded_time_percentile',
       'Percentiles of SendTo Page Loaded Latency Time',
       dist='quantile',
       desired_direction=NEGATIVE
    )

    rendered_best_friends = Metric(
       'rendered_best_friends',
       'Percentiles of Best Friend Rendered Time',
       dist='quantile',
       desired_direction=NEGATIVE
    )

    rendered_recents = Metric(
       'rendered_recents',
       'Percentiles of Recents Rendered Time',
       dist='quantile',
       desired_direction=NEGATIVE
    )

    rendered_stories = Metric(
       'rendered_stories',
       'Percentiles of Story Rendered Time',
       dist='quantile',
       desired_direction=NEGATIVE
    )

    rendered_groups = Metric(
       'rendered_groups',
       'Percentiles of Groups Rendered Time',
       dist='quantile',
       desired_direction=NEGATIVE
    )

    rendered_friends = Metric(
       'rendered_friends',
       'Percentiles of Friends Rendered Time',
       dist='quantile',
       desired_direction=NEGATIVE
    )

    rendered_snap_back = Metric(
       'rendered_snap_back',
       'Percentiles of Reply Rendered Time',
       dist='quantile',
       desired_direction=NEGATIVE
    )

    rendered_list = Metric(
       'rendered_list',
       'Percentiles of List Rendered Time',
       dist='quantile',
       desired_direction=NEGATIVE
    )

    rendered_contextual_list = Metric(
       'rendered_contextual_list',
       'Percentiles of Contextual List Rendered Time',
       dist='quantile',
       desired_direction=NEGATIVE
    )

    rendered_spotlight = Metric(
       'rendered_spotlight',
       'Percentiles of Spotlight Rendered Time',
       dist='quantile',
       desired_direction=NEGATIVE
    )

    CAMERA_rendered_latency_time = Metric(
        'CAMERA_rendered_latency_time',
        'SendTo Latency Rendered Time CAMERA',
        dist='quantile',
        desired_direction=NEGATIVE
    )

    GALLERY_rendered_latency_time = Metric(
        'GALLERY_rendered_latency_time',
        'SendTo Latency Rendered Time GALLERY',
        dist='quantile',
        desired_direction=NEGATIVE
    )

    FEED_rendered_latency_time = Metric(
        'FEED_rendered_latency_time',
        'SendTo Latency Rendered Time FEED',
        dist='quantile',
        desired_direction=NEGATIVE
    )

    CAMERA_ROLL_rendered_latency_time = Metric(
        'CAMERA_ROLL_rendered_latency_time',
        'SendTo Latency Rendered Time CAMERA ROLL',
        dist='quantile',
        desired_direction=NEGATIVE
    )

    IN_CHAT_rendered_latency_time = Metric(
        'IN_CHAT_rendered_latency_time',
        'SendTo Latency Rendered Time CHAT',
        dist='quantile',
        desired_direction=NEGATIVE
    )

    SHARE_rendered_latency_time = Metric(
        'SHARE_rendered_latency_time',
        'SendTo Latency Rendered Time SHARE',
        dist='quantile',
        desired_direction=NEGATIVE
    )

    DISCOVER_rendered_latency_time = Metric(
        'DISCOVER_rendered_latency_time',
        'SendTo Latency Rendered Time DISCOVER',
        dist='quantile',
        desired_direction=NEGATIVE
    )

    FEED_SNAP_REPLY_rendered_latency_time = Metric(
        'FEED_SNAP_REPLY_rendered_latency_time',
        'SendTo Latency Rendered Time FEED REPLY',
        dist='quantile',
        desired_direction=NEGATIVE
    )

    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        ghost_user_id,
        (if(max_rendered_latency>0, max_rendered_latency,NULL))AS rendered_latency_time_percentile,
        (if(max_rendered_latency>0 and app_startup_type ='WARM', max_rendered_latency,NULL)) as WARM_rendered_latency_time_percentile,
        (if(max_rendered_latency>0 and app_startup_type ='COLD', max_rendered_latency,NULL)) as COLD_rendered_latency_time_percentile,

        (if(splits_SEND_TO_PAGE_LOADED>0, splits_SEND_TO_PAGE_LOADED,NULL))AS send_to_page_loaded_time_percentile,

        if(SAFE_CAST(JSON_EXTRACT(rendered_sections, '$.BEST_FRIENDS' )AS INT64)>0,SAFE_CAST(JSON_EXTRACT(rendered_sections, '$.BEST_FRIENDS' )AS INT64),null)AS rendered_best_friends,
        if(SAFE_CAST(JSON_EXTRACT(rendered_sections, '$.RECENTS' )AS INT64)>0,     SAFE_CAST(JSON_EXTRACT(rendered_sections, '$.RECENTS' )AS INT64),null)AS rendered_recents,
        if(SAFE_CAST(JSON_EXTRACT(rendered_sections, '$.STORIES' )AS INT64)>0,     SAFE_CAST(JSON_EXTRACT(rendered_sections, '$.STORIES' )AS INT64),null)AS rendered_stories,
        if(SAFE_CAST(JSON_EXTRACT(rendered_sections, '$.GROUPS' )AS INT64)>0,      SAFE_CAST(JSON_EXTRACT(rendered_sections, '$.GROUPS' )AS INT64),null)AS rendered_groups,
        if(SAFE_CAST(JSON_EXTRACT(rendered_sections, '$.FRIENDS' )AS INT64)>0,     SAFE_CAST(JSON_EXTRACT(rendered_sections, '$.FRIENDS' )AS INT64),null)AS rendered_friends,
        if(SAFE_CAST(JSON_EXTRACT(rendered_sections, '$.SNAP_BACK' )AS INT64)>0,     SAFE_CAST(JSON_EXTRACT(rendered_sections, '$.SNAP_BACK' )AS INT64),null)AS rendered_snap_back,
        if(SAFE_CAST(JSON_EXTRACT(rendered_sections, '$.LIST' )AS INT64)>0,     SAFE_CAST(JSON_EXTRACT(rendered_sections, '$.LIST' )AS INT64),null)AS rendered_list,
        if(SAFE_CAST(JSON_EXTRACT(rendered_sections, '$.CONTEXTUAL_LIST' )AS INT64)>0,     SAFE_CAST(JSON_EXTRACT(rendered_sections, '$.CONTEXTUAL_LIST' )AS INT64),null)AS rendered_contextual_list,
        if(SAFE_CAST(JSON_EXTRACT(rendered_sections, '$.SPOTLIGHT' )AS INT64)>0,     SAFE_CAST(JSON_EXTRACT(rendered_sections, '$.SPOTLIGHT' )AS INT64),null)AS rendered_spotlight,

        (if(snap_source = 'CAMERA' AND max_rendered_latency>0, max_rendered_latency,NULL))AS CAMERA_rendered_latency_time,
        (if(snap_source = 'GALLERY' AND max_rendered_latency>0, max_rendered_latency,NULL))AS GALLERY_rendered_latency_time,
        (if(snap_source = 'FEED' AND max_rendered_latency>0, max_rendered_latency,NULL))AS FEED_rendered_latency_time,
        (if(snap_source = 'CAMERA_ROLL' AND max_rendered_latency>0, max_rendered_latency,NULL))AS CAMERA_ROLL_rendered_latency_time,
        (if(snap_source = 'IN_CHAT' AND max_rendered_latency>0, max_rendered_latency,NULL))AS IN_CHAT_rendered_latency_time,
        (if(snap_source = 'SHARE' AND max_rendered_latency>0, max_rendered_latency,NULL))AS SHARE_rendered_latency_time,
        (if(snap_source = 'DISCOVER' AND max_rendered_latency>0, max_rendered_latency,NULL))AS DISCOVER_rendered_latency_time,
        (if(snap_source = 'FEED_SNAP_REPLY' AND max_rendered_latency>0, max_rendered_latency,NULL))AS FEED_SNAP_REPLY_rendered_latency_time,

        FROM {source_table}
        """.format(source_table=source_table,
                  ),
        metrics=[
                rendered_latency_time_percentile,
                WARM_rendered_latency_time_percentile,
                COLD_rendered_latency_time_percentile,
                send_to_page_loaded_time_percentile,
                CAMERA_rendered_latency_time,
                GALLERY_rendered_latency_time,
                FEED_rendered_latency_time,
                CAMERA_ROLL_rendered_latency_time,
                IN_CHAT_rendered_latency_time,
                SHARE_rendered_latency_time,
                DISCOVER_rendered_latency_time,
                FEED_SNAP_REPLY_rendered_latency_time,
                rendered_best_friends,
                rendered_recents,
                rendered_stories,
                rendered_groups,
                rendered_friends,
                rendered_snap_back,
                rendered_list,
                rendered_contextual_list,
                rendered_spotlight
                ],
        quantile_metrics=True,
        name="sendto_render_latency_percentile",
        bq_dialect="standard"
    )
    return mt

def sendto_data_ready_view_model_ready(start_date, end_date):
    """
    Percentiles of Send To data ready & view model ready Time
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    source_table = """
    `sc-analytics.report_camera.send_to_latency_raw_events_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    data_ready_BEST_FRIENDS = Metric(
        'data_ready_BEST_FRIENDS',
        'Percentiles of Best Friends Data Ready Time',
        dist='quantile',
        desired_direction=NEGATIVE
    )
    data_ready_RECENTS = Metric(
        'data_ready_RECENTS',
        'Percentiles of Recents Data Ready Time',
        dist='quantile',
        desired_direction=NEGATIVE
    )
    data_ready_STORIES = Metric(
        'data_ready_STORIES',
        'Percentiles of Stories Data Ready Time',
        dist='quantile',
        desired_direction=NEGATIVE
    )
    data_ready_GROUPS = Metric(
        'data_ready_GROUPS',
        'Percentiles of Groups Data Ready Time',
        dist='quantile',
        desired_direction=NEGATIVE
    )
    data_ready_FRIENDS = Metric(
        'data_ready_FRIENDS',
        'Percentiles of Friends Data Ready Time',
        dist='quantile',
        desired_direction=NEGATIVE
    )
    data_ready_SNAP_BACK = Metric(
        'data_ready_SNAP_BACK',
        'Percentiles of SNAP BACK Data Ready Time',
        dist='quantile',
        desired_direction=NEGATIVE
    )
    data_ready_LIST = Metric(
        'data_ready_LIST',
        'Percentiles of LIST Data Ready Time',
        dist='quantile',
        desired_direction=NEGATIVE
    )
    data_ready_CONTEXTUAL_LIST = Metric(
        'data_ready_CONTEXTUAL_LIST',
        'Percentiles of CONTEXTUAL LIST Data Ready Time',
        dist='quantile',
        desired_direction=NEGATIVE
    )
    data_ready_SPOTLIGHT = Metric(
        'data_ready_SPOTLIGHT',
        'Percentiles of SPOTLIGHT Data Ready Time',
        dist='quantile',
        desired_direction=NEGATIVE
    )

    view_model_ready_BEST_FRIENDS = Metric(
        'view_model_ready_BEST_FRIENDS',
        'Percentiles of Best Friends View Model Ready Time',
        dist='quantile',
        desired_direction=NEGATIVE
    )
    view_model_ready_RECENTS = Metric(
        'view_model_ready_RECENTS',
        'Percentiles of Recents View Model Ready Time',
        dist='quantile',
        desired_direction=NEGATIVE
    )
    view_model_ready_STORIES = Metric(
        'view_model_ready_STORIES',
        'Percentiles of Stories View Model Ready Time',
        dist='quantile',
        desired_direction=NEGATIVE
    )
    view_model_ready_GROUPS = Metric(
        'view_model_ready_GROUPS',
        'Percentiles of Groups View Model Ready Time',
        dist='quantile',
        desired_direction=NEGATIVE
    )
    view_model_ready_FRIENDS = Metric(
        'view_model_ready_FRIENDS',
        'Percentiles of Friends View Model Ready Time',
        dist='quantile',
        desired_direction=NEGATIVE
    )
    view_model_ready_SNAP_BACK = Metric(
        'view_model_ready_SNAP_BACK',
        'Percentiles of SNAP BACK View Model Ready Time',
        dist='quantile',
        desired_direction=NEGATIVE
    )
    view_model_ready_LIST = Metric(
        'view_model_ready_LIST',
        'Percentiles of LIST View Model Ready Time',
        dist='quantile',
        desired_direction=NEGATIVE
    )
    view_model_ready_CONTEXTUAL_LIST = Metric(
        'view_model_ready_CONTEXTUAL_LIST',
        'Percentiles of CONTEXTUAL LIST View Model Ready Time',
        dist='quantile',
        desired_direction=NEGATIVE
    )
    view_model_ready_SPOTLIGHT = Metric(
        'view_model_ready_SPOTLIGHT',
        'Percentiles of SPOTLIGHT View Model Ready Time',
        dist='quantile',
        desired_direction=NEGATIVE
    )

    send_to_init_time_percentile_ = Metric(
        'send_to_init_time_percentile_',
        'Percentiles of SendTo Init Latency Time',
        dist='quantile',
        desired_direction=NEGATIVE
    )

    send_to_view_created_time_percentile_ = Metric(
        'send_to_view_created_time_percentile_',
        'Percentiles of SendTo View Created Latency Time',
        dist='quantile',
        desired_direction=NEGATIVE
    )

    send_to_page_loaded_time_percentile_ = Metric(
        'send_to_page_loaded_time_percentile_',
        'Percentiles of SendTo Page Loaded Latency Time',
        dist='quantile',
        desired_direction=NEGATIVE
    )

    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        ghost_user_id,

        (if(splits_SEND_TO_PAGE_LOADED>0, splits_SEND_TO_PAGE_LOADED,NULL)) AS send_to_page_loaded_time_percentile_,
        (if(splits_SEND_TO_INIT>0, splits_SEND_TO_INIT,NULL)) AS send_to_init_time_percentile_,
        (if(splits_SEND_TO_VIEW_CREATED>0, splits_SEND_TO_VIEW_CREATED,NULL)) AS send_to_view_created_time_percentile_,

        if(SAFE_CAST(JSON_EXTRACT(data_ready_sections, '$.BEST_FRIENDS' ) AS INT64) > 0, SAFE_CAST(JSON_EXTRACT(data_ready_sections, '$.BEST_FRIENDS' ) AS INT64), null) as data_ready_BEST_FRIENDS,
        if(SAFE_CAST(JSON_EXTRACT(data_ready_sections, '$.RECENTS' ) AS INT64) > 0, SAFE_CAST(JSON_EXTRACT(data_ready_sections, '$.RECENTS' ) AS INT64), null) as data_ready_RECENTS,
        if(SAFE_CAST(JSON_EXTRACT(data_ready_sections, '$.STORIES' ) AS INT64) > 0, SAFE_CAST(JSON_EXTRACT(data_ready_sections, '$.STORIES' ) AS INT64), null) as data_ready_STORIES,
        if(SAFE_CAST(JSON_EXTRACT(data_ready_sections, '$.GROUPS' ) AS INT64) > 0, SAFE_CAST(JSON_EXTRACT(data_ready_sections, '$.GROUPS' ) AS INT64), null) as data_ready_GROUPS,
        if(SAFE_CAST(JSON_EXTRACT(data_ready_sections, '$.FRIENDS' ) AS INT64) > 0, SAFE_CAST(JSON_EXTRACT(data_ready_sections, '$.FRIENDS' ) AS INT64), null) as data_ready_FRIENDS,
        if(SAFE_CAST(JSON_EXTRACT(data_ready_sections, '$.SNAP_BACK' ) AS INT64) > 0, SAFE_CAST(JSON_EXTRACT(data_ready_sections, '$.SNAP_BACK' ) AS INT64), null) as data_ready_SNAP_BACK,
        if(SAFE_CAST(JSON_EXTRACT(data_ready_sections, '$.LIST' ) AS INT64) > 0, SAFE_CAST(JSON_EXTRACT(data_ready_sections, '$.LIST' ) AS INT64), null) as data_ready_LIST,
        if(SAFE_CAST(JSON_EXTRACT(data_ready_sections, '$.CONTEXTUAL_LIST' ) AS INT64) > 0, SAFE_CAST(JSON_EXTRACT(data_ready_sections, '$.CONTEXTUAL_LIST' ) AS INT64), null) as data_ready_CONTEXTUAL_LIST,
        if(SAFE_CAST(JSON_EXTRACT(data_ready_sections, '$.SPOTLIGHT' ) AS INT64) > 0, SAFE_CAST(JSON_EXTRACT(data_ready_sections, '$.SPOTLIGHT' ) AS INT64), null) as data_ready_SPOTLIGHT,

        if(SAFE_CAST(JSON_EXTRACT(view_model_ready_sections, '$.BEST_FRIENDS' ) AS INT64) > 0, SAFE_CAST(JSON_EXTRACT(view_model_ready_sections, '$.BEST_FRIENDS' ) AS INT64), null) as view_model_ready_BEST_FRIENDS,
        if(SAFE_CAST(JSON_EXTRACT(view_model_ready_sections, '$.RECENTS' ) AS INT64) > 0, SAFE_CAST(JSON_EXTRACT(view_model_ready_sections, '$.RECENTS' ) AS INT64), null) as view_model_ready_RECENTS,
        if(SAFE_CAST(JSON_EXTRACT(view_model_ready_sections, '$.STORIES' ) AS INT64) > 0, SAFE_CAST(JSON_EXTRACT(view_model_ready_sections, '$.STORIES' ) AS INT64), null) as view_model_ready_STORIES,
        if(SAFE_CAST(JSON_EXTRACT(view_model_ready_sections, '$.GROUPS' ) AS INT64) > 0, SAFE_CAST(JSON_EXTRACT(view_model_ready_sections, '$.GROUPS' ) AS INT64), null) as view_model_ready_GROUPS,
        if(SAFE_CAST(JSON_EXTRACT(view_model_ready_sections, '$.FRIENDS' ) AS INT64) > 0, SAFE_CAST(JSON_EXTRACT(view_model_ready_sections, '$.FRIENDS' ) AS INT64), null) as view_model_ready_FRIENDS,
        if(SAFE_CAST(JSON_EXTRACT(view_model_ready_sections, '$.SNAP_BACK' ) AS INT64) > 0, SAFE_CAST(JSON_EXTRACT(view_model_ready_sections, '$.SNAP_BACK' ) AS INT64), null) as view_model_ready_SNAP_BACK,
        if(SAFE_CAST(JSON_EXTRACT(view_model_ready_sections, '$.LIST' ) AS INT64) > 0, SAFE_CAST(JSON_EXTRACT(view_model_ready_sections, '$.LIST' ) AS INT64), null) as view_model_ready_LIST,
        if(SAFE_CAST(JSON_EXTRACT(view_model_ready_sections, '$.CONTEXTUAL_LIST' ) AS INT64) > 0, SAFE_CAST(JSON_EXTRACT(view_model_ready_sections, '$.CONTEXTUAL_LIST' ) AS INT64), null) as view_model_ready_CONTEXTUAL_LIST,
        if(SAFE_CAST(JSON_EXTRACT(view_model_ready_sections, '$.SPOTLIGHT' ) AS INT64) > 0, SAFE_CAST(JSON_EXTRACT(view_model_ready_sections, '$.SPOTLIGHT' ) AS INT64), null) as view_model_ready_SPOTLIGHT,

        FROM {source_table}
        """.format(source_table=source_table,
                  ),
        metrics=[
            send_to_init_time_percentile_,
            send_to_page_loaded_time_percentile_,
            send_to_view_created_time_percentile_,

            data_ready_BEST_FRIENDS,
            data_ready_RECENTS,
            data_ready_STORIES,
            data_ready_GROUPS,
            data_ready_FRIENDS,
            data_ready_SNAP_BACK,
            data_ready_LIST,
            data_ready_CONTEXTUAL_LIST,
            data_ready_SPOTLIGHT,

            view_model_ready_BEST_FRIENDS,
            view_model_ready_RECENTS,
            view_model_ready_STORIES,
            view_model_ready_GROUPS,
            view_model_ready_FRIENDS,
            view_model_ready_SNAP_BACK,
            view_model_ready_LIST,
            view_model_ready_CONTEXTUAL_LIST,
            view_model_ready_SPOTLIGHT,
        ],
        quantile_metrics=True,
        name="sendto_data_ready_view_model_ready",
        bq_dialect="standard"
    )
    return mt

def sendto_candidate_side_dau_delta(start_date, end_date):
    """
    SendTo canddidate side DAU delta
    """
    
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    source_table = """
    `sc-analytics.report_growth.sendto_impression_action_candidate_dau_delta_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    n_candidate_selections_individual = Metric('n_candidate_selections_individual', 'Candidates Selected (Individual)', dist='cont', daily=True, cumulative=True)
    n_candidate_selections_via_group = Metric('n_candidate_selections_via_group', 'Candidates Selected (Via Group)', dist='cont', daily=True, cumulative=True)
    n_candidate_selections = Metric('n_candidate_selections', 'Candidates Selected (Any)', dist='cont', daily=True, cumulative=True)
    n_candidate_selections_individual_positive_delta = Metric('n_candidate_selections_individual_positive_delta', 'Candidates Selected (Individual + Positive DAU Delta)', dist='cont', daily=True, cumulative=True)
    n_candidate_selections_via_group_positive_delta = Metric('n_candidate_selections_via_group_positive_delta', 'Candidates Selected (Via Group + Positive DAU Delta)', dist='cont', daily=True, cumulative=True)
    n_candidate_selections_positive_delta = Metric('n_candidate_selections_positive_delta', 'Candidates Selected (Any + Positive DAU Delta)', dist='cont', daily=True, cumulative=True)
    n_candidate_lost_streak = Metric('n_candidate_lost_streak', 'Candidates Lost Streak', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    n_candidate_lost_streak_negative_delta = Metric('n_candidate_lost_streak_negative_delta', 'Candidates Lost Streak (Negative DAU Delta)', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    sum_candidate_is_dau_delta_selections_individual = Metric('sum_candidate_is_dau_delta_selections_individual', 'Candidate DAU Delta Sum (Selections Individual)', dist='cont', daily=True, cumulative=True)
    sum_candidate_is_dau_delta_selections_via_group = Metric('sum_candidate_is_dau_delta_selections_via_group', 'Candidate DAU Delta Sum (Selections Via Group)', dist='cont', daily=True, cumulative=True)
    sum_candidate_is_dau_delta_selections = Metric('sum_candidate_is_dau_delta_selections', 'Candidate DAU Delta Sum (Selections Any)', dist='cont', daily=True, cumulative=True)
    sum_candidate_is_dau_delta_lost_streak = Metric('sum_candidate_is_dau_delta_lost_streak', 'Candidate DAU Delta Sum (Lost Streak)', dist='cont', daily=True, cumulative=True)

    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        ghost_user_id,

        count(distinct if(n_selections_individual > 0, candidate_ghost_user_id, NULL)) as n_candidate_selections_individual,
        count(distinct if(n_selections_via_group > 0, candidate_ghost_user_id, NULL)) as n_candidate_selections_via_group,
        count(distinct if(n_selections > 0, candidate_ghost_user_id, NULL)) as n_candidate_selections,
        count(distinct if(n_selections_individual > 0 and candidate_is_dau_delta > 0, candidate_ghost_user_id, NULL)) as n_candidate_selections_individual_positive_delta,
        count(distinct if(n_selections_via_group > 0 and candidate_is_dau_delta > 0, candidate_ghost_user_id, NULL)) as n_candidate_selections_via_group_positive_delta,
        count(distinct if(n_selections > 0 and candidate_is_dau_delta > 0, candidate_ghost_user_id, NULL)) as n_candidate_selections_positive_delta,
        sum(if(n_selections_individual > 0, candidate_is_dau_delta, 0)) as sum_candidate_is_dau_delta_selections_individual,
        sum(if(n_selections_via_group > 0, candidate_is_dau_delta, 0)) as sum_candidate_is_dau_delta_selections_via_group,
        sum(if(n_selections > 0, candidate_is_dau_delta, 0)) as sum_candidate_is_dau_delta_selections,

        count(distinct if(is_streak_friend_1md and not is_streak_friend, candidate_ghost_user_id, NULL)) as n_candidate_lost_streak,
        count(distinct if(is_streak_friend_1md and not is_streak_friend and candidate_is_dau_delta < 0, candidate_ghost_user_id, NULL)) as n_candidate_lost_streak_negative_delta,
        sum(if(is_streak_friend_1md and not is_streak_friend, candidate_is_dau_delta, 0)) as sum_candidate_is_dau_delta_lost_streak,

        FROM {source_table}
        group by 1, 2
        """.format(source_table=source_table),
        metrics=[
            n_candidate_selections_individual,
            n_candidate_selections_via_group,
            n_candidate_selections,
            n_candidate_selections_individual_positive_delta,
            n_candidate_selections_via_group_positive_delta,
            n_candidate_selections_positive_delta,
            sum_candidate_is_dau_delta_selections_individual,
            sum_candidate_is_dau_delta_selections_via_group,
            sum_candidate_is_dau_delta_selections,
            n_candidate_lost_streak,
            n_candidate_lost_streak_negative_delta,
            sum_candidate_is_dau_delta_lost_streak            
        ],
        quantile_metrics=False,
        name="sendto_candidate_side_dau_delta",
        bq_dialect="standard"
    )
    return mt    
    
def off_platform_share(start_date, end_date):
    """
    Off-platform shares with specific media, link types, destinations
    """

    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
    `sc-analytics.report_growth.off_platform_share_2*`
    WHERE concat('2', _table_suffix) between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    ops_selected = Metric('ops_selected', 'ops_selected', dist='cont', daily=True, cumulative=True,)
    ops_selected_uu = Metric('ops_selected_uu', 'ops_selected_uu', dist='bin', daily=True, cumulative=True,)
    ops_selected_active_day = Metric('ops_selected_active_day', 'ops_selected_active_day', dist='cont', daily=True, cumulative=True,)
    ops_selected_excl_auto_copy_link = Metric('ops_selected_excl_auto_copy_link', 'ops_selected_excl_auto_copy_link', dist='cont', daily=True, cumulative=True,)
    ops_selected_uu_excl_auto_copy_link = Metric('ops_selected_uu_excl_auto_copy_link', 'ops_selected_uu_excl_auto_copy_link', dist='bin', daily=True, cumulative=True,)
    ops_selected_active_day_excl_auto_copy_link = Metric('ops_selected_active_day_excl_auto_copy_link', 'ops_selected_active_day_excl_auto_copy_link', dist='cont', daily=True, cumulative=True,)
    ops_selected_by_new_user = Metric('ops_selected_by_new_user', 'ops_selected_by_new_user', dist='cont', daily=True, cumulative=True,)
    ops_selected_by_new_user_uu = Metric('ops_selected_by_new_user_uu', 'ops_selected_by_new_user_uu', dist='bin', daily=True, cumulative=True,)
    ops_selected_by_new_user_active_day = Metric('ops_selected_by_new_user_active_day', 'ops_selected_by_new_user_active_day', dist='cont', daily=True, cumulative=True,)
    ops_selected_with_url = Metric('ops_selected_with_url', 'ops_selected_with_url', dist='cont', daily=True, cumulative=True,)
    ops_selected_with_url_uu = Metric('ops_selected_with_url_uu', 'ops_selected_with_url_uu', dist='bin', daily=True, cumulative=True,)
    ops_selected_with_url_active_day = Metric('ops_selected_with_url_active_day', 'ops_selected_with_url_active_day', dist='cont', daily=True, cumulative=True,)
    ops_selected_with_watermark = Metric('ops_selected_with_watermark', 'ops_selected_with_watermark', dist='cont', daily=True, cumulative=True,)
    ops_selected_with_watermark_uu = Metric('ops_selected_with_watermark_uu', 'ops_selected_with_watermark_uu', dist='bin', daily=True, cumulative=True,)
    ops_selected_with_watermark_active_day = Metric('ops_selected_with_watermark_active_day', 'ops_selected_with_watermark_active_day', dist='cont', daily=True, cumulative=True,)
    ops_selected_image_with_url = Metric('ops_selected_image_with_url', 'ops_selected_image_with_url', dist='cont', daily=True, cumulative=True,)
    ops_selected_image_with_url_uu = Metric('ops_selected_image_with_url_uu', 'ops_selected_image_with_url_uu', dist='bin', daily=True, cumulative=True,)
    ops_selected_image_with_url_active_day = Metric('ops_selected_image_with_url_active_day', 'ops_selected_image_with_url_active_day', dist='cont', daily=True, cumulative=True,)
    ops_selected_video_with_url = Metric('ops_selected_video_with_url', 'ops_selected_video_with_url', dist='cont', daily=True, cumulative=True,)
    ops_selected_video_with_url_uu = Metric('ops_selected_video_with_url_uu', 'ops_selected_video_with_url_uu', dist='bin', daily=True, cumulative=True,)
    ops_selected_video_with_url_active_day = Metric('ops_selected_video_with_url_active_day', 'ops_selected_video_with_url_active_day', dist='cont', daily=True, cumulative=True,)
    ops_selected_with_lens = Metric('ops_selected_with_lens', 'ops_selected_with_lens', dist='cont', daily=True, cumulative=True,)
    ops_selected_with_lens_uu = Metric('ops_selected_with_lens_uu', 'ops_selected_with_lens_uu', dist='bin', daily=True, cumulative=True,)
    ops_selected_with_lens_active_day = Metric('ops_selected_with_lens_active_day', 'ops_selected_with_lens_active_day', dist='cont', daily=True, cumulative=True,)
    ops_selected_from_profile = Metric('ops_selected_from_profile', 'ops_selected_from_profile', dist='cont', daily=True, cumulative=True,)
    ops_selected_from_send_to = Metric('ops_selected_from_send_to', 'ops_selected_from_send_to', dist='cont', daily=True, cumulative=True,)
    ops_selected_from_memories = Metric('ops_selected_from_memories', 'ops_selected_from_memories', dist='cont', daily=True, cumulative=True,)
    ops_selected_from_memories_fallback = Metric('ops_selected_from_memories_fallback', 'ops_selected_from_memories_fallback', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    ops_selected_from_lens_info_card = Metric('ops_selected_from_lens_info_card', 'ops_selected_from_lens_info_card', dist='cont', daily=True, cumulative=True,)
    ops_selected_from_lens_info_card_non_auto_copy_link = Metric('ops_selected_from_lens_info_card_non_auto_copy_link', 'ops_selected_from_lens_info_card_non_auto_copy_link', dist='cont', daily=True, cumulative=True,)
    ops_selected_from_camera_preview = Metric('ops_selected_from_camera_preview', 'ops_selected_from_camera_preview', dist='cont', daily=True, cumulative=True,)
    ops_selected_from_registration_invites = Metric('ops_selected_from_registration_invites', 'ops_selected_from_registration_invites', dist='cont', daily=True, cumulative=True,)
    ops_selected_share_sheet_type_twilio = Metric('ops_selected_share_sheet_type_twilio', 'ops_selected_share_sheet_type_twilio', dist='cont', daily=True, cumulative=True,)  
    ops_selected_share_sheet_type_phone = Metric('ops_selected_share_sheet_type_phone', 'ops_selected_share_sheet_type_phone', dist='cont', daily=True, cumulative=True,)  
    ops_selected_share_sheet_type_post_screenshot = Metric('ops_selected_share_sheet_type_post_screenshot', 'ops_selected_share_sheet_type_post_screenshot', dist='cont', daily=True, cumulative=True,)  
    ops_selected_share_sheet_type_button = Metric('ops_selected_share_sheet_type_button', 'ops_selected_share_sheet_type_button', dist='cont', daily=True, cumulative=True,)  
    ops_selected_share_sheet_type_inline = Metric('ops_selected_share_sheet_type_inline', 'ops_selected_share_sheet_type_inline', dist='cont', daily=True, cumulative=True,)  
    ops_selected_page_tab_type_discover = Metric('ops_selected_page_tab_type_discover', 'ops_selected_page_tab_type_discover', dist='cont', daily=True, cumulative=True,)  
    ops_selected_page_tab_type_chat = Metric('ops_selected_page_tab_type_chat', 'ops_selected_page_tab_type_chat', dist='cont', daily=True, cumulative=True,)  
    ops_selected_page_tab_type_maps = Metric('ops_selected_page_tab_type_maps', 'ops_selected_page_tab_type_maps', dist='cont', daily=True, cumulative=True,)  
    ops_selected_page_tab_type_spotlight = Metric('ops_selected_page_tab_type_spotlight', 'ops_selected_page_tab_type_spotlight', dist='cont', daily=True, cumulative=True,)  
    ops_selected_page_tab_type_camera = Metric('ops_selected_page_tab_type_camera', 'ops_selected_page_tab_type_camera', dist='cont', daily=True, cumulative=True,)  
    ops_selected_page_tab_type_unknown = Metric('ops_selected_page_tab_type_unknown', 'ops_selected_page_tab_type_unknown', dist='cont', daily=True, cumulative=True,)      
    ops_selected_to_system_share = Metric('ops_selected_to_system_share', 'ops_selected_to_system_share', dist='cont', daily=True, cumulative=True,)
    ops_selected_to_whatsapp = Metric('ops_selected_to_whatsapp', 'ops_selected_to_whatsapp', dist='cont', daily=True, cumulative=True,)
    ops_selected_to_messenger = Metric('ops_selected_to_messenger', 'ops_selected_to_messenger', dist='cont', daily=True, cumulative=True,)
    ops_selected_to_auto_copy_link = Metric('ops_selected_to_auto_copy_link', 'ops_selected_to_auto_copy_link', dist='cont', daily=True, cumulative=True,)
    ops_selected_to_copy_link = Metric('ops_selected_to_copy_link', 'ops_selected_to_copy_link', dist='cont', daily=True, cumulative=True,)
    ops_selected_to_sms = Metric('ops_selected_to_sms', 'ops_selected_to_sms', dist='cont', daily=True, cumulative=True,)
    ops_selected_to_twitter = Metric('ops_selected_to_twitter', 'ops_selected_to_twitter', dist='cont', daily=True, cumulative=True,)
    ops_selected_to_facebook = Metric('ops_selected_to_facebook', 'ops_selected_to_facebook', dist='cont', daily=True, cumulative=True,)
    ops_selected_to_camera_roll = Metric('ops_selected_to_camera_roll', 'ops_selected_to_camera_roll', dist='cont', daily=True, cumulative=True,)
    ops_selected_to_instagram = Metric('ops_selected_to_instagram', 'ops_selected_to_instagram', dist='cont', daily=True, cumulative=True,)
    ops_selected_to_instagram_direct = Metric('ops_selected_to_instagram_direct', 'ops_selected_to_instagram_direct', dist='cont', daily=True, cumulative=True,)
    ops_selected_to_instagram_story = Metric('ops_selected_to_instagram_story', 'ops_selected_to_instagram_story', dist='cont', daily=True, cumulative=True,)
    ops_selected_to_tiktok = Metric('ops_selected_to_tiktok', 'ops_selected_to_tiktok', dist='cont', daily=True, cumulative=True,)
    ops_selected_to_telegram = Metric('ops_selected_to_telegram', 'ops_selected_to_telegram', dist='cont', daily=True, cumulative=True,)
    ops_selected_to_discord = Metric('ops_selected_to_discord', 'ops_selected_to_discord', dist='cont', daily=True, cumulative=True,)
    ops_selected_to_line = Metric('ops_selected_to_line', 'ops_selected_to_line', dist='cont', daily=True, cumulative=True,)
    ops_selected_to_viber = Metric('ops_selected_to_viber', 'ops_selected_to_viber', dist='cont', daily=True, cumulative=True,)
    ops_selected_to_kakao = Metric('ops_selected_to_kakao', 'ops_selected_to_kakao', dist='cont', daily=True, cumulative=True,)
    ops_selected_to_signal = Metric('ops_selected_to_signal', 'ops_selected_to_signal', dist='cont', daily=True, cumulative=True,)
    ops_selected_to_imo = Metric('ops_selected_to_imo', 'ops_selected_to_imo', dist='cont', daily=True, cumulative=True,)
    ops_selected_to_jiochat = Metric('ops_selected_to_jiochat', 'ops_selected_to_jiochat', dist='cont', daily=True, cumulative=True,)
    ops_selected_with_add_friend_link = Metric('ops_selected_with_add_friend_link', 'ops_selected_with_add_friend_link', dist='cont', daily=True, cumulative=True,)
    ops_selected_with_commerce_link = Metric('ops_selected_with_commerce_link', 'ops_selected_with_commerce_link', dist='cont', daily=True, cumulative=True,)
    ops_selected_with_discover_link = Metric('ops_selected_with_discover_link', 'ops_selected_with_discover_link', dist='cont', daily=True, cumulative=True,)
    ops_selected_with_lenses_link = Metric('ops_selected_with_lenses_link', 'ops_selected_with_lenses_link', dist='cont', daily=True, cumulative=True,)
    ops_selected_with_map_link = Metric('ops_selected_with_map_link', 'ops_selected_with_map_link', dist='cont', daily=True, cumulative=True,)
    ops_selected_with_our_story_link = Metric('ops_selected_with_our_story_link', 'ops_selected_with_our_story_link', dist='cont', daily=True, cumulative=True,)
    ops_selected_with_profile_link = Metric('ops_selected_with_profile_link', 'ops_selected_with_profile_link', dist='cont', daily=True, cumulative=True,)
    ops_selected_with_public_user_story_link = Metric('ops_selected_with_public_user_story_link', 'ops_selected_with_public_user_story_link', dist='cont', daily=True, cumulative=True,)
    ops_selected_with_snap_pro_link = Metric('ops_selected_with_snap_pro_link', 'ops_selected_with_snap_pro_link', dist='cont', daily=True, cumulative=True,)
    ops_selected_with_memories_link = Metric('ops_selected_with_memories_link', 'ops_selected_with_memories_link', dist='cont', daily=True, cumulative=True,)
    ops_selected_with_saved_story_link = Metric('ops_selected_with_saved_story_link', 'ops_selected_with_saved_story_link', dist='cont', daily=True, cumulative=True,)

    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', concat('2', _table_suffix)) as ts,
        ghost_user_id,

        SUM(IF(destination_selected is not null, if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected,
        MAX(IF(destination_selected is not null, 1, 0)) AS ops_selected_uu,
        MAX(IF(destination_selected is not null, 1, 0)) AS ops_selected_active_day,
        SUM(IF(destination_selected is not null AND destination_selected NOT LIKE '%AUTO_COPY_LINK%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_excl_auto_copy_link,
        MAX(IF(destination_selected is not null AND destination_selected NOT LIKE '%AUTO_COPY_LINK%', 1, 0)) AS ops_selected_uu_excl_auto_copy_link,
        MAX(IF(destination_selected is not null AND destination_selected NOT LIKE '%AUTO_COPY_LINK%', 1, 0)) AS ops_selected_active_day_excl_auto_copy_link,        
        SUM(IF(destination_selected is not null AND date(client_ts) = date(creation_time), if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_by_new_user,
        MAX(IF(destination_selected is not null AND date(client_ts) = date(creation_time), 1, 0)) AS ops_selected_by_new_user_uu,
        MAX(IF(destination_selected is not null AND date(client_ts) = date(creation_time), 1, 0)) AS ops_selected_by_new_user_active_day,
        SUM(IF(destination_selected is not null AND deep_link_url_hash is not null, if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_with_url,
        MAX(IF(destination_selected is not null AND deep_link_url_hash is not null, 1, 0)) AS ops_selected_with_url_uu,
        MAX(IF(destination_selected is not null AND deep_link_url_hash is not null, 1, 0)) AS ops_selected_with_url_active_day,
        SUM(IF(destination_selected is not null AND has_watermark = true, if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_with_watermark,
        MAX(IF(destination_selected is not null AND has_watermark = true, 1, 0)) AS ops_selected_with_watermark_uu,
        MAX(IF(destination_selected is not null AND has_watermark = true, 1, 0)) AS ops_selected_with_watermark_active_day,
        SUM(IF(destination_selected is not null AND deep_link_url_hash is not null AND media_type IN ('IMAGE'), if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_image_with_url,
        MAX(IF(destination_selected is not null AND deep_link_url_hash is not null AND media_type IN ('IMAGE'), 1, 0)) AS ops_selected_image_with_url_uu,
        MAX(IF(destination_selected is not null AND deep_link_url_hash is not null AND media_type IN ('IMAGE'), 1, 0)) AS ops_selected_image_with_url_active_day,
        SUM(IF(destination_selected is not null AND deep_link_url_hash is not null AND media_type IN ('VIDEO', 'VIDEO_NO_SOUND'), if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_video_with_url,
        MAX(IF(destination_selected is not null AND deep_link_url_hash is not null AND media_type IN ('VIDEO', 'VIDEO_NO_SOUND'), 1, 0)) AS ops_selected_video_with_url_uu,
        MAX(IF(destination_selected is not null AND deep_link_url_hash is not null AND media_type IN ('VIDEO', 'VIDEO_NO_SOUND'), 1, 0)) AS ops_selected_video_with_url_active_day,
        SUM(IF(destination_selected is not null AND filter_lens_id_hash is not null, if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_with_lens,
        MAX(IF(destination_selected is not null AND filter_lens_id_hash is not null, 1, 0)) AS ops_selected_with_lens_uu,
        MAX(IF(destination_selected is not null AND filter_lens_id_hash is not null, 1, 0)) AS ops_selected_with_lens_active_day,

        SUM(IF(destination_selected is not null AND source = "PROFILE", if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_from_profile,
        SUM(IF(destination_selected is not null AND source = "SEND_TO", if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_from_send_to,
        SUM(IF(destination_selected is not null AND source = "MEMORIES", if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_from_memories,
        SUM(IF(destination_selected is not null AND source = "MEMORIES" and deep_link_source is not null and (deep_link_source = 'ADD_FRIEND' OR deep_link_source = 'LENSES'), if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_from_memories_fallback,
        SUM(IF(destination_selected is not null AND source = "LENS_INFO_CARD", if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_from_lens_info_card,
        SUM(IF(destination_selected is not null AND source = "LENS_INFO_CARD" AND destination_selected NOT LIKE '%AUTO_COPY_LINK%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_from_lens_info_card_non_auto_copy_link,
        SUM(IF(destination_selected IS not null AND source = 'CAMERA_PREVIEW', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_from_camera_preview,
        SUM(IF(destination_selected IS not null AND source = 'REGISTRATION_INVITES', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_from_registration_invites,

        SUM(IF(destination_selected IS NOT NULL AND share_sheet_type = 'TWILIO', IF(multi_selection_count >= 1, multi_selection_count, 1), 0)) AS ops_selected_share_sheet_type_twilio,  
        SUM(IF(destination_selected IS NOT NULL AND share_sheet_type = 'PHONE', IF(multi_selection_count >= 1, multi_selection_count, 1), 0)) AS ops_selected_share_sheet_type_phone,  
        SUM(IF(destination_selected IS NOT NULL AND share_sheet_type = 'POST_SCREENSHOT', IF(multi_selection_count >= 1, multi_selection_count, 1), 0)) AS ops_selected_share_sheet_type_post_screenshot,  
        SUM(IF(destination_selected IS NOT NULL AND share_sheet_type = 'BUTTON', IF(multi_selection_count >= 1, multi_selection_count, 1), 0)) AS ops_selected_share_sheet_type_button,  
        SUM(IF(destination_selected IS NOT NULL AND share_sheet_type = 'INLINE', IF(multi_selection_count >= 1, multi_selection_count, 1), 0)) AS ops_selected_share_sheet_type_inline,

        SUM(IF(destination_selected IS NOT NULL AND page_tab_type = 'DISCOVER', IF(multi_selection_count >= 1, multi_selection_count, 1), 0)) AS ops_selected_page_tab_type_discover,  
        SUM(IF(destination_selected IS NOT NULL AND page_tab_type = 'CHAT', IF(multi_selection_count >= 1, multi_selection_count, 1), 0)) AS ops_selected_page_tab_type_chat,  
        SUM(IF(destination_selected IS NOT NULL AND page_tab_type = 'MAPS', IF(multi_selection_count >= 1, multi_selection_count, 1), 0)) AS ops_selected_page_tab_type_maps,  
        SUM(IF(destination_selected IS NOT NULL AND page_tab_type = 'SPOTLIGHT', IF(multi_selection_count >= 1, multi_selection_count, 1), 0)) AS ops_selected_page_tab_type_spotlight,  
        SUM(IF(destination_selected IS NOT NULL AND page_tab_type = 'CAMERA', IF(multi_selection_count >= 1, multi_selection_count, 1), 0)) AS ops_selected_page_tab_type_camera,  
        SUM(IF(destination_selected IS NOT NULL AND page_tab_type = 'UNKNOWN', IF(multi_selection_count >= 1, multi_selection_count, 1), 0)) AS ops_selected_page_tab_type_unknown,  

        SUM(IF(destination_selected LIKE '%SYSTEM_SHARE%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_to_system_share,
        SUM(IF(destination_selected LIKE '%WHATSAPP%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_to_whatsapp,
        SUM(IF(destination_selected LIKE '%MESSENGER%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_to_messenger,
        SUM(IF(destination_selected LIKE '%AUTO_COPY_LINK%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_to_auto_copy_link,
        SUM(IF(destination_selected LIKE 'COPY_LINK%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_to_copy_link,
        SUM(IF(destination_selected LIKE '%SMS%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_to_sms,
        SUM(IF(destination_selected LIKE '%TWITTER%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_to_twitter,
        SUM(IF(destination_selected LIKE '%FACEBOOK%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_to_facebook,
        SUM(IF(destination_selected LIKE '%CAMERA_ROLL%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_to_camera_roll,
        SUM(IF(destination_selected = 'INSTAGRAM', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_to_instagram,
        SUM(IF(destination_selected = 'INSTAGRAM_DIRECT', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_to_instagram_direct,
        SUM(IF(destination_selected = 'INSTAGRAM_STORY', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_to_instagram_story,
        SUM(IF(destination_selected LIKE '%TIKTOK%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_to_tiktok,
        SUM(IF(destination_selected LIKE '%TELEGRAM%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_to_telegram,
        SUM(IF(destination_selected LIKE '%DISCORD%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_to_discord,
        SUM(IF(destination_selected LIKE 'LINE%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_to_line,
        SUM(IF(destination_selected LIKE '%VIBER%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_to_viber,
        SUM(IF(destination_selected LIKE '%KAKAO%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_to_kakao,
        SUM(IF(destination_selected LIKE '%SIGNAL%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_to_signal,
        SUM(IF(destination_selected LIKE '%IMO%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_to_imo,
        SUM(IF(destination_selected LIKE '%JIO_CHAT%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_to_jiochat,

        SUM(IF(destination_selected is not null AND deep_link_source LIKE '%ADD_FRIEND%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_with_add_friend_link,
        SUM(IF(destination_selected is not null AND deep_link_source LIKE '%COMMERCE%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_with_commerce_link,
        SUM(IF(destination_selected is not null AND deep_link_source LIKE '%DISCOVER%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_with_discover_link,
        SUM(IF(destination_selected is not null AND deep_link_source LIKE '%LENSES%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_with_lenses_link,
        SUM(IF(destination_selected is not null AND deep_link_source LIKE '%MAP%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_with_map_link,
        SUM(IF(destination_selected is not null AND deep_link_source LIKE '%OUR_STORY%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_with_our_story_link,
        SUM(IF(destination_selected is not null AND deep_link_source LIKE '%PROFILE%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_with_profile_link,
        SUM(IF(destination_selected is not null AND deep_link_source LIKE '%PUBLIC_USER_STORY%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_with_public_user_story_link,
        SUM(IF(destination_selected is not null AND deep_link_source LIKE '%SNAP_PRO%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_with_snap_pro_link,
        SUM(IF(destination_selected is not null AND (deep_link_source LIKE '%MEMORIES_LINK%' OR deep_link_source LIKE '%MEMORIES%'), if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_with_memories_link,
        SUM(IF(destination_selected is not null AND deep_link_source LIKE '%SAVED_STORY%', if(multi_selection_count>=1,multi_selection_count,1), 0)) AS ops_selected_with_saved_story_link,

        FROM {source_table}
        GROUP BY
        ts,
        ghost_user_id
        """.format(source_table=source_table),

        metrics=[
        ops_selected,
        ops_selected_uu,
        ops_selected_active_day,
        ops_selected_excl_auto_copy_link,
        ops_selected_uu_excl_auto_copy_link,
        ops_selected_active_day_excl_auto_copy_link,
        ops_selected_by_new_user,
        ops_selected_by_new_user_uu,
        ops_selected_by_new_user_active_day,
        ops_selected_with_url,
        ops_selected_with_url_uu,
        ops_selected_with_url_active_day,
        ops_selected_with_watermark,
        ops_selected_with_watermark_uu,
        ops_selected_with_watermark_active_day,
        ops_selected_image_with_url,
        ops_selected_image_with_url_uu,
        ops_selected_image_with_url_active_day,
        ops_selected_video_with_url,
        ops_selected_video_with_url_uu,
        ops_selected_video_with_url_active_day,
        ops_selected_with_lens,
        ops_selected_with_lens_uu,
        ops_selected_with_lens_active_day,
        ops_selected_from_profile,
        ops_selected_from_send_to,
        ops_selected_from_memories,
        ops_selected_from_memories_fallback,
        ops_selected_from_lens_info_card,
        ops_selected_from_lens_info_card_non_auto_copy_link,
        ops_selected_from_camera_preview,
        ops_selected_from_registration_invites,
        ops_selected_share_sheet_type_twilio,
        ops_selected_share_sheet_type_phone,
        ops_selected_share_sheet_type_post_screenshot,
        ops_selected_share_sheet_type_button,
        ops_selected_share_sheet_type_inline,
        ops_selected_page_tab_type_discover,
        ops_selected_page_tab_type_chat,
        ops_selected_page_tab_type_maps,
        ops_selected_page_tab_type_spotlight,
        ops_selected_page_tab_type_camera,
        ops_selected_page_tab_type_unknown,        
        ops_selected_to_system_share,
        ops_selected_to_whatsapp,
        ops_selected_to_messenger,
        ops_selected_to_auto_copy_link,
        ops_selected_to_copy_link,
        ops_selected_to_sms,
        ops_selected_to_twitter,
        ops_selected_to_facebook,
        ops_selected_to_camera_roll,
        ops_selected_to_instagram,
        ops_selected_to_instagram_direct,
        ops_selected_to_instagram_story,
        ops_selected_to_tiktok,
        ops_selected_to_telegram,
        ops_selected_to_discord,
        ops_selected_to_line,
        ops_selected_to_viber,
        ops_selected_to_kakao,
        ops_selected_to_signal,
        ops_selected_to_imo,
        ops_selected_to_jiochat,
        ops_selected_with_add_friend_link,
        ops_selected_with_commerce_link,
        ops_selected_with_discover_link,
        ops_selected_with_lenses_link,
        ops_selected_with_map_link,
        ops_selected_with_our_story_link,
        ops_selected_with_profile_link,
        ops_selected_with_public_user_story_link,
        ops_selected_with_snap_pro_link,
        ops_selected_with_memories_link,
        ops_selected_with_saved_story_link
        ],

        name="off_platform_share",
        bq_dialect="standard"
    )
    return mt

def off_platform_share_session_time_quantile(start_date, end_date):
    """
    Sesstion time quantile metrics for off-platform shares with destination selection
    """

    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
    `sc-analytics.prod_analytics_ops_page.daily_events_*`
    WHERE _table_suffix between '{start}' and '{end}'
    AND event_name = 'OFF_PLATFORM_SHARE' 
    AND ghost_user_id IS NOT NULL 
    AND destination_selected is not null
    """.format(
        start=start_date,
        end=end_date,
    )

    off_platform_share_with_selection_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_session_time_sec_quantile',
        'off_platform_share_with_selection_session_time_sec_quantile',
        dist='quantile'
    )

    off_platform_share_with_selection_camera_roll_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_camera_roll_session_time_sec_quantile',
        'off_platform_share_with_selection_camera_roll_session_time_sec_quantile',
        dist='quantile'
    )

    off_platform_share_with_selection_whatsapp_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_whatsapp_session_time_sec_quantile',
        'off_platform_share_with_selection_whatsapp_session_time_sec_quantile',
        dist='quantile'
    )

    off_platform_share_with_selection_system_share_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_system_share_session_time_sec_quantile',
        'off_platform_share_with_selection_system_share_session_time_sec_quantile',
        dist='quantile'
    )

    off_platform_share_with_selection_auto_copy_link_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_auto_copy_link_session_time_sec_quantile',
        'off_platform_share_with_selection_auto_copy_link_session_time_sec_quantile',
        dist='quantile'
    )

    off_platform_share_with_selection_instagram_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_instagram_session_time_sec_quantile',
        'off_platform_share_with_selection_instagram_session_time_sec_quantile',
        dist='quantile'
    )

    off_platform_share_with_selection_sms_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_sms_session_time_sec_quantile',
        'off_platform_share_with_selection_sms_session_time_sec_quantile',
        dist='quantile'
    )

    off_platform_share_with_selection_copy_link_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_copy_link_session_time_sec_quantile',
        'off_platform_share_with_selection_copy_link_session_time_sec_quantile',
        dist='quantile'
    )

    off_platform_share_with_selection_messenger_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_messenger_session_time_sec_quantile',
        'off_platform_share_with_selection_messenger_session_time_sec_quantile',
        dist='quantile'
    )

    off_platform_share_with_selection_facebook_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_facebook_session_time_sec_quantile',
        'off_platform_share_with_selection_facebook_session_time_sec_quantile',
        dist='quantile'
    )

    off_platform_share_with_selection_instagram_story_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_instagram_story_session_time_sec_quantile',
        'off_platform_share_with_selection_instagram_story_session_time_sec_quantile',
        dist='quantile'
    )

    off_platform_share_with_selection_tiktok_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_tiktok_session_time_sec_quantile',
        'off_platform_share_with_selection_tiktok_session_time_sec_quantile',
        dist='quantile'
    )

    off_platform_share_with_selection_telegram_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_telegram_session_time_sec_quantile',
        'off_platform_share_with_selection_telegram_session_time_sec_quantile',
        dist='quantile'
    )

    off_platform_share_with_selection_twitter_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_twitter_session_time_sec_quantile',
        'off_platform_share_with_selection_twitter_session_time_sec_quantile',
        dist='quantile'
    )

    off_platform_share_with_selection_messenger_direct_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_messenger_direct_session_time_sec_quantile',
        'off_platform_share_with_selection_messenger_direct_session_time_sec_quantile',
        dist='quantile'
    )

    off_platform_share_with_selection_discord_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_discord_session_time_sec_quantile',
        'off_platform_share_with_selection_discord_session_time_sec_quantile',
        dist='quantile'
    )

    off_platform_share_with_selection_viber_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_viber_session_time_sec_quantile',
        'off_platform_share_with_selection_viber_session_time_sec_quantile',
        dist='quantile'
    )

    off_platform_share_with_selection_unknown_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_unknown_session_time_sec_quantile',
        'off_platform_share_with_selection_unknown_session_time_sec_quantile',
        dist='quantile'
    )

    off_platform_share_with_selection_signal_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_signal_session_time_sec_quantile',
        'off_platform_share_with_selection_signal_session_time_sec_quantile',
        dist='quantile'
    )

    off_platform_share_with_selection_facebook_feed_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_facebook_feed_session_time_sec_quantile',
        'off_platform_share_with_selection_facebook_feed_session_time_sec_quantile',
        dist='quantile'
    )

    off_platform_share_with_selection_line_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_line_session_time_sec_quantile',
        'off_platform_share_with_selection_line_session_time_sec_quantile',
        dist='quantile'
    )

    off_platform_share_with_selection_instagram_feed_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_instagram_feed_session_time_sec_quantile',
        'off_platform_share_with_selection_instagram_feed_session_time_sec_quantile',
        dist='quantile'
    )

    off_platform_share_with_selection_kakao_talk_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_kakao_talk_session_time_sec_quantile',
        'off_platform_share_with_selection_kakao_talk_session_time_sec_quantile',
        dist='quantile'
    )

    off_platform_share_with_selection_jio_chat_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_jio_chat_session_time_sec_quantile',
        'off_platform_share_with_selection_jio_chat_session_time_sec_quantile',
        dist='quantile'
    )

    off_platform_share_with_selection_imo_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_imo_session_time_sec_quantile',
        'off_platform_share_with_selection_imo_session_time_sec_quantile',
        dist='quantile'
    )

    off_platform_share_with_selection_twitter_tweet_session_time_sec_quantile = Metric(
        'off_platform_share_with_selection_twitter_tweet_session_time_sec_quantile',
        'off_platform_share_with_selection_twitter_tweet_session_time_sec_quantile',
        dist='quantile'
    )

    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        ghost_user_id,

        session_time_sec as off_platform_share_with_selection_session_time_sec_quantile,
        if(destination_selected = 'CAMERA_ROLL', session_time_sec, NULL) as off_platform_share_with_selection_camera_roll_session_time_sec_quantile,
        if(destination_selected = 'WHATSAPP', session_time_sec, NULL) as off_platform_share_with_selection_whatsapp_session_time_sec_quantile,
        if(destination_selected = 'SYSTEM_SHARE', session_time_sec, NULL) as off_platform_share_with_selection_system_share_session_time_sec_quantile,
        if(destination_selected = 'AUTO_COPY_LINK', session_time_sec, NULL) as off_platform_share_with_selection_auto_copy_link_session_time_sec_quantile,
        if(destination_selected = 'INSTAGRAM', session_time_sec, NULL) as off_platform_share_with_selection_instagram_session_time_sec_quantile,
        if(destination_selected = 'SMS', session_time_sec, NULL) as off_platform_share_with_selection_sms_session_time_sec_quantile,
        if(destination_selected = 'COPY_LINK', session_time_sec, NULL) as off_platform_share_with_selection_copy_link_session_time_sec_quantile,
        if(destination_selected = 'MESSENGER', session_time_sec, NULL) as off_platform_share_with_selection_messenger_session_time_sec_quantile,
        if(destination_selected = 'FACEBOOK', session_time_sec, NULL) as off_platform_share_with_selection_facebook_session_time_sec_quantile,
        if(destination_selected = 'INSTAGRAM_STORY', session_time_sec, NULL) as off_platform_share_with_selection_instagram_story_session_time_sec_quantile,
        if(destination_selected = 'TIKTOK', session_time_sec, NULL) as off_platform_share_with_selection_tiktok_session_time_sec_quantile,
        if(destination_selected = 'TELEGRAM', session_time_sec, NULL) as off_platform_share_with_selection_telegram_session_time_sec_quantile,
        if(destination_selected = 'TWITTER', session_time_sec, NULL) as off_platform_share_with_selection_twitter_session_time_sec_quantile,
        if(destination_selected = 'MESSENGER_DIRECT', session_time_sec, NULL) as off_platform_share_with_selection_messenger_direct_session_time_sec_quantile,
        if(destination_selected = 'DISCORD', session_time_sec, NULL) as off_platform_share_with_selection_discord_session_time_sec_quantile,
        if(destination_selected = 'VIBER', session_time_sec, NULL) as off_platform_share_with_selection_viber_session_time_sec_quantile,
        if(destination_selected = 'UNKNOWN', session_time_sec, NULL) as off_platform_share_with_selection_unknown_session_time_sec_quantile,
        if(destination_selected = 'SIGNAL', session_time_sec, NULL) as off_platform_share_with_selection_signal_session_time_sec_quantile,
        if(destination_selected = 'FACEBOOK_FEED', session_time_sec, NULL) as off_platform_share_with_selection_facebook_feed_session_time_sec_quantile,
        if(destination_selected = 'LINE', session_time_sec, NULL) as off_platform_share_with_selection_line_session_time_sec_quantile,
        if(destination_selected = 'INSTAGRAM_FEED', session_time_sec, NULL) as off_platform_share_with_selection_instagram_feed_session_time_sec_quantile,
        if(destination_selected = 'KAKAO_TALK', session_time_sec, NULL) as off_platform_share_with_selection_kakao_talk_session_time_sec_quantile,
        if(destination_selected = 'JIO_CHAT', session_time_sec, NULL) as off_platform_share_with_selection_jio_chat_session_time_sec_quantile,
        if(destination_selected = 'IMO', session_time_sec, NULL) as off_platform_share_with_selection_imo_session_time_sec_quantile,
        if(destination_selected = 'TWITTER_TWEET', session_time_sec, NULL) as off_platform_share_with_selection_twitter_tweet_session_time_sec_quantile

        FROM {source_table}
        """.format(source_table=source_table),

        metrics=[
        off_platform_share_with_selection_session_time_sec_quantile,
        off_platform_share_with_selection_camera_roll_session_time_sec_quantile,
        off_platform_share_with_selection_whatsapp_session_time_sec_quantile,
        off_platform_share_with_selection_system_share_session_time_sec_quantile,
        off_platform_share_with_selection_auto_copy_link_session_time_sec_quantile,
        off_platform_share_with_selection_instagram_session_time_sec_quantile,
        off_platform_share_with_selection_sms_session_time_sec_quantile,
        off_platform_share_with_selection_copy_link_session_time_sec_quantile,
        off_platform_share_with_selection_messenger_session_time_sec_quantile,
        off_platform_share_with_selection_facebook_session_time_sec_quantile,
        off_platform_share_with_selection_instagram_story_session_time_sec_quantile,
        off_platform_share_with_selection_tiktok_session_time_sec_quantile,
        off_platform_share_with_selection_telegram_session_time_sec_quantile,
        off_platform_share_with_selection_twitter_session_time_sec_quantile,
        off_platform_share_with_selection_messenger_direct_session_time_sec_quantile,
        off_platform_share_with_selection_discord_session_time_sec_quantile,
        off_platform_share_with_selection_viber_session_time_sec_quantile,
        off_platform_share_with_selection_unknown_session_time_sec_quantile,
        off_platform_share_with_selection_signal_session_time_sec_quantile,
        off_platform_share_with_selection_facebook_feed_session_time_sec_quantile,
        off_platform_share_with_selection_line_session_time_sec_quantile,
        off_platform_share_with_selection_instagram_feed_session_time_sec_quantile,
        off_platform_share_with_selection_kakao_talk_session_time_sec_quantile,
        off_platform_share_with_selection_jio_chat_session_time_sec_quantile,
        off_platform_share_with_selection_imo_session_time_sec_quantile,
        off_platform_share_with_selection_twitter_tweet_session_time_sec_quantile
        ],

        name="off_platform_share_session_time_quantile",
        bq_dialect="standard", 
        quantile_metrics=True
    )
    return mt

def off_platform_share_operation_quantile(start_date, end_date):
    """
    Off-platform share operation metrics for OPS performance and quality
    """

    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
    `sc-analytics.report_growth.off_platform_share_operation_2*`
    WHERE concat('2', _table_suffix) between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    off_platform_share_operation_export_complete_latency = Metric('off_platform_share_operation_export_complete_latency', 'Off-Platform Share Export Complete Latency', dist='quantile')
    off_platform_share_operation_link_generation_complete_latency = Metric('off_platform_share_operation_link_generation_complete_latency', 'Off-Platform Share Link Generation Latency', dist='quantile')
    off_platform_share_operation_export_complete_latency_add_friend = Metric('off_platform_share_operation_export_complete_latency_add_friend', 'Off-Platform Share Export Complete Latency (ADD_FRIEND)', dist='quantile')
    off_platform_share_operation_link_generation_complete_latency_add_friend = Metric('off_platform_share_operation_link_generation_complete_latency_add_friend', 'Off-Platform Share Link Generation Latency (ADD_FRIEND)', dist='quantile')
    off_platform_share_operation_export_complete_latency_public_user_story = Metric('off_platform_share_operation_export_complete_latency_public_user_story', 'Off-Platform Share Export Complete Latency (PUBLIC_USER_STORY)', dist='quantile')
    off_platform_share_operation_link_generation_complete_latency_public_user_story = Metric('off_platform_share_operation_link_generation_complete_latency_public_user_story', 'Off-Platform Share Link Generation Latency (PUBLIC_USER_STORY)', dist='quantile')
    off_platform_share_operation_export_complete_latency_our_story = Metric('off_platform_share_operation_export_complete_latency_our_story', 'Off-Platform Share Export Complete Latency (OUR_STORY)', dist='quantile')
    off_platform_share_operation_link_generation_complete_latency_our_story = Metric('off_platform_share_operation_link_generation_complete_latency_our_story', 'Off-Platform Share Link Generation Latency (OUR_STORY)', dist='quantile')
    off_platform_share_operation_export_complete_latency_lenses = Metric('off_platform_share_operation_export_complete_latency_lenses', 'Off-Platform Share Export Complete Latency (LENSES)', dist='quantile')
    off_platform_share_operation_link_generation_complete_latency_lenses = Metric('off_platform_share_operation_link_generation_complete_latency_lenses', 'Off-Platform Share Link Generation Latency (LENSES)', dist='quantile')
    off_platform_share_operation_export_complete_latency_map = Metric('off_platform_share_operation_export_complete_latency_map', 'Off-Platform Share Export Complete Latency (MAP)', dist='quantile')
    off_platform_share_operation_link_generation_complete_latency_map = Metric('off_platform_share_operation_link_generation_complete_latency_map', 'Off-Platform Share Link Generation Latency (MAP)', dist='quantile')
    off_platform_share_operation_export_complete_latency_discover_edition = Metric('off_platform_share_operation_export_complete_latency_discover_edition', 'Off-Platform Share Export Complete Latency (DISCOVER_EDITION)', dist='quantile')
    off_platform_share_operation_link_generation_complete_latency_discover_edition = Metric('off_platform_share_operation_link_generation_complete_latency_discover_edition', 'Off-Platform Share Link Generation Latency (DISCOVER_EDITION)', dist='quantile')
    off_platform_share_operation_export_complete_latency_commerce = Metric('off_platform_share_operation_export_complete_latency_commerce', 'Off-Platform Share Export Complete Latency (COMMERCE)', dist='quantile')
    off_platform_share_operation_link_generation_complete_latency_commerce = Metric('off_platform_share_operation_link_generation_complete_latency_commerce', 'Off-Platform Share Link Generation Latency (COMMERCE)', dist='quantile')
    off_platform_share_operation_export_complete_latency_group_invite = Metric('off_platform_share_operation_export_complete_latency_group_invite', 'Off-Platform Share Export Complete Latency (GROUP_INVITE)', dist='quantile')
    off_platform_share_operation_link_generation_complete_latency_group_invite = Metric('off_platform_share_operation_link_generation_complete_latency_group_invite', 'Off-Platform Share Link Generation Latency (GROUP_INVITE)', dist='quantile')
    off_platform_share_operation_export_complete_latency_snap_pro = Metric('off_platform_share_operation_export_complete_latency_snap_pro', 'Off-Platform Share Export Complete Latency (SNAP_PRO)', dist='quantile')
    off_platform_share_operation_link_generation_complete_latency_snap_pro = Metric('off_platform_share_operation_link_generation_complete_latency_snap_pro', 'Off-Platform Share Link Generation Latency (SNAP_PRO)', dist='quantile')
    off_platform_share_operation_export_complete_latency_discover = Metric('off_platform_share_operation_export_complete_latency_discover', 'Off-Platform Share Export Complete Latency (DISCOVER)', dist='quantile')
    off_platform_share_operation_link_generation_complete_latency_discover = Metric('off_platform_share_operation_link_generation_complete_latency_discover', 'Off-Platform Share Link Generation Latency (DISCOVER)', dist='quantile')
    off_platform_share_operation_export_complete_latency_saved_story = Metric('off_platform_share_operation_export_complete_latency_saved_story', 'Off-Platform Share Export Complete Latency (SAVED_STORY)', dist='quantile')
    off_platform_share_operation_link_generation_complete_latency_saved_story = Metric('off_platform_share_operation_link_generation_complete_latency_saved_story', 'Off-Platform Share Link Generation Latency (SAVED_STORY)', dist='quantile')
    off_platform_share_operation_export_complete_latency_communities = Metric('off_platform_share_operation_export_complete_latency_communities', 'Off-Platform Share Export Complete Latency (COMMUNITIES)', dist='quantile')
    off_platform_share_operation_link_generation_complete_latency_communities = Metric('off_platform_share_operation_link_generation_complete_latency_communities', 'Off-Platform Share Link Generation Latency (COMMUNITIES)', dist='quantile')
    off_platform_share_operation_export_complete_latency_memories_link = Metric('off_platform_share_operation_export_complete_latency_memories_link', 'Off-Platform Share Export Complete Latency (MEMORIES_LINK)', dist='quantile')
    off_platform_share_operation_link_generation_complete_latency_memories_link = Metric('off_platform_share_operation_link_generation_complete_latency_memories_link', 'Off-Platform Share Link Generation Latency (MEMORIES_LINK)', dist='quantile')

    mt = MetricTable(
    sql="""
        SELECT
        COALESCE(
            stage_timestamp_sharesheet_request,
            stage_timestamp_sharesheet_show,
            stage_timestamp_destination_selected,
            stage_timestamp_link_generation_start,
            stage_timestamp_link_generation_complete,
            stage_timestamp_download_media_start,
            stage_timestamp_download_media_complete,
            stage_timestamp_media_export_start,
            stage_timestamp_media_export_complete,
            stage_timestamp_export_complete
        ) AS ts,
        ghost_user_id,

        TIMESTAMP_DIFF(stage_timestamp_export_complete, stage_timestamp_destination_selected, MILLISECOND) AS off_platform_share_operation_export_complete_latency,
        TIMESTAMP_DIFF(stage_timestamp_link_generation_complete, stage_timestamp_link_generation_start, MILLISECOND) AS off_platform_share_operation_link_generation_complete_latency,

        IF(deep_link_source = 'ADD_FRIEND', TIMESTAMP_DIFF(stage_timestamp_export_complete, stage_timestamp_destination_selected, MILLISECOND), NULL) AS off_platform_share_operation_export_complete_latency_add_friend,
        IF(deep_link_source = 'ADD_FRIEND', TIMESTAMP_DIFF(stage_timestamp_link_generation_complete, stage_timestamp_link_generation_start, MILLISECOND), NULL) AS off_platform_share_operation_link_generation_complete_latency_add_friend,

        IF(deep_link_source = 'PUBLIC_USER_STORY', TIMESTAMP_DIFF(stage_timestamp_export_complete, stage_timestamp_destination_selected, MILLISECOND), NULL) AS off_platform_share_operation_export_complete_latency_public_user_story,
        IF(deep_link_source = 'PUBLIC_USER_STORY', TIMESTAMP_DIFF(stage_timestamp_link_generation_complete, stage_timestamp_link_generation_start, MILLISECOND), NULL) AS off_platform_share_operation_link_generation_complete_latency_public_user_story,

        IF(deep_link_source = 'OUR_STORY', TIMESTAMP_DIFF(stage_timestamp_export_complete, stage_timestamp_destination_selected, MILLISECOND), NULL) AS off_platform_share_operation_export_complete_latency_our_story,
        IF(deep_link_source = 'OUR_STORY', TIMESTAMP_DIFF(stage_timestamp_link_generation_complete, stage_timestamp_link_generation_start, MILLISECOND), NULL) AS off_platform_share_operation_link_generation_complete_latency_our_story,

        IF(deep_link_source = 'LENSES', TIMESTAMP_DIFF(stage_timestamp_export_complete, stage_timestamp_destination_selected, MILLISECOND), NULL) AS off_platform_share_operation_export_complete_latency_lenses,
        IF(deep_link_source = 'LENSES', TIMESTAMP_DIFF(stage_timestamp_link_generation_complete, stage_timestamp_link_generation_start, MILLISECOND), NULL) AS off_platform_share_operation_link_generation_complete_latency_lenses,

        IF(deep_link_source = 'MAP', TIMESTAMP_DIFF(stage_timestamp_export_complete, stage_timestamp_destination_selected, MILLISECOND), NULL) AS off_platform_share_operation_export_complete_latency_map,
        IF(deep_link_source = 'MAP', TIMESTAMP_DIFF(stage_timestamp_link_generation_complete, stage_timestamp_link_generation_start, MILLISECOND), NULL) AS off_platform_share_operation_link_generation_complete_latency_map,

        IF(deep_link_source = 'DISCOVER_EDITION', TIMESTAMP_DIFF(stage_timestamp_export_complete, stage_timestamp_destination_selected, MILLISECOND), NULL) AS off_platform_share_operation_export_complete_latency_discover_edition,
        IF(deep_link_source = 'DISCOVER_EDITION', TIMESTAMP_DIFF(stage_timestamp_link_generation_complete, stage_timestamp_link_generation_start, MILLISECOND), NULL) AS off_platform_share_operation_link_generation_complete_latency_discover_edition,

        IF(deep_link_source = 'COMMERCE', TIMESTAMP_DIFF(stage_timestamp_export_complete, stage_timestamp_destination_selected, MILLISECOND), NULL) AS off_platform_share_operation_export_complete_latency_commerce,
        IF(deep_link_source = 'COMMERCE', TIMESTAMP_DIFF(stage_timestamp_link_generation_complete, stage_timestamp_link_generation_start, MILLISECOND), NULL) AS off_platform_share_operation_link_generation_complete_latency_commerce,

        IF(deep_link_source = 'GROUP_INVITE', TIMESTAMP_DIFF(stage_timestamp_export_complete, stage_timestamp_destination_selected, MILLISECOND), NULL) AS off_platform_share_operation_export_complete_latency_group_invite,
        IF(deep_link_source = 'GROUP_INVITE', TIMESTAMP_DIFF(stage_timestamp_link_generation_complete, stage_timestamp_link_generation_start, MILLISECOND), NULL) AS off_platform_share_operation_link_generation_complete_latency_group_invite,

        IF(deep_link_source = 'SNAP_PRO', TIMESTAMP_DIFF(stage_timestamp_export_complete, stage_timestamp_destination_selected, MILLISECOND), NULL) AS off_platform_share_operation_export_complete_latency_snap_pro,
        IF(deep_link_source = 'SNAP_PRO', TIMESTAMP_DIFF(stage_timestamp_link_generation_complete, stage_timestamp_link_generation_start, MILLISECOND), NULL) AS off_platform_share_operation_link_generation_complete_latency_snap_pro,

        IF(deep_link_source = 'DISCOVER', TIMESTAMP_DIFF(stage_timestamp_export_complete, stage_timestamp_destination_selected, MILLISECOND), NULL) AS off_platform_share_operation_export_complete_latency_discover,
        IF(deep_link_source = 'DISCOVER', TIMESTAMP_DIFF(stage_timestamp_link_generation_complete, stage_timestamp_link_generation_start, MILLISECOND), NULL) AS off_platform_share_operation_link_generation_complete_latency_discover,

        IF(deep_link_source = 'SAVED_STORY', TIMESTAMP_DIFF(stage_timestamp_export_complete, stage_timestamp_destination_selected, MILLISECOND), NULL) AS off_platform_share_operation_export_complete_latency_saved_story,
        IF(deep_link_source = 'SAVED_STORY', TIMESTAMP_DIFF(stage_timestamp_link_generation_complete, stage_timestamp_link_generation_start, MILLISECOND), NULL) AS off_platform_share_operation_link_generation_complete_latency_saved_story,

        IF(deep_link_source = 'COMMUNITIES', TIMESTAMP_DIFF(stage_timestamp_export_complete, stage_timestamp_destination_selected, MILLISECOND), NULL) AS off_platform_share_operation_export_complete_latency_communities,
        IF(deep_link_source = 'COMMUNITIES', TIMESTAMP_DIFF(stage_timestamp_link_generation_complete, stage_timestamp_link_generation_start, MILLISECOND), NULL) AS off_platform_share_operation_link_generation_complete_latency_communities,

        IF(deep_link_source = 'MEMORIES_LINK', TIMESTAMP_DIFF(stage_timestamp_export_complete, stage_timestamp_destination_selected, MILLISECOND), NULL) AS off_platform_share_operation_export_complete_latency_memories_link,
        IF(deep_link_source = 'MEMORIES_LINK', TIMESTAMP_DIFF(stage_timestamp_link_generation_complete, stage_timestamp_link_generation_start, MILLISECOND), NULL) AS off_platform_share_operation_link_generation_complete_latency_memories_link

    FROM {source_table}
    AND ghost_user_id IS NOT NULL
    AND share_result != 'CANCELLED'
    """.format(source_table=source_table),

    metrics = [
        # Export Complete Latency
        off_platform_share_operation_export_complete_latency,
        off_platform_share_operation_export_complete_latency_add_friend,
        off_platform_share_operation_export_complete_latency_public_user_story,
        off_platform_share_operation_export_complete_latency_our_story,
        off_platform_share_operation_export_complete_latency_lenses,
        off_platform_share_operation_export_complete_latency_map,
        off_platform_share_operation_export_complete_latency_discover_edition,
        off_platform_share_operation_export_complete_latency_commerce,
        off_platform_share_operation_export_complete_latency_group_invite,
        off_platform_share_operation_export_complete_latency_snap_pro,
        off_platform_share_operation_export_complete_latency_discover,
        off_platform_share_operation_export_complete_latency_saved_story,
        off_platform_share_operation_export_complete_latency_communities,
        off_platform_share_operation_export_complete_latency_memories_link,

        # Link Generation Complete Latency
        off_platform_share_operation_link_generation_complete_latency,
        off_platform_share_operation_link_generation_complete_latency_add_friend,
        off_platform_share_operation_link_generation_complete_latency_public_user_story,
        off_platform_share_operation_link_generation_complete_latency_our_story,
        off_platform_share_operation_link_generation_complete_latency_lenses,
        off_platform_share_operation_link_generation_complete_latency_map,
        off_platform_share_operation_link_generation_complete_latency_discover_edition,
        off_platform_share_operation_link_generation_complete_latency_commerce,
        off_platform_share_operation_link_generation_complete_latency_group_invite,
        off_platform_share_operation_link_generation_complete_latency_snap_pro,
        off_platform_share_operation_link_generation_complete_latency_discover,
        off_platform_share_operation_link_generation_complete_latency_saved_story,
        off_platform_share_operation_link_generation_complete_latency_communities,
        off_platform_share_operation_link_generation_complete_latency_memories_link
    ],

    name="off_platform_share_operation_quantile",
    bq_dialect="standard",
    quantile_metrics=True
    )

    return mt


def context_menu_action(start_date, end_date):
    """
    Context menu action metircs
    """

    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
    `sc-analytics.report_growth.context_funnel_by_action_name_2*`
    WHERE concat('2', _table_suffix) between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    context_menu_action = Metric(
        'context_menu_action',
        'Context Menu Actions',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    context_menu_action_uu = Metric(
        'context_menu_action_uu',
        'Context Menu Action Users',
        dist='bin',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    context_menu_action_save = Metric(
        'context_menu_action_save',
        'Context Menu Actions (Save)',
        dist='cont',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    context_menu_action_save_uu = Metric(
        'context_menu_action_save_uu',
        'Context Menu Action Users (Save)',
        dist='bin',
        daily=True,
        cumulative=True,
        desired_direction=POSITIVE
    )

    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', concat('2', _table_suffix)) as ts,
        ghost_user_id,

        sum(CONTEXT_MENU_ACTION) as context_menu_action,
        max(if(CONTEXT_MENU_ACTION > 0, 1, 0)) as context_menu_action_uu,
        sum(if(action_type = 'save', CONTEXT_MENU_ACTION, 0)) as context_menu_action_save,
        max(if(CONTEXT_MENU_ACTION > 0 and action_type = 'save', 1, 0)) as context_menu_action_save_uu,

        FROM {source_table}
        GROUP BY
        ts,
        ghost_user_id
        """.format(source_table=source_table),

        metrics=[
        context_menu_action,
        context_menu_action_uu,
        context_menu_action_save,
        context_menu_action_save_uu,        
        ],

        name="context_menu_action",
        bq_dialect="standard"
    )
    return mt

def social_sms_action(start_date, end_date):
    """
    Social SMS action with feature breakdowns
    """

    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
    `sc-analytics.report_growth.social_sms_action_daily_events_*`
    WHERE _table_suffix between '{start}' and '{end}'
    and delivery_status = 'ATTEMPTED'
    and feature not like '%NOTIFICATION%'
    """.format(
        start=start_date,
        end=end_date,
    )

    social_sms_action = Metric('social_sms_action', 'social_sms_action', dist='cont', daily=True, cumulative=True,)
    social_sms_action_uu = Metric('social_sms_action_uu', 'social_sms_action_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_action_active_day = Metric('social_sms_action_active_day', 'social_sms_action_active_day', dist='cont', daily=True, cumulative=True,)
    social_sms_action_friend_invite_add_friends = Metric('social_sms_action_friend_invite_add_friends', 'social_sms_action_friend_invite_add_friends', dist='cont', daily=True, cumulative=True,)
    social_sms_action_friend_invite_add_friends_uu = Metric('social_sms_action_friend_invite_add_friends_uu', 'social_sms_action_friend_invite_add_friends_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_action_friend_invite_add_friends_active_day = Metric('social_sms_action_friend_invite_add_friends_active_day', 'social_sms_action_friend_invite_add_friends_active_day', dist='cont', daily=True, cumulative=True,)
    social_sms_action_friend_invite_all_contacts = Metric('social_sms_action_friend_invite_all_contacts', 'social_sms_action_friend_invite_all_contacts', dist='cont', daily=True, cumulative=True,)
    social_sms_action_friend_invite_all_contacts_uu = Metric('social_sms_action_friend_invite_all_contacts_uu', 'social_sms_action_friend_invite_all_contacts_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_action_friend_invite_all_contacts_active_day = Metric('social_sms_action_friend_invite_all_contacts_active_day', 'social_sms_action_friend_invite_all_contacts_active_day', dist='cont', daily=True, cumulative=True,)
    social_sms_action_friend_invite_friend_feed = Metric('social_sms_action_friend_invite_friend_feed', 'social_sms_action_friend_invite_friend_feed', dist='cont', daily=True, cumulative=True,)
    social_sms_action_friend_invite_friend_feed_uu = Metric('social_sms_action_friend_invite_friend_feed_uu', 'social_sms_action_friend_invite_friend_feed_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_action_friend_invite_friend_feed_active_day = Metric('social_sms_action_friend_invite_friend_feed_active_day', 'social_sms_action_friend_invite_friend_feed_active_day', dist='cont', daily=True, cumulative=True,)
    social_sms_action_friend_invite_invite_friends_page = Metric('social_sms_action_friend_invite_invite_friends_page', 'social_sms_action_friend_invite_invite_friends_page', dist='cont', daily=True, cumulative=True,)
    social_sms_action_friend_invite_invite_friends_page_uu = Metric('social_sms_action_friend_invite_invite_friends_page_uu', 'social_sms_action_friend_invite_invite_friends_page_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_action_friend_invite_invite_friends_page_active_day = Metric('social_sms_action_friend_invite_invite_friends_page_active_day', 'social_sms_action_friend_invite_invite_friends_page_active_day', dist='cont', daily=True, cumulative=True,)
    social_sms_action_friend_invite_registration = Metric('social_sms_action_friend_invite_registration', 'social_sms_action_friend_invite_registration', dist='cont', daily=True, cumulative=True,)
    social_sms_action_friend_invite_registration_uu = Metric('social_sms_action_friend_invite_registration_uu', 'social_sms_action_friend_invite_registration_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_action_friend_invite_registration_active_day = Metric('social_sms_action_friend_invite_registration_active_day', 'social_sms_action_friend_invite_registration_active_day', dist='cont', daily=True, cumulative=True,)
    social_sms_action_friend_invite_universal_search = Metric('social_sms_action_friend_invite_universal_search', 'social_sms_action_friend_invite_universal_search', dist='cont', daily=True, cumulative=True,)
    social_sms_action_friend_invite_universal_search_uu = Metric('social_sms_action_friend_invite_universal_search_uu', 'social_sms_action_friend_invite_universal_search_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_action_friend_invite_universal_search_active_day = Metric('social_sms_action_friend_invite_universal_search_active_day', 'social_sms_action_friend_invite_universal_search_active_day', dist='cont', daily=True, cumulative=True,)
    social_sms_action_memories_link_share = Metric('social_sms_action_memories_link_share', 'social_sms_action_memories_link_share', dist='cont', daily=True, cumulative=True,)
    social_sms_action_memories_link_share_uu = Metric('social_sms_action_memories_link_share_uu', 'social_sms_action_memories_link_share_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_action_memories_link_share_active_day = Metric('social_sms_action_memories_link_share_active_day', 'social_sms_action_memories_link_share_active_day', dist='cont', daily=True, cumulative=True,)
    social_sms_action_public_link_discover_episode = Metric('social_sms_action_public_link_discover_episode', 'social_sms_action_public_link_discover_episode', dist='cont', daily=True, cumulative=True,)
    social_sms_action_public_link_discover_episode_uu = Metric('social_sms_action_public_link_discover_episode_uu', 'social_sms_action_public_link_discover_episode_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_action_public_link_discover_episode_active_day = Metric('social_sms_action_public_link_discover_episode_active_day', 'social_sms_action_public_link_discover_episode_active_day', dist='cont', daily=True, cumulative=True,)
    social_sms_action_public_link_discover_profile = Metric('social_sms_action_public_link_discover_profile', 'social_sms_action_public_link_discover_profile', dist='cont', daily=True, cumulative=True,)
    social_sms_action_public_link_discover_profile_uu = Metric('social_sms_action_public_link_discover_profile_uu', 'social_sms_action_public_link_discover_profile_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_action_public_link_discover_profile_active_day = Metric('social_sms_action_public_link_discover_profile_active_day', 'social_sms_action_public_link_discover_profile_active_day', dist='cont', daily=True, cumulative=True,)
    social_sms_action_public_link_lens = Metric('social_sms_action_public_link_lens', 'social_sms_action_public_link_lens', dist='cont', daily=True, cumulative=True,)
    social_sms_action_public_link_lens_uu = Metric('social_sms_action_public_link_lens_uu', 'social_sms_action_public_link_lens_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_action_public_link_lens_active_day = Metric('social_sms_action_public_link_lens_active_day', 'social_sms_action_public_link_lens_active_day', dist='cont', daily=True, cumulative=True,)
    social_sms_action_public_link_map_story = Metric('social_sms_action_public_link_map_story', 'social_sms_action_public_link_map_story', dist='cont', daily=True, cumulative=True,)
    social_sms_action_public_link_map_story_uu = Metric('social_sms_action_public_link_map_story_uu', 'social_sms_action_public_link_map_story_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_action_public_link_map_story_active_day = Metric('social_sms_action_public_link_map_story_active_day', 'social_sms_action_public_link_map_story_active_day', dist='cont', daily=True, cumulative=True,)
    social_sms_action_public_link_public_profile = Metric('social_sms_action_public_link_public_profile', 'social_sms_action_public_link_public_profile', dist='cont', daily=True, cumulative=True,)
    social_sms_action_public_link_public_profile_uu = Metric('social_sms_action_public_link_public_profile_uu', 'social_sms_action_public_link_public_profile_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_action_public_link_public_profile_active_day = Metric('social_sms_action_public_link_public_profile_active_day', 'social_sms_action_public_link_public_profile_active_day', dist='cont', daily=True, cumulative=True,)
    social_sms_action_public_link_public_profile_snap = Metric('social_sms_action_public_link_public_profile_snap', 'social_sms_action_public_link_public_profile_snap', dist='cont', daily=True, cumulative=True,)
    social_sms_action_public_link_public_profile_snap_uu = Metric('social_sms_action_public_link_public_profile_snap_uu', 'social_sms_action_public_link_public_profile_snap_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_action_public_link_public_profile_snap_active_day = Metric('social_sms_action_public_link_public_profile_snap_active_day', 'social_sms_action_public_link_public_profile_snap_active_day', dist='cont', daily=True, cumulative=True,)
    social_sms_action_public_link_spotlight_snap = Metric('social_sms_action_public_link_spotlight_snap', 'social_sms_action_public_link_spotlight_snap', dist='cont', daily=True, cumulative=True,)
    social_sms_action_public_link_spotlight_snap_uu = Metric('social_sms_action_public_link_spotlight_snap_uu', 'social_sms_action_public_link_spotlight_snap_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_action_public_link_spotlight_snap_active_day = Metric('social_sms_action_public_link_spotlight_snap_active_day', 'social_sms_action_public_link_spotlight_snap_active_day', dist='cont', daily=True, cumulative=True,)
    social_sms_action_public_link_user_profile = Metric('social_sms_action_public_link_user_profile', 'social_sms_action_public_link_user_profile', dist='cont', daily=True, cumulative=True,)
    social_sms_action_public_link_user_profile_uu = Metric('social_sms_action_public_link_user_profile_uu', 'social_sms_action_public_link_user_profile_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_action_public_link_user_profile_active_day = Metric('social_sms_action_public_link_user_profile_active_day', 'social_sms_action_public_link_user_profile_active_day', dist='cont', daily=True, cumulative=True,)
    social_sms_action_registration_contacts = Metric('social_sms_action_registration_contacts', 'social_sms_action_registration_contacts', dist='cont', daily=True, cumulative=True,)
    social_sms_action_registration_contacts_uu = Metric('social_sms_action_registration_contacts_uu', 'social_sms_action_registration_contacts_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_action_registration_contacts_active_day = Metric('social_sms_action_registration_contacts_active_day', 'social_sms_action_registration_contacts_active_day', dist='cont', daily=True, cumulative=True,)
    social_sms_action_snap_anyone_invite = Metric('social_sms_action_snap_anyone_invite', 'social_sms_action_snap_anyone_invite', dist='cont', daily=True, cumulative=True,)
    social_sms_action_snap_anyone_invite_uu = Metric('social_sms_action_snap_anyone_invite_uu', 'social_sms_action_snap_anyone_invite_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_action_snap_anyone_invite_active_day = Metric('social_sms_action_snap_anyone_invite_active_day', 'social_sms_action_snap_anyone_invite_active_day', dist='cont', daily=True, cumulative=True,)
    social_sms_action_friend_invite_send_to = Metric('social_sms_action_friend_invite_send_to', 'social_sms_action_friend_invite_send_to', dist='cont', daily=True, cumulative=True,)
    social_sms_action_friend_invite_send_to_uu = Metric('social_sms_action_friend_invite_send_to_uu', 'social_sms_action_friend_invite_send_to_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_action_friend_invite_send_to_active_day = Metric('social_sms_action_friend_invite_send_to_active_day', 'social_sms_action_friend_invite_send_to_active_day', dist='cont', daily=True, cumulative=True,)

    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        ghost_user_id,

        SUM(1) AS social_sms_action,
        MAX(1) AS social_sms_action_uu,
        MAX(1) AS social_sms_action_active_day,
        SUM(IF(feature = 'FRIEND_INVITE_ADD_FRIENDS', 1, 0)) AS social_sms_action_friend_invite_add_friends,
        MAX(IF(feature = 'FRIEND_INVITE_ADD_FRIENDS', 1, 0)) AS social_sms_action_friend_invite_add_friends_uu,
        MAX(IF(feature = 'FRIEND_INVITE_ADD_FRIENDS', 1, 0)) AS social_sms_action_friend_invite_add_friends_active_day,
        SUM(IF(feature = 'FRIEND_INVITE_ALL_CONTACTS', 1, 0)) AS social_sms_action_friend_invite_all_contacts,
        MAX(IF(feature = 'FRIEND_INVITE_ALL_CONTACTS', 1, 0)) AS social_sms_action_friend_invite_all_contacts_uu,
        MAX(IF(feature = 'FRIEND_INVITE_ALL_CONTACTS', 1, 0)) AS social_sms_action_friend_invite_all_contacts_active_day,
        SUM(IF(feature = 'FRIEND_INVITE_FRIEND_FEED', 1, 0)) AS social_sms_action_friend_invite_friend_feed,
        MAX(IF(feature = 'FRIEND_INVITE_FRIEND_FEED', 1, 0)) AS social_sms_action_friend_invite_friend_feed_uu,
        MAX(IF(feature = 'FRIEND_INVITE_FRIEND_FEED', 1, 0)) AS social_sms_action_friend_invite_friend_feed_active_day,
        SUM(IF(feature = 'FRIEND_INVITE_INVITE_FRIENDS_PAGE', 1, 0)) AS social_sms_action_friend_invite_invite_friends_page,
        MAX(IF(feature = 'FRIEND_INVITE_INVITE_FRIENDS_PAGE', 1, 0)) AS social_sms_action_friend_invite_invite_friends_page_uu,
        MAX(IF(feature = 'FRIEND_INVITE_INVITE_FRIENDS_PAGE', 1, 0)) AS social_sms_action_friend_invite_invite_friends_page_active_day,
        SUM(IF(feature = 'FRIEND_INVITE_REGISTRATION', 1, 0)) AS social_sms_action_friend_invite_registration,
        MAX(IF(feature = 'FRIEND_INVITE_REGISTRATION', 1, 0)) AS social_sms_action_friend_invite_registration_uu,
        MAX(IF(feature = 'FRIEND_INVITE_REGISTRATION', 1, 0)) AS social_sms_action_friend_invite_registration_active_day,
        SUM(IF(feature = 'FRIEND_INVITE_UNIVERSAL_SEARCH', 1, 0)) AS social_sms_action_friend_invite_universal_search,
        MAX(IF(feature = 'FRIEND_INVITE_UNIVERSAL_SEARCH', 1, 0)) AS social_sms_action_friend_invite_universal_search_uu,
        MAX(IF(feature = 'FRIEND_INVITE_UNIVERSAL_SEARCH', 1, 0)) AS social_sms_action_friend_invite_universal_search_active_day,
        SUM(IF(feature = 'MEMORIES_LINK_SHARE', 1, 0)) AS social_sms_action_memories_link_share,
        MAX(IF(feature = 'MEMORIES_LINK_SHARE', 1, 0)) AS social_sms_action_memories_link_share_uu,
        MAX(IF(feature = 'MEMORIES_LINK_SHARE', 1, 0)) AS social_sms_action_memories_link_share_active_day,
        SUM(IF(feature = 'PUBLIC_LINK_DISCOVER_EPISODE', 1, 0)) AS social_sms_action_public_link_discover_episode,
        MAX(IF(feature = 'PUBLIC_LINK_DISCOVER_EPISODE', 1, 0)) AS social_sms_action_public_link_discover_episode_uu,
        MAX(IF(feature = 'PUBLIC_LINK_DISCOVER_EPISODE', 1, 0)) AS social_sms_action_public_link_discover_episode_active_day,
        SUM(IF(feature = 'PUBLIC_LINK_DISCOVER_PROFILE', 1, 0)) AS social_sms_action_public_link_discover_profile,
        MAX(IF(feature = 'PUBLIC_LINK_DISCOVER_PROFILE', 1, 0)) AS social_sms_action_public_link_discover_profile_uu,
        MAX(IF(feature = 'PUBLIC_LINK_DISCOVER_PROFILE', 1, 0)) AS social_sms_action_public_link_discover_profile_active_day,
        SUM(IF(feature = 'PUBLIC_LINK_LENS', 1, 0)) AS social_sms_action_public_link_lens,
        MAX(IF(feature = 'PUBLIC_LINK_LENS', 1, 0)) AS social_sms_action_public_link_lens_uu,
        MAX(IF(feature = 'PUBLIC_LINK_LENS', 1, 0)) AS social_sms_action_public_link_lens_active_day,
        SUM(IF(feature = 'PUBLIC_LINK_MAP_STORY', 1, 0)) AS social_sms_action_public_link_map_story,
        MAX(IF(feature = 'PUBLIC_LINK_MAP_STORY', 1, 0)) AS social_sms_action_public_link_map_story_uu,
        MAX(IF(feature = 'PUBLIC_LINK_MAP_STORY', 1, 0)) AS social_sms_action_public_link_map_story_active_day,
        SUM(IF(feature = 'PUBLIC_LINK_PUBLIC_PROFILE', 1, 0)) AS social_sms_action_public_link_public_profile,
        MAX(IF(feature = 'PUBLIC_LINK_PUBLIC_PROFILE', 1, 0)) AS social_sms_action_public_link_public_profile_uu,
        MAX(IF(feature = 'PUBLIC_LINK_PUBLIC_PROFILE', 1, 0)) AS social_sms_action_public_link_public_profile_active_day,
        SUM(IF(feature = 'PUBLIC_LINK_PUBLIC_PROFILE_SNAP', 1, 0)) AS social_sms_action_public_link_public_profile_snap,
        MAX(IF(feature = 'PUBLIC_LINK_PUBLIC_PROFILE_SNAP', 1, 0)) AS social_sms_action_public_link_public_profile_snap_uu,
        MAX(IF(feature = 'PUBLIC_LINK_PUBLIC_PROFILE_SNAP', 1, 0)) AS social_sms_action_public_link_public_profile_snap_active_day,
        SUM(IF(feature = 'PUBLIC_LINK_SPOTLIGHT_SNAP', 1, 0)) AS social_sms_action_public_link_spotlight_snap,
        MAX(IF(feature = 'PUBLIC_LINK_SPOTLIGHT_SNAP', 1, 0)) AS social_sms_action_public_link_spotlight_snap_uu,
        MAX(IF(feature = 'PUBLIC_LINK_SPOTLIGHT_SNAP', 1, 0)) AS social_sms_action_public_link_spotlight_snap_active_day,
        SUM(IF(feature = 'PUBLIC_LINK_USER_PROFILE', 1, 0)) AS social_sms_action_public_link_user_profile,
        MAX(IF(feature = 'PUBLIC_LINK_USER_PROFILE', 1, 0)) AS social_sms_action_public_link_user_profile_uu,
        MAX(IF(feature = 'PUBLIC_LINK_USER_PROFILE', 1, 0)) AS social_sms_action_public_link_user_profile_active_day,
        SUM(IF(feature = 'REGISTRATION_CONTACTS', 1, 0)) AS social_sms_action_registration_contacts,
        MAX(IF(feature = 'REGISTRATION_CONTACTS', 1, 0)) AS social_sms_action_registration_contacts_uu,
        MAX(IF(feature = 'REGISTRATION_CONTACTS', 1, 0)) AS social_sms_action_registration_contacts_active_day,
        SUM(IF(feature = 'SNAP_ANYONE_INVITE', 1, 0)) AS social_sms_action_snap_anyone_invite,
        MAX(IF(feature = 'SNAP_ANYONE_INVITE', 1, 0)) AS social_sms_action_snap_anyone_invite_uu,
        MAX(IF(feature = 'SNAP_ANYONE_INVITE', 1, 0)) AS social_sms_action_snap_anyone_invite_active_day,
        SUM(IF(feature = 'FRIEND_INVITE_SEND_TO', 1, 0)) AS social_sms_action_friend_invite_send_to,
        MAX(IF(feature = 'FRIEND_INVITE_SEND_TO', 1, 0)) AS social_sms_action_friend_invite_send_to_uu,
        MAX(IF(feature = 'FRIEND_INVITE_SEND_TO', 1, 0)) AS social_sms_action_friend_invite_send_to_active_day,

        FROM {source_table}
        GROUP BY
        ts,
        ghost_user_id
        """.format(source_table=source_table),

        metrics=[
        social_sms_action,
        social_sms_action_uu,
        social_sms_action_active_day,
        social_sms_action_friend_invite_add_friends,
        social_sms_action_friend_invite_add_friends_uu,
        social_sms_action_friend_invite_add_friends_active_day,
        social_sms_action_friend_invite_all_contacts,
        social_sms_action_friend_invite_all_contacts_uu,
        social_sms_action_friend_invite_all_contacts_active_day,
        social_sms_action_friend_invite_friend_feed,
        social_sms_action_friend_invite_friend_feed_uu,
        social_sms_action_friend_invite_friend_feed_active_day,
        social_sms_action_friend_invite_invite_friends_page,
        social_sms_action_friend_invite_invite_friends_page_uu,
        social_sms_action_friend_invite_invite_friends_page_active_day,
        social_sms_action_friend_invite_registration,
        social_sms_action_friend_invite_registration_uu,
        social_sms_action_friend_invite_registration_active_day,
        social_sms_action_friend_invite_universal_search,
        social_sms_action_friend_invite_universal_search_uu,
        social_sms_action_friend_invite_universal_search_active_day,
        social_sms_action_memories_link_share,
        social_sms_action_memories_link_share_uu,
        social_sms_action_memories_link_share_active_day,
        social_sms_action_public_link_discover_episode,
        social_sms_action_public_link_discover_episode_uu,
        social_sms_action_public_link_discover_episode_active_day,
        social_sms_action_public_link_discover_profile,
        social_sms_action_public_link_discover_profile_uu,
        social_sms_action_public_link_discover_profile_active_day,
        social_sms_action_public_link_lens,
        social_sms_action_public_link_lens_uu,
        social_sms_action_public_link_lens_active_day,
        social_sms_action_public_link_map_story,
        social_sms_action_public_link_map_story_uu,
        social_sms_action_public_link_map_story_active_day,
        social_sms_action_public_link_public_profile,
        social_sms_action_public_link_public_profile_uu,
        social_sms_action_public_link_public_profile_active_day,
        social_sms_action_public_link_public_profile_snap,
        social_sms_action_public_link_public_profile_snap_uu,
        social_sms_action_public_link_public_profile_snap_active_day,
        social_sms_action_public_link_spotlight_snap,
        social_sms_action_public_link_spotlight_snap_uu,
        social_sms_action_public_link_spotlight_snap_active_day,
        social_sms_action_public_link_user_profile,
        social_sms_action_public_link_user_profile_uu,
        social_sms_action_public_link_user_profile_active_day,
        social_sms_action_registration_contacts,
        social_sms_action_registration_contacts_uu,
        social_sms_action_registration_contacts_active_day,
        social_sms_action_snap_anyone_invite,
        social_sms_action_snap_anyone_invite_uu,
        social_sms_action_snap_anyone_invite_active_day,
        social_sms_action_friend_invite_send_to,
        social_sms_action_friend_invite_send_to_uu,
        social_sms_action_friend_invite_send_to_active_day        
        ],

        name="social_sms_action",
        bq_dialect="standard"
    )
    return mt

def social_sms_invite_funnel_daily(start_date, end_date):
    """
    Social SMS invite clicks and unsubscribe clicks
    """

    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
    `sc-analytics.report_growth.social_sms_invite_funnel_daily_*`
    WHERE _table_suffix between '{start}' and '{end}'
    and ghost_user_id is not null
    """.format(
        start=start_date,
        end=end_date,
    )

    social_sms_invite_send = Metric('social_sms_invite_send', 'social_sms_invite_send', dist='cont', daily=True, cumulative=True,)
    social_sms_invite_send_uu = Metric('social_sms_invite_send_uu', 'social_sms_invite_send_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_invite_send_onplatform_active = Metric('social_sms_invite_send_onplatform_active', 'social_sms_invite_send (to active Snapchatter)', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    social_sms_invite_send_onplatform_active_uu = Metric('social_sms_invite_send_onplatform_active_uu', 'social_sms_invite_send_uu (to active Snapchatter)', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    social_sms_invite_send_onplatform_dnu = Metric('social_sms_invite_send_onplatform_dnu', 'social_sms_invite_send (to DNU Snapchatter)', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    social_sms_invite_send_onplatform_dnu_uu = Metric('social_sms_invite_send_onplatform_dnu_uu', 'social_sms_invite_send_uu (to DNU Snapchatter)', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    social_sms_invite_send_onplatform_deleted = Metric('social_sms_invite_send_onplatform_deleted', 'social_sms_invite_send (to deleted Snapchatter)', dist='cont', daily=True, cumulative=True)
    social_sms_invite_send_onplatform_deleted_uu = Metric('social_sms_invite_send_onplatform_deleted_uu', 'social_sms_invite_send_uu (to deleted Snapchatter)', dist='bin', daily=True, cumulative=True)
    
    social_sms_invite_click = Metric('social_sms_invite_click', 'social_sms_invite_click', dist='cont', daily=True, cumulative=True,)
    social_sms_invite_click_uu = Metric('social_sms_invite_click_uu', 'social_sms_invite_click_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_invite_click_aws = Metric('social_sms_invite_click_aws', 'social_sms_invite_click_aws', dist='cont', daily=True, cumulative=True,)
    social_sms_invite_click_aws_uu = Metric('social_sms_invite_click_aws_uu', 'social_sms_invite_click_aws_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_invite_click_twilio = Metric('social_sms_invite_click_twilio', 'social_sms_invite_click_twilio', dist='cont', daily=True, cumulative=True,)
    social_sms_invite_click_twilio_uu = Metric('social_sms_invite_click_twilio_uu', 'social_sms_invite_click_twilio_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_invite_click_nexmo = Metric('social_sms_invite_click_nexmo', 'social_sms_invite_click_nexmo', dist='cont', daily=True, cumulative=True,)
    social_sms_invite_click_nexmo_uu = Metric('social_sms_invite_click_nexmo_uu', 'social_sms_invite_click_nexmo_uu', dist='bin', daily=True, cumulative=True,)    
    social_sms_invite_click_registration_contacts = Metric('social_sms_invite_click_registration_contacts', 'social_sms_invite_click_registration_contacts', dist='cont', daily=True, cumulative=True,)
    social_sms_invite_click_registration_contacts_uu = Metric('social_sms_invite_click_registration_contacts_uu', 'social_sms_invite_click_registration_contacts_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_invite_click_friend_invite_friend_feed = Metric('social_sms_invite_click_friend_invite_friend_feed', 'social_sms_invite_click_friend_invite_friend_feed', dist='cont', daily=True, cumulative=True,)
    social_sms_invite_click_friend_invite_friend_feed_uu = Metric('social_sms_invite_click_friend_invite_friend_feed_uu', 'social_sms_invite_click_friend_invite_friend_feed_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_invite_click_friend_invite_all_contacts = Metric('social_sms_invite_click_friend_invite_all_contacts', 'social_sms_invite_click_friend_invite_all_contacts', dist='cont', daily=True, cumulative=True,)
    social_sms_invite_click_friend_invite_all_contacts_uu = Metric('social_sms_invite_click_friend_invite_all_contacts_uu', 'social_sms_invite_click_friend_invite_all_contacts_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_invite_click_snap_anyone_invite = Metric('social_sms_invite_click_snap_anyone_invite', 'social_sms_invite_click_snap_anyone_invite', dist='cont', daily=True, cumulative=True,)
    social_sms_invite_click_snap_anyone_invite_uu = Metric('social_sms_invite_click_snap_anyone_invite_uu', 'social_sms_invite_click_snap_anyone_invite_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_invite_click_memories_link_share = Metric('social_sms_invite_click_memories_link_share', 'social_sms_invite_click_memories_link_share', dist='cont', daily=True, cumulative=True,)
    social_sms_invite_click_memories_link_share_uu = Metric('social_sms_invite_click_memories_link_share_uu', 'social_sms_invite_click_memories_link_share_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_invite_click_public_link_public_profile_snap = Metric('social_sms_invite_click_public_link_public_profile_snap', 'social_sms_invite_click_public_link_public_profile_snap', dist='cont', daily=True, cumulative=True,)
    social_sms_invite_click_public_link_public_profile_snap_uu = Metric('social_sms_invite_click_public_link_public_profile_snap_uu', 'social_sms_invite_click_public_link_public_profile_snap_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_invite_click_friend_invite_add_friends = Metric('social_sms_invite_click_friend_invite_add_friends', 'social_sms_invite_click_friend_invite_add_friends', dist='cont', daily=True, cumulative=True,)
    social_sms_invite_click_friend_invite_add_friends_uu = Metric('social_sms_invite_click_friend_invite_add_friends_uu', 'social_sms_invite_click_friend_invite_add_friends_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_invite_click_friend_invite_registration = Metric('social_sms_invite_click_friend_invite_registration', 'social_sms_invite_click_friend_invite_registration', dist='cont', daily=True, cumulative=True,)
    social_sms_invite_click_friend_invite_registration_uu = Metric('social_sms_invite_click_friend_invite_registration_uu', 'social_sms_invite_click_friend_invite_registration_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_invite_click_friend_invite_universal_search = Metric('social_sms_invite_click_friend_invite_universal_search', 'social_sms_invite_click_friend_invite_universal_search', dist='cont', daily=True, cumulative=True,)
    social_sms_invite_click_friend_invite_universal_search_uu = Metric('social_sms_invite_click_friend_invite_universal_search_uu', 'social_sms_invite_click_friend_invite_universal_search_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_invite_click_friend_invite_invite_friends_page = Metric('social_sms_invite_click_friend_invite_invite_friends_page', 'social_sms_invite_click_friend_invite_invite_friends_page', dist='cont', daily=True, cumulative=True,)
    social_sms_invite_click_friend_invite_invite_friends_page_uu = Metric('social_sms_invite_click_friend_invite_invite_friends_page_uu', 'social_sms_invite_click_friend_invite_invite_friends_page_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_invite_click_public_link_lens = Metric('social_sms_invite_click_public_link_lens', 'social_sms_invite_click_public_link_lens', dist='cont', daily=True, cumulative=True,)
    social_sms_invite_click_public_link_lens_uu = Metric('social_sms_invite_click_public_link_lens_uu', 'social_sms_invite_click_public_link_lens_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_invite_click_public_link_spotlight_snap = Metric('social_sms_invite_click_public_link_spotlight_snap', 'social_sms_invite_click_public_link_spotlight_snap', dist='cont', daily=True, cumulative=True,)
    social_sms_invite_click_public_link_spotlight_snap_uu = Metric('social_sms_invite_click_public_link_spotlight_snap_uu', 'social_sms_invite_click_public_link_spotlight_snap_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_invite_click_public_link_discover_episode = Metric('social_sms_invite_click_public_link_discover_episode', 'social_sms_invite_click_public_link_discover_episode', dist='cont', daily=True, cumulative=True,)
    social_sms_invite_click_public_link_discover_episode_uu = Metric('social_sms_invite_click_public_link_discover_episode_uu', 'social_sms_invite_click_public_link_discover_episode_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_invite_click_public_link_user_profile = Metric('social_sms_invite_click_public_link_user_profile', 'social_sms_invite_click_public_link_user_profile', dist='cont', daily=True, cumulative=True,)
    social_sms_invite_click_public_link_user_profile_uu = Metric('social_sms_invite_click_public_link_user_profile_uu', 'social_sms_invite_click_public_link_user_profile_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_invite_click_public_link_discover_profile = Metric('social_sms_invite_click_public_link_discover_profile', 'social_sms_invite_click_public_link_discover_profile', dist='cont', daily=True, cumulative=True,)
    social_sms_invite_click_public_link_discover_profile_uu = Metric('social_sms_invite_click_public_link_discover_profile_uu', 'social_sms_invite_click_public_link_discover_profile_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_invite_click_public_link_public_profile = Metric('social_sms_invite_click_public_link_public_profile', 'social_sms_invite_click_public_link_public_profile', dist='cont', daily=True, cumulative=True,)
    social_sms_invite_click_public_link_public_profile_uu = Metric('social_sms_invite_click_public_link_public_profile_uu', 'social_sms_invite_click_public_link_public_profile_uu', dist='bin', daily=True, cumulative=True,)
    social_sms_invite_click_friend_invite_send_to = Metric('social_sms_invite_click_friend_invite_send_to', 'social_sms_invite_click_friend_invite_send_to', dist='cont', daily=True, cumulative=True,)
    social_sms_invite_click_friend_invite_send_to_uu = Metric('social_sms_invite_click_friend_invite_send_to_uu', 'social_sms_invite_click_friend_invite_send_to_uu', dist='bin', daily=True, cumulative=True,)    
    
    social_sms_invite_unsubscribe_click = Metric('social_sms_invite_unsubscribe_click', 'social_sms_invite_unsubscribe_click', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    social_sms_invite_unsubscribe_click_uu = Metric('social_sms_invite_unsubscribe_click_uu', 'social_sms_invite_unsubscribe_click_uu', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)

    social_sms_invite_click_through_rate = Metric(
        col='social_sms_invite_click_through_rate', 
        numerator='social_sms_invite_click', 
        denominator='social_sms_invite_send', 
        dist='ratio', 
        desired_direction=POSITIVE)
    
    social_sms_invite_unsubscribe_click_through_rate = Metric(
        col='social_sms_invite_unsubscribe_click_through_rate', 
        numerator='social_sms_invite_unsubscribe_click', 
        denominator='social_sms_invite_send', 
        dist='ratio', 
        desired_direction=NEGATIVE)

    social_sms_invite_cost = Metric('social_sms_invite_cost', 'social_sms_invite_cost', dist='cont', daily=True, cumulative=True,)
    social_sms_invite_cost_using_segments = Metric('social_sms_invite_cost_using_segments', 'social_sms_invite_cost_using_segments', dist='cont', daily=True, cumulative=True,)    

    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        ghost_user_id,

        sum(1) as social_sms_invite_send,
        max(1) as social_sms_invite_send_uu,
        sum(if(receiver_ghost_user_id is not null, 1, 0)) as social_sms_invite_send_onplatform_active,
        max(if(receiver_ghost_user_id is not null, 1, 0)) as social_sms_invite_send_onplatform_active_uu,
        sum(if(receiver_ghost_user_id is not null and date(receiver_creation_time) = date(event_time), 1, 0)) as social_sms_invite_send_onplatform_dnu,
        max(if(receiver_ghost_user_id is not null and date(receiver_creation_time) = date(event_time), 1, 0)) as social_sms_invite_send_onplatform_dnu_uu,
        sum(if(receiver_ghost_user_id is null and receiver_creation_time is not null, 1, 0)) as social_sms_invite_send_onplatform_deleted,
        max(if(receiver_ghost_user_id is null and receiver_creation_time is not null, 1, 0)) as social_sms_invite_send_onplatform_deleted_uu,
        
        sum(if(n_decode_click > 0, 1, 0)) as social_sms_invite_click,
        max(if(n_decode_click > 0, 1, 0)) as social_sms_invite_click_uu,
        sum(if(n_decode_click > 0 and vendor_decode = 'AWS', 1, 0)) as social_sms_invite_click_aws,
        max(if(n_decode_click > 0 and vendor_decode = 'AWS', 1, 0)) as social_sms_invite_click_aws_uu,
        sum(if(n_decode_click > 0 and vendor_decode = 'TWILIO', 1, 0)) as social_sms_invite_click_twilio,
        max(if(n_decode_click > 0 and vendor_decode = 'TWILIO', 1, 0)) as social_sms_invite_click_twilio_uu,
        sum(if(n_decode_click > 0 and vendor_decode = 'NEXMO', 1, 0)) as social_sms_invite_click_nexmo,
        max(if(n_decode_click > 0 and vendor_decode = 'NEXMO', 1, 0)) as social_sms_invite_click_nexmo_uu,
        sum(if(n_decode_click > 0 and destination_decode = 'REGISTRATION_CONTACTS', 1, 0)) as social_sms_invite_click_registration_contacts,
        max(if(n_decode_click > 0 and destination_decode = 'REGISTRATION_CONTACTS', 1, 0)) as social_sms_invite_click_registration_contacts_uu,
        sum(if(n_decode_click > 0 and destination_decode = 'FRIEND_INVITE_FRIEND_FEED', 1, 0)) as social_sms_invite_click_friend_invite_friend_feed,
        max(if(n_decode_click > 0 and destination_decode = 'FRIEND_INVITE_FRIEND_FEED', 1, 0)) as social_sms_invite_click_friend_invite_friend_feed_uu,
        sum(if(n_decode_click > 0 and destination_decode = 'FRIEND_INVITE_ALL_CONTACTS', 1, 0)) as social_sms_invite_click_friend_invite_all_contacts,
        max(if(n_decode_click > 0 and destination_decode = 'FRIEND_INVITE_ALL_CONTACTS', 1, 0)) as social_sms_invite_click_friend_invite_all_contacts_uu,
        sum(if(n_decode_click > 0 and destination_decode = 'SNAP_ANYONE_INVITE', 1, 0)) as social_sms_invite_click_snap_anyone_invite,
        max(if(n_decode_click > 0 and destination_decode = 'SNAP_ANYONE_INVITE', 1, 0)) as social_sms_invite_click_snap_anyone_invite_uu,
        sum(if(n_decode_click > 0 and destination_decode = 'MEMORIES_LINK_SHARE', 1, 0)) as social_sms_invite_click_memories_link_share,
        max(if(n_decode_click > 0 and destination_decode = 'MEMORIES_LINK_SHARE', 1, 0)) as social_sms_invite_click_memories_link_share_uu,
        sum(if(n_decode_click > 0 and destination_decode = 'PUBLIC_LINK_PUBLIC_PROFILE_SNAP', 1, 0)) as social_sms_invite_click_public_link_public_profile_snap,
        max(if(n_decode_click > 0 and destination_decode = 'PUBLIC_LINK_PUBLIC_PROFILE_SNAP', 1, 0)) as social_sms_invite_click_public_link_public_profile_snap_uu,
        sum(if(n_decode_click > 0 and destination_decode = 'FRIEND_INVITE_ADD_FRIENDS', 1, 0)) as social_sms_invite_click_friend_invite_add_friends,
        max(if(n_decode_click > 0 and destination_decode = 'FRIEND_INVITE_ADD_FRIENDS', 1, 0)) as social_sms_invite_click_friend_invite_add_friends_uu,
        sum(if(n_decode_click > 0 and destination_decode = 'FRIEND_INVITE_REGISTRATION', 1, 0)) as social_sms_invite_click_friend_invite_registration,
        max(if(n_decode_click > 0 and destination_decode = 'FRIEND_INVITE_REGISTRATION', 1, 0)) as social_sms_invite_click_friend_invite_registration_uu,
        sum(if(n_decode_click > 0 and destination_decode = 'FRIEND_INVITE_UNIVERSAL_SEARCH', 1, 0)) as social_sms_invite_click_friend_invite_universal_search,
        max(if(n_decode_click > 0 and destination_decode = 'FRIEND_INVITE_UNIVERSAL_SEARCH', 1, 0)) as social_sms_invite_click_friend_invite_universal_search_uu,
        sum(if(n_decode_click > 0 and destination_decode = 'FRIEND_INVITE_INVITE_FRIENDS_PAGE', 1, 0)) as social_sms_invite_click_friend_invite_invite_friends_page,
        max(if(n_decode_click > 0 and destination_decode = 'FRIEND_INVITE_INVITE_FRIENDS_PAGE', 1, 0)) as social_sms_invite_click_friend_invite_invite_friends_page_uu,
        sum(if(n_decode_click > 0 and destination_decode = 'PUBLIC_LINK_LENS', 1, 0)) as social_sms_invite_click_public_link_lens,
        max(if(n_decode_click > 0 and destination_decode = 'PUBLIC_LINK_LENS', 1, 0)) as social_sms_invite_click_public_link_lens_uu,
        sum(if(n_decode_click > 0 and destination_decode = 'PUBLIC_LINK_SPOTLIGHT_SNAP', 1, 0)) as social_sms_invite_click_public_link_spotlight_snap,
        max(if(n_decode_click > 0 and destination_decode = 'PUBLIC_LINK_SPOTLIGHT_SNAP', 1, 0)) as social_sms_invite_click_public_link_spotlight_snap_uu,
        sum(if(n_decode_click > 0 and destination_decode = 'PUBLIC_LINK_DISCOVER_EPISODE', 1, 0)) as social_sms_invite_click_public_link_discover_episode,
        max(if(n_decode_click > 0 and destination_decode = 'PUBLIC_LINK_DISCOVER_EPISODE', 1, 0)) as social_sms_invite_click_public_link_discover_episode_uu,
        sum(if(n_decode_click > 0 and destination_decode = 'PUBLIC_LINK_USER_PROFILE', 1, 0)) as social_sms_invite_click_public_link_user_profile,
        max(if(n_decode_click > 0 and destination_decode = 'PUBLIC_LINK_USER_PROFILE', 1, 0)) as social_sms_invite_click_public_link_user_profile_uu,
        sum(if(n_decode_click > 0 and destination_decode = 'PUBLIC_LINK_DISCOVER_PROFILE', 1, 0)) as social_sms_invite_click_public_link_discover_profile,
        max(if(n_decode_click > 0 and destination_decode = 'PUBLIC_LINK_DISCOVER_PROFILE', 1, 0)) as social_sms_invite_click_public_link_discover_profile_uu,
        sum(if(n_decode_click > 0 and destination_decode = 'PUBLIC_LINK_PUBLIC_PROFILE', 1, 0)) as social_sms_invite_click_public_link_public_profile,
        max(if(n_decode_click > 0 and destination_decode = 'PUBLIC_LINK_PUBLIC_PROFILE', 1, 0)) as social_sms_invite_click_public_link_public_profile_uu,
        sum(if(n_decode_click > 0 and destination_decode = 'FRIEND_INVITE_SEND_TO', 1, 0)) as social_sms_invite_click_friend_invite_send_to,
        max(if(n_decode_click > 0 and destination_decode = 'FRIEND_INVITE_SEND_TO', 1, 0)) as social_sms_invite_click_friend_invite_send_to_uu,

        sum(if(n_decode_unsubscribe_click > 0, 1, 0)) as social_sms_invite_unsubscribe_click,
        max(if(n_decode_unsubscribe_click > 0, 1, 0)) as social_sms_invite_unsubscribe_click_uu,

        sum(avg_cost) as social_sms_invite_cost,
        sum(avg_cost_using_segments) as social_sms_invite_cost_using_segments,        

        FROM {source_table}
        GROUP BY
        ts,
        ghost_user_id
        """.format(source_table=source_table),

        metrics=[
        social_sms_invite_send,
        social_sms_invite_send_uu,
        social_sms_invite_send_onplatform_active,
        social_sms_invite_send_onplatform_active_uu,
        social_sms_invite_send_onplatform_dnu,
        social_sms_invite_send_onplatform_dnu_uu,
        social_sms_invite_send_onplatform_deleted,
        social_sms_invite_send_onplatform_deleted_uu,
        social_sms_invite_click,
        social_sms_invite_click_uu,
        social_sms_invite_click_aws,
        social_sms_invite_click_aws_uu,
        social_sms_invite_click_twilio,
        social_sms_invite_click_twilio_uu,
        social_sms_invite_click_nexmo,
        social_sms_invite_click_nexmo_uu,            
        social_sms_invite_click_registration_contacts,
        social_sms_invite_click_registration_contacts_uu,
        social_sms_invite_click_friend_invite_friend_feed,
        social_sms_invite_click_friend_invite_friend_feed_uu,
        social_sms_invite_click_friend_invite_all_contacts,
        social_sms_invite_click_friend_invite_all_contacts_uu,
        social_sms_invite_click_snap_anyone_invite,
        social_sms_invite_click_snap_anyone_invite_uu,
        social_sms_invite_click_memories_link_share,
        social_sms_invite_click_memories_link_share_uu,
        social_sms_invite_click_public_link_public_profile_snap,
        social_sms_invite_click_public_link_public_profile_snap_uu,
        social_sms_invite_click_friend_invite_add_friends,
        social_sms_invite_click_friend_invite_add_friends_uu,
        social_sms_invite_click_friend_invite_registration,
        social_sms_invite_click_friend_invite_registration_uu,
        social_sms_invite_click_friend_invite_universal_search,
        social_sms_invite_click_friend_invite_universal_search_uu,
        social_sms_invite_click_friend_invite_invite_friends_page,
        social_sms_invite_click_friend_invite_invite_friends_page_uu,
        social_sms_invite_click_public_link_lens,
        social_sms_invite_click_public_link_lens_uu,
        social_sms_invite_click_public_link_spotlight_snap,
        social_sms_invite_click_public_link_spotlight_snap_uu,
        social_sms_invite_click_public_link_discover_episode,
        social_sms_invite_click_public_link_discover_episode_uu,
        social_sms_invite_click_public_link_user_profile,
        social_sms_invite_click_public_link_user_profile_uu,
        social_sms_invite_click_public_link_discover_profile,
        social_sms_invite_click_public_link_discover_profile_uu,
        social_sms_invite_click_public_link_public_profile,
        social_sms_invite_click_public_link_public_profile_uu,
        social_sms_invite_click_friend_invite_send_to,
        social_sms_invite_click_friend_invite_send_to_uu,            
        social_sms_invite_unsubscribe_click,
        social_sms_invite_unsubscribe_click_uu, 
        social_sms_invite_click_through_rate,
        social_sms_invite_unsubscribe_click_through_rate, 
        social_sms_invite_cost,
        social_sms_invite_cost_using_segments
        ],

        name="social_sms_invite_funnel_daily",
        bq_dialect="standard"
    )
    return mt    

def invite_contact_impression_action(start_date, end_date):
    """
    Impression and action on Invite pages
    """

    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
    `sc-analytics.report_growth.invite_contact_impression_action_agg_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    invite_contact_impressed = Metric('invite_contact_impressed', 'invite_contact_impressed', dist='cont', daily=True, cumulative=True,)
    invite_contact_impressed_uu = Metric('invite_contact_impressed_uu', 'invite_contact_impressed_uu', dist='bin', daily=True, cumulative=True,)
    invite_contact_impressed_onplatform_active = Metric('invite_contact_impressed_onplatform_active', 'invite_contact_impressed (active Snapchatter)', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    invite_contact_impressed_onplatform_active_uu = Metric('invite_contact_impressed_onplatform_active_uu', 'invite_contact_impressed_uu (active Snapchatter)', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    invite_contact_impressed_onplatform_dnu = Metric('invite_contact_impressed_onplatform_dnu', 'invite_contact_impressed (DNU Snapchatter)', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    invite_contact_impressed_onplatform_dnu_uu = Metric('invite_contact_impressed_onplatform_dnu_uu', 'invite_contact_impressed_uu (DNU Snapchatter)', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)
    invite_contact_impressed_onplatform_deleted = Metric('invite_contact_impressed_onplatform_deleted', 'invite_contact_impressed (deleted Snapchatter)', dist='cont', daily=True, cumulative=True)
    invite_contact_impressed_onplatform_deleted_uu = Metric('invite_contact_impressed_onplatform_deleted_uu', 'invite_contact_impressed_uu (deleted Snapchatter)', dist='bin', daily=True, cumulative=True)
    invite_contact_invited = Metric('invite_contact_invited', 'invite_contact_invited', dist='cont', daily=True, cumulative=True,)
    invite_contact_invited_uu = Metric('invite_contact_invited_uu', 'invite_contact_invited_uu', dist='bin', daily=True, cumulative=True,)    
    invite_contact_invited_onplatform_active = Metric('invite_contact_invited_onplatform_active', 'invite_contact_invited (active Snapchatter)', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    invite_contact_invited_onplatform_active_uu = Metric('invite_contact_invited_onplatform_active_uu', 'invite_contact_invited_uu (active Snapchatter)', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)    
    invite_contact_invited_onplatform_dnu = Metric('invite_contact_invited_onplatform_dnu', 'invite_contact_invited (DNU Snapchatter)', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)
    invite_contact_invited_onplatform_dnu_uu = Metric('invite_contact_invited_onplatform_dnu_uu', 'invite_contact_invited_uu (DNU Snapchatter)', dist='bin', daily=True, cumulative=True, desired_direction=NEGATIVE)    
    invite_contact_invited_onplatform_deleted = Metric('invite_contact_invited_onplatform_deleted', 'invite_contact_invited (deleted Snapchatter)', dist='cont', daily=True, cumulative=True)
    invite_contact_invited_onplatform_deleted_uu = Metric('invite_contact_invited_onplatform_deleted_uu', 'invite_contact_invited_uu (deleted Snapchatter)', dist='bin', daily=True, cumulative=True)    
    invite_contact_invitation_rate = Metric(
        col='invite_contact_invitation_rate', 
        numerator='invite_contact_invited', 
        denominator='invite_contact_impressed', 
        dist='ratio', 
        desired_direction=POSITIVE)

    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        owner_ghost_user_id as ghost_user_id,

        count(distinct hashed_phone_number) as invite_contact_impressed,
        max(if(hashed_phone_number is not null, 1, 0)) as invite_contact_impressed_uu,
        count(distinct if(ghost_user_id is not null, hashed_phone_number, NULL)) as invite_contact_impressed_onplatform_active,
        max(if(hashed_phone_number is not null and ghost_user_id is not null, 1, 0)) as invite_contact_impressed_onplatform_active_uu,
        count(distinct if(ghost_user_id is not null and date(creation_time) = date(event_time_impression), hashed_phone_number, NULL)) as invite_contact_impressed_onplatform_dnu,
        max(if(hashed_phone_number is not null and ghost_user_id is not null and date(creation_time) = date(event_time_impression), 1, 0)) as invite_contact_impressed_onplatform_dnu_uu,
        count(distinct if(ghost_user_id is null and creation_time is not null, hashed_phone_number, NULL)) as invite_contact_impressed_onplatform_deleted,
        max(if(hashed_phone_number is not null and ghost_user_id is null and creation_time is not null, 1, 0)) as invite_contact_impressed_onplatform_deleted_uu,
        count(distinct invited_hashed_phone_number) as invite_contact_invited,
        max(if(invited_hashed_phone_number is not null, 1, 0)) as invite_contact_invited_uu,
        count(distinct if(ghost_user_id is not null, invited_hashed_phone_number, NULL)) as invite_contact_invited_onplatform_active,
        max(if(invited_hashed_phone_number is not null and ghost_user_id is not null, 1, 0)) as invite_contact_invited_onplatform_active_uu,
        count(distinct if(ghost_user_id is not null and date(creation_time) = date(event_time_impression), invited_hashed_phone_number, NULL)) as invite_contact_invited_onplatform_dnu,
        max(if(invited_hashed_phone_number is not null and ghost_user_id is not null and date(creation_time) = date(event_time_impression), 1, 0)) as invite_contact_invited_onplatform_dnu_uu,
        count(distinct if(ghost_user_id is null and creation_time is not null, invited_hashed_phone_number, NULL)) as invite_contact_invited_onplatform_deleted,
        max(if(invited_hashed_phone_number is not null and ghost_user_id is null and creation_time is not null, 1, 0)) as invite_contact_invited_onplatform_deleted_uu,

        FROM {source_table}
        GROUP BY
        ts,
        ghost_user_id
        """.format(source_table=source_table),

        metrics=[
        invite_contact_impressed,
        invite_contact_impressed_uu,
        invite_contact_impressed_onplatform_active,
        invite_contact_impressed_onplatform_active_uu,
        invite_contact_impressed_onplatform_dnu,
        invite_contact_impressed_onplatform_dnu_uu,
        invite_contact_impressed_onplatform_deleted,
        invite_contact_impressed_onplatform_deleted_uu,
        invite_contact_invited,
        invite_contact_invited_uu,
        invite_contact_invited_onplatform_active,
        invite_contact_invited_onplatform_active_uu,
        invite_contact_invited_onplatform_dnu,
        invite_contact_invited_onplatform_dnu_uu,
        invite_contact_invited_onplatform_deleted,
        invite_contact_invited_onplatform_deleted_uu,
        invite_contact_invitation_rate
        ],

        name="invite_contact_impression_action",
        bq_dialect="standard"
    )
    return mt

def twilio_total_cost(start_date, end_date):
    """
    Approximated total Twilio cost with breakdowns by feature
    """

    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
    `sc-analytics.report_growth.twilio_total_cost_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    twilio_total_cost_dollar = Metric('twilio_total_cost_dollar', 'twilio_total_cost_dollar', dist='cont', daily=True, cumulative=True,)
    twilio_total_cost_dollar_memories_link_share = Metric('twilio_total_cost_dollar_memories_link_share', 'twilio_total_cost_dollar_memories_link_share', dist='cont', daily=True, cumulative=True,)
    twilio_total_cost_dollar_public_link_discover_episode = Metric('twilio_total_cost_dollar_public_link_discover_episode', 'twilio_total_cost_dollar_public_link_discover_episode', dist='cont', daily=True, cumulative=True,)
    twilio_total_cost_dollar_public_link_discover_profile = Metric('twilio_total_cost_dollar_public_link_discover_profile', 'twilio_total_cost_dollar_public_link_discover_profile', dist='cont', daily=True, cumulative=True,)
    twilio_total_cost_dollar_public_link_lens = Metric('twilio_total_cost_dollar_public_link_lens', 'twilio_total_cost_dollar_public_link_lens', dist='cont', daily=True, cumulative=True,)
    twilio_total_cost_dollar_public_link_map_story = Metric('twilio_total_cost_dollar_public_link_map_story', 'twilio_total_cost_dollar_public_link_map_story', dist='cont', daily=True, cumulative=True,)
    twilio_total_cost_dollar_public_link_public_profile = Metric('twilio_total_cost_dollar_public_link_public_profile', 'twilio_total_cost_dollar_public_link_public_profile', dist='cont', daily=True, cumulative=True,)
    twilio_total_cost_dollar_public_link_public_profile_snap = Metric('twilio_total_cost_dollar_public_link_public_profile_snap', 'twilio_total_cost_dollar_public_link_public_profile_snap', dist='cont', daily=True, cumulative=True,)
    twilio_total_cost_dollar_public_link_spotlight_snap = Metric('twilio_total_cost_dollar_public_link_spotlight_snap', 'twilio_total_cost_dollar_public_link_spotlight_snap', dist='cont', daily=True, cumulative=True,)
    twilio_total_cost_dollar_public_link_user_profile = Metric('twilio_total_cost_dollar_public_link_user_profile', 'twilio_total_cost_dollar_public_link_user_profile', dist='cont', daily=True, cumulative=True,)
    twilio_total_cost_dollar_snap_anyone_invite = Metric('twilio_total_cost_dollar_snap_anyone_invite', 'twilio_total_cost_dollar_snap_anyone_invite', dist='cont', daily=True, cumulative=True,)
    twilio_total_cost_dollar_registration_contacts = Metric('twilio_total_cost_dollar_registration_contacts', 'twilio_total_cost_dollar_registration_contacts', dist='cont', daily=True, cumulative=True,)

    mt = MetricTable(
        sql="""
        SELECT *
        from {source_table}
        """.format(source_table=source_table),

        metrics=[
                twilio_total_cost_dollar,
                twilio_total_cost_dollar_memories_link_share,
                twilio_total_cost_dollar_public_link_discover_episode,
                twilio_total_cost_dollar_public_link_discover_profile,
                twilio_total_cost_dollar_public_link_lens,
                twilio_total_cost_dollar_public_link_map_story,
                twilio_total_cost_dollar_public_link_public_profile,
                twilio_total_cost_dollar_public_link_public_profile_snap,
                twilio_total_cost_dollar_public_link_spotlight_snap,
                twilio_total_cost_dollar_public_link_user_profile,
                twilio_total_cost_dollar_snap_anyone_invite,
                twilio_total_cost_dollar_registration_contacts,
                ],
        name="twilio_total_cost",
        bq_dialect="standard"
    )
    return mt

def twilio_total_cost_per_dnu(start_date, end_date):
    """
    Approximated total Twilio cost per DNU (same day) with breakdowns by feature
    """

    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table1 = """
    `sc-analytics.report_growth.app_application_external_deep_link_user_level_2*`
    WHERE concat('2', _table_suffix) between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )
    source_table2 = """
    `sc-analytics.report_growth.twilio_total_cost_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    twilio_total_cost_dollar_per_dnu = Metric('twilio_total_cost_dollar_per_dnu', 'twilio_total_cost_dollar_per_dnu (same day)', dist='cont', daily=True, cumulative=True,)
    twilio_total_cost_dollar_per_dnu_memories_link_share = Metric('twilio_total_cost_dollar_per_dnu_memories_link_share', 'twilio_total_cost_dollar_per_dnu_memories_link_share (same day)', dist='cont', daily=True, cumulative=True,)
    twilio_total_cost_dollar_per_dnu_public_link_discover_episode = Metric('twilio_total_cost_dollar_per_dnu_public_link_discover_episode', 'twilio_total_cost_dollar_per_dnu_public_link_discover_episode (same day)', dist='cont', daily=True, cumulative=True,)
    twilio_total_cost_dollar_per_dnu_public_link_discover_profile = Metric('twilio_total_cost_dollar_per_dnu_public_link_discover_profile', 'twilio_total_cost_dollar_per_dnu_public_link_discover_profile (same day)', dist='cont', daily=True, cumulative=True,)
    twilio_total_cost_dollar_per_dnu_public_link_lens = Metric('twilio_total_cost_dollar_per_dnu_public_link_lens', 'twilio_total_cost_dollar_per_dnu_public_link_lens (same day)', dist='cont', daily=True, cumulative=True,)
    twilio_total_cost_dollar_per_dnu_public_link_map_story = Metric('twilio_total_cost_dollar_per_dnu_public_link_map_story', 'twilio_total_cost_dollar_per_dnu_public_link_map_story (same day)', dist='cont', daily=True, cumulative=True,)
    twilio_total_cost_dollar_per_dnu_public_link_public_profile = Metric('twilio_total_cost_dollar_per_dnu_public_link_public_profile', 'twilio_total_cost_dollar_per_dnu_public_link_public_profile (same day)', dist='cont', daily=True, cumulative=True,)
    twilio_total_cost_dollar_per_dnu_public_link_public_profile_snap = Metric('twilio_total_cost_dollar_per_dnu_public_link_public_profile_snap', 'twilio_total_cost_dollar_per_dnu_public_link_public_profile_snap (same day)', dist='cont', daily=True, cumulative=True,)
    twilio_total_cost_dollar_per_dnu_public_link_spotlight_snap = Metric('twilio_total_cost_dollar_per_dnu_public_link_spotlight_snap', 'twilio_total_cost_dollar_per_dnu_public_link_spotlight_snap (same day)', dist='cont', daily=True, cumulative=True,)
    twilio_total_cost_dollar_per_dnu_public_link_user_profile = Metric('twilio_total_cost_dollar_per_dnu_public_link_user_profile', 'twilio_total_cost_dollar_per_dnu_public_link_user_profile (same day)', dist='cont', daily=True, cumulative=True,)
    twilio_total_cost_dollar_per_dnu_snap_anyone_invite = Metric('twilio_total_cost_dollar_per_dnu_snap_anyone_invite', 'twilio_total_cost_dollar_per_dnu_snap_anyone_invite (same day)', dist='cont', daily=True, cumulative=True,)
    twilio_total_cost_dollar_per_dnu_registration_contacts = Metric('twilio_total_cost_dollar_per_dnu_registration_contacts', 'twilio_total_cost_dollar_per_dnu_registration_contacts (same day)', dist='cont', daily=True, cumulative=True,)

    mt = MetricTable(
        sql="""
        with receiver_side_dnus as
        (
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', concat('2', _table_suffix)) as ts,
        ops_ghost_user_id AS ghost_user_id,

        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature is not null, ghost_user_id, NULL)) AS link_receiver_new_user_app_open_twilio,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'MEMORIES_LINK_SHARE', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_twilio_memories_link_share,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'PUBLIC_LINK_DISCOVER_EPISODE', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_twilio_public_link_discover_episode,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'PUBLIC_LINK_DISCOVER_PROFILE', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_twilio_public_link_discover_profile,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'PUBLIC_LINK_LENS', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_twilio_public_link_lens,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'PUBLIC_LINK_MAP_STORY', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_twilio_public_link_map_story,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'PUBLIC_LINK_PUBLIC_PROFILE', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_twilio_public_link_public_profile,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'PUBLIC_LINK_PUBLIC_PROFILE_SNAP', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_twilio_public_link_public_profile_snap,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'PUBLIC_LINK_SPOTLIGHT_SNAP', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_twilio_public_link_spotlight_snap,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'PUBLIC_LINK_USER_PROFILE', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_twilio_public_link_user_profile,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'SNAP_ANYONE_INVITE', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_twilio_snap_anyone_invite,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'REGISTRATION_CONTACTS', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_twilio_registration_contacts,

        from {source_table1}
        and ops_ghost_user_id is not null
        and ops_join_key not in ('deep_link_url')
        and create_date_diff = 0
        GROUP BY 1,2
        ),

        twilio_total_cost as
        (
        select *
        from {source_table2}
        )

        select
        coalesce(t.ts, r.ts) as ts,
        coalesce(t.ghost_user_id, r.ghost_user_id) as ghost_user_id,
        safe_divide(twilio_total_cost_dollar, link_receiver_new_user_app_open_twilio) as twilio_total_cost_dollar_per_dnu,
        safe_divide(twilio_total_cost_dollar_memories_link_share, link_receiver_new_user_app_open_twilio_memories_link_share) as twilio_total_cost_dollar_per_dnu_memories_link_share,
        safe_divide(twilio_total_cost_dollar_public_link_discover_episode, link_receiver_new_user_app_open_twilio_public_link_discover_episode) as twilio_total_cost_dollar_per_dnu_public_link_discover_episode,
        safe_divide(twilio_total_cost_dollar_public_link_discover_profile, link_receiver_new_user_app_open_twilio_public_link_discover_profile) as twilio_total_cost_dollar_per_dnu_public_link_discover_profile,
        safe_divide(twilio_total_cost_dollar_public_link_lens, link_receiver_new_user_app_open_twilio_public_link_lens) as twilio_total_cost_dollar_per_dnu_public_link_lens,
        safe_divide(twilio_total_cost_dollar_public_link_map_story, link_receiver_new_user_app_open_twilio_public_link_map_story) as twilio_total_cost_dollar_per_dnu_public_link_map_story,
        safe_divide(twilio_total_cost_dollar_public_link_public_profile, link_receiver_new_user_app_open_twilio_public_link_public_profile) as twilio_total_cost_dollar_per_dnu_public_link_public_profile,
        safe_divide(twilio_total_cost_dollar_public_link_public_profile_snap, link_receiver_new_user_app_open_twilio_public_link_public_profile_snap) as twilio_total_cost_dollar_per_dnu_public_link_public_profile_snap,
        safe_divide(twilio_total_cost_dollar_public_link_spotlight_snap, link_receiver_new_user_app_open_twilio_public_link_spotlight_snap) as twilio_total_cost_dollar_per_dnu_public_link_spotlight_snap,
        safe_divide(twilio_total_cost_dollar_public_link_user_profile, link_receiver_new_user_app_open_twilio_public_link_user_profile) as twilio_total_cost_dollar_per_dnu_public_link_user_profile,
        safe_divide(twilio_total_cost_dollar_snap_anyone_invite, link_receiver_new_user_app_open_twilio_snap_anyone_invite) as twilio_total_cost_dollar_per_dnu_snap_anyone_invite,
        safe_divide(twilio_total_cost_dollar_registration_contacts, link_receiver_new_user_app_open_twilio_registration_contacts) as twilio_total_cost_dollar_per_dnu_registration_contacts,
        from twilio_total_cost t
        full outer join receiver_side_dnus r
        on t.ts = r.ts
        and t.ghost_user_id = r.ghost_user_id
        """.format(source_table1=source_table1, source_table2=source_table2),

        metrics=[
                twilio_total_cost_dollar_per_dnu,
                twilio_total_cost_dollar_per_dnu_memories_link_share,
                twilio_total_cost_dollar_per_dnu_public_link_discover_episode,
                twilio_total_cost_dollar_per_dnu_public_link_discover_profile,
                twilio_total_cost_dollar_per_dnu_public_link_lens,
                twilio_total_cost_dollar_per_dnu_public_link_map_story,
                twilio_total_cost_dollar_per_dnu_public_link_public_profile,
                twilio_total_cost_dollar_per_dnu_public_link_public_profile_snap,
                twilio_total_cost_dollar_per_dnu_public_link_spotlight_snap,
                twilio_total_cost_dollar_per_dnu_public_link_user_profile,
                twilio_total_cost_dollar_per_dnu_snap_anyone_invite,
                twilio_total_cost_dollar_per_dnu_registration_contacts
                ],
        name="twilio_total_cost_per_dnu",
        bq_dialect="standard"
    )
    return mt

def link_receiver_link_app_open(start_date, end_date):
    """
    Off-platform share receiver-side metrics on the app
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    source_table = """
    `sc-analytics.report_growth.app_application_external_deep_link_user_level_2*`
    WHERE concat('2', _table_suffix) between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    link_receiver_app_open = Metric('link_receiver_app_open', 'Receiver-side app open user', dist='cont', daily=True, cumulative=True,)
    # link_receiver_app_open_nonsender = Metric('link_receiver_app_open_nonsender', 'Receiver-side app open user (non-sender)', dist='cont', daily=True, cumulative=True,)
    link_receiver_add_friend_link_app_open = Metric('link_receiver_add_friend_link_app_open', 'Receiver-side app open user with ADD_FRIEND link', dist='cont', daily=True, cumulative=True,)
    link_receiver_commerce_link_app_open = Metric('link_receiver_commerce_link_app_open', 'Receiver-side app open user with COMMERCE link', dist='cont', daily=True, cumulative=True,)
    link_receiver_discover_link_app_open = Metric('link_receiver_discover_link_app_open', 'Receiver-side app open user with DISCOVER link', dist='cont', daily=True, cumulative=True,)
    link_receiver_lens_link_app_open = Metric('link_receiver_lens_link_app_open', 'Receiver-side app open user with LENS link', dist='cont', daily=True, cumulative=True,)
    link_receiver_map_link_app_open = Metric('link_receiver_map_link_app_open', 'Receiver-side app open user with MAP link', dist='cont', daily=True, cumulative=True,)
    link_receiver_our_story_link_app_open = Metric('link_receiver_our_story_link_app_open', 'Receiver-side app open user with OUR_STORY link', dist='cont', daily=True, cumulative=True,)
    link_receiver_profile_link_app_open = Metric('link_receiver_profile_link_app_open', 'Receiver-side app open user with PROFILE link', dist='cont', daily=True, cumulative=True,)
    link_receiver_public_user_story_link_app_open = Metric('link_receiver_public_user_story_link_app_open', 'Receiver-side app open user with PUBLIC_USER_STORY link', dist='cont', daily=True, cumulative=True,)
    link_receiver_snap_pro_link_app_open = Metric('link_receiver_snap_pro_link_app_open', 'Receiver-side app open user with SNAP_PRO link', dist='cont', daily=True, cumulative=True,)
    link_receiver_memories_link_app_open = Metric('link_receiver_memories_link_app_open', 'Receiver-side app open user with MEMORIES_LINK link', dist='cont', daily=True, cumulative=True,)

    link_receiver_new_user_app_open = Metric('link_receiver_new_user_app_open', 'Receiver-side app open new user', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_app_open_phone_hash_attrib = Metric('link_receiver_new_user_app_open_phone_hash_attrib', 'Receiver-side app open new user (phone_hash attributed)', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_app_open_share_id_attrib = Metric('link_receiver_new_user_app_open_share_id_attrib', 'Receiver-side app open new user (share_id attributed)', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_app_open_deep_link_url_attrib = Metric('link_receiver_new_user_app_open_deep_link_url_attrib', 'Receiver-side app open new user (deep_link_url attributed)', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_app_open_deep_link_url_share_id_attrib = Metric('link_receiver_new_user_app_open_deep_link_url_share_id_attrib', 'Receiver-side app open new user (deep_link_url with share_id attributed)', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_app_open_short_link_url_attrib = Metric('link_receiver_new_user_app_open_short_link_url_attrib', 'Receiver-side app open new user (short_link_url attributed)', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_friend_count = Metric('link_receiver_new_user_friend_count', 'Receiver-side app open new user friend count', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_friend_request_count = Metric('link_receiver_new_user_friend_request_count', 'Receiver-side app open new user friend request to OPS sender count', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_friend_reciprocate_count = Metric('link_receiver_new_user_friend_reciprocate_count', 'Receiver-side app open new user friend reciprocate to OPS sender count', dist='cont', daily=True, cumulative=True,)

    # link_receiver_new_user_app_open_nonsender = Metric('link_receiver_new_user_app_open_nonsender', 'Receiver-side app open new user (non-sender)', dist='cont', daily=True, cumulative=True,)    
    # link_receiver_new_user_app_open_nonsender_phone_hash_attrib = Metric('link_receiver_new_user_app_open_nonsender_phone_hash_attrib', 'Receiver-side app open new user (non-sender phone_hash attributed)', dist='cont', daily=True, cumulative=True,)
    # link_receiver_new_user_app_open_nonsender_share_id_attrib = Metric('link_receiver_new_user_app_open_nonsender_share_id_attrib', 'Receiver-side app open new user (non-sender share_id attributed)', dist='cont', daily=True, cumulative=True,)
    # link_receiver_new_user_app_open_nonsender_deep_link_url_attrib = Metric('link_receiver_new_user_app_open_nonsender_deep_link_url_attrib', 'Receiver-side app open new user (non-sender deep_link_url attributed)', dist='cont', daily=True, cumulative=True,)
    # link_receiver_new_user_app_open_nonsender_deep_link_url_share_id_attrib = Metric('link_receiver_new_user_app_open_nonsender_deep_link_url_share_id_attrib', 'Receiver-side app open new user (non-sender deep_link_url with share_id attributed)', dist='cont', daily=True, cumulative=True,)
    # link_receiver_new_user_app_open_nonsender_short_link_url_attrib = Metric('link_receiver_new_user_app_open_nonsender_short_link_url_attrib', 'Receiver-side app open new user (non-sender short_link_url attributed)', dist='cont', daily=True, cumulative=True,)

    link_receiver_new_user_app_open_invite = Metric('link_receiver_new_user_app_open_invite', 'Receiver-side app open new user from invite', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_app_open_invite_memories_link_share = Metric('link_receiver_new_user_app_open_invite_memories_link_share', 'Receiver-side app open new user from invite MEMORIES_LINK_SHARE', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_app_open_invite_public_link_discover_episode = Metric('link_receiver_new_user_app_open_invite_public_link_discover_episode', 'Receiver-side app open new user from invite PUBLIC_LINK_DISCOVER_EPISODE', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_app_open_invite_public_link_discover_profile = Metric('link_receiver_new_user_app_open_invite_public_link_discover_profile', 'Receiver-side app open new user from invite PUBLIC_LINK_DISCOVER_PROFILE', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_app_open_invite_public_link_lens = Metric('link_receiver_new_user_app_open_invite_public_link_lens', 'Receiver-side app open new user from invite PUBLIC_LINK_LENS', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_app_open_invite_public_link_map_story = Metric('link_receiver_new_user_app_open_invite_public_link_map_story', 'Receiver-side app open new user from invite PUBLIC_LINK_MAP_STORY', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_app_open_invite_public_link_public_profile = Metric('link_receiver_new_user_app_open_invite_public_link_public_profile', 'Receiver-side app open new user from invite PUBLIC_LINK_PUBLIC_PROFILE', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_app_open_invite_public_link_public_profile_snap = Metric('link_receiver_new_user_app_open_invite_public_link_public_profile_snap', 'Receiver-side app open new user from invite PUBLIC_LINK_PUBLIC_PROFILE_SNAP', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_app_open_invite_public_link_spotlight_snap = Metric('link_receiver_new_user_app_open_invite_public_link_spotlight_snap', 'Receiver-side app open new user from invite PUBLIC_LINK_SPOTLIGHT_SNAP', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_app_open_invite_public_link_user_profile = Metric('link_receiver_new_user_app_open_invite_public_link_user_profile', 'Receiver-side app open new user from invite PUBLIC_LINK_USER_PROFILE', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_app_open_invite_snap_anyone_invite = Metric('link_receiver_new_user_app_open_invite_snap_anyone_invite', 'Receiver-side app open new user from invite SNAP_ANYONE_INVITE', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_app_open_invite_registration_contacts = Metric('link_receiver_new_user_app_open_invite_registration_contacts', 'Receiver-side app open new user from invite REGISTRATION_CONTACTS', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_app_open_invite_friend_invite_registration = Metric('link_receiver_new_user_app_open_invite_friend_invite_registration', 'Receiver-side app open new user from invite FRIEND_INVITE_REGISTRATION', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_app_open_invite_friend_invite_add_friends = Metric('link_receiver_new_user_app_open_invite_friend_invite_add_friends', 'Receiver-side app open new user from invite FRIEND_INVITE_ADD_FRIENDS', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_app_open_invite_friend_invite_universal_search = Metric('link_receiver_new_user_app_open_invite_friend_invite_universal_search', 'Receiver-side app open new user from invite FRIEND_INVITE_UNIVERSAL_SEARCH', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_app_open_invite_friend_invite_all_contacts = Metric('link_receiver_new_user_app_open_invite_friend_invite_all_contacts', 'Receiver-side app open new user from invite FRIEND_INVITE_ALL_CONTACTS', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_app_open_invite_friend_invite_friend_feed = Metric('link_receiver_new_user_app_open_invite_friend_invite_friend_feed', 'Receiver-side app open new user from invite FRIEND_INVITE_FRIEND_FEED', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_app_open_invite_friend_invite_invite_friends_page = Metric('link_receiver_new_user_app_open_invite_friend_invite_invite_friends_page', 'Receiver-side app open new user from invite FRIEND_INVITE_INVITE_FRIENDS_PAGE', dist='cont', daily=True, cumulative=True,)
    link_receiver_new_user_app_open_invite_friend_invite_send_to = Metric('link_receiver_new_user_app_open_invite_friend_invite_send_to', 'Receiver-side app open new user from invite FRIEND_INVITE_SEND_TO', dist='cont', daily=True, cumulative=True,)

    link_receiver_inactive_user_app_open = Metric('link_receiver_inactive_user_app_open', 'Receiver-side app open inactive user', dist='cont', daily=True, cumulative=True,)
    # link_receiver_inactive_user_app_open_nonsender = Metric('link_receiver_inactive_user_app_open_nonsender', 'Receiver-side app open inactive user (non-sender)', dist='cont', daily=True, cumulative=True,)
    link_receiver_inactive_user_add_friend_link_app_open = Metric('link_receiver_inactive_user_add_friend_link_app_open', 'Receiver-side inactive user app open with ADD_FRIEND link', dist='cont', daily=True, cumulative=True)
    link_receiver_inactive_user_commerce_link_app_open = Metric('link_receiver_inactive_user_commerce_link_app_open', 'Receiver-side inactive user app open with COMMERCE link', dist='cont', daily=True, cumulative=True)
    link_receiver_inactive_user_discover_link_app_open = Metric('link_receiver_inactive_user_discover_link_app_open', 'Receiver-side inactive user app open with DISCOVER link', dist='cont', daily=True, cumulative=True)
    link_receiver_inactive_user_lens_link_app_open = Metric('link_receiver_inactive_user_lens_link_app_open', 'Receiver-side inactive user app open with LENS link', dist='cont', daily=True, cumulative=True)
    link_receiver_inactive_user_map_link_app_open = Metric('link_receiver_inactive_user_map_link_app_open', 'Receiver-side inactive user app open with MAP link', dist='cont', daily=True, cumulative=True)
    link_receiver_inactive_user_our_story_link_app_open = Metric('link_receiver_inactive_user_our_story_link_app_open', 'Receiver-side inactive user app open with OUR_STORY link', dist='cont', daily=True, cumulative=True)
    link_receiver_inactive_user_profile_link_app_open = Metric('link_receiver_inactive_user_profile_link_app_open', 'Receiver-side inactive user app open with PROFILE link', dist='cont', daily=True, cumulative=True)
    link_receiver_inactive_user_public_user_story_link_app_open = Metric('link_receiver_inactive_user_public_user_story_link_app_open', 'Receiver-side inactive user app open with PUBLIC_USER_STORY link', dist='cont', daily=True, cumulative=True)
    link_receiver_inactive_user_snap_pro_link_app_open = Metric('link_receiver_inactive_user_snap_pro_link_app_open', 'Receiver-side inactive user app open with SNAP_PRO link', dist='cont', daily=True, cumulative=True)
    link_receiver_inactive_user_memories_link_app_open = Metric('link_receiver_inactive_user_memories_link_app_open', 'Receiver-side inactive user app open with MEMORIES_LINK link', dist='cont', daily=True, cumulative=True)

    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', concat('2', _table_suffix)) as ts,
        ops_ghost_user_id AS ghost_user_id,

        COUNT(DISTINCT ghost_user_id) AS link_receiver_app_open,
        -- COUNT(DISTINCT if(ops_ghost_user_id != ghost_user_id, ghost_user_id, null)) AS link_receiver_app_open_nonsender,
        COUNT(DISTINCT IF(ops_deep_link_source LIKE '%ADD_FRIEND%', ghost_user_id, NULL)) AS link_receiver_add_friend_link_app_open,
        COUNT(DISTINCT IF(ops_deep_link_source LIKE '%COMMERCE%', ghost_user_id, NULL)) AS link_receiver_commerce_link_app_open,
        COUNT(DISTINCT IF(ops_deep_link_source LIKE '%DISCOVER%', ghost_user_id, NULL)) AS link_receiver_discover_link_app_open,
        COUNT(DISTINCT IF(ops_deep_link_source LIKE '%LENSES%', ghost_user_id, NULL)) AS link_receiver_lens_link_app_open,
        COUNT(DISTINCT IF(ops_deep_link_source LIKE '%MAP%', ghost_user_id, NULL)) AS link_receiver_map_link_app_open,
        COUNT(DISTINCT IF(ops_deep_link_source LIKE '%OUR_STORY%', ghost_user_id, NULL)) AS link_receiver_our_story_link_app_open,
        COUNT(DISTINCT IF(ops_deep_link_source LIKE '%PROFILE%', ghost_user_id, NULL)) AS link_receiver_profile_link_app_open,
        COUNT(DISTINCT IF(ops_deep_link_source LIKE '%PUBLIC_USER_STORY%', ghost_user_id, NULL)) AS link_receiver_public_user_story_link_app_open,
        COUNT(DISTINCT IF(ops_deep_link_source LIKE '%SNAP_PRO%', ghost_user_id, NULL)) AS link_receiver_snap_pro_link_app_open,
        COUNT(DISTINCT IF(ops_deep_link_source LIKE '%MEMORIES_LINK%' OR ops_deep_link_source LIKE '%MEMORIES%', ghost_user_id, NULL)) AS link_receiver_memories_link_app_open,

        COUNT(DISTINCT if(new_user >= 1, ghost_user_id, NULL)) AS link_receiver_new_user_app_open,
        COUNT(DISTINCT if(new_user >= 1 and ops_join_key='phone_hash', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_phone_hash_attrib,
        COUNT(DISTINCT if(new_user >= 1 and ops_join_key='share_id', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_share_id_attrib,
        COUNT(DISTINCT if(new_user >= 1 and ops_join_key='deep_link_url', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_deep_link_url_attrib,
        COUNT(DISTINCT if(new_user >= 1 and ops_join_key='deep_link_url (share_id)', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_deep_link_url_share_id_attrib,
        COUNT(DISTINCT if(new_user >= 1 and ops_join_key='short_url', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_short_link_url_attrib,
        SUM(if(new_user >= 1, coalesce(friend_count, 0), 0)) / SUM(1) AS link_receiver_new_user_friend_count,
        SUM(if(new_user >= 1 and is_send_user_link_open_user,coalesce(friend_request, 0),0)) / SUM(1) AS link_receiver_new_user_friend_request_count,
        SUM(if(new_user >= 1 and is_reciprocate_user_link_open_user,coalesce(friend_reciprocate, 0),0)) / SUM(1) AS link_receiver_new_user_friend_reciprocate_count,

        -- COUNT(DISTINCT if(new_user >= 1 and ops_ghost_user_id != ghost_user_id, ghost_user_id, NULL)) AS link_receiver_new_user_app_open_nonsender,
        -- COUNT(DISTINCT if(new_user >= 1 and ops_ghost_user_id != ghost_user_id and ops_join_key='phone_hash', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_nonsender_phone_hash_attrib,
        -- COUNT(DISTINCT if(new_user >= 1 and ops_ghost_user_id != ghost_user_id and ops_join_key='share_id', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_nonsender_share_id_attrib,
        -- COUNT(DISTINCT if(new_user >= 1 and ops_ghost_user_id != ghost_user_id and ops_join_key='deep_link_url', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_nonsender_deep_link_url_attrib,
        -- COUNT(DISTINCT if(new_user >= 1 and ops_ghost_user_id != ghost_user_id and ops_join_key='deep_link_url (share_id)', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_nonsender_deep_link_url_share_id_attrib,
        -- COUNT(DISTINCT if(new_user >= 1 and ops_ghost_user_id != ghost_user_id and ops_join_key='short_url', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_nonsender_short_link_url_attrib,

        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature is not null, ghost_user_id, NULL)) AS link_receiver_new_user_app_open_invite,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'MEMORIES_LINK_SHARE', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_invite_memories_link_share,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'PUBLIC_LINK_DISCOVER_EPISODE', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_invite_public_link_discover_episode,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'PUBLIC_LINK_DISCOVER_PROFILE', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_invite_public_link_discover_profile,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'PUBLIC_LINK_LENS', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_invite_public_link_lens,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'PUBLIC_LINK_MAP_STORY', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_invite_public_link_map_story,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'PUBLIC_LINK_PUBLIC_PROFILE', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_invite_public_link_public_profile,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'PUBLIC_LINK_PUBLIC_PROFILE_SNAP', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_invite_public_link_public_profile_snap,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'PUBLIC_LINK_SPOTLIGHT_SNAP', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_invite_public_link_spotlight_snap,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'PUBLIC_LINK_USER_PROFILE', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_invite_public_link_user_profile,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'SNAP_ANYONE_INVITE', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_invite_snap_anyone_invite,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'REGISTRATION_CONTACTS', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_invite_registration_contacts,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'FRIEND_INVITE_REGISTRATION', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_invite_friend_invite_registration,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'FRIEND_INVITE_ADD_FRIENDS', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_invite_friend_invite_add_friends,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'FRIEND_INVITE_UNIVERSAL_SEARCH', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_invite_friend_invite_universal_search,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'FRIEND_INVITE_ALL_CONTACTS', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_invite_friend_invite_all_contacts,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'FRIEND_INVITE_FRIEND_FEED', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_invite_friend_invite_friend_feed,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'FRIEND_INVITE_INVITE_FRIENDS_PAGE', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_invite_friend_invite_invite_friends_page,
        COUNT(DISTINCT if(new_user >= 1 and ops_server_feature = 'FRIEND_INVITE_SEND_TO', ghost_user_id, NULL)) AS link_receiver_new_user_app_open_invite_friend_invite_send_to,

        COUNT(DISTINCT if(inactive_day >= 1, ghost_user_id, NULL)) AS link_receiver_inactive_user_app_open,
        -- COUNT(DISTINCT if(inactive_day >= 1 and ops_ghost_user_id != ghost_user_id, ghost_user_id, NULL)) AS link_receiver_inactive_user_app_open_nonsender,
        COUNT(DISTINCT IF(inactive_day >= 1 AND ops_deep_link_source LIKE '%ADD_FRIEND%', ghost_user_id, NULL)) AS link_receiver_inactive_user_add_friend_link_app_open,
        COUNT(DISTINCT IF(inactive_day >= 1 AND ops_deep_link_source LIKE '%COMMERCE%', ghost_user_id, NULL)) AS link_receiver_inactive_user_commerce_link_app_open,
        COUNT(DISTINCT IF(inactive_day >= 1 AND ops_deep_link_source LIKE '%DISCOVER%', ghost_user_id, NULL)) AS link_receiver_inactive_user_discover_link_app_open,
        COUNT(DISTINCT IF(inactive_day >= 1 AND ops_deep_link_source LIKE '%LENSES%', ghost_user_id, NULL)) AS link_receiver_inactive_user_lens_link_app_open,
        COUNT(DISTINCT IF(inactive_day >= 1 AND ops_deep_link_source LIKE '%MAP%', ghost_user_id, NULL)) AS link_receiver_inactive_user_map_link_app_open,
        COUNT(DISTINCT IF(inactive_day >= 1 AND ops_deep_link_source LIKE '%OUR_STORY%', ghost_user_id, NULL)) AS link_receiver_inactive_user_our_story_link_app_open,
        COUNT(DISTINCT IF(inactive_day >= 1 AND ops_deep_link_source LIKE '%PROFILE%', ghost_user_id, NULL)) AS link_receiver_inactive_user_profile_link_app_open,
        COUNT(DISTINCT IF(inactive_day >= 1 AND ops_deep_link_source LIKE '%PUBLIC_USER_STORY%', ghost_user_id, NULL)) AS link_receiver_inactive_user_public_user_story_link_app_open,
        COUNT(DISTINCT IF(inactive_day >= 1 AND ops_deep_link_source LIKE '%SNAP_PRO%', ghost_user_id, NULL)) AS link_receiver_inactive_user_snap_pro_link_app_open,
        COUNT(DISTINCT IF(inactive_day >= 1 AND (ops_deep_link_source LIKE '%MEMORIES_LINK%' OR ops_deep_link_source LIKE '%MEMORIES%'), ghost_user_id, NULL)) AS link_receiver_inactive_user_memories_link_app_open,

        FROM {source_table}
        and ops_ghost_user_id is not null
        and ops_join_key not in ('deep_link_url')
        and event_time > ops_event_time
        and ghost_user_id != ops_ghost_user_id
        GROUP BY 1,2
        """.format(source_table=source_table),

        metrics=[
        link_receiver_app_open,
        # link_receiver_app_open_nonsender,
        link_receiver_add_friend_link_app_open,
        link_receiver_commerce_link_app_open,
        link_receiver_discover_link_app_open,
        link_receiver_lens_link_app_open,
        link_receiver_map_link_app_open,
        link_receiver_our_story_link_app_open,
        link_receiver_profile_link_app_open,
        link_receiver_public_user_story_link_app_open,
        link_receiver_snap_pro_link_app_open,
        link_receiver_memories_link_app_open,

        link_receiver_new_user_app_open,
        link_receiver_new_user_app_open_phone_hash_attrib,
        link_receiver_new_user_app_open_share_id_attrib,
        link_receiver_new_user_app_open_deep_link_url_attrib,
        link_receiver_new_user_app_open_deep_link_url_share_id_attrib,
        link_receiver_new_user_app_open_short_link_url_attrib,
        link_receiver_new_user_friend_count,
        link_receiver_new_user_friend_request_count,
        link_receiver_new_user_friend_reciprocate_count,
        
        # link_receiver_new_user_app_open_nonsender,
        # link_receiver_new_user_app_open_nonsender_phone_hash_attrib,
        # link_receiver_new_user_app_open_nonsender_share_id_attrib,
        # link_receiver_new_user_app_open_nonsender_deep_link_url_attrib,
        # link_receiver_new_user_app_open_nonsender_deep_link_url_share_id_attrib,
        # link_receiver_new_user_app_open_nonsender_short_link_url_attrib,

        link_receiver_new_user_app_open_invite,
        link_receiver_new_user_app_open_invite_memories_link_share,
        link_receiver_new_user_app_open_invite_public_link_discover_episode,
        link_receiver_new_user_app_open_invite_public_link_discover_profile,
        link_receiver_new_user_app_open_invite_public_link_lens,
        link_receiver_new_user_app_open_invite_public_link_map_story,
        link_receiver_new_user_app_open_invite_public_link_public_profile,
        link_receiver_new_user_app_open_invite_public_link_public_profile_snap,
        link_receiver_new_user_app_open_invite_public_link_spotlight_snap,
        link_receiver_new_user_app_open_invite_public_link_user_profile,
        link_receiver_new_user_app_open_invite_snap_anyone_invite,
        link_receiver_new_user_app_open_invite_registration_contacts,
        link_receiver_new_user_app_open_invite_friend_invite_registration,
        link_receiver_new_user_app_open_invite_friend_invite_add_friends,
        link_receiver_new_user_app_open_invite_friend_invite_universal_search,
        link_receiver_new_user_app_open_invite_friend_invite_all_contacts,
        link_receiver_new_user_app_open_invite_friend_invite_friend_feed,
        link_receiver_new_user_app_open_invite_friend_invite_invite_friends_page,
        link_receiver_new_user_app_open_invite_friend_invite_send_to,        

        link_receiver_inactive_user_app_open,
        # link_receiver_inactive_user_app_open_nonsender
        link_receiver_inactive_user_add_friend_link_app_open,
        link_receiver_inactive_user_commerce_link_app_open,
        link_receiver_inactive_user_discover_link_app_open,
        link_receiver_inactive_user_lens_link_app_open,
        link_receiver_inactive_user_map_link_app_open,
        link_receiver_inactive_user_our_story_link_app_open,
        link_receiver_inactive_user_profile_link_app_open,
        link_receiver_inactive_user_public_user_story_link_app_open,
        link_receiver_inactive_user_snap_pro_link_app_open,
        link_receiver_inactive_user_memories_link_app_open,
        ],

        name="link_receiver_link_app_open",
        bq_dialect="standard"
    )
    return mt

def link_receiver_deep_link_lifecycle(start_date, end_date):
    """
    Receiver-side deep link lifecycle metrics
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    source_table = """
    `sc-analytics.report_growth.app_application_external_deep_link_lifecycle_2*`
    WHERE concat('2', _table_suffix) between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    link_receiver_app_application_deep_link = Metric('link_receiver_app_application_deep_link', 'link_receiver_app_application_deep_link', dist='cont', daily=True, cumulative=True)
    link_receiver_app_application_deep_link_error = Metric('link_receiver_app_application_deep_link_error', 'link_receiver_app_application_deep_link_error', dist='cont', daily=True, cumulative=True, desired_direction=NEGATIVE)

    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', concat('2', _table_suffix)) as ts,
        ops_ghost_user_id as ghost_user_id,
        sum(app_application_deep_link) as link_receiver_app_application_deep_link,
        sum(app_application_deep_link_error) as link_receiver_app_application_deep_link_error,

        FROM {source_table}
        GROUP BY 1,2
        """.format(source_table=source_table),

        metrics = [
            link_receiver_app_application_deep_link,
            link_receiver_app_application_deep_link_error,
        ],

        name="link_receiver_deep_link_lifecycle",
        bq_dialect="standard"
    )
    return mt

def link_receiver_link_web_page_view(start_date, end_date):
    """
    Off-platform share receiver-side metrics on consumer web
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    source_table = """
    `content-platform-gcp.report_web_sharing.web_sharing_daily_page_view_events_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    link_receiver_web_page_view = Metric('link_receiver_web_page_view', 'Receiver-side web page viewer', dist='cont', daily=True, cumulative=True,)
    link_receiver_add_friend_link_web_page_view = Metric('link_receiver_add_friend_link_web_page_view', 'Receiver-side web page viewer with ADD_FRIEND link', dist='cont', daily=True, cumulative=True,)
    link_receiver_commerce_link_web_page_view = Metric('link_receiver_commerce_link_web_page_view', 'Receiver-side web page viewer with COMMERCE link', dist='cont', daily=True, cumulative=True,)
    link_receiver_discover_link_web_page_view = Metric('link_receiver_discover_link_web_page_view', 'Receiver-side web page viewer with DISCOVER link', dist='cont', daily=True, cumulative=True,)
    link_receiver_lenses_link_web_page_view = Metric('link_receiver_lenses_link_web_page_view', 'Receiver-side web page viewer with LENSES link', dist='cont', daily=True, cumulative=True,)
    link_receiver_map_link_web_page_view = Metric('link_receiver_map_link_web_page_view', 'Receiver-side web page viewer with MAP link', dist='cont', daily=True, cumulative=True,)
    link_receiver_our_story_link_web_page_view = Metric('link_receiver_our_story_link_web_page_view', 'Receiver-side web page viewer with OUR_STORY link', dist='cont', daily=True, cumulative=True,)
    link_receiver_profile_link_web_page_view = Metric('link_receiver_profile_link_web_page_view', 'Receiver-side web page viewer with PROFILE link', dist='cont', daily=True, cumulative=True,)
    link_receiver_public_user_story_link_web_page_view = Metric('link_receiver_public_user_story_link_web_page_view', 'Receiver-side web page viewer with PUBLIC_USER_STORY link', dist='cont', daily=True, cumulative=True,)
    link_receiver_snap_pro_link_web_page_view = Metric('link_receiver_snap_pro_link_web_page_view', 'Receiver-side web page viewer with SNAP_PRO link', dist='cont', daily=True, cumulative=True,)
    link_receiver_memories_link_link_web_page_view = Metric('link_receiver_memories_link_link_web_page_view', 'Receiver-side web page viewer with MEMORIES_LINK link', dist='cont', daily=True, cumulative=True,)
    link_receiver_web_page_view_share_id_attrib = Metric('link_receiver_web_page_view_share_id_attrib', 'Receiver-side web page viewer (share_id attributed)', dist='cont', daily=True, cumulative=True,)
    link_receiver_web_page_view_canonical_url_attrib = Metric('link_receiver_web_page_view_canonical_url_attrib', 'Receiver-side web page viewer (canonical_url attributed)', dist='cont', daily=True, cumulative=True,)
    
    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        ops_ghost_user_id AS ghost_user_id,

        COUNT(DISTINCT web_client_id_hash) AS link_receiver_web_page_view,
        COUNT(DISTINCT IF(ops_deep_link_source LIKE '%ADD_FRIEND%', web_client_id_hash, NULL)) AS link_receiver_add_friend_link_web_page_view,
        COUNT(DISTINCT IF(ops_deep_link_source LIKE '%COMMERCE%', web_client_id_hash, NULL)) AS link_receiver_commerce_link_web_page_view,
        COUNT(DISTINCT IF(ops_deep_link_source LIKE '%DISCOVER%', web_client_id_hash, NULL)) AS link_receiver_discover_link_web_page_view,
        COUNT(DISTINCT IF(ops_deep_link_source LIKE '%LENSES%', web_client_id_hash, NULL)) AS link_receiver_lenses_link_web_page_view,
        COUNT(DISTINCT IF(ops_deep_link_source LIKE '%MAP%', web_client_id_hash, NULL)) AS link_receiver_map_link_web_page_view,
        COUNT(DISTINCT IF(ops_deep_link_source LIKE '%OUR_STORY%', web_client_id_hash, NULL)) AS link_receiver_our_story_link_web_page_view,
        COUNT(DISTINCT IF(ops_deep_link_source LIKE '%PROFILE%', web_client_id_hash, NULL)) AS link_receiver_profile_link_web_page_view,
        COUNT(DISTINCT IF(ops_deep_link_source LIKE '%PUBLIC_USER_STORY%', web_client_id_hash, NULL)) AS link_receiver_public_user_story_link_web_page_view,
        COUNT(DISTINCT IF(ops_deep_link_source LIKE '%SNAP_PRO%', web_client_id_hash, NULL)) AS link_receiver_snap_pro_link_web_page_view,
        COUNT(DISTINCT IF(ops_deep_link_source LIKE '%MEMORIES_LINK%' OR ops_deep_link_source LIKE '%MEMORIES%', web_client_id_hash, NULL)) AS link_receiver_memories_link_link_web_page_view,
        COUNT(DISTINCT IF(share_id is not null, web_client_id_hash, NULL)) AS link_receiver_web_page_view_share_id_attrib,        
        COUNT(DISTINCT IF(share_id is null, web_client_id_hash, NULL)) AS link_receiver_web_page_view_canonical_url_attrib,
        
        FROM {source_table}
        and ops_ghost_user_id is not null
        and (ghost_user_id != ops_ghost_user_id or ghost_user_id is null)
        GROUP BY
        1,2
        """.format(source_table=source_table),
        metrics=[
        link_receiver_web_page_view,
        link_receiver_add_friend_link_web_page_view,
        link_receiver_commerce_link_web_page_view,
        link_receiver_discover_link_web_page_view,
        link_receiver_lenses_link_web_page_view,
        link_receiver_map_link_web_page_view,
        link_receiver_our_story_link_web_page_view,
        link_receiver_profile_link_web_page_view,
        link_receiver_public_user_story_link_web_page_view,
        link_receiver_snap_pro_link_web_page_view,
        link_receiver_memories_link_link_web_page_view,
        link_receiver_web_page_view_share_id_attrib,
        link_receiver_web_page_view_canonical_url_attrib
                ],
        name="link_receiver_link_web_page_view",
        bq_dialect="standard"
    )
    return mt

def dnu_from_ops_ip(start_date, end_date):
    """
    DNU from OPS (IP attribution)
    https://docs.google.com/document/d/1Dx0i5MAyat_ow__0L-xmsOQ1R1yZxqdXiFv757r-RtU/edit#
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    dnu_from_ops_ip = Metric('dnu_from_ops_ip', 'Receiver-side app open new user (IP)', dist='cont', daily=True, cumulative=True,)
    dnu_from_ops_ip_det = Metric('dnu_from_ops_ip_det', 'Receiver-side app open new user (IP deterministic)', dist='cont', daily=True, cumulative=True,)
    
    mt = MetricTable(
        sql="""
        with 
        ops as (
        select distinct
        PARSE_TIMESTAMP('%Y%m%d', concat('2', _table_suffix)) as event_date,
        ghost_user_id,
        share_id,
        FROM `sc-analytics.report_growth.off_platform_share_2*`
        WHERE PARSE_DATE('%Y%m%d', concat('2', _TABLE_SUFFIX)) BETWEEN PARSE_DATE('%Y%m%d', '{start}') and PARSE_DATE('%Y%m%d', '{end}')
        ),

        dnu_from_ops_ip as (
        SELECT distinct
        event_time_reg,
        ghost_user_id_reg,
        share_id_wpv, 
        n_ghost_user_id_reg_ip,
        n_web_client_id_wpv_ip,
        os_type_reg,
        os_type_wpv,
        FROM `content-platform-gcp.report_web_sharing.dnu_from_web_ip_daily_2*`
        WHERE PARSE_DATE('%Y%m%d', concat('2', _TABLE_SUFFIX)) BETWEEN PARSE_DATE('%Y%m%d', '{start}') and PARSE_DATE('%Y%m%d', '{end}')
        and ghost_user_id_reg is not null
        and ghost_user_id_aao is not null
        and web_client_id_wpv is not null
        and share_id_wpv is not null
        )

        select distinct
        o.event_date as ts,
        o.ghost_user_id,
        count(distinct d.ghost_user_id_reg) as dnu_from_ops_ip,
        count(distinct if(n_ghost_user_id_reg_ip = 1 and n_web_client_id_wpv_ip = 1 and ((lower(os_type_wpv) = lower(os_type_reg)) or (lower(os_type_wpv) not in ('ios', 'android') and lower(os_type_reg) in ('ios', 'android'))), d.ghost_user_id_reg, NULL)) as dnu_from_ops_ip_det,
        from ops o
        left join dnu_from_ops_ip d
        on o.share_id = d.share_id_wpv
        group by 1, 2        
        """.format(start=start_date, end=end_date),

        metrics=[
        dnu_from_ops_ip,
        dnu_from_ops_ip_det
        ],

        name="dnu_from_ops_ip",
        bq_dialect="standard"
    )
    return mt

def indirect_org_dnu_attrib_1d(start_date, end_date):
    """
    Organic DNUs who can be indirectly attributed to off-platform share senders within 1 day
    """

    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')
    source_table = """
    `sc-analytics.report_growth.indirect_org_dnu_attrib_1d_*`
    WHERE _table_suffix between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )

    fr_org_dnu = Metric('fr_org_dnu', 'Indirect organic DNU from friending signal', dist='cont', daily=True, cumulative=True,)
    fr_org_dnu_only_frnd_act = Metric('fr_org_dnu_only_frnd_act', 'Indirect organic DNU from friending signal (only_frnd_act)', dist='cont', daily=True, cumulative=True,)
    comms_org_dnu = Metric('comms_org_dnu', 'Indirect organic DNU from communication signal', dist='cont', daily=True, cumulative=True,)
    comms_org_dnu_only_comms_act = Metric('comms_org_dnu_only_comms_act', 'Indirect organic DNU from communication signal (only_comms_act)', dist='cont', daily=True, cumulative=True,)
    comms_org_dnu_first_comms_act = Metric('comms_org_dnu_first_comms_act', 'Indirect organic DNU from communication signal (first_comms_act)', dist='cont', daily=True, cumulative=True,)
    comms_org_dnu_most_comms_act = Metric('comms_org_dnu_most_comms_act', 'Indirect organic DNU from communication signal (most_comms_act)', dist='cont', daily=True, cumulative=True,)
    # contact_org_dnu = Metric('contact_org_dnu', 'Indirect organic DNU from contact book signal', dist='cont', daily=True, cumulative=True,)
    # contact_org_dnu_dnu_in_ops_contact = Metric('contact_org_dnu_dnu_in_ops_contact', 'Indirect organic DNU from contact book signal (dnu_in_ops_contact)', dist='cont', daily=True, cumulative=True,)
    # contact_org_dnu_ops_in_dnu_contact = Metric('contact_org_dnu_ops_in_dnu_contact', 'Indirect organic DNU from contact book signal (ops_in_dnu_contact)', dist='cont', daily=True, cumulative=True,)
    # contact_org_dnu_bidirect_contact = Metric('contact_org_dnu_bidirect_contact', 'Indirect organic DNU from contact book signal (bidirect_contact)', dist='cont', daily=True, cumulative=True,)

    mt = MetricTable(
        sql="""
        SELECT
        PARSE_TIMESTAMP('%Y%m%d', _table_suffix) as ts,
        COALESCE(fr_ops_ghost_user_id, comms_ops_ghost_user_id) AS ghost_user_id,
        COUNT(DISTINCT IF(is_fr_org_dnu, all_dnu_ghost_user_id, NULL)) AS fr_org_dnu,
        COUNT(DISTINCT IF(is_fr_org_dnu_only_frnd_act, all_dnu_ghost_user_id, NULL)) AS fr_org_dnu_only_frnd_act,
        COUNT(DISTINCT IF(is_comms_org_dnu, all_dnu_ghost_user_id, NULL)) AS comms_org_dnu,
        COUNT(DISTINCT IF(is_comms_org_dnu_only_comms_act, all_dnu_ghost_user_id, NULL)) AS comms_org_dnu_only_comms_act,
        COUNT(DISTINCT IF(is_comms_org_dnu_first_comms_act, all_dnu_ghost_user_id, NULL)) AS comms_org_dnu_first_comms_act,
        COUNT(DISTINCT IF(is_comms_org_dnu_most_comms_act, all_dnu_ghost_user_id, NULL)) AS comms_org_dnu_most_comms_act,
        -- COUNT(DISTINCT IF(is_contact_org_dnu, all_dnu_ghost_user_id, NULL)) AS contact_org_dnu,
        -- COUNT(DISTINCT IF(is_contact_org_dnu_dnu_in_ops_contact, all_dnu_ghost_user_id, NULL)) AS contact_org_dnu_dnu_in_ops_contact,
        -- COUNT(DISTINCT IF(is_contact_org_dnu_ops_in_dnu_contact, all_dnu_ghost_user_id, NULL)) AS contact_org_dnu_ops_in_dnu_contact,
        -- COUNT(DISTINCT IF(is_contact_org_dnu_bidirect_contact, all_dnu_ghost_user_id, NULL)) AS contact_org_dnu_bidirect_contact,
        FROM {source_table}
        and COALESCE(fr_ops_ghost_user_id, comms_ops_ghost_user_id) is not null
        GROUP BY 1,2
        """.format(source_table=source_table),

        metrics=[
                fr_org_dnu,
                fr_org_dnu_only_frnd_act,
                comms_org_dnu,
                comms_org_dnu_only_comms_act,
                comms_org_dnu_first_comms_act,
                comms_org_dnu_most_comms_act,
                # contact_org_dnu,
                # contact_org_dnu_dnu_in_ops_contact,
                # contact_org_dnu_ops_in_dnu_contact,
                # contact_org_dnu_bidirect_contact
                ],
        name="indirect_org_dnu_attrib_1d",
        bq_dialect="standard"
    )
    return mt

def dnu_correspondent_dnu(start_date, end_date):
    """
    DNU correspondent DNU
    """
    start_date = pd.to_datetime(start_date).strftime('%Y%m%d')
    end_date = pd.to_datetime(end_date).strftime('%Y%m%d')

    source_table_1 = """
    `sc-analytics.report_app.dau_user_country_2*`
    WHERE concat('2',_TABLE_SUFFIX) between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )
    source_table_2 = """
    `sc-analytics.report_growth.app_application_external_deep_link_user_level_2*`
    WHERE concat('2',_TABLE_SUFFIX) between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )
    source_table_3 = """
    `sc-analytics.report_growth.friend_adds_2*`
    WHERE concat('2',_TABLE_SUFFIX) between '{start}' and '{end}'
    """.format(
        start=start_date,
        end=end_date,
    )
    REGISTRATION_USER_SUCCESS_UU = Metric( 'REGISTRATION_USER_SUCCESS_UU',   'Registration User Success UU',  dist='cont',   daily=True, cumulative=True,  )
    dnu = Metric( 'dnu',   'DNU',  dist='cont',   daily=True, cumulative=True,  )
    link_correspondent_dnu = Metric( 'link_correspondent_dnu',   'Receiver Side DNU with link open',  dist='cont',   daily=True, cumulative=True,  )
    friend_add_correspondent_dnu = Metric( 'friend_add_correspondent_dnu',   'Receiver side DNU with outgoing/incoming friend action',  dist='cont',   daily=True, cumulative=True,  )
    correspondent_dnu = Metric( 'correspondent_dnu',   'Receiver Side DNU (link OR friend action)',  dist='cont',   daily=True, cumulative=True,  )
    dnu_and_correspondent_dnu = Metric( 'dnu_and_correspondent_dnu',   'DNU + Receiver Side DNU',  dist='cont',   daily=True, cumulative=True,  )

    mt = MetricTable(
        sql="""
        WITH dau AS
        (
        select
        SAFE_CAST(null AS STRING)AS connection,
        TIMESTAMP(PARSE_DATE('%Y%m%d',concat('2',_TABLE_SUFFIX))) AS ts,
        ghost_user_id,
        APP_APPLICATION_OPEN_UU,
        REGISTRATION_USER_SUCCESS_UU,
        SAFE_CAST(null AS STRING)AS ghost_correspondent_id,
        from {source_table_1}),

        link_1 AS
        (
        select
        "link"AS connection,
        TIMESTAMP(PARSE_DATE('%Y%m%d',concat('2',_TABLE_SUFFIX))) AS ts,
        ops_ghost_user_id AS ghost_user_id,
        null AS APP_APPLICATION_OPEN_UU,
        null AS REGISTRATION_USER_SUCCESS_UU,
        ghost_user_id AS ghost_correspondent_id,
        from {source_table_2}),

        link_2 AS
        (
        select
        "link"AS connection,
        TIMESTAMP(PARSE_DATE('%Y%m%d',concat('2',_TABLE_SUFFIX))) AS ts,
        ghost_user_id AS ghost_user_id,
        null AS APP_APPLICATION_OPEN_UU,
        null AS REGISTRATION_USER_SUCCESS_UU,
        ops_ghost_user_id AS ghost_correspondent_id,
        from {source_table_2}),

        add_1 AS
        (select
        "friend_add"AS connection,
        TIMESTAMP(PARSE_DATE('%Y%m%d',concat('2',_TABLE_SUFFIX))) AS ts,
        ghost_user_id AS ghost_user_id,
        null AS APP_APPLICATION_OPEN_UU,
        null AS REGISTRATION_USER_SUCCESS_UU,
        ghost_to_user_id AS ghost_correspondent_id,
        from {source_table_3}),

        add_2 AS
        (select
        "friend_add"AS connection,
        TIMESTAMP(PARSE_DATE('%Y%m%d',concat('2',_TABLE_SUFFIX))) AS ts,
        ghost_to_user_id AS ghost_user_id,
        null AS APP_APPLICATION_OPEN_UU,
        null AS REGISTRATION_USER_SUCCESS_UU,
        ghost_user_id AS ghost_correspondent_id,
        from {source_table_3})


        select*,
        COALESCE(dnu,0)+COALESCE(correspondent_dnu,0) AS dnu_and_correspondent_dnu,
        from
        (select
        main_data.ts,
        main_data.ghost_user_id,

        max(if(main_data.REGISTRATION_USER_SUCCESS_UU=1,1,0)) AS REGISTRATION_USER_SUCCESS_UU,
        max(if(main_data.APP_APPLICATION_OPEN_UU=1 and main_data.REGISTRATION_USER_SUCCESS_UU=1,1,0)) AS dnu,

        count(distinct if(correspondent_dau.REGISTRATION_USER_SUCCESS_UU=1
        and main_data.connection="link"
        and main_data.ghost_correspondent_id!=main_data.ghost_user_id,main_data.ghost_correspondent_id,null))
        AS link_correspondent_dnu,

        count(distinct if(correspondent_dau.REGISTRATION_USER_SUCCESS_UU=1
        and main_data.connection="friend_add"
        and main_data.ghost_correspondent_id!=main_data.ghost_user_id,main_data.ghost_correspondent_id,null))
        AS friend_add_correspondent_dnu,

        count(distinct if(correspondent_dau.REGISTRATION_USER_SUCCESS_UU=1
        and main_data.ghost_correspondent_id!=main_data.ghost_user_id,main_data.ghost_correspondent_id,null))
        AS correspondent_dnu,
        from
        (select * from dau
        union all
        select * from link_1
        union all
        select * from link_2
        union all
        select * from add_1
        union all
        select * from add_2)main_data
        LEFT JOIN dau AS correspondent_dau
        ON main_data.ghost_correspondent_id=correspondent_dau.ghost_user_id
        and main_data.ts=correspondent_dau.ts
        GROUP BY 1,2)
        """.format(source_table_1=source_table_1,source_table_2=source_table_2,source_table_3=source_table_3),
        metrics=[
                REGISTRATION_USER_SUCCESS_UU,
                dnu,
                link_correspondent_dnu,
                friend_add_correspondent_dnu,
                correspondent_dnu,
                dnu_and_correspondent_dnu,
                ],
        name="dnu_correspondent_dnu",
        bq_dialect="standard"
    )
    return mt