# Shibainu

A modular framework for classification using various LLM providers.

## Overview

Shibainu is a flexible framework designed with a modular architecture to support:
- Multiple LLM providers (OpenAI, Gemini, and AGI)
- Unified interface for different input types (text, images, videos, mixed content)
- Flexible processor configuration for different content types
- GCS upload support for media processing
- Validation and visualization utilities
- Cost estimation and logging
- Built-in retry mechanism with provider reinitialization
- Pre-built prompt library with taxonomy support

## Architecture

The module follows a modular design pattern with these components:

- **Core** - Main classification interface with retry logic
- **Providers** - LLM provider implementations (OpenAI, Gemini, AGI)
- **Processors** - Input type processors (Text, Image, Video, Mixed)
- **Utilities** - Helper modules (Logger, Cost Estimator, Prompt Library)
- **Validation** - Tools for validating classification results
- **Visualization** - Tools for visualizing classification results

## Processor Configuration

Each processor type supports specific configuration options that can be set during Classification initialization:

### Image Processor

```python
processor_config = {
    "processing_mode": "image_url",  # "image_url" or "bytes"
    "return_direct_url": True,        # Return the same URL without changing it

}
```

### Video Processor

```python
processor_config = {
    "processing_mode": "image_url",  # "image_url" or "bytes"
    "return_direct_url": False,        # Upload frames to GCS and return URLs
    "download": True,              # Whether to download videos from URLs
    "sampling_mode": "fps",         # "fps", "interval", or "first_last"
    "sampling_value": 1.0,          # Frames per second or interval in seconds
    "max_frames": 20,              # Maximum frames to extract
    "image_quality": 75,           # Quality of processed frames
    "image_format": "JPEG"          # Format of processed frames
}
```

### Mixed Content Processor

Mixed content processor supports the same options as the Video processor, since it may need to handle any type of content.

## Usage Examples

### Text Classification

```python
from banjo.utils.shibainu import Classification, PromptLibrary, configure_logger
import logging

# Configure logging
configure_logger(level=logging.ERROR)

# Get a classification prompt from the prompt library
prompt = PromptLibrary.get_classification_prompt(
    categories=["positive", "negative", "neutral"]
)

# Initialize the classifier
classifier = Classification(
    provider_name="openai",
    model_name="gpt-4o-mini",
    input_type="text",
    provider_config={
        "service_account": "your-service-account",
    },
    model_parameters={
        "temperature": 0,
        "max_tokens": 128
    },
    prompt=prompt
)

# Classify text
text = "I love this product, it's amazing!"
result = classifier.classify(text)
label = classifier.get_result(result)
print(f"Classification result: {label}")
```

### Image Classification with OpenAI

```python
# Initialize for image classification with OpenAI
classifier = Classification(
    provider_name="openai",
    model_name="gpt-4o",
    input_type="image",
    provider_config={
        "service_account": "your-service-account",
    },
    processor_config={
        "processing_mode": "bytes",    # Use 'image_url' or 'bytes'
        "return_gcs_url": False,       # Set to True to upload to GCS
        "image_quality": 80,           # JPEG quality (1-100)
        "image_format": "JPEG"         # Image format (JPEG, PNG, etc.)
    },
    prompt="What is shown in this image? Classify as: person, animal, object, scene, or other."
)

# Classify an image
image_url = "https://example.com/image.jpg"
result = classifier.classify(image_url)
label = classifier.get_result(result)
print(f"Image classification: {label}")
```

### Image Classification with Gemini

```python
# Initialize for image classification with Gemini
classifier = Classification(
    provider_name="gemini",
    model_name="gemini-2.5-flash",
    input_type="image",
    provider_config={
        "project_id": "your-project-id",
        "location": "us-central1"
    },
    processor_config={
        "processing_mode": "bytes",
        "return_gcs_url": False
    },
    prompt="What is shown in this image? Classify as: person, animal, object, scene, or other."
)

# Classify an image
image_url = "https://example.com/image.jpg"
result = classifier.classify(image_url)
label = classifier.get_result(result)
print(f"Image classification: {label}")
```

### Video Classification

```python
# Initialize for video classification
classifier = Classification(
    provider_name="openai",
    model_name="gpt-4o",
    input_type="video",
    provider_config={
        "service_account": "your-service-account",
    },
    processor_config={
        "processing_mode": "bytes",    # Use 'image_url' or 'bytes'
        "return_gcs_url": False,       # Upload frames to GCS
        "download": True,              # Whether to download videos from URLs
        "sampling_mode": "fps",        # 'fps', 'interval', or 'first_last'
        "sampling_value": 1.0,         # Frames per second or interval in seconds
        "max_frames": 10,             # Maximum frames to extract
        "image_quality": 75            # Quality of saved frames
    },
    prompt="What is happening in this video? Classify as: sports, entertainment, news, education, or other."
)

# Classify a video
video_path = "path/to/video.mp4"
result = classifier.classify(video_path)
label = classifier.get_result(result)
print(f"Video classification: {label}")
```

### AGI Provider Classification

```python
# Initialize for text classification with AGI provider
classifier = Classification(
    provider_name="agi",
    model_name="Qwen3-30B-A3B-FP8",  # or other AGI models
    input_type="text",
    model_parameters={
        "max_tokens": 1000,
        "temperature": 0
    },
    prompt="Classify this text into appropriate categories"
)

# Classify text
text = "This is a sample text for classification"
result = classifier.classify(text)
label = classifier.get_result(result)
print(f"AGI classification: {label}")
```

### Using Taxonomy-Based Prompts

```python
from banjo.utils.shibainu import Classification, PromptLibrary

# Get categories from predefined taxonomies
# Available: "SCC" (Snapchat Content Categories), "GCC" (Google Content Categories), 
#           "S2I" (Snap to Interest), "LTC" (Lens Topic Cluster)
prompt = PromptLibrary.get_taxonomy_prompt(
    taxonomy_name="SCC",
    project_id="myaigcp"
)

# Initialize classifier with taxonomy prompt
classifier = Classification(
    provider_name="gemini",
    model_name="gemini-2.5-flash",
    input_type="text",
    provider_config={
        "project_id": "your-project-id",
        "location": "us-central1"
    },
    model_parameters={
        "temperature": 0,
        "max_tokens": 1280
    },
    prompt=prompt
)

result = classifier.classify("Sample content to classify")
label = classifier.get_result(result)
```

### Batch Processing with Cost Estimation

```python
from banjo.utils.shibainu import Classification, estimate_run_cost
from tqdm.contrib.concurrent import thread_map
import pandas as pd

# Estimate cost before running
data_samples = ["text1", "text2", "text3"]  # Your dataset
cost_estimate = estimate_run_cost(
    provider_name="gemini",
    model_name="gemini-2.5-flash",
    input_type="text",
    data_samples=data_samples,
    prompt="Your classification prompt"
)
print(f"Estimated cost: ${cost_estimate}")

# Initialize classifier
classifier = Classification(
    provider_name="gemini",
    model_name="gemini-2.5-flash",
    input_type="text",
    prompt="Classify this text"
)

# Process in parallel
def classify_single(text):
    result = classifier.classify(text)
    return {
        'text': text,
        'label': classifier.get_result(result),
        'completion_tokens': classifier.get_token_usage(result)['completion_tokens'],
        'prompt_tokens': classifier.get_token_usage(result)['prompt_tokens']
    }

# Run parallel processing
results = thread_map(classify_single, data_samples, max_workers=5)
df = pd.DataFrame(results)
print(df.head())
```

### Validation and Visualization

```python
from banjo.utils.shibainu import Validation, Visualization
import pandas as pd

# Assume you have a DataFrame with predictions and ground truth
df = pd.DataFrame({
    'text': ['sample1', 'sample2', 'sample3'],
    'label': ['positive', 'negative', 'neutral'],  # Model predictions
    'validation_label': ['positive', 'positive', 'neutral']  # Ground truth
})

# Initialize validation
validation = Validation(
    df=df,
    defined_category=['positive', 'negative', 'neutral'],
    label_col='label',
    validated_label_col='validation_label'
)

# Calculate metrics
accuracy = validation.calculate_accuracy()
precision = validation.calculate_precision()
recall = validation.calculate_recall()
f1 = validation.calculate_f1_score()

print(f"Accuracy: {accuracy:.3f}")
print(f"Precision: {precision:.3f}")
print(f"Recall: {recall:.3f}")
print(f"F1 Score: {f1:.3f}")

# Visualize results
visualization = Visualization(df, label_col='label')
visualization.plot_distribution()  # Plot label distribution
visualization.plot_confusion_matrix(validated_label_col='validation_label')
```

### Working with BigQuery Data

```python
from banjo import utils
from banjo.utils.shibainu import Classification, configure_logger
import logging
import pandas as pd

# Configure logging
configure_logger(level=logging.ERROR)

# Load data from BigQuery
sql = """
SELECT text_column, id_column
FROM `your-project.dataset.table`
WHERE LENGTH(text_column) > 10
LIMIT 1000
"""

data = utils.gbq.read_gbq(
    sql,
    project_id='myaigcp',
    dialect="standard",
    priority="INTERACTIVE"
)

# Initialize classifier
classifier = Classification(
    provider_name="gemini",
    model_name="gemini-2.5-flash",
    input_type="text",
    provider_config={
        "project_id": "your-project-id",
        "location": "us-central1"
    },
    prompt="Your classification prompt here"
)

# Classify the data
results = []
for text in data['text_column'].values:
    try:
        result = classifier.classify(text)
        results.append({
            'text': text,
            'label': classifier.get_result(result),
            'tokens': classifier.get_token_usage(result)
        })
    except Exception as e:
        print(f"Error processing text: {e}")
        results.append({
            'text': text,
            'label': 'error',
            'tokens': None
        })

# Convert to DataFrame for analysis
results_df = pd.DataFrame(results)
print(results_df['label'].value_counts())
```

### Parallel Processing with Retry Logic

```python
from concurrent.futures import ThreadPoolExecutor
from tqdm import tqdm
import time
from datetime import datetime

def process_image_with_retry(url, classification_instance, max_retries=3, delay=2):
    """Process an image URL with retry logic for handling timeouts."""
    for attempt in range(max_retries):
        try:
            # Use the classify method which handles the image processing internally
            result = classification_instance.classify(url)
            
            # Extract the text result
            text_result = classification_instance.get_result(result)
            
            # Get token usage information
            token_usage = classification_instance.get_token_usage(result)
            completion_tokens = token_usage.get("completion_tokens", 0)
            prompt_tokens = token_usage.get("prompt_tokens", 0)
            
            return (text_result, completion_tokens, prompt_tokens)
        
        except Exception as e:
            error_msg = str(e).lower()
            if any(err in error_msg for err in ["timeout", "invalid_image_url"]) and attempt < max_retries-1:
                print(f"Attempt {attempt+1} failed for {url}. Retrying in {delay} seconds...")
                time.sleep(delay)
                delay *= 1.5
            else:
                print(f"Failed to process image: {url} after {attempt+1} attempts")
                return ("image_failure", 0, 0)

# Process multiple images in parallel
def process_images_in_parallel(image_urls, classification_instance, max_workers=10):
    start_time = datetime.now()
    
    results = []
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = [executor.submit(process_image_with_retry, url, classification_instance) 
                  for url in image_urls]
        
        for future in tqdm(futures, total=len(futures), desc="Processing images"):
            results.append(future.result())
    
    print(f"Inference time: {datetime.now()-start_time}")
    return results
```

### Validation and Visualization

```python
import pandas as pd
from banjo.utils.shibainu import Classification, Validation, Visualization

# Assume we have a dataframe with classification results
df = pd.DataFrame({
    "text": ["Sample 1", "Sample 2", "Sample 3"],
    "label": ["positive", "negative", "neutral"],
    "validation_label": ["positive", "negative", "positive"]  # Ground truth
})

# Initialize validation
validation = Validation(
    df=df,
    defined_category=["positive", "negative", "neutral"],
    label_col="label",
    validated_label_col="validation_label"
)

# Get validation metrics
metrics = validation.get_validation_metrics()
print(f"Accuracy: {metrics['accuracy']}")

# Generate confusion matrix
validation.plot_confusion_matrix()

# Initialize visualization
viz = Visualization(data=df, data_type="text")

# Generate label distribution plot
viz.plot_label_counts(column_name="label", title="Label Distribution")

# Display sample data
samples = viz.display_samples(column_name="text")
```

### Cost Estimation

```python
from banjo.utils.shibainu import estimate_cost

# Get token usage from a classification result
token_usage = classifier.get_token_usage(result)

# Estimate cost
cost = estimate_cost(token_usage, "gpt-4o-mini")
print(f"Estimated cost: ${cost:.6f}")
```

## Extending the Framework

### Adding a New Provider

1. Create a new provider class in `providers/` directory:

```python
from .base import LLMProvider

class NewProvider(LLMProvider):
    def initialize(self, **kwargs):
        # Initialize your provider
        
    def process_text(self, text, **kwargs):
        # Implement text processing
        
    def process_image(self, image_data, **kwargs):
        # Implement image processing
        
    def process_mixed_content(self, content_blocks, **kwargs):
        # Implement mixed content processing
        
    def get_token_usage(self, response):
        # Extract token usage information
```

2. Register the provider in `providers/__init__.py`:

```python
from .new_provider import NewProvider

PROVIDERS = {
    "openai": OpenAIProvider,
    "new_provider": NewProvider,
}
```

### Adding a New Input Processor

1. Create a new processor class in `processors/` directory:

```python
from .base import InputProcessor

class NewProcessor(InputProcessor):
    def prepare(self, data, **kwargs):
        # Implement data preparation
        return prepared_data
```

2. Register the processor in `processors/__init__.py`:

```python
from .new_processor import NewProcessor

PROCESSORS = {
    "text": TextProcessor,
    "new_type": NewProcessor,
}
```

## Logging

The module uses a custom logger that can be configured:

```python
from banjo.utils.shibainu import logger
import logging

# Set log level
logger.setLevel(logging.DEBUG)

# Add log handlers for custom output
handler = logging.FileHandler("shibainu.log")
handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
logger.addHandler(handler)
