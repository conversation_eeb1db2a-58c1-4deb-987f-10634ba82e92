from openai import OpenAI
import lca
from oauth2client.client import GoogleCredentials
from .base import LL<PERSON>rovider


REASONING_MODELS = ["o1-mini", 'o3', 'o3-mini', 'o4-mini']

class OpenAIProvider(LLMProvider):
    """Provider implementation for OpenAI models."""
    
    def initialize(self, service_account, model_name, max_retries=2, **kwargs):
        """Initialize OpenAI provider with retry logic.
        
        Args:
            service_account: Service account for authentication
            model_name: Name of the model to use
            max_retries: Maximum number of retries on initialization failure
            **kwargs: Additional initialization parameters
        """
        self.service_account = service_account
        self.model_name = model_name
        self.max_retries = max_retries

        self.temperature = kwargs.get("temperature", 0)
        self.max_token = kwargs.get("max_token", 128)

        credentials = GoogleCredentials.get_application_default()
        issuer = lca.LcaIssuer(self.service_account, credentials)
        lca_token = issuer.get_lca_token(audience='ats.snap')

        self.client = OpenAI(
            api_key='dummy key',
            base_url="https://ingress-us-central1-gcp.api.snapchat.com/ats/v1/",
            default_headers={"X-ATS-INTEGRATION-ID": "openai-testing", "SC-LCA-1": lca_token}
        )

    def process(self, data, **kwargs):
        """Process data based on its type.
        
        Args:
            data: The data to process - should be already prepared by the appropriate processor
            **kwargs: Additional arguments including input_type and prompt
            
        Returns:
            Model response
        """
        input_type = kwargs.get("input_type", "text")
        prompt = kwargs.get("prompt", None)
        
        # Check if model is o1-mini, which only supports text
        if self.model_name in REASONING_MODELS and input_type != "text":
            raise ValueError(f"o1-mini model does not support {input_type} input")
            
        # Handle messages based on input type and data format
        messages = []
        
        # Add system prompt if available and supported
        if prompt:
            if self.model_name in REASONING_MODELS and input_type == "text":
                # o1-mini doesn't support system messages, combine with user message later
                pass
            else:
                messages.append({"role": "system", "content": prompt})
        
        # Process content based on input type and data format
        if input_type == "text":
            # For o1-mini, combine prompt with text
            if self.model_name in REASONING_MODELS and prompt:
                messages.append({"role": "user", "content": f"{prompt}\n\n{data}"})
            else:
                messages.append({"role": "user", "content": data})
        else:
            # For image, video, mixed content - data should be already prepared as content blocks
            # If data is a list, it's already in the right format
            if isinstance(data, list):
                messages.append({"role": "user", "content": data})
            else:
                # If it's a single content block (dict), wrap it in a list
                # OpenAI expects content to be a list of content blocks for non-text inputs
                messages.append({"role": "user", "content": [data]})
            
        return self.send_message(messages)
    
    def send_message(self, message):
        """Send a message to the model.
        
        Args:
            message: The message to send (string or list of message dicts)
            
        Returns:
            Model response
        """
        # Convert single string message to proper format
        if isinstance(message, str):
            message = [{"role": "user", "content": message}]
        
        # Handle the o1-mini model which has specific requirements
        if self.model_name in REASONING_MODELS:
            create_params = {
                "model": self.model_name,
                "messages": message
            }
        else:
            # Standard models can use all parameters
            create_params = {
                "model": self.model_name,
                "messages": message,
                "temperature": self.temperature,
                "max_tokens": self.max_token
            }
        
        completion = self.client.chat.completions.create(**create_params)
        return completion

    
    def get_token_usage(self, response):
        """Extract token usage from OpenAI response."""
        if hasattr(response, "usage"):
            return {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.prompt_tokens + response.usage.completion_tokens
            }
        else:
            # Fallback if token info not available
            return {
                "prompt_tokens": 0,
                "completion_tokens": 0,
                "total_tokens": 0
            }
    
    def get_result(self, response):
        """Extract the text result from the response."""
        if hasattr(response, "choices") and len(response.choices) > 0:
            return response.choices[0].message.content.strip()
        return ""
