{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# A/B Study Seed Finder\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/47/b56b714j7nx18mkf3kyfdwy80000gn/T/ipykernel_14052/476227911.py:8: DeprecationWarning: Importing display from IPython.core.display is deprecated since IPython 7.14, please import from IPython display\n", "  from IPython.core.display import HTML, display\n"]}], "source": ["import copy\n", "import os\n", "from datetime import datetime, timedelta\n", "\n", "import numpy as np\n", "import pandas as pd\n", "from IPython import get_ipython\n", "from IPython.core.display import HTML, display\n", "from pb_schema.ads.standardab.ab_pb2 import GroupConfig, GroupID, LayerType, SegmentID\n", "\n", "if any(['VELLUM' in k for k in os.environ.keys()]):\n", "    # Use Jupyter magic to install banjo if running in VELLUM environment\n", "    get_ipython().run_line_magic('pip', 'install banjo')\n", "\n", "from banjo import abtest\n", "from banjo.utils import gbq, helpers\n", "\n", "project = 'sc-bq-gcs-billingonly'\n", "\n", "def str_date(dt):\n", "    return pd.to_datetime(dt).strftime(\"%Y%m%d\")\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["UNUSED_BGID_DESCRIPTION = \"unused\"\n", "TOTAL_SEGMENTS = 200\n", "\n", "# Copied from https://github.sc-corp.net/Snapchat/configment/blob/dd9fa6385d3e10a2b42514a3afef8b6ea9f05692/configment/ad_publisher/budget_ab/base_prod_expt.py#L2841-L4607\n", "layer_group_segments = {\n", "    LayerType.BUDGET_UNAWARE_AB_LAYER_1: [\n", "        GroupConfig(\n", "            group_id=GroupID.GID_1,\n", "            segment_id=range(SegmentID.SID_1, SegmentID.SID_20 + 1),\n", "            test_name=\"control_1\",\n", "            treatment_name=\"control_1\",\n", "            is_control=True,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_2,\n", "            segment_id=range(SegmentID.SID_21, SegmentID.SID_40 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            control_group_id=GroupID.GID_1,\n", "            is_control=False,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_3,\n", "            segment_id=range(SegmentID.SID_41, SegmentID.SID_60 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_4,\n", "            segment_id=range(SegmentID.SID_61, SegmentID.SID_80 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_5,\n", "            segment_id=range(SegmentID.SID_81, SegmentID.SID_100 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_6,\n", "            segment_id=range(SegmentID.SID_101, SegmentID.SID_120 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_7,\n", "            segment_id=range(SegmentID.SID_121, SegmentID.SID_140 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_8,\n", "            segment_id=range(SegmentID.SID_141, SegmentID.SID_160 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_9,\n", "            segment_id=range(SegmentID.SID_161, SegmentID.SID_180 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_10,\n", "            segment_id=range(SegmentID.SID_181, SegmentID.SID_200 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "    ],\n", "    LayerType.BUDGET_UNAWARE_AB_LAYER_2: [\n", "        GroupConfig(\n", "            group_id=GroupID.GID_1,\n", "            segment_id=range(SegmentID.SID_1, SegmentID.SID_20 + 1),\n", "            test_name=\"control_1\",\n", "            treatment_name=\"control_1\",\n", "            is_control=True,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_2,\n", "            segment_id=range(SegmentID.SID_21, SegmentID.SID_40 + 1),\n", "            test_name=\"czhang3_quota_tuning_0528\",\n", "            treatment_name=\"czhang3_quota_tuning_0528\",\n", "            control_group_id=GroupID.GID_1,\n", "            owner=\"<EMAIL>\",\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_3,\n", "            segment_id=range(SegmentID.SID_41, SegmentID.SID_60 + 1),\n", "            test_name=\"ops_v2_experiment_20250602\",\n", "            treatment_name=\"ops_v2_experiment_20250602\",\n", "            control_group_id=GroupID.GID_1,\n", "            owner=\"<EMAIL>\",\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_4,\n", "            segment_id=range(SegmentID.SID_61, SegmentID.SID_80 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            control_group_id=GroupID.GID_1,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_5,\n", "            segment_id=range(SegmentID.SID_81, SegmentID.SID_100 + 1),\n", "            test_name=\"zli12_1p_smape_0523\",\n", "            treatment_name=\"zli12_1p_smape_0523\",\n", "            owner=\"<EMAIL>\",\n", "            control_group_id=GroupID.GID_1,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_6,\n", "            segment_id=range(SegmentID.SID_101, SegmentID.SID_120 + 1),\n", "            test_name=\"dpa_mtl_skan_with_downweighted_f1_0522\",\n", "            treatment_name=\"dpa_mtl_skan_with_downweighted_f1_0522\",\n", "            owner=\"<EMAIL>\",\n", "            control_group_id=GroupID.GID_1,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_7,\n", "            segment_id=range(SegmentID.SID_121, SegmentID.SID_140 + 1),\n", "            test_name=\"spham2_uid_freshness_improvement_0513\",\n", "            treatment_name=\"spham2_uid_freshness_improvement_0513\",\n", "            owner=\"<EMAIL>\",\n", "            control_group_id=GroupID.GID_1,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_8,\n", "            segment_id=range(SegmentID.SID_141, SegmentID.SID_160 + 1),\n", "            test_name=\"sqamar_sp_evaclip_seed_prio_0520\",\n", "            treatment_name=\"sqamar_sp_evaclip_seed_prio_0520\",\n", "            control_group_id=GroupID.GID_1,\n", "            owner=\"<EMAIL>\",\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_9,\n", "            segment_id=range(SegmentID.SID_161, SegmentID.SID_180 + 1),\n", "            test_name=\"sqamar_sp_no_cache_clip_control_0520\",\n", "            treatment_name=\"sqamar_sp_no_cache_clip_control_0520\",\n", "            control_group_id=GroupID.GID_1,\n", "            owner=\"<EMAIL>\",\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_10,\n", "            segment_id=range(SegmentID.SID_181, SegmentID.SID_200 + 1),\n", "            test_name=\"swang7_amazon_swipe_canonical_20250530\",\n", "            treatment_name=\"swang7_amazon_swipe_canonical_20250530\",\n", "            control_group_id=GroupID.GID_1,\n", "            owner=\"<EMAIL>\",\n", "            is_control=False,\n", "        ),\n", "    ],\n", "    LayerType.BUDGET_UNAWARE_AB_LAYER_3: [\n", "        GroupConfig(\n", "            group_id=GroupID.GID_1,\n", "            segment_id=range(SegmentID.SID_1, SegmentID.SID_10 + 1),\n", "            test_name=\"control_1\",\n", "            treatment_name=\"control_1\",\n", "            is_control=True,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_2,\n", "            segment_id=range(SegmentID.SID_11, SegmentID.SID_20 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_3,\n", "            segment_id=range(SegmentID.SID_21, SegmentID.SID_30 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_4,\n", "            segment_id=range(SegmentID.SID_31, SegmentID.SID_40 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_5,\n", "            segment_id=range(SegmentID.SID_41, SegmentID.SID_50 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_6,\n", "            segment_id=range(SegmentID.SID_51, SegmentID.SID_60 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_7,\n", "            segment_id=range(SegmentID.SID_61, SegmentID.SID_70 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_8,\n", "            segment_id=range(SegmentID.SID_71, SegmentID.SID_80 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_9,\n", "            segment_id=range(SegmentID.SID_81, SegmentID.SID_90 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_10,\n", "            segment_id=range(SegmentID.SID_91, SegmentID.SID_100 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_11,\n", "            segment_id=range(SegmentID.SID_101, SegmentID.SID_110 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_12,\n", "            segment_id=range(SegmentID.SID_111, SegmentID.SID_120 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_13,\n", "            segment_id=range(SegmentID.SID_121, SegmentID.SID_130 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_14,\n", "            segment_id=range(SegmentID.SID_131, SegmentID.SID_140 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_15,\n", "            segment_id=range(SegmentID.SID_141, SegmentID.SID_150 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_16,\n", "            segment_id=range(SegmentID.SID_151, SegmentID.SID_160 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_17,\n", "            segment_id=range(SegmentID.SID_161, SegmentID.SID_170 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_18,\n", "            segment_id=range(SegmentID.SID_171, SegmentID.SID_180 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_19,\n", "            segment_id=range(SegmentID.SID_181, SegmentID.SID_190 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_20,\n", "            segment_id=range(SegmentID.SID_191, SegmentID.SID_200 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "    ],\n", "    LayerType.BUDGET_UNAWARE_AB_LAYER_4: [\n", "        GroupConfig(\n", "            group_id=GroupID.GID_1,\n", "            segment_id=range(SegmentID.SID_1, SegmentID.SID_10 + 1),\n", "            test_name=\"control_1\",\n", "            treatment_name=\"control_1\",\n", "            is_control=True,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_2,\n", "            segment_id=range(SegmentID.SID_11, SegmentID.SID_20 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=\"\",\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_3,\n", "            segment_id=range(SegmentID.SID_21, SegmentID.SID_30 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_4,\n", "            segment_id=range(SegmentID.SID_31, SegmentID.SID_40 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_5,\n", "            segment_id=range(SegmentID.SID_41, SegmentID.SID_50 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_6,\n", "            segment_id=range(SegmentID.SID_51, SegmentID.SID_60 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_7,\n", "            segment_id=range(SegmentID.SID_61, SegmentID.SID_70 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_8,\n", "            segment_id=range(SegmentID.SID_71, SegmentID.SID_80 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_9,\n", "            segment_id=range(SegmentID.SID_81, SegmentID.SID_90 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_10,\n", "            segment_id=range(SegmentID.SID_91, SegmentID.SID_100 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_11,\n", "            segment_id=range(SegmentID.SID_101, SegmentID.SID_110 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_12,\n", "            segment_id=range(SegmentID.SID_111, SegmentID.SID_120 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_13,\n", "            segment_id=range(SegmentID.SID_121, SegmentID.SID_130 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_14,\n", "            segment_id=range(SegmentID.SID_131, SegmentID.SID_140 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_15,\n", "            segment_id=range(SegmentID.SID_141, SegmentID.SID_150 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_16,\n", "            segment_id=range(SegmentID.SID_151, SegmentID.SID_160 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_17,\n", "            segment_id=range(SegmentID.SID_161, SegmentID.SID_170 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_18,\n", "            segment_id=range(SegmentID.SID_171, SegmentID.SID_180 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_19,\n", "            segment_id=range(SegmentID.SID_181, SegmentID.SID_190 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_20,\n", "            segment_id=range(SegmentID.SID_191, SegmentID.SID_200 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "    ],\n", "    LayerType.BUDGET_UNAWARE_AB_LAYER_5: [\n", "        GroupConfig(\n", "            group_id=GroupID.GID_1,\n", "            segment_id=range(SegmentID.SID_1, SegmentID.SID_3),\n", "            test_name=\"control_1\",\n", "            treatment_name=\"control_1\",\n", "            is_control=True,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_2,\n", "            segment_id=range(SegmentID.SID_3, SegmentID.SID_5),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_3,\n", "            segment_id=range(SegmentID.SID_5, SegmentID.SID_7),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_4,\n", "            segment_id=range(SegmentID.SID_7, SegmentID.SID_9),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_5,\n", "            segment_id=range(SegmentID.SID_9, SegmentID.SID_11),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_6,\n", "            segment_id=range(SegmentID.SID_11, SegmentID.SID_13),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_7,\n", "            segment_id=range(SegmentID.SID_13, SegmentID.SID_15),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_8,\n", "            segment_id=range(SegmentID.SID_15, SegmentID.SID_17),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_9,\n", "            segment_id=range(SegmentID.SID_17, SegmentID.SID_19),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_10,\n", "            segment_id=range(SegmentID.SID_19, SegmentID.SID_21),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_11,\n", "            segment_id=range(SegmentID.SID_21, SegmentID.SID_23),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_12,\n", "            segment_id=range(SegmentID.SID_23, SegmentID.SID_25),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_13,\n", "            segment_id=range(SegmentID.SID_25, SegmentID.SID_27),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_14,\n", "            segment_id=range(SegmentID.SID_27, SegmentID.SID_29),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_15,\n", "            segment_id=range(SegmentID.SID_29, SegmentID.SID_31),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_16,\n", "            segment_id=range(SegmentID.SID_31, SegmentID.SID_33),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_17,\n", "            segment_id=range(SegmentID.SID_33, SegmentID.SID_35),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_18,\n", "            segment_id=range(SegmentID.SID_35, SegmentID.SID_37),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_19,\n", "            segment_id=range(SegmentID.SID_37, SegmentID.SID_39),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_20,\n", "            segment_id=range(SegmentID.SID_39, SegmentID.SID_41),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_21,\n", "            segment_id=range(SegmentID.SID_41, SegmentID.SID_43),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_22,\n", "            segment_id=range(SegmentID.SID_43, SegmentID.SID_45),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_23,\n", "            segment_id=range(SegmentID.SID_45, SegmentID.SID_47),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_24,\n", "            segment_id=range(SegmentID.SID_47, SegmentID.SID_49),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_25,\n", "            segment_id=range(SegmentID.SID_49, SegmentID.SID_51),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_26,\n", "            segment_id=range(SegmentID.SID_51, SegmentID.SID_53),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_27,\n", "            segment_id=range(SegmentID.SID_53, SegmentID.SID_55),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_28,\n", "            segment_id=range(SegmentID.SID_55, SegmentID.SID_57),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_29,\n", "            segment_id=range(SegmentID.SID_57, SegmentID.SID_59),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_30,\n", "            segment_id=range(SegmentID.SID_59, SegmentID.SID_61),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_31,\n", "            segment_id=range(SegmentID.SID_61, SegmentID.SID_63),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_32,\n", "            segment_id=range(SegmentID.SID_63, SegmentID.SID_65),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_33,\n", "            segment_id=range(SegmentID.SID_65, SegmentID.SID_67),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_34,\n", "            segment_id=range(SegmentID.SID_67, SegmentID.SID_69),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_35,\n", "            segment_id=range(SegmentID.SID_69, SegmentID.SID_71),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_36,\n", "            segment_id=range(SegmentID.SID_71, SegmentID.SID_73),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_37,\n", "            segment_id=range(SegmentID.SID_73, SegmentID.SID_75),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_38,\n", "            segment_id=range(SegmentID.SID_75, SegmentID.SID_77),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_39,\n", "            segment_id=range(SegmentID.SID_77, SegmentID.SID_79),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_40,\n", "            segment_id=range(SegmentID.SID_79, SegmentID.SID_81),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_41,\n", "            segment_id=range(SegmentID.SID_81, SegmentID.SID_83),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_42,\n", "            segment_id=range(SegmentID.SID_83, SegmentID.SID_85),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_43,\n", "            segment_id=range(SegmentID.SID_85, SegmentID.SID_87),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_44,\n", "            segment_id=range(SegmentID.SID_87, SegmentID.SID_89),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_45,\n", "            segment_id=range(SegmentID.SID_89, SegmentID.SID_91),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_46,\n", "            segment_id=range(SegmentID.SID_91, SegmentID.SID_93),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_47,\n", "            segment_id=range(SegmentID.SID_93, SegmentID.SID_95),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_48,\n", "            segment_id=range(SegmentID.SID_95, SegmentID.SID_97),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_49,\n", "            segment_id=range(SegmentID.SID_97, SegmentID.SID_99),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_50,\n", "            segment_id=range(SegmentID.SID_99, SegmentID.SID_101),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_51,\n", "            segment_id=range(SegmentID.SID_101, SegmentID.SID_105),\n", "            test_name=\"control_2\",\n", "            treatment_name=\"control_2\",\n", "            is_control=True,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_52,\n", "            segment_id=range(SegmentID.SID_105, SegmentID.SID_109),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_51,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_53,\n", "            segment_id=range(SegmentID.SID_109, SegmentID.SID_113),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_51,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_54,\n", "            segment_id=range(SegmentID.SID_113, SegmentID.SID_117),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_51,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_55,\n", "            segment_id=range(SegmentID.SID_117, SegmentID.SID_121),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_51,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_56,\n", "            segment_id=range(SegmentID.SID_121, SegmentID.SID_125),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_51,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_57,\n", "            segment_id=range(SegmentID.SID_125, SegmentID.SID_129),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_51,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_58,\n", "            segment_id=range(SegmentID.SID_129, SegmentID.SID_133),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_51,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_59,\n", "            segment_id=range(SegmentID.SID_133, SegmentID.SID_137),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_51,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_60,\n", "            segment_id=range(SegmentID.SID_137, SegmentID.SID_141),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_51,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_61,\n", "            segment_id=range(SegmentID.SID_141, SegmentID.SID_145),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_51,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_62,\n", "            segment_id=range(SegmentID.SID_145, SegmentID.SID_149),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_51,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_63,\n", "            segment_id=range(SegmentID.SID_149, SegmentID.SID_153),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_51,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_64,\n", "            segment_id=range(SegmentID.SID_153, SegmentID.SID_157),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_51,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_65,\n", "            segment_id=range(SegmentID.SID_157, SegmentID.SID_161),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_51,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_66,\n", "            segment_id=range(SegmentID.SID_161, SegmentID.SID_165),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_51,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_67,\n", "            segment_id=range(SegmentID.SID_165, SegmentID.SID_169),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_51,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_68,\n", "            segment_id=range(SegmentID.SID_169, SegmentID.SID_173),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_51,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_69,\n", "            segment_id=range(SegmentID.SID_173, SegmentID.SID_177),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_51,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_70,\n", "            segment_id=range(SegmentID.SID_177, SegmentID.SID_181),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_51,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_71,\n", "            segment_id=range(SegmentID.SID_181, SegmentID.SID_185),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_51,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_72,\n", "            segment_id=range(SegmentID.SID_185, SegmentID.SID_189),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_51,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_73,\n", "            segment_id=range(SegmentID.SID_189, SegmentID.SID_193),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_51,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_74,\n", "            segment_id=range(SegmentID.SID_193, SegmentID.SID_197),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_51,\n", "            owner=None,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_75,\n", "            segment_id=range(SegmentID.SID_197, SegmentID.SID_200 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            start_date=None,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_51,\n", "            owner=None,\n", "        ),\n", "    ],\n", "    LayerType.BUDGET_UNAWARE_AB_LAYER_6: [\n", "        GroupConfig(\n", "            group_id=GroupID.GID_1,\n", "            segment_id=range(SegmentID.SID_1, SegmentID.SID_20 + 1),\n", "            test_name=\"control_1\",\n", "            treatment_name=\"control_1\",\n", "            is_control=True,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_2,\n", "            segment_id=range(SegmentID.SID_21, SegmentID.SID_40 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_3,\n", "            segment_id=range(SegmentID.SID_41, SegmentID.SID_60 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_4,\n", "            segment_id=range(SegmentID.SID_61, SegmentID.SID_80 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_5,\n", "            segment_id=range(SegmentID.SID_81, SegmentID.SID_100 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_6,\n", "            segment_id=range(SegmentID.SID_101, SegmentID.SID_120 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_7,\n", "            segment_id=range(SegmentID.SID_121, SegmentID.SID_140 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_8,\n", "            segment_id=range(SegmentID.SID_141, SegmentID.SID_160 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_9,\n", "            segment_id=range(SegmentID.SID_161, SegmentID.SID_180 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_10,\n", "            segment_id=range(SegmentID.SID_181, SegmentID.SID_200 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "    ],\n", "    LayerType.BUDGET_UNAWARE_AB_LAYER_7: [\n", "        GroupConfig(\n", "            group_id=GroupID.GID_1,\n", "            segment_id=range(SegmentID.SID_1, SegmentID.SID_20 + 1),\n", "            test_name=\"control_1\",\n", "            treatment_name=\"control_1\",\n", "            is_control=True,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_2,\n", "            segment_id=range(SegmentID.SID_21, SegmentID.SID_40 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_3,\n", "            segment_id=range(SegmentID.SID_41, SegmentID.SID_60 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_4,\n", "            segment_id=range(SegmentID.SID_61, SegmentID.SID_80 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_5,\n", "            segment_id=range(SegmentID.SID_81, SegmentID.SID_100 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_6,\n", "            segment_id=range(SegmentID.SID_101, SegmentID.SID_120 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_7,\n", "            segment_id=range(SegmentID.SID_121, SegmentID.SID_140 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_8,\n", "            segment_id=range(SegmentID.SID_141, SegmentID.SID_160 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_9,\n", "            segment_id=range(SegmentID.SID_161, SegmentID.SID_180 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_10,\n", "            segment_id=range(SegmentID.SID_181, SegmentID.SID_200 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "    ],\n", "    LayerType.BUDGET_UNAWARE_AB_LAYER_8: [\n", "        GroupConfig(\n", "            group_id=GroupID.GID_1,\n", "            segment_id=range(SegmentID.SID_1, SegmentID.SID_10 + 1),\n", "            test_name=\"control_1\",\n", "            treatment_name=\"control_1\",\n", "            is_control=True,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_2,\n", "            segment_id=range(SegmentID.SID_11, SegmentID.SID_20 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_3,\n", "            segment_id=range(SegmentID.SID_21, SegmentID.SID_30 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_4,\n", "            segment_id=range(SegmentID.SID_31, SegmentID.SID_40 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_5,\n", "            segment_id=range(SegmentID.SID_41, SegmentID.SID_50 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_6,\n", "            segment_id=range(SegmentID.SID_51, SegmentID.SID_60 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_7,\n", "            segment_id=range(SegmentID.SID_61, SegmentID.SID_70 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_8,\n", "            segment_id=range(SegmentID.SID_71, SegmentID.SID_80 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_9,\n", "            segment_id=range(SegmentID.SID_81, SegmentID.SID_90 + 1),\n", "            test_name=\"an<PERSON><PERSON>_mbu_8_1_configment_integration_aa\",\n", "            treatment_name=\"an<PERSON><PERSON>_mbu_8_1_configment_integration_aa\",\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=\"<EMAIL>\",\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_10,\n", "            segment_id=range(SegmentID.SID_91, SegmentID.SID_100 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_11,\n", "            segment_id=range(SegmentID.SID_101, SegmentID.SID_110 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_12,\n", "            segment_id=range(SegmentID.SID_111, SegmentID.SID_120 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_13,\n", "            segment_id=range(SegmentID.SID_121, SegmentID.SID_130 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_14,\n", "            segment_id=range(SegmentID.SID_131, SegmentID.SID_140 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_15,\n", "            segment_id=range(SegmentID.SID_141, SegmentID.SID_150 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_16,\n", "            segment_id=range(SegmentID.SID_151, SegmentID.SID_160 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_17,\n", "            segment_id=range(SegmentID.SID_161, SegmentID.SID_170 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_18,\n", "            segment_id=range(SegmentID.SID_171, SegmentID.SID_180 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_19,\n", "            segment_id=range(SegmentID.SID_181, SegmentID.SID_190 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_20,\n", "            segment_id=range(SegmentID.SID_191, SegmentID.SID_200 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "    ],\n", "    LayerType.BUDGET_UNAWARE_AB_LAYER_9: [\n", "        GroupConfig(\n", "            group_id=GroupID.GID_1,\n", "            segment_id=range(SegmentID.SID_1, SegmentID.SID_20 + 1),\n", "            test_name=\"control_1\",\n", "            treatment_name=\"control_1\",\n", "            is_control=True,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_2,\n", "            segment_id=range(SegmentID.SID_21, SegmentID.SID_40 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_3,\n", "            segment_id=range(SegmentID.SID_41, SegmentID.SID_60 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_4,\n", "            segment_id=range(SegmentID.SID_61, SegmentID.SID_80 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_5,\n", "            segment_id=range(SegmentID.SID_81, SegmentID.SID_100 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_6,\n", "            segment_id=range(SegmentID.SID_101, SegmentID.SID_120 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_7,\n", "            segment_id=range(SegmentID.SID_121, SegmentID.SID_140 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_8,\n", "            segment_id=range(SegmentID.SID_141, SegmentID.SID_160 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_9,\n", "            segment_id=range(SegmentID.SID_161, SegmentID.SID_180 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_10,\n", "            segment_id=range(SegmentID.SID_181, SegmentID.SID_200 + 1),\n", "            test_name=\"an<PERSON>son_mbu_9_1_configment_integration_aa\",\n", "            treatment_name=\"an<PERSON><PERSON>_mbu_9_1_configment_integration_aa\",\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_1,\n", "            owner=\"<EMAIL>\",\n", "        ),\n", "    ],\n", "    LayerType.BUDGET_UNAWARE_AB_LAYER_10: [\n", "        GroupConfig(\n", "            group_id=GroupID.GID_1,\n", "            segment_id=range(SegmentID.SID_1, SegmentID.SID_10 + 1),\n", "            test_name='control_1',\n", "            treatment_name='control_1',\n", "            is_control=True,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_2,\n", "            segment_id=range(SegmentID.SID_11, SegmentID.SID_20 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_3,\n", "            segment_id=range(SegmentID.SID_21, SegmentID.SID_30 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_4,\n", "            segment_id=range(SegmentID.SID_31, SegmentID.SID_40 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_5,\n", "            segment_id=range(SegmentID.SID_41, SegmentID.SID_50 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_6,\n", "            segment_id=range(SegmentID.SID_51, SegmentID.SID_60 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_7,\n", "            segment_id=range(SegmentID.SID_61, SegmentID.SID_70 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_8,\n", "            segment_id=range(SegmentID.SID_71, SegmentID.SID_80 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_9,\n", "            segment_id=range(SegmentID.SID_81, SegmentID.SID_90 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_10,\n", "            segment_id=range(SegmentID.SID_91, SegmentID.SID_100 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_11,\n", "            segment_id=range(SegmentID.SID_101, SegmentID.SID_110 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_12,\n", "            segment_id=range(SegmentID.SID_111, SegmentID.SID_120 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_13,\n", "            segment_id=range(SegmentID.SID_121, SegmentID.SID_130 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_14,\n", "            segment_id=range(SegmentID.SID_131, SegmentID.SID_140 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_15,\n", "            segment_id=range(SegmentID.SID_141, SegmentID.SID_150 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_16,\n", "            segment_id=range(SegmentID.SID_151, SegmentID.SID_160 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_17,\n", "            segment_id=range(SegmentID.SID_161, SegmentID.SID_170 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_18,\n", "            segment_id=range(SegmentID.SID_171, SegmentID.SID_180 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_19,\n", "            segment_id=range(SegmentID.SID_181, SegmentID.SID_190 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_20,\n", "            segment_id=range(SegmentID.SID_191, SegmentID.SID_200 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "    ],\n", "    LayerType.BUDGET_UNAWARE_AB_LAYER_11: [\n", "        GroupConfig(\n", "            group_id=GroupID.GID_1,\n", "            segment_id=range(SegmentID.SID_1, SegmentID.SID_10 + 1),\n", "            test_name='control_1',\n", "            treatment_name='control_1',\n", "            is_control=True,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_2,\n", "            segment_id=range(SegmentID.SID_11, SegmentID.SID_20 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_3,\n", "            segment_id=range(SegmentID.SID_21, SegmentID.SID_30 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_4,\n", "            segment_id=range(SegmentID.SID_31, SegmentID.SID_40 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_5,\n", "            segment_id=range(SegmentID.SID_41, SegmentID.SID_50 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_6,\n", "            segment_id=range(SegmentID.SID_51, SegmentID.SID_60 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_7,\n", "            segment_id=range(SegmentID.SID_61, SegmentID.SID_70 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_8,\n", "            segment_id=range(SegmentID.SID_71, SegmentID.SID_80 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_9,\n", "            segment_id=range(SegmentID.SID_81, SegmentID.SID_90 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_10,\n", "            segment_id=range(SegmentID.SID_91, SegmentID.SID_100 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_11,\n", "            segment_id=range(SegmentID.SID_101, SegmentID.SID_110 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_12,\n", "            segment_id=range(SegmentID.SID_111, SegmentID.SID_120 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_13,\n", "            segment_id=range(SegmentID.SID_121, SegmentID.SID_130 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_14,\n", "            segment_id=range(SegmentID.SID_131, SegmentID.SID_140 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_15,\n", "            segment_id=range(SegmentID.SID_141, SegmentID.SID_150 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_16,\n", "            segment_id=range(SegmentID.SID_151, SegmentID.SID_160 + 1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_17,\n", "            segment_id=range(SegmentID.SID_161, SegmentID.SID_163),\n", "            test_name='control_2',\n", "            treatment_name='control_2',\n", "            is_control=True,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_18,\n", "            segment_id=range(SegmentID.SID_163, SegmentID.SID_165),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_17,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_19,\n", "            segment_id=range(SegmentID.SID_165, SegmentID.SID_167),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_17,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_20,\n", "            segment_id=range(SegmentID.SID_167, SegmentID.SID_169),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_17,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_21,\n", "            segment_id=range(SegmentID.SID_169, SegmentID.SID_171),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_17,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_22,\n", "            segment_id=range(SegmentID.SID_171, SegmentID.SID_173),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_17,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_23,\n", "            segment_id=range(SegmentID.SID_173, SegmentID.SID_175),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_17,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_24,\n", "            segment_id=range(SegmentID.SID_175, SegmentID.SID_177),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_17,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_25,\n", "            segment_id=range(SegmentID.SID_177, SegmentID.SID_179),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_17,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_26,\n", "            segment_id=range(SegmentID.SID_179, SegmentID.SID_181),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_17,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_27,\n", "            segment_id=range(SegmentID.SID_181, SegmentID.SID_183),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_17,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_28,\n", "            segment_id=range(SegmentID.SID_183, SegmentID.SID_185),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_17,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_29,\n", "            segment_id=range(SegmentID.SID_185, SegmentID.SID_187),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_17,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_30,\n", "            segment_id=range(SegmentID.SID_187, SegmentID.SID_189),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_17,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_31,\n", "            segment_id=range(SegmentID.SID_189, SegmentID.SID_191),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_17,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_32,\n", "            segment_id=range(SegmentID.SID_191, SegmentID.SID_193),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_17,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_33,\n", "            segment_id=range(SegmentID.SID_193, SegmentID.SID_195),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_17,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_34,\n", "            segment_id=range(SegmentID.SID_195, SegmentID.SID_197),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_17,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_35,\n", "            segment_id=range(SegmentID.SID_197, SegmentID.SID_199),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_17,\n", "        ),\n", "        GroupConfig(\n", "            group_id=GroupID.GID_36,\n", "            segment_id=range(SegmentID.SID_199, SegmentID.SID_200+1),\n", "            test_name=UNUSED_BGID_DESCRIPTION,\n", "            treatment_name=UNUSED_BGID_DESCRIPTION,\n", "            is_control=False,\n", "            control_group_id=GroupID.GID_17,\n", "        ),\n", "    ],\n", "}\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["temp_dataset = \"temp_abtest\"\n", "\n", "layer_enum_number_str = \"11\"\n", "\n", "ds = \"20250604\"\n", "\n", "# dropdown available metrics in Seed Finder Husky UI\n", "evaluation_metrics = [\n", "    \"app_application_open_count\",\n", "    \"chat_chat_send_count\",\n", "    \"chat_chat_view_count\",\n", "    \"direct_snap_create_count\",\n", "    \"direct_snap_send_count\",\n", "    \"direct_snap_view_count\",\n", "    \"direct_snap_save_count\",\n", "    \"discover_feed_non_friend_story_view_count\",\n", "    \"discover_feed_non_friend_story_time_viewed\",\n", "    \"friend_story_snap_view_count\",\n", "    \"friend_story_snap_view_time\",\n", "    \"lens_direct_snap_send_count\",\n", "    \"total_lens_swipe_count\",\n", "    \"spotlight_story_snap_view_count\",\n", "    \"spotlight_story_snap_view_time\",\n", "    \"story_snap_post_count\",\n", "    \"story_snap_view_count\",\n", "]\n", "\n", "evaluation_quest_metrics = [\n", "    # \"friend_outbound_request_to_new_user_send\", # \"Outbound Friend Requests Sent To New Users\",\n", "    # \"friend_outbound_request_send\", # \"Outbound Friend Requests Sent\",\n", "    # \"friend_inbound_request_accept\", # \"Inbound Friend Requests Accept\",\n", "    # \"friend_bidirectional_request_accept\", # \"Bidirectional Friends Made\",\n", "    # \"new_friends_made_with_bidirectional_communication\", # \"New friendships with bi-directional communication\"\n", "]\n", "\n", "cohort_definition_metric = \"app_application_open_count\"\n", "cohort_definition_min_active_days = 3\n", "\n", "os_types = [\"iOS\", \"Android\"]\n", "countries = []\n", "\n", "seed_field_name_pattern = \"seed_{:04d}\"\n", "total_seeds = 25\n", "\n", "USE_BATCH_BQ_PRIORITY = False\n", "default_seed = \"\"\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ds = str_date(ds)\n", "layer_enum_number = int(layer_enum_number_str)\n", "\n", "group_configs = layer_group_segments[layer_enum_number]\n", "\n", "traffic_splits = (\n", "    np.divide(\n", "        [len(gc.segment_id) for gc in layer_group_segments[layer_enum_number]],\n", "        TOTAL_SEGMENTS,\n", "    )\n", "    * 100\n", ")\n", "\n", "total_seeds = np.min([100, int(total_seeds)])\n", "total_seeds += 1  # plus the default layerX_YYYYMMdd as seed\n", "\n", "fields_processed_in_udf = [\n", "    seed_field_name_pattern.format(index) for index in range(total_seeds)\n", "]\n", "\n", "\n", "# Ensure certain items passed from husky are lists\n", "def ensure_list(parameter):\n", "    if parameter is None:\n", "        return None\n", "    if isinstance(parameter, list):\n", "        return parameter\n", "    else:\n", "        return [parameter]\n", "\n", "\n", "# Final metrics used in Seed Finder, selections from \"A/B Study Seed Finder\" Husky UI Metrics dropdown\n", "evaluation_metrics = ensure_list(evaluation_metrics)\n", "evaluation_quest_metrics = ensure_list(evaluation_quest_metrics)\n", "os_types = ensure_list(os_types)\n", "countries = ensure_list(countries)\n", "BQ_PRIORITY = \"BATCH\" if USE_BATCH_BQ_PRIORITY else \"INTERACTIVE\"\n", "\n", "default_seed = default_seed.strip()\n", "if not default_seed:\n", "    # Budget unaware layer starts with enum number 3\n", "    default_seed = f\"layer{layer_enum_number - 2}_{ds}\"\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["def find_last_available_date(project, dataset, table, ds):\n", "    ds = pd.to_datetime(ds)\n", "    while not gbq.get_table(\n", "        project,\n", "        dataset=dataset,\n", "        table=\"{}_{}\".format(table, str_date(ds)),\n", "    ):\n", "        ds -= <PERSON><PERSON><PERSON>(days=1)\n", "        if ds < datetime.now() - <PERSON><PERSON><PERSON>(days=120):\n", "            raise ValueError(\n", "                \"Seed Finder cannot be run for date earlier than {}\".format(\n", "                    datetime.now() - <PERSON><PERSON><PERSON>(days=120)\n", "                )\n", "            )\n", "\n", "    return str_date(ds)\n", "\n", "\n", "input_ds = ds\n", "ds = find_last_available_date(\n", "    \"sc-analytics\",\n", "    \"report_app\",\n", "    \"dau_user_country\",\n", "    input_ds,\n", ")\n", "if ds != input_ds:\n", "    display(\n", "        HTML(\n", "            \"<p>Because the table is not available for the date you picked ({}), \"\n", "            \"we use the seven days ending on {} for the computation.</p>\".format(\n", "                input_ds, ds\n", "            )\n", "        )\n", "    )\n", "\n", "seed_str_pattern = \"layer{layer}_{ds}_{{index:04d}}\".format(\n", "    layer=(layer_enum_number - 2),\n", "    ds=ds,\n", ")\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["def get_seed(\n", "    index: int,\n", "    default: str = default_seed,\n", ") -> str:\n", "    if index == 0:\n", "        return default\n", "    else:\n", "        return seed_str_pattern.format(index=index)\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["# handle extra filtering logic\n", "\n", "extra_wheres = []\n", "if countries:\n", "    extra_wheres.append(\n", "        \"country IN ({})\".format(\", \".join(\"'{}'\".format(country) for country in countries))\n", "    )\n", "\n", "if extra_wheres:\n", "    extra_where_sql = \" \".join(\n", "        \"AND {}\".format(extra_where) for extra_where in extra_wheres\n", "    )\n", "else:\n", "    extra_where_sql = \"\"\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["sql_temp_ab_user_split = \"\"\"\n", "WITH user AS (\n", "  SELECT\n", "    userId AS user_id,\n", "    ghostId AS ghost_id,\n", "    said AS said\n", "  FROM\n", "    `snap-atlas-prod.user_identity.user_id_mapping_20*`\n", "  WHERE\n", "    _TABLE_SUFFIX = (\n", "      SELECT\n", "        SUBSTR (table_id, -6, 6) AS table_ymd\n", "      FROM\n", "        `snap-atlas-prod.user_identity.__TABLES_SUMMARY__`\n", "      WHERE\n", "        table_id LIKE 'user_id_mapping_20%'\n", "      ORDER BY\n", "        table_id DESC\n", "      LIMIT\n", "        1\n", "    )\n", ")\n", "SELECT\n", "  *\n", "FROM (\n", "  SELECT\n", "    ghost_user_id,\n", "    {create_traffic_split_hash_mapping}\n", "  FROM (\n", "    SELECT\n", "      app.ghost_user_id AS ghost_user_id,\n", "      user.said AS user_name\n", "    FROM (\n", "      SELECT\n", "        ghost_user_id,\n", "        COUNT(*) AS active_days\n", "      FROM\n", "        (\n", "          SELECT\n", "            ghost_user_id,\n", "            event_date\n", "          FROM\n", "            `sc-analytics.report_app.dau_user_country_20*`\n", "          WHERE\n", "            _TABLE_SUFFIX BETWEEN SUBSTR('{ds_start}', 3) and SUBSTR('{ds_end}', 3)\n", "            AND {cohort_definition_metric} >  0\n", "            AND top_device IN ({os_types})\n", "            {extra_wheres}\n", "          GROUP BY ghost_user_id, event_date\n", "        )\n", "      GROUP BY ghost_user_id\n", "      HAVING active_days >= {cohort_definition_min_active_days}\n", "    )  app\n", "    INNER JOIN\n", "      user\n", "    ON\n", "      app.ghost_user_id = user.ghost_id\n", "    )\n", ")\n", "WHERE 1=1\n", "\"\"\".format(\n", "    create_traffic_split_hash_mapping=\",\\n\".join(\n", "        \"bqChooseSIDUniformlyFromCount('{split_seed}' || user_name) AS {seed_field_name}\".format(\n", "            split_seed=get_seed(index),\n", "            seed_field_name=seed_field_name_pattern.format(index),\n", "        )\n", "        for index in range(total_seeds)\n", "    ),\n", "    os_types=\", \".join(\"'{}'\".format(os) for os in os_types),\n", "    ds_start=str_date(pd.to_datetime(ds) - timed<PERSON>ta(days=6)),\n", "    ds_end=ds,\n", "    extra_wheres=extra_where_sql,\n", "    cohort_definition_metric=cohort_definition_metric,\n", "    cohort_definition_min_active_days=cohort_definition_min_active_days,\n", ")\n"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["js_get_ab_slices_udf = \"\"\"\n", "CREATE TEMP FUNCTION bqChooseSIDUniformlyFromCount(\n", "  seedId STRING\n", ")\n", "RETURNS NUMERIC\n", "LANGUAGE js\n", "OPTIONS (library=\"gs://ranking-analysis/libs/murmurhash3js.min.js\") -- Ensure this path is correct\n", "AS \\\"\"\"\n", "  // Internal helper function to replicate the hashToDouble logic\n", "  const internalHashToDouble = (seedIdStr) => {\n", "    // Ensure the murmurHash3 library is loaded\n", "    if (typeof murmurHash3 === 'undefined' || !murmurHash3.x64 || !murmurHash3.x64.hash128) {\n", "      throw new Error(\"MurmurHash3 library not loaded or function not found. Check OPTIONS library path.\");\n", "    }\n", "\n", "    const hash128 = murmurHash3.x64.hash128(seedIdStr);\n", "    // Assuming hash128 returns a hex string; we need the lower 64 bits (first 16 hex characters)\n", "    const lower64Hex = hash128.substring(0, 16);\n", "    const hBigint = BigInt(\"0x\" + lower64Hex);\n", "\n", "    // Constants from the Java version\n", "    const MASK_POSITIVE_63_BITS = BigInt(\"0x7fffffffffffffff\"); // (1 << 63) - 1\n", "    const TWO_POW_53 = BigInt(1) << BigInt(53);                 // 2^53\n", "\n", "    const valForModulo = hBigint & MASK_POSITIVE_63_BITS;\n", "    const randomValueBigint = valForModulo % TWO_POW_53;\n", "    const randomValueNum = Number(randomValueBigint);\n", "\n", "    return randomValueNum / Number(TWO_POW_53); // Returns a value between 0.0 (inclusive) and 1.0 (exclusive)\n", "  };\n", "\n", "  // Main UDF logic starts here\n", "  const numSids = 200;\n", "\n", "  if (numSids === null || numSids <= 0) {\n", "    return null; // Or handle as an error, depending on desired behavior for invalid count\n", "  }\n", "\n", "  // 1. Calculate the 'hashedDouble' value (between 0.0 and 1.0)\n", "  const hashedDouble = internalHashToDouble(seedId);\n", "\n", "  // 2. Determine the index to pick.\n", "  // Math.floor(hashedDouble * numSids) will give an index from 0 to numSids - 1.\n", "  // This is because hashedDouble is in [0, 1), so hashedDouble * numSids is in [0, numSids).\n", "  const chosenIndex = Math.floor(hashedDouble * numSids);\n", "\n", "  // 3. Construct the SID. chosenIndex is 0-based, SID numbers are 1-based.\n", "  return chosenIndex + 1;\n", "\\\"\"\";\n", "\"\"\"\n"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["run_id = \"_\".join(\n", "    [\n", "        LayerType.Name(layer_enum_number),\n", "        ds,\n", "        \"uuid\",\n", "        \"_\".join(\"{:.0f}\".format(ts) for ts in traffic_splits),\n", "        helpers.hash_string(sql_temp_ab_user_split)\n", "    ]\n", ")\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["temp_ab_user_split_table_name = \"ab_study_mbu_seed_finder_slices_{}\".format(run_id)\n", "sql_temp_ab_user_split = js_get_ab_slices_udf + sql_temp_ab_user_split\n", "create_temp_ab_user_split_job = gbq.submit_sync_query(\n", "    sql_temp_ab_user_split,\n", "    project_id=project,\n", "    dest_dataset_id=temp_dataset,\n", "    dest_table_name=temp_ab_user_split_table_name,\n", "    write_disposition='WRITE_TRUNCATE',\n", "    priority=BQ_PRIORITY,\n", "    dialect='standard',\n", ")\n", "# moving udf to query for standard sql. https://www.googleapis.com/discovery/v1/apis/bigquery/v2/rest\n"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["dum = gbq.get_table(project_id='sc-analytics', dataset=\"report_app\", table='dau_user_country_{}'.format(ds))\n"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["# Maybe add a map usage one\n", "class Metric:\n", "    def __init__(self, field, name=None, is_active_days=False, ):\n", "        self.field = field\n", "        self.is_active_days = is_active_days\n", "        self.name = name if name else field.replace(\"_\", \" \").title()\n", "    @property\n", "    def sql_in_sum(self):\n", "        if self.is_active_days:\n", "            sql = \"IF(SUM({}) > 0, 1, 0)\".format(self.field)\n", "        else:\n", "            sql = \"SUM({})\".format(self.field)\n", "        return sql\n", "    @property\n", "    def output_field_name(self):\n", "        return \"{}{}\".format(self.field, \"_active_days\" if self.is_active_days else \"\")\n", "    def __str__(self):\n", "        return self.field\n", "    def __repr__(self):\n", "        return \"Metric({}, is_active_days={})\".format(self.field, self.is_active_days)\n", "\n", "\n", "metrics = [\n", "    Metric(\"app_application_open_count\", \"App Active Days\", is_active_days=True),\n", "    Metric(\"chat_chat_send_count\", \"Chat Send\"),\n", "    Metric(\"chat_chat_view_count\", \"Chat View\"),\n", "    Metric(\"direct_snap_create_count\", \"Direct Snap Create\"),\n", "    Metric(\"direct_snap_send_count\", \"Direct Snap Send\"),\n", "    Metric(\"direct_snap_view_count\", \"Direct Snap View\"),\n", "    Metric(\"direct_snap_save_count\", \"Direct Snap Save\"),\n", "    Metric(\"discover_feed_non_friend_story_view_count\", \"DF Non-Friend Story View\"),\n", "    Metric(\"discover_feed_non_friend_story_time_viewed\", \"DF Non-Friend Story View Time\"),\n", "    Metric(\"friend_story_snap_view_count\", \"Friend Story View\"),\n", "    Metric(\"friend_story_snap_view_time\", \"Friend Story View Time\"),\n", "    Metric(\"lens_direct_snap_send_count\", \"Lens Send\"),\n", "    Metric(\"total_lens_swipe_count\", \"Lens Swipe\"),\n", "    Metric(\"spotlight_story_snap_view_count\", \"Spotlight Story View\"),\n", "    Metric(\"spotlight_story_snap_view_time\", \"Spotlight Story View Time\"),\n", "    Metric(\"story_snap_post_count\", \"Story Snap Post\"),\n", "    Metric(\"story_snap_view_count\", \"Story Snap View\")\n", "]\n", "\n", "# Add additional Metric from evaluation_metrics, selected metric fields in Seed Finder Husky UI, if they are not in metrics above\n", "metric_fields = [metric.field for metric in metrics]\n", "for field in evaluation_metrics:\n", "    if field not in metric_fields:\n", "        metrics.append(Metric(field))\n"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["metrics = [\n", "    metric for metric in metrics\n", "    if metric.field.lower() in [sch.name.lower() for sch in dum.schema] and metric.field in evaluation_metrics\n", "]\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["class QuestMetric:\n", "    def __init__(self, table, fields, is_active_days=False):\n", "        \"\"\"\n", "        table: quest table name, quest_section + quest_job\n", "        fields: {quest_measure: metric_name}\n", "        is_active_days: active_day table\n", "        \"\"\"\n", "        self.table = table\n", "        self.fields = fields\n", "        self.is_active_days = is_active_days\n", "\n", "    @property\n", "    def sql_in_sum(self):\n", "        sum_clause = ',\\n'.join([\"SUM({quest_measure}) AS {quest_measure}\".format(quest_measure=k) for k in self.fields])\n", "        return sum_clause\n", "\n", "    @property\n", "    def output_field_names(self):\n", "        output_clause = ',\\n'.join([\"COALESCE({quest_measure}, 0) AS {quest_measure}\".format(quest_measure=k) for k in self.fields])\n", "        return output_clause\n", "\n", "    @property\n", "    def select_field_names(self):\n", "        select_clause = ',\\n'.join(self.fields)\n", "        return select_clause\n", "\n", "    @property\n", "    def aggregate_fields(self):\n", "        aggregate_clause = \",\\n\".join([\n", "            \"CAST(SUM(IF(IS_NAN({quest_measure}), 0, {quest_measure})) AS FLOAT64) AS {quest_measure}_sum, \"\n", "            \"CAST(SUM(POW(IF(IS_NAN({quest_measure}), 0, {quest_measure}), 2)) AS FLOAT64) AS {quest_measure}_ssq\".format(\n", "                quest_measure=k\n", "            ) for k in self.fields\n", "        ])\n", "        return aggregate_clause\n", "\n", "\"\"\"\n", "FRIENDING_ACTION____bidirectional_friends_make\n", "FRIENDING_ACTION____new_friends_made_with_bidirectional_communication\n", "FRIENDING_ACTION____inbound_friend_requests_accept\n", "FRIENDING_ACTION____outbound_friend_requests_send\n", "FRIENDING_ACTION____outbound_friend_requests_send_to_new_users\n", "\"\"\"\n", "quest_metric_candidates = [\n", "    QuestMetric(\n", "        'friending_friend_request_send_user',\n", "        {\n", "            \"friend_outbound_request_to_new_user_send\": \"Outbound Friend Requests Sent To New Users\",\n", "            \"friend_outbound_request_send\": \"Outbound Friend Requests Sent\"\n", "        }\n", "    ),\n", "    QuestMetric(\n", "        'friending_friend_request_accept_user',\n", "        {\n", "            \"friend_inbound_request_accept\": \"Inbound Friend Requests Accept\",\n", "            \"friend_bidirectional_request_accept\": \"Bidirectional Friends Made\"\n", "        }\n", "    ),\n", "    QuestMetric(\n", "        'friending_new_friendship_with_bidirectional_communication',\n", "        {\n", "            \"new_friends_made_with_bidirectional_communication\": \"New friendships with bi-directional communication\"\n", "        }\n", "    ),\n", "    QuestMetric(\n", "        'maps_map_open_user',\n", "        {\n", "            \"map_open\": \"Map Opens\"\n", "        }\n", "    ),\n", "    QuestMetric(\n", "        'maps_map_open_active_day_user',\n", "        {\n", "            \"map_open_active_day\": \"Map Open Active Day\"\n", "        }\n", "    )\n", "]\n"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["quest_metrics = []\n", "for quest_metric in quest_metric_candidates:\n", "    quest_metric_copy = copy.deepcopy(quest_metric)\n", "    selected = False\n", "    for quest_measure in quest_metric.fields:\n", "        if quest_measure in evaluation_quest_metrics:\n", "            selected = True\n", "        else:\n", "            quest_metric_copy.fields.pop(quest_measure)\n", "    if selected:\n", "        quest_metrics.append(quest_metric_copy)\n"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["quest_metric_sql_template = \"\"\"\n", "    (SELECT\n", "        ghost_user_id,\n", "        {sum_metrics}\n", "    FROM\n", "        `sc-portal.quest.{metric_table}_20*`\n", "    WHERE\n", "        PARSE_DATE(\"%Y%m%d\", CONCAT('20', _TABLE_SUFFIX))\n", "            BETWEEN DATE_SUB(PARSE_DATE('%Y%m%d', '{ds}'), INTERVAL 6 DAY)\n", "            AND PARSE_DATE('%Y%m%d', '{ds}')\n", "    GROUP BY\n", "        ghost_user_id)\n", "\"\"\"\n", "\n", "\n", "quest_metric_sql = \"\"\"\n", "    SELECT\n", "      *\n", "    FROM\n", "      {joined_quest_metrics_sql}\n", "\"\"\".format(\n", "    joined_quest_metrics_sql=\"\\n\".join([\n", "        (\"{table}\" if index == 0 else \"FULL OUTER JOIN {table} USING(ghost_user_id)\").format(\n", "            table=quest_metric_sql_template.format(\n", "                sum_metrics=quest_metric.sql_in_sum,\n", "                metric_table=quest_metric.table,\n", "                ds=ds\n", "            )\n", "        ) for index, quest_metric in enumerate(quest_metrics)\n", "    ])\n", ")\n", "# print(quest_metric_sql)\n"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["if len(quest_metrics) == 0:\n", "    metric_sql = \"\"\"\n", "    SELECT\n", "        ghost_user_id,\n", "        {outer_sum_metrics}\n", "      FROM (\n", "        SELECT\n", "          ghost_user_id,\n", "          {inner_sum_metrics}\n", "        FROM\n", "          `sc-analytics.report_app.dau_user_country_20*`\n", "        WHERE\n", "          PARSE_DATE(\"%Y%m%d\", CONCAT('20', _TABLE_SUFFIX))\n", "            BETWEEN DATE_SUB(PARSE_DATE('%Y%m%d', '{ds}'), INTERVAL 6 DAY)\n", "            AND PARSE_DATE('%Y%m%d', '{ds}')\n", "          AND top_device IN ({os_types})\n", "        GROUP BY ghost_user_id, event_date\n", "      )\n", "      GROUP BY ghost_user_id\n", "    \"\"\".format(\n", "        inner_sum_metrics=\",\\n\".join(\n", "            \"{0} AS {1}\".format(\n", "                metric.sql_in_sum,\n", "                metric.output_field_name,\n", "            ) for metric in metrics\n", "        ),\n", "        outer_sum_metrics=\",\\n\".join(\n", "            \"COALESCE(SUM({0}), 0) AS {0}\".format(\n", "                metric.output_field_name\n", "            ) for metric in metrics\n", "        ),\n", "        ds=ds,\n", "        os_types=\", \".join(\"'{}'\".format(os) for os in os_types)\n", "    )\n", "else:\n", "    metric_sql = \"\"\"\n", "        SELECT\n", "          dau_metrics.*,\n", "          {outer_quest_metrics}\n", "        FROM\n", "          (SELECT\n", "            ghost_user_id,\n", "            {outer_sum_metrics}\n", "          FROM (\n", "            SELECT\n", "              ghost_user_id,\n", "              {inner_sum_metrics}\n", "            FROM\n", "              `sc-analytics.report_app.dau_user_country_*`\n", "            WHERE\n", "              PARSE_DATE(\"%Y%m%d\", CONCAT('20', _TABLE_SUFFIX))\n", "                BETWEEN DATE_SUB(PARSE_DATE('%Y%m%d', '{ds}'), INTERVAL 6 DAY)\n", "                AND PARSE_DATE('%Y%m%d', '{ds}')\n", "              AND top_device IN ({os_types})\n", "            GROUP BY ghost_user_id, event_date\n", "          )\n", "          GROUP BY ghost_user_id) dau_metrics\n", "        LEFT JOIN\n", "          ({quest_metric_sql}) quest_metrics\n", "        USING(ghost_user_id)\n", "    \"\"\".format(\n", "        inner_sum_metrics=\",\\n\".join(\n", "            \"{0} AS {1}\".format(\n", "                metric.sql_in_sum,\n", "                metric.output_field_name,\n", "            ) for metric in metrics\n", "        ),\n", "        outer_sum_metrics=\",\\n\".join(\n", "            \"COALESCE(SUM({0}), 0) AS {0}\".format(\n", "                metric.output_field_name\n", "            ) for metric in metrics\n", "        ),\n", "        ds=ds,\n", "        os_types=\", \".join(\"'{}'\".format(os) for os in os_types),\n", "        quest_metric_sql=quest_metric_sql,\n", "        outer_quest_metrics=\",\\n\".join([\n", "            quest_metric.output_field_names for quest_metric in quest_metrics\n", "        ])\n", "    )\n"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["when_thens = []\n", "\n", "cumulative_sids = 0\n", "for index, split in enumerate(traffic_splits, 1):\n", "    cumulative_sids += int(split * 2)  # Each 1% = 2 SIDs\n", "    when_thens.append(\n", "        'WHEN {{field_name}} <= {group_threshold} THEN \"GID_{group_no} ({pct}%)\"'.format(\n", "            group_threshold=cumulative_sids,\n", "            group_no=\"{:02d}\".format(index),\n", "            pct=int(split)\n", "        )\n", "    )\n", "\n", "when_thens_template = \" \".join(when_thens)\n"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [], "source": ["sql_split_to_groups = \",\\n\".join(\n", "    \"CASE {when_thens} END AS assignment_{index:04d}\".format(\n", "        when_thens=when_thens_template.format(field_name=\"seed_{index:04d}\".format(index=index)),\n", "        index=index\n", "    ) for index in range(total_seeds)\n", ")\n", "\n", "# Join slices with metrics\n", "sql_join_slice_with_metrics = \"\"\"\n", "    SELECT\n", "      users.*,\n", "      {metrics},\n", "      {assignments}\n", "    FROM\n", "      `{user_table}` AS users\n", "    INNER JOIN\n", "      ({metric_sql}) metrics\n", "    ON users.ghost_user_id = metrics.ghost_user_id\n", "\"\"\".format(\n", "    metrics=\",\\n\".join(\n", "        [metric.output_field_name for metric in metrics] +\n", "        [quest_metric.select_field_names for quest_metric in quest_metrics]\n", "    ),\n", "    assignments=sql_split_to_groups,\n", "    user_table=\"{}.{}.{}\".format(\n", "        project,\n", "        temp_dataset,\n", "        temp_ab_user_split_table_name,\n", "    ),\n", "    metric_sql=metric_sql\n", ")\n"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [], "source": ["joined_table_name = \"ab_study_seed_finder_metrics_{}\".format(run_id)\n", "\n", "create_joined_table = gbq.submit_sync_query(\n", "    sql_join_slice_with_metrics,\n", "    project_id=project,\n", "    dest_dataset_id=temp_dataset,\n", "    dest_table_name=joined_table_name,\n", "    write_disposition='WRITE_TRUNCATE',\n", "    dialect='standard',\n", "    priority=BQ_PRIORITY,\n", ")\n"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [], "source": ["# Summarize results\n", "\n", "\n", "def get_sql_summarize(indices):\n", "    sql_summarize_templates = \"\"\"\n", "      (SELECT\n", "        \"{seed_string}\" AS seed,\n", "        assignment_{index:04d} AS assignment,\n", "        COUNT(*) AS user_count,\n", "        {agg_metrics}\n", "      FROM\n", "        `{joined_table}`\n", "      GROUP BY 1, 2)\n", "    \"\"\"\n", "\n", "    sql_summarize = \"SELECT * FROM {}\".format(\n", "        \"\\nUNION ALL\\n\".join(\n", "            sql_summarize_templates.format(\n", "                seed_string=get_seed(index),\n", "                index=index,\n", "                joined_table=\"{}.{}.{}\".format(\n", "                    project, temp_dataset, joined_table_name\n", "                ),\n", "                agg_metrics=\",\\n\".join(\n", "                    \"CAST(SUM(IF(IS_NAN({0}), 0, {0})) AS FLOAT64) AS {0}_sum, \"\n", "                    \"CAST(SUM(POW(IF(IS_NAN({0}), 0, {0}), 2)) AS FLOAT64) AS {0}_ssq\".format(\n", "                        metric.output_field_name\n", "                    )\n", "                    for metric in metrics\n", "                )\n", "                + (\n", "                    \",\\n{}\".format(\n", "                        \",\\n\".join(\n", "                            [\n", "                                quest_metric.aggregate_fields\n", "                                for quest_metric in quest_metrics\n", "                            ]\n", "                        )\n", "                    )\n", "                    if quest_metrics\n", "                    else \"\"\n", "                ),\n", "            )\n", "            for index in indices\n", "        )\n", "    )\n", "    return sql_summarize\n"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["chunk_size = 19\n", "index_chunks = [\n", "    list(range(start, min(total_seeds, start+chunk_size)))\n", "    for start in range(0, total_seeds, chunk_size)\n", "]\n", "\n", "results = gbq.batch_read_gbq(\n", "    [get_sql_summarize(indices) for indices in index_chunks],\n", "    project_id=project,\n", "    dialect='standard',\n", "    priority=BQ_PRIORITY,\n", "    parallel=BQ_PRIORITY == \"BATCH\",\n", "    use_bqstorage_api=True\n", ")\n"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": ["# concat\n", "df_results = pd.concat(results).sort_values(['seed', 'assignment'])\n", "\n", "# pivot\n", "df_results_1 = pd.melt(\n", "    df_results, id_vars=['seed', 'assignment', 'user_count'], var_name='metric'\n", ")\n", "\n", "extracted = df_results_1.metric.str.extract(\n", "    r\"^(\\w+)_(sum|ssq)$\",\n", "    expand=True\n", ")\n", "extracted.columns = ['metric', 'stat']\n", "\n", "df_results_2 = pd.concat([df_results_1.drop('metric', axis=1), extracted], axis=1)\n", "\n", "df_results_3 = df_results_2.pivot_table(\n", "    index=['seed', 'assignment', 'user_count', 'metric'],\n", "    columns='stat',\n", "    values='value',\n", "    aggfunc=lambda x: x if len(x) == 1 else None\n", ").reset_index(None)\n", "\n", "metric_name_mapping = {metric.output_field_name: metric.name for metric in metrics}\n", "for quest_metric in quest_metrics:\n", "    metric_name_mapping.update(quest_metric.fields)\n", "\n", "df_results_3['metric'] = (\n", "    df_results_3.metric.replace(metric_name_mapping)\n", ")\n", "\n", "df_results_3['metric'] = pd.Categorical(\n", "    df_results_3['metric'],\n", "    categories=list(metric_name_mapping.values()),\n", "    ordered=True,\n", ")\n"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [], "source": ["def aggregator(\n", "    input_df,\n", "    expected_sample_props=(traffic_splits / np.sum(traffic_splits)).tolist(),\n", "):\n", "    output_df = input_df.copy()\n", "\n", "    overall_avg = output_df[\"sum\"].sum() / output_df.user_count.sum()\n", "    output_df[\"overall_avg\"] = overall_avg\n", "    output_df[\"avg\"] = output_df[\"sum\"] / output_df.user_count\n", "    output_df[\"pct_diff\"] = 100 * (output_df.avg / overall_avg - 1)\n", "    try:\n", "        pvalue_anova, _ = abtest.stat_funcs.anova_sample_mean(\n", "            output_df[\"user_count\"],\n", "            output_df[\"sum\"],\n", "            output_df[\"ssq\"],\n", "        )\n", "    except:\n", "        print(output_df)\n", "        raise\n", "\n", "    pvalue_sample_size, _ = abtest.stat_funcs.chisq_test_sample_size(\n", "        output_df[\"user_count\"],\n", "        expected_props=expected_sample_props,\n", "    )\n", "\n", "    output_df[\"p_mean\"] = pvalue_anova\n", "    output_df[\"p_sample_size\"] = pvalue_sample_size\n", "    return output_df\n", "\n", "\n", "# aggregator(df_results_3.query(\"metric == 'app_application_open' and seed == 'PU_DF_PUBLISHER_DELTA_FETCH'\"))\n"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/47/b56b714j7nx18mkf3kyfdwy80000gn/T/ipykernel_14052/991910591.py:1: FutureWarning: Not prepending group keys to the result index of transform-like apply. In the future, the group keys will be included in the index, regardless of whether the applied function returns a like-indexed object.\n", "To preserve the previous behavior, use\n", "\n", "\t>>> .groupby(..., group_keys=False)\n", "\n", "To adopt the future behavior and silence this warning, use \n", "\n", "\t>>> .groupby(..., group_keys=True)\n", "  df_final_results = df_results_3.groupby(['seed', 'metric']).apply(aggregator).reset_index(drop=True)\n"]}], "source": ["df_final_results = df_results_3.groupby(['seed', 'metric']).apply(aggregator).reset_index(drop=True)\n"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [], "source": ["# Get the minimum p-values across all metrics for each seed\n", "\n", "df_pvalue = df_final_results.groupby(['seed']).aggregate(\n", "    {\"p_mean\": np.min, \"p_sample_size\": np.min}\n", ").reset_index()\n"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [], "source": ["# Pick the seeds with the largest minimum p-values\n", "\n", "df_default_seed = df_pvalue.query(\"seed == '{}'\".format(default_seed))\n", "best_seeds = df_pvalue.sort_values(['p_mean'], ascending=False).head(7)\n", "default_seed_rank = np.sum(df_default_seed.p_mean.iloc[0] <= df_pvalue.p_mean)\n"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "<p>A/B console now has built-in seed finder support.\n", "See the <a target=\"_blank\"\n", "href=\"https://wiki.sc-corp.net/pages/viewpage.action?pageId=92526861#A/BStudySeeds-SeedCalculationStrategies(withinABConsole)\">\n", "wiki</a> page for more details. You should use A/B console's builtin support unless you need to customize your targeting criteria or evaluation metrics.</p>\n", "\n", "<p>With your provided configuration, we have computed 25 seed strings\n", "and chosen the best seed strings for you. The top five best seed strings are (in order of better to worse):\n", "<span style='color:blue;'>layer1_20250604_0010</span>, <span style='color:blue;'>layer1_$20250604</span>, <span style='color:blue;'>layer1_20250604_0005</span>, <span style='color:blue;'>layer1_20250604_0017</span>, <span style='color:blue;'>layer1_20250604_0019</span>, <span style='color:blue;'>layer1_20250604_0016</span>, <span style='color:blue;'>layer1_20250604_0024</span>. <strong>Use these strings in A/B console's Seed String field.</strong></p>\n", "\n", "\n", "<p>The seed strings are evaluated using 17 important metrics in the seven-day period ending on 20250604.\n", "For each of them, we used the one-way\n", "ANOVA test of equal means across all groups. A small p-value indicates that the average per user values are different\n", "among the groups. Thereby for each seed, we have 17 such p-values and then take the minimum of all p-values as a\n", "measure of the seed quality. The larger this minimum p-value, the more balances the groups are.</p>\n", "\n", "\n", "<p>The best seed resulted in minimum p-value of 0.1697, while the default seed string\n", "(the layer - layer1_$20250604) yielded a p-value of 0.1692. The default seed string\n", "is the <strong>No.2 best of out of the 25</strong> strings we tried plus itself. See the table below for how well\n", "the groups are balanced for these metrics.</p>\n", "\n", "<p> Your configurations: </p>\n", "<ul>\n", "<li>Layer: BUDGET_UNAWARE_AB_LAYER_1</li>\n", "<li>Evaluation Date: 20250604</li>\n", "<li>Traffic_splits: (10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0, 10.0)</li>\n", "<li>OS Types: ('iOS', 'Android')</li>\n", "<li>Countries: all</li>\n", "<li>Targeted cohort: L7 app_application_open_count active days >= 3 </li>\n", "</ul>\n", "\n", "\n", "<p>\n", "<small>Debug info: <a href=\"https://bigquery.cloud.google.com/table/config-optimization:ziqiao.ab_study_mbu_seed_finder_slices_BUDGET_UNAWARE_AB_LAYER_1_20250604_uuid_10_10_10_10_10_10_10_10_10_10_6bb7b4654f208d62074273fd\" target=\"_blank\">step 1 bq table</a>, <a href=\"https://bigquery.cloud.google.com/table/config-optimization:ziqiao.ab_study_seed_finder_metrics_BUDGET_UNAWARE_AB_LAYER_1_20250604_uuid_10_10_10_10_10_10_10_10_10_10_6bb7b4654f208d62074273fd\" target=\"_blank\">step 2 bq table</a>. NB: When doing the evaluation, we only include l7 &#x2265; 3 users.</small>\n", "</p>\n"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["html_template = \"\"\"\n", "<p>A/B console now has built-in seed finder support.\n", "See the <a target=\"_blank\"\n", "href=\"https://wiki.sc-corp.net/pages/viewpage.action?pageId=92526861#A/BStudySeeds-SeedCalculationStrategies(withinABConsole)\">\n", "wiki</a> page for more details. You should use A/B console's builtin support unless you need to customize your targeting criteria or evaluation metrics.</p>\n", "\n", "<p>With your provided configuration, we have computed {total_seeds} seed strings\n", "and chosen the best seed strings for you. The top five best seed strings are (in order of better to worse):\n", "{best_seed_strings}. <strong>Use these strings in A/B console's Seed String field.</strong></p>\n", "\n", "\n", "<p>The seed strings are evaluated using {metric_count} important metrics in the seven-day period ending on {ds}.\n", "For each of them, we used the one-way\n", "ANOVA test of equal means across all groups. A small p-value indicates that the average per user values are different\n", "among the groups. Thereby for each seed, we have {metric_count} such p-values and then take the minimum of all p-values as a\n", "measure of the seed quality. The larger this minimum p-value, the more balances the groups are.</p>\n", "\n", "\n", "<p>The best seed resulted in minimum p-value of {best_seed_p_value:.4f}, while the default seed string\n", "(the layer - {default_seed_str}) yielded a p-value of {default_seed_p_value:.4f}. The default seed string\n", "is the <strong>No.{default_seed_rank} best of out of the {total_seeds}</strong> strings we tried plus itself. See the table below for how well\n", "the groups are balanced for these metrics.</p>\n", "\n", "<p> Your configurations: </p>\n", "<ul>\n", "<li>Layer: {layer_name}</li>\n", "<li>Evaluation Date: {ds}</li>\n", "<li>Traffic_splits: {traffic_splits}</li>\n", "<li>OS Types: {os_types}</li>\n", "<li>Countries: {countries}</li>\n", "<li>Targeted cohort: L7 {cohort_definition_metric} active days >= {cohort_definition_min_active_days} </li>\n", "</ul>\n", "\n", "\n", "<p>\n", "<small>Debug info: {temp_table_1}, {temp_table_2}. NB: When doing the evaluation, we only include l7 &#x2265; 3 users.</small>\n", "</p>\n", "\"\"\"\n", "\n", "\n", "def get_url(link_text, link):\n", "    return '<a href=\"{}\" target=\"_blank\">{}</a>'.format(link, link_text)\n", "\n", "\n", "def get_bq_link(table):\n", "    return \"https://bigquery.cloud.google.com/table/{}:{}.{}\".format(\n", "        table.project,\n", "        table.dataset_id,\n", "        table.table_id,\n", "    )\n", "\n", "\n", "display(\n", "    HTML(\n", "        html_template.format(\n", "            total_seeds=total_seeds - 1,\n", "            best_seed_strings=\", \".join(\n", "                \"<span style='color:blue;'>{}</span>\".format(seed)\n", "                for seed in best_seeds.seed.tolist()\n", "            ),\n", "            metric_count=len(metrics),\n", "            best_seed_p_value=best_seeds.p_mean.iloc[0],\n", "            default_seed_p_value=df_default_seed.p_mean.iloc[0],\n", "            default_seed_rank=default_seed_rank,\n", "            default_seed_str=default_seed,\n", "            layer_name=LayerType.Name(layer_enum_number),\n", "            ds=ds,\n", "            traffic_splits=tuple(traffic_splits),\n", "            os_types=tuple(os_types),\n", "            countries=tuple(countries) if countries else \"all\",\n", "            cohort_definition_metric=cohort_definition_metric,\n", "            cohort_definition_min_active_days=cohort_definition_min_active_days,\n", "            temp_table_1=get_url(\n", "                \"step 1 bq table\",\n", "                get_bq_link(create_temp_ab_user_split_job.destination),\n", "            ),\n", "            temp_table_2=get_url(\n", "                \"step 2 bq table\", get_bq_link(create_joined_table.destination)\n", "            ),\n", "        )\n", "    )\n", ")\n"]}, {"cell_type": "code", "execution_count": 60, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<h3>The best seed is <strong style='color:blue;'>layer1_20250604_0010</strong></h3>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p>The table below displays the percent difference between the group's average and the overall average per user value for the metric.</p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>stat</th>\n", "      <th colspan=\"10\" halign=\"left\">percent_difference</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>seed</th>\n", "      <th colspan=\"10\" halign=\"left\">layer1_20250604_0010</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>assignment</th>\n", "      <th>GID_01 (10%)</th>\n", "      <th>GID_02 (10%)</th>\n", "      <th>GID_03 (10%)</th>\n", "      <th>GID_04 (10%)</th>\n", "      <th>GID_05 (10%)</th>\n", "      <th>GID_06 (10%)</th>\n", "      <th>GID_07 (10%)</th>\n", "      <th>GID_08 (10%)</th>\n", "      <th>GID_09 (10%)</th>\n", "      <th>GID_10 (10%)</th>\n", "    </tr>\n", "    <tr>\n", "      <th>metric</th>\n", "      <th>p_mean_anova</th>\n", "      <th>overall_avg</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>App Active Days</th>\n", "      <th>0.5198</th>\n", "      <th>5.75</th>\n", "      <td>-0.01% (5.75)</td>\n", "      <td>-0.01% (5.75)</td>\n", "      <td>0.01% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>-0.01% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>-0.00% (5.75)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Chat Send</th>\n", "      <th>0.7123</th>\n", "      <th>140.7</th>\n", "      <td>0.05% (140.8)</td>\n", "      <td>-0.06% (140.6)</td>\n", "      <td>0.02% (140.8)</td>\n", "      <td>0.05% (140.8)</td>\n", "      <td>-0.07% (140.6)</td>\n", "      <td>-0.00% (140.7)</td>\n", "      <td>0.05% (140.8)</td>\n", "      <td>-0.04% (140.7)</td>\n", "      <td>0.07% (140.8)</td>\n", "      <td>-0.07% (140.6)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Chat View</th>\n", "      <th>0.2150</th>\n", "      <th>193.6</th>\n", "      <td>-0.04% (193.5)</td>\n", "      <td>-0.12% (193.4)</td>\n", "      <td>0.04% (193.7)</td>\n", "      <td>0.12% (193.9)</td>\n", "      <td>-0.14% (193.3)</td>\n", "      <td>0.00% (193.6)</td>\n", "      <td>0.11% (193.8)</td>\n", "      <td>-0.09% (193.4)</td>\n", "      <td>0.15% (193.9)</td>\n", "      <td>-0.03% (193.6)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap Create</th>\n", "      <th>0.6607</th>\n", "      <th>66.39</th>\n", "      <td>-0.08% (66.33)</td>\n", "      <td>-0.08% (66.34)</td>\n", "      <td>-0.05% (66.35)</td>\n", "      <td>0.07% (66.43)</td>\n", "      <td>0.05% (66.42)</td>\n", "      <td>0.07% (66.43)</td>\n", "      <td>0.03% (66.41)</td>\n", "      <td>-0.03% (66.37)</td>\n", "      <td>0.04% (66.41)</td>\n", "      <td>-0.04% (66.36)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap Send</th>\n", "      <th>0.5426</th>\n", "      <th>40.84</th>\n", "      <td>-0.13% (40.78)</td>\n", "      <td>-0.05% (40.81)</td>\n", "      <td>-0.08% (40.80)</td>\n", "      <td>0.09% (40.87)</td>\n", "      <td>0.07% (40.86)</td>\n", "      <td>0.14% (40.89)</td>\n", "      <td>-0.02% (40.83)</td>\n", "      <td>-0.06% (40.81)</td>\n", "      <td>0.09% (40.87)</td>\n", "      <td>-0.07% (40.81)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap View</th>\n", "      <th>0.7277</th>\n", "      <th>162.3</th>\n", "      <td>-0.05% (162.2)</td>\n", "      <td>-0.02% (162.3)</td>\n", "      <td>0.01% (162.3)</td>\n", "      <td>0.04% (162.4)</td>\n", "      <td>0.07% (162.4)</td>\n", "      <td>0.02% (162.4)</td>\n", "      <td>-0.01% (162.3)</td>\n", "      <td>-0.02% (162.3)</td>\n", "      <td>0.04% (162.4)</td>\n", "      <td>-0.08% (162.2)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap Save</th>\n", "      <th>0.3583</th>\n", "      <th>11.40</th>\n", "      <td>-0.04% (11.40)</td>\n", "      <td>-0.03% (11.40)</td>\n", "      <td>0.03% (11.40)</td>\n", "      <td>0.06% (11.41)</td>\n", "      <td>0.03% (11.41)</td>\n", "      <td>0.05% (11.41)</td>\n", "      <td>0.02% (11.40)</td>\n", "      <td>-0.02% (11.40)</td>\n", "      <td>-0.06% (11.39)</td>\n", "      <td>-0.05% (11.40)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DF Non-Friend Story View</th>\n", "      <th>0.9737</th>\n", "      <th>27.49</th>\n", "      <td>-0.03% (27.48)</td>\n", "      <td>-0.02% (27.49)</td>\n", "      <td>0.07% (27.51)</td>\n", "      <td>0.01% (27.49)</td>\n", "      <td>-0.07% (27.47)</td>\n", "      <td>0.06% (27.51)</td>\n", "      <td>0.01% (27.49)</td>\n", "      <td>-0.06% (27.48)</td>\n", "      <td>-0.02% (27.48)</td>\n", "      <td>0.03% (27.50)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DF Non-Friend Story View Time</th>\n", "      <th>0.5109</th>\n", "      <th>795.1</th>\n", "      <td>-0.09% (794.4)</td>\n", "      <td>-0.17% (793.7)</td>\n", "      <td>0.01% (795.2)</td>\n", "      <td>0.11% (796.0)</td>\n", "      <td>0.04% (795.4)</td>\n", "      <td>0.01% (795.2)</td>\n", "      <td>-0.02% (794.9)</td>\n", "      <td>-0.05% (794.7)</td>\n", "      <td>0.02% (795.3)</td>\n", "      <td>0.13% (796.1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Friend Story View</th>\n", "      <th>0.5241</th>\n", "      <th>141.5</th>\n", "      <td>0.07% (141.6)</td>\n", "      <td>0.01% (141.5)</td>\n", "      <td>-0.06% (141.4)</td>\n", "      <td>0.04% (141.5)</td>\n", "      <td>-0.12% (141.3)</td>\n", "      <td>-0.05% (141.4)</td>\n", "      <td>0.06% (141.5)</td>\n", "      <td>-0.02% (141.4)</td>\n", "      <td>0.09% (141.6)</td>\n", "      <td>-0.03% (141.4)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Friend Story View Time</th>\n", "      <th>0.8322</th>\n", "      <th>373.1</th>\n", "      <td>0.10% (373.5)</td>\n", "      <td>0.01% (373.2)</td>\n", "      <td>-0.05% (372.9)</td>\n", "      <td>0.05% (373.3)</td>\n", "      <td>-0.07% (372.9)</td>\n", "      <td>-0.02% (373.1)</td>\n", "      <td>-0.01% (373.1)</td>\n", "      <td>-0.02% (373.1)</td>\n", "      <td>0.04% (373.3)</td>\n", "      <td>-0.02% (373.0)</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON>s Send</th>\n", "      <th>0.8913</th>\n", "      <th>7.08</th>\n", "      <td>-0.04% (7.08)</td>\n", "      <td>-0.15% (7.07)</td>\n", "      <td>-0.02% (7.08)</td>\n", "      <td>0.11% (7.09)</td>\n", "      <td>0.05% (7.08)</td>\n", "      <td>0.04% (7.08)</td>\n", "      <td>0.02% (7.08)</td>\n", "      <td>-0.09% (7.07)</td>\n", "      <td>0.06% (7.08)</td>\n", "      <td>0.02% (7.08)</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON> Swipe</th>\n", "      <th>0.5584</th>\n", "      <th>132.5</th>\n", "      <td>0.00% (132.5)</td>\n", "      <td>-0.00% (132.5)</td>\n", "      <td>-0.06% (132.4)</td>\n", "      <td>0.07% (132.6)</td>\n", "      <td>-0.00% (132.5)</td>\n", "      <td>0.02% (132.5)</td>\n", "      <td>-0.01% (132.5)</td>\n", "      <td>0.00% (132.5)</td>\n", "      <td>-0.05% (132.4)</td>\n", "      <td>0.03% (132.5)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Spotlight Story View</th>\n", "      <th>0.6455</th>\n", "      <th>88.39</th>\n", "      <td>0.09% (88.48)</td>\n", "      <td>0.04% (88.43)</td>\n", "      <td>-0.11% (88.30)</td>\n", "      <td>-0.01% (88.38)</td>\n", "      <td>0.10% (88.48)</td>\n", "      <td>-0.02% (88.38)</td>\n", "      <td>0.04% (88.43)</td>\n", "      <td>-0.13% (88.28)</td>\n", "      <td>0.07% (88.45)</td>\n", "      <td>-0.07% (88.33)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Spotlight Story View Time</th>\n", "      <th>0.1697</th>\n", "      <th>570.9</th>\n", "      <td>0.24% (572.3)</td>\n", "      <td>-0.10% (570.3)</td>\n", "      <td>-0.16% (570.0)</td>\n", "      <td>0.01% (570.9)</td>\n", "      <td>0.02% (571.0)</td>\n", "      <td>0.12% (571.6)</td>\n", "      <td>-0.04% (570.7)</td>\n", "      <td>-0.04% (570.7)</td>\n", "      <td>0.16% (571.8)</td>\n", "      <td>-0.18% (569.9)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Story Snap Post</th>\n", "      <th>0.5742</th>\n", "      <th>2.45</th>\n", "      <td>0.14% (2.45)</td>\n", "      <td>-0.38% (2.44)</td>\n", "      <td>-0.24% (2.44)</td>\n", "      <td>0.05% (2.45)</td>\n", "      <td>0.12% (2.45)</td>\n", "      <td>0.06% (2.45)</td>\n", "      <td>0.16% (2.45)</td>\n", "      <td>-0.04% (2.45)</td>\n", "      <td>-0.08% (2.45)</td>\n", "      <td>0.23% (2.46)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Story Snap View</th>\n", "      <th>0.8324</th>\n", "      <th>508.3</th>\n", "      <td>-0.03% (508.1)</td>\n", "      <td>-0.09% (507.8)</td>\n", "      <td>-0.02% (508.2)</td>\n", "      <td>0.03% (508.4)</td>\n", "      <td>0.03% (508.4)</td>\n", "      <td>0.06% (508.6)</td>\n", "      <td>0.02% (508.4)</td>\n", "      <td>-0.08% (507.9)</td>\n", "      <td>0.05% (508.5)</td>\n", "      <td>0.02% (508.4)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["stat                                                     percent_difference  \\\n", "seed                                                   layer1_20250604_0010   \n", "assignment                                                     GID_01 (10%)   \n", "metric                        p_mean_anova overall_avg                        \n", "App Active Days               0.5198       5.75               -0.01% (5.75)   \n", "Chat Send                     0.7123       140.7              0.05% (140.8)   \n", "Chat View                     0.2150       193.6             -0.04% (193.5)   \n", "Direct Snap Create            0.6607       66.39             -0.08% (66.33)   \n", "Direct Snap Send              0.5426       40.84             -0.13% (40.78)   \n", "Direct Snap View              0.7277       162.3             -0.05% (162.2)   \n", "Direct Snap Save              0.3583       11.40             -0.04% (11.40)   \n", "DF Non-Friend Story View      0.9737       27.49             -0.03% (27.48)   \n", "DF Non-Friend Story View Time 0.5109       795.1             -0.09% (794.4)   \n", "Friend Story View             0.5241       141.5              0.07% (141.6)   \n", "Friend Story View Time        0.8322       373.1              0.10% (373.5)   \n", "Lens Send                     0.8913       7.08               -0.04% (7.08)   \n", "Lens Swipe                    0.5584       132.5              0.00% (132.5)   \n", "Spotlight Story View          0.6455       88.39              0.09% (88.48)   \n", "Spotlight Story View Time     0.1697       570.9              0.24% (572.3)   \n", "Story Snap Post               0.5742       2.45                0.14% (2.45)   \n", "Story Snap View               0.8324       508.3             -0.03% (508.1)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_02 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.5198       5.75          -0.01% (5.75)   \n", "Chat Send                     0.7123       140.7        -0.06% (140.6)   \n", "Chat View                     0.2150       193.6        -0.12% (193.4)   \n", "Direct Snap Create            0.6607       66.39        -0.08% (66.34)   \n", "Direct Snap Send              0.5426       40.84        -0.05% (40.81)   \n", "Direct Snap View              0.7277       162.3        -0.02% (162.3)   \n", "Direct Snap Save              0.3583       11.40        -0.03% (11.40)   \n", "DF Non-Friend Story View      0.9737       27.49        -0.02% (27.49)   \n", "DF Non-Friend Story View Time 0.5109       795.1        -0.17% (793.7)   \n", "Friend Story View             0.5241       141.5         0.01% (141.5)   \n", "Friend Story View Time        0.8322       373.1         0.01% (373.2)   \n", "Lens Send                     0.8913       7.08          -0.15% (7.07)   \n", "Lens Swipe                    0.5584       132.5        -0.00% (132.5)   \n", "Spotlight Story View          0.6455       88.39         0.04% (88.43)   \n", "Spotlight Story View Time     0.1697       570.9        -0.10% (570.3)   \n", "Story Snap Post               0.5742       2.45          -0.38% (2.44)   \n", "Story Snap View               0.8324       508.3        -0.09% (507.8)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_03 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.5198       5.75           0.01% (5.75)   \n", "Chat Send                     0.7123       140.7         0.02% (140.8)   \n", "Chat View                     0.2150       193.6         0.04% (193.7)   \n", "Direct Snap Create            0.6607       66.39        -0.05% (66.35)   \n", "Direct Snap Send              0.5426       40.84        -0.08% (40.80)   \n", "Direct Snap View              0.7277       162.3         0.01% (162.3)   \n", "Direct Snap Save              0.3583       11.40         0.03% (11.40)   \n", "DF Non-Friend Story View      0.9737       27.49         0.07% (27.51)   \n", "DF Non-Friend Story View Time 0.5109       795.1         0.01% (795.2)   \n", "Friend Story View             0.5241       141.5        -0.06% (141.4)   \n", "Friend Story View Time        0.8322       373.1        -0.05% (372.9)   \n", "Lens Send                     0.8913       7.08          -0.02% (7.08)   \n", "Lens Swipe                    0.5584       132.5        -0.06% (132.4)   \n", "Spotlight Story View          0.6455       88.39        -0.11% (88.30)   \n", "Spotlight Story View Time     0.1697       570.9        -0.16% (570.0)   \n", "Story Snap Post               0.5742       2.45          -0.24% (2.44)   \n", "Story Snap View               0.8324       508.3        -0.02% (508.2)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_04 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.5198       5.75           0.00% (5.75)   \n", "Chat Send                     0.7123       140.7         0.05% (140.8)   \n", "Chat View                     0.2150       193.6         0.12% (193.9)   \n", "Direct Snap Create            0.6607       66.39         0.07% (66.43)   \n", "Direct Snap Send              0.5426       40.84         0.09% (40.87)   \n", "Direct Snap View              0.7277       162.3         0.04% (162.4)   \n", "Direct Snap Save              0.3583       11.40         0.06% (11.41)   \n", "DF Non-Friend Story View      0.9737       27.49         0.01% (27.49)   \n", "DF Non-Friend Story View Time 0.5109       795.1         0.11% (796.0)   \n", "Friend Story View             0.5241       141.5         0.04% (141.5)   \n", "Friend Story View Time        0.8322       373.1         0.05% (373.3)   \n", "Lens Send                     0.8913       7.08           0.11% (7.09)   \n", "Lens Swipe                    0.5584       132.5         0.07% (132.6)   \n", "Spotlight Story View          0.6455       88.39        -0.01% (88.38)   \n", "Spotlight Story View Time     0.1697       570.9         0.01% (570.9)   \n", "Story Snap Post               0.5742       2.45           0.05% (2.45)   \n", "Story Snap View               0.8324       508.3         0.03% (508.4)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_05 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.5198       5.75           0.00% (5.75)   \n", "Chat Send                     0.7123       140.7        -0.07% (140.6)   \n", "Chat View                     0.2150       193.6        -0.14% (193.3)   \n", "Direct Snap Create            0.6607       66.39         0.05% (66.42)   \n", "Direct Snap Send              0.5426       40.84         0.07% (40.86)   \n", "Direct Snap View              0.7277       162.3         0.07% (162.4)   \n", "Direct Snap Save              0.3583       11.40         0.03% (11.41)   \n", "DF Non-Friend Story View      0.9737       27.49        -0.07% (27.47)   \n", "DF Non-Friend Story View Time 0.5109       795.1         0.04% (795.4)   \n", "Friend Story View             0.5241       141.5        -0.12% (141.3)   \n", "Friend Story View Time        0.8322       373.1        -0.07% (372.9)   \n", "Lens Send                     0.8913       7.08           0.05% (7.08)   \n", "Lens Swipe                    0.5584       132.5        -0.00% (132.5)   \n", "Spotlight Story View          0.6455       88.39         0.10% (88.48)   \n", "Spotlight Story View Time     0.1697       570.9         0.02% (571.0)   \n", "Story Snap Post               0.5742       2.45           0.12% (2.45)   \n", "Story Snap View               0.8324       508.3         0.03% (508.4)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_06 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.5198       5.75           0.00% (5.75)   \n", "Chat Send                     0.7123       140.7        -0.00% (140.7)   \n", "Chat View                     0.2150       193.6         0.00% (193.6)   \n", "Direct Snap Create            0.6607       66.39         0.07% (66.43)   \n", "Direct Snap Send              0.5426       40.84         0.14% (40.89)   \n", "Direct Snap View              0.7277       162.3         0.02% (162.4)   \n", "Direct Snap Save              0.3583       11.40         0.05% (11.41)   \n", "DF Non-Friend Story View      0.9737       27.49         0.06% (27.51)   \n", "DF Non-Friend Story View Time 0.5109       795.1         0.01% (795.2)   \n", "Friend Story View             0.5241       141.5        -0.05% (141.4)   \n", "Friend Story View Time        0.8322       373.1        -0.02% (373.1)   \n", "Lens Send                     0.8913       7.08           0.04% (7.08)   \n", "Lens Swipe                    0.5584       132.5         0.02% (132.5)   \n", "Spotlight Story View          0.6455       88.39        -0.02% (88.38)   \n", "Spotlight Story View Time     0.1697       570.9         0.12% (571.6)   \n", "Story Snap Post               0.5742       2.45           0.06% (2.45)   \n", "Story Snap View               0.8324       508.3         0.06% (508.6)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_07 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.5198       5.75           0.00% (5.75)   \n", "Chat Send                     0.7123       140.7         0.05% (140.8)   \n", "Chat View                     0.2150       193.6         0.11% (193.8)   \n", "Direct Snap Create            0.6607       66.39         0.03% (66.41)   \n", "Direct Snap Send              0.5426       40.84        -0.02% (40.83)   \n", "Direct Snap View              0.7277       162.3        -0.01% (162.3)   \n", "Direct Snap Save              0.3583       11.40         0.02% (11.40)   \n", "DF Non-Friend Story View      0.9737       27.49         0.01% (27.49)   \n", "DF Non-Friend Story View Time 0.5109       795.1        -0.02% (794.9)   \n", "Friend Story View             0.5241       141.5         0.06% (141.5)   \n", "Friend Story View Time        0.8322       373.1        -0.01% (373.1)   \n", "Lens Send                     0.8913       7.08           0.02% (7.08)   \n", "Lens Swipe                    0.5584       132.5        -0.01% (132.5)   \n", "Spotlight Story View          0.6455       88.39         0.04% (88.43)   \n", "Spotlight Story View Time     0.1697       570.9        -0.04% (570.7)   \n", "Story Snap Post               0.5742       2.45           0.16% (2.45)   \n", "Story Snap View               0.8324       508.3         0.02% (508.4)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_08 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.5198       5.75          -0.01% (5.75)   \n", "Chat Send                     0.7123       140.7        -0.04% (140.7)   \n", "Chat View                     0.2150       193.6        -0.09% (193.4)   \n", "Direct Snap Create            0.6607       66.39        -0.03% (66.37)   \n", "Direct Snap Send              0.5426       40.84        -0.06% (40.81)   \n", "Direct Snap View              0.7277       162.3        -0.02% (162.3)   \n", "Direct Snap Save              0.3583       11.40        -0.02% (11.40)   \n", "DF Non-Friend Story View      0.9737       27.49        -0.06% (27.48)   \n", "DF Non-Friend Story View Time 0.5109       795.1        -0.05% (794.7)   \n", "Friend Story View             0.5241       141.5        -0.02% (141.4)   \n", "Friend Story View Time        0.8322       373.1        -0.02% (373.1)   \n", "Lens Send                     0.8913       7.08          -0.09% (7.07)   \n", "Lens Swipe                    0.5584       132.5         0.00% (132.5)   \n", "Spotlight Story View          0.6455       88.39        -0.13% (88.28)   \n", "Spotlight Story View Time     0.1697       570.9        -0.04% (570.7)   \n", "Story Snap Post               0.5742       2.45          -0.04% (2.45)   \n", "Story Snap View               0.8324       508.3        -0.08% (507.9)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_09 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.5198       5.75           0.00% (5.75)   \n", "Chat Send                     0.7123       140.7         0.07% (140.8)   \n", "Chat View                     0.2150       193.6         0.15% (193.9)   \n", "Direct Snap Create            0.6607       66.39         0.04% (66.41)   \n", "Direct Snap Send              0.5426       40.84         0.09% (40.87)   \n", "Direct Snap View              0.7277       162.3         0.04% (162.4)   \n", "Direct Snap Save              0.3583       11.40        -0.06% (11.39)   \n", "DF Non-Friend Story View      0.9737       27.49        -0.02% (27.48)   \n", "DF Non-Friend Story View Time 0.5109       795.1         0.02% (795.3)   \n", "Friend Story View             0.5241       141.5         0.09% (141.6)   \n", "Friend Story View Time        0.8322       373.1         0.04% (373.3)   \n", "Lens Send                     0.8913       7.08           0.06% (7.08)   \n", "Lens Swipe                    0.5584       132.5        -0.05% (132.4)   \n", "Spotlight Story View          0.6455       88.39         0.07% (88.45)   \n", "Spotlight Story View Time     0.1697       570.9         0.16% (571.8)   \n", "Story Snap Post               0.5742       2.45          -0.08% (2.45)   \n", "Story Snap View               0.8324       508.3         0.05% (508.5)   \n", "\n", "stat                                                                    \n", "seed                                                                    \n", "assignment                                                GID_10 (10%)  \n", "metric                        p_mean_anova overall_avg                  \n", "App Active Days               0.5198       5.75          -0.00% (5.75)  \n", "Chat Send                     0.7123       140.7        -0.07% (140.6)  \n", "Chat View                     0.2150       193.6        -0.03% (193.6)  \n", "Direct Snap Create            0.6607       66.39        -0.04% (66.36)  \n", "Direct Snap Send              0.5426       40.84        -0.07% (40.81)  \n", "Direct Snap View              0.7277       162.3        -0.08% (162.2)  \n", "Direct Snap Save              0.3583       11.40        -0.05% (11.40)  \n", "DF Non-Friend Story View      0.9737       27.49         0.03% (27.50)  \n", "DF Non-Friend Story View Time 0.5109       795.1         0.13% (796.1)  \n", "Friend Story View             0.5241       141.5        -0.03% (141.4)  \n", "Friend Story View Time        0.8322       373.1        -0.02% (373.0)  \n", "Lens Send                     0.8913       7.08           0.02% (7.08)  \n", "Lens Swipe                    0.5584       132.5         0.03% (132.5)  \n", "Spotlight Story View          0.6455       88.39        -0.07% (88.33)  \n", "Spotlight Story View Time     0.1697       570.9        -0.18% (569.9)  \n", "Story Snap Post               0.5742       2.45           0.23% (2.46)  \n", "Story Snap View               0.8324       508.3         0.02% (508.4)  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>The second best seed is <strong style='color:blue;'>layer1_$20250604</strong></h3>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p>The table below displays the percent difference between the group's average and the overall average per user value for the metric.</p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>stat</th>\n", "      <th colspan=\"10\" halign=\"left\">percent_difference</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>seed</th>\n", "      <th colspan=\"10\" halign=\"left\">layer1_$20250604</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>assignment</th>\n", "      <th>GID_01 (10%)</th>\n", "      <th>GID_02 (10%)</th>\n", "      <th>GID_03 (10%)</th>\n", "      <th>GID_04 (10%)</th>\n", "      <th>GID_05 (10%)</th>\n", "      <th>GID_06 (10%)</th>\n", "      <th>GID_07 (10%)</th>\n", "      <th>GID_08 (10%)</th>\n", "      <th>GID_09 (10%)</th>\n", "      <th>GID_10 (10%)</th>\n", "    </tr>\n", "    <tr>\n", "      <th>metric</th>\n", "      <th>p_mean_anova</th>\n", "      <th>overall_avg</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>App Active Days</th>\n", "      <th>0.1820</th>\n", "      <th>5.75</th>\n", "      <td>-0.01% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>-0.00% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>-0.01% (5.75)</td>\n", "      <td>-0.00% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>0.01% (5.75)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Chat Send</th>\n", "      <th>0.1850</th>\n", "      <th>140.7</th>\n", "      <td>0.17% (141.0)</td>\n", "      <td>0.09% (140.9)</td>\n", "      <td>-0.07% (140.6)</td>\n", "      <td>-0.01% (140.7)</td>\n", "      <td>0.01% (140.7)</td>\n", "      <td>0.04% (140.8)</td>\n", "      <td>-0.04% (140.7)</td>\n", "      <td>-0.01% (140.7)</td>\n", "      <td>-0.09% (140.6)</td>\n", "      <td>-0.08% (140.6)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Chat View</th>\n", "      <th>0.6805</th>\n", "      <th>193.6</th>\n", "      <td>0.14% (193.8)</td>\n", "      <td>0.08% (193.7)</td>\n", "      <td>-0.08% (193.4)</td>\n", "      <td>0.01% (193.6)</td>\n", "      <td>-0.03% (193.5)</td>\n", "      <td>-0.06% (193.5)</td>\n", "      <td>0.07% (193.7)</td>\n", "      <td>0.04% (193.7)</td>\n", "      <td>-0.08% (193.4)</td>\n", "      <td>-0.07% (193.4)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap Create</th>\n", "      <th>0.4377</th>\n", "      <th>66.37</th>\n", "      <td>0.12% (66.45)</td>\n", "      <td>0.03% (66.39)</td>\n", "      <td>0.03% (66.39)</td>\n", "      <td>-0.04% (66.34)</td>\n", "      <td>-0.02% (66.36)</td>\n", "      <td>-0.07% (66.32)</td>\n", "      <td>-0.02% (66.36)</td>\n", "      <td>0.10% (66.44)</td>\n", "      <td>-0.09% (66.31)</td>\n", "      <td>-0.03% (66.35)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap Send</th>\n", "      <th>0.4202</th>\n", "      <th>40.82</th>\n", "      <td>0.19% (40.90)</td>\n", "      <td>0.03% (40.84)</td>\n", "      <td>0.05% (40.85)</td>\n", "      <td>-0.04% (40.81)</td>\n", "      <td>-0.04% (40.81)</td>\n", "      <td>-0.01% (40.82)</td>\n", "      <td>-0.02% (40.81)</td>\n", "      <td>0.10% (40.86)</td>\n", "      <td>-0.16% (40.76)</td>\n", "      <td>-0.08% (40.79)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap View</th>\n", "      <th>0.8394</th>\n", "      <th>162.3</th>\n", "      <td>0.04% (162.4)</td>\n", "      <td>0.03% (162.3)</td>\n", "      <td>-0.02% (162.3)</td>\n", "      <td>0.01% (162.3)</td>\n", "      <td>-0.01% (162.3)</td>\n", "      <td>0.07% (162.4)</td>\n", "      <td>0.01% (162.3)</td>\n", "      <td>-0.00% (162.3)</td>\n", "      <td>-0.08% (162.2)</td>\n", "      <td>-0.03% (162.2)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap Save</th>\n", "      <th>0.4030</th>\n", "      <th>11.40</th>\n", "      <td>-0.01% (11.40)</td>\n", "      <td>0.02% (11.40)</td>\n", "      <td>-0.05% (11.40)</td>\n", "      <td>0.04% (11.41)</td>\n", "      <td>0.05% (11.41)</td>\n", "      <td>-0.09% (11.39)</td>\n", "      <td>0.01% (11.40)</td>\n", "      <td>-0.01% (11.40)</td>\n", "      <td>-0.01% (11.40)</td>\n", "      <td>0.03% (11.40)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DF Non-Friend Story View</th>\n", "      <th>0.1692</th>\n", "      <th>27.48</th>\n", "      <td>-0.14% (27.44)</td>\n", "      <td>-0.06% (27.47)</td>\n", "      <td>0.14% (27.52)</td>\n", "      <td>-0.05% (27.47)</td>\n", "      <td>-0.01% (27.48)</td>\n", "      <td>0.12% (27.52)</td>\n", "      <td>-0.07% (27.47)</td>\n", "      <td>-0.00% (27.48)</td>\n", "      <td>-0.09% (27.46)</td>\n", "      <td>0.14% (27.52)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DF Non-Friend Story View Time</th>\n", "      <th>0.6808</th>\n", "      <th>794.8</th>\n", "      <td>-0.06% (794.3)</td>\n", "      <td>-0.01% (794.7)</td>\n", "      <td>0.08% (795.4)</td>\n", "      <td>0.01% (794.8)</td>\n", "      <td>-0.05% (794.3)</td>\n", "      <td>0.10% (795.6)</td>\n", "      <td>-0.03% (794.5)</td>\n", "      <td>0.11% (795.7)</td>\n", "      <td>-0.14% (793.6)</td>\n", "      <td>-0.01% (794.7)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Friend Story View</th>\n", "      <th>0.8112</th>\n", "      <th>141.5</th>\n", "      <td>-0.04% (141.4)</td>\n", "      <td>0.08% (141.6)</td>\n", "      <td>0.00% (141.5)</td>\n", "      <td>0.01% (141.5)</td>\n", "      <td>0.03% (141.5)</td>\n", "      <td>0.04% (141.5)</td>\n", "      <td>-0.06% (141.4)</td>\n", "      <td>-0.07% (141.4)</td>\n", "      <td>-0.06% (141.4)</td>\n", "      <td>0.06% (141.6)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Friend Story View Time</th>\n", "      <th>0.7259</th>\n", "      <th>373.2</th>\n", "      <td>-0.02% (373.1)</td>\n", "      <td>0.09% (373.6)</td>\n", "      <td>-0.04% (373.1)</td>\n", "      <td>0.01% (373.3)</td>\n", "      <td>0.03% (373.3)</td>\n", "      <td>-0.02% (373.1)</td>\n", "      <td>-0.07% (372.9)</td>\n", "      <td>0.03% (373.4)</td>\n", "      <td>-0.08% (372.9)</td>\n", "      <td>0.07% (373.5)</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON>s Send</th>\n", "      <th>0.8602</th>\n", "      <th>7.08</th>\n", "      <td>0.11% (7.09)</td>\n", "      <td>0.01% (7.08)</td>\n", "      <td>-0.01% (7.08)</td>\n", "      <td>-0.02% (7.08)</td>\n", "      <td>0.14% (7.09)</td>\n", "      <td>0.01% (7.08)</td>\n", "      <td>0.00% (7.08)</td>\n", "      <td>-0.08% (7.08)</td>\n", "      <td>-0.15% (7.07)</td>\n", "      <td>-0.01% (7.08)</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON> Swipe</th>\n", "      <th>0.9765</th>\n", "      <th>132.5</th>\n", "      <td>-0.04% (132.5)</td>\n", "      <td>0.01% (132.5)</td>\n", "      <td>0.01% (132.5)</td>\n", "      <td>-0.01% (132.5)</td>\n", "      <td>0.04% (132.6)</td>\n", "      <td>-0.02% (132.5)</td>\n", "      <td>-0.01% (132.5)</td>\n", "      <td>-0.01% (132.5)</td>\n", "      <td>0.01% (132.5)</td>\n", "      <td>0.01% (132.5)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Spotlight Story View</th>\n", "      <th>0.8994</th>\n", "      <th>88.38</th>\n", "      <td>0.05% (88.43)</td>\n", "      <td>-0.05% (88.34)</td>\n", "      <td>-0.03% (88.36)</td>\n", "      <td>-0.12% (88.28)</td>\n", "      <td>0.07% (88.44)</td>\n", "      <td>0.02% (88.40)</td>\n", "      <td>-0.02% (88.37)</td>\n", "      <td>0.10% (88.47)</td>\n", "      <td>-0.04% (88.35)</td>\n", "      <td>0.02% (88.40)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Spotlight Story View Time</th>\n", "      <th>0.9198</th>\n", "      <th>570.9</th>\n", "      <td>-0.04% (570.7)</td>\n", "      <td>-0.02% (570.8)</td>\n", "      <td>-0.16% (570.0)</td>\n", "      <td>-0.03% (570.8)</td>\n", "      <td>0.03% (571.1)</td>\n", "      <td>-0.01% (570.9)</td>\n", "      <td>0.12% (571.6)</td>\n", "      <td>0.04% (571.1)</td>\n", "      <td>0.04% (571.1)</td>\n", "      <td>0.02% (571.0)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Story Snap Post</th>\n", "      <th>0.9560</th>\n", "      <th>2.45</th>\n", "      <td>0.20% (2.46)</td>\n", "      <td>0.22% (2.46)</td>\n", "      <td>-0.03% (2.45)</td>\n", "      <td>-0.24% (2.44)</td>\n", "      <td>-0.02% (2.45)</td>\n", "      <td>-0.08% (2.45)</td>\n", "      <td>-0.03% (2.45)</td>\n", "      <td>-0.19% (2.45)</td>\n", "      <td>-0.08% (2.45)</td>\n", "      <td>0.27% (2.46)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Story Snap View</th>\n", "      <th>0.8156</th>\n", "      <th>508.3</th>\n", "      <td>-0.06% (508.0)</td>\n", "      <td>-0.02% (508.2)</td>\n", "      <td>0.05% (508.5)</td>\n", "      <td>-0.06% (508.0)</td>\n", "      <td>0.07% (508.6)</td>\n", "      <td>0.06% (508.6)</td>\n", "      <td>-0.02% (508.2)</td>\n", "      <td>0.01% (508.3)</td>\n", "      <td>-0.08% (507.9)</td>\n", "      <td>0.03% (508.4)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["stat                                                   percent_difference  \\\n", "seed                                                     layer1_$20250604   \n", "assignment                                                   GID_01 (10%)   \n", "metric                        p_mean_anova overall_avg                      \n", "App Active Days               0.1820       5.75             -0.01% (5.75)   \n", "Chat Send                     0.1850       140.7            0.17% (141.0)   \n", "Chat View                     0.6805       193.6            0.14% (193.8)   \n", "Direct Snap Create            0.4377       66.37            0.12% (66.45)   \n", "Direct Snap Send              0.4202       40.82            0.19% (40.90)   \n", "Direct Snap View              0.8394       162.3            0.04% (162.4)   \n", "Direct Snap Save              0.4030       11.40           -0.01% (11.40)   \n", "DF Non-Friend Story View      0.1692       27.48           -0.14% (27.44)   \n", "DF Non-Friend Story View Time 0.6808       794.8           -0.06% (794.3)   \n", "Friend Story View             0.8112       141.5           -0.04% (141.4)   \n", "Friend Story View Time        0.7259       373.2           -0.02% (373.1)   \n", "Lens Send                     0.8602       7.08              0.11% (7.09)   \n", "Lens Swipe                    0.9765       132.5           -0.04% (132.5)   \n", "Spotlight Story View          0.8994       88.38            0.05% (88.43)   \n", "Spotlight Story View Time     0.9198       570.9           -0.04% (570.7)   \n", "Story Snap Post               0.9560       2.45              0.20% (2.46)   \n", "Story Snap View               0.8156       508.3           -0.06% (508.0)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_02 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.1820       5.75           0.00% (5.75)   \n", "Chat Send                     0.1850       140.7         0.09% (140.9)   \n", "Chat View                     0.6805       193.6         0.08% (193.7)   \n", "Direct Snap Create            0.4377       66.37         0.03% (66.39)   \n", "Direct Snap Send              0.4202       40.82         0.03% (40.84)   \n", "Direct Snap View              0.8394       162.3         0.03% (162.3)   \n", "Direct Snap Save              0.4030       11.40         0.02% (11.40)   \n", "DF Non-Friend Story View      0.1692       27.48        -0.06% (27.47)   \n", "DF Non-Friend Story View Time 0.6808       794.8        -0.01% (794.7)   \n", "Friend Story View             0.8112       141.5         0.08% (141.6)   \n", "Friend Story View Time        0.7259       373.2         0.09% (373.6)   \n", "Lens Send                     0.8602       7.08           0.01% (7.08)   \n", "Lens Swipe                    0.9765       132.5         0.01% (132.5)   \n", "Spotlight Story View          0.8994       88.38        -0.05% (88.34)   \n", "Spotlight Story View Time     0.9198       570.9        -0.02% (570.8)   \n", "Story Snap Post               0.9560       2.45           0.22% (2.46)   \n", "Story Snap View               0.8156       508.3        -0.02% (508.2)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_03 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.1820       5.75          -0.00% (5.75)   \n", "Chat Send                     0.1850       140.7        -0.07% (140.6)   \n", "Chat View                     0.6805       193.6        -0.08% (193.4)   \n", "Direct Snap Create            0.4377       66.37         0.03% (66.39)   \n", "Direct Snap Send              0.4202       40.82         0.05% (40.85)   \n", "Direct Snap View              0.8394       162.3        -0.02% (162.3)   \n", "Direct Snap Save              0.4030       11.40        -0.05% (11.40)   \n", "DF Non-Friend Story View      0.1692       27.48         0.14% (27.52)   \n", "DF Non-Friend Story View Time 0.6808       794.8         0.08% (795.4)   \n", "Friend Story View             0.8112       141.5         0.00% (141.5)   \n", "Friend Story View Time        0.7259       373.2        -0.04% (373.1)   \n", "Lens Send                     0.8602       7.08          -0.01% (7.08)   \n", "Lens Swipe                    0.9765       132.5         0.01% (132.5)   \n", "Spotlight Story View          0.8994       88.38        -0.03% (88.36)   \n", "Spotlight Story View Time     0.9198       570.9        -0.16% (570.0)   \n", "Story Snap Post               0.9560       2.45          -0.03% (2.45)   \n", "Story Snap View               0.8156       508.3         0.05% (508.5)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_04 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.1820       5.75           0.00% (5.75)   \n", "Chat Send                     0.1850       140.7        -0.01% (140.7)   \n", "Chat View                     0.6805       193.6         0.01% (193.6)   \n", "Direct Snap Create            0.4377       66.37        -0.04% (66.34)   \n", "Direct Snap Send              0.4202       40.82        -0.04% (40.81)   \n", "Direct Snap View              0.8394       162.3         0.01% (162.3)   \n", "Direct Snap Save              0.4030       11.40         0.04% (11.41)   \n", "DF Non-Friend Story View      0.1692       27.48        -0.05% (27.47)   \n", "DF Non-Friend Story View Time 0.6808       794.8         0.01% (794.8)   \n", "Friend Story View             0.8112       141.5         0.01% (141.5)   \n", "Friend Story View Time        0.7259       373.2         0.01% (373.3)   \n", "Lens Send                     0.8602       7.08          -0.02% (7.08)   \n", "Lens Swipe                    0.9765       132.5        -0.01% (132.5)   \n", "Spotlight Story View          0.8994       88.38        -0.12% (88.28)   \n", "Spotlight Story View Time     0.9198       570.9        -0.03% (570.8)   \n", "Story Snap Post               0.9560       2.45          -0.24% (2.44)   \n", "Story Snap View               0.8156       508.3        -0.06% (508.0)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_05 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.1820       5.75          -0.01% (5.75)   \n", "Chat Send                     0.1850       140.7         0.01% (140.7)   \n", "Chat View                     0.6805       193.6        -0.03% (193.5)   \n", "Direct Snap Create            0.4377       66.37        -0.02% (66.36)   \n", "Direct Snap Send              0.4202       40.82        -0.04% (40.81)   \n", "Direct Snap View              0.8394       162.3        -0.01% (162.3)   \n", "Direct Snap Save              0.4030       11.40         0.05% (11.41)   \n", "DF Non-Friend Story View      0.1692       27.48        -0.01% (27.48)   \n", "DF Non-Friend Story View Time 0.6808       794.8        -0.05% (794.3)   \n", "Friend Story View             0.8112       141.5         0.03% (141.5)   \n", "Friend Story View Time        0.7259       373.2         0.03% (373.3)   \n", "Lens Send                     0.8602       7.08           0.14% (7.09)   \n", "Lens Swipe                    0.9765       132.5         0.04% (132.6)   \n", "Spotlight Story View          0.8994       88.38         0.07% (88.44)   \n", "Spotlight Story View Time     0.9198       570.9         0.03% (571.1)   \n", "Story Snap Post               0.9560       2.45          -0.02% (2.45)   \n", "Story Snap View               0.8156       508.3         0.07% (508.6)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_06 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.1820       5.75          -0.00% (5.75)   \n", "Chat Send                     0.1850       140.7         0.04% (140.8)   \n", "Chat View                     0.6805       193.6        -0.06% (193.5)   \n", "Direct Snap Create            0.4377       66.37        -0.07% (66.32)   \n", "Direct Snap Send              0.4202       40.82        -0.01% (40.82)   \n", "Direct Snap View              0.8394       162.3         0.07% (162.4)   \n", "Direct Snap Save              0.4030       11.40        -0.09% (11.39)   \n", "DF Non-Friend Story View      0.1692       27.48         0.12% (27.52)   \n", "DF Non-Friend Story View Time 0.6808       794.8         0.10% (795.6)   \n", "Friend Story View             0.8112       141.5         0.04% (141.5)   \n", "Friend Story View Time        0.7259       373.2        -0.02% (373.1)   \n", "Lens Send                     0.8602       7.08           0.01% (7.08)   \n", "Lens Swipe                    0.9765       132.5        -0.02% (132.5)   \n", "Spotlight Story View          0.8994       88.38         0.02% (88.40)   \n", "Spotlight Story View Time     0.9198       570.9        -0.01% (570.9)   \n", "Story Snap Post               0.9560       2.45          -0.08% (2.45)   \n", "Story Snap View               0.8156       508.3         0.06% (508.6)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_07 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.1820       5.75           0.00% (5.75)   \n", "Chat Send                     0.1850       140.7        -0.04% (140.7)   \n", "Chat View                     0.6805       193.6         0.07% (193.7)   \n", "Direct Snap Create            0.4377       66.37        -0.02% (66.36)   \n", "Direct Snap Send              0.4202       40.82        -0.02% (40.81)   \n", "Direct Snap View              0.8394       162.3         0.01% (162.3)   \n", "Direct Snap Save              0.4030       11.40         0.01% (11.40)   \n", "DF Non-Friend Story View      0.1692       27.48        -0.07% (27.47)   \n", "DF Non-Friend Story View Time 0.6808       794.8        -0.03% (794.5)   \n", "Friend Story View             0.8112       141.5        -0.06% (141.4)   \n", "Friend Story View Time        0.7259       373.2        -0.07% (372.9)   \n", "Lens Send                     0.8602       7.08           0.00% (7.08)   \n", "Lens Swipe                    0.9765       132.5        -0.01% (132.5)   \n", "Spotlight Story View          0.8994       88.38        -0.02% (88.37)   \n", "Spotlight Story View Time     0.9198       570.9         0.12% (571.6)   \n", "Story Snap Post               0.9560       2.45          -0.03% (2.45)   \n", "Story Snap View               0.8156       508.3        -0.02% (508.2)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_08 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.1820       5.75           0.00% (5.75)   \n", "Chat Send                     0.1850       140.7        -0.01% (140.7)   \n", "Chat View                     0.6805       193.6         0.04% (193.7)   \n", "Direct Snap Create            0.4377       66.37         0.10% (66.44)   \n", "Direct Snap Send              0.4202       40.82         0.10% (40.86)   \n", "Direct Snap View              0.8394       162.3        -0.00% (162.3)   \n", "Direct Snap Save              0.4030       11.40        -0.01% (11.40)   \n", "DF Non-Friend Story View      0.1692       27.48        -0.00% (27.48)   \n", "DF Non-Friend Story View Time 0.6808       794.8         0.11% (795.7)   \n", "Friend Story View             0.8112       141.5        -0.07% (141.4)   \n", "Friend Story View Time        0.7259       373.2         0.03% (373.4)   \n", "Lens Send                     0.8602       7.08          -0.08% (7.08)   \n", "Lens Swipe                    0.9765       132.5        -0.01% (132.5)   \n", "Spotlight Story View          0.8994       88.38         0.10% (88.47)   \n", "Spotlight Story View Time     0.9198       570.9         0.04% (571.1)   \n", "Story Snap Post               0.9560       2.45          -0.19% (2.45)   \n", "Story Snap View               0.8156       508.3         0.01% (508.3)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_09 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.1820       5.75           0.00% (5.75)   \n", "Chat Send                     0.1850       140.7        -0.09% (140.6)   \n", "Chat View                     0.6805       193.6        -0.08% (193.4)   \n", "Direct Snap Create            0.4377       66.37        -0.09% (66.31)   \n", "Direct Snap Send              0.4202       40.82        -0.16% (40.76)   \n", "Direct Snap View              0.8394       162.3        -0.08% (162.2)   \n", "Direct Snap Save              0.4030       11.40        -0.01% (11.40)   \n", "DF Non-Friend Story View      0.1692       27.48        -0.09% (27.46)   \n", "DF Non-Friend Story View Time 0.6808       794.8        -0.14% (793.6)   \n", "Friend Story View             0.8112       141.5        -0.06% (141.4)   \n", "Friend Story View Time        0.7259       373.2        -0.08% (372.9)   \n", "Lens Send                     0.8602       7.08          -0.15% (7.07)   \n", "Lens Swipe                    0.9765       132.5         0.01% (132.5)   \n", "Spotlight Story View          0.8994       88.38        -0.04% (88.35)   \n", "Spotlight Story View Time     0.9198       570.9         0.04% (571.1)   \n", "Story Snap Post               0.9560       2.45          -0.08% (2.45)   \n", "Story Snap View               0.8156       508.3        -0.08% (507.9)   \n", "\n", "stat                                                                    \n", "seed                                                                    \n", "assignment                                                GID_10 (10%)  \n", "metric                        p_mean_anova overall_avg                  \n", "App Active Days               0.1820       5.75           0.01% (5.75)  \n", "Chat Send                     0.1850       140.7        -0.08% (140.6)  \n", "Chat View                     0.6805       193.6        -0.07% (193.4)  \n", "Direct Snap Create            0.4377       66.37        -0.03% (66.35)  \n", "Direct Snap Send              0.4202       40.82        -0.08% (40.79)  \n", "Direct Snap View              0.8394       162.3        -0.03% (162.2)  \n", "Direct Snap Save              0.4030       11.40         0.03% (11.40)  \n", "DF Non-Friend Story View      0.1692       27.48         0.14% (27.52)  \n", "DF Non-Friend Story View Time 0.6808       794.8        -0.01% (794.7)  \n", "Friend Story View             0.8112       141.5         0.06% (141.6)  \n", "Friend Story View Time        0.7259       373.2         0.07% (373.5)  \n", "Lens Send                     0.8602       7.08          -0.01% (7.08)  \n", "Lens Swipe                    0.9765       132.5         0.01% (132.5)  \n", "Spotlight Story View          0.8994       88.38         0.02% (88.40)  \n", "Spotlight Story View Time     0.9198       570.9         0.02% (571.0)  \n", "Story Snap Post               0.9560       2.45           0.27% (2.46)  \n", "Story Snap View               0.8156       508.3         0.03% (508.4)  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>The third best seed is <strong style='color:blue;'>layer1_20250604_0005</strong></h3>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p>The table below displays the percent difference between the group's average and the overall average per user value for the metric.</p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>stat</th>\n", "      <th colspan=\"10\" halign=\"left\">percent_difference</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>seed</th>\n", "      <th colspan=\"10\" halign=\"left\">layer1_20250604_0005</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>assignment</th>\n", "      <th>GID_01 (10%)</th>\n", "      <th>GID_02 (10%)</th>\n", "      <th>GID_03 (10%)</th>\n", "      <th>GID_04 (10%)</th>\n", "      <th>GID_05 (10%)</th>\n", "      <th>GID_06 (10%)</th>\n", "      <th>GID_07 (10%)</th>\n", "      <th>GID_08 (10%)</th>\n", "      <th>GID_09 (10%)</th>\n", "      <th>GID_10 (10%)</th>\n", "    </tr>\n", "    <tr>\n", "      <th>metric</th>\n", "      <th>p_mean_anova</th>\n", "      <th>overall_avg</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>App Active Days</th>\n", "      <th>0.5713</th>\n", "      <th>5.75</th>\n", "      <td>0.00% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>-0.00% (5.75)</td>\n", "      <td>0.01% (5.75)</td>\n", "      <td>-0.01% (5.75)</td>\n", "      <td>-0.01% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>-0.00% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Chat Send</th>\n", "      <th>0.3960</th>\n", "      <th>140.7</th>\n", "      <td>0.01% (140.7)</td>\n", "      <td>0.07% (140.8)</td>\n", "      <td>0.04% (140.8)</td>\n", "      <td>0.03% (140.8)</td>\n", "      <td>-0.12% (140.5)</td>\n", "      <td>0.06% (140.8)</td>\n", "      <td>0.04% (140.8)</td>\n", "      <td>-0.03% (140.7)</td>\n", "      <td>0.03% (140.8)</td>\n", "      <td>-0.12% (140.5)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Chat View</th>\n", "      <th>0.4515</th>\n", "      <th>193.6</th>\n", "      <td>0.06% (193.7)</td>\n", "      <td>0.05% (193.7)</td>\n", "      <td>-0.02% (193.6)</td>\n", "      <td>0.05% (193.7)</td>\n", "      <td>-0.08% (193.4)</td>\n", "      <td>0.01% (193.6)</td>\n", "      <td>-0.01% (193.6)</td>\n", "      <td>-0.05% (193.5)</td>\n", "      <td>0.16% (193.9)</td>\n", "      <td>-0.16% (193.3)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap Create</th>\n", "      <th>0.4781</th>\n", "      <th>66.37</th>\n", "      <td>-0.02% (66.36)</td>\n", "      <td>-0.14% (66.28)</td>\n", "      <td>0.04% (66.39)</td>\n", "      <td>0.04% (66.40)</td>\n", "      <td>-0.02% (66.36)</td>\n", "      <td>0.10% (66.44)</td>\n", "      <td>-0.03% (66.35)</td>\n", "      <td>0.00% (66.37)</td>\n", "      <td>0.06% (66.41)</td>\n", "      <td>-0.02% (66.36)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap Send</th>\n", "      <th>0.3812</th>\n", "      <th>40.83</th>\n", "      <td>-0.01% (40.83)</td>\n", "      <td>-0.25% (40.73)</td>\n", "      <td>0.06% (40.85)</td>\n", "      <td>0.04% (40.85)</td>\n", "      <td>0.01% (40.83)</td>\n", "      <td>0.13% (40.88)</td>\n", "      <td>-0.01% (40.83)</td>\n", "      <td>-0.03% (40.82)</td>\n", "      <td>0.04% (40.85)</td>\n", "      <td>0.02% (40.84)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap View</th>\n", "      <th>0.5145</th>\n", "      <th>162.3</th>\n", "      <td>-0.05% (162.2)</td>\n", "      <td>-0.08% (162.2)</td>\n", "      <td>0.02% (162.3)</td>\n", "      <td>-0.01% (162.3)</td>\n", "      <td>-0.00% (162.3)</td>\n", "      <td>0.09% (162.4)</td>\n", "      <td>0.03% (162.3)</td>\n", "      <td>-0.05% (162.2)</td>\n", "      <td>0.06% (162.4)</td>\n", "      <td>-0.03% (162.2)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap Save</th>\n", "      <th>0.7912</th>\n", "      <th>11.40</th>\n", "      <td>-0.02% (11.40)</td>\n", "      <td>-0.04% (11.39)</td>\n", "      <td>-0.02% (11.40)</td>\n", "      <td>0.02% (11.40)</td>\n", "      <td>0.03% (11.40)</td>\n", "      <td>0.02% (11.40)</td>\n", "      <td>0.00% (11.40)</td>\n", "      <td>0.03% (11.40)</td>\n", "      <td>0.03% (11.40)</td>\n", "      <td>-0.06% (11.39)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DF Non-Friend Story View</th>\n", "      <th>0.8897</th>\n", "      <th>27.49</th>\n", "      <td>0.06% (27.50)</td>\n", "      <td>-0.02% (27.48)</td>\n", "      <td>0.02% (27.49)</td>\n", "      <td>0.07% (27.51)</td>\n", "      <td>-0.05% (27.47)</td>\n", "      <td>-0.03% (27.48)</td>\n", "      <td>-0.02% (27.48)</td>\n", "      <td>-0.11% (27.46)</td>\n", "      <td>0.05% (27.50)</td>\n", "      <td>0.03% (27.50)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DF Non-Friend Story View Time</th>\n", "      <th>0.8374</th>\n", "      <th>795.0</th>\n", "      <td>0.07% (795.5)</td>\n", "      <td>-0.01% (794.9)</td>\n", "      <td>-0.00% (794.9)</td>\n", "      <td>0.09% (795.7)</td>\n", "      <td>-0.02% (794.8)</td>\n", "      <td>0.00% (795.0)</td>\n", "      <td>0.08% (795.6)</td>\n", "      <td>-0.01% (794.9)</td>\n", "      <td>-0.13% (793.9)</td>\n", "      <td>-0.05% (794.5)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Friend Story View</th>\n", "      <th>0.5749</th>\n", "      <th>141.5</th>\n", "      <td>0.09% (141.6)</td>\n", "      <td>-0.12% (141.3)</td>\n", "      <td>0.02% (141.5)</td>\n", "      <td>0.01% (141.5)</td>\n", "      <td>-0.04% (141.4)</td>\n", "      <td>0.11% (141.6)</td>\n", "      <td>-0.02% (141.4)</td>\n", "      <td>-0.03% (141.4)</td>\n", "      <td>0.02% (141.5)</td>\n", "      <td>-0.03% (141.4)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Friend Story View Time</th>\n", "      <th>0.5445</th>\n", "      <th>373.1</th>\n", "      <td>0.09% (373.5)</td>\n", "      <td>-0.10% (372.8)</td>\n", "      <td>0.06% (373.3)</td>\n", "      <td>0.01% (373.2)</td>\n", "      <td>-0.09% (372.8)</td>\n", "      <td>0.06% (373.3)</td>\n", "      <td>0.01% (373.2)</td>\n", "      <td>-0.02% (373.1)</td>\n", "      <td>0.01% (373.2)</td>\n", "      <td>-0.02% (373.1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON>s Send</th>\n", "      <th>0.5619</th>\n", "      <th>7.08</th>\n", "      <td>-0.03% (7.08)</td>\n", "      <td>0.04% (7.08)</td>\n", "      <td>-0.10% (7.07)</td>\n", "      <td>-0.16% (7.07)</td>\n", "      <td>-0.03% (7.08)</td>\n", "      <td>-0.07% (7.08)</td>\n", "      <td>0.21% (7.09)</td>\n", "      <td>0.08% (7.09)</td>\n", "      <td>0.06% (7.08)</td>\n", "      <td>0.01% (7.08)</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON> Swipe</th>\n", "      <th>0.7269</th>\n", "      <th>132.5</th>\n", "      <td>0.01% (132.5)</td>\n", "      <td>-0.03% (132.5)</td>\n", "      <td>0.01% (132.5)</td>\n", "      <td>-0.05% (132.4)</td>\n", "      <td>0.01% (132.5)</td>\n", "      <td>0.00% (132.5)</td>\n", "      <td>0.00% (132.5)</td>\n", "      <td>0.06% (132.6)</td>\n", "      <td>0.03% (132.5)</td>\n", "      <td>-0.03% (132.5)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Spotlight Story View</th>\n", "      <th>0.1783</th>\n", "      <th>88.36</th>\n", "      <td>0.10% (88.45)</td>\n", "      <td>-0.17% (88.21)</td>\n", "      <td>0.16% (88.50)</td>\n", "      <td>0.02% (88.37)</td>\n", "      <td>-0.03% (88.33)</td>\n", "      <td>-0.16% (88.22)</td>\n", "      <td>0.13% (88.47)</td>\n", "      <td>-0.04% (88.32)</td>\n", "      <td>0.01% (88.37)</td>\n", "      <td>-0.01% (88.35)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Spotlight Story View Time</th>\n", "      <th>0.1597</th>\n", "      <th>570.8</th>\n", "      <td>0.04% (571.0)</td>\n", "      <td>-0.04% (570.6)</td>\n", "      <td>0.25% (572.3)</td>\n", "      <td>-0.06% (570.5)</td>\n", "      <td>-0.02% (570.7)</td>\n", "      <td>-0.26% (569.3)</td>\n", "      <td>0.12% (571.5)</td>\n", "      <td>-0.01% (570.7)</td>\n", "      <td>-0.10% (570.2)</td>\n", "      <td>0.07% (571.2)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Story Snap Post</th>\n", "      <th>0.4622</th>\n", "      <th>2.45</th>\n", "      <td>-0.26% (2.44)</td>\n", "      <td>0.03% (2.45)</td>\n", "      <td>-0.04% (2.45)</td>\n", "      <td>0.56% (2.46)</td>\n", "      <td>-0.12% (2.45)</td>\n", "      <td>0.02% (2.45)</td>\n", "      <td>-0.28% (2.44)</td>\n", "      <td>-0.03% (2.45)</td>\n", "      <td>-0.02% (2.45)</td>\n", "      <td>0.12% (2.45)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Story Snap View</th>\n", "      <th>0.7409</th>\n", "      <th>508.3</th>\n", "      <td>0.08% (508.7)</td>\n", "      <td>-0.08% (507.9)</td>\n", "      <td>0.03% (508.4)</td>\n", "      <td>0.08% (508.7)</td>\n", "      <td>-0.06% (508.0)</td>\n", "      <td>0.02% (508.4)</td>\n", "      <td>0.04% (508.5)</td>\n", "      <td>-0.03% (508.2)</td>\n", "      <td>-0.01% (508.2)</td>\n", "      <td>-0.06% (508.0)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["stat                                                     percent_difference  \\\n", "seed                                                   layer1_20250604_0005   \n", "assignment                                                     GID_01 (10%)   \n", "metric                        p_mean_anova overall_avg                        \n", "App Active Days               0.5713       5.75                0.00% (5.75)   \n", "Chat Send                     0.3960       140.7              0.01% (140.7)   \n", "Chat View                     0.4515       193.6              0.06% (193.7)   \n", "Direct Snap Create            0.4781       66.37             -0.02% (66.36)   \n", "Direct Snap Send              0.3812       40.83             -0.01% (40.83)   \n", "Direct Snap View              0.5145       162.3             -0.05% (162.2)   \n", "Direct Snap Save              0.7912       11.40             -0.02% (11.40)   \n", "DF Non-Friend Story View      0.8897       27.49              0.06% (27.50)   \n", "DF Non-Friend Story View Time 0.8374       795.0              0.07% (795.5)   \n", "Friend Story View             0.5749       141.5              0.09% (141.6)   \n", "Friend Story View Time        0.5445       373.1              0.09% (373.5)   \n", "Lens Send                     0.5619       7.08               -0.03% (7.08)   \n", "Lens Swipe                    0.7269       132.5              0.01% (132.5)   \n", "Spotlight Story View          0.1783       88.36              0.10% (88.45)   \n", "Spotlight Story View Time     0.1597       570.8              0.04% (571.0)   \n", "Story Snap Post               0.4622       2.45               -0.26% (2.44)   \n", "Story Snap View               0.7409       508.3              0.08% (508.7)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_02 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.5713       5.75           0.00% (5.75)   \n", "Chat Send                     0.3960       140.7         0.07% (140.8)   \n", "Chat View                     0.4515       193.6         0.05% (193.7)   \n", "Direct Snap Create            0.4781       66.37        -0.14% (66.28)   \n", "Direct Snap Send              0.3812       40.83        -0.25% (40.73)   \n", "Direct Snap View              0.5145       162.3        -0.08% (162.2)   \n", "Direct Snap Save              0.7912       11.40        -0.04% (11.39)   \n", "DF Non-Friend Story View      0.8897       27.49        -0.02% (27.48)   \n", "DF Non-Friend Story View Time 0.8374       795.0        -0.01% (794.9)   \n", "Friend Story View             0.5749       141.5        -0.12% (141.3)   \n", "Friend Story View Time        0.5445       373.1        -0.10% (372.8)   \n", "Lens Send                     0.5619       7.08           0.04% (7.08)   \n", "Lens Swipe                    0.7269       132.5        -0.03% (132.5)   \n", "Spotlight Story View          0.1783       88.36        -0.17% (88.21)   \n", "Spotlight Story View Time     0.1597       570.8        -0.04% (570.6)   \n", "Story Snap Post               0.4622       2.45           0.03% (2.45)   \n", "Story Snap View               0.7409       508.3        -0.08% (507.9)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_03 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.5713       5.75          -0.00% (5.75)   \n", "Chat Send                     0.3960       140.7         0.04% (140.8)   \n", "Chat View                     0.4515       193.6        -0.02% (193.6)   \n", "Direct Snap Create            0.4781       66.37         0.04% (66.39)   \n", "Direct Snap Send              0.3812       40.83         0.06% (40.85)   \n", "Direct Snap View              0.5145       162.3         0.02% (162.3)   \n", "Direct Snap Save              0.7912       11.40        -0.02% (11.40)   \n", "DF Non-Friend Story View      0.8897       27.49         0.02% (27.49)   \n", "DF Non-Friend Story View Time 0.8374       795.0        -0.00% (794.9)   \n", "Friend Story View             0.5749       141.5         0.02% (141.5)   \n", "Friend Story View Time        0.5445       373.1         0.06% (373.3)   \n", "Lens Send                     0.5619       7.08          -0.10% (7.07)   \n", "Lens Swipe                    0.7269       132.5         0.01% (132.5)   \n", "Spotlight Story View          0.1783       88.36         0.16% (88.50)   \n", "Spotlight Story View Time     0.1597       570.8         0.25% (572.3)   \n", "Story Snap Post               0.4622       2.45          -0.04% (2.45)   \n", "Story Snap View               0.7409       508.3         0.03% (508.4)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_04 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.5713       5.75           0.01% (5.75)   \n", "Chat Send                     0.3960       140.7         0.03% (140.8)   \n", "Chat View                     0.4515       193.6         0.05% (193.7)   \n", "Direct Snap Create            0.4781       66.37         0.04% (66.40)   \n", "Direct Snap Send              0.3812       40.83         0.04% (40.85)   \n", "Direct Snap View              0.5145       162.3        -0.01% (162.3)   \n", "Direct Snap Save              0.7912       11.40         0.02% (11.40)   \n", "DF Non-Friend Story View      0.8897       27.49         0.07% (27.51)   \n", "DF Non-Friend Story View Time 0.8374       795.0         0.09% (795.7)   \n", "Friend Story View             0.5749       141.5         0.01% (141.5)   \n", "Friend Story View Time        0.5445       373.1         0.01% (373.2)   \n", "Lens Send                     0.5619       7.08          -0.16% (7.07)   \n", "Lens Swipe                    0.7269       132.5        -0.05% (132.4)   \n", "Spotlight Story View          0.1783       88.36         0.02% (88.37)   \n", "Spotlight Story View Time     0.1597       570.8        -0.06% (570.5)   \n", "Story Snap Post               0.4622       2.45           0.56% (2.46)   \n", "Story Snap View               0.7409       508.3         0.08% (508.7)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_05 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.5713       5.75          -0.01% (5.75)   \n", "Chat Send                     0.3960       140.7        -0.12% (140.5)   \n", "Chat View                     0.4515       193.6        -0.08% (193.4)   \n", "Direct Snap Create            0.4781       66.37        -0.02% (66.36)   \n", "Direct Snap Send              0.3812       40.83         0.01% (40.83)   \n", "Direct Snap View              0.5145       162.3        -0.00% (162.3)   \n", "Direct Snap Save              0.7912       11.40         0.03% (11.40)   \n", "DF Non-Friend Story View      0.8897       27.49        -0.05% (27.47)   \n", "DF Non-Friend Story View Time 0.8374       795.0        -0.02% (794.8)   \n", "Friend Story View             0.5749       141.5        -0.04% (141.4)   \n", "Friend Story View Time        0.5445       373.1        -0.09% (372.8)   \n", "Lens Send                     0.5619       7.08          -0.03% (7.08)   \n", "Lens Swipe                    0.7269       132.5         0.01% (132.5)   \n", "Spotlight Story View          0.1783       88.36        -0.03% (88.33)   \n", "Spotlight Story View Time     0.1597       570.8        -0.02% (570.7)   \n", "Story Snap Post               0.4622       2.45          -0.12% (2.45)   \n", "Story Snap View               0.7409       508.3        -0.06% (508.0)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_06 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.5713       5.75          -0.01% (5.75)   \n", "Chat Send                     0.3960       140.7         0.06% (140.8)   \n", "Chat View                     0.4515       193.6         0.01% (193.6)   \n", "Direct Snap Create            0.4781       66.37         0.10% (66.44)   \n", "Direct Snap Send              0.3812       40.83         0.13% (40.88)   \n", "Direct Snap View              0.5145       162.3         0.09% (162.4)   \n", "Direct Snap Save              0.7912       11.40         0.02% (11.40)   \n", "DF Non-Friend Story View      0.8897       27.49        -0.03% (27.48)   \n", "DF Non-Friend Story View Time 0.8374       795.0         0.00% (795.0)   \n", "Friend Story View             0.5749       141.5         0.11% (141.6)   \n", "Friend Story View Time        0.5445       373.1         0.06% (373.3)   \n", "Lens Send                     0.5619       7.08          -0.07% (7.08)   \n", "Lens Swipe                    0.7269       132.5         0.00% (132.5)   \n", "Spotlight Story View          0.1783       88.36        -0.16% (88.22)   \n", "Spotlight Story View Time     0.1597       570.8        -0.26% (569.3)   \n", "Story Snap Post               0.4622       2.45           0.02% (2.45)   \n", "Story Snap View               0.7409       508.3         0.02% (508.4)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_07 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.5713       5.75           0.00% (5.75)   \n", "Chat Send                     0.3960       140.7         0.04% (140.8)   \n", "Chat View                     0.4515       193.6        -0.01% (193.6)   \n", "Direct Snap Create            0.4781       66.37        -0.03% (66.35)   \n", "Direct Snap Send              0.3812       40.83        -0.01% (40.83)   \n", "Direct Snap View              0.5145       162.3         0.03% (162.3)   \n", "Direct Snap Save              0.7912       11.40         0.00% (11.40)   \n", "DF Non-Friend Story View      0.8897       27.49        -0.02% (27.48)   \n", "DF Non-Friend Story View Time 0.8374       795.0         0.08% (795.6)   \n", "Friend Story View             0.5749       141.5        -0.02% (141.4)   \n", "Friend Story View Time        0.5445       373.1         0.01% (373.2)   \n", "Lens Send                     0.5619       7.08           0.21% (7.09)   \n", "Lens Swipe                    0.7269       132.5         0.00% (132.5)   \n", "Spotlight Story View          0.1783       88.36         0.13% (88.47)   \n", "Spotlight Story View Time     0.1597       570.8         0.12% (571.5)   \n", "Story Snap Post               0.4622       2.45          -0.28% (2.44)   \n", "Story Snap View               0.7409       508.3         0.04% (508.5)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_08 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.5713       5.75          -0.00% (5.75)   \n", "Chat Send                     0.3960       140.7        -0.03% (140.7)   \n", "Chat View                     0.4515       193.6        -0.05% (193.5)   \n", "Direct Snap Create            0.4781       66.37         0.00% (66.37)   \n", "Direct Snap Send              0.3812       40.83        -0.03% (40.82)   \n", "Direct Snap View              0.5145       162.3        -0.05% (162.2)   \n", "Direct Snap Save              0.7912       11.40         0.03% (11.40)   \n", "DF Non-Friend Story View      0.8897       27.49        -0.11% (27.46)   \n", "DF Non-Friend Story View Time 0.8374       795.0        -0.01% (794.9)   \n", "Friend Story View             0.5749       141.5        -0.03% (141.4)   \n", "Friend Story View Time        0.5445       373.1        -0.02% (373.1)   \n", "Lens Send                     0.5619       7.08           0.08% (7.09)   \n", "Lens Swipe                    0.7269       132.5         0.06% (132.6)   \n", "Spotlight Story View          0.1783       88.36        -0.04% (88.32)   \n", "Spotlight Story View Time     0.1597       570.8        -0.01% (570.7)   \n", "Story Snap Post               0.4622       2.45          -0.03% (2.45)   \n", "Story Snap View               0.7409       508.3        -0.03% (508.2)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_09 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.5713       5.75           0.00% (5.75)   \n", "Chat Send                     0.3960       140.7         0.03% (140.8)   \n", "Chat View                     0.4515       193.6         0.16% (193.9)   \n", "Direct Snap Create            0.4781       66.37         0.06% (66.41)   \n", "Direct Snap Send              0.3812       40.83         0.04% (40.85)   \n", "Direct Snap View              0.5145       162.3         0.06% (162.4)   \n", "Direct Snap Save              0.7912       11.40         0.03% (11.40)   \n", "DF Non-Friend Story View      0.8897       27.49         0.05% (27.50)   \n", "DF Non-Friend Story View Time 0.8374       795.0        -0.13% (793.9)   \n", "Friend Story View             0.5749       141.5         0.02% (141.5)   \n", "Friend Story View Time        0.5445       373.1         0.01% (373.2)   \n", "Lens Send                     0.5619       7.08           0.06% (7.08)   \n", "Lens Swipe                    0.7269       132.5         0.03% (132.5)   \n", "Spotlight Story View          0.1783       88.36         0.01% (88.37)   \n", "Spotlight Story View Time     0.1597       570.8        -0.10% (570.2)   \n", "Story Snap Post               0.4622       2.45          -0.02% (2.45)   \n", "Story Snap View               0.7409       508.3        -0.01% (508.2)   \n", "\n", "stat                                                                    \n", "seed                                                                    \n", "assignment                                                GID_10 (10%)  \n", "metric                        p_mean_anova overall_avg                  \n", "App Active Days               0.5713       5.75           0.00% (5.75)  \n", "Chat Send                     0.3960       140.7        -0.12% (140.5)  \n", "Chat View                     0.4515       193.6        -0.16% (193.3)  \n", "Direct Snap Create            0.4781       66.37        -0.02% (66.36)  \n", "Direct Snap Send              0.3812       40.83         0.02% (40.84)  \n", "Direct Snap View              0.5145       162.3        -0.03% (162.2)  \n", "Direct Snap Save              0.7912       11.40        -0.06% (11.39)  \n", "DF Non-Friend Story View      0.8897       27.49         0.03% (27.50)  \n", "DF Non-Friend Story View Time 0.8374       795.0        -0.05% (794.5)  \n", "Friend Story View             0.5749       141.5        -0.03% (141.4)  \n", "Friend Story View Time        0.5445       373.1        -0.02% (373.1)  \n", "Lens Send                     0.5619       7.08           0.01% (7.08)  \n", "Lens Swipe                    0.7269       132.5        -0.03% (132.5)  \n", "Spotlight Story View          0.1783       88.36        -0.01% (88.35)  \n", "Spotlight Story View Time     0.1597       570.8         0.07% (571.2)  \n", "Story Snap Post               0.4622       2.45           0.12% (2.45)  \n", "Story Snap View               0.7409       508.3        -0.06% (508.0)  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>The fourth best seed is <strong style='color:blue;'>layer1_20250604_0017</strong></h3>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p>The table below displays the percent difference between the group's average and the overall average per user value for the metric.</p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>stat</th>\n", "      <th colspan=\"10\" halign=\"left\">percent_difference</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>seed</th>\n", "      <th colspan=\"10\" halign=\"left\">layer1_20250604_0017</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>assignment</th>\n", "      <th>GID_01 (10%)</th>\n", "      <th>GID_02 (10%)</th>\n", "      <th>GID_03 (10%)</th>\n", "      <th>GID_04 (10%)</th>\n", "      <th>GID_05 (10%)</th>\n", "      <th>GID_06 (10%)</th>\n", "      <th>GID_07 (10%)</th>\n", "      <th>GID_08 (10%)</th>\n", "      <th>GID_09 (10%)</th>\n", "      <th>GID_10 (10%)</th>\n", "    </tr>\n", "    <tr>\n", "      <th>metric</th>\n", "      <th>p_mean_anova</th>\n", "      <th>overall_avg</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>App Active Days</th>\n", "      <th>0.3730</th>\n", "      <th>5.75</th>\n", "      <td>0.00% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>-0.00% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>-0.01% (5.75)</td>\n", "      <td>-0.01% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>0.01% (5.75)</td>\n", "      <td>-0.01% (5.75)</td>\n", "      <td>-0.00% (5.75)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Chat Send</th>\n", "      <th>0.2255</th>\n", "      <th>140.7</th>\n", "      <td>-0.02% (140.7)</td>\n", "      <td>-0.00% (140.7)</td>\n", "      <td>-0.08% (140.6)</td>\n", "      <td>0.16% (140.9)</td>\n", "      <td>-0.07% (140.6)</td>\n", "      <td>-0.01% (140.7)</td>\n", "      <td>0.08% (140.8)</td>\n", "      <td>-0.00% (140.7)</td>\n", "      <td>-0.09% (140.6)</td>\n", "      <td>0.04% (140.7)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Chat View</th>\n", "      <th>0.1410</th>\n", "      <th>193.6</th>\n", "      <td>-0.06% (193.5)</td>\n", "      <td>0.00% (193.6)</td>\n", "      <td>-0.05% (193.5)</td>\n", "      <td>0.26% (194.1)</td>\n", "      <td>-0.06% (193.5)</td>\n", "      <td>-0.10% (193.4)</td>\n", "      <td>0.11% (193.8)</td>\n", "      <td>0.01% (193.6)</td>\n", "      <td>-0.04% (193.5)</td>\n", "      <td>-0.09% (193.4)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap Create</th>\n", "      <th>0.4298</th>\n", "      <th>66.35</th>\n", "      <td>0.00% (66.35)</td>\n", "      <td>-0.11% (66.27)</td>\n", "      <td>-0.06% (66.31)</td>\n", "      <td>0.07% (66.39)</td>\n", "      <td>-0.02% (66.34)</td>\n", "      <td>0.03% (66.37)</td>\n", "      <td>0.06% (66.39)</td>\n", "      <td>-0.08% (66.29)</td>\n", "      <td>0.06% (66.39)</td>\n", "      <td>0.07% (66.39)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap Send</th>\n", "      <th>0.2242</th>\n", "      <th>40.81</th>\n", "      <td>0.01% (40.82)</td>\n", "      <td>-0.15% (40.75)</td>\n", "      <td>-0.09% (40.78)</td>\n", "      <td>0.06% (40.84)</td>\n", "      <td>0.04% (40.83)</td>\n", "      <td>0.10% (40.85)</td>\n", "      <td>0.10% (40.86)</td>\n", "      <td>-0.20% (40.73)</td>\n", "      <td>0.02% (40.82)</td>\n", "      <td>0.11% (40.86)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap View</th>\n", "      <th>0.1312</th>\n", "      <th>162.2</th>\n", "      <td>-0.09% (162.1)</td>\n", "      <td>-0.08% (162.1)</td>\n", "      <td>-0.04% (162.2)</td>\n", "      <td>0.08% (162.4)</td>\n", "      <td>0.01% (162.3)</td>\n", "      <td>0.05% (162.3)</td>\n", "      <td>0.09% (162.4)</td>\n", "      <td>-0.07% (162.1)</td>\n", "      <td>-0.03% (162.2)</td>\n", "      <td>0.06% (162.3)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap Save</th>\n", "      <th>0.7990</th>\n", "      <th>11.40</th>\n", "      <td>-0.01% (11.40)</td>\n", "      <td>-0.00% (11.40)</td>\n", "      <td>-0.01% (11.40)</td>\n", "      <td>0.00% (11.40)</td>\n", "      <td>-0.06% (11.39)</td>\n", "      <td>-0.01% (11.40)</td>\n", "      <td>0.03% (11.40)</td>\n", "      <td>0.04% (11.40)</td>\n", "      <td>0.05% (11.40)</td>\n", "      <td>-0.02% (11.40)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DF Non-Friend Story View</th>\n", "      <th>0.5108</th>\n", "      <th>27.47</th>\n", "      <td>0.04% (27.48)</td>\n", "      <td>0.01% (27.47)</td>\n", "      <td>0.04% (27.48)</td>\n", "      <td>0.04% (27.48)</td>\n", "      <td>-0.07% (27.45)</td>\n", "      <td>-0.07% (27.45)</td>\n", "      <td>0.08% (27.49)</td>\n", "      <td>-0.17% (27.42)</td>\n", "      <td>0.05% (27.49)</td>\n", "      <td>0.06% (27.49)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DF Non-Friend Story View Time</th>\n", "      <th>0.5717</th>\n", "      <th>794.6</th>\n", "      <td>-0.03% (794.4)</td>\n", "      <td>0.06% (795.1)</td>\n", "      <td>0.07% (795.2)</td>\n", "      <td>0.02% (794.8)</td>\n", "      <td>-0.01% (794.6)</td>\n", "      <td>-0.14% (793.5)</td>\n", "      <td>0.03% (794.9)</td>\n", "      <td>-0.12% (793.6)</td>\n", "      <td>-0.03% (794.4)</td>\n", "      <td>0.14% (795.7)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Friend Story View</th>\n", "      <th>0.6026</th>\n", "      <th>141.4</th>\n", "      <td>0.06% (141.5)</td>\n", "      <td>-0.01% (141.4)</td>\n", "      <td>0.06% (141.5)</td>\n", "      <td>0.00% (141.4)</td>\n", "      <td>-0.07% (141.3)</td>\n", "      <td>-0.04% (141.4)</td>\n", "      <td>0.12% (141.6)</td>\n", "      <td>-0.08% (141.3)</td>\n", "      <td>-0.06% (141.4)</td>\n", "      <td>0.02% (141.5)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Friend Story View Time</th>\n", "      <th>0.3060</th>\n", "      <th>373.1</th>\n", "      <td>0.02% (373.1)</td>\n", "      <td>-0.06% (372.8)</td>\n", "      <td>0.09% (373.4)</td>\n", "      <td>-0.00% (373.1)</td>\n", "      <td>-0.05% (372.9)</td>\n", "      <td>-0.03% (373.0)</td>\n", "      <td>0.14% (373.6)</td>\n", "      <td>-0.07% (372.8)</td>\n", "      <td>-0.07% (372.8)</td>\n", "      <td>0.03% (373.2)</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON>s Send</th>\n", "      <th>0.4611</th>\n", "      <th>7.08</th>\n", "      <td>0.03% (7.08)</td>\n", "      <td>-0.04% (7.08)</td>\n", "      <td>-0.13% (7.07)</td>\n", "      <td>-0.11% (7.07)</td>\n", "      <td>0.11% (7.09)</td>\n", "      <td>0.09% (7.08)</td>\n", "      <td>0.14% (7.09)</td>\n", "      <td>-0.15% (7.07)</td>\n", "      <td>-0.06% (7.07)</td>\n", "      <td>0.12% (7.09)</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON> Swipe</th>\n", "      <th>0.1542</th>\n", "      <th>132.5</th>\n", "      <td>0.06% (132.6)</td>\n", "      <td>-0.07% (132.4)</td>\n", "      <td>0.03% (132.5)</td>\n", "      <td>-0.05% (132.4)</td>\n", "      <td>-0.04% (132.4)</td>\n", "      <td>0.04% (132.5)</td>\n", "      <td>0.02% (132.5)</td>\n", "      <td>0.06% (132.6)</td>\n", "      <td>0.00% (132.5)</td>\n", "      <td>-0.04% (132.4)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Spotlight Story View</th>\n", "      <th>0.4030</th>\n", "      <th>88.35</th>\n", "      <td>0.09% (88.43)</td>\n", "      <td>-0.16% (88.21)</td>\n", "      <td>-0.05% (88.31)</td>\n", "      <td>-0.10% (88.26)</td>\n", "      <td>0.01% (88.35)</td>\n", "      <td>0.12% (88.45)</td>\n", "      <td>0.05% (88.39)</td>\n", "      <td>-0.07% (88.29)</td>\n", "      <td>0.11% (88.45)</td>\n", "      <td>0.01% (88.36)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Spotlight Story View Time</th>\n", "      <th>0.1819</th>\n", "      <th>570.8</th>\n", "      <td>-0.17% (569.8)</td>\n", "      <td>-0.04% (570.5)</td>\n", "      <td>-0.17% (569.8)</td>\n", "      <td>-0.09% (570.3)</td>\n", "      <td>0.16% (571.7)</td>\n", "      <td>0.03% (570.9)</td>\n", "      <td>0.19% (571.9)</td>\n", "      <td>-0.10% (570.2)</td>\n", "      <td>0.16% (571.7)</td>\n", "      <td>0.03% (570.9)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Story Snap Post</th>\n", "      <th>0.9240</th>\n", "      <th>2.45</th>\n", "      <td>0.21% (2.45)</td>\n", "      <td>-0.03% (2.45)</td>\n", "      <td>0.03% (2.45)</td>\n", "      <td>0.05% (2.45)</td>\n", "      <td>-0.33% (2.44)</td>\n", "      <td>0.03% (2.45)</td>\n", "      <td>-0.06% (2.45)</td>\n", "      <td>-0.08% (2.45)</td>\n", "      <td>-0.14% (2.45)</td>\n", "      <td>0.34% (2.46)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Story Snap View</th>\n", "      <th>0.5751</th>\n", "      <th>508.1</th>\n", "      <td>0.02% (508.1)</td>\n", "      <td>0.00% (508.1)</td>\n", "      <td>0.06% (508.4)</td>\n", "      <td>0.04% (508.2)</td>\n", "      <td>-0.03% (507.9)</td>\n", "      <td>-0.07% (507.7)</td>\n", "      <td>0.07% (508.4)</td>\n", "      <td>-0.12% (507.4)</td>\n", "      <td>-0.03% (507.9)</td>\n", "      <td>0.06% (508.4)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["stat                                                     percent_difference  \\\n", "seed                                                   layer1_20250604_0017   \n", "assignment                                                     GID_01 (10%)   \n", "metric                        p_mean_anova overall_avg                        \n", "App Active Days               0.3730       5.75                0.00% (5.75)   \n", "Chat Send                     0.2255       140.7             -0.02% (140.7)   \n", "Chat View                     0.1410       193.6             -0.06% (193.5)   \n", "Direct Snap Create            0.4298       66.35              0.00% (66.35)   \n", "Direct Snap Send              0.2242       40.81              0.01% (40.82)   \n", "Direct Snap View              0.1312       162.2             -0.09% (162.1)   \n", "Direct Snap Save              0.7990       11.40             -0.01% (11.40)   \n", "DF Non-Friend Story View      0.5108       27.47              0.04% (27.48)   \n", "DF Non-Friend Story View Time 0.5717       794.6             -0.03% (794.4)   \n", "Friend Story View             0.6026       141.4              0.06% (141.5)   \n", "Friend Story View Time        0.3060       373.1              0.02% (373.1)   \n", "Lens Send                     0.4611       7.08                0.03% (7.08)   \n", "Lens Swipe                    0.1542       132.5              0.06% (132.6)   \n", "Spotlight Story View          0.4030       88.35              0.09% (88.43)   \n", "Spotlight Story View Time     0.1819       570.8             -0.17% (569.8)   \n", "Story Snap Post               0.9240       2.45                0.21% (2.45)   \n", "Story Snap View               0.5751       508.1              0.02% (508.1)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_02 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.3730       5.75           0.00% (5.75)   \n", "Chat Send                     0.2255       140.7        -0.00% (140.7)   \n", "Chat View                     0.1410       193.6         0.00% (193.6)   \n", "Direct Snap Create            0.4298       66.35        -0.11% (66.27)   \n", "Direct Snap Send              0.2242       40.81        -0.15% (40.75)   \n", "Direct Snap View              0.1312       162.2        -0.08% (162.1)   \n", "Direct Snap Save              0.7990       11.40        -0.00% (11.40)   \n", "DF Non-Friend Story View      0.5108       27.47         0.01% (27.47)   \n", "DF Non-Friend Story View Time 0.5717       794.6         0.06% (795.1)   \n", "Friend Story View             0.6026       141.4        -0.01% (141.4)   \n", "Friend Story View Time        0.3060       373.1        -0.06% (372.8)   \n", "Lens Send                     0.4611       7.08          -0.04% (7.08)   \n", "Lens Swipe                    0.1542       132.5        -0.07% (132.4)   \n", "Spotlight Story View          0.4030       88.35        -0.16% (88.21)   \n", "Spotlight Story View Time     0.1819       570.8        -0.04% (570.5)   \n", "Story Snap Post               0.9240       2.45          -0.03% (2.45)   \n", "Story Snap View               0.5751       508.1         0.00% (508.1)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_03 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.3730       5.75          -0.00% (5.75)   \n", "Chat Send                     0.2255       140.7        -0.08% (140.6)   \n", "Chat View                     0.1410       193.6        -0.05% (193.5)   \n", "Direct Snap Create            0.4298       66.35        -0.06% (66.31)   \n", "Direct Snap Send              0.2242       40.81        -0.09% (40.78)   \n", "Direct Snap View              0.1312       162.2        -0.04% (162.2)   \n", "Direct Snap Save              0.7990       11.40        -0.01% (11.40)   \n", "DF Non-Friend Story View      0.5108       27.47         0.04% (27.48)   \n", "DF Non-Friend Story View Time 0.5717       794.6         0.07% (795.2)   \n", "Friend Story View             0.6026       141.4         0.06% (141.5)   \n", "Friend Story View Time        0.3060       373.1         0.09% (373.4)   \n", "Lens Send                     0.4611       7.08          -0.13% (7.07)   \n", "Lens Swipe                    0.1542       132.5         0.03% (132.5)   \n", "Spotlight Story View          0.4030       88.35        -0.05% (88.31)   \n", "Spotlight Story View Time     0.1819       570.8        -0.17% (569.8)   \n", "Story Snap Post               0.9240       2.45           0.03% (2.45)   \n", "Story Snap View               0.5751       508.1         0.06% (508.4)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_04 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.3730       5.75           0.00% (5.75)   \n", "Chat Send                     0.2255       140.7         0.16% (140.9)   \n", "Chat View                     0.1410       193.6         0.26% (194.1)   \n", "Direct Snap Create            0.4298       66.35         0.07% (66.39)   \n", "Direct Snap Send              0.2242       40.81         0.06% (40.84)   \n", "Direct Snap View              0.1312       162.2         0.08% (162.4)   \n", "Direct Snap Save              0.7990       11.40         0.00% (11.40)   \n", "DF Non-Friend Story View      0.5108       27.47         0.04% (27.48)   \n", "DF Non-Friend Story View Time 0.5717       794.6         0.02% (794.8)   \n", "Friend Story View             0.6026       141.4         0.00% (141.4)   \n", "Friend Story View Time        0.3060       373.1        -0.00% (373.1)   \n", "Lens Send                     0.4611       7.08          -0.11% (7.07)   \n", "Lens Swipe                    0.1542       132.5        -0.05% (132.4)   \n", "Spotlight Story View          0.4030       88.35        -0.10% (88.26)   \n", "Spotlight Story View Time     0.1819       570.8        -0.09% (570.3)   \n", "Story Snap Post               0.9240       2.45           0.05% (2.45)   \n", "Story Snap View               0.5751       508.1         0.04% (508.2)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_05 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.3730       5.75          -0.01% (5.75)   \n", "Chat Send                     0.2255       140.7        -0.07% (140.6)   \n", "Chat View                     0.1410       193.6        -0.06% (193.5)   \n", "Direct Snap Create            0.4298       66.35        -0.02% (66.34)   \n", "Direct Snap Send              0.2242       40.81         0.04% (40.83)   \n", "Direct Snap View              0.1312       162.2         0.01% (162.3)   \n", "Direct Snap Save              0.7990       11.40        -0.06% (11.39)   \n", "DF Non-Friend Story View      0.5108       27.47        -0.07% (27.45)   \n", "DF Non-Friend Story View Time 0.5717       794.6        -0.01% (794.6)   \n", "Friend Story View             0.6026       141.4        -0.07% (141.3)   \n", "Friend Story View Time        0.3060       373.1        -0.05% (372.9)   \n", "Lens Send                     0.4611       7.08           0.11% (7.09)   \n", "Lens Swipe                    0.1542       132.5        -0.04% (132.4)   \n", "Spotlight Story View          0.4030       88.35         0.01% (88.35)   \n", "Spotlight Story View Time     0.1819       570.8         0.16% (571.7)   \n", "Story Snap Post               0.9240       2.45          -0.33% (2.44)   \n", "Story Snap View               0.5751       508.1        -0.03% (507.9)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_06 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.3730       5.75          -0.01% (5.75)   \n", "Chat Send                     0.2255       140.7        -0.01% (140.7)   \n", "Chat View                     0.1410       193.6        -0.10% (193.4)   \n", "Direct Snap Create            0.4298       66.35         0.03% (66.37)   \n", "Direct Snap Send              0.2242       40.81         0.10% (40.85)   \n", "Direct Snap View              0.1312       162.2         0.05% (162.3)   \n", "Direct Snap Save              0.7990       11.40        -0.01% (11.40)   \n", "DF Non-Friend Story View      0.5108       27.47        -0.07% (27.45)   \n", "DF Non-Friend Story View Time 0.5717       794.6        -0.14% (793.5)   \n", "Friend Story View             0.6026       141.4        -0.04% (141.4)   \n", "Friend Story View Time        0.3060       373.1        -0.03% (373.0)   \n", "Lens Send                     0.4611       7.08           0.09% (7.08)   \n", "Lens Swipe                    0.1542       132.5         0.04% (132.5)   \n", "Spotlight Story View          0.4030       88.35         0.12% (88.45)   \n", "Spotlight Story View Time     0.1819       570.8         0.03% (570.9)   \n", "Story Snap Post               0.9240       2.45           0.03% (2.45)   \n", "Story Snap View               0.5751       508.1        -0.07% (507.7)   \n", "\n", "stat                                                                   \\\n", "seed                                                                    \n", "assignment                                               GID_07 (10%)   \n", "metric                        p_mean_anova overall_avg                  \n", "App Active Days               0.3730       5.75          0.00% (5.75)   \n", "Chat Send                     0.2255       140.7        0.08% (140.8)   \n", "Chat View                     0.1410       193.6        0.11% (193.8)   \n", "Direct Snap Create            0.4298       66.35        0.06% (66.39)   \n", "Direct Snap Send              0.2242       40.81        0.10% (40.86)   \n", "Direct Snap View              0.1312       162.2        0.09% (162.4)   \n", "Direct Snap Save              0.7990       11.40        0.03% (11.40)   \n", "DF Non-Friend Story View      0.5108       27.47        0.08% (27.49)   \n", "DF Non-Friend Story View Time 0.5717       794.6        0.03% (794.9)   \n", "Friend Story View             0.6026       141.4        0.12% (141.6)   \n", "Friend Story View Time        0.3060       373.1        0.14% (373.6)   \n", "Lens Send                     0.4611       7.08          0.14% (7.09)   \n", "Lens Swipe                    0.1542       132.5        0.02% (132.5)   \n", "Spotlight Story View          0.4030       88.35        0.05% (88.39)   \n", "Spotlight Story View Time     0.1819       570.8        0.19% (571.9)   \n", "Story Snap Post               0.9240       2.45         -0.06% (2.45)   \n", "Story Snap View               0.5751       508.1        0.07% (508.4)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_08 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.3730       5.75           0.01% (5.75)   \n", "Chat Send                     0.2255       140.7        -0.00% (140.7)   \n", "Chat View                     0.1410       193.6         0.01% (193.6)   \n", "Direct Snap Create            0.4298       66.35        -0.08% (66.29)   \n", "Direct Snap Send              0.2242       40.81        -0.20% (40.73)   \n", "Direct Snap View              0.1312       162.2        -0.07% (162.1)   \n", "Direct Snap Save              0.7990       11.40         0.04% (11.40)   \n", "DF Non-Friend Story View      0.5108       27.47        -0.17% (27.42)   \n", "DF Non-Friend Story View Time 0.5717       794.6        -0.12% (793.6)   \n", "Friend Story View             0.6026       141.4        -0.08% (141.3)   \n", "Friend Story View Time        0.3060       373.1        -0.07% (372.8)   \n", "Lens Send                     0.4611       7.08          -0.15% (7.07)   \n", "Lens Swipe                    0.1542       132.5         0.06% (132.6)   \n", "Spotlight Story View          0.4030       88.35        -0.07% (88.29)   \n", "Spotlight Story View Time     0.1819       570.8        -0.10% (570.2)   \n", "Story Snap Post               0.9240       2.45          -0.08% (2.45)   \n", "Story Snap View               0.5751       508.1        -0.12% (507.4)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_09 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.3730       5.75          -0.01% (5.75)   \n", "Chat Send                     0.2255       140.7        -0.09% (140.6)   \n", "Chat View                     0.1410       193.6        -0.04% (193.5)   \n", "Direct Snap Create            0.4298       66.35         0.06% (66.39)   \n", "Direct Snap Send              0.2242       40.81         0.02% (40.82)   \n", "Direct Snap View              0.1312       162.2        -0.03% (162.2)   \n", "Direct Snap Save              0.7990       11.40         0.05% (11.40)   \n", "DF Non-Friend Story View      0.5108       27.47         0.05% (27.49)   \n", "DF Non-Friend Story View Time 0.5717       794.6        -0.03% (794.4)   \n", "Friend Story View             0.6026       141.4        -0.06% (141.4)   \n", "Friend Story View Time        0.3060       373.1        -0.07% (372.8)   \n", "Lens Send                     0.4611       7.08          -0.06% (7.07)   \n", "Lens Swipe                    0.1542       132.5         0.00% (132.5)   \n", "Spotlight Story View          0.4030       88.35         0.11% (88.45)   \n", "Spotlight Story View Time     0.1819       570.8         0.16% (571.7)   \n", "Story Snap Post               0.9240       2.45          -0.14% (2.45)   \n", "Story Snap View               0.5751       508.1        -0.03% (507.9)   \n", "\n", "stat                                                                    \n", "seed                                                                    \n", "assignment                                                GID_10 (10%)  \n", "metric                        p_mean_anova overall_avg                  \n", "App Active Days               0.3730       5.75          -0.00% (5.75)  \n", "Chat Send                     0.2255       140.7         0.04% (140.7)  \n", "Chat View                     0.1410       193.6        -0.09% (193.4)  \n", "Direct Snap Create            0.4298       66.35         0.07% (66.39)  \n", "Direct Snap Send              0.2242       40.81         0.11% (40.86)  \n", "Direct Snap View              0.1312       162.2         0.06% (162.3)  \n", "Direct Snap Save              0.7990       11.40        -0.02% (11.40)  \n", "DF Non-Friend Story View      0.5108       27.47         0.06% (27.49)  \n", "DF Non-Friend Story View Time 0.5717       794.6         0.14% (795.7)  \n", "Friend Story View             0.6026       141.4         0.02% (141.5)  \n", "Friend Story View Time        0.3060       373.1         0.03% (373.2)  \n", "Lens Send                     0.4611       7.08           0.12% (7.09)  \n", "Lens Swipe                    0.1542       132.5        -0.04% (132.4)  \n", "Spotlight Story View          0.4030       88.35         0.01% (88.36)  \n", "Spotlight Story View Time     0.1819       570.8         0.03% (570.9)  \n", "Story Snap Post               0.9240       2.45           0.34% (2.46)  \n", "Story Snap View               0.5751       508.1         0.06% (508.4)  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>The fifth best seed is <strong style='color:blue;'>layer1_20250604_0019</strong></h3>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p>The table below displays the percent difference between the group's average and the overall average per user value for the metric.</p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>stat</th>\n", "      <th colspan=\"10\" halign=\"left\">percent_difference</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>seed</th>\n", "      <th colspan=\"10\" halign=\"left\">layer1_20250604_0019</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>assignment</th>\n", "      <th>GID_01 (10%)</th>\n", "      <th>GID_02 (10%)</th>\n", "      <th>GID_03 (10%)</th>\n", "      <th>GID_04 (10%)</th>\n", "      <th>GID_05 (10%)</th>\n", "      <th>GID_06 (10%)</th>\n", "      <th>GID_07 (10%)</th>\n", "      <th>GID_08 (10%)</th>\n", "      <th>GID_09 (10%)</th>\n", "      <th>GID_10 (10%)</th>\n", "    </tr>\n", "    <tr>\n", "      <th>metric</th>\n", "      <th>p_mean_anova</th>\n", "      <th>overall_avg</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>App Active Days</th>\n", "      <th>0.1305</th>\n", "      <th>5.75</th>\n", "      <td>0.00% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>-0.01% (5.75)</td>\n", "      <td>0.01% (5.75)</td>\n", "      <td>0.01% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>-0.01% (5.75)</td>\n", "      <td>-0.01% (5.75)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Chat Send</th>\n", "      <th>0.7560</th>\n", "      <th>140.7</th>\n", "      <td>-0.02% (140.7)</td>\n", "      <td>-0.07% (140.6)</td>\n", "      <td>-0.03% (140.7)</td>\n", "      <td>0.07% (140.8)</td>\n", "      <td>-0.06% (140.6)</td>\n", "      <td>0.07% (140.8)</td>\n", "      <td>0.07% (140.8)</td>\n", "      <td>-0.03% (140.7)</td>\n", "      <td>0.02% (140.7)</td>\n", "      <td>-0.03% (140.7)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Chat View</th>\n", "      <th>0.9821</th>\n", "      <th>193.6</th>\n", "      <td>0.05% (193.7)</td>\n", "      <td>-0.10% (193.4)</td>\n", "      <td>0.06% (193.7)</td>\n", "      <td>0.03% (193.7)</td>\n", "      <td>-0.06% (193.5)</td>\n", "      <td>0.01% (193.6)</td>\n", "      <td>0.02% (193.6)</td>\n", "      <td>0.01% (193.6)</td>\n", "      <td>-0.01% (193.6)</td>\n", "      <td>-0.01% (193.6)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap Create</th>\n", "      <th>0.8553</th>\n", "      <th>66.35</th>\n", "      <td>-0.04% (66.33)</td>\n", "      <td>-0.06% (66.31)</td>\n", "      <td>0.02% (66.36)</td>\n", "      <td>0.06% (66.39)</td>\n", "      <td>-0.04% (66.33)</td>\n", "      <td>-0.03% (66.33)</td>\n", "      <td>0.07% (66.40)</td>\n", "      <td>0.07% (66.40)</td>\n", "      <td>-0.02% (66.34)</td>\n", "      <td>-0.02% (66.34)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap Send</th>\n", "      <th>0.7632</th>\n", "      <th>40.81</th>\n", "      <td>-0.01% (40.80)</td>\n", "      <td>-0.16% (40.74)</td>\n", "      <td>0.01% (40.81)</td>\n", "      <td>0.06% (40.83)</td>\n", "      <td>-0.04% (40.79)</td>\n", "      <td>-0.03% (40.80)</td>\n", "      <td>0.09% (40.85)</td>\n", "      <td>0.09% (40.84)</td>\n", "      <td>-0.05% (40.79)</td>\n", "      <td>0.04% (40.82)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap View</th>\n", "      <th>0.8088</th>\n", "      <th>162.3</th>\n", "      <td>0.02% (162.3)</td>\n", "      <td>-0.08% (162.1)</td>\n", "      <td>0.00% (162.3)</td>\n", "      <td>0.00% (162.3)</td>\n", "      <td>0.01% (162.3)</td>\n", "      <td>-0.04% (162.2)</td>\n", "      <td>-0.02% (162.2)</td>\n", "      <td>0.08% (162.4)</td>\n", "      <td>-0.00% (162.3)</td>\n", "      <td>0.04% (162.3)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap Save</th>\n", "      <th>0.5322</th>\n", "      <th>11.40</th>\n", "      <td>0.00% (11.40)</td>\n", "      <td>-0.01% (11.40)</td>\n", "      <td>-0.03% (11.40)</td>\n", "      <td>-0.04% (11.39)</td>\n", "      <td>0.04% (11.40)</td>\n", "      <td>-0.03% (11.40)</td>\n", "      <td>0.04% (11.40)</td>\n", "      <td>0.06% (11.41)</td>\n", "      <td>0.03% (11.40)</td>\n", "      <td>-0.05% (11.39)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DF Non-Friend Story View</th>\n", "      <th>0.6829</th>\n", "      <th>27.48</th>\n", "      <td>0.10% (27.51)</td>\n", "      <td>-0.07% (27.46)</td>\n", "      <td>-0.08% (27.46)</td>\n", "      <td>0.09% (27.51)</td>\n", "      <td>-0.00% (27.48)</td>\n", "      <td>0.08% (27.51)</td>\n", "      <td>0.01% (27.49)</td>\n", "      <td>-0.00% (27.48)</td>\n", "      <td>-0.10% (27.46)</td>\n", "      <td>-0.02% (27.48)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DF Non-Friend Story View Time</th>\n", "      <th>0.5325</th>\n", "      <th>795.1</th>\n", "      <td>0.11% (795.9)</td>\n", "      <td>-0.07% (794.5)</td>\n", "      <td>-0.07% (794.5)</td>\n", "      <td>0.17% (796.4)</td>\n", "      <td>-0.03% (794.8)</td>\n", "      <td>-0.03% (794.8)</td>\n", "      <td>0.06% (795.5)</td>\n", "      <td>0.01% (795.1)</td>\n", "      <td>-0.03% (794.8)</td>\n", "      <td>-0.10% (794.3)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Friend Story View</th>\n", "      <th>0.7943</th>\n", "      <th>141.4</th>\n", "      <td>0.02% (141.5)</td>\n", "      <td>-0.02% (141.4)</td>\n", "      <td>-0.10% (141.3)</td>\n", "      <td>0.03% (141.5)</td>\n", "      <td>-0.02% (141.4)</td>\n", "      <td>0.06% (141.5)</td>\n", "      <td>0.08% (141.6)</td>\n", "      <td>0.03% (141.5)</td>\n", "      <td>-0.02% (141.4)</td>\n", "      <td>-0.06% (141.3)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Friend Story View Time</th>\n", "      <th>0.7932</th>\n", "      <th>373.1</th>\n", "      <td>0.04% (373.2)</td>\n", "      <td>-0.05% (372.9)</td>\n", "      <td>-0.08% (372.8)</td>\n", "      <td>0.06% (373.3)</td>\n", "      <td>-0.04% (372.9)</td>\n", "      <td>0.06% (373.3)</td>\n", "      <td>0.03% (373.2)</td>\n", "      <td>0.02% (373.1)</td>\n", "      <td>0.02% (373.1)</td>\n", "      <td>-0.05% (372.9)</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON>s Send</th>\n", "      <th>0.8432</th>\n", "      <th>7.08</th>\n", "      <td>0.09% (7.09)</td>\n", "      <td>-0.09% (7.07)</td>\n", "      <td>-0.15% (7.07)</td>\n", "      <td>-0.06% (7.08)</td>\n", "      <td>0.03% (7.08)</td>\n", "      <td>0.04% (7.08)</td>\n", "      <td>0.04% (7.08)</td>\n", "      <td>-0.01% (7.08)</td>\n", "      <td>0.12% (7.09)</td>\n", "      <td>-0.00% (7.08)</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON> Swipe</th>\n", "      <th>0.6622</th>\n", "      <th>132.5</th>\n", "      <td>-0.02% (132.5)</td>\n", "      <td>-0.02% (132.5)</td>\n", "      <td>0.01% (132.5)</td>\n", "      <td>-0.01% (132.5)</td>\n", "      <td>-0.02% (132.5)</td>\n", "      <td>-0.04% (132.5)</td>\n", "      <td>0.01% (132.5)</td>\n", "      <td>-0.03% (132.5)</td>\n", "      <td>0.06% (132.6)</td>\n", "      <td>0.05% (132.6)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Spotlight Story View</th>\n", "      <th>0.9572</th>\n", "      <th>88.43</th>\n", "      <td>-0.06% (88.38)</td>\n", "      <td>-0.02% (88.41)</td>\n", "      <td>0.00% (88.43)</td>\n", "      <td>-0.04% (88.39)</td>\n", "      <td>0.06% (88.48)</td>\n", "      <td>-0.05% (88.39)</td>\n", "      <td>0.06% (88.48)</td>\n", "      <td>-0.08% (88.36)</td>\n", "      <td>0.05% (88.47)</td>\n", "      <td>0.06% (88.48)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Spotlight Story View Time</th>\n", "      <th>0.4732</th>\n", "      <th>571.0</th>\n", "      <td>0.07% (571.5)</td>\n", "      <td>-0.04% (570.8)</td>\n", "      <td>-0.00% (571.0)</td>\n", "      <td>-0.08% (570.6)</td>\n", "      <td>0.03% (571.2)</td>\n", "      <td>-0.13% (570.3)</td>\n", "      <td>0.16% (572.0)</td>\n", "      <td>-0.17% (570.1)</td>\n", "      <td>0.15% (571.9)</td>\n", "      <td>0.02% (571.2)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Story Snap Post</th>\n", "      <th>0.3173</th>\n", "      <th>2.45</th>\n", "      <td>-0.36% (2.44)</td>\n", "      <td>0.28% (2.46)</td>\n", "      <td>-0.02% (2.45)</td>\n", "      <td>0.11% (2.45)</td>\n", "      <td>-0.16% (2.45)</td>\n", "      <td>0.61% (2.47)</td>\n", "      <td>-0.14% (2.45)</td>\n", "      <td>-0.14% (2.45)</td>\n", "      <td>0.17% (2.45)</td>\n", "      <td>-0.39% (2.44)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Story Snap View</th>\n", "      <th>0.6444</th>\n", "      <th>508.3</th>\n", "      <td>0.06% (508.6)</td>\n", "      <td>-0.07% (507.9)</td>\n", "      <td>-0.04% (508.1)</td>\n", "      <td>0.07% (508.7)</td>\n", "      <td>0.03% (508.4)</td>\n", "      <td>0.03% (508.5)</td>\n", "      <td>0.08% (508.7)</td>\n", "      <td>-0.06% (508.0)</td>\n", "      <td>-0.06% (508.0)</td>\n", "      <td>-0.03% (508.1)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["stat                                                     percent_difference  \\\n", "seed                                                   layer1_20250604_0019   \n", "assignment                                                     GID_01 (10%)   \n", "metric                        p_mean_anova overall_avg                        \n", "App Active Days               0.1305       5.75                0.00% (5.75)   \n", "Chat Send                     0.7560       140.7             -0.02% (140.7)   \n", "Chat View                     0.9821       193.6              0.05% (193.7)   \n", "Direct Snap Create            0.8553       66.35             -0.04% (66.33)   \n", "Direct Snap Send              0.7632       40.81             -0.01% (40.80)   \n", "Direct Snap View              0.8088       162.3              0.02% (162.3)   \n", "Direct Snap Save              0.5322       11.40              0.00% (11.40)   \n", "DF Non-Friend Story View      0.6829       27.48              0.10% (27.51)   \n", "DF Non-Friend Story View Time 0.5325       795.1              0.11% (795.9)   \n", "Friend Story View             0.7943       141.4              0.02% (141.5)   \n", "Friend Story View Time        0.7932       373.1              0.04% (373.2)   \n", "Lens Send                     0.8432       7.08                0.09% (7.09)   \n", "Lens Swipe                    0.6622       132.5             -0.02% (132.5)   \n", "Spotlight Story View          0.9572       88.43             -0.06% (88.38)   \n", "Spotlight Story View Time     0.4732       571.0              0.07% (571.5)   \n", "Story Snap Post               0.3173       2.45               -0.36% (2.44)   \n", "Story Snap View               0.6444       508.3              0.06% (508.6)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_02 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.1305       5.75           0.00% (5.75)   \n", "Chat Send                     0.7560       140.7        -0.07% (140.6)   \n", "Chat View                     0.9821       193.6        -0.10% (193.4)   \n", "Direct Snap Create            0.8553       66.35        -0.06% (66.31)   \n", "Direct Snap Send              0.7632       40.81        -0.16% (40.74)   \n", "Direct Snap View              0.8088       162.3        -0.08% (162.1)   \n", "Direct Snap Save              0.5322       11.40        -0.01% (11.40)   \n", "DF Non-Friend Story View      0.6829       27.48        -0.07% (27.46)   \n", "DF Non-Friend Story View Time 0.5325       795.1        -0.07% (794.5)   \n", "Friend Story View             0.7943       141.4        -0.02% (141.4)   \n", "Friend Story View Time        0.7932       373.1        -0.05% (372.9)   \n", "Lens Send                     0.8432       7.08          -0.09% (7.07)   \n", "Lens Swipe                    0.6622       132.5        -0.02% (132.5)   \n", "Spotlight Story View          0.9572       88.43        -0.02% (88.41)   \n", "Spotlight Story View Time     0.4732       571.0        -0.04% (570.8)   \n", "Story Snap Post               0.3173       2.45           0.28% (2.46)   \n", "Story Snap View               0.6444       508.3        -0.07% (507.9)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_03 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.1305       5.75          -0.01% (5.75)   \n", "Chat Send                     0.7560       140.7        -0.03% (140.7)   \n", "Chat View                     0.9821       193.6         0.06% (193.7)   \n", "Direct Snap Create            0.8553       66.35         0.02% (66.36)   \n", "Direct Snap Send              0.7632       40.81         0.01% (40.81)   \n", "Direct Snap View              0.8088       162.3         0.00% (162.3)   \n", "Direct Snap Save              0.5322       11.40        -0.03% (11.40)   \n", "DF Non-Friend Story View      0.6829       27.48        -0.08% (27.46)   \n", "DF Non-Friend Story View Time 0.5325       795.1        -0.07% (794.5)   \n", "Friend Story View             0.7943       141.4        -0.10% (141.3)   \n", "Friend Story View Time        0.7932       373.1        -0.08% (372.8)   \n", "Lens Send                     0.8432       7.08          -0.15% (7.07)   \n", "Lens Swipe                    0.6622       132.5         0.01% (132.5)   \n", "Spotlight Story View          0.9572       88.43         0.00% (88.43)   \n", "Spotlight Story View Time     0.4732       571.0        -0.00% (571.0)   \n", "Story Snap Post               0.3173       2.45          -0.02% (2.45)   \n", "Story Snap View               0.6444       508.3        -0.04% (508.1)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_04 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.1305       5.75           0.01% (5.75)   \n", "Chat Send                     0.7560       140.7         0.07% (140.8)   \n", "Chat View                     0.9821       193.6         0.03% (193.7)   \n", "Direct Snap Create            0.8553       66.35         0.06% (66.39)   \n", "Direct Snap Send              0.7632       40.81         0.06% (40.83)   \n", "Direct Snap View              0.8088       162.3         0.00% (162.3)   \n", "Direct Snap Save              0.5322       11.40        -0.04% (11.39)   \n", "DF Non-Friend Story View      0.6829       27.48         0.09% (27.51)   \n", "DF Non-Friend Story View Time 0.5325       795.1         0.17% (796.4)   \n", "Friend Story View             0.7943       141.4         0.03% (141.5)   \n", "Friend Story View Time        0.7932       373.1         0.06% (373.3)   \n", "Lens Send                     0.8432       7.08          -0.06% (7.08)   \n", "Lens Swipe                    0.6622       132.5        -0.01% (132.5)   \n", "Spotlight Story View          0.9572       88.43        -0.04% (88.39)   \n", "Spotlight Story View Time     0.4732       571.0        -0.08% (570.6)   \n", "Story Snap Post               0.3173       2.45           0.11% (2.45)   \n", "Story Snap View               0.6444       508.3         0.07% (508.7)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_05 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.1305       5.75           0.01% (5.75)   \n", "Chat Send                     0.7560       140.7        -0.06% (140.6)   \n", "Chat View                     0.9821       193.6        -0.06% (193.5)   \n", "Direct Snap Create            0.8553       66.35        -0.04% (66.33)   \n", "Direct Snap Send              0.7632       40.81        -0.04% (40.79)   \n", "Direct Snap View              0.8088       162.3         0.01% (162.3)   \n", "Direct Snap Save              0.5322       11.40         0.04% (11.40)   \n", "DF Non-Friend Story View      0.6829       27.48        -0.00% (27.48)   \n", "DF Non-Friend Story View Time 0.5325       795.1        -0.03% (794.8)   \n", "Friend Story View             0.7943       141.4        -0.02% (141.4)   \n", "Friend Story View Time        0.7932       373.1        -0.04% (372.9)   \n", "Lens Send                     0.8432       7.08           0.03% (7.08)   \n", "Lens Swipe                    0.6622       132.5        -0.02% (132.5)   \n", "Spotlight Story View          0.9572       88.43         0.06% (88.48)   \n", "Spotlight Story View Time     0.4732       571.0         0.03% (571.2)   \n", "Story Snap Post               0.3173       2.45          -0.16% (2.45)   \n", "Story Snap View               0.6444       508.3         0.03% (508.4)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_06 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.1305       5.75           0.00% (5.75)   \n", "Chat Send                     0.7560       140.7         0.07% (140.8)   \n", "Chat View                     0.9821       193.6         0.01% (193.6)   \n", "Direct Snap Create            0.8553       66.35        -0.03% (66.33)   \n", "Direct Snap Send              0.7632       40.81        -0.03% (40.80)   \n", "Direct Snap View              0.8088       162.3        -0.04% (162.2)   \n", "Direct Snap Save              0.5322       11.40        -0.03% (11.40)   \n", "DF Non-Friend Story View      0.6829       27.48         0.08% (27.51)   \n", "DF Non-Friend Story View Time 0.5325       795.1        -0.03% (794.8)   \n", "Friend Story View             0.7943       141.4         0.06% (141.5)   \n", "Friend Story View Time        0.7932       373.1         0.06% (373.3)   \n", "Lens Send                     0.8432       7.08           0.04% (7.08)   \n", "Lens Swipe                    0.6622       132.5        -0.04% (132.5)   \n", "Spotlight Story View          0.9572       88.43        -0.05% (88.39)   \n", "Spotlight Story View Time     0.4732       571.0        -0.13% (570.3)   \n", "Story Snap Post               0.3173       2.45           0.61% (2.47)   \n", "Story Snap View               0.6444       508.3         0.03% (508.5)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_07 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.1305       5.75           0.00% (5.75)   \n", "Chat Send                     0.7560       140.7         0.07% (140.8)   \n", "Chat View                     0.9821       193.6         0.02% (193.6)   \n", "Direct Snap Create            0.8553       66.35         0.07% (66.40)   \n", "Direct Snap Send              0.7632       40.81         0.09% (40.85)   \n", "Direct Snap View              0.8088       162.3        -0.02% (162.2)   \n", "Direct Snap Save              0.5322       11.40         0.04% (11.40)   \n", "DF Non-Friend Story View      0.6829       27.48         0.01% (27.49)   \n", "DF Non-Friend Story View Time 0.5325       795.1         0.06% (795.5)   \n", "Friend Story View             0.7943       141.4         0.08% (141.6)   \n", "Friend Story View Time        0.7932       373.1         0.03% (373.2)   \n", "Lens Send                     0.8432       7.08           0.04% (7.08)   \n", "Lens Swipe                    0.6622       132.5         0.01% (132.5)   \n", "Spotlight Story View          0.9572       88.43         0.06% (88.48)   \n", "Spotlight Story View Time     0.4732       571.0         0.16% (572.0)   \n", "Story Snap Post               0.3173       2.45          -0.14% (2.45)   \n", "Story Snap View               0.6444       508.3         0.08% (508.7)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_08 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.1305       5.75           0.00% (5.75)   \n", "Chat Send                     0.7560       140.7        -0.03% (140.7)   \n", "Chat View                     0.9821       193.6         0.01% (193.6)   \n", "Direct Snap Create            0.8553       66.35         0.07% (66.40)   \n", "Direct Snap Send              0.7632       40.81         0.09% (40.84)   \n", "Direct Snap View              0.8088       162.3         0.08% (162.4)   \n", "Direct Snap Save              0.5322       11.40         0.06% (11.41)   \n", "DF Non-Friend Story View      0.6829       27.48        -0.00% (27.48)   \n", "DF Non-Friend Story View Time 0.5325       795.1         0.01% (795.1)   \n", "Friend Story View             0.7943       141.4         0.03% (141.5)   \n", "Friend Story View Time        0.7932       373.1         0.02% (373.1)   \n", "Lens Send                     0.8432       7.08          -0.01% (7.08)   \n", "Lens Swipe                    0.6622       132.5        -0.03% (132.5)   \n", "Spotlight Story View          0.9572       88.43        -0.08% (88.36)   \n", "Spotlight Story View Time     0.4732       571.0        -0.17% (570.1)   \n", "Story Snap Post               0.3173       2.45          -0.14% (2.45)   \n", "Story Snap View               0.6444       508.3        -0.06% (508.0)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_09 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.1305       5.75          -0.01% (5.75)   \n", "Chat Send                     0.7560       140.7         0.02% (140.7)   \n", "Chat View                     0.9821       193.6        -0.01% (193.6)   \n", "Direct Snap Create            0.8553       66.35        -0.02% (66.34)   \n", "Direct Snap Send              0.7632       40.81        -0.05% (40.79)   \n", "Direct Snap View              0.8088       162.3        -0.00% (162.3)   \n", "Direct Snap Save              0.5322       11.40         0.03% (11.40)   \n", "DF Non-Friend Story View      0.6829       27.48        -0.10% (27.46)   \n", "DF Non-Friend Story View Time 0.5325       795.1        -0.03% (794.8)   \n", "Friend Story View             0.7943       141.4        -0.02% (141.4)   \n", "Friend Story View Time        0.7932       373.1         0.02% (373.1)   \n", "Lens Send                     0.8432       7.08           0.12% (7.09)   \n", "Lens Swipe                    0.6622       132.5         0.06% (132.6)   \n", "Spotlight Story View          0.9572       88.43         0.05% (88.47)   \n", "Spotlight Story View Time     0.4732       571.0         0.15% (571.9)   \n", "Story Snap Post               0.3173       2.45           0.17% (2.45)   \n", "Story Snap View               0.6444       508.3        -0.06% (508.0)   \n", "\n", "stat                                                                    \n", "seed                                                                    \n", "assignment                                                GID_10 (10%)  \n", "metric                        p_mean_anova overall_avg                  \n", "App Active Days               0.1305       5.75          -0.01% (5.75)  \n", "Chat Send                     0.7560       140.7        -0.03% (140.7)  \n", "Chat View                     0.9821       193.6        -0.01% (193.6)  \n", "Direct Snap Create            0.8553       66.35        -0.02% (66.34)  \n", "Direct Snap Send              0.7632       40.81         0.04% (40.82)  \n", "Direct Snap View              0.8088       162.3         0.04% (162.3)  \n", "Direct Snap Save              0.5322       11.40        -0.05% (11.39)  \n", "DF Non-Friend Story View      0.6829       27.48        -0.02% (27.48)  \n", "DF Non-Friend Story View Time 0.5325       795.1        -0.10% (794.3)  \n", "Friend Story View             0.7943       141.4        -0.06% (141.3)  \n", "Friend Story View Time        0.7932       373.1        -0.05% (372.9)  \n", "Lens Send                     0.8432       7.08          -0.00% (7.08)  \n", "Lens Swipe                    0.6622       132.5         0.05% (132.6)  \n", "Spotlight Story View          0.9572       88.43         0.06% (88.48)  \n", "Spotlight Story View Time     0.4732       571.0         0.02% (571.2)  \n", "Story Snap Post               0.3173       2.45          -0.39% (2.44)  \n", "Story Snap View               0.6444       508.3        -0.03% (508.1)  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>The sixth best seed is <strong style='color:blue;'>layer1_20250604_0016</strong></h3>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p>The table below displays the percent difference between the group's average and the overall average per user value for the metric.</p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>stat</th>\n", "      <th colspan=\"10\" halign=\"left\">percent_difference</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>seed</th>\n", "      <th colspan=\"10\" halign=\"left\">layer1_20250604_0016</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>assignment</th>\n", "      <th>GID_01 (10%)</th>\n", "      <th>GID_02 (10%)</th>\n", "      <th>GID_03 (10%)</th>\n", "      <th>GID_04 (10%)</th>\n", "      <th>GID_05 (10%)</th>\n", "      <th>GID_06 (10%)</th>\n", "      <th>GID_07 (10%)</th>\n", "      <th>GID_08 (10%)</th>\n", "      <th>GID_09 (10%)</th>\n", "      <th>GID_10 (10%)</th>\n", "    </tr>\n", "    <tr>\n", "      <th>metric</th>\n", "      <th>p_mean_anova</th>\n", "      <th>overall_avg</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>App Active Days</th>\n", "      <th>0.4168</th>\n", "      <th>5.75</th>\n", "      <td>0.01% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>-0.01% (5.75)</td>\n", "      <td>-0.00% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>-0.00% (5.75)</td>\n", "      <td>-0.00% (5.75)</td>\n", "      <td>-0.01% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>0.01% (5.75)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Chat Send</th>\n", "      <th>0.7928</th>\n", "      <th>140.7</th>\n", "      <td>0.08% (140.9)</td>\n", "      <td>-0.03% (140.7)</td>\n", "      <td>-0.06% (140.7)</td>\n", "      <td>-0.07% (140.6)</td>\n", "      <td>-0.02% (140.7)</td>\n", "      <td>0.03% (140.8)</td>\n", "      <td>-0.04% (140.7)</td>\n", "      <td>0.03% (140.8)</td>\n", "      <td>0.07% (140.8)</td>\n", "      <td>0.01% (140.8)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Chat View</th>\n", "      <th>0.5252</th>\n", "      <th>193.7</th>\n", "      <td>0.10% (193.9)</td>\n", "      <td>-0.08% (193.5)</td>\n", "      <td>-0.09% (193.5)</td>\n", "      <td>-0.14% (193.4)</td>\n", "      <td>-0.03% (193.6)</td>\n", "      <td>0.03% (193.7)</td>\n", "      <td>-0.03% (193.6)</td>\n", "      <td>0.08% (193.8)</td>\n", "      <td>0.07% (193.8)</td>\n", "      <td>0.11% (193.9)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap Create</th>\n", "      <th>0.3551</th>\n", "      <th>66.38</th>\n", "      <td>0.12% (66.46)</td>\n", "      <td>0.04% (66.41)</td>\n", "      <td>0.10% (66.45)</td>\n", "      <td>-0.03% (66.36)</td>\n", "      <td>-0.10% (66.32)</td>\n", "      <td>0.03% (66.40)</td>\n", "      <td>-0.05% (66.35)</td>\n", "      <td>0.03% (66.40)</td>\n", "      <td>-0.07% (66.33)</td>\n", "      <td>-0.04% (66.35)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap Send</th>\n", "      <th>0.3892</th>\n", "      <th>40.84</th>\n", "      <td>0.19% (40.92)</td>\n", "      <td>0.00% (40.84)</td>\n", "      <td>0.11% (40.88)</td>\n", "      <td>-0.00% (40.84)</td>\n", "      <td>-0.15% (40.78)</td>\n", "      <td>0.04% (40.85)</td>\n", "      <td>-0.06% (40.81)</td>\n", "      <td>0.04% (40.85)</td>\n", "      <td>-0.04% (40.82)</td>\n", "      <td>-0.11% (40.80)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap View</th>\n", "      <th>0.3243</th>\n", "      <th>162.3</th>\n", "      <td>0.03% (162.3)</td>\n", "      <td>0.00% (162.3)</td>\n", "      <td>0.02% (162.3)</td>\n", "      <td>-0.02% (162.2)</td>\n", "      <td>-0.05% (162.2)</td>\n", "      <td>0.03% (162.3)</td>\n", "      <td>0.05% (162.4)</td>\n", "      <td>0.06% (162.4)</td>\n", "      <td>-0.14% (162.0)</td>\n", "      <td>0.02% (162.3)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap Save</th>\n", "      <th>0.7558</th>\n", "      <th>11.40</th>\n", "      <td>0.01% (11.40)</td>\n", "      <td>0.04% (11.40)</td>\n", "      <td>0.05% (11.41)</td>\n", "      <td>-0.06% (11.39)</td>\n", "      <td>0.00% (11.40)</td>\n", "      <td>-0.04% (11.40)</td>\n", "      <td>-0.01% (11.40)</td>\n", "      <td>0.02% (11.40)</td>\n", "      <td>-0.03% (11.40)</td>\n", "      <td>0.02% (11.40)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DF Non-Friend Story View</th>\n", "      <th>0.2803</th>\n", "      <th>27.48</th>\n", "      <td>-0.15% (27.44)</td>\n", "      <td>0.02% (27.49)</td>\n", "      <td>-0.04% (27.47)</td>\n", "      <td>-0.14% (27.44)</td>\n", "      <td>0.01% (27.48)</td>\n", "      <td>-0.02% (27.48)</td>\n", "      <td>0.15% (27.52)</td>\n", "      <td>0.04% (27.49)</td>\n", "      <td>0.10% (27.51)</td>\n", "      <td>0.00% (27.48)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DF Non-Friend Story View Time</th>\n", "      <th>0.2421</th>\n", "      <th>795.0</th>\n", "      <td>-0.17% (793.7)</td>\n", "      <td>0.13% (796.1)</td>\n", "      <td>-0.01% (795.0)</td>\n", "      <td>-0.12% (794.0)</td>\n", "      <td>-0.03% (794.7)</td>\n", "      <td>-0.06% (794.5)</td>\n", "      <td>0.12% (796.0)</td>\n", "      <td>-0.03% (794.8)</td>\n", "      <td>0.14% (796.1)</td>\n", "      <td>0.01% (795.1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Friend Story View</th>\n", "      <th>0.2304</th>\n", "      <th>141.5</th>\n", "      <td>-0.08% (141.3)</td>\n", "      <td>0.13% (141.6)</td>\n", "      <td>-0.01% (141.5)</td>\n", "      <td>-0.14% (141.3)</td>\n", "      <td>-0.06% (141.4)</td>\n", "      <td>0.03% (141.5)</td>\n", "      <td>0.01% (141.5)</td>\n", "      <td>0.08% (141.6)</td>\n", "      <td>-0.04% (141.4)</td>\n", "      <td>0.07% (141.6)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Friend Story View Time</th>\n", "      <th>0.1339</th>\n", "      <th>373.2</th>\n", "      <td>-0.07% (372.9)</td>\n", "      <td>0.10% (373.6)</td>\n", "      <td>-0.04% (373.0)</td>\n", "      <td>-0.13% (372.7)</td>\n", "      <td>-0.09% (372.9)</td>\n", "      <td>0.04% (373.3)</td>\n", "      <td>0.08% (373.5)</td>\n", "      <td>0.07% (373.5)</td>\n", "      <td>-0.03% (373.1)</td>\n", "      <td>0.06% (373.4)</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON>s Send</th>\n", "      <th>0.8359</th>\n", "      <th>7.08</th>\n", "      <td>0.10% (7.09)</td>\n", "      <td>-0.05% (7.08)</td>\n", "      <td>0.14% (7.09)</td>\n", "      <td>0.12% (7.09)</td>\n", "      <td>-0.09% (7.08)</td>\n", "      <td>-0.03% (7.08)</td>\n", "      <td>-0.05% (7.08)</td>\n", "      <td>-0.07% (7.08)</td>\n", "      <td>-0.01% (7.08)</td>\n", "      <td>-0.05% (7.08)</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON> Swipe</th>\n", "      <th>0.2384</th>\n", "      <th>132.5</th>\n", "      <td>0.05% (132.6)</td>\n", "      <td>0.01% (132.5)</td>\n", "      <td>0.01% (132.5)</td>\n", "      <td>-0.07% (132.4)</td>\n", "      <td>-0.04% (132.4)</td>\n", "      <td>0.01% (132.5)</td>\n", "      <td>0.05% (132.6)</td>\n", "      <td>-0.04% (132.4)</td>\n", "      <td>-0.03% (132.5)</td>\n", "      <td>0.05% (132.6)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Spotlight Story View</th>\n", "      <th>0.3950</th>\n", "      <th>88.35</th>\n", "      <td>0.07% (88.41)</td>\n", "      <td>0.07% (88.41)</td>\n", "      <td>0.11% (88.44)</td>\n", "      <td>-0.04% (88.31)</td>\n", "      <td>-0.10% (88.26)</td>\n", "      <td>-0.01% (88.34)</td>\n", "      <td>-0.01% (88.34)</td>\n", "      <td>-0.20% (88.17)</td>\n", "      <td>0.07% (88.41)</td>\n", "      <td>0.06% (88.40)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Spotlight Story View Time</th>\n", "      <th>0.4907</th>\n", "      <th>570.6</th>\n", "      <td>0.12% (571.3)</td>\n", "      <td>-0.07% (570.2)</td>\n", "      <td>0.04% (570.8)</td>\n", "      <td>-0.03% (570.4)</td>\n", "      <td>-0.13% (569.9)</td>\n", "      <td>-0.04% (570.4)</td>\n", "      <td>-0.02% (570.5)</td>\n", "      <td>-0.16% (569.7)</td>\n", "      <td>0.15% (571.5)</td>\n", "      <td>0.14% (571.4)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Story Snap Post</th>\n", "      <th>0.5560</th>\n", "      <th>2.45</th>\n", "      <td>-0.25% (2.45)</td>\n", "      <td>-0.02% (2.45)</td>\n", "      <td>-0.30% (2.44)</td>\n", "      <td>0.01% (2.45)</td>\n", "      <td>0.03% (2.45)</td>\n", "      <td>-0.11% (2.45)</td>\n", "      <td>-0.03% (2.45)</td>\n", "      <td>0.52% (2.46)</td>\n", "      <td>0.18% (2.46)</td>\n", "      <td>-0.04% (2.45)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Story Snap View</th>\n", "      <th>0.1215</th>\n", "      <th>508.2</th>\n", "      <td>-0.12% (507.6)</td>\n", "      <td>0.11% (508.8)</td>\n", "      <td>-0.05% (508.0)</td>\n", "      <td>-0.13% (507.6)</td>\n", "      <td>-0.05% (508.0)</td>\n", "      <td>-0.02% (508.1)</td>\n", "      <td>0.07% (508.6)</td>\n", "      <td>0.02% (508.3)</td>\n", "      <td>0.11% (508.8)</td>\n", "      <td>0.04% (508.4)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["stat                                                     percent_difference  \\\n", "seed                                                   layer1_20250604_0016   \n", "assignment                                                     GID_01 (10%)   \n", "metric                        p_mean_anova overall_avg                        \n", "App Active Days               0.4168       5.75                0.01% (5.75)   \n", "Chat Send                     0.7928       140.7              0.08% (140.9)   \n", "Chat View                     0.5252       193.7              0.10% (193.9)   \n", "Direct Snap Create            0.3551       66.38              0.12% (66.46)   \n", "Direct Snap Send              0.3892       40.84              0.19% (40.92)   \n", "Direct Snap View              0.3243       162.3              0.03% (162.3)   \n", "Direct Snap Save              0.7558       11.40              0.01% (11.40)   \n", "DF Non-Friend Story View      0.2803       27.48             -0.15% (27.44)   \n", "DF Non-Friend Story View Time 0.2421       795.0             -0.17% (793.7)   \n", "Friend Story View             0.2304       141.5             -0.08% (141.3)   \n", "Friend Story View Time        0.1339       373.2             -0.07% (372.9)   \n", "Lens Send                     0.8359       7.08                0.10% (7.09)   \n", "Lens Swipe                    0.2384       132.5              0.05% (132.6)   \n", "Spotlight Story View          0.3950       88.35              0.07% (88.41)   \n", "Spotlight Story View Time     0.4907       570.6              0.12% (571.3)   \n", "Story Snap Post               0.5560       2.45               -0.25% (2.45)   \n", "Story Snap View               0.1215       508.2             -0.12% (507.6)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_02 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.4168       5.75           0.00% (5.75)   \n", "Chat Send                     0.7928       140.7        -0.03% (140.7)   \n", "Chat View                     0.5252       193.7        -0.08% (193.5)   \n", "Direct Snap Create            0.3551       66.38         0.04% (66.41)   \n", "Direct Snap Send              0.3892       40.84         0.00% (40.84)   \n", "Direct Snap View              0.3243       162.3         0.00% (162.3)   \n", "Direct Snap Save              0.7558       11.40         0.04% (11.40)   \n", "DF Non-Friend Story View      0.2803       27.48         0.02% (27.49)   \n", "DF Non-Friend Story View Time 0.2421       795.0         0.13% (796.1)   \n", "Friend Story View             0.2304       141.5         0.13% (141.6)   \n", "Friend Story View Time        0.1339       373.2         0.10% (373.6)   \n", "Lens Send                     0.8359       7.08          -0.05% (7.08)   \n", "Lens Swipe                    0.2384       132.5         0.01% (132.5)   \n", "Spotlight Story View          0.3950       88.35         0.07% (88.41)   \n", "Spotlight Story View Time     0.4907       570.6        -0.07% (570.2)   \n", "Story Snap Post               0.5560       2.45          -0.02% (2.45)   \n", "Story Snap View               0.1215       508.2         0.11% (508.8)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_03 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.4168       5.75          -0.01% (5.75)   \n", "Chat Send                     0.7928       140.7        -0.06% (140.7)   \n", "Chat View                     0.5252       193.7        -0.09% (193.5)   \n", "Direct Snap Create            0.3551       66.38         0.10% (66.45)   \n", "Direct Snap Send              0.3892       40.84         0.11% (40.88)   \n", "Direct Snap View              0.3243       162.3         0.02% (162.3)   \n", "Direct Snap Save              0.7558       11.40         0.05% (11.41)   \n", "DF Non-Friend Story View      0.2803       27.48        -0.04% (27.47)   \n", "DF Non-Friend Story View Time 0.2421       795.0        -0.01% (795.0)   \n", "Friend Story View             0.2304       141.5        -0.01% (141.5)   \n", "Friend Story View Time        0.1339       373.2        -0.04% (373.0)   \n", "Lens Send                     0.8359       7.08           0.14% (7.09)   \n", "Lens Swipe                    0.2384       132.5         0.01% (132.5)   \n", "Spotlight Story View          0.3950       88.35         0.11% (88.44)   \n", "Spotlight Story View Time     0.4907       570.6         0.04% (570.8)   \n", "Story Snap Post               0.5560       2.45          -0.30% (2.44)   \n", "Story Snap View               0.1215       508.2        -0.05% (508.0)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_04 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.4168       5.75          -0.00% (5.75)   \n", "Chat Send                     0.7928       140.7        -0.07% (140.6)   \n", "Chat View                     0.5252       193.7        -0.14% (193.4)   \n", "Direct Snap Create            0.3551       66.38        -0.03% (66.36)   \n", "Direct Snap Send              0.3892       40.84        -0.00% (40.84)   \n", "Direct Snap View              0.3243       162.3        -0.02% (162.2)   \n", "Direct Snap Save              0.7558       11.40        -0.06% (11.39)   \n", "DF Non-Friend Story View      0.2803       27.48        -0.14% (27.44)   \n", "DF Non-Friend Story View Time 0.2421       795.0        -0.12% (794.0)   \n", "Friend Story View             0.2304       141.5        -0.14% (141.3)   \n", "Friend Story View Time        0.1339       373.2        -0.13% (372.7)   \n", "Lens Send                     0.8359       7.08           0.12% (7.09)   \n", "Lens Swipe                    0.2384       132.5        -0.07% (132.4)   \n", "Spotlight Story View          0.3950       88.35        -0.04% (88.31)   \n", "Spotlight Story View Time     0.4907       570.6        -0.03% (570.4)   \n", "Story Snap Post               0.5560       2.45           0.01% (2.45)   \n", "Story Snap View               0.1215       508.2        -0.13% (507.6)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_05 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.4168       5.75           0.00% (5.75)   \n", "Chat Send                     0.7928       140.7        -0.02% (140.7)   \n", "Chat View                     0.5252       193.7        -0.03% (193.6)   \n", "Direct Snap Create            0.3551       66.38        -0.10% (66.32)   \n", "Direct Snap Send              0.3892       40.84        -0.15% (40.78)   \n", "Direct Snap View              0.3243       162.3        -0.05% (162.2)   \n", "Direct Snap Save              0.7558       11.40         0.00% (11.40)   \n", "DF Non-Friend Story View      0.2803       27.48         0.01% (27.48)   \n", "DF Non-Friend Story View Time 0.2421       795.0        -0.03% (794.7)   \n", "Friend Story View             0.2304       141.5        -0.06% (141.4)   \n", "Friend Story View Time        0.1339       373.2        -0.09% (372.9)   \n", "Lens Send                     0.8359       7.08          -0.09% (7.08)   \n", "Lens Swipe                    0.2384       132.5        -0.04% (132.4)   \n", "Spotlight Story View          0.3950       88.35        -0.10% (88.26)   \n", "Spotlight Story View Time     0.4907       570.6        -0.13% (569.9)   \n", "Story Snap Post               0.5560       2.45           0.03% (2.45)   \n", "Story Snap View               0.1215       508.2        -0.05% (508.0)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_06 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.4168       5.75          -0.00% (5.75)   \n", "Chat Send                     0.7928       140.7         0.03% (140.8)   \n", "Chat View                     0.5252       193.7         0.03% (193.7)   \n", "Direct Snap Create            0.3551       66.38         0.03% (66.40)   \n", "Direct Snap Send              0.3892       40.84         0.04% (40.85)   \n", "Direct Snap View              0.3243       162.3         0.03% (162.3)   \n", "Direct Snap Save              0.7558       11.40        -0.04% (11.40)   \n", "DF Non-Friend Story View      0.2803       27.48        -0.02% (27.48)   \n", "DF Non-Friend Story View Time 0.2421       795.0        -0.06% (794.5)   \n", "Friend Story View             0.2304       141.5         0.03% (141.5)   \n", "Friend Story View Time        0.1339       373.2         0.04% (373.3)   \n", "Lens Send                     0.8359       7.08          -0.03% (7.08)   \n", "Lens Swipe                    0.2384       132.5         0.01% (132.5)   \n", "Spotlight Story View          0.3950       88.35        -0.01% (88.34)   \n", "Spotlight Story View Time     0.4907       570.6        -0.04% (570.4)   \n", "Story Snap Post               0.5560       2.45          -0.11% (2.45)   \n", "Story Snap View               0.1215       508.2        -0.02% (508.1)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_07 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.4168       5.75          -0.00% (5.75)   \n", "Chat Send                     0.7928       140.7        -0.04% (140.7)   \n", "Chat View                     0.5252       193.7        -0.03% (193.6)   \n", "Direct Snap Create            0.3551       66.38        -0.05% (66.35)   \n", "Direct Snap Send              0.3892       40.84        -0.06% (40.81)   \n", "Direct Snap View              0.3243       162.3         0.05% (162.4)   \n", "Direct Snap Save              0.7558       11.40        -0.01% (11.40)   \n", "DF Non-Friend Story View      0.2803       27.48         0.15% (27.52)   \n", "DF Non-Friend Story View Time 0.2421       795.0         0.12% (796.0)   \n", "Friend Story View             0.2304       141.5         0.01% (141.5)   \n", "Friend Story View Time        0.1339       373.2         0.08% (373.5)   \n", "Lens Send                     0.8359       7.08          -0.05% (7.08)   \n", "Lens Swipe                    0.2384       132.5         0.05% (132.6)   \n", "Spotlight Story View          0.3950       88.35        -0.01% (88.34)   \n", "Spotlight Story View Time     0.4907       570.6        -0.02% (570.5)   \n", "Story Snap Post               0.5560       2.45          -0.03% (2.45)   \n", "Story Snap View               0.1215       508.2         0.07% (508.6)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_08 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.4168       5.75          -0.01% (5.75)   \n", "Chat Send                     0.7928       140.7         0.03% (140.8)   \n", "Chat View                     0.5252       193.7         0.08% (193.8)   \n", "Direct Snap Create            0.3551       66.38         0.03% (66.40)   \n", "Direct Snap Send              0.3892       40.84         0.04% (40.85)   \n", "Direct Snap View              0.3243       162.3         0.06% (162.4)   \n", "Direct Snap Save              0.7558       11.40         0.02% (11.40)   \n", "DF Non-Friend Story View      0.2803       27.48         0.04% (27.49)   \n", "DF Non-Friend Story View Time 0.2421       795.0        -0.03% (794.8)   \n", "Friend Story View             0.2304       141.5         0.08% (141.6)   \n", "Friend Story View Time        0.1339       373.2         0.07% (373.5)   \n", "Lens Send                     0.8359       7.08          -0.07% (7.08)   \n", "Lens Swipe                    0.2384       132.5        -0.04% (132.4)   \n", "Spotlight Story View          0.3950       88.35        -0.20% (88.17)   \n", "Spotlight Story View Time     0.4907       570.6        -0.16% (569.7)   \n", "Story Snap Post               0.5560       2.45           0.52% (2.46)   \n", "Story Snap View               0.1215       508.2         0.02% (508.3)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_09 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.4168       5.75           0.00% (5.75)   \n", "Chat Send                     0.7928       140.7         0.07% (140.8)   \n", "Chat View                     0.5252       193.7         0.07% (193.8)   \n", "Direct Snap Create            0.3551       66.38        -0.07% (66.33)   \n", "Direct Snap Send              0.3892       40.84        -0.04% (40.82)   \n", "Direct Snap View              0.3243       162.3        -0.14% (162.0)   \n", "Direct Snap Save              0.7558       11.40        -0.03% (11.40)   \n", "DF Non-Friend Story View      0.2803       27.48         0.10% (27.51)   \n", "DF Non-Friend Story View Time 0.2421       795.0         0.14% (796.1)   \n", "Friend Story View             0.2304       141.5        -0.04% (141.4)   \n", "Friend Story View Time        0.1339       373.2        -0.03% (373.1)   \n", "Lens Send                     0.8359       7.08          -0.01% (7.08)   \n", "Lens Swipe                    0.2384       132.5        -0.03% (132.5)   \n", "Spotlight Story View          0.3950       88.35         0.07% (88.41)   \n", "Spotlight Story View Time     0.4907       570.6         0.15% (571.5)   \n", "Story Snap Post               0.5560       2.45           0.18% (2.46)   \n", "Story Snap View               0.1215       508.2         0.11% (508.8)   \n", "\n", "stat                                                                    \n", "seed                                                                    \n", "assignment                                                GID_10 (10%)  \n", "metric                        p_mean_anova overall_avg                  \n", "App Active Days               0.4168       5.75           0.01% (5.75)  \n", "Chat Send                     0.7928       140.7         0.01% (140.8)  \n", "Chat View                     0.5252       193.7         0.11% (193.9)  \n", "Direct Snap Create            0.3551       66.38        -0.04% (66.35)  \n", "Direct Snap Send              0.3892       40.84        -0.11% (40.80)  \n", "Direct Snap View              0.3243       162.3         0.02% (162.3)  \n", "Direct Snap Save              0.7558       11.40         0.02% (11.40)  \n", "DF Non-Friend Story View      0.2803       27.48         0.00% (27.48)  \n", "DF Non-Friend Story View Time 0.2421       795.0         0.01% (795.1)  \n", "Friend Story View             0.2304       141.5         0.07% (141.6)  \n", "Friend Story View Time        0.1339       373.2         0.06% (373.4)  \n", "Lens Send                     0.8359       7.08          -0.05% (7.08)  \n", "Lens Swipe                    0.2384       132.5         0.05% (132.6)  \n", "Spotlight Story View          0.3950       88.35         0.06% (88.40)  \n", "Spotlight Story View Time     0.4907       570.6         0.14% (571.4)  \n", "Story Snap Post               0.5560       2.45          -0.04% (2.45)  \n", "Story Snap View               0.1215       508.2         0.04% (508.4)  "]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3>The seventh best seed is <strong style='color:blue;'>layer1_20250604_0024</strong></h3>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p>The table below displays the percent difference between the group's average and the overall average per user value for the metric.</p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>stat</th>\n", "      <th colspan=\"10\" halign=\"left\">percent_difference</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>seed</th>\n", "      <th colspan=\"10\" halign=\"left\">layer1_20250604_0024</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>assignment</th>\n", "      <th>GID_01 (10%)</th>\n", "      <th>GID_02 (10%)</th>\n", "      <th>GID_03 (10%)</th>\n", "      <th>GID_04 (10%)</th>\n", "      <th>GID_05 (10%)</th>\n", "      <th>GID_06 (10%)</th>\n", "      <th>GID_07 (10%)</th>\n", "      <th>GID_08 (10%)</th>\n", "      <th>GID_09 (10%)</th>\n", "      <th>GID_10 (10%)</th>\n", "    </tr>\n", "    <tr>\n", "      <th>metric</th>\n", "      <th>p_mean_anova</th>\n", "      <th>overall_avg</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>App Active Days</th>\n", "      <th>0.6603</th>\n", "      <th>5.75</th>\n", "      <td>-0.00% (5.75)</td>\n", "      <td>-0.00% (5.75)</td>\n", "      <td>-0.00% (5.75)</td>\n", "      <td>-0.00% (5.75)</td>\n", "      <td>0.01% (5.75)</td>\n", "      <td>-0.00% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>-0.00% (5.75)</td>\n", "      <td>-0.00% (5.75)</td>\n", "      <td>0.01% (5.75)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Chat Send</th>\n", "      <th>0.6304</th>\n", "      <th>140.7</th>\n", "      <td>0.03% (140.7)</td>\n", "      <td>-0.08% (140.6)</td>\n", "      <td>0.07% (140.8)</td>\n", "      <td>-0.05% (140.6)</td>\n", "      <td>0.06% (140.8)</td>\n", "      <td>0.04% (140.7)</td>\n", "      <td>-0.09% (140.6)</td>\n", "      <td>-0.01% (140.7)</td>\n", "      <td>0.05% (140.8)</td>\n", "      <td>-0.03% (140.7)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Chat View</th>\n", "      <th>0.3774</th>\n", "      <th>193.6</th>\n", "      <td>0.04% (193.6)</td>\n", "      <td>-0.13% (193.3)</td>\n", "      <td>0.16% (193.9)</td>\n", "      <td>-0.04% (193.5)</td>\n", "      <td>-0.03% (193.5)</td>\n", "      <td>0.00% (193.6)</td>\n", "      <td>-0.06% (193.4)</td>\n", "      <td>0.00% (193.6)</td>\n", "      <td>0.15% (193.9)</td>\n", "      <td>-0.10% (193.4)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap Create</th>\n", "      <th>0.7022</th>\n", "      <th>66.37</th>\n", "      <td>-0.01% (66.36)</td>\n", "      <td>-0.03% (66.35)</td>\n", "      <td>0.02% (66.38)</td>\n", "      <td>-0.01% (66.36)</td>\n", "      <td>0.03% (66.39)</td>\n", "      <td>0.07% (66.42)</td>\n", "      <td>-0.11% (66.29)</td>\n", "      <td>0.08% (66.42)</td>\n", "      <td>-0.05% (66.34)</td>\n", "      <td>0.00% (66.37)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap Send</th>\n", "      <th>0.8307</th>\n", "      <th>40.82</th>\n", "      <td>-0.03% (40.81)</td>\n", "      <td>-0.08% (40.79)</td>\n", "      <td>0.02% (40.83)</td>\n", "      <td>0.03% (40.84)</td>\n", "      <td>0.01% (40.83)</td>\n", "      <td>0.08% (40.86)</td>\n", "      <td>-0.12% (40.78)</td>\n", "      <td>0.11% (40.87)</td>\n", "      <td>-0.06% (40.80)</td>\n", "      <td>0.05% (40.85)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap View</th>\n", "      <th>0.2661</th>\n", "      <th>162.3</th>\n", "      <td>0.03% (162.3)</td>\n", "      <td>-0.07% (162.2)</td>\n", "      <td>-0.05% (162.2)</td>\n", "      <td>0.02% (162.3)</td>\n", "      <td>0.06% (162.4)</td>\n", "      <td>0.09% (162.4)</td>\n", "      <td>-0.02% (162.2)</td>\n", "      <td>0.01% (162.3)</td>\n", "      <td>-0.11% (162.1)</td>\n", "      <td>0.04% (162.3)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap Save</th>\n", "      <th>0.1191</th>\n", "      <th>11.40</th>\n", "      <td>0.03% (11.40)</td>\n", "      <td>-0.10% (11.39)</td>\n", "      <td>0.08% (11.41)</td>\n", "      <td>-0.02% (11.40)</td>\n", "      <td>0.03% (11.40)</td>\n", "      <td>0.03% (11.40)</td>\n", "      <td>-0.01% (11.40)</td>\n", "      <td>0.05% (11.41)</td>\n", "      <td>-0.06% (11.39)</td>\n", "      <td>-0.02% (11.40)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DF Non-Friend Story View</th>\n", "      <th>0.3451</th>\n", "      <th>27.48</th>\n", "      <td>-0.01% (27.48)</td>\n", "      <td>0.02% (27.49)</td>\n", "      <td>0.00% (27.48)</td>\n", "      <td>0.04% (27.49)</td>\n", "      <td>0.10% (27.51)</td>\n", "      <td>0.02% (27.49)</td>\n", "      <td>-0.17% (27.43)</td>\n", "      <td>0.02% (27.49)</td>\n", "      <td>-0.12% (27.45)</td>\n", "      <td>0.10% (27.51)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DF Non-Friend Story View Time</th>\n", "      <th>0.6863</th>\n", "      <th>794.7</th>\n", "      <td>0.09% (795.4)</td>\n", "      <td>0.09% (795.5)</td>\n", "      <td>-0.05% (794.4)</td>\n", "      <td>-0.02% (794.6)</td>\n", "      <td>0.11% (795.6)</td>\n", "      <td>-0.03% (794.5)</td>\n", "      <td>-0.07% (794.2)</td>\n", "      <td>0.01% (794.8)</td>\n", "      <td>-0.14% (793.6)</td>\n", "      <td>0.01% (794.8)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Friend Story View</th>\n", "      <th>0.5430</th>\n", "      <th>141.4</th>\n", "      <td>-0.02% (141.4)</td>\n", "      <td>-0.08% (141.3)</td>\n", "      <td>-0.06% (141.4)</td>\n", "      <td>-0.04% (141.4)</td>\n", "      <td>0.12% (141.6)</td>\n", "      <td>0.06% (141.5)</td>\n", "      <td>-0.06% (141.4)</td>\n", "      <td>-0.02% (141.4)</td>\n", "      <td>0.01% (141.4)</td>\n", "      <td>0.08% (141.6)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Friend Story View Time</th>\n", "      <th>0.5160</th>\n", "      <th>373.1</th>\n", "      <td>-0.04% (372.9)</td>\n", "      <td>-0.05% (372.9)</td>\n", "      <td>-0.06% (372.8)</td>\n", "      <td>-0.05% (372.9)</td>\n", "      <td>0.12% (373.5)</td>\n", "      <td>0.07% (373.3)</td>\n", "      <td>-0.02% (373.0)</td>\n", "      <td>-0.03% (372.9)</td>\n", "      <td>0.03% (373.2)</td>\n", "      <td>0.03% (373.2)</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON>s Send</th>\n", "      <th>0.3898</th>\n", "      <th>7.08</th>\n", "      <td>-0.02% (7.08)</td>\n", "      <td>-0.11% (7.07)</td>\n", "      <td>0.18% (7.09)</td>\n", "      <td>-0.04% (7.08)</td>\n", "      <td>0.06% (7.08)</td>\n", "      <td>0.08% (7.09)</td>\n", "      <td>-0.20% (7.07)</td>\n", "      <td>-0.10% (7.07)</td>\n", "      <td>0.04% (7.08)</td>\n", "      <td>0.11% (7.09)</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON> Swipe</th>\n", "      <th>0.2723</th>\n", "      <th>132.5</th>\n", "      <td>-0.09% (132.4)</td>\n", "      <td>-0.05% (132.4)</td>\n", "      <td>0.06% (132.6)</td>\n", "      <td>-0.03% (132.5)</td>\n", "      <td>0.04% (132.6)</td>\n", "      <td>0.00% (132.5)</td>\n", "      <td>0.01% (132.5)</td>\n", "      <td>0.03% (132.5)</td>\n", "      <td>-0.00% (132.5)</td>\n", "      <td>0.01% (132.5)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Spotlight Story View</th>\n", "      <th>0.6515</th>\n", "      <th>88.34</th>\n", "      <td>0.06% (88.39)</td>\n", "      <td>0.14% (88.46)</td>\n", "      <td>-0.06% (88.29)</td>\n", "      <td>0.09% (88.42)</td>\n", "      <td>-0.02% (88.32)</td>\n", "      <td>0.04% (88.38)</td>\n", "      <td>-0.01% (88.33)</td>\n", "      <td>-0.02% (88.33)</td>\n", "      <td>-0.10% (88.25)</td>\n", "      <td>-0.11% (88.25)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Spotlight Story View Time</th>\n", "      <th>0.3441</th>\n", "      <th>570.8</th>\n", "      <td>0.08% (571.3)</td>\n", "      <td>0.16% (571.7)</td>\n", "      <td>-0.16% (569.9)</td>\n", "      <td>0.07% (571.2)</td>\n", "      <td>-0.01% (570.8)</td>\n", "      <td>0.14% (571.6)</td>\n", "      <td>0.05% (571.1)</td>\n", "      <td>-0.18% (569.8)</td>\n", "      <td>-0.10% (570.2)</td>\n", "      <td>-0.03% (570.6)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Story Snap Post</th>\n", "      <th>0.9637</th>\n", "      <th>2.45</th>\n", "      <td>-0.04% (2.45)</td>\n", "      <td>0.10% (2.45)</td>\n", "      <td>-0.10% (2.45)</td>\n", "      <td>-0.06% (2.45)</td>\n", "      <td>-0.10% (2.45)</td>\n", "      <td>-0.27% (2.44)</td>\n", "      <td>0.02% (2.45)</td>\n", "      <td>-0.01% (2.45)</td>\n", "      <td>0.09% (2.45)</td>\n", "      <td>0.37% (2.46)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Story Snap View</th>\n", "      <th>0.4882</th>\n", "      <th>508.2</th>\n", "      <td>0.03% (508.3)</td>\n", "      <td>0.01% (508.2)</td>\n", "      <td>-0.05% (507.9)</td>\n", "      <td>0.05% (508.4)</td>\n", "      <td>0.11% (508.7)</td>\n", "      <td>0.01% (508.2)</td>\n", "      <td>-0.10% (507.7)</td>\n", "      <td>-0.00% (508.2)</td>\n", "      <td>-0.10% (507.7)</td>\n", "      <td>0.05% (508.4)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["stat                                                     percent_difference  \\\n", "seed                                                   layer1_20250604_0024   \n", "assignment                                                     GID_01 (10%)   \n", "metric                        p_mean_anova overall_avg                        \n", "App Active Days               0.6603       5.75               -0.00% (5.75)   \n", "Chat Send                     0.6304       140.7              0.03% (140.7)   \n", "Chat View                     0.3774       193.6              0.04% (193.6)   \n", "Direct Snap Create            0.7022       66.37             -0.01% (66.36)   \n", "Direct Snap Send              0.8307       40.82             -0.03% (40.81)   \n", "Direct Snap View              0.2661       162.3              0.03% (162.3)   \n", "Direct Snap Save              0.1191       11.40              0.03% (11.40)   \n", "DF Non-Friend Story View      0.3451       27.48             -0.01% (27.48)   \n", "DF Non-Friend Story View Time 0.6863       794.7              0.09% (795.4)   \n", "Friend Story View             0.5430       141.4             -0.02% (141.4)   \n", "Friend Story View Time        0.5160       373.1             -0.04% (372.9)   \n", "Lens Send                     0.3898       7.08               -0.02% (7.08)   \n", "Lens Swipe                    0.2723       132.5             -0.09% (132.4)   \n", "Spotlight Story View          0.6515       88.34              0.06% (88.39)   \n", "Spotlight Story View Time     0.3441       570.8              0.08% (571.3)   \n", "Story Snap Post               0.9637       2.45               -0.04% (2.45)   \n", "Story Snap View               0.4882       508.2              0.03% (508.3)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_02 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.6603       5.75          -0.00% (5.75)   \n", "Chat Send                     0.6304       140.7        -0.08% (140.6)   \n", "Chat View                     0.3774       193.6        -0.13% (193.3)   \n", "Direct Snap Create            0.7022       66.37        -0.03% (66.35)   \n", "Direct Snap Send              0.8307       40.82        -0.08% (40.79)   \n", "Direct Snap View              0.2661       162.3        -0.07% (162.2)   \n", "Direct Snap Save              0.1191       11.40        -0.10% (11.39)   \n", "DF Non-Friend Story View      0.3451       27.48         0.02% (27.49)   \n", "DF Non-Friend Story View Time 0.6863       794.7         0.09% (795.5)   \n", "Friend Story View             0.5430       141.4        -0.08% (141.3)   \n", "Friend Story View Time        0.5160       373.1        -0.05% (372.9)   \n", "Lens Send                     0.3898       7.08          -0.11% (7.07)   \n", "Lens Swipe                    0.2723       132.5        -0.05% (132.4)   \n", "Spotlight Story View          0.6515       88.34         0.14% (88.46)   \n", "Spotlight Story View Time     0.3441       570.8         0.16% (571.7)   \n", "Story Snap Post               0.9637       2.45           0.10% (2.45)   \n", "Story Snap View               0.4882       508.2         0.01% (508.2)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_03 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.6603       5.75          -0.00% (5.75)   \n", "Chat Send                     0.6304       140.7         0.07% (140.8)   \n", "Chat View                     0.3774       193.6         0.16% (193.9)   \n", "Direct Snap Create            0.7022       66.37         0.02% (66.38)   \n", "Direct Snap Send              0.8307       40.82         0.02% (40.83)   \n", "Direct Snap View              0.2661       162.3        -0.05% (162.2)   \n", "Direct Snap Save              0.1191       11.40         0.08% (11.41)   \n", "DF Non-Friend Story View      0.3451       27.48         0.00% (27.48)   \n", "DF Non-Friend Story View Time 0.6863       794.7        -0.05% (794.4)   \n", "Friend Story View             0.5430       141.4        -0.06% (141.4)   \n", "Friend Story View Time        0.5160       373.1        -0.06% (372.8)   \n", "Lens Send                     0.3898       7.08           0.18% (7.09)   \n", "Lens Swipe                    0.2723       132.5         0.06% (132.6)   \n", "Spotlight Story View          0.6515       88.34        -0.06% (88.29)   \n", "Spotlight Story View Time     0.3441       570.8        -0.16% (569.9)   \n", "Story Snap Post               0.9637       2.45          -0.10% (2.45)   \n", "Story Snap View               0.4882       508.2        -0.05% (507.9)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_04 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.6603       5.75          -0.00% (5.75)   \n", "Chat Send                     0.6304       140.7        -0.05% (140.6)   \n", "Chat View                     0.3774       193.6        -0.04% (193.5)   \n", "Direct Snap Create            0.7022       66.37        -0.01% (66.36)   \n", "Direct Snap Send              0.8307       40.82         0.03% (40.84)   \n", "Direct Snap View              0.2661       162.3         0.02% (162.3)   \n", "Direct Snap Save              0.1191       11.40        -0.02% (11.40)   \n", "DF Non-Friend Story View      0.3451       27.48         0.04% (27.49)   \n", "DF Non-Friend Story View Time 0.6863       794.7        -0.02% (794.6)   \n", "Friend Story View             0.5430       141.4        -0.04% (141.4)   \n", "Friend Story View Time        0.5160       373.1        -0.05% (372.9)   \n", "Lens Send                     0.3898       7.08          -0.04% (7.08)   \n", "Lens Swipe                    0.2723       132.5        -0.03% (132.5)   \n", "Spotlight Story View          0.6515       88.34         0.09% (88.42)   \n", "Spotlight Story View Time     0.3441       570.8         0.07% (571.2)   \n", "Story Snap Post               0.9637       2.45          -0.06% (2.45)   \n", "Story Snap View               0.4882       508.2         0.05% (508.4)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_05 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.6603       5.75           0.01% (5.75)   \n", "Chat Send                     0.6304       140.7         0.06% (140.8)   \n", "Chat View                     0.3774       193.6        -0.03% (193.5)   \n", "Direct Snap Create            0.7022       66.37         0.03% (66.39)   \n", "Direct Snap Send              0.8307       40.82         0.01% (40.83)   \n", "Direct Snap View              0.2661       162.3         0.06% (162.4)   \n", "Direct Snap Save              0.1191       11.40         0.03% (11.40)   \n", "DF Non-Friend Story View      0.3451       27.48         0.10% (27.51)   \n", "DF Non-Friend Story View Time 0.6863       794.7         0.11% (795.6)   \n", "Friend Story View             0.5430       141.4         0.12% (141.6)   \n", "Friend Story View Time        0.5160       373.1         0.12% (373.5)   \n", "Lens Send                     0.3898       7.08           0.06% (7.08)   \n", "Lens Swipe                    0.2723       132.5         0.04% (132.6)   \n", "Spotlight Story View          0.6515       88.34        -0.02% (88.32)   \n", "Spotlight Story View Time     0.3441       570.8        -0.01% (570.8)   \n", "Story Snap Post               0.9637       2.45          -0.10% (2.45)   \n", "Story Snap View               0.4882       508.2         0.11% (508.7)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_06 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.6603       5.75          -0.00% (5.75)   \n", "Chat Send                     0.6304       140.7         0.04% (140.7)   \n", "Chat View                     0.3774       193.6         0.00% (193.6)   \n", "Direct Snap Create            0.7022       66.37         0.07% (66.42)   \n", "Direct Snap Send              0.8307       40.82         0.08% (40.86)   \n", "Direct Snap View              0.2661       162.3         0.09% (162.4)   \n", "Direct Snap Save              0.1191       11.40         0.03% (11.40)   \n", "DF Non-Friend Story View      0.3451       27.48         0.02% (27.49)   \n", "DF Non-Friend Story View Time 0.6863       794.7        -0.03% (794.5)   \n", "Friend Story View             0.5430       141.4         0.06% (141.5)   \n", "Friend Story View Time        0.5160       373.1         0.07% (373.3)   \n", "Lens Send                     0.3898       7.08           0.08% (7.09)   \n", "Lens Swipe                    0.2723       132.5         0.00% (132.5)   \n", "Spotlight Story View          0.6515       88.34         0.04% (88.38)   \n", "Spotlight Story View Time     0.3441       570.8         0.14% (571.6)   \n", "Story Snap Post               0.9637       2.45          -0.27% (2.44)   \n", "Story Snap View               0.4882       508.2         0.01% (508.2)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_07 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.6603       5.75           0.00% (5.75)   \n", "Chat Send                     0.6304       140.7        -0.09% (140.6)   \n", "Chat View                     0.3774       193.6        -0.06% (193.4)   \n", "Direct Snap Create            0.7022       66.37        -0.11% (66.29)   \n", "Direct Snap Send              0.8307       40.82        -0.12% (40.78)   \n", "Direct Snap View              0.2661       162.3        -0.02% (162.2)   \n", "Direct Snap Save              0.1191       11.40        -0.01% (11.40)   \n", "DF Non-Friend Story View      0.3451       27.48        -0.17% (27.43)   \n", "DF Non-Friend Story View Time 0.6863       794.7        -0.07% (794.2)   \n", "Friend Story View             0.5430       141.4        -0.06% (141.4)   \n", "Friend Story View Time        0.5160       373.1        -0.02% (373.0)   \n", "Lens Send                     0.3898       7.08          -0.20% (7.07)   \n", "Lens Swipe                    0.2723       132.5         0.01% (132.5)   \n", "Spotlight Story View          0.6515       88.34        -0.01% (88.33)   \n", "Spotlight Story View Time     0.3441       570.8         0.05% (571.1)   \n", "Story Snap Post               0.9637       2.45           0.02% (2.45)   \n", "Story Snap View               0.4882       508.2        -0.10% (507.7)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_08 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.6603       5.75          -0.00% (5.75)   \n", "Chat Send                     0.6304       140.7        -0.01% (140.7)   \n", "Chat View                     0.3774       193.6         0.00% (193.6)   \n", "Direct Snap Create            0.7022       66.37         0.08% (66.42)   \n", "Direct Snap Send              0.8307       40.82         0.11% (40.87)   \n", "Direct Snap View              0.2661       162.3         0.01% (162.3)   \n", "Direct Snap Save              0.1191       11.40         0.05% (11.41)   \n", "DF Non-Friend Story View      0.3451       27.48         0.02% (27.49)   \n", "DF Non-Friend Story View Time 0.6863       794.7         0.01% (794.8)   \n", "Friend Story View             0.5430       141.4        -0.02% (141.4)   \n", "Friend Story View Time        0.5160       373.1        -0.03% (372.9)   \n", "Lens Send                     0.3898       7.08          -0.10% (7.07)   \n", "Lens Swipe                    0.2723       132.5         0.03% (132.5)   \n", "Spotlight Story View          0.6515       88.34        -0.02% (88.33)   \n", "Spotlight Story View Time     0.3441       570.8        -0.18% (569.8)   \n", "Story Snap Post               0.9637       2.45          -0.01% (2.45)   \n", "Story Snap View               0.4882       508.2        -0.00% (508.2)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                GID_09 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.6603       5.75          -0.00% (5.75)   \n", "Chat Send                     0.6304       140.7         0.05% (140.8)   \n", "Chat View                     0.3774       193.6         0.15% (193.9)   \n", "Direct Snap Create            0.7022       66.37        -0.05% (66.34)   \n", "Direct Snap Send              0.8307       40.82        -0.06% (40.80)   \n", "Direct Snap View              0.2661       162.3        -0.11% (162.1)   \n", "Direct Snap Save              0.1191       11.40        -0.06% (11.39)   \n", "DF Non-Friend Story View      0.3451       27.48        -0.12% (27.45)   \n", "DF Non-Friend Story View Time 0.6863       794.7        -0.14% (793.6)   \n", "Friend Story View             0.5430       141.4         0.01% (141.4)   \n", "Friend Story View Time        0.5160       373.1         0.03% (373.2)   \n", "Lens Send                     0.3898       7.08           0.04% (7.08)   \n", "Lens Swipe                    0.2723       132.5        -0.00% (132.5)   \n", "Spotlight Story View          0.6515       88.34        -0.10% (88.25)   \n", "Spotlight Story View Time     0.3441       570.8        -0.10% (570.2)   \n", "Story Snap Post               0.9637       2.45           0.09% (2.45)   \n", "Story Snap View               0.4882       508.2        -0.10% (507.7)   \n", "\n", "stat                                                                    \n", "seed                                                                    \n", "assignment                                                GID_10 (10%)  \n", "metric                        p_mean_anova overall_avg                  \n", "App Active Days               0.6603       5.75           0.01% (5.75)  \n", "Chat Send                     0.6304       140.7        -0.03% (140.7)  \n", "Chat View                     0.3774       193.6        -0.10% (193.4)  \n", "Direct Snap Create            0.7022       66.37         0.00% (66.37)  \n", "Direct Snap Send              0.8307       40.82         0.05% (40.85)  \n", "Direct Snap View              0.2661       162.3         0.04% (162.3)  \n", "Direct Snap Save              0.1191       11.40        -0.02% (11.40)  \n", "DF Non-Friend Story View      0.3451       27.48         0.10% (27.51)  \n", "DF Non-Friend Story View Time 0.6863       794.7         0.01% (794.8)  \n", "Friend Story View             0.5430       141.4         0.08% (141.6)  \n", "Friend Story View Time        0.5160       373.1         0.03% (373.2)  \n", "Lens Send                     0.3898       7.08           0.11% (7.09)  \n", "Lens Swipe                    0.2723       132.5         0.01% (132.5)  \n", "Spotlight Story View          0.6515       88.34        -0.11% (88.25)  \n", "Spotlight Story View Time     0.3441       570.8        -0.03% (570.6)  \n", "Story Snap Post               0.9637       2.45           0.37% (2.46)  \n", "Story Snap View               0.4882       508.2         0.05% (508.4)  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["nth = {\n", "    1: \"best\",\n", "    2: \"second best\",\n", "    3: \"third best\",\n", "    4: \"fourth best\",\n", "    5: \"fifth best\",\n", "    6: \"sixth best\",\n", "    7: \"seventh best\"\n", "#     6: \"default (study name - <span style='color:red;'>not recommended</span>)\"\n", "}\n", "\n", "def format_float(number):\n", "    if abs(number) < 100:\n", "        return \"{:.2f}\".format(number)\n", "    else:\n", "        return \"{:.1f}\".format(number)\n", "\n", "def display_table(seed):\n", "    df_seed_info = (\n", "        df_final_results\n", "            .query(\"seed == '{}'\".format(seed))\n", "            .copy()\n", "    )\n", "    df_seed_info['percent_difference'] = df_seed_info.apply(\n", "        lambda x: \"{:.2f}% ({})\".format(x.pct_diff, format_float(x.avg)), axis=1\n", "    )\n", "    df_seed_info['p_mean_anova'] = df_seed_info['p_mean'].map(\"{:.4f}\".format)\n", "    df_seed_info['overall_avg'] = df_seed_info['overall_avg'].map(format_float)\n", "\n", "    pt = pd.pivot_table(\n", "        df_seed_info,\n", "        index=['metric', 'p_mean_anova', 'overall_avg'],\n", "        values=['percent_difference'],\n", "        columns=['seed', 'assignment'],\n", "        aggfunc=lambda x: x if len(x) <= 1 else None,\n", "        fill_value='',\n", "    )\n", "    display(pt)\n", "\n", "for index, seed in enumerate(best_seeds.seed.tolist(), 1):\n", "    display(HTML(\"<h3>The {} seed is <strong style='color:blue;'>{}</strong></h3>\".format(nth[index], seed)))\n", "    display(HTML(\"<p>The table below displays the percent difference between the group's average and the overall average per user value for the metric.</p>\"))\n", "    display_table(seed)\n"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/html": ["<h2>The default seed is <strong style='color:blue;'>layer1_$20250604</strong></h3>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p>These numbers are provided as the baseline for comparison.</p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>stat</th>\n", "      <th colspan=\"10\" halign=\"left\">percent_difference</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>seed</th>\n", "      <th colspan=\"10\" halign=\"left\">layer1_$20250604</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th></th>\n", "      <th>assignment</th>\n", "      <th>Exp 01 (10%)</th>\n", "      <th>Exp 02 (10%)</th>\n", "      <th>Exp 03 (10%)</th>\n", "      <th>Exp 04 (10%)</th>\n", "      <th>Exp 05 (10%)</th>\n", "      <th>Exp 06 (10%)</th>\n", "      <th>Exp 07 (10%)</th>\n", "      <th>Exp 08 (10%)</th>\n", "      <th>Exp 09 (10%)</th>\n", "      <th>Exp 10 (10%)</th>\n", "    </tr>\n", "    <tr>\n", "      <th>metric</th>\n", "      <th>p_mean_anova</th>\n", "      <th>overall_avg</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>App Active Days</th>\n", "      <th>0.1820</th>\n", "      <th>5.75</th>\n", "      <td>-0.01% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>-0.00% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>-0.01% (5.75)</td>\n", "      <td>-0.00% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>0.00% (5.75)</td>\n", "      <td>0.01% (5.75)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Chat Send</th>\n", "      <th>0.1850</th>\n", "      <th>140.7</th>\n", "      <td>0.17% (141.0)</td>\n", "      <td>0.09% (140.9)</td>\n", "      <td>-0.07% (140.6)</td>\n", "      <td>-0.01% (140.7)</td>\n", "      <td>0.01% (140.7)</td>\n", "      <td>0.04% (140.8)</td>\n", "      <td>-0.04% (140.7)</td>\n", "      <td>-0.01% (140.7)</td>\n", "      <td>-0.09% (140.6)</td>\n", "      <td>-0.08% (140.6)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Chat View</th>\n", "      <th>0.6805</th>\n", "      <th>193.6</th>\n", "      <td>0.14% (193.8)</td>\n", "      <td>0.08% (193.7)</td>\n", "      <td>-0.08% (193.4)</td>\n", "      <td>0.01% (193.6)</td>\n", "      <td>-0.03% (193.5)</td>\n", "      <td>-0.06% (193.5)</td>\n", "      <td>0.07% (193.7)</td>\n", "      <td>0.04% (193.7)</td>\n", "      <td>-0.08% (193.4)</td>\n", "      <td>-0.07% (193.4)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap Create</th>\n", "      <th>0.4377</th>\n", "      <th>66.37</th>\n", "      <td>0.12% (66.45)</td>\n", "      <td>0.03% (66.39)</td>\n", "      <td>0.03% (66.39)</td>\n", "      <td>-0.04% (66.34)</td>\n", "      <td>-0.02% (66.36)</td>\n", "      <td>-0.07% (66.32)</td>\n", "      <td>-0.02% (66.36)</td>\n", "      <td>0.10% (66.44)</td>\n", "      <td>-0.09% (66.31)</td>\n", "      <td>-0.03% (66.35)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap Send</th>\n", "      <th>0.4202</th>\n", "      <th>40.82</th>\n", "      <td>0.19% (40.90)</td>\n", "      <td>0.03% (40.84)</td>\n", "      <td>0.05% (40.85)</td>\n", "      <td>-0.04% (40.81)</td>\n", "      <td>-0.04% (40.81)</td>\n", "      <td>-0.01% (40.82)</td>\n", "      <td>-0.02% (40.81)</td>\n", "      <td>0.10% (40.86)</td>\n", "      <td>-0.16% (40.76)</td>\n", "      <td>-0.08% (40.79)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap View</th>\n", "      <th>0.8394</th>\n", "      <th>162.3</th>\n", "      <td>0.04% (162.4)</td>\n", "      <td>0.03% (162.3)</td>\n", "      <td>-0.02% (162.3)</td>\n", "      <td>0.01% (162.3)</td>\n", "      <td>-0.01% (162.3)</td>\n", "      <td>0.07% (162.4)</td>\n", "      <td>0.01% (162.3)</td>\n", "      <td>-0.00% (162.3)</td>\n", "      <td>-0.08% (162.2)</td>\n", "      <td>-0.03% (162.2)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Direct Snap Save</th>\n", "      <th>0.4030</th>\n", "      <th>11.40</th>\n", "      <td>-0.01% (11.40)</td>\n", "      <td>0.02% (11.40)</td>\n", "      <td>-0.05% (11.40)</td>\n", "      <td>0.04% (11.41)</td>\n", "      <td>0.05% (11.41)</td>\n", "      <td>-0.09% (11.39)</td>\n", "      <td>0.01% (11.40)</td>\n", "      <td>-0.01% (11.40)</td>\n", "      <td>-0.01% (11.40)</td>\n", "      <td>0.03% (11.40)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DF Non-Friend Story View</th>\n", "      <th>0.1692</th>\n", "      <th>27.48</th>\n", "      <td>-0.14% (27.44)</td>\n", "      <td>-0.06% (27.47)</td>\n", "      <td>0.14% (27.52)</td>\n", "      <td>-0.05% (27.47)</td>\n", "      <td>-0.01% (27.48)</td>\n", "      <td>0.12% (27.52)</td>\n", "      <td>-0.07% (27.47)</td>\n", "      <td>-0.00% (27.48)</td>\n", "      <td>-0.09% (27.46)</td>\n", "      <td>0.14% (27.52)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>DF Non-Friend Story View Time</th>\n", "      <th>0.6808</th>\n", "      <th>794.8</th>\n", "      <td>-0.06% (794.3)</td>\n", "      <td>-0.01% (794.7)</td>\n", "      <td>0.08% (795.4)</td>\n", "      <td>0.01% (794.8)</td>\n", "      <td>-0.05% (794.3)</td>\n", "      <td>0.10% (795.6)</td>\n", "      <td>-0.03% (794.5)</td>\n", "      <td>0.11% (795.7)</td>\n", "      <td>-0.14% (793.6)</td>\n", "      <td>-0.01% (794.7)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Friend Story View</th>\n", "      <th>0.8112</th>\n", "      <th>141.5</th>\n", "      <td>-0.04% (141.4)</td>\n", "      <td>0.08% (141.6)</td>\n", "      <td>0.00% (141.5)</td>\n", "      <td>0.01% (141.5)</td>\n", "      <td>0.03% (141.5)</td>\n", "      <td>0.04% (141.5)</td>\n", "      <td>-0.06% (141.4)</td>\n", "      <td>-0.07% (141.4)</td>\n", "      <td>-0.06% (141.4)</td>\n", "      <td>0.06% (141.6)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Friend Story View Time</th>\n", "      <th>0.7259</th>\n", "      <th>373.2</th>\n", "      <td>-0.02% (373.1)</td>\n", "      <td>0.09% (373.6)</td>\n", "      <td>-0.04% (373.1)</td>\n", "      <td>0.01% (373.3)</td>\n", "      <td>0.03% (373.3)</td>\n", "      <td>-0.02% (373.1)</td>\n", "      <td>-0.07% (372.9)</td>\n", "      <td>0.03% (373.4)</td>\n", "      <td>-0.08% (372.9)</td>\n", "      <td>0.07% (373.5)</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON>s Send</th>\n", "      <th>0.8602</th>\n", "      <th>7.08</th>\n", "      <td>0.11% (7.09)</td>\n", "      <td>0.01% (7.08)</td>\n", "      <td>-0.01% (7.08)</td>\n", "      <td>-0.02% (7.08)</td>\n", "      <td>0.14% (7.09)</td>\n", "      <td>0.01% (7.08)</td>\n", "      <td>0.00% (7.08)</td>\n", "      <td>-0.08% (7.08)</td>\n", "      <td>-0.15% (7.07)</td>\n", "      <td>-0.01% (7.08)</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON> Swipe</th>\n", "      <th>0.9765</th>\n", "      <th>132.5</th>\n", "      <td>-0.04% (132.5)</td>\n", "      <td>0.01% (132.5)</td>\n", "      <td>0.01% (132.5)</td>\n", "      <td>-0.01% (132.5)</td>\n", "      <td>0.04% (132.6)</td>\n", "      <td>-0.02% (132.5)</td>\n", "      <td>-0.01% (132.5)</td>\n", "      <td>-0.01% (132.5)</td>\n", "      <td>0.01% (132.5)</td>\n", "      <td>0.01% (132.5)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Spotlight Story View</th>\n", "      <th>0.8994</th>\n", "      <th>88.38</th>\n", "      <td>0.05% (88.43)</td>\n", "      <td>-0.05% (88.34)</td>\n", "      <td>-0.03% (88.36)</td>\n", "      <td>-0.12% (88.28)</td>\n", "      <td>0.07% (88.44)</td>\n", "      <td>0.02% (88.40)</td>\n", "      <td>-0.02% (88.37)</td>\n", "      <td>0.10% (88.47)</td>\n", "      <td>-0.04% (88.35)</td>\n", "      <td>0.02% (88.40)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Spotlight Story View Time</th>\n", "      <th>0.9198</th>\n", "      <th>570.9</th>\n", "      <td>-0.04% (570.7)</td>\n", "      <td>-0.02% (570.8)</td>\n", "      <td>-0.16% (570.0)</td>\n", "      <td>-0.03% (570.8)</td>\n", "      <td>0.03% (571.1)</td>\n", "      <td>-0.01% (570.9)</td>\n", "      <td>0.12% (571.6)</td>\n", "      <td>0.04% (571.1)</td>\n", "      <td>0.04% (571.1)</td>\n", "      <td>0.02% (571.0)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Story Snap Post</th>\n", "      <th>0.9560</th>\n", "      <th>2.45</th>\n", "      <td>0.20% (2.46)</td>\n", "      <td>0.22% (2.46)</td>\n", "      <td>-0.03% (2.45)</td>\n", "      <td>-0.24% (2.44)</td>\n", "      <td>-0.02% (2.45)</td>\n", "      <td>-0.08% (2.45)</td>\n", "      <td>-0.03% (2.45)</td>\n", "      <td>-0.19% (2.45)</td>\n", "      <td>-0.08% (2.45)</td>\n", "      <td>0.27% (2.46)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Story Snap View</th>\n", "      <th>0.8156</th>\n", "      <th>508.3</th>\n", "      <td>-0.06% (508.0)</td>\n", "      <td>-0.02% (508.2)</td>\n", "      <td>0.05% (508.5)</td>\n", "      <td>-0.06% (508.0)</td>\n", "      <td>0.07% (508.6)</td>\n", "      <td>0.06% (508.6)</td>\n", "      <td>-0.02% (508.2)</td>\n", "      <td>0.01% (508.3)</td>\n", "      <td>-0.08% (507.9)</td>\n", "      <td>0.03% (508.4)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["stat                                                   percent_difference  \\\n", "seed                                                     layer1_$20250604   \n", "assignment                                                   Exp 01 (10%)   \n", "metric                        p_mean_anova overall_avg                      \n", "App Active Days               0.1820       5.75             -0.01% (5.75)   \n", "Chat Send                     0.1850       140.7            0.17% (141.0)   \n", "Chat View                     0.6805       193.6            0.14% (193.8)   \n", "Direct Snap Create            0.4377       66.37            0.12% (66.45)   \n", "Direct Snap Send              0.4202       40.82            0.19% (40.90)   \n", "Direct Snap View              0.8394       162.3            0.04% (162.4)   \n", "Direct Snap Save              0.4030       11.40           -0.01% (11.40)   \n", "DF Non-Friend Story View      0.1692       27.48           -0.14% (27.44)   \n", "DF Non-Friend Story View Time 0.6808       794.8           -0.06% (794.3)   \n", "Friend Story View             0.8112       141.5           -0.04% (141.4)   \n", "Friend Story View Time        0.7259       373.2           -0.02% (373.1)   \n", "Lens Send                     0.8602       7.08              0.11% (7.09)   \n", "Lens Swipe                    0.9765       132.5           -0.04% (132.5)   \n", "Spotlight Story View          0.8994       88.38            0.05% (88.43)   \n", "Spotlight Story View Time     0.9198       570.9           -0.04% (570.7)   \n", "Story Snap Post               0.9560       2.45              0.20% (2.46)   \n", "Story Snap View               0.8156       508.3           -0.06% (508.0)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                Exp 02 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.1820       5.75           0.00% (5.75)   \n", "Chat Send                     0.1850       140.7         0.09% (140.9)   \n", "Chat View                     0.6805       193.6         0.08% (193.7)   \n", "Direct Snap Create            0.4377       66.37         0.03% (66.39)   \n", "Direct Snap Send              0.4202       40.82         0.03% (40.84)   \n", "Direct Snap View              0.8394       162.3         0.03% (162.3)   \n", "Direct Snap Save              0.4030       11.40         0.02% (11.40)   \n", "DF Non-Friend Story View      0.1692       27.48        -0.06% (27.47)   \n", "DF Non-Friend Story View Time 0.6808       794.8        -0.01% (794.7)   \n", "Friend Story View             0.8112       141.5         0.08% (141.6)   \n", "Friend Story View Time        0.7259       373.2         0.09% (373.6)   \n", "Lens Send                     0.8602       7.08           0.01% (7.08)   \n", "Lens Swipe                    0.9765       132.5         0.01% (132.5)   \n", "Spotlight Story View          0.8994       88.38        -0.05% (88.34)   \n", "Spotlight Story View Time     0.9198       570.9        -0.02% (570.8)   \n", "Story Snap Post               0.9560       2.45           0.22% (2.46)   \n", "Story Snap View               0.8156       508.3        -0.02% (508.2)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                Exp 03 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.1820       5.75          -0.00% (5.75)   \n", "Chat Send                     0.1850       140.7        -0.07% (140.6)   \n", "Chat View                     0.6805       193.6        -0.08% (193.4)   \n", "Direct Snap Create            0.4377       66.37         0.03% (66.39)   \n", "Direct Snap Send              0.4202       40.82         0.05% (40.85)   \n", "Direct Snap View              0.8394       162.3        -0.02% (162.3)   \n", "Direct Snap Save              0.4030       11.40        -0.05% (11.40)   \n", "DF Non-Friend Story View      0.1692       27.48         0.14% (27.52)   \n", "DF Non-Friend Story View Time 0.6808       794.8         0.08% (795.4)   \n", "Friend Story View             0.8112       141.5         0.00% (141.5)   \n", "Friend Story View Time        0.7259       373.2        -0.04% (373.1)   \n", "Lens Send                     0.8602       7.08          -0.01% (7.08)   \n", "Lens Swipe                    0.9765       132.5         0.01% (132.5)   \n", "Spotlight Story View          0.8994       88.38        -0.03% (88.36)   \n", "Spotlight Story View Time     0.9198       570.9        -0.16% (570.0)   \n", "Story Snap Post               0.9560       2.45          -0.03% (2.45)   \n", "Story Snap View               0.8156       508.3         0.05% (508.5)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                Exp 04 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.1820       5.75           0.00% (5.75)   \n", "Chat Send                     0.1850       140.7        -0.01% (140.7)   \n", "Chat View                     0.6805       193.6         0.01% (193.6)   \n", "Direct Snap Create            0.4377       66.37        -0.04% (66.34)   \n", "Direct Snap Send              0.4202       40.82        -0.04% (40.81)   \n", "Direct Snap View              0.8394       162.3         0.01% (162.3)   \n", "Direct Snap Save              0.4030       11.40         0.04% (11.41)   \n", "DF Non-Friend Story View      0.1692       27.48        -0.05% (27.47)   \n", "DF Non-Friend Story View Time 0.6808       794.8         0.01% (794.8)   \n", "Friend Story View             0.8112       141.5         0.01% (141.5)   \n", "Friend Story View Time        0.7259       373.2         0.01% (373.3)   \n", "Lens Send                     0.8602       7.08          -0.02% (7.08)   \n", "Lens Swipe                    0.9765       132.5        -0.01% (132.5)   \n", "Spotlight Story View          0.8994       88.38        -0.12% (88.28)   \n", "Spotlight Story View Time     0.9198       570.9        -0.03% (570.8)   \n", "Story Snap Post               0.9560       2.45          -0.24% (2.44)   \n", "Story Snap View               0.8156       508.3        -0.06% (508.0)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                Exp 05 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.1820       5.75          -0.01% (5.75)   \n", "Chat Send                     0.1850       140.7         0.01% (140.7)   \n", "Chat View                     0.6805       193.6        -0.03% (193.5)   \n", "Direct Snap Create            0.4377       66.37        -0.02% (66.36)   \n", "Direct Snap Send              0.4202       40.82        -0.04% (40.81)   \n", "Direct Snap View              0.8394       162.3        -0.01% (162.3)   \n", "Direct Snap Save              0.4030       11.40         0.05% (11.41)   \n", "DF Non-Friend Story View      0.1692       27.48        -0.01% (27.48)   \n", "DF Non-Friend Story View Time 0.6808       794.8        -0.05% (794.3)   \n", "Friend Story View             0.8112       141.5         0.03% (141.5)   \n", "Friend Story View Time        0.7259       373.2         0.03% (373.3)   \n", "Lens Send                     0.8602       7.08           0.14% (7.09)   \n", "Lens Swipe                    0.9765       132.5         0.04% (132.6)   \n", "Spotlight Story View          0.8994       88.38         0.07% (88.44)   \n", "Spotlight Story View Time     0.9198       570.9         0.03% (571.1)   \n", "Story Snap Post               0.9560       2.45          -0.02% (2.45)   \n", "Story Snap View               0.8156       508.3         0.07% (508.6)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                Exp 06 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.1820       5.75          -0.00% (5.75)   \n", "Chat Send                     0.1850       140.7         0.04% (140.8)   \n", "Chat View                     0.6805       193.6        -0.06% (193.5)   \n", "Direct Snap Create            0.4377       66.37        -0.07% (66.32)   \n", "Direct Snap Send              0.4202       40.82        -0.01% (40.82)   \n", "Direct Snap View              0.8394       162.3         0.07% (162.4)   \n", "Direct Snap Save              0.4030       11.40        -0.09% (11.39)   \n", "DF Non-Friend Story View      0.1692       27.48         0.12% (27.52)   \n", "DF Non-Friend Story View Time 0.6808       794.8         0.10% (795.6)   \n", "Friend Story View             0.8112       141.5         0.04% (141.5)   \n", "Friend Story View Time        0.7259       373.2        -0.02% (373.1)   \n", "Lens Send                     0.8602       7.08           0.01% (7.08)   \n", "Lens Swipe                    0.9765       132.5        -0.02% (132.5)   \n", "Spotlight Story View          0.8994       88.38         0.02% (88.40)   \n", "Spotlight Story View Time     0.9198       570.9        -0.01% (570.9)   \n", "Story Snap Post               0.9560       2.45          -0.08% (2.45)   \n", "Story Snap View               0.8156       508.3         0.06% (508.6)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                Exp 07 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.1820       5.75           0.00% (5.75)   \n", "Chat Send                     0.1850       140.7        -0.04% (140.7)   \n", "Chat View                     0.6805       193.6         0.07% (193.7)   \n", "Direct Snap Create            0.4377       66.37        -0.02% (66.36)   \n", "Direct Snap Send              0.4202       40.82        -0.02% (40.81)   \n", "Direct Snap View              0.8394       162.3         0.01% (162.3)   \n", "Direct Snap Save              0.4030       11.40         0.01% (11.40)   \n", "DF Non-Friend Story View      0.1692       27.48        -0.07% (27.47)   \n", "DF Non-Friend Story View Time 0.6808       794.8        -0.03% (794.5)   \n", "Friend Story View             0.8112       141.5        -0.06% (141.4)   \n", "Friend Story View Time        0.7259       373.2        -0.07% (372.9)   \n", "Lens Send                     0.8602       7.08           0.00% (7.08)   \n", "Lens Swipe                    0.9765       132.5        -0.01% (132.5)   \n", "Spotlight Story View          0.8994       88.38        -0.02% (88.37)   \n", "Spotlight Story View Time     0.9198       570.9         0.12% (571.6)   \n", "Story Snap Post               0.9560       2.45          -0.03% (2.45)   \n", "Story Snap View               0.8156       508.3        -0.02% (508.2)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                Exp 08 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.1820       5.75           0.00% (5.75)   \n", "Chat Send                     0.1850       140.7        -0.01% (140.7)   \n", "Chat View                     0.6805       193.6         0.04% (193.7)   \n", "Direct Snap Create            0.4377       66.37         0.10% (66.44)   \n", "Direct Snap Send              0.4202       40.82         0.10% (40.86)   \n", "Direct Snap View              0.8394       162.3        -0.00% (162.3)   \n", "Direct Snap Save              0.4030       11.40        -0.01% (11.40)   \n", "DF Non-Friend Story View      0.1692       27.48        -0.00% (27.48)   \n", "DF Non-Friend Story View Time 0.6808       794.8         0.11% (795.7)   \n", "Friend Story View             0.8112       141.5        -0.07% (141.4)   \n", "Friend Story View Time        0.7259       373.2         0.03% (373.4)   \n", "Lens Send                     0.8602       7.08          -0.08% (7.08)   \n", "Lens Swipe                    0.9765       132.5        -0.01% (132.5)   \n", "Spotlight Story View          0.8994       88.38         0.10% (88.47)   \n", "Spotlight Story View Time     0.9198       570.9         0.04% (571.1)   \n", "Story Snap Post               0.9560       2.45          -0.19% (2.45)   \n", "Story Snap View               0.8156       508.3         0.01% (508.3)   \n", "\n", "stat                                                                    \\\n", "seed                                                                     \n", "assignment                                                Exp 09 (10%)   \n", "metric                        p_mean_anova overall_avg                   \n", "App Active Days               0.1820       5.75           0.00% (5.75)   \n", "Chat Send                     0.1850       140.7        -0.09% (140.6)   \n", "Chat View                     0.6805       193.6        -0.08% (193.4)   \n", "Direct Snap Create            0.4377       66.37        -0.09% (66.31)   \n", "Direct Snap Send              0.4202       40.82        -0.16% (40.76)   \n", "Direct Snap View              0.8394       162.3        -0.08% (162.2)   \n", "Direct Snap Save              0.4030       11.40        -0.01% (11.40)   \n", "DF Non-Friend Story View      0.1692       27.48        -0.09% (27.46)   \n", "DF Non-Friend Story View Time 0.6808       794.8        -0.14% (793.6)   \n", "Friend Story View             0.8112       141.5        -0.06% (141.4)   \n", "Friend Story View Time        0.7259       373.2        -0.08% (372.9)   \n", "Lens Send                     0.8602       7.08          -0.15% (7.07)   \n", "Lens Swipe                    0.9765       132.5         0.01% (132.5)   \n", "Spotlight Story View          0.8994       88.38        -0.04% (88.35)   \n", "Spotlight Story View Time     0.9198       570.9         0.04% (571.1)   \n", "Story Snap Post               0.9560       2.45          -0.08% (2.45)   \n", "Story Snap View               0.8156       508.3        -0.08% (507.9)   \n", "\n", "stat                                                                    \n", "seed                                                                    \n", "assignment                                                Exp 10 (10%)  \n", "metric                        p_mean_anova overall_avg                  \n", "App Active Days               0.1820       5.75           0.01% (5.75)  \n", "Chat Send                     0.1850       140.7        -0.08% (140.6)  \n", "Chat View                     0.6805       193.6        -0.07% (193.4)  \n", "Direct Snap Create            0.4377       66.37        -0.03% (66.35)  \n", "Direct Snap Send              0.4202       40.82        -0.08% (40.79)  \n", "Direct Snap View              0.8394       162.3        -0.03% (162.2)  \n", "Direct Snap Save              0.4030       11.40         0.03% (11.40)  \n", "DF Non-Friend Story View      0.1692       27.48         0.14% (27.52)  \n", "DF Non-Friend Story View Time 0.6808       794.8        -0.01% (794.7)  \n", "Friend Story View             0.8112       141.5         0.06% (141.6)  \n", "Friend Story View Time        0.7259       373.2         0.07% (373.5)  \n", "Lens Send                     0.8602       7.08          -0.01% (7.08)  \n", "Lens Swipe                    0.9765       132.5         0.01% (132.5)  \n", "Spotlight Story View          0.8994       88.38         0.02% (88.40)  \n", "Spotlight Story View Time     0.9198       570.9         0.02% (571.0)  \n", "Story Snap Post               0.9560       2.45           0.27% (2.46)  \n", "Story Snap View               0.8156       508.3         0.03% (508.4)  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(HTML(\"<h2>The default seed is <strong style='color:blue;'>{}</strong></h3>\".format(default_seed)))\n", "display(HTML(\"<p>These numbers are provided as the baseline for comparison.</p>\"))\n", "\n", "display_table(default_seed)\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": true}}, "nbformat": 4, "nbformat_minor": 4}