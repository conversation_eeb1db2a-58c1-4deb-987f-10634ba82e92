{"cells": [{"cell_type": "code", "execution_count": null, "id": "72792b79", "metadata": {"editable": true, "jupyter": {"is_executing": true}, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["# Uncomment these lines when running for the first time to install the required packages.  This is mostly required for local development.\n", "# %pip install openai\n", "# %pip install google-auth\n", "# %pip install google-cloud-bigquery\n", "# %pip install google-cloud-storage\n", "# %pip install lca\n", "# %pip install banjo\n", "# %pip install tqdm\n", "# %pip install oauth2client\n", "# %pip install tqdm.contrib\n", "\n", "import time\n", "from google.cloud import storage\n", "from banjo import utils\n", "from datetime import datetime\n", "from tqdm.contrib.concurrent import thread_map\n", "from concurrent.futures import ThreadPoolExecutor\n", "from tqdm import tqdm"]}, {"cell_type": "code", "execution_count": null, "id": "9a917a23", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["#from banjo.utils.shibainu import Classification, Validation, Visualization, estimate_run_cost\n", "from banjo.utils.shibainu import Classification, configure_logger\n", "\n", "import logging\n", "\n", "configure_logger(level=logging.ERROR)"]}, {"cell_type": "code", "execution_count": null, "id": "05774d23", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["husky_version = 'v1'"]}, {"cell_type": "code", "execution_count": null, "id": "0e74e7d1", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["SERVICE_ACCOUNT = \"<EMAIL>\"\n", "# Use the `sc-bq-gcs-billingonly` project for local development\n", "#PROJECT = 'sc-bq-gcs-billingonly' \n", "PROJECT = 'bitmoji-analytics' \n", "PRIORITY = 'INTERACTIVE' #Should be \"BATCH' in prod\n", "TEMPERATURE = 0\n", "MAX_TOKEN = 1280\n", "MAX_WORKER = 5\n", "DATE_NODASH = '********'\n", "TAG_TYPE = \"weather\"\n", "UPLOAD_TO_GCS = False"]}, {"cell_type": "code", "execution_count": null, "id": "a899f891", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["## Validation parameter\n", "VALIDATION_MODEL_NAME = 'gpt-4o'"]}, {"cell_type": "code", "execution_count": null, "id": "f79adedd", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["### Init parameters"]}, {"cell_type": "code", "execution_count": null, "id": "dac38cf3", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["MODEL_NAME = \"gpt-4o\"\n", "PASS = \"https://pass.mesh.sc-corp.net/privacyReviews/pass?id=************************************\""]}, {"cell_type": "code", "execution_count": null, "id": "b6c7bc3f", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["### Data parameters"]}, {"cell_type": "code", "execution_count": null, "id": "6fb96b9b", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["parameters"]}, "outputs": [], "source": ["WEATHER_PROMPT = \"\"\"You are an expert garment classifier.  \n", "Analyze the provided 3D rendered image and determine the typical weather suitability of the garment based on its design, materials, and intended use.   \n", "\n", "Categories: All Year, Warm Weather, Cold Weather\n", "\n", "Instructions:\n", "- Return only the applicable categories as a comma-separated list (e.g., Warm Weather, All Year).\n", "- Include multiple categories if appropriate.\n", "- Warm weather garments are typically lighter material and short sleeved or legged.\n", "- Cold weather garments are typically heaver material and long sleeved or legged.\n", "- If something can technically be warn for either Warm or Cold weather conditions also include All Year in the result.\n", "- Return an empty response if the category is unclear or if no garment is shown.\n", "- Return \"image_failure\" only if you are unable to download or view the image.\n", "- Do not include any explanations or extra text.\"\"\"\n", "\n", "\n", "# This mapping connects specific colors to their basic color counterparts\n", "# for use in both specific classification and basic filtering\n", "COLOR_TO_BASIC_COLOR_MAPPING = {\n", "    \"Navy\": \"Blue\",\n", "    \"Teal\": \"Green\",\n", "    \"Burgundy\": \"Red\",\n", "    \"Gold\": \"Yellow\",\n", "    \"Silver\": \"Gray\",\n", "    \"Olive\": \"Green\",\n", "    \"Coral\": \"Orange\",\n", "    \"Turquoise\": \"Blue\",\n", "    \"Mint\": \"Green\",\n", "}\n", "\n", "COLORS = [\n", "        \"Black\",\n", "        \"White\",\n", "        \"Gray\",\n", "        \"Red\",\n", "        \"Blue\",\n", "        \"Green\",\n", "        \"Yellow\",\n", "        \"Orange\",\n", "        \"Purple\",\n", "        \"Pink\",\n", "        \"Brown\",\n", "        \"Beige\",\n", "        \"Multicolor\",\n", "        \"Navy\",\n", "        \"Teal\",\n", "        \"Burgundy\",\n", "        \"Gold\",\n", "        \"Silver\",\n", "        \"Olive\",\n", "        \"Coral\",\n", "        \"Turquoise\",\n", "        \"Mint\",\n", "    ]\n", "\n", "COLOR_PROMPT = f\"\"\"\n", "    You are an expert fashion classifier analyzing 3D rendered garments. Determine the color(s) of the displayed garment.\n", "\n", "    Available categories: {', '.join(COLORS)}\n", "\n", "    FORMAT REQUIREMENTS (CRITICAL):\n", "    - Return ONLY a plain text comma-separated list with color:proportion format\n", "    - DO NOT include backticks (`) or quotation marks\n", "    - DO NOT return JSON format\n", "    - DO NOT include any explanatory text\n", "    - Example correct format: red:0.70, black:0.30\n", "    - Example correct format: blue:1.0\n", "    - Example correct format: multicolor:1.0\n", "\n", "    Classification instructions:\n", "    1. Examine the entire garment and identify all colors that cover more than 10% of the surface area\n", "    2. For standard colors (red, blue, green, etc.), use the closest match from the available categories\n", "    3. IMPORTANT: Be extremely careful to identify the TRUE color of the garment. DO NOT misclassify colorful garments as white.\n", "    \n", "    4. SPECIFIC TO BASIC COLOR MAPPING (ONLY USE WHEN APPLICABLE):\n", "       When you identify a specific color variant that matches the left side of this list, you MUST ALSO include \n", "       its basic color counterpart (right side) with the same proportion value (Do this for all colors in the list below):\n", "       \n", "       • Navy → include both \"Navy\" AND \"Blue\" with same proportion\n", "       • Teal → include both \"Teal\" AND \"Green\" with same proportion\n", "       • Burgundy → include both \"Burgundy\" AND \"Red\" with same proportion\n", "       • Gold → include both \"Gold\" AND \"Yellow\" with same proportion\n", "       • Silver → include both \"Silver\" AND \"Gray\" with same proportion\n", "       • Olive → include both \"Olive\" AND \"Green\" with same proportion\n", "       • Coral → include both \"Coral\" AND \"Orange\" with same proportion\n", "       • Turquoise → include both \"Turquoise\" AND \"Blue\" with same proportion\n", "       • Mint → include both \"Mint\" AND \"Green\" with same proportion\n", "\n", "    5. Return colors as a comma-separated list with coverage percentages, ordered from most to least coverage\n", "\n", "    Special color handling:\n", "    - Gold: Use for yellowish metallic finishes resembling gold metal\n", "    - Silver: Use for grayish/white metallic finishes resembling silver metal\n", "    - Multicolor: Use when either:\n", "      * The garment contains 4+ distinct colors each covering more than 10% of the surface\n", "      * The garment has an intricate pattern with multiple small colors (e.g., complex prints)\n", "      * The colors blend together in a way that makes individual color identification impractical\n", "\n", "    Special case handling:\n", "    - If the garment is outerwear with a visible shirt underneath, ignore the shirt for color classification\n", "    - For footwear, ignore the insole color\n", "    - For accessories, focus only on the main visible part\n", "\n", "    Remember to return ONLY the comma-separated list in the format: color:proportion, color:proportion\n", "    All proportions should be between 0.10 and 1.00\n", "    The total of all proportions should equal 1.0\n", "\"\"\"\n", "\n", "custom_prompt = WEATHER_PROMPT\n", "\n", "SELECTED_GARMENT_LIST = [\n", "    \"bag\",\n", "    \"bodysuit\",\n", "    \"bottom\",\n", "    \"footwear\",\n", "    \"glasses\",\n", "    \"hat\",\n", "    \"one_piece\",\n", "    \"outerwear\",\n", "    \"top\",\n", "]\n", "\n", "SAMPLE_LIMIT = True\n", "SAMPLE_SIZE = None\n", "\n", "# Determines whether or not to filter by release_date\n", "FILTER_RELEASE_DATE = False\n", "# Format for release dates: YYYY-MM-DD\n", "RELEASE_START_DATE = None\n", "RELEASE_END_DATE = None\n", "\n", "# Determines whether or not to filter by created_ms\n", "FILTER_CREATED_DATE = False\n", "# Format for create dates: YYYY-MM-DD\n", "CREATED_START_DATE = None\n", "CREATED_END_DATE = None"]}, {"cell_type": "code", "execution_count": null, "id": "0113dd38", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["# create list\n", "def ensure_list(parameter):\n", "    if isinstance(parameter, list):\n", "        return parameter\n", "    else:\n", "        return [parameter]\n", "\n", "\n", "SELECTED_GARMENT_LIST = ensure_list(SELECTED_GARMENT_LIST)\n", "\n", "selected_garment_types_upper = []\n", "for garment_type in SELECTED_GARMENT_LIST:\n", "    selected_garment_types_upper.append(garment_type.upper())"]}, {"cell_type": "code", "execution_count": null, "id": "e1383774", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["COLUMN_NAME = \"image_url\"\n", "\n", "# Function to generate tone-related SQL dynamically\n", "# tones_index can either be \"tones_index\" or and integer between 0 and 9\n", "def generate_tone_sql(prefix, tones_index, num_tones=10):\n", "    \"\"\"\n", "    Generate SQL for tone parameters dynamically.\n", "\n", "    Args:\n", "        prefix (str): The prefix for the tone (e.g., 'top', 'bottom', or 'lower(garment_type)').\n", "        num_tones (int): The number of tones to generate (default is 10).\n", "\n", "    Returns:\n", "        str: The generated SQL for tone parameters.\n", "    \"\"\"\n", "    tone_sql = []\n", "    for i in range(1, num_tones + 1):\n", "        tone_sql.append(\n", "            f'if(tones[SAFE_OFFSET({tones_index})].color{i} is not null, '\n", "            f'concat(\"{prefix}_tone{i}=\", tones[SAFE_OFFSET({tones_index})].color{i}, \"&\"), \"\")'\n", "        )\n", "    return \", \".join(tone_sql)\n", "\n", "tones_index = \"tones_index\" if TAG_TYPE == \"color\" else 0\n", "\n", "def date_to_epoch(date_string):\n", "    if not date_string:\n", "        return 0\n", "    dt_object = datetime.strptime(date_string, '%Y-%m-%d')\n", "    epoch_time = int(dt_object.timestamp())\n", "    return epoch_time\n", "\n", "created_start_date_seconds = date_to_epoch(CREATED_START_DATE)\n", "created_end_date_seconds = date_to_epoch(CREATED_END_DATE)\n", "\n", "created_date_filter = \"\"\n", "if FILTER_CREATED_DATE:\n", "    created_date_filter = f\"AND (created_ms >= {created_start_date_seconds} AND created_ms <= {created_end_date_seconds})\"\n", "\n", "release_date_filter = \"\"\n", "if FILTER_RELEASE_DATE:\n", "    release_date_filter = f\"AND (release_date >= '{RELEASE_START_DATE}' AND release_date <= '{RELEASE_END_DATE}')\"\n", "\n", "sample_limit_part = \"\"\n", "if SAMPLE_LIMIT and SAMPLE_SIZE is not None:\n", "    sample_limit_part = f\"LIMIT {SAMPLE_SIZE}\"\n", "\n", "CUSTOM_SQL = f\"\"\"\n", "SELECT * FROM (\n", "    -- First query: Items with non-empty tones\n", "    SELECT\n", "    brand_content_id, \n", "    brand_name, \n", "    cms_id, \n", "    content_type,\n", "    garment_type, \n", "    option_id, \n", "    tags, \n", "    token_price, \n", "    token_catalog_id,\n", "    release_date,\n", "    created_ms,\n", "    tones,\n", "    {\"CAST(tones_index AS STRING) as tones_index,\" if TAG_TYPE == \"color\" else \"\"}\n", "    CONCAT(\"https://preview.bitmoji.com/avatar-builder-v3/preview/\", \n", "    case \n", "        when garment_type in (\"ONE_PIECE\") then concat(\n", "            \"mannequin-center?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&top=\", option_id, '&scope=CLOTHES&',\n", "            {generate_tone_sql(\"top\", tones_index)},\n", "            \"bottom=\", option_id, '&',\n", "            {generate_tone_sql(\"bottom\", tones_index)}\n", "        )\n", "        when garment_type in (\"BODYSUIT\") then concat(\n", "            \"bodysuit?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&top=\", option_id, '&',\n", "            {generate_tone_sql(\"top\", tones_index)},\n", "            \"bottom=\", option_id, '&',\n", "            {generate_tone_sql(\"bottom\", tones_index)}\n", "        )\n", "        when garment_type in (\"BAG\") then concat(\n", "            \"bag?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&bag=\", option_id, '&scope=CLOTHES&footwear=0&',\n", "            {generate_tone_sql(\"bag\", tones_index)}\n", "        )\n", "        when garment_type in (\"BOTTOM\") then concat(\n", "            \"bottom?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&bottom=\", option_id, '&scope=CLOTHES&footwear=0&',\n", "            {generate_tone_sql(\"bottom\", tones_index)}\n", "        )\n", "        when garment_type in (\"FOOTWEAR\") then concat(\n", "            \"footwear?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&footwear=\", option_id, '&scope=CLOTHES&',\n", "            {generate_tone_sql(\"footwear\", tones_index)}\n", "        )\n", "        when garment_type in (\"OUTERWEAR\") then concat(\n", "            \"outerwear?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&outerwear=\", option_id, '&scope=CLOTHES&bottom=0&',\n", "            {generate_tone_sql(\"outerwear\", tones_index)}\n", "        )\n", "        when garment_type in (\"HAT\") then concat(\n", "            \"hat?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&hat=\", option_id, '&',\n", "            {generate_tone_sql(\"hat\", tones_index)}\n", "        )\n", "        when garment_type in (\"GLASSES\") then concat(\n", "            \"glasses?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&glasses=\", option_id, '&',\n", "            {generate_tone_sql(\"glasses\", tones_index)}\n", "        )\n", "        when garment_type in (\"SOCK\") then concat(\n", "            \"sock?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&sock=\", option_id, '&',\n", "            {generate_tone_sql(\"sock\", tones_index)}\n", "        )\n", "        when garment_type in (\"TOP\") then concat(\n", "            \"mannequin-center?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&top=\", option_id, '&scope=CLOTHES&',\n", "            {generate_tone_sql(\"top\", tones_index)}\n", "        )\n", "        else concat(\n", "            \"mannequin-center?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&\", lower(garment_type), \"=\", option_id, '&scope=CLOTHES&',\n", "            {generate_tone_sql(\"lower(garment_type)\", tones_index)}\n", "        )\n", "    end) as image_url  \n", "        \n", "    FROM `sc-analytics.report_bitmoji.cms_bitmoji_fashion_metadata_table_2*`{\", UNNEST(tones) WITH OFFSET AS tones_index\" if TAG_TYPE == \"color\" else \"\"}\n", "    WHERE _TABLE_SUFFIX = (\n", "        SELECT MAX(REPLACE(table_name, 'cms_bitmoji_fashion_metadata_table_2', '')) \n", "        FROM `sc-analytics.report_bitmoji.INFORMATION_SCHEMA.TABLES`\n", "        WHERE table_name LIKE '%cms_bitmoji_fashion_metadata_table_2%'\n", "    )\n", "    AND content_type IN ('GARMENT')\n", "    AND garment_type IN UNNEST({selected_garment_types_upper})\n", "    AND (internal_art_name IS NULL OR \n", "        (lower(internal_art_name) NOT LIKE '%baresu%' AND \n", "            lower(internal_art_name) NOT LIKE '%test%'))\n", "        AND ARRAY_LENGTH(tones) > 0\n", "    {created_date_filter}\n", "    {release_date_filter}\n", "\n", "    UNION ALL\n", "\n", "    -- Second query: Items with empty tones\n", "    SELECT\n", "    brand_content_id, \n", "    brand_name, \n", "    cms_id, \n", "    content_type,\n", "    garment_type, \n", "    option_id, \n", "    tags, \n", "    token_price, \n", "    token_catalog_id,\n", "    release_date,\n", "    created_ms,\n", "    tones,\n", "    {\"'' as tones_index,\" if TAG_TYPE == \"color\" else \"\"}\n", "    CONCAT(\"https://preview.bitmoji.com/avatar-builder-v3/preview/\", \n", "    case \n", "        when garment_type in (\"ONE_PIECE\") then \n", "        \"mannequin-center?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&top=\" || option_id || '&scope=CLOTHES&bottom=' || option_id\n", "        when garment_type in (\"BODYSUIT\") then \n", "        \"bodysuit?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&top=\" || option_id || '&bottom=' || option_id\n", "        when garment_type in (\"BAG\") then \n", "        \"bag?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&bag=\" || option_id || '&scope=CLOTHES&footwear=0'\n", "        when garment_type in (\"BOTTOM\") then \n", "        \"bottom?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&bottom=\" || option_id || '&scope=CLOTHES&footwear=0'\n", "        when garment_type in (\"FOOTWEAR\") then \n", "        \"footwear?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&footwear=\" || option_id || '&scope=CLOTHES'\n", "        when garment_type in (\"OUTERWEAR\") then \n", "        \"outerwear?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&outerwear=\" || option_id || '&scope=CLOTHES&bottom=0'\n", "        when garment_type in (\"HAT\") then \n", "        \"hat?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&hat=\" || option_id\n", "        when garment_type in (\"GLASSES\") then \n", "        \"glasses?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&glasses=\" || option_id\n", "        when garment_type in (\"SOCK\") then \n", "        \"sock?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&sock=\" || option_id\n", "        when garment_type in (\"TOP\") then \n", "        \"mannequin-center?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&top=\" || option_id || '&scope=CLOTHES'\n", "        else \n", "        \"mannequin-center?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&\" || lower(garment_type) || \"=\" || option_id || '&scope=CLOTHES'\n", "    end) as image_url  \n", "    FROM `sc-analytics.report_bitmoji.cms_bitmoji_fashion_metadata_table_2*`\n", "    WHERE _TABLE_SUFFIX = (\n", "    SELECT MAX(REPLACE(table_name, 'cms_bitmoji_fashion_metadata_table_2', '')) \n", "    FROM `sc-analytics.report_bitmoji.INFORMATION_SCHEMA.TABLES`\n", "    WHERE table_name LIKE '%cms_bitmoji_fashion_metadata_table_2%'\n", "    )\n", "    AND content_type IN ('GARMENT')\n", "    AND garment_type IN UNNEST({selected_garment_types_upper})\n", "    AND (internal_art_name IS NULL OR \n", "        (lower(internal_art_name) NOT LIKE '%baresu%' AND \n", "        lower(internal_art_name) NOT LIKE '%test%'))\n", "    AND (created_ms >= 0)\n", "    AND (tones IS NULL OR ARRAY_LENGTH(tones) = 0)\n", "    {created_date_filter}\n", "    {release_date_filter}\n", ")\n", "{sample_limit_part}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "f73f9c69", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["### Output parameters"]}, {"cell_type": "code", "execution_count": null, "id": "205d6c6e", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["DESTINATION_TABLE = 'shibainu_image_bitmoji_husky_test_********'\n", "DESTINATION_DATASET = 'report_bitmoji_dev'\n", "DESTINATION_PROJECT = 'bitmoji-analytics'\n", "\n", "# DESTINATION_TABLE = 'shibainu_image_bitmoji_husky_test_20250319'\n", "# DESTINATION_DATASET = 'temp_qi'\n", "# DESTINATION_PROJECT = 'spectre-1217'\n", "\n", "RETENTION_DAYS = None\n", "\n", "# utils.gbq.to_gbq(\n", "#                  data,\n", "#                  project_id = PROJECT,\n", "#                  dest_table_name = DESTINATION_TABLE,\n", "#                  dest_dataset_id = DESTINATION_DATASET,\n", "#                  dest_project_id = DESTINATION_PROJECT)"]}, {"cell_type": "code", "execution_count": null, "id": "3ec49642", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["### Parameters processing"]}, {"cell_type": "code", "execution_count": null, "id": "0bc37e31", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["# handle the checkbox\n", "def handle_husky_checkbox(parameter, checked_value, unchecked_value):\n", "    \"\"\"husky always returns the checkbox values as strings\"\"\"\n", "    if isinstance(parameter, str):\n", "        return checked_value\n", "    elif parameter in [checked_value, unchecked_value]:\n", "        return parameter\n", "    else:\n", "        return unchecked_value"]}, {"cell_type": "code", "execution_count": null, "id": "74d47c25", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["# DEFAULT_PROMT  = \"\"\"\n", "# You are a classification model. \n", "# Assign the following message to one of the predefined classes: \n", "# {CLASS_LIST}.\n", "\n", "# Provide only the class name as your response.\n", "# If the class is unclear or the context is not understood, choose 'unknown'.\n", "# \"\"\"\n", "\n", "#USE_CUSTOM_PROMPT = handle_husky_checkbox(USE_CUSTOM_PROMPT, True, False)  \n", "#PROMPT = DEFAULT_PROMT.format(CLASS_LIST = CLASSES) if not USE_CUSTOM_PROMPT else CUSTOM_PROMPT"]}, {"cell_type": "code", "execution_count": null, "id": "a293fdff", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["class PrivacyException(Exception):\n", "    def __init__(self, msg=\"\"\"\n", "        Please, attach the link to approved PASS. \n", "        Note: It is your responsibility to ensure that it is filled out correctly and approved.\"\"\", *args, **kwargs):\n", "        super().__init__(msg, *args, **kwargs)\n", "    pass\n", "\n", "#check if PASS is attached\n", "if (\"https://pass.mesh.sc-corp.net/privacyReviews/pass\" in PASS):\n", "    pass\n", "else:\n", "    raise PrivacyException\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "9a1c6c92", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["## Query data"]}, {"cell_type": "code", "execution_count": null, "id": "59521b9c", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["data = utils.gbq.read_gbq(CUSTOM_SQL, \n", "                          project_id=PROJECT,\n", "                          dialect=\"standard\",\n", "                          priority = PRIORITY)\n", "dataset = data[COLUMN_NAME].values"]}, {"cell_type": "code", "execution_count": null, "id": "d3229c86", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["import pandas as pd\n", "pd.set_option('display.max_colwidth', None)"]}, {"cell_type": "code", "execution_count": null, "id": "8d966dfa", "metadata": {"editable": true, "scrolled": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["data.drop(columns=['tones'])"]}, {"cell_type": "code", "execution_count": null, "id": "6a737451", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["image_urls = data['image_url']"]}, {"cell_type": "code", "execution_count": null, "id": "a56e15fb", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["## Classifcation processing"]}, {"cell_type": "code", "execution_count": null, "id": "da4e8262-fe9c-4160-9901-fe58712be4b9", "metadata": {}, "outputs": [], "source": ["# print(\"Starting classification with prompt\", custom_prompt)\n", "\n", "def create_dynamic_prompt(garment_type, base_prompt):\n", "    \"\"\"\n", "    Create a dynamic prompt that includes the garment type information.\n", "    \n", "    Args:\n", "        garment_type: The type of garment (e.g., 'TOP', 'BOTTOM', 'FOOTWEAR')\n", "        base_prompt: The base prompt template\n", "        \n", "    Returns:\n", "        Modified prompt with garment type context\n", "    \"\"\"\n", "    # Add garment type context to the prompt\n", "    garment_context = f\"You are analyzing a {garment_type.lower()} garment. \"\n", "    \n", "    # Insert the context at the beginning of the prompt\n", "    modified_prompt = garment_context + base_prompt\n", "    \n", "    return modified_prompt\n", "\n", "# Create a base classification model (we'll modify the prompt dynamically)\n", "classification_model = Classification(provider_name = 'openai',\n", "                                     model_name = MODEL_NAME,\n", "                                     prompt = custom_prompt,  # This will be overridden per image\n", "                                     provider_config={\n", "                                        \"service_account\": SERVICE_ACCOUNT\n", "                                     },\n", "                                     processor_config = {'processing_mode': 'image_url'},\n", "                                     model_parameters={'temperature': TEMPERATURE, 'max_token': MAX_TOKEN},\n", "                                     input_type = 'image',\n", "                                     )"]}, {"cell_type": "code", "execution_count": null, "id": "4343aa39", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["def process_image_with_retry(url, garment_type, classification_instance, max_retries=3, delay=2):\n", "    \"\"\"\n", "    Process an image URL with retry logic for handling timeouts.\n", "    \n", "    Args:\n", "        url: Image URL to process\n", "        garment_type: The type of garment (e.g., 'TOP', 'BOTTOM', 'FOOTWEAR')\n", "        classification_instance: The Classification instance to use\n", "        max_retries: Number of retry attempts\n", "        delay: Seconds to wait between retries\n", "        \n", "    Returns:\n", "        Tuple of (result, completion_tokens, prompt_tokens)\n", "    \"\"\"\n", "    for attempt in range(max_retries):\n", "        try:\n", "            # Create dynamic prompt with garment type information\n", "            dynamic_prompt = create_dynamic_prompt(garment_type, custom_prompt)\n", "            \n", "            # Update the classification instance with the dynamic prompt\n", "            classification_instance.prompt = dynamic_prompt\n", "            \n", "            # Use the classify method which handles the image processing internally\n", "            result = classification_instance.classify(url)\n", "            \n", "            # Extract the text result\n", "            text_result = classification_instance.get_result(result)\n", "            \n", "            # Get token usage information\n", "            token_usage = classification_instance.get_token_usage(result)\n", "            completion_tokens = token_usage.get(\"completion_tokens\", 0) if token_usage else 0\n", "            prompt_tokens = token_usage.get(\"prompt_tokens\", 0) if token_usage else 0\n", "            \n", "            return (text_result, completion_tokens, prompt_tokens)\n", "        \n", "        except Exception as e:\n", "            error_msg = str(e).lower()\n", "            # Check if it's a timeout or download error\n", "            if any(err in error_msg for err in [\"timeout\", \"invalid_image_url\"]) and attempt < max_retries-1:\n", "                print(f\"Attempt {attempt+1} failed for {url}. Retrying in {delay} seconds...\")\n", "                time.sleep(delay)\n", "                # Increase delay for next retry (exponential backoff)\n", "                delay *= 1.5\n", "            else:\n", "                print(f\"Failed to process image: {url} after {attempt+1} attempts\")\n", "                # Return a standardized fallback response\n", "                return (\"image_failure\", 0, 0)\n", "start_time = datetime.now()\n", "\n", "# Use our custom function with ThreadPoolExecutor to parallelize the processing\n", "results = []\n", "with ThreadPoolExecutor(max_workers=MAX_WORKER) as executor:\n", "    # Create futures with garment type information\n", "    futures = [executor.submit(process_image_with_retry, url, garment_type, classification_model) \n", "               for url, garment_type in zip(image_urls, data['garment_type'])]\n", "    \n", "    # Process results with progress bar\n", "    for future in tqdm(futures, total=len(futures), desc=\"Processing images\"):\n", "        results.append(future.result())\n", "\n", "print(\"GPT inference time: \", datetime.now()-start_time)"]}, {"cell_type": "code", "execution_count": null, "id": "959cfc4c", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["data['label'] = [i[0] for i in results]\n", "data['completion_tokens'] = [i[1] for i in results]\n", "data['prompt_tokens'] = [i[2] for i in results]"]}, {"cell_type": "code", "execution_count": null, "id": "f37d0e0f", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["data['label']"]}, {"cell_type": "code", "execution_count": null, "id": "d32b4f02", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["# Add the labels as a new column in the DataFrame\n", "#data['predicted_label_prompt1'] = labels\n", "#data['label'] = labels"]}, {"cell_type": "code", "execution_count": null, "id": "332efc9e", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["## Upload results"]}, {"cell_type": "code", "execution_count": null, "id": "922d027c", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["# utils.gbq.to_gbq(\n", "#                  data,\n", "#                  project_id = PROJECT,\n", "#                  dest_table_name = DESTINATION_TABLE,\n", "#                  dest_dataset_id = DESTINATION_DATASET,\n", "#                  dest_project_id = DESTINATION_PROJECT)"]}, {"cell_type": "code", "execution_count": null, "id": "544e72ce", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["# if RETENTION_DAYS:\n", "#     client = bigquery.Client(project=DESTINATION_PROJECT)\n", "#     table = client.get_table(f\"{DESTINATION_PROJECT}.{DESTINATION_DATASET}.{DESTINATION_TABLE}\")  \n", "#     # Set the expiration time (e.g., 7 days from now)\n", "    \n", "#     expiration_time = datetime.now(timezone.utc) + timedelta(days=RETENTION_DAYS)\n", "#     table.expires = expiration_time\n", "    \n", "#     # Update the table with new expiration time\n", "#     client.update_table(table, [\"expires\"])  # Make an API request.\n", "    \n", "#     print(f\"Table {table} expiration set to {expiration_time}\")"]}, {"cell_type": "markdown", "id": "71a2acb2", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["## Result Visualization ##"]}, {"cell_type": "code", "execution_count": null, "id": "7b429ccf", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["# import packages and settings\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import matplotlib\n", "from IPython.display import display, HTML\n", "\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "sns.set_palette(palette='Set2')\n", "sns.set_theme(style=\"white\")\n", "font = {'family' : 'DejaVu Sans',\n", "       'weight' : 'normal',\n", "       'size'   : 12}\n", "matplotlib.rc('font', **font)\n", "\n", "# parameters\n", "# PROJECT_ALIAS = 'Ad Creative Classification'\n", "# RESULT_VISUALIZATION_SAMPLE = 5"]}, {"cell_type": "code", "execution_count": null, "id": "7a48b249", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["class Visualization:\n", "    \n", "    def __init__(self,\n", "                 data\n", "                 ):\n", "        self.data = data\n", "    \n", "    def plot_label_counts(self, \n", "                          column_name,\n", "                          figsize=(10, 5), \n", "                          palette=\"Accent\",\n", "                          threshold=5):\n", "        \"\"\"\n", "        Generates a bar plot showing the distribution of classification labels, \n", "        including only categories with a count >= threshold.\n", "        \n", "        Parameters:\n", "        ----------\n", "        column_name (str): The column name to group by and display in the plot.\n", "        figsize (tuple): Size of the figure.\n", "        palette (str): Color palette for the bars.\n", "        threshold (int): Minimum count required for a category to appear in the plot.\n", "            \n", "        Returns:\n", "        -------\n", "        None\n", "        \"\"\"\n", "        if 'label' not in self.data.columns:\n", "            raise ValueError(\"DataFrame must contain a 'label' column.\")\n", "        \n", "        print(f\"Displaying distribution of classification labels (count >= {threshold}):\")\n", "        \n", "        # Group by the specified column and count\n", "        df_plot = (\n", "            self.data\n", "            .groupby(column_name)\n", "            .size()\n", "            .reset_index(name='count')\n", "            .sort_values('count', ascending=False)\n", "        )\n", "        \n", "        # Filter out categories below the threshold\n", "        df_plot = df_plot[df_plot['count'] >= threshold]\n", "        \n", "        # If there are no categories above the threshold, just print a message\n", "        if df_plot.empty:\n", "            print(f\"No categories with count >= {threshold}\")\n", "            return\n", "        \n", "        fig, ax = plt.subplots(figsize=figsize)\n", "        g = sns.barplot(data=df_plot, x=column_name, y='count', palette=palette, ax=ax)\n", "        \n", "        # Label each bar with its count\n", "        for container in g.containers:\n", "            g.bar_label(container, labels=[f'{int(x.get_height())}' for x in container], fontsize=9)\n", "        \n", "        plt.xticks(rotation=0)\n", "        plt.xlabel(\"Label\")\n", "        plt.ylabel(\"Count\")\n", "        plt.title(\"Distribution by Classification Labels\")\n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "    def display_samples(self, \n", "                        data_type,\n", "                        column_name,\n", "                        n_sample=5):\n", "        \"\"\"\n", "        This function displays sample data points for the classification result.\n", "        \n", "        Parameters:\n", "        -------\n", "            n_sample (int): Number of samples to display per label.\n", "            \n", "        Returns:\n", "        -------\n", "            None\n", "            \n", "        \"\"\"\n", "        \n", "#         # function to render images\n", "#         def show_image_preview(image_url):\n", "#             return f'<img src=\"{image_url}\" width=\"100\"/>'\n", "\n", "#         if 'label' not in self.data.columns:\n", "#             raise ValueError(\"DataFrame must contain a 'label' column.\")\n", "        \n", "#         samples = self.data.groupby('label').apply(lambda x: x.sample(n=min(n_sample, len(x)), random_state=42)).reset_index(drop=True)\n", "#         samples['sample_id'] = samples.groupby('label').cumcount() + 1\n", "        \n", "#         try:  \n", "#             print(f\"Displaying {n_sample} samples for each label:\")\n", "#             if data_type == 'text':\n", "#                 samples_pivot = samples[['sample_id',column_name,'label']].pivot(index='label', columns='sample_id', values=column_name).fillna('')\n", "#                 display(samples_pivot)\n", "#             elif data_type == 'image' or data_type == 'video':\n", "#                 samples['image_preview'] = samples[column_name].apply(show_image_preview)\n", "#                 samples_pivot = samples[['sample_id','image_preview','label']].pivot(index='label', columns='sample_id', values='image_preview').fillna('')\n", "#                 display(HTML(samples_pivot.to_html(escape=False)))\n", "#         except Exception as e:\n", "#             print(f\"Error returning {n_sample} samples for label '{label}': {e}\")\n", "#         print(\"\\n\")\n", "        \n", "        \n", "        def show_image_preview(image_url):\n", "            # Return HTML code for displaying an image at a fixed width\n", "            return f'<img src=\"{image_url}\" style=\"width:200px; display: inline-block; margin: 50px;\"/>'\n", "\n", "        if 'label' not in self.data.columns:\n", "            raise ValueError(\"DataFrame must contain a 'label' column.\")\n", "\n", "        samples = self.data.groupby('label').apply(lambda x: x.sample(n=min(n_sample, len(x)), random_state=42)).reset_index(drop=True)\n", "        samples['sample_id'] = samples.groupby('label').cumcount() + 1\n", "\n", "        try:  \n", "            print(f\"Displaying {n_sample} samples for each label:\")\n", "            if data_type == 'text':\n", "                samples_pivot = samples[['sample_id', column_name, 'label']].pivot(index='label', columns='sample_id', values=column_name).fillna('')\n", "                display(samples_pivot)\n", "            elif data_type == 'image' or data_type == 'video':\n", "                samples['image_preview'] = samples[column_name].apply(show_image_preview)\n", "                samples_pivot = samples[['sample_id', 'image_preview', 'label']].pivot(index='label', columns='sample_id', values='image_preview').fillna('')\n", "                display(HTML(f'''<div style=\"display: grid; grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));\">\n", "                                 {samples_pivot.to_html(escape=False)}\n", "                                 </div>'''))\n", "        except Exception as e:\n", "            print(f\"Error returning {n_sample} samples for label '{label}': {e}\")\n", "        print(\"\\n\")\n", "\n", "\n", "    def display_mismatches(self, data_type, column_name, n_sample=5):\n", "        \"\"\"\n", "        Displays a sample of mismatched rows (where 'label' and 'validation_labels' differ).\n", "        \n", "        Parameters:\n", "        -------\n", "            data_type (str): The type of data to display ('text' or 'image').\n", "            column_name (str): The column containing the text or image paths/URLs.\n", "            n_sample (int): Number of mismatched samples to display.\n", "        \n", "        Returns:\n", "        -------\n", "            None\n", "        \"\"\"\n", "        \n", "        # Helper function for rendering images\n", "        def show_image_preview(image_url):\n", "            return f'<img src=\"{image_url}\" width=\"100\"/>'\n", "    \n", "        # Check if the required columns exist\n", "        for col in ['label', 'validation_label']:\n", "            if col not in self.data.columns:\n", "                raise ValueError(f\"DataFrame must contain a '{col}' column.\")\n", "        \n", "        # Filter for rows where label != validation_labels\n", "        mismatched_data = self.data[self.data['label'] != self.data['validation_label']]\n", "        mismatched_data = mismatched_data[~mismatched_data['validation_label'].isna()]\n", "\n", "        # If there are no mismatches, just print a message\n", "        if mismatched_data.empty:\n", "            print(\"No mismatched rows found.\")\n", "            return\n", "        \n", "        # Take a random sample of size n_sample (or all mismatches if fewer than n_sample)\n", "        mismatch_sample = mismatched_data.sample(\n", "            n=min(n_sample, len(mismatched_data)), \n", "            random_state=42\n", "        )\n", "        \n", "        print(f\"Displaying up to {n_sample} mismatched samples (label vs. validation_labels):\")\n", "        \n", "        # Display the mismatched rows depending on data type\n", "        if data_type == 'text':\n", "            # Show column_name, label, validation_labels\n", "            display(mismatch_sample[[column_name, 'label', 'validation_label']])\n", "            \n", "        elif data_type == 'image' or data_type == 'video':\n", "            # Create an HTML column for image previews\n", "            mismatch_sample['image_preview'] = mismatch_sample[column_name].apply(show_image_preview)\n", "            \n", "            # Display columns: image_preview, label, validation_labels\n", "            display(HTML(\n", "                mismatch_sample[['image_preview', 'label', 'validation_label']]\n", "                .to_html(escape=False, index=False)\n", "            ))\n", "        else:\n", "            raise ValueError(\"data_type must be either 'text' or 'image'.\")\n", "        \n", "        print(\"\\n\")\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "d4b7a5c3", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["RESULT_VISUALIZATION_SAMPLE = 50\n", "RESULT_CATEGORIES_SIZE_THRESHOLD = 5 \n", "DATA_TYPE = \"image\""]}, {"cell_type": "code", "execution_count": null, "id": "5126db2b", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["def select_rows_by_value(data, column_name, target_value):\n", "    \"\"\"\n", "    Selects rows from a DataFrame based on a specific value in a column.\n", "\n", "    Args:\n", "        data: Pandas DataFrame.\n", "        column_name: Name of the column to filter by.\n", "        target_value: The value to match in the specified column.\n", "\n", "    Returns:\n", "        A new DataFrame containing only the rows where the column value matches the target value.\n", "        Returns None if the column is not found or if the DataFrame is empty.\n", "    \"\"\"\n", "    if not isinstance(data, pd.DataFrame) or data.empty:\n", "        print(\"Error: Invalid DataFrame or empty DataFrame.\")\n", "        return None\n", "    \n", "    if column_name not in data.columns:\n", "        print(f\"Error: Column '{column_name}' not found in the DataFrame.\")\n", "        return None\n", "\n", "    selected_rows = data[data[column_name] == target_value]\n", "    selected_rows = selected_rows[selected_rows['label'] != 'Error processing image']  # remove rows with processing error \n", "    selected_rows = selected_rows[selected_rows['label'] != 'error processing']\n", "    return selected_rows"]}, {"cell_type": "code", "execution_count": null, "id": "11809a6a", "metadata": {"editable": true, "scrolled": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["target_values = data['garment_type'].unique()\n", "\n", "for value in target_values:\n", "  selected_df = select_rows_by_value(data, 'garment_type', value)\n", "  if selected_df is not None:\n", "    print(f\"Rows where garment_type is '{value}':\")\n", "    display(selected_df.drop(columns=['tones']).head())\n", "    \n", "    # instantiate the visualization class\n", "    visualization = Visualization(data = selected_df)\n", "\n", "    # figure1. plot the distribution of classification labels\n", "#     visualization.plot_label_counts(figsize=(10,5), column_name='predicted_label_prompt1', threshold=RESULT_CATEGORIES_SIZE_THRESHOLD)\n", "    visualization.plot_label_counts(figsize=(10,5), column_name='label', threshold=RESULT_CATEGORIES_SIZE_THRESHOLD)\n", "\n", "    # figure2. plot the sample of the data\n", "    visualization.display_samples(data_type = DATA_TYPE, column_name=COLUMN_NAME, n_sample=RESULT_VISUALIZATION_SAMPLE)"]}, {"cell_type": "markdown", "id": "34eee9c9", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["## Google Sheet Output"]}, {"cell_type": "code", "execution_count": null, "id": "564e1b39", "metadata": {"ExecuteTime": {"end_time": "2025-04-23T13:34:12.454016Z", "start_time": "2025-04-22T19:57:19.516538Z"}, "editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["#from banjo import utils\n", "#import pandas as pd\n", "#from datetime import datetime\n", "from banjo.utils.pandas.to_google_sheets import StylersToGoogleSheets\n", "from IPython.core.display import display, HTML\n", "display(HTML(\"<style>.container { width:100% !important; }</style>\"))"]}, {"cell_type": "code", "execution_count": null, "id": "2c69ab07", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["# SQL = \"\"\"\n", "# select \n", "# *\n", "# from `bitmoji-analytics.report_bitmoji_dev.shibainu_image_bitmoji_husky_test_20250213`\n", "# \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "e34d716f", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["# df = utils.gbq.read_gbq(SQL, project_id=PROJECT,\n", "#                               dialect=\"standard\",\n", "#                               priority = PRIORITY)"]}, {"cell_type": "code", "execution_count": null, "id": "f9750072", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["df = pd.DataFrame(data)"]}, {"cell_type": "code", "execution_count": null, "id": "711ea1bf", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["df.rename(columns={'cms_id': 'Content ID'}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "d6a24d8d", "metadata": {"editable": true, "scrolled": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["import json\n", "\n", "# # Sample DataFrame with comma-separated tags\n", "# df = pd.DataFrame({\n", "#     'tags_string': [\"shirt,blouse,crop top\", \"jeans,skirt\", \"jacket,sweater\"]\n", "# })\n", "\n", "# Function to split the tags and convert to CSV-friendly JSON string\n", "def convert_tags_to_csv(row):\n", "    \"\"\"\n", "    Converts a row's label, tones, and tones_index into a JSON-friendly format.\n", "    \n", "    Args:\n", "        row (pd.Series): A row from the DataFrame containing 'label', 'tones', and 'tones_index'.\n", "        \n", "    Returns:\n", "        str: JSON string of tag references.\n", "    \"\"\"\n", "    tags_string = row['label']  # Extract the label (tags string)\n", "    tones = row['tones']  # Extract the tones array\n", "    tones_index = row['tones_index'] if 'tones_index' in row else 0  # Extract the tones index\n", "     \n", "    # Generate the colorSwatch list\n", "    colorSwatch = []\n", "    \n", "    # Check if tones is not None and not empty, then check if index is valid\n", "    should_parse_color_swatch = (len(tones) > 0) and (int(tones_index) < len(tones)) and (TAG_TYPE == \"color\")\n", "    if should_parse_color_swatch:\n", "        tone_entry = tones[int(tones_index)]\n", "        if isinstance(tone_entry, dict):  # If the tone entry is an object\n", "            # Extract integer values from the tone object, set to 0 if cast fails\n", "            for i in range(1, 11):\n", "                key = f'color{i}'\n", "                if key in tone_entry:\n", "                    try:\n", "                        colorSwatch.append(int(tone_entry[key]))\n", "                    except (ValueErro<PERSON>, TypeError):\n", "                        colorSwatch.append(0)\n", "    \n", "    # Tags are comma-separated\n", "    tags = tags_string.split(',')\n", "    \n", "    tag_refs = []\n", "    for tag in tags:\n", "        if not tag.strip():  # Skip empty tags\n", "            continue\n", "            \n", "        colon_separated_tag = tag.split(':')\n", "        tag_external_id = colon_separated_tag[0].strip()\n", "        \n", "        # Handle tag_score based on TAG_TYPE and if colon is present\n", "        if TAG_TYPE == \"color\" and len(colon_separated_tag) > 1:\n", "            try:\n", "                tag_score = float(colon_separated_tag[1].strip())\n", "            except (ValueErro<PERSON>, TypeError):\n", "                tag_score = 0.0\n", "        else:\n", "            tag_score = 0.0\n", "        \n", "        tag_ref = {\n", "            \"tagExternalId\": tag_external_id,\n", "            \"tagSource\": \"TAG_SOURCE_GENERATIVE\",\n", "            \"tagScore\": tag_score,\n", "            \"colorSwatch\": colorSwatch\n", "        }\n", "        tag_refs.append(tag_ref)\n", "    \n", "    json_string = json.dumps(tag_refs)\n", "    return json_string\n", "\n", "# Apply the function to each row in the DataFrame\n", "if not df.empty:\n", "    # Only apply the function if the DataFrame has rows\n", "    df['tagRefs'] = df.apply(convert_tags_to_csv, axis=1)\n", "else:\n", "    # For empty DataFrames, just add an empty column\n", "    df['tagRefs'] = pd.Series(dtype='object')"]}, {"cell_type": "code", "execution_count": null, "id": "a69a0ad7", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["df['image'] =  \"=image(\\\"\" + df['image_url'] + \"\\\")\""]}, {"cell_type": "code", "execution_count": null, "id": "f147c097", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["import string\n", "# # The list of words to check\n", "# #words_to_check = ['warm', 'cold']\n", "\n", "## split the custom prompt into individual words ##\n", "# Create a translation table for removing punctuation\n", "#translator = str.maketrans('', '', string.punctuation)\n", "\n", "# Create a translation table that excludes the dash\n", "translator = str.maketrans('', '', string.punctuation.replace('-', ''))\n", "\n", "# Remove punctuation using the translator\n", "clean_sentence = custom_prompt.translate(translator)\n", "\n", "# lower case \n", "lowercase_sentence = clean_sentence.lower()\n", "\n", "# Split the sentence into words\n", "words_to_check = lowercase_sentence.split()\n", "\n", "#words_to_check = [word.strip() for word in str(custom_prompt).split(',')]\n", "set_to_check = set(words_to_check)\n", "\n", "# # Function to check if all words are in the text array for each row\n", "# def check_words_in_row(word):\n", "#     return word in set_to_check\n", "\n", "# # Apply function to each row in the DataFrame\n", "# df['contains_words'] = df['label'].apply(check_words_in_row)\n", "\n", "# View the DataFrame with new column\n", "#print(df)"]}, {"cell_type": "code", "execution_count": null, "id": "b270c54f", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["def check_words_in_row(words, set_to_check):\n", "    # Split the string on commas and strip whitespace\n", "    words = str(words).split(',')\n", "    # Check if any stripped word is in the set\n", "    return any(word.strip() in set_to_check for word in words)\n", "\n", "def apply_check_to_dataframe(df, column_name, set_to_check):\n", "    df['contains_words'] = df[column_name].apply(lambda words: check_words_in_row(words, set_to_check))\n", "    return df\n", "\n", "# Set to check\n", "#set_to_check = {'error', 't-shirt', 'sweater'}\n", "\n", "# Apply the function to the DataFrame\n", "df = apply_check_to_dataframe(df, 'label', set_to_check)"]}, {"cell_type": "code", "execution_count": null, "id": "ad03b2cb", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["def apply_check_to_dataframe(df, column_name, set_to_check):\n", "    df['contains_errors'] = df[column_name].apply(lambda words: check_words_in_row(words, set_to_check))\n", "    return df\n", "\n", "# Set to check\n", "set_to_check_error = {'error', 'Error', 'error processing', 'Error processing'}\n", "\n", "# Apply the function to the DataFrame\n", "df = apply_check_to_dataframe(df, 'label', set_to_check_error)"]}, {"cell_type": "code", "execution_count": null, "id": "4c3e1d13", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["# sorting by two columns \n", "df = df.sort_values(by=['contains_words', 'label'], ascending=[True, True])"]}, {"cell_type": "code", "execution_count": null, "id": "b6f71142", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["dat = {}\n", "for garment_type_upper in selected_garment_types_upper:\n", "    dat[garment_type_upper] = df[df[\"garment_type\"] == garment_type_upper].drop(columns=['tones'])"]}, {"cell_type": "code", "execution_count": null, "id": "bd202f54", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["### Writing DFs to Gsheet ### "]}, {"cell_type": "code", "execution_count": null, "id": "475d7608", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["# Optional: Provide a parent folder ID within the Shared Drive (if applicable)\n", "parent_folder_id = \"1X6UqULcaXLG_RpJXEyGDqdeTyZbi4HHe\"\n", "\n", "# Initialize the class with the Google Sheets file name\n", "sheets_uploader = StylersToGoogleSheets(\n", "    sheet_file_name=f\"Bitmoji Fashion Garment Tags Classification generated at {datetime.now()}\",\n", "    parent_folder_id=parent_folder_id,\n", ")\n", "\n", "# Create dataframes list and sheet names list simultaneously\n", "df_list = []\n", "sheet_names = []\n", "\n", "for garment_type_upper in selected_garment_types_upper:\n", "    if garment_type_upper in dat and not dat[garment_type_upper].empty:\n", "        df_list.append(dat[garment_type_upper])\n", "        sheet_names.append(garment_type_upper.lower())\n", "\n", "if not df_list:\n", "    # If no dataframes are available, skip the export step\n", "    print(\"No data available to export to Google Sheets. Skipping export step.\")\n", "else:\n", "    # Insert the stylers into Google Sheets and retrieve the new spreadsheet ID\n", "    spreadsheet_id = sheets_uploader.export_pandas_to_google_sheets(\n", "        df_list,\n", "        sheet_names,\n", "    )\n", "    gsheet_link = f\"https://docs.google.com/spreadsheets/d/{spreadsheet_id}\"\n", "    display(HTML(f'<a href=\"{gsheet_link}\" target=\"_blank\">\"gsheet output link\"</a>'))"]}, {"cell_type": "markdown", "id": "39338fcd", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["## Upload to GCS Bucket"]}, {"cell_type": "code", "execution_count": null, "id": "5d395574", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["def upload_to_bucket(blob_name, path_to_file, bucket_name):\n", "    print(\n", "        f\"Uploading file '{path_to_file}' to GCS bucket '{bucket_name}' as '{blob_name}'\"\n", "    )\n", "    \"\"\" Upload data to a bucket\"\"\"\n", "    storage_client = storage.Client(project=\"sc-bq-gcs-billingonly\")\n", "\n", "    # print(buckets = list(storage_client.list_buckets())\n", "\n", "    bucket = storage_client.bucket(bucket_name)\n", "    blob = bucket.blob(blob_name)\n", "\n", "    try:\n", "        blob.upload_from_filename(path_to_file)\n", "        print(\n", "            f\"File '{path_to_file}' uploaded to '{blob_name}' in bucket '{bucket_name}' successfully.\"\n", "        )\n", "        return True\n", "    except Exception as e:\n", "        print(f\"Failed to upload '{path_to_file}' to '{blob_name}': {str(e)}\")\n", "        return False"]}, {"cell_type": "code", "execution_count": null, "id": "c630d0d6", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["if UPLOAD_TO_GCS:\n", "    # create a csv file for each of the garment types selected and upload to GCS\n", "    for garment_type in SELECTED_GARMENT_LIST:\n", "        file_name = \"{}_{}_{}_{}.csv\".format(\n", "            # remove the underscore from garment_type because Pistachio's generative tag fetcher uses it as a delimiter when reading the csv's\n", "            TAG_TYPE, garment_type.lower().replace(\"_\", \"\"), husky_version, DATE_NODASH\n", "        )\n", "\n", "        df_temp = df[df[\"garment_type\"] == garment_type.upper()][\n", "            [\"Content ID\", \"tagRefs\", \"image_url\", \"image\"]\n", "        ].copy()\n", "        \n", "        df_temp.to_csv(\"./{}\".format(file_name), index=False)\n", "\n", "        upload_to_bucket(\n", "            \"bitmoji/fashion_classification/{}\".format(file_name),\n", "            file_name,\n", "            \"prod-models\",\n", "        )"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}}, "nbformat": 4, "nbformat_minor": 5}