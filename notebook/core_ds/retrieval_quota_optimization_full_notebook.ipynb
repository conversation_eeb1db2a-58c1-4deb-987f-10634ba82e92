{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"id": "afvQi8tDRS9J"}, "outputs": [], "source": ["from google.cloud import bigquery\n", "import pandas as pd\n", "import numpy as np\n", "from scipy.optimize import curve_fit, least_squares, minimize\n", "import matplotlib.pyplot as plt\n", "from scipy.stats import f\n", "from datetime import datetime, timedelta\n", "import osqp\n", "from scipy import sparse\n", "import pickle\n", "import gc\n", "import os\n", "import itertools"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Parameters and setups"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "ptWKET3NQYa5", "tags": ["parameters"]}, "outputs": [], "source": ["# Parameters\n", "GCLOUD_PROJECT = \"sc-bq-ds-adhoc\"\n", "num_days_replay_data = 7\n", "ds_end = '20250628'\n", "\n", "# define data sources and output tables\n", "\n", "# ## for production\n", "# PROJECT = 'snapads-ranking'\n", "# DATASET = 'retrieval_optimization'\n", "# retention_days = 360\n", "\n", "## for testing\n", "PROJECT = 'sc-bq-gcs-billingonly'\n", "DATASET = 'temp_datascience'\n", "retention_days = 3\n", "\n", "## table names\n", "query_group_daily_summary_table_prefix = 'query_group_daily_summary_'\n", "query_group_summary_table_prefix = 'query_group_summary_'\n", "processed_data_table_prefix = 'temp_auction_replay_processed_'\n", "quadratic_fit_table_prefix = 'q_fit_output_'\n", "quadratic_opt_table_prefix = 'q_opt_output_'\n", "quadratic_opt_k_mapping_table_prefix = 'q_opt_k_mapping_'\n", "retrieval_gbb_model_dist_table_prefix = 'retrieval_gbb_model_dist_'\n", "opt_retrieval_quota_table_prefix = 'opt_retrieval_quota_'\n", "\n", "# fixed data source\n", "hourly_replay_data_prefix = 'snapads-ranking.retrieval_optimization.auction_replay_full_'\n", "query_mapping_table = 'snapads-ranking.retrieval_optimization.query_mapping_20250506'\n", "\n", "# replay revenue column\n", "rev_col = 'revenue_final_bid'\n", "\n", "# Define bounds for k_i and the weighted sum limit L\n", "# lower and upper bounds for each k_i\n", "# total constraint sum(w_i * k_i) <= L\n", "\n", "bounds_combo_list = [\n", "    {\n", "        \"l_bound\": 20,\n", "        \"u_bound\": 400,    \n", "    },\n", "    {\n", "        \"l_bound\": 50,\n", "        \"u_bound\": 300,    \n", "    },\n", "    {\n", "        \"l_bound\": 100,\n", "        \"u_bound\": 250,   \n", "    },\n", "]\n", "\n", "# total constraint sum(w_i * k_i) <= L\n", "L = 175\n", "\n", "T_quota_list = range(1,6)\n", "\n", "# Define the percentile (max_k_p * 5) of max_k to be used in fitting the revenue curve\n", "# e.g. max_k_p = 19 corresponds to 95-percentile\n", "max_k_p = 19\n", "\n", "# we remove requests where revenue are outliers\n", "# outlier defined as percentile rank great a cutoff: 0.99 represent 99%\n", "outlier_cutoff = 0.99\n", "\n", "# retrieval data\n", "# retrieval_data_table_prefix = \"snap-ads-debug.retrieval.data_\"\n", "# auction data\n", "# auction_data_table_prefix = \"ads-forecast.auction_details_prod.prod_auction_details_\""]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "RrA5KIbttaUb"}, "outputs": [], "source": ["processed_data_table = processed_data_table_prefix + ds_end\n", "query_group_summary_table = query_group_summary_table_prefix + ds_end\n", "quadratic_fit_table = quadratic_fit_table_prefix + ds_end\n", "quadratic_opt_table = quadratic_opt_table_prefix + ds_end\n", "quadratic_opt_k_mapping_table = quadratic_opt_k_mapping_table_prefix + ds_end\n", "opt_retrieval_quota_table = opt_retrieval_quota_table_prefix + ds_end"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "ByAuwQjNRNdX"}, "outputs": [], "source": ["# Find ds_start, the beginning of replay data window,\n", "date_end = datetime.strptime(ds_end, \"%Y%m%d\")\n", "date_start = date_end +timed<PERSON>ta(days=1-num_days_replay_data)\n", "ds_start = date_start.strftime(\"%Y%m%d\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["['20250628',\n", " '20250627',\n", " '20250626',\n", " '20250625',\n", " '20250624',\n", " '20250623',\n", " '20250622']"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["ds_list = [(date_end + timedelta(days=-days_diff)).strftime(\"%Y%m%d\") for days_diff in range(num_days_replay_data)]\n", "ds_list"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "SB6QEO2pCwgi"}, "outputs": [], "source": ["client = bigquery.Client(project = GCLOUD_PROJECT)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Data Preparation\n", "#### Daily summary table: \n", "- Number of ad requests and those with revenue in each country-channel-os group, \n", "  among the intersect of 1% retrieval holdout (no quota) and 1% auction data (all \n", "  candidates logged)\n", "- This serves two purposes:\n", "  - To determine the proportion of ad requests with revenue in each bucket, to scale the revenue curve fitted using ad requests with revenue only\n", "  - To determine the proportion of ad requests in each bucket among all buckets"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XU-T05vW3GY8", "outputId": "ba1ffce0-1c26-4c70-f905-e0b6f1f82c9c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Table `sc-bq-gcs-billingonly.temp_datascience.query_group_daily_summary_20250628` generated\n", "Table `sc-bq-gcs-billingonly.temp_datascience.query_group_daily_summary_20250627` generated\n", "Table `sc-bq-gcs-billingonly.temp_datascience.query_group_daily_summary_20250626` generated\n", "Table `sc-bq-gcs-billingonly.temp_datascience.query_group_daily_summary_20250625` generated\n", "Table `sc-bq-gcs-billingonly.temp_datascience.query_group_daily_summary_20250624` generated\n", "Table `sc-bq-gcs-billingonly.temp_datascience.query_group_daily_summary_20250623` generated\n", "Table `sc-bq-gcs-billingonly.temp_datascience.query_group_daily_summary_20250622` generated\n"]}], "source": ["# query to create query group summary\n", "query_group_daily_summary = f\"\"\"\n", "CREATE TABLE IF NOT EXISTS\n", "##CREATE OR REPLACE TABLE\n", "  `{PROJECT}.{DATASET}.{query_group_daily_summary_table_prefix}{{ds}}` \n", "\n", "OPTIONS(expiration_timestamp = TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL {retention_days} DAY)) AS (\n", "  WITH\n", "    t_all_requests AS (\n", "    SELECT\n", "      country,\n", "      distribution_channel,\n", "      platform_type,\n", "      COUNT(DISTINCT retrieval_data.request_id) AS num_all_requests\n", "    FROM (\n", "      SELECT\n", "        request_id,\n", "        country,\n", "        targeting_parameters.platform_type AS platform_type,\n", "      FROM\n", "        `ads-forecast.auction_details_prod.prod_auction_details_*`\n", "      WHERE\n", "        all_participants_logged\n", "      AND LEFT(_table_suffix,8) = '{{ds}}') auction_data\n", "    JOIN\n", "      `snap-ads-debug.retrieval.data_*` retrieval_data\n", "    ON\n", "      auction_data.request_id = retrieval_data.request_id\n", "    WHERE\n", "      _table_suffix = '{{ds}}'\n", "    GROUP BY\n", "      1,\n", "      2,\n", "      3),\n", "    t_rev_requests AS (\n", "    SELECT\n", "      country,\n", "      distribution_channel,\n", "      platform_type,\n", "      COUNT(DISTINCT request_id) AS num_rev_requests\n", "    FROM\n", "      `{hourly_replay_data_prefix}*` replay_data\n", "    WHERE LEFT(_table_suffix,8) = '{{ds}}'\n", "    GROUP BY\n", "      1,\n", "      2,\n", "      3 )\n", "    SELECT\n", "      country,\n", "      distribution_channel,\n", "      platform_type,\n", "      num_all_requests,\n", "      num_rev_requests,\n", "    FROM\n", "      t_rev_requests\n", "    JOIN\n", "      t_all_requests\n", "    USING\n", "      (country,\n", "        distribution_channel,\n", "        platform_type) )\n", "\n", "\"\"\"\n", "\n", "for ds in ds_list:\n", "    query = query_group_daily_summary.format(ds=ds)\n", "    job = client.query(query)\n", "    job.result()\n", "    print(f\"Table `{PROJECT}.{DATASET}.{query_group_daily_summary_table_prefix}{ds}` generated\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Multi-day summary table\n", "- aggregate multiple days to so that the estimates are smoother\n", "- attach the bucket id for processing later"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XU-T05vW3GY8", "outputId": "ba1ffce0-1c26-4c70-f905-e0b6f1f82c9c"}, "outputs": [{"data": {"text/plain": ["<google.cloud.bigquery.table._EmptyRowIterator at 0x7a1d5ca96490>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# query to create query group summary\n", "query_group_summary = f\"\"\"\n", "CREATE OR REPLACE TABLE\n", "  `{PROJECT}.{DATASET}.{query_group_summary_table}`\n", "\n", "OPTIONS(expiration_timestamp = TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL {retention_days} DAY)) AS (\n", "\n", "  WITH \n", "  t_group_summary AS (\n", "    SELECT\n", "      country,\n", "      distribution_channel,\n", "      platform_type,\n", "      IF\n", "      (country IN ('AU','CA','FR','DE','NL',\n", "                   'NO','SA','SE','AE','GB',\n", "                   'US','KW','IN','QA','DK',\n", "                   'OM','BH'), country, 'other') AS grouped_country,\n", "      SUM(num_all_requests) AS num_all_requests,\n", "      SUM(num_rev_requests) AS num_rev_requests,\n", "    FROM\n", "      `{PROJECT}.{DATASET}.{query_group_daily_summary_table_prefix}*`\n", "    WHERE \n", "      _table_suffix between '{ds_start}' and '{ds_end}'\n", "    GROUP BY \n", "      1,2,3,4)\n", "    SELECT\n", "      t_group_summary.*, \n", "      bucket_id,\n", "    FROM\n", "      t_group_summary\n", "    JOIN\n", "      `{query_mapping_table}` mapping\n", "    ON\n", "      t_group_summary.grouped_country = mapping.country\n", "      AND t_group_summary.distribution_channel = mapping.distribution_channel\n", "      AND t_group_summary.platform_type = mapping.platform_type)\n", "\"\"\"\n", "# print(query_group_summary)\n", "job = client.query(query_group_summary)\n", "job.result()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Process the query replay data\n", "- In raw replay data, different ad requests have different range for k (up to the maximum retrieval rank of auction winners, and after that the revenue does not increase)\n", "- We fill/truncate the data so that ad requests in the same bucket have the same range for k (95-th percentile of max winner retrieval rank)\n", "  - We also restricted the largest k between the optimization upper/lower bounds, so that the fitted curve is relevant to the range \n", "- Outliers in revenue would be removed (currently 1%), which is very important since the outliers can significantly change the results and affect curve fitting"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["<google.cloud.bigquery.table._EmptyRowIterator at 0x7a1d461bbfa0>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["query_create_processed_replay_data_table = f\"\"\"\n", "CREATE OR REPLACE TABLE \n", "  `{PROJECT}.{DATASET}.{processed_data_table}` (\n", "    l_bound INT,\n", "    u_bound INT,\n", "    bucket_id INT,\n", "    request_id STRING,\n", "    k INT,\n", "    revenue FLOAT64,\n", "    revenue_final_bid FLOAT64,\n", "    p_rank_max_revenue FLOAT64,\n", "    p_rank_max_revenue_final_bid FLOAT64\n", "  )\n", "  \n", "OPTIONS(expiration_timestamp = TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL {retention_days} DAY))\n", "\"\"\"\n", "\n", "job = client.query(query_create_processed_replay_data_table)\n", "job.result()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "LqLZ8kaPgxms", "outputId": "4dda7b21-52bd-4088-d5ec-1019a93397d4"}, "outputs": [], "source": ["query_process_replay_data = f\"\"\"\n", "INSERT INTO \n", "  `{PROJECT}.{DATASET}.{processed_data_table}` (\n", "    l_bound,\n", "    u_bound,\n", "    bucket_id,\n", "    request_id,\n", "    k,\n", "    revenue,\n", "    revenue_final_bid,\n", "    p_rank_max_revenue,\n", "    p_rank_max_revenue_final_bid\n", "  )\n", "  (\n", "  WITH\n", "    t_max_k AS (\n", "    SELECT\n", "      COALESCE(bucket_id,0) as bucket_id,\n", "      request_id,\n", "      MAX(max_k_of_billed_winners) OVER (PARTITION BY request_id) AS max_k_of_billed_winners,\n", "      ROW_NUMBER() OVER (PARTITION BY request_id ORDER BY k desc) AS rn,\n", "      revenue AS max_revenue,\n", "      revenue_final_bid AS max_revenue_final_bid,\n", "    FROM\n", "      `{hourly_replay_data_prefix}*` replay_data\n", "    LEFT JOIN\n", "      (SELECT country, distribution_channel, platform_type, bucket_id from `{PROJECT}.{DATASET}.{query_group_summary_table}`) bucket_mapping\n", "    USING\n", "      (distribution_channel,\n", "        country,\n", "        platform_type)\n", "    WHERE LEFT(_table_suffix,8) between '{ds_start}' and '{ds_end}'\n", "    QUALIFY\n", "      rn = 1 ),\n", "    t_max_k_cutoffs AS (\n", "    SELECT\n", "      bucket_id,\n", "      APPROX_QUANTILES(max_k_of_billed_winners, 20)[OFFSET({max_k_p})] AS max_k_cutoff,\n", "    FROM\n", "      t_max_k\n", "    GROUP BY\n", "      1 ),\n", "    t_cutoffs AS (\n", "    SELECT\n", "      bucket_id,\n", "      LEAST(GREATEST(max_k_cutoff, {{l_bound}}),{{u_bound}}) AS cutoff\n", "    FROM\n", "      t_max_k_cutoffs ),\n", "    t_max_k_filtered AS (\n", "    SELECT\n", "      t_max_k.*,\n", "      percent_rank() over (partition by bucket_id order by max_revenue) as p_rank_max_revenue,\n", "      percent_rank() over (partition by bucket_id order by max_revenue_final_bid) as p_rank_max_revenue_final_bid,\n", "    FROM\n", "      t_max_k\n", "    ),\n", "    t_k_array AS (\n", "    SELECT\n", "      bucket_id,\n", "      k\n", "    FROM\n", "      t_cutoffs,\n", "      UNNEST(GENERATE_ARRAY(0, cutoff)) AS k),\n", "    t_k_request_array AS (\n", "    SELECT\n", "      t_k_array.bucket_id,\n", "      t_max_k_filtered.request_id,\n", "      t_k_array.k,\n", "      max_revenue,\n", "      max_revenue_final_bid,\n", "      p_rank_max_revenue,\n", "      p_rank_max_revenue_final_bid,\n", "    FROM\n", "      t_k_array\n", "    JOIN\n", "      t_max_k_filtered\n", "    ON\n", "      t_k_array.bucket_id = t_max_k_filtered.bucket_id\n", "    )\n", "  SELECT\n", "    {{l_bound}} as l_bound,\n", "    {{u_bound}} as u_bound,\n", "    t_k_request_array.bucket_id,\n", "    t_k_request_array.request_id,\n", "    t_k_request_array.k,\n", "    COALESCE(revenue, max_revenue) AS revenue,\n", "    COALESCE(revenue_final_bid, max_revenue_final_bid) AS revenue_final_bid,\n", "    p_rank_max_revenue,\n", "    p_rank_max_revenue_final_bid,\n", "  FROM\n", "    t_k_request_array\n", "  LEFT JOIN\n", "    (SELECT\n", "       request_id,\n", "       k,\n", "       revenue,\n", "       revenue_final_bid\n", "     FROM\n", "       `{hourly_replay_data_prefix}*`\n", "     WHERE\n", "       LEFT(_table_suffix,8) between '{ds_start}' and '{ds_end}') replay_data\n", "  ON\n", "    t_k_request_array.request_id = replay_data.request_id\n", "    AND t_k_request_array.k = replay_data.k)\n", "\"\"\"\n", "\n", "for bounds_combo in bounds_combo_list:\n", "    l_bound = bounds_combo['l_bound']\n", "    u_bound = bounds_combo['u_bound']\n", "    \n", "    query = query_process_replay_data.format(l_bound=l_bound,\n", "                                             u_bound=u_bound,)\n", "    job = client.query(query)\n", "    job.result()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Curve fitting\n", "#### Query the data for curve fitting\n", "- For each bucket and optimization upper/lower bounds, we find what is the average revenue at each k"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"id": "jiC10PDsAwYf"}, "outputs": [], "source": ["query_sample = f\"\"\"\n", "SELECT\n", "  l_bound,\n", "  u_bound,\n", "  bucket_id,\n", "  k,\n", "  count(1) as num_requests,\n", "  avg(revenue) as avg_revenue,\n", "  avg(revenue_final_bid) as avg_revenue_final_bid,\n", "  sum(revenue * revenue) - count(1)*pow(avg(revenue),2) as pure_error_revenue,\n", "  sum(revenue_final_bid * revenue_final_bid) - count(1)*pow(avg(revenue_final_bid),2) as pure_error_revenue_final_bid,\n", "FROM\n", "  `{PROJECT}.{DATASET}.{processed_data_table}`\n", "WHERE \n", "  p_rank_max_revenue < {outlier_cutoff}\n", "  AND p_rank_max_revenue_final_bid < {outlier_cutoff}\n", "GROUP BY 1,2,3,4\n", "\"\"\"\n", "\n", "df_sample_all_bounds = client.query(query_sample).to_dataframe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Define helper functions and curve fitting functions"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"id": "hW3mUNzUzrAY"}, "outputs": [], "source": ["def calculate_rss(y_true, y_pred, pure_error, group_sizes):\n", "    return np.sum((y_true - y_pred) ** 2 * group_sizes + pure_error)\n", "\n", "# # Function to calculate R-squared\n", "# def calculate_r_squared(y_true, y_pred):\n", "#     ss_residual = np.sum((y_true - y_pred) ** 2)\n", "#     ss_total = np.sum((y_true - np.mean(y_true)) ** 2)\n", "#     return 1 - (ss_residual / ss_total)\n", "\n", "# Function to calculate lack-of-fit statistics\n", "def calculate_lack_of_fit(y_true, y_pred, pure_error, group_sizes):\n", "    ss_lack_of_fit = np.sum((y_true - y_pred) ** 2 * group_sizes)\n", "    lack_of_fit_df = len(y_true) - 3  # df_lack_of_fit = #unique_x - #parameters\n", "    ss_pure_error = np.sum(pure_error)\n", "    pure_error_df = sum(group_sizes) - len(y_true)\n", "\n", "    lof_stat = (ss_lack_of_fit / lack_of_fit_df) / (np.sum(pure_error) / pure_error_df)\n", "    p_value = 1 - f.cdf(lof_stat, lack_of_fit_df, pure_error_df)\n", "\n", "    return ss_lack_of_fit, np.sum(pure_error), lof_stat, p_value"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"id": "Yi3Fx-x7xCa0"}, "outputs": [], "source": ["# Define the quadratic function: a * (x - b)^2 + c\n", "def quadratic_half(x, a, b, c):\n", "        return a * (x - b)**2 + c\n", "\n", "# Define the function to fit a quadratic curve to the data\n", "def quadratic_fit(x_data,\n", "                  y_data,\n", "                  pure_error,\n", "                  group_sizes,\n", "                  bounds=None,\n", "                  p0=None):\n", "\n", "    if bounds is None:\n", "        bounds=([-np.inf, np.max(x_data), -np.inf], [0, np.inf, np.inf])\n", "\n", "    if p0 is None:\n", "        p0=[-np.max(y_data)/((np.max(x_data))**2), np.max(x_data) + 1, 0]\n", "\n", "    # Fit the constrained quadratic curve\n", "    params, covariance = curve_fit(\n", "        quadratic_half, x_data, y_data, p0=p0, bounds=bounds\n", "    )\n", "\n", "    # Extract fitted parameters for the constrained quadratic model\n", "    a_fit, b_fit, c_fit = params\n", "\n", "    y_pred = quadratic_half(x_data, a_fit, b_fit, c_fit)\n", "\n", "    rss = calculate_rss(y_data, y_pred, pure_error, group_sizes)\n", "\n", "    # r_squared = calculate_r_squared(y_data, y_pred)\n", "\n", "    ss_lack_of_fit, ss_pure_error, lof_stat, p_value = calculate_lack_of_fit(\n", "        y_data, y_pred, pure_error, group_sizes\n", "    )\n", "\n", "    output = {\n", "        'a': a_fit,\n", "        'b': b_fit,\n", "        'c': c_fit,\n", "        # 'r_squared':r_squared,\n", "        'rss':rss,\n", "        'ss_lack_of_fit':ss_lack_of_fit,\n", "        'ss_pure_error':ss_pure_error,\n", "        'lof_stat':lof_stat,\n", "        'p_value':p_value,\n", "    }\n", "\n", "    return output"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def make_residuals(x, y):\n", "    def residuals(params):\n", "        a, b, c = params\n", "        y_pred = quadratic_half(x, a, b, c)\n", "        return (y - y_pred).to_numpy(dtype=float)\n", "    return residuals\n", "\n", "\n", "\n", "def quadratic_fit_3(x_data,\n", "                    y_data,\n", "                    pure_error,\n", "                    group_sizes):\n", "    \n", "    # Build loss function\n", "    residual_fn = make_residuals(x_data, y_data)\n", "    \n", "    # Constraints\n", "    bounds=([-np.inf, np.max(x_data), -np.inf], [0, np.inf, np.inf])\n", "\n", "    # Initial guess\n", "    initial_guess = [-(np.max(y_data)-np.min(y_data))/((np.max(x_data))**2), np.max(x_data), np.max(y_data)]\n", "\n", "    # Optimize\n", "    result = least_squares(residual_fn, initial_guess, bounds=bounds)\n", "\n", "    # Extract fitted parameters for the constrained quadratic model\n", "    a_fit, b_fit, c_fit = result.x\n", "\n", "    y_pred = quadratic_half(x_data, a_fit, b_fit, c_fit)\n", "\n", "    rss = calculate_rss(y_data, y_pred, pure_error, group_sizes)\n", "\n", "    # r_squared = calculate_r_squared(y_data, y_pred)\n", "\n", "    ss_lack_of_fit, ss_pure_error, lof_stat, p_value = calculate_lack_of_fit(\n", "        y_data, y_pred, pure_error, group_sizes\n", "    )\n", "\n", "    output = {\n", "        'a': a_fit,\n", "        'b': b_fit,\n", "        'c': c_fit,\n", "        # 'r_squared':r_squared,\n", "        'rss':rss,\n", "        'ss_lack_of_fit':ss_lack_of_fit,\n", "        'ss_pure_error':ss_pure_error,\n", "        'lof_stat':lof_stat,\n", "        'p_value':p_value,\n", "    }\n", "\n", "    return output"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Perform curve fitting\n", "- Separate fitting for each bucket and each upper/lower bounds combination"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"id": "K15JaDVn23yu"}, "outputs": [], "source": ["quadratic_outputs = []\n", "# exponential_approach_outputs = []\n", "\n", "for bounds_combo in bounds_combo_list:\n", "    l_bound = bounds_combo['l_bound']\n", "    u_bound = bounds_combo['u_bound']\n", "    \n", "    df_sample = df_sample_all_bounds.loc[(df_sample_all_bounds['l_bound']==l_bound)&(df_sample_all_bounds['u_bound']==u_bound)]\n", "    buckets = np.sort(df_sample['bucket_id'].unique())\n", "\n", "    for bucket_id in buckets:\n", "        # print(bucket_id)\n", "\n", "        x_data = df_sample.loc[df_sample['bucket_id']==bucket_id, 'k'].values\n", "        y_data = df_sample.loc[df_sample['bucket_id']==bucket_id, 'avg_'+rev_col].values\n", "        pure_error = df_sample.loc[df_sample['bucket_id']==bucket_id, 'pure_error_'+rev_col].values\n", "        group_sizes = df_sample.loc[df_sample['bucket_id']==bucket_id, 'num_requests'].values\n", "\n", "        q_output = quadratic_fit_3(x_data, y_data, pure_error, group_sizes)\n", "        q_output['bucket_id'] = bucket_id\n", "        q_output['replay_rev_col'] = rev_col\n", "        q_output['l_bound'] = l_bound\n", "        q_output['u_bound'] = u_bound\n", "        quadratic_outputs.append(q_output)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"id": "nFArJsEu4IIC"}, "outputs": [], "source": ["df_q_output = pd.DataFrame(quadratic_outputs)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 423}, "id": "Q-c1vrqM4Hjt", "outputId": "a5b79734-cd7b-420a-f3eb-e50ec48d2163"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>a</th>\n", "      <th>b</th>\n", "      <th>c</th>\n", "      <th>rss</th>\n", "      <th>ss_lack_of_fit</th>\n", "      <th>ss_pure_error</th>\n", "      <th>lof_stat</th>\n", "      <th>p_value</th>\n", "      <th>bucket_id</th>\n", "      <th>replay_rev_col</th>\n", "      <th>l_bound</th>\n", "      <th>u_bound</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>-2.687665e-09</td>\n", "      <td>400.0</td>\n", "      <td>0.002665</td>\n", "      <td>4828.281705</td>\n", "      <td>3.307661</td>\n", "      <td>4824.974044</td>\n", "      <td>270.190936</td>\n", "      <td>1.110223e-16</td>\n", "      <td>0</td>\n", "      <td>revenue_final_bid</td>\n", "      <td>20</td>\n", "      <td>400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>-3.332844e-08</td>\n", "      <td>400.0</td>\n", "      <td>0.027576</td>\n", "      <td>478.754416</td>\n", "      <td>0.350514</td>\n", "      <td>478.403903</td>\n", "      <td>0.236223</td>\n", "      <td>1.000000e+00</td>\n", "      <td>1</td>\n", "      <td>revenue_final_bid</td>\n", "      <td>20</td>\n", "      <td>400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>-2.027414e-07</td>\n", "      <td>192.0</td>\n", "      <td>0.065902</td>\n", "      <td>8279.099976</td>\n", "      <td>2.191248</td>\n", "      <td>8276.908728</td>\n", "      <td>0.188784</td>\n", "      <td>1.000000e+00</td>\n", "      <td>2</td>\n", "      <td>revenue_final_bid</td>\n", "      <td>20</td>\n", "      <td>400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>-5.220561e-09</td>\n", "      <td>400.0</td>\n", "      <td>0.006789</td>\n", "      <td>7.294845</td>\n", "      <td>0.011052</td>\n", "      <td>7.283793</td>\n", "      <td>0.291984</td>\n", "      <td>1.000000e+00</td>\n", "      <td>3</td>\n", "      <td>revenue_final_bid</td>\n", "      <td>20</td>\n", "      <td>400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>-1.328172e-08</td>\n", "      <td>400.0</td>\n", "      <td>0.017596</td>\n", "      <td>441.583352</td>\n", "      <td>0.360169</td>\n", "      <td>441.223182</td>\n", "      <td>1.059357</td>\n", "      <td>1.992024e-01</td>\n", "      <td>4</td>\n", "      <td>revenue_final_bid</td>\n", "      <td>20</td>\n", "      <td>400</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>439</th>\n", "      <td>-7.401887e-08</td>\n", "      <td>250.0</td>\n", "      <td>0.027995</td>\n", "      <td>2548.784679</td>\n", "      <td>4.112480</td>\n", "      <td>2544.672199</td>\n", "      <td>11.409477</td>\n", "      <td>1.110223e-16</td>\n", "      <td>145</td>\n", "      <td>revenue_final_bid</td>\n", "      <td>100</td>\n", "      <td>250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>440</th>\n", "      <td>-7.556311e-09</td>\n", "      <td>250.0</td>\n", "      <td>0.002582</td>\n", "      <td>59.868589</td>\n", "      <td>0.042876</td>\n", "      <td>59.825713</td>\n", "      <td>6.636902</td>\n", "      <td>1.110223e-16</td>\n", "      <td>146</td>\n", "      <td>revenue_final_bid</td>\n", "      <td>100</td>\n", "      <td>250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>441</th>\n", "      <td>-1.894403e-08</td>\n", "      <td>119.0</td>\n", "      <td>0.001895</td>\n", "      <td>7.966228</td>\n", "      <td>0.007499</td>\n", "      <td>7.958728</td>\n", "      <td>5.447760</td>\n", "      <td>1.110223e-16</td>\n", "      <td>147</td>\n", "      <td>revenue_final_bid</td>\n", "      <td>100</td>\n", "      <td>250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>442</th>\n", "      <td>-7.378072e-09</td>\n", "      <td>250.0</td>\n", "      <td>0.003926</td>\n", "      <td>104.863197</td>\n", "      <td>0.119075</td>\n", "      <td>104.744122</td>\n", "      <td>16.040375</td>\n", "      <td>1.110223e-16</td>\n", "      <td>148</td>\n", "      <td>revenue_final_bid</td>\n", "      <td>100</td>\n", "      <td>250</td>\n", "    </tr>\n", "    <tr>\n", "      <th>443</th>\n", "      <td>-1.179991e-08</td>\n", "      <td>100.0</td>\n", "      <td>0.004536</td>\n", "      <td>0.573312</td>\n", "      <td>0.000099</td>\n", "      <td>0.573213</td>\n", "      <td>0.034514</td>\n", "      <td>1.000000e+00</td>\n", "      <td>149</td>\n", "      <td>revenue_final_bid</td>\n", "      <td>100</td>\n", "      <td>250</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>444 rows × 12 columns</p>\n", "</div>"], "text/plain": ["                a      b         c          rss  ss_lack_of_fit  \\\n", "0   -2.687665e-09  400.0  0.002665  4828.281705        3.307661   \n", "1   -3.332844e-08  400.0  0.027576   478.754416        0.350514   \n", "2   -2.027414e-07  192.0  0.065902  8279.099976        2.191248   \n", "3   -5.220561e-09  400.0  0.006789     7.294845        0.011052   \n", "4   -1.328172e-08  400.0  0.017596   441.583352        0.360169   \n", "..            ...    ...       ...          ...             ...   \n", "439 -7.401887e-08  250.0  0.027995  2548.784679        4.112480   \n", "440 -7.556311e-09  250.0  0.002582    59.868589        0.042876   \n", "441 -1.894403e-08  119.0  0.001895     7.966228        0.007499   \n", "442 -7.378072e-09  250.0  0.003926   104.863197        0.119075   \n", "443 -1.179991e-08  100.0  0.004536     0.573312        0.000099   \n", "\n", "     ss_pure_error    lof_stat       p_value  bucket_id     replay_rev_col  \\\n", "0      4824.974044  270.190936  1.110223e-16          0  revenue_final_bid   \n", "1       478.403903    0.236223  1.000000e+00          1  revenue_final_bid   \n", "2      8276.908728    0.188784  1.000000e+00          2  revenue_final_bid   \n", "3         7.283793    0.291984  1.000000e+00          3  revenue_final_bid   \n", "4       441.223182    1.059357  1.992024e-01          4  revenue_final_bid   \n", "..             ...         ...           ...        ...                ...   \n", "439    2544.672199   11.409477  1.110223e-16        145  revenue_final_bid   \n", "440      59.825713    6.636902  1.110223e-16        146  revenue_final_bid   \n", "441       7.958728    5.447760  1.110223e-16        147  revenue_final_bid   \n", "442     104.744122   16.040375  1.110223e-16        148  revenue_final_bid   \n", "443       0.573213    0.034514  1.000000e+00        149  revenue_final_bid   \n", "\n", "     l_bound  u_bound  \n", "0         20      400  \n", "1         20      400  \n", "2         20      400  \n", "3         20      400  \n", "4         20      400  \n", "..       ...      ...  \n", "439      100      250  \n", "440      100      250  \n", "441      100      250  \n", "442      100      250  \n", "443      100      250  \n", "\n", "[444 rows x 12 columns]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["df_q_output"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Save curve fitting outcomes"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "hh3bxBgA4G8j", "outputId": "f4c68a57-c7b4-4f36-fa44-198e6348d8f2"}, "outputs": [{"data": {"text/plain": ["Table(TableReference(DatasetReference('sc-bq-gcs-billingonly', 'temp_datascience'), 'q_fit_output_20250628'))"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["job_config = bigquery.LoadJobConfig(\n", "    write_disposition=\"WRITE_TRUNCATE\",\n", ")\n", "\n", "job_q = client.load_table_from_dataframe(\n", "    df_q_output,\n", "    f\"{PROJECT}.{DATASET}.{quadratic_fit_table}\",\n", "    job_config = job_config,\n", ")\n", "job_q.result()\n", "\n", "# 2) Fetch the newly‐created table\n", "table = client.get_table(f\"{PROJECT}.{DATASET}.{quadratic_fit_table}\")\n", "\n", "# 3) Set its expiration (UTC) to now + retention_days\n", "table.expires = datetime.now() + timedelta(days=retention_days)\n", "\n", "# 4) Push that update\n", "client.update_table(table, [\"expires\"])"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["1586"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["gc.collect()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Optimization"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Load the previously fitted curves"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["query_fitted_curves = f\"\"\"\n", "SELECT\n", "  *\n", "FROM\n", "  `{PROJECT}.{DATASET}.{quadratic_fit_table}`\n", "\"\"\"\n", "\n", "df_fitted_all = client.query(query_fitted_curves).to_dataframe()\n", "df_fitted_all.sort_values(by=['l_bound','u_bound','bucket_id'],inplace=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Additional data for optimization\n", "- The proportion of requests with revenue and bucket weight"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["query_weights = f\"\"\"\n", "WITH\n", "  t_buckets AS (\n", "  SELECT\n", "    bucket_id,\n", "    SUM(num_rev_requests) AS num_rev_requests,\n", "    SUM(num_all_requests) AS num_all_requests,\n", "  FROM\n", "    `{PROJECT}.{DATASET}.{query_group_summary_table}`\n", "  GROUP BY\n", "    1 )\n", "SELECT\n", "  bucket_id,\n", "  SAFE_DIVIDE(num_rev_requests, num_all_requests) AS prop_rev_requests,\n", "  num_all_requests/(SUM(num_all_requests) OVER ()) AS bucket_weight\n", "FROM\n", "  t_buckets\n", "ORDER BY\n", "  1\n", "\"\"\"\n", "\n", "df_weights = client.query(query_weights).to_dataframe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Function for performing optimization\n", "- Input: \n", "  - fitting curves (parameters a, b, c for each bucket)\n", "  - upper and lower bounds for optimization\n", "  - weighted k\n", "  - multiplier to prevent premature convergence due to the scale of the objective function"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["def find_optimal_k(df, l_bound, u_bound, L, multiplier=1E12):\n", "    n = df['bucket_id'].nunique()\n", "\n", "    a = -df['a'].values * df['prop_rev_requests'].values\n", "    b = df['b'].values\n", "    c = -df['c'].values * df['prop_rev_requests'].values\n", "\n", "    w = np.array(df['bucket_weight'].values)\n", "    w = w/np.sum(w)\n", "\n", "    # multiplier = 1E12   \n", "    \n", "    # QP objective: 0.5 * k^T Q k + q^T k\n", "    # where Q is diagonal with elements 2 * w[i] * a[i]\n", "    Q_diag = 2 * w * a * multiplier\n", "    Q = sparse.diags(Q_diag)\n", "\n", "    # q vector: q[i] = -2 * w[i] * a[i] * b[i]\n", "    q = -2 * w * a * b * multiplier\n", "\n", "    # Constraints\n", "    # 1. Weighted sum constraint: sum(w * k) <= L\n", "    #    This gives a row vector A1 = [w_1, w_2, ..., w_n]\n", "    A1 = sparse.csr_matrix(w.reshape(1, -1))\n", "\n", "    # 2. Box constraints: k_i in [l_bound, u_bound]\n", "    A2 = sparse.eye(n)\n", "\n", "    # Stack the constraints:\n", "    # First row: weighted sum; next n rows: box constraints.\n", "    A = sparse.vstack([A1, A2]).tocsc()\n", "\n", "    # Lower and upper bounds for the constraints:\n", "    # For weighted sum: no lower bound (-inf) and an upper bound L.\n", "    # For box constraints: lower bound l_bound and upper bound u_bound for each k_i.\n", "    l_cons = np.hstack([-np.inf, np.full(n, l_bound)])\n", "    u_cons = np.hstack([L, np.full(n, u_bound)])\n", "    \n", "    # --- Set Up and Solve the QP with OSQP ---\n", "    prob = osqp.OSQP()\n", "    prob.setup(P=Q, q=q, A=A, l=l_cons, u=u_cons, verbose=True)\n", "    res = prob.solve()\n", "\n", "    # --- Output the Results ---\n", "    print(f\"Optimization results for bounds: {l_bound}-{u_bound}\")\n", "    print(\"Optimal objective value:\", res.info.obj_val)\n", "    # print(\"Optimal k values:\", res.x)\n", "\n", "    # sanity check whether bound was respected\n", "    print(\"-\"*65)\n", "    print(f\"Bound on total constraint sum(w_i * k_i) is {L}\")\n", "    print(\"Actual value after optimization\", np.sum(res.x * w))\n", "    print(f\"Bounds for each k_i are {l_bound} and {u_bound}\")\n", "    print(f\"Number of k_i's outside of this bound is:\", np.sum((np.round(res.x)<l_bound) & (np.round(res.x)>u_bound)))\n", "\n", "    \n", "    df['fitted_k'] = res.x\n", "    df['fitted_k_round'] = np.round(df['fitted_k']).astype(int)\n", "    df['L']=L\n", "    \n", "    return df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Perform optimization for each combination of upper/lower bounds\n", "- Reasoning: try more conservative range to make sure things run as expected, then gradually move to more aggresive combinations"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-----------------------------------------------------------------\n", "           OSQP v1.0.0  -  Operator Splitting QP Solver\n", "              (c) The OSQP Developer Team\n", "-----------------------------------------------------------------\n", "problem:  variables n = 147, constraints m = 148\n", "          nnz(P) + nnz(A) = 441\n", "settings: algebra = Built-in,\n", "          OSQPInt = 4 bytes, OSQPFloat = 8 bytes,\n", "          linear system solver = QDLDL v0.1.8,\n", "          eps_abs = 1.0e-03, eps_rel = 1.0e-03,\n", "          eps_prim_inf = 1.0e-04, eps_dual_inf = 1.0e-04,\n", "          rho = 1.00e-01 (adaptive: 50 iterations),\n", "          sigma = 1.00e-06, alpha = 1.60, max_iter = 4000\n", "          check_termination: on (interval 25, duality gap: on),\n", "          time_limit: 1.00e+10 sec,\n", "          scaling: on (10 iterations), scaled_termination: off\n", "          warm starting: on, polishing: off, \n", "iter   objective    prim res   dual res   gap        rel kkt    rho         time\n", "   1  -3.3320e+06   2.42e+01   1.80e+07  -5.63e+09   1.80e+07   1.00e-01    1.50e-04s\n", "  50  -2.1217e+08   3.25e-01   4.28e+05  -1.86e+08   4.28e+05   2.52e-03*   1.06e-03s\n", " 100  -5.5421e+08   9.67e-01   1.82e+03  -1.13e+07   1.82e+03   4.07e-04*   1.40e-03s\n", " 125  -5.5596e+08   2.87e-02   8.94e+01  -3.46e+05   8.94e+01   4.07e-04    1.68e-03s\n", "\n", "status:               solved\n", "number of iterations: 125\n", "optimal objective:    -555957227.9265\n", "dual objective:       -555610952.2496\n", "duality gap:          -3.4628e+05\n", "primal-dual integral: 6.1669e+09\n", "run time:             1.99e-03s\n", "optimal rho estimate: 3.47e-05\n", "\n", "Optimization results for bounds: 20-400\n", "Optimal objective value: -555957227.9264517\n", "-----------------------------------------------------------------\n", "Bound on total constraint sum(w_i * k_i) is 175\n", "Actual value after optimization 175.00101757168983\n", "Bounds for each k_i are 20 and 400\n", "Number of k_i's outside of this bound is: 0\n", "-----------------------------------------------------------------\n", "           OSQP v1.0.0  -  Operator Splitting QP Solver\n", "              (c) The OSQP Developer Team\n", "-----------------------------------------------------------------\n", "problem:  variables n = 147, constraints m = 148\n", "          nnz(P) + nnz(A) = 441\n", "settings: algebra = Built-in,\n", "          OSQPInt = 4 bytes, OSQPFloat = 8 bytes,\n", "          linear system solver = QDLDL v0.1.8,\n", "          eps_abs = 1.0e-03, eps_rel = 1.0e-03,\n", "          eps_prim_inf = 1.0e-04, eps_dual_inf = 1.0e-04,\n", "          rho = 1.00e-01 (adaptive: 50 iterations),\n", "          sigma = 1.00e-06, alpha = 1.60, max_iter = 4000\n", "          check_termination: on (interval 25, duality gap: on),\n", "          time_limit: 1.00e+10 sec,\n", "          scaling: on (10 iterations), scaled_termination: off\n", "          warm starting: on, polishing: off, \n", "iter   objective    prim res   dual res   gap        rel kkt    rho         time\n", "   1  -3.0766e+06   5.31e+01   3.40e+07  -4.98e+10   3.40e+07   1.00e-01    1.45e-04s\n", "  50  -4.0852e+08   4.15e-08   4.88e+05  -2.88e+08   4.88e+05   1.00e-06*   1.28e-03s\n", " 100  -6.4945e+08   2.40e+02   1.52e+00  -5.92e+06   2.40e+02   8.77e-05*   1.84e-03s\n", " 125  -6.4140e+08   1.07e-03   3.82e+00   1.03e+03   1.03e+03   8.77e-05    1.94e-03s\n", "\n", "status:               solved\n", "number of iterations: 125\n", "optimal objective:    -641397971.1643\n", "dual objective:       -641398997.7526\n", "duality gap:          1.0266e+03\n", "primal-dual integral: 5.0360e+10\n", "run time:             2.23e-03s\n", "optimal rho estimate: 9.93e-06\n", "\n", "Optimization results for bounds: 50-300\n", "Optimal objective value: -641397971.1643165\n", "-----------------------------------------------------------------\n", "Bound on total constraint sum(w_i * k_i) is 175\n", "Actual value after optimization 174.9999667811541\n", "Bounds for each k_i are 50 and 300\n", "Number of k_i's outside of this bound is: 0\n", "-----------------------------------------------------------------\n", "           OSQP v1.0.0  -  Operator Splitting QP Solver\n", "              (c) The OSQP Developer Team\n", "-----------------------------------------------------------------\n", "problem:  variables n = 147, constraints m = 148\n", "          nnz(P) + nnz(A) = 441\n", "settings: algebra = Built-in,\n", "          OSQPInt = 4 bytes, OSQPFloat = 8 bytes,\n", "          linear system solver = QDLDL v0.1.8,\n", "          eps_abs = 1.0e-03, eps_rel = 1.0e-03,\n", "          eps_prim_inf = 1.0e-04, eps_dual_inf = 1.0e-04,\n", "          rho = 1.00e-01 (adaptive: 50 iterations),\n", "          sigma = 1.00e-06, alpha = 1.60, max_iter = 4000\n", "          check_termination: on (interval 25, duality gap: on),\n", "          time_limit: 1.00e+10 sec,\n", "          scaling: on (10 iterations), scaled_termination: off\n", "          warm starting: on, polishing: off, \n", "iter   objective    prim res   dual res   gap        rel kkt    rho         time\n", "   1  -3.0918e+06   1.02e+02   1.03e+08  -2.34e+11   1.03e+08   1.00e-01    1.41e-04s\n", "  50  -6.3196e+08   1.57e-07   2.41e+05  -2.48e+08   2.41e+05   1.32e-06*   1.27e-03s\n", " 100  -6.9524e+08   8.75e+01   1.32e+00  -2.52e+06   8.75e+01   9.98e-05*   1.59e-03s\n", " 125  -6.9187e+08   6.84e-04   1.98e+00   8.08e+02   8.08e+02   9.98e-05    1.86e-03s\n", "\n", "status:               solved\n", "number of iterations: 125\n", "optimal objective:    -691873198.0962\n", "dual objective:       -691874006.4197\n", "duality gap:          8.0832e+02\n", "primal-dual integral: 2.3490e+11\n", "run time:             2.14e-03s\n", "optimal rho estimate: 1.01e-05\n", "\n", "Optimization results for bounds: 100-250\n", "Optimal objective value: -691873198.0962086\n", "-----------------------------------------------------------------\n", "Bound on total constraint sum(w_i * k_i) is 175\n", "Actual value after optimization 174.99997760102823\n", "Bounds for each k_i are 100 and 250\n", "Number of k_i's outside of this bound is: 0\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.local/lib/python3.8/site-packages/osqp/interface.py:226: UserWarning: Converting sparse P to a CSC matrix. This may take a while...\n", "  warnings.warn('Converting sparse P to a CSC matrix. This may take a while...')\n", "/home/<USER>/.local/lib/python3.8/site-packages/osqp/interface.py:226: UserWarning: Converting sparse P to a CSC matrix. This may take a while...\n", "  warnings.warn('Converting sparse P to a CSC matrix. This may take a while...')\n", "/home/<USER>/.local/lib/python3.8/site-packages/osqp/interface.py:226: UserWarning: Converting sparse P to a CSC matrix. This may take a while...\n", "  warnings.warn('Converting sparse P to a CSC matrix. This may take a while...')\n"]}], "source": ["df_opt_list = []\n", "for bounds_combo in bounds_combo_list:\n", "    l_bound = bounds_combo['l_bound']\n", "    u_bound = bounds_combo['u_bound']\n", "    \n", "    df_fitted = df_fitted_all.loc[(df_fitted_all['l_bound']==l_bound)&(df_fitted_all['u_bound']==u_bound)].copy()\n", "    df_fitted = pd.merge(df_fitted, df_weights, on='bucket_id')\n", "    \n", "    df_opt = find_optimal_k(df_fitted, l_bound, u_bound, L, multiplier=1E12)\n", "    df_opt_list.append(df_opt)\n", "\n", "df_opt_all = pd.concat(df_opt_list)\n", "df_opt_all.reset_index(drop=True, inplace=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Combine with bucket mapping\n", "- contains top 17 countries and all other countries combined into one level"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"data": {"text/plain": ["(288, 4)"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["query_mapping = f\"\"\"\n", "SELECT\n", "  *\n", "FROM\n", "  `{query_mapping_table}` bucket_mapping\n", "\"\"\"\n", "\n", "df_mapping = client.query(query_mapping).to_dataframe()\n", "df_mapping.shape"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>country</th>\n", "      <th>distribution_channel</th>\n", "      <th>platform_type</th>\n", "      <th>bucket_id</th>\n", "      <th>fitted_k_round</th>\n", "      <th>l_bound</th>\n", "      <th>u_bound</th>\n", "      <th>L</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AE</td>\n", "      <td>AUTO_ADVANCE</td>\n", "      <td>android</td>\n", "      <td>1</td>\n", "      <td>369</td>\n", "      <td>20</td>\n", "      <td>400</td>\n", "      <td>175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>AE</td>\n", "      <td>AUTO_ADVANCE</td>\n", "      <td>android</td>\n", "      <td>1</td>\n", "      <td>288</td>\n", "      <td>50</td>\n", "      <td>300</td>\n", "      <td>175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>AE</td>\n", "      <td>AUTO_ADVANCE</td>\n", "      <td>android</td>\n", "      <td>1</td>\n", "      <td>244</td>\n", "      <td>100</td>\n", "      <td>250</td>\n", "      <td>175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>AU</td>\n", "      <td>AUTO_ADVANCE</td>\n", "      <td>android</td>\n", "      <td>2</td>\n", "      <td>187</td>\n", "      <td>20</td>\n", "      <td>400</td>\n", "      <td>175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>AU</td>\n", "      <td>AUTO_ADVANCE</td>\n", "      <td>android</td>\n", "      <td>2</td>\n", "      <td>188</td>\n", "      <td>50</td>\n", "      <td>300</td>\n", "      <td>175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>859</th>\n", "      <td>US</td>\n", "      <td>INTERSTITIAL_SPOTLIGHT</td>\n", "      <td>web</td>\n", "      <td>149</td>\n", "      <td>50</td>\n", "      <td>50</td>\n", "      <td>300</td>\n", "      <td>175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>860</th>\n", "      <td>US</td>\n", "      <td>INTERSTITIAL_SPOTLIGHT</td>\n", "      <td>web</td>\n", "      <td>149</td>\n", "      <td>100</td>\n", "      <td>100</td>\n", "      <td>250</td>\n", "      <td>175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>861</th>\n", "      <td>other</td>\n", "      <td>INTERSTITIAL_SPOTLIGHT</td>\n", "      <td>web</td>\n", "      <td>149</td>\n", "      <td>20</td>\n", "      <td>20</td>\n", "      <td>400</td>\n", "      <td>175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>862</th>\n", "      <td>other</td>\n", "      <td>INTERSTITIAL_SPOTLIGHT</td>\n", "      <td>web</td>\n", "      <td>149</td>\n", "      <td>50</td>\n", "      <td>50</td>\n", "      <td>300</td>\n", "      <td>175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>863</th>\n", "      <td>other</td>\n", "      <td>INTERSTITIAL_SPOTLIGHT</td>\n", "      <td>web</td>\n", "      <td>149</td>\n", "      <td>100</td>\n", "      <td>100</td>\n", "      <td>250</td>\n", "      <td>175</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>864 rows × 8 columns</p>\n", "</div>"], "text/plain": ["    country    distribution_channel platform_type  bucket_id  fitted_k_round  \\\n", "0        AE            AUTO_ADVANCE       android          1             369   \n", "1        AE            AUTO_ADVANCE       android          1             288   \n", "2        AE            AUTO_ADVANCE       android          1             244   \n", "3        AU            AUTO_ADVANCE       android          2             187   \n", "4        AU            AUTO_ADVANCE       android          2             188   \n", "..      ...                     ...           ...        ...             ...   \n", "859      US  INTERSTITIAL_SPOTLIGHT           web        149              50   \n", "860      US  INTERSTITIAL_SPOTLIGHT           web        149             100   \n", "861   other  INTERSTITIAL_SPOTLIGHT           web        149              20   \n", "862   other  INTERSTITIAL_SPOTLIGHT           web        149              50   \n", "863   other  INTERSTITIAL_SPOTLIGHT           web        149             100   \n", "\n", "     l_bound  u_bound    L  \n", "0         20      400  175  \n", "1         50      300  175  \n", "2        100      250  175  \n", "3         20      400  175  \n", "4         50      300  175  \n", "..       ...      ...  ...  \n", "859       50      300  175  \n", "860      100      250  175  \n", "861       20      400  175  \n", "862       50      300  175  \n", "863      100      250  175  \n", "\n", "[864 rows x 8 columns]"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["df_opt_k_mapping = pd.merge(df_mapping, df_opt_all[['bucket_id','fitted_k_round','l_bound','u_bound','L']],on='bucket_id')\n", "df_opt_k_mapping"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Map to a more granular level\n", "- map to each country-channel-os type bucket, including each individual countries"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>country</th>\n", "      <th>distribution_channel</th>\n", "      <th>platform_type</th>\n", "      <th>bucket_id</th>\n", "      <th>fitted_k_round</th>\n", "      <th>l_bound</th>\n", "      <th>u_bound</th>\n", "      <th>L</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AE</td>\n", "      <td>AUTO_ADVANCE</td>\n", "      <td>android</td>\n", "      <td>1</td>\n", "      <td>369</td>\n", "      <td>20</td>\n", "      <td>400</td>\n", "      <td>175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>AE</td>\n", "      <td>AUTO_ADVANCE</td>\n", "      <td>android</td>\n", "      <td>1</td>\n", "      <td>288</td>\n", "      <td>50</td>\n", "      <td>300</td>\n", "      <td>175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>AE</td>\n", "      <td>AUTO_ADVANCE</td>\n", "      <td>android</td>\n", "      <td>1</td>\n", "      <td>244</td>\n", "      <td>100</td>\n", "      <td>250</td>\n", "      <td>175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>AE</td>\n", "      <td>CONTENT_INTERSTITIAL</td>\n", "      <td>android</td>\n", "      <td>20</td>\n", "      <td>340</td>\n", "      <td>20</td>\n", "      <td>400</td>\n", "      <td>175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>AE</td>\n", "      <td>CONTENT_INTERSTITIAL</td>\n", "      <td>android</td>\n", "      <td>20</td>\n", "      <td>277</td>\n", "      <td>50</td>\n", "      <td>300</td>\n", "      <td>175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2131</th>\n", "      <td>HK</td>\n", "      <td>INTERSTITIAL_SPOTLIGHT</td>\n", "      <td>ios</td>\n", "      <td>146</td>\n", "      <td>104</td>\n", "      <td>50</td>\n", "      <td>300</td>\n", "      <td>175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2132</th>\n", "      <td>HK</td>\n", "      <td>INTERSTITIAL_SPOTLIGHT</td>\n", "      <td>ios</td>\n", "      <td>146</td>\n", "      <td>140</td>\n", "      <td>100</td>\n", "      <td>250</td>\n", "      <td>175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2133</th>\n", "      <td>CR</td>\n", "      <td>INTERSTITIAL_SPOTLIGHT</td>\n", "      <td>ios</td>\n", "      <td>146</td>\n", "      <td>24</td>\n", "      <td>20</td>\n", "      <td>400</td>\n", "      <td>175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2134</th>\n", "      <td>CR</td>\n", "      <td>INTERSTITIAL_SPOTLIGHT</td>\n", "      <td>ios</td>\n", "      <td>146</td>\n", "      <td>104</td>\n", "      <td>50</td>\n", "      <td>300</td>\n", "      <td>175</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2135</th>\n", "      <td>CR</td>\n", "      <td>INTERSTITIAL_SPOTLIGHT</td>\n", "      <td>ios</td>\n", "      <td>146</td>\n", "      <td>140</td>\n", "      <td>100</td>\n", "      <td>250</td>\n", "      <td>175</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2136 rows × 8 columns</p>\n", "</div>"], "text/plain": ["     country    distribution_channel platform_type  bucket_id  fitted_k_round  \\\n", "0         AE            AUTO_ADVANCE       android          1             369   \n", "1         AE            AUTO_ADVANCE       android          1             288   \n", "2         AE            AUTO_ADVANCE       android          1             244   \n", "3         AE    CONTENT_INTERSTITIAL       android         20             340   \n", "4         AE    CONTENT_INTERSTITIAL       android         20             277   \n", "...      ...                     ...           ...        ...             ...   \n", "2131      HK  INTERSTITIAL_SPOTLIGHT           ios        146             104   \n", "2132      HK  INTERSTITIAL_SPOTLIGHT           ios        146             140   \n", "2133      CR  INTERSTITIAL_SPOTLIGHT           ios        146              24   \n", "2134      CR  INTERSTITIAL_SPOTLIGHT           ios        146             104   \n", "2135      CR  INTERSTITIAL_SPOTLIGHT           ios        146             140   \n", "\n", "      l_bound  u_bound    L  \n", "0          20      400  175  \n", "1          50      300  175  \n", "2         100      250  175  \n", "3          20      400  175  \n", "4          50      300  175  \n", "...       ...      ...  ...  \n", "2131       50      300  175  \n", "2132      100      250  175  \n", "2133       20      400  175  \n", "2134       50      300  175  \n", "2135      100      250  175  \n", "\n", "[2136 rows x 8 columns]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["query_mapping_full = f\"\"\"\n", "SELECT\n", "  country,\n", "  distribution_channel,\n", "  platform_type,\n", "  bucket_id\n", "from `{PROJECT}.{DATASET}.{query_group_summary_table}`\n", "\"\"\"\n", "\n", "df_mapping_full = client.query(query_mapping_full).to_dataframe()\n", "df_opt_k_mapping_full = pd.merge(df_mapping_full, df_opt_all[['bucket_id','fitted_k_round','l_bound','u_bound','L']],on='bucket_id')\n", "df_opt_k_mapping_full"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Output results"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["Table(TableReference(DatasetReference('sc-bq-gcs-billingonly', 'temp_datascience'), 'q_opt_output_20250628'))"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["opt_output_table = f\"{PROJECT}.{DATASET}.{quadratic_opt_table}\"\n", "\n", "job_config = bigquery.LoadJobConfig(\n", "    write_disposition=\"WRITE_TRUNCATE\",\n", ")\n", "\n", "job_q = client.load_table_from_dataframe(\n", "    df_opt_all,\n", "    opt_output_table,\n", "    job_config = job_config,\n", ")\n", "job_q.result()\n", "\n", "\n", "# 2) Fetch the newly‐created table\n", "table = client.get_table(opt_output_table)\n", "\n", "# 3) Set its expiration (UTC) to now + retention_days\n", "table.expires = datetime.now() + timedelta(days=retention_days)\n", "\n", "# 4) Push that update\n", "client.update_table(table, [\"expires\"])"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/plain": ["Table(TableReference(DatasetReference('sc-bq-gcs-billingonly', 'temp_datascience'), 'q_opt_k_mapping_20250628'))"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["opt_output_k_mapping_table = f\"{PROJECT}.{DATASET}.{quadratic_opt_k_mapping_table}\"\n", "\n", "job_config = bigquery.LoadJobConfig(\n", "    write_disposition=\"WRITE_TRUNCATE\",\n", ")\n", "\n", "job_q = client.load_table_from_dataframe(\n", "    df_opt_k_mapping_full,\n", "    opt_output_k_mapping_table,\n", "    job_config = job_config,\n", ")\n", "job_q.result()\n", "\n", "# 2) Fetch the newly‐created table\n", "table = client.get_table(opt_output_k_mapping_table)\n", "\n", "# 3) Set its expiration (UTC) to now + retention_days\n", "table.expires = datetime.now() + timedelta(days=retention_days)\n", "\n", "# 4) Push that update\n", "client.update_table(table, [\"expires\"])"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["2074"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["gc.collect()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Purpose of Notebook\n", "- current retrievel parameters include an overall top-k, as well as [quota on different gbb and certain models](https://github.sc-corp.net/Snapchat/configment/blob/e81ea5f7ad523aaed7679cff2e07a47af080589e/configment/ad_publisher/budget_ab/budgetab_helper.py#L2592)\n", "- We used the proportion of retrieval candidates in the gbbs and models of interest to help determine these quotas\n", "- Some of these proportions tend to be quite high (e.g. Pixel Purchases), making the quotas too far from their current levels. We used a temperature parameter in softmax calculation to smooth the distribution."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Obtain the proportion of retrieval candidates in the GBBs and models"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["query_retrieval_gbb_model_dist = f\"\"\"\n", "CREATE TABLE IF NOT EXISTS\n", "##CREATE OR REPLACE TABLE\n", "  `{PROJECT}.{DATASET}.{retrieval_gbb_model_dist_table_prefix}{{ds}}`\n", "  \n", "OPTIONS(expiration_timestamp = TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL {retention_days} DAY)) AS (\n", "\n", "  WITH\n", "    joined_requests AS (\n", "    SELECT\n", "      request_id,\n", "      distribution_channel,\n", "      country,\n", "      platform_type,\n", "      participants_info\n", "    FROM (\n", "      SELECT\n", "        request_id,\n", "        distribution_channel,\n", "        participants_info\n", "      FROM\n", "        `snap-ads-debug.retrieval.data_{{ds}}`) retrieval\n", "    JOIN (\n", "      SELECT\n", "        request_id,\n", "        country,\n", "        targeting_parameters.platform_type\n", "      FROM\n", "        `ads-forecast.auction_data_daily_view.prod_auction_details_daily_view_{{ds}}`\n", "      WHERE\n", "        inventory_type IN ('AUTO_ADVANCE',\n", "          'CHAT_FEED',\n", "          'CONTENT_INTERSTITIAL',\n", "          'DISCOVER',\n", "          'INTERSTITIAL_SPOTLIGHT',\n", "          'INSTREAM_SPOTLIGHT') ) auction\n", "    USING\n", "      (request_id))\n", "  SELECT\n", "    distribution_channel,\n", "    country,\n", "    platform_type,\n", "    p.optimization_goal,\n", "    p.chosen_ad.eav_model_type as model_type,\n", "    COUNT(1) AS num_line_items\n", "  FROM\n", "    joined_requests,\n", "    UNNEST(participants_info) p\n", "  WHERE\n", "    p.status = 'SCORED'\n", "  GROUP BY\n", "    1,\n", "    2,\n", "    3,\n", "    4,\n", "    5)\n", "\"\"\"\n"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["job_config = bigquery.QueryJobConfig(priority=bigquery.QueryPriority.BATCH)\n", "jobs = []\n", "for ds in ds_list:\n", "    job = client.query(query_retrieval_gbb_model_dist.format(ds=ds),  job_config=job_config)\n", "    jobs.append(job)"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["for job in jobs: \n", "    job.result()"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["query_gbb_model_prop = f\"\"\"\n", "WITH\n", "  t_gbb_model_count AS (\n", "  SELECT\n", "    mapping.bucket_id,\n", "    optimization_goal,\n", "    model_type,\n", "    SUM(num_line_items) AS num_line_items\n", "  FROM\n", "    `{PROJECT}.{DATASET}.{{gbb_model_dist_table}}*` dist\n", "  JOIN\n", "    (select distinct\n", "       distribution_channel, \n", "       country, \n", "       platform_type, \n", "       bucket_id,\n", "     from `{PROJECT}.{DATASET}.{quadratic_opt_k_mapping_table}`) mapping\n", "  ON\n", "    dist.distribution_channel = mapping.distribution_channel\n", "    AND dist.country = mapping.country\n", "    AND dist.platform_type = mapping.platform_type\n", "  WHERE\n", "    dist._table_suffix BETWEEN '{ds_start}' AND '{ds_end}'\n", "  GROUP BY\n", "    1,\n", "    2,\n", "    3),\n", "  t_total AS (\n", "  SELECT\n", "    bucket_id,\n", "    SUM(num_line_items) AS num_line_items_total\n", "  FROM\n", "    t_gbb_model_count\n", "  GROUP BY\n", "    1 )\n", "SELECT\n", "  t_gbb_model_count.*,\n", "  t_gbb_model_count.num_line_items/t_total.num_line_items_total AS prop_gbb_model\n", "FROM\n", "  t_gbb_model_count\n", "JOIN\n", "  t_total\n", "USING\n", "  (bucket_id)\n", "\"\"\"\n", "\n", "df_retrieval_prop = client.query(query_gbb_model_prop.format(gbb_model_dist_table=retrieval_gbb_model_dist_table_prefix)).to_dataframe()"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["df_opt_k_mapping_full = client.query(f\"SELECT * FROM `{PROJECT}.{DATASET}.{quadratic_opt_k_mapping_table}`\").to_dataframe()"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["df_opt_results = client.query(f'SELECT * FROM `{PROJECT}.{DATASET}.{quadratic_opt_table}`').to_dataframe()\n", "df_query_mapping = client.query(f'SELECT * FROM `{query_mapping_table}`').to_dataframe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Calculate the per-GBB and per-model quota"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Define how to identify GBBs and models of interest"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [], "source": ["conditions_gbb = {\n", "    'swipe': ['SWIPES'],\n", "    'landing_page_view': ['LANDING_PAGE_VIEW'],\n", "    'impressions': ['IMPRESSIONS'],\n", "    'install': ['APP_INSTALLS'],\n", "    'pixel_signup': ['PIXEL_SIGNUP'],\n", "    'pixel_addcart': ['PIXEL_ADD_TO_CART'],\n", "    'pixel_pageview': ['PIXEL_PAGE_VIEW'],\n", "    'app_purchase': ['APP_PURCHASE'],\n", "    'app_signup': ['APP_SIGNUP'],\n", "    'app_addcart': ['APP_ADD_TO_CART'],\n", "    'video_views_15_sec': ['VIDEO_VIEWS_15_SEC'],\n", "    'video_views': ['VIDEO_VIEWS']\n", "}\n", "\n", "conditions_model = {\n", "    'pixel_purchase': ['PIXEL_PURCHASE','LAT_PIXEL_PURCHASE'],\n", "    'lat_pixel_purchase': ['PIXEL_PURCHASE','LAT_PIXEL_PURCHASE'],\n", "    'pixel_purchase_7_0': ['PIXEL_PURCHASE_7_0','LAT_PIXEL_PURCHASE_7_0'],\n", "    'lat_pixel_purchase_7_0': ['PIXEL_PURCHASE_7_0','LAT_PIXEL_PURCHASE_7_0'],\n", "    'amazon_swipe': ['AMAZON_SWIPES'],\n", "    'hce_swipe': ['HCE_SWIPE'],    \n", "}\n"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["\n", "considered_gbbs = list(itertools.chain.from_iterable(conditions_gbb.values()))\n", "considered_gbbs\n", "\n", "gbb_cols = list(conditions_gbb.keys())\n", "gbb_cols.append('other_gbbs')\n", "model_cols = list(conditions_model.keys())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Current quota for comparison purposes"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>country</th>\n", "      <th>distribution_channel</th>\n", "      <th>platform_type</th>\n", "      <th>group_display_name</th>\n", "      <th>bucket_id</th>\n", "      <th>swipe</th>\n", "      <th>landing_page_view</th>\n", "      <th>impressions</th>\n", "      <th>install</th>\n", "      <th>pixel_signup</th>\n", "      <th>...</th>\n", "      <th>app_addcart</th>\n", "      <th>video_views_15_sec</th>\n", "      <th>video_views</th>\n", "      <th>other_gbbs</th>\n", "      <th>pixel_purchase</th>\n", "      <th>lat_pixel_purchase</th>\n", "      <th>pixel_purchase_7_0</th>\n", "      <th>lat_pixel_purchase_7_0</th>\n", "      <th>amazon_swipe</th>\n", "      <th>hce_swipe</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>all</td>\n", "      <td>all</td>\n", "      <td>all</td>\n", "      <td>current</td>\n", "      <td>NaN</td>\n", "      <td>18</td>\n", "      <td>14</td>\n", "      <td>16</td>\n", "      <td>30</td>\n", "      <td>9</td>\n", "      <td>...</td>\n", "      <td>3</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>27</td>\n", "      <td>34</td>\n", "      <td>34</td>\n", "      <td>48</td>\n", "      <td>48</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 24 columns</p>\n", "</div>"], "text/plain": ["  country distribution_channel platform_type group_display_name  bucket_id  \\\n", "0     all                  all           all            current        NaN   \n", "\n", "   swipe  landing_page_view  impressions  install  pixel_signup  ...  \\\n", "0     18                 14           16       30             9  ...   \n", "\n", "   app_addcart  video_views_15_sec  video_views  other_gbbs  pixel_purchase  \\\n", "0            3                   4            4          27              34   \n", "\n", "   lat_pixel_purchase  pixel_purchase_7_0  lat_pixel_purchase_7_0  \\\n", "0                  34                  48                      48   \n", "\n", "   amazon_swipe  hce_swipe  \n", "0             3          1  \n", "\n", "[1 rows x 24 columns]"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["df_current_quota = pd.DataFrame.from_records([{\n", "    'country':'all',\n", "    'distribution_channel':'all',\n", "    'platform_type':'all',\n", "    'group_display_name':'current',\n", "    'bucket_id': np.nan,\n", "#     'fitted_k_round':175,\n", "    'swipe':18,\n", "    'landing_page_view':14,\n", "    'impressions':16,\n", "    'install':30,\n", "    'pixel_signup':9,\n", "    'pixel_addcart':10,\n", "    'pixel_pageview':6,\n", "    'app_purchase':30,\n", "    'app_signup':4,\n", "    'app_addcart':3,\n", "    'video_views_15_sec':4,\n", "    'video_views':4,\n", "    'other_gbbs':27,\n", "    'pixel_purchase':34,\n", "    'lat_pixel_purchase':34,\n", "    'pixel_purchase_7_0':48,\n", "    'lat_pixel_purchase_7_0':48,\n", "    'amazon_swipe':3,\n", "    'hce_swipe':1,\n", "}])\n", "df_current_quota"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>country</th>\n", "      <th>distribution_channel</th>\n", "      <th>platform_type</th>\n", "      <th>group_display_name</th>\n", "      <th>bucket_id</th>\n", "      <th>swipe</th>\n", "      <th>landing_page_view</th>\n", "      <th>impressions</th>\n", "      <th>install</th>\n", "      <th>pixel_signup</th>\n", "      <th>...</th>\n", "      <th>app_addcart</th>\n", "      <th>video_views_15_sec</th>\n", "      <th>video_views</th>\n", "      <th>other_gbbs</th>\n", "      <th>pixel_purchase</th>\n", "      <th>lat_pixel_purchase</th>\n", "      <th>pixel_purchase_7_0</th>\n", "      <th>lat_pixel_purchase_7_0</th>\n", "      <th>amazon_swipe</th>\n", "      <th>hce_swipe</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>all</td>\n", "      <td>all</td>\n", "      <td>all</td>\n", "      <td>current</td>\n", "      <td>NaN</td>\n", "      <td>0.102857</td>\n", "      <td>0.08</td>\n", "      <td>0.091429</td>\n", "      <td>0.171429</td>\n", "      <td>0.051429</td>\n", "      <td>...</td>\n", "      <td>0.017143</td>\n", "      <td>0.022857</td>\n", "      <td>0.022857</td>\n", "      <td>0.154286</td>\n", "      <td>0.194286</td>\n", "      <td>0.194286</td>\n", "      <td>0.274286</td>\n", "      <td>0.274286</td>\n", "      <td>0.017143</td>\n", "      <td>0.005714</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1 rows × 24 columns</p>\n", "</div>"], "text/plain": ["  country distribution_channel platform_type group_display_name  bucket_id  \\\n", "0     all                  all           all            current        NaN   \n", "\n", "      swipe  landing_page_view  impressions   install  pixel_signup  ...  \\\n", "0  0.102857               0.08     0.091429  0.171429      0.051429  ...   \n", "\n", "   app_addcart  video_views_15_sec  video_views  other_gbbs  pixel_purchase  \\\n", "0     0.017143            0.022857     0.022857    0.154286        0.194286   \n", "\n", "   lat_pixel_purchase  pixel_purchase_7_0  lat_pixel_purchase_7_0  \\\n", "0            0.194286            0.274286                0.274286   \n", "\n", "   amazon_swipe  hce_swipe  \n", "0      0.017143   0.005714  \n", "\n", "[1 rows x 24 columns]"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["df_current_quota_prop = df_current_quota.copy()\n", "df_current_quota_prop[gbb_cols+model_cols]=df_current_quota_prop[gbb_cols+model_cols]/175\n", "df_current_quota_prop"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["def filter_and_sum(df, name, filter_col, filter_val, groupby_col, value_col):\n", "    return df.loc[np.isin(df[filter_col], filter_val)].groupby(groupby_col)[value_col].sum().to_frame(name=name)\n", "\n", "def exclude_and_sum(df, name, filter_col, exclude_val, groupby_col, value_col):\n", "    return df.loc[~np.isin(df[filter_col], exclude_val)].groupby(groupby_col)[value_col].sum().to_frame(name=name)"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["df_lines_prop = df_retrieval_prop.copy()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Getting temperature-tuned quotas\n", "#### Step 1: For each bucket, find the number of retrieval candidates under each GBB/model of interest"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["retrieval_results = []\n", "groupby_col='bucket_id'\n", "value_col='num_line_items'\n", "\n", "for name in conditions_gbb.keys():\n", "    filter_col='optimization_goal'\n", "    filter_value = conditions_gbb[name]\n", "    result = filter_and_sum(df=df_lines_prop,\n", "                            name=name,\n", "                            filter_col=filter_col,\n", "                            filter_val=filter_value,\n", "                            groupby_col=groupby_col,\n", "                            value_col=value_col)\n", "    retrieval_results.append(result)\n", "\n", "result = exclude_and_sum(df=df_lines_prop,\n", "                         name='other_gbbs',\n", "                         filter_col='optimization_goal',\n", "                         exclude_val=considered_gbbs,\n", "                         groupby_col=groupby_col,\n", "                         value_col=value_col)\n", "retrieval_results.append(result)\n", "    \n", "for name in conditions_model.keys():\n", "    filter_col='model_type'\n", "    filter_value = conditions_model[name]\n", "    result = filter_and_sum(df=df_lines_prop,\n", "                            name=name,\n", "                            filter_col=filter_col,\n", "                            filter_val=filter_value,\n", "                            groupby_col=groupby_col,\n", "                            value_col=value_col)\n", "    retrieval_results.append(result)\n", "\n", "df_gbb_model_counts = pd.concat(retrieval_results, axis=1).fillna(1).reset_index()"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>bucket_id</th>\n", "      <th>swipe</th>\n", "      <th>landing_page_view</th>\n", "      <th>impressions</th>\n", "      <th>install</th>\n", "      <th>pixel_signup</th>\n", "      <th>pixel_addcart</th>\n", "      <th>pixel_pageview</th>\n", "      <th>app_purchase</th>\n", "      <th>app_signup</th>\n", "      <th>...</th>\n", "      <th>video_views_15_sec</th>\n", "      <th>video_views</th>\n", "      <th>other_gbbs</th>\n", "      <th>pixel_purchase</th>\n", "      <th>lat_pixel_purchase</th>\n", "      <th>pixel_purchase_7_0</th>\n", "      <th>lat_pixel_purchase_7_0</th>\n", "      <th>amazon_swipe</th>\n", "      <th>hce_swipe</th>\n", "      <th>total</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>46691750</td>\n", "      <td>4307443</td>\n", "      <td>10262517</td>\n", "      <td>1606568</td>\n", "      <td>2352385</td>\n", "      <td>3594617</td>\n", "      <td>742171</td>\n", "      <td>1868580</td>\n", "      <td>100542</td>\n", "      <td>...</td>\n", "      <td>1139198</td>\n", "      <td>6442474</td>\n", "      <td>141718104</td>\n", "      <td>34580925</td>\n", "      <td>34580925</td>\n", "      <td>101022258</td>\n", "      <td>101022258</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2.208264e+08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>21812538</td>\n", "      <td>2588567</td>\n", "      <td>4784926</td>\n", "      <td>2837434</td>\n", "      <td>1869277</td>\n", "      <td>189081</td>\n", "      <td>3712558</td>\n", "      <td>6526930</td>\n", "      <td>91683</td>\n", "      <td>...</td>\n", "      <td>1603876</td>\n", "      <td>1805088</td>\n", "      <td>148295626</td>\n", "      <td>55397831</td>\n", "      <td>55397831</td>\n", "      <td>92879508</td>\n", "      <td>92879508</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1.961176e+08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>10103666</td>\n", "      <td>504773</td>\n", "      <td>1270413</td>\n", "      <td>327190</td>\n", "      <td>185289</td>\n", "      <td>458824</td>\n", "      <td>118143</td>\n", "      <td>437176</td>\n", "      <td>35839</td>\n", "      <td>...</td>\n", "      <td>89660</td>\n", "      <td>888602</td>\n", "      <td>28927390</td>\n", "      <td>6522850</td>\n", "      <td>6522850</td>\n", "      <td>22188078</td>\n", "      <td>22188078</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>4.334697e+07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>52557998</td>\n", "      <td>5446293</td>\n", "      <td>19562947</td>\n", "      <td>4260723</td>\n", "      <td>5355758</td>\n", "      <td>19137700</td>\n", "      <td>11808048</td>\n", "      <td>9212422</td>\n", "      <td>226251</td>\n", "      <td>...</td>\n", "      <td>2522134</td>\n", "      <td>5726559</td>\n", "      <td>378981600</td>\n", "      <td>87319891</td>\n", "      <td>87319891</td>\n", "      <td>291365549</td>\n", "      <td>291365549</td>\n", "      <td>1</td>\n", "      <td>40943</td>\n", "      <td>5.147984e+08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>23895225</td>\n", "      <td>2039279</td>\n", "      <td>12777373</td>\n", "      <td>2096771</td>\n", "      <td>1033274</td>\n", "      <td>1632752</td>\n", "      <td>1039295</td>\n", "      <td>5317039</td>\n", "      <td>126402</td>\n", "      <td>...</td>\n", "      <td>1447603</td>\n", "      <td>2560468</td>\n", "      <td>149034227</td>\n", "      <td>19747430</td>\n", "      <td>19747430</td>\n", "      <td>129245122</td>\n", "      <td>129245122</td>\n", "      <td>1</td>\n", "      <td>44820</td>\n", "      <td>2.029997e+08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>142</th>\n", "      <td>145</td>\n", "      <td>2294280065</td>\n", "      <td>158248985</td>\n", "      <td>1075635003</td>\n", "      <td>316333541</td>\n", "      <td>237220284</td>\n", "      <td>86108736</td>\n", "      <td>266143192</td>\n", "      <td>299132963</td>\n", "      <td>4625887</td>\n", "      <td>...</td>\n", "      <td>82726858</td>\n", "      <td>398402697</td>\n", "      <td>5821549380</td>\n", "      <td>2640308720</td>\n", "      <td>2640308720</td>\n", "      <td>3168736714</td>\n", "      <td>3168736714</td>\n", "      <td>10329059</td>\n", "      <td>4953920</td>\n", "      <td>1.105264e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>143</th>\n", "      <td>146</td>\n", "      <td>321072669</td>\n", "      <td>5783697</td>\n", "      <td>39458386</td>\n", "      <td>21797680</td>\n", "      <td>3049608</td>\n", "      <td>33456908</td>\n", "      <td>3804621</td>\n", "      <td>34510266</td>\n", "      <td>223415</td>\n", "      <td>...</td>\n", "      <td>2132836</td>\n", "      <td>24284986</td>\n", "      <td>544183565</td>\n", "      <td>72010430</td>\n", "      <td>72010430</td>\n", "      <td>471319420</td>\n", "      <td>471319420</td>\n", "      <td>91</td>\n", "      <td>17106</td>\n", "      <td>1.033769e+09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>144</th>\n", "      <td>147</td>\n", "      <td>478914095</td>\n", "      <td>834682139</td>\n", "      <td>182806018</td>\n", "      <td>41503687</td>\n", "      <td>63704403</td>\n", "      <td>40005998</td>\n", "      <td>33585290</td>\n", "      <td>30356631</td>\n", "      <td>2620257</td>\n", "      <td>...</td>\n", "      <td>17484731</td>\n", "      <td>63039546</td>\n", "      <td>2302703340</td>\n", "      <td>779114484</td>\n", "      <td>779114484</td>\n", "      <td>1483405420</td>\n", "      <td>1483405420</td>\n", "      <td>249249</td>\n", "      <td>228819</td>\n", "      <td>4.092159e+09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>145</th>\n", "      <td>148</td>\n", "      <td>1923388622</td>\n", "      <td>453663412</td>\n", "      <td>706596904</td>\n", "      <td>364625844</td>\n", "      <td>303105107</td>\n", "      <td>141675905</td>\n", "      <td>150027539</td>\n", "      <td>243331319</td>\n", "      <td>8525011</td>\n", "      <td>...</td>\n", "      <td>68485181</td>\n", "      <td>271221078</td>\n", "      <td>10561069358</td>\n", "      <td>4036863752</td>\n", "      <td>4036863752</td>\n", "      <td>6307004265</td>\n", "      <td>6307004265</td>\n", "      <td>1145322</td>\n", "      <td>1626390</td>\n", "      <td>1.520267e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>146</th>\n", "      <td>149</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1350823</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1.350835e+06</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>147 rows × 21 columns</p>\n", "</div>"], "text/plain": ["     bucket_id       swipe  landing_page_view  impressions    install  \\\n", "0            1    46691750            4307443     10262517    1606568   \n", "1            2    21812538            2588567      4784926    2837434   \n", "2            3    10103666             504773      1270413     327190   \n", "3            4    52557998            5446293     19562947    4260723   \n", "4            5    23895225            2039279     12777373    2096771   \n", "..         ...         ...                ...          ...        ...   \n", "142        145  2294280065          158248985   1075635003  316333541   \n", "143        146   321072669            5783697     39458386   21797680   \n", "144        147   478914095          834682139    182806018   41503687   \n", "145        148  1923388622          453663412    706596904  364625844   \n", "146        149           1                  1      1350823          1   \n", "\n", "     pixel_signup  pixel_addcart  pixel_pageview  app_purchase  app_signup  \\\n", "0         2352385        3594617          742171       1868580      100542   \n", "1         1869277         189081         3712558       6526930       91683   \n", "2          185289         458824          118143        437176       35839   \n", "3         5355758       19137700        11808048       9212422      226251   \n", "4         1033274        1632752         1039295       5317039      126402   \n", "..            ...            ...             ...           ...         ...   \n", "142     237220284       86108736       266143192     299132963     4625887   \n", "143       3049608       33456908         3804621      34510266      223415   \n", "144      63704403       40005998        33585290      30356631     2620257   \n", "145     303105107      141675905       150027539     243331319     8525011   \n", "146             1              1               1             1           1   \n", "\n", "     ...  video_views_15_sec  video_views   other_gbbs  pixel_purchase  \\\n", "0    ...             1139198      6442474    141718104        34580925   \n", "1    ...             1603876      1805088    148295626        55397831   \n", "2    ...               89660       888602     28927390         6522850   \n", "3    ...             2522134      5726559    378981600        87319891   \n", "4    ...             1447603      2560468    149034227        19747430   \n", "..   ...                 ...          ...          ...             ...   \n", "142  ...            82726858    398402697   5821549380      2640308720   \n", "143  ...             2132836     24284986    544183565        72010430   \n", "144  ...            17484731     63039546   2302703340       779114484   \n", "145  ...            68485181    271221078  10561069358      4036863752   \n", "146  ...                   1            1            1               1   \n", "\n", "     lat_pixel_purchase  pixel_purchase_7_0  lat_pixel_purchase_7_0  \\\n", "0              34580925           101022258               101022258   \n", "1              55397831            92879508                92879508   \n", "2               6522850            22188078                22188078   \n", "3              87319891           291365549               291365549   \n", "4              19747430           129245122               129245122   \n", "..                  ...                 ...                     ...   \n", "142          2640308720          3168736714              3168736714   \n", "143            72010430           471319420               471319420   \n", "144           779114484          1483405420              1483405420   \n", "145          4036863752          6307004265              6307004265   \n", "146                   1                   1                       1   \n", "\n", "     amazon_swipe  hce_swipe         total  \n", "0               1          1  2.208264e+08  \n", "1               1          1  1.961176e+08  \n", "2               1          1  4.334697e+07  \n", "3               1      40943  5.147984e+08  \n", "4               1      44820  2.029997e+08  \n", "..            ...        ...           ...  \n", "142      10329059    4953920  1.105264e+10  \n", "143            91      17106  1.033769e+09  \n", "144        249249     228819  4.092159e+09  \n", "145       1145322    1626390  1.520267e+10  \n", "146             1          1  1.350835e+06  \n", "\n", "[147 rows x 21 columns]"]}, "execution_count": 43, "metadata": {}, "output_type": "execute_result"}], "source": ["# df_gbb_model_counts = pd.merge(df_gbb_model_counts, df_k_mapping, on='bucket_id')\n", "\n", "df_gbb_model_counts['total'] = df_gbb_model_counts[gbb_cols].sum(axis=1)\n", "df_gbb_model_counts"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Step 2: For each bucket, find the proportion of retrieval candidates under each GBB/model of interest"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>bucket_id</th>\n", "      <th>swipe</th>\n", "      <th>landing_page_view</th>\n", "      <th>impressions</th>\n", "      <th>install</th>\n", "      <th>pixel_signup</th>\n", "      <th>pixel_addcart</th>\n", "      <th>pixel_pageview</th>\n", "      <th>app_purchase</th>\n", "      <th>app_signup</th>\n", "      <th>...</th>\n", "      <th>video_views_15_sec</th>\n", "      <th>video_views</th>\n", "      <th>other_gbbs</th>\n", "      <th>pixel_purchase</th>\n", "      <th>lat_pixel_purchase</th>\n", "      <th>pixel_purchase_7_0</th>\n", "      <th>lat_pixel_purchase_7_0</th>\n", "      <th>amazon_swipe</th>\n", "      <th>hce_swipe</th>\n", "      <th>total</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>0.211441</td>\n", "      <td>0.019506</td>\n", "      <td>0.046473</td>\n", "      <td>0.007275</td>\n", "      <td>0.010653</td>\n", "      <td>0.016278</td>\n", "      <td>0.003361</td>\n", "      <td>0.008462</td>\n", "      <td>0.000455</td>\n", "      <td>...</td>\n", "      <td>0.005159</td>\n", "      <td>0.029174</td>\n", "      <td>0.641763</td>\n", "      <td>0.156598</td>\n", "      <td>0.156598</td>\n", "      <td>0.457474</td>\n", "      <td>0.457474</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>2.208264e+08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>0.111222</td>\n", "      <td>0.013199</td>\n", "      <td>0.024398</td>\n", "      <td>0.014468</td>\n", "      <td>0.009531</td>\n", "      <td>0.000964</td>\n", "      <td>0.01893</td>\n", "      <td>0.033281</td>\n", "      <td>0.000467</td>\n", "      <td>...</td>\n", "      <td>0.008178</td>\n", "      <td>0.009204</td>\n", "      <td>0.756157</td>\n", "      <td>0.282473</td>\n", "      <td>0.282473</td>\n", "      <td>0.473591</td>\n", "      <td>0.473591</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>1.961176e+08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>0.233088</td>\n", "      <td>0.011645</td>\n", "      <td>0.029308</td>\n", "      <td>0.007548</td>\n", "      <td>0.004275</td>\n", "      <td>0.010585</td>\n", "      <td>0.002726</td>\n", "      <td>0.010086</td>\n", "      <td>0.000827</td>\n", "      <td>...</td>\n", "      <td>0.002068</td>\n", "      <td>0.0205</td>\n", "      <td>0.667345</td>\n", "      <td>0.15048</td>\n", "      <td>0.15048</td>\n", "      <td>0.511872</td>\n", "      <td>0.511872</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>4.334697e+07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>0.102094</td>\n", "      <td>0.010579</td>\n", "      <td>0.038001</td>\n", "      <td>0.008276</td>\n", "      <td>0.010404</td>\n", "      <td>0.037175</td>\n", "      <td>0.022937</td>\n", "      <td>0.017895</td>\n", "      <td>0.000439</td>\n", "      <td>...</td>\n", "      <td>0.004899</td>\n", "      <td>0.011124</td>\n", "      <td>0.736175</td>\n", "      <td>0.16962</td>\n", "      <td>0.16962</td>\n", "      <td>0.56598</td>\n", "      <td>0.56598</td>\n", "      <td>0.0</td>\n", "      <td>0.00008</td>\n", "      <td>5.147984e+08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>0.117711</td>\n", "      <td>0.010046</td>\n", "      <td>0.062943</td>\n", "      <td>0.010329</td>\n", "      <td>0.00509</td>\n", "      <td>0.008043</td>\n", "      <td>0.00512</td>\n", "      <td>0.026192</td>\n", "      <td>0.000623</td>\n", "      <td>...</td>\n", "      <td>0.007131</td>\n", "      <td>0.012613</td>\n", "      <td>0.73416</td>\n", "      <td>0.097278</td>\n", "      <td>0.097278</td>\n", "      <td>0.636676</td>\n", "      <td>0.636676</td>\n", "      <td>0.0</td>\n", "      <td>0.000221</td>\n", "      <td>2.029997e+08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>142</th>\n", "      <td>145</td>\n", "      <td>0.207578</td>\n", "      <td>0.014318</td>\n", "      <td>0.097319</td>\n", "      <td>0.028621</td>\n", "      <td>0.021463</td>\n", "      <td>0.007791</td>\n", "      <td>0.02408</td>\n", "      <td>0.027064</td>\n", "      <td>0.000419</td>\n", "      <td>...</td>\n", "      <td>0.007485</td>\n", "      <td>0.036046</td>\n", "      <td>0.526711</td>\n", "      <td>0.238885</td>\n", "      <td>0.238885</td>\n", "      <td>0.286695</td>\n", "      <td>0.286695</td>\n", "      <td>0.000935</td>\n", "      <td>0.000448</td>\n", "      <td>1.105264e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>143</th>\n", "      <td>146</td>\n", "      <td>0.310585</td>\n", "      <td>0.005595</td>\n", "      <td>0.038169</td>\n", "      <td>0.021086</td>\n", "      <td>0.00295</td>\n", "      <td>0.032364</td>\n", "      <td>0.00368</td>\n", "      <td>0.033383</td>\n", "      <td>0.000216</td>\n", "      <td>...</td>\n", "      <td>0.002063</td>\n", "      <td>0.023492</td>\n", "      <td>0.526408</td>\n", "      <td>0.069658</td>\n", "      <td>0.069658</td>\n", "      <td>0.455924</td>\n", "      <td>0.455924</td>\n", "      <td>0.0</td>\n", "      <td>0.000017</td>\n", "      <td>1.033769e+09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>144</th>\n", "      <td>147</td>\n", "      <td>0.117032</td>\n", "      <td>0.203971</td>\n", "      <td>0.044672</td>\n", "      <td>0.010142</td>\n", "      <td>0.015567</td>\n", "      <td>0.009776</td>\n", "      <td>0.008207</td>\n", "      <td>0.007418</td>\n", "      <td>0.00064</td>\n", "      <td>...</td>\n", "      <td>0.004273</td>\n", "      <td>0.015405</td>\n", "      <td>0.562711</td>\n", "      <td>0.190392</td>\n", "      <td>0.190392</td>\n", "      <td>0.362499</td>\n", "      <td>0.362499</td>\n", "      <td>0.000061</td>\n", "      <td>0.000056</td>\n", "      <td>4.092159e+09</td>\n", "    </tr>\n", "    <tr>\n", "      <th>145</th>\n", "      <td>148</td>\n", "      <td>0.126517</td>\n", "      <td>0.029841</td>\n", "      <td>0.046478</td>\n", "      <td>0.023984</td>\n", "      <td>0.019938</td>\n", "      <td>0.009319</td>\n", "      <td>0.009869</td>\n", "      <td>0.016006</td>\n", "      <td>0.000561</td>\n", "      <td>...</td>\n", "      <td>0.004505</td>\n", "      <td>0.01784</td>\n", "      <td>0.694685</td>\n", "      <td>0.265537</td>\n", "      <td>0.265537</td>\n", "      <td>0.414862</td>\n", "      <td>0.414862</td>\n", "      <td>0.000075</td>\n", "      <td>0.000107</td>\n", "      <td>1.520267e+10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>146</th>\n", "      <td>149</td>\n", "      <td>0.000001</td>\n", "      <td>0.000001</td>\n", "      <td>0.999991</td>\n", "      <td>0.000001</td>\n", "      <td>0.000001</td>\n", "      <td>0.000001</td>\n", "      <td>0.000001</td>\n", "      <td>0.000001</td>\n", "      <td>0.000001</td>\n", "      <td>...</td>\n", "      <td>0.000001</td>\n", "      <td>0.000001</td>\n", "      <td>0.000001</td>\n", "      <td>0.000001</td>\n", "      <td>0.000001</td>\n", "      <td>0.000001</td>\n", "      <td>0.000001</td>\n", "      <td>0.000001</td>\n", "      <td>0.000001</td>\n", "      <td>1.350835e+06</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>147 rows × 21 columns</p>\n", "</div>"], "text/plain": ["     bucket_id     swipe  landing_page_view  impressions   install  \\\n", "0            1  0.211441           0.019506     0.046473  0.007275   \n", "1            2  0.111222           0.013199     0.024398  0.014468   \n", "2            3  0.233088           0.011645     0.029308  0.007548   \n", "3            4  0.102094           0.010579     0.038001  0.008276   \n", "4            5  0.117711           0.010046     0.062943  0.010329   \n", "..         ...       ...                ...          ...       ...   \n", "142        145  0.207578           0.014318     0.097319  0.028621   \n", "143        146  0.310585           0.005595     0.038169  0.021086   \n", "144        147  0.117032           0.203971     0.044672  0.010142   \n", "145        148  0.126517           0.029841     0.046478  0.023984   \n", "146        149  0.000001           0.000001     0.999991  0.000001   \n", "\n", "     pixel_signup  pixel_addcart  pixel_pageview  app_purchase  app_signup  \\\n", "0        0.010653       0.016278        0.003361      0.008462    0.000455   \n", "1        0.009531       0.000964         0.01893      0.033281    0.000467   \n", "2        0.004275       0.010585        0.002726      0.010086    0.000827   \n", "3        0.010404       0.037175        0.022937      0.017895    0.000439   \n", "4         0.00509       0.008043         0.00512      0.026192    0.000623   \n", "..            ...            ...             ...           ...         ...   \n", "142      0.021463       0.007791         0.02408      0.027064    0.000419   \n", "143       0.00295       0.032364         0.00368      0.033383    0.000216   \n", "144      0.015567       0.009776        0.008207      0.007418     0.00064   \n", "145      0.019938       0.009319        0.009869      0.016006    0.000561   \n", "146      0.000001       0.000001        0.000001      0.000001    0.000001   \n", "\n", "     ...  video_views_15_sec  video_views  other_gbbs  pixel_purchase  \\\n", "0    ...            0.005159     0.029174    0.641763        0.156598   \n", "1    ...            0.008178     0.009204    0.756157        0.282473   \n", "2    ...            0.002068       0.0205    0.667345         0.15048   \n", "3    ...            0.004899     0.011124    0.736175         0.16962   \n", "4    ...            0.007131     0.012613     0.73416        0.097278   \n", "..   ...                 ...          ...         ...             ...   \n", "142  ...            0.007485     0.036046    0.526711        0.238885   \n", "143  ...            0.002063     0.023492    0.526408        0.069658   \n", "144  ...            0.004273     0.015405    0.562711        0.190392   \n", "145  ...            0.004505      0.01784    0.694685        0.265537   \n", "146  ...            0.000001     0.000001    0.000001        0.000001   \n", "\n", "     lat_pixel_purchase  pixel_purchase_7_0  lat_pixel_purchase_7_0  \\\n", "0              0.156598            0.457474                0.457474   \n", "1              0.282473            0.473591                0.473591   \n", "2               0.15048            0.511872                0.511872   \n", "3               0.16962             0.56598                 0.56598   \n", "4              0.097278            0.636676                0.636676   \n", "..                  ...                 ...                     ...   \n", "142            0.238885            0.286695                0.286695   \n", "143            0.069658            0.455924                0.455924   \n", "144            0.190392            0.362499                0.362499   \n", "145            0.265537            0.414862                0.414862   \n", "146            0.000001            0.000001                0.000001   \n", "\n", "     amazon_swipe  hce_swipe         total  \n", "0             0.0        0.0  2.208264e+08  \n", "1             0.0        0.0  1.961176e+08  \n", "2             0.0        0.0  4.334697e+07  \n", "3             0.0    0.00008  5.147984e+08  \n", "4             0.0   0.000221  2.029997e+08  \n", "..            ...        ...           ...  \n", "142      0.000935   0.000448  1.105264e+10  \n", "143           0.0   0.000017  1.033769e+09  \n", "144      0.000061   0.000056  4.092159e+09  \n", "145      0.000075   0.000107  1.520267e+10  \n", "146      0.000001   0.000001  1.350835e+06  \n", "\n", "[147 rows x 21 columns]"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["df_gbb_model_props = df_gbb_model_counts.copy()\n", "for col in (gbb_cols + model_cols):\n", "    df_gbb_model_props[col] = df_gbb_model_props[col] / df_gbb_model_props['total']\n", "df_gbb_model_props"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Step 3: Taking logarithm of the above proportions to get correponding z"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["df_gbb_model_z = df_gbb_model_props.copy()\n", "df_gbb_model_z[gbb_cols + model_cols] = np.log(df_gbb_model_z[gbb_cols + model_cols])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Step 4: Get the temperature tuned proportions and get the corresponding quota\n", "- $p_{i,T}=\\exp(z_i/T)/\\sum_k \\exp(z_k/T)$\n", "- Intuition:\n", "  - When T=1, $p_{i,T}=p_i$\n", "  - When T<1, $p_{i,T}$'s will be more concentrated\n", "  - When T>1, $p_{i,T}$'s will be more dispersed/smoother"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["def prop_with_temperature(df_z, cols_denom, cols_numer, T):\n", "    temp = df_z.copy()\n", "    temp['denom'] = np.sum(np.exp(temp[cols_denom]/T), axis=1)\n", "    for col in cols_numer:\n", "        temp[col] = np.exp(temp[col]/T)/temp['denom']\n", "    \n", "    return temp"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["dict_prop_with_temperature = {}\n", "dict_prop_to_plot = {}\n", "list_prop_to_plot = []\n", "dict_quotas_query_mapping = {}\n", "list_quotas_query_mapping = []\n", "\n", "T_list = T_quota_list\n", "\n", "for T in T_list:\n", "    df_prop_with_temperature = (\n", "        prop_with_temperature(\n", "            df_z=df_gbb_model_z,\n", "            cols_denom=gbb_cols,\n", "            cols_numer=(gbb_cols + model_cols),\n", "            T=T\n", "        )\n", "    )\n", "    \n", "    df_prop_with_temperature['T'] = T\n", "    dict_prop_with_temperature[T] = df_prop_with_temperature\n", "    \n", "    df_quotas_query_mapping = df_prop_with_temperature.copy()\n", "    df_quotas_query_mapping = pd.merge(df_opt_k_mapping_full, df_quotas_query_mapping, on='bucket_id')\n", "    df_quotas_query_mapping[gbb_cols + model_cols] = np.ceil(df_quotas_query_mapping[gbb_cols + model_cols].mul(df_quotas_query_mapping['fitted_k_round'],axis=0)).astype(int)\n", "    dict_quotas_query_mapping[T] = df_quotas_query_mapping\n", "    list_quotas_query_mapping.append(df_quotas_query_mapping)\n", "    \n", "#     df_prop_to_plot = pd.merge(df_prop_with_temperature, top10_buckets, on='bucket_id')\n", "#     df_prop_to_plot['group_display_name'] = df_prop_to_plot.apply(lambda x: f\"{x.country}-{x.distribution_channel}-{x.platform_type}\", axis=1)\n", "#     df_prop_to_plot.sort_values(by='bucket_weight', ascending=False, inplace=True)\n", "#     list_prop_to_plot.append(df_prop_to_plot)\n", "    \n", "#     df_prop_to_plot = pd.concat([df_prop_to_plot, df_current_quota_prop])\n", "#     dict_prop_to_plot[T] = df_prop_to_plot\n", "\n", "df_quotas_query_mapping_combined = pd.concat(list_quotas_query_mapping)"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>country</th>\n", "      <th>distribution_channel</th>\n", "      <th>platform_type</th>\n", "      <th>bucket_id</th>\n", "      <th>fitted_k_round</th>\n", "      <th>l_bound</th>\n", "      <th>u_bound</th>\n", "      <th>L</th>\n", "      <th>swipe</th>\n", "      <th>landing_page_view</th>\n", "      <th>...</th>\n", "      <th>other_gbbs</th>\n", "      <th>pixel_purchase</th>\n", "      <th>lat_pixel_purchase</th>\n", "      <th>pixel_purchase_7_0</th>\n", "      <th>lat_pixel_purchase_7_0</th>\n", "      <th>amazon_swipe</th>\n", "      <th>hce_swipe</th>\n", "      <th>total</th>\n", "      <th>denom</th>\n", "      <th>T</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AE</td>\n", "      <td>AUTO_ADVANCE</td>\n", "      <td>android</td>\n", "      <td>1</td>\n", "      <td>244</td>\n", "      <td>100</td>\n", "      <td>250</td>\n", "      <td>175</td>\n", "      <td>52</td>\n", "      <td>5</td>\n", "      <td>...</td>\n", "      <td>157</td>\n", "      <td>39</td>\n", "      <td>39</td>\n", "      <td>112</td>\n", "      <td>112</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>220826350.0</td>\n", "      <td>1.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>AE</td>\n", "      <td>AUTO_ADVANCE</td>\n", "      <td>android</td>\n", "      <td>1</td>\n", "      <td>288</td>\n", "      <td>50</td>\n", "      <td>300</td>\n", "      <td>175</td>\n", "      <td>61</td>\n", "      <td>6</td>\n", "      <td>...</td>\n", "      <td>185</td>\n", "      <td>46</td>\n", "      <td>46</td>\n", "      <td>132</td>\n", "      <td>132</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>220826350.0</td>\n", "      <td>1.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>AE</td>\n", "      <td>AUTO_ADVANCE</td>\n", "      <td>android</td>\n", "      <td>1</td>\n", "      <td>369</td>\n", "      <td>20</td>\n", "      <td>400</td>\n", "      <td>175</td>\n", "      <td>79</td>\n", "      <td>8</td>\n", "      <td>...</td>\n", "      <td>237</td>\n", "      <td>58</td>\n", "      <td>58</td>\n", "      <td>169</td>\n", "      <td>169</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>220826350.0</td>\n", "      <td>1.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>AE</td>\n", "      <td>CONTENT_INTERSTITIAL</td>\n", "      <td>android</td>\n", "      <td>20</td>\n", "      <td>237</td>\n", "      <td>100</td>\n", "      <td>250</td>\n", "      <td>175</td>\n", "      <td>48</td>\n", "      <td>5</td>\n", "      <td>...</td>\n", "      <td>155</td>\n", "      <td>39</td>\n", "      <td>39</td>\n", "      <td>110</td>\n", "      <td>110</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>232706999.0</td>\n", "      <td>1.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>AE</td>\n", "      <td>CONTENT_INTERSTITIAL</td>\n", "      <td>android</td>\n", "      <td>20</td>\n", "      <td>277</td>\n", "      <td>50</td>\n", "      <td>300</td>\n", "      <td>175</td>\n", "      <td>56</td>\n", "      <td>6</td>\n", "      <td>...</td>\n", "      <td>181</td>\n", "      <td>45</td>\n", "      <td>45</td>\n", "      <td>128</td>\n", "      <td>128</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>232706999.0</td>\n", "      <td>1.000000</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2131</th>\n", "      <td>SI</td>\n", "      <td>AUTO_ADVANCE</td>\n", "      <td>web</td>\n", "      <td>149</td>\n", "      <td>20</td>\n", "      <td>20</td>\n", "      <td>400</td>\n", "      <td>175</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1350835.0</td>\n", "      <td>1.712951</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2132</th>\n", "      <td>KE</td>\n", "      <td>AUTO_ADVANCE</td>\n", "      <td>web</td>\n", "      <td>149</td>\n", "      <td>20</td>\n", "      <td>20</td>\n", "      <td>400</td>\n", "      <td>175</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1350835.0</td>\n", "      <td>1.712951</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2133</th>\n", "      <td>BE</td>\n", "      <td>AUTO_ADVANCE</td>\n", "      <td>web</td>\n", "      <td>149</td>\n", "      <td>20</td>\n", "      <td>20</td>\n", "      <td>400</td>\n", "      <td>175</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1350835.0</td>\n", "      <td>1.712951</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2134</th>\n", "      <td>NG</td>\n", "      <td>AUTO_ADVANCE</td>\n", "      <td>web</td>\n", "      <td>149</td>\n", "      <td>20</td>\n", "      <td>20</td>\n", "      <td>400</td>\n", "      <td>175</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1350835.0</td>\n", "      <td>1.712951</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2135</th>\n", "      <td>TR</td>\n", "      <td>AUTO_ADVANCE</td>\n", "      <td>web</td>\n", "      <td>149</td>\n", "      <td>20</td>\n", "      <td>20</td>\n", "      <td>400</td>\n", "      <td>175</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1350835.0</td>\n", "      <td>1.712951</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10680 rows × 30 columns</p>\n", "</div>"], "text/plain": ["     country  distribution_channel platform_type  bucket_id  fitted_k_round  \\\n", "0         AE          AUTO_ADVANCE       android          1             244   \n", "1         AE          AUTO_ADVANCE       android          1             288   \n", "2         AE          AUTO_ADVANCE       android          1             369   \n", "3         AE  CONTENT_INTERSTITIAL       android         20             237   \n", "4         AE  CONTENT_INTERSTITIAL       android         20             277   \n", "...      ...                   ...           ...        ...             ...   \n", "2131      SI          AUTO_ADVANCE           web        149              20   \n", "2132      KE          AUTO_ADVANCE           web        149              20   \n", "2133      BE          AUTO_ADVANCE           web        149              20   \n", "2134      NG          AUTO_ADVANCE           web        149              20   \n", "2135      TR          AUTO_ADVANCE           web        149              20   \n", "\n", "      l_bound  u_bound    L  swipe  landing_page_view  ...  other_gbbs  \\\n", "0         100      250  175     52                  5  ...         157   \n", "1          50      300  175     61                  6  ...         185   \n", "2          20      400  175     79                  8  ...         237   \n", "3         100      250  175     48                  5  ...         155   \n", "4          50      300  175     56                  6  ...         181   \n", "...       ...      ...  ...    ...                ...  ...         ...   \n", "2131       20      400  175      1                  1  ...           1   \n", "2132       20      400  175      1                  1  ...           1   \n", "2133       20      400  175      1                  1  ...           1   \n", "2134       20      400  175      1                  1  ...           1   \n", "2135       20      400  175      1                  1  ...           1   \n", "\n", "      pixel_purchase  lat_pixel_purchase  pixel_purchase_7_0  \\\n", "0                 39                  39                 112   \n", "1                 46                  46                 132   \n", "2                 58                  58                 169   \n", "3                 39                  39                 110   \n", "4                 45                  45                 128   \n", "...              ...                 ...                 ...   \n", "2131               1                   1                   1   \n", "2132               1                   1                   1   \n", "2133               1                   1                   1   \n", "2134               1                   1                   1   \n", "2135               1                   1                   1   \n", "\n", "      lat_pixel_purchase_7_0  amazon_swipe  hce_swipe        total     denom  \\\n", "0                        112             1          1  220826350.0  1.000000   \n", "1                        132             1          1  220826350.0  1.000000   \n", "2                        169             1          1  220826350.0  1.000000   \n", "3                        110             1          1  232706999.0  1.000000   \n", "4                        128             1          1  232706999.0  1.000000   \n", "...                      ...           ...        ...          ...       ...   \n", "2131                       1             1          1    1350835.0  1.712951   \n", "2132                       1             1          1    1350835.0  1.712951   \n", "2133                       1             1          1    1350835.0  1.712951   \n", "2134                       1             1          1    1350835.0  1.712951   \n", "2135                       1             1          1    1350835.0  1.712951   \n", "\n", "      T  \n", "0     1  \n", "1     1  \n", "2     1  \n", "3     1  \n", "4     1  \n", "...  ..  \n", "2131  5  \n", "2132  5  \n", "2133  5  \n", "2134  5  \n", "2135  5  \n", "\n", "[10680 rows x 30 columns]"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["df_quotas_query_mapping_combined"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Output results"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [], "source": ["df_quotas_query_mapping_combined.drop(columns=['total','denom','other_gbbs'], inplace=True)\n", "df_quotas_query_mapping_combined.rename(columns={'fitted_k_round':'top_k'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>country</th>\n", "      <th>distribution_channel</th>\n", "      <th>platform_type</th>\n", "      <th>bucket_id</th>\n", "      <th>top_k</th>\n", "      <th>l_bound</th>\n", "      <th>u_bound</th>\n", "      <th>L</th>\n", "      <th>swipe</th>\n", "      <th>landing_page_view</th>\n", "      <th>...</th>\n", "      <th>app_addcart</th>\n", "      <th>video_views_15_sec</th>\n", "      <th>video_views</th>\n", "      <th>pixel_purchase</th>\n", "      <th>lat_pixel_purchase</th>\n", "      <th>pixel_purchase_7_0</th>\n", "      <th>lat_pixel_purchase_7_0</th>\n", "      <th>amazon_swipe</th>\n", "      <th>hce_swipe</th>\n", "      <th>T</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AE</td>\n", "      <td>AUTO_ADVANCE</td>\n", "      <td>android</td>\n", "      <td>1</td>\n", "      <td>244</td>\n", "      <td>100</td>\n", "      <td>250</td>\n", "      <td>175</td>\n", "      <td>52</td>\n", "      <td>5</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>8</td>\n", "      <td>39</td>\n", "      <td>39</td>\n", "      <td>112</td>\n", "      <td>112</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>AE</td>\n", "      <td>AUTO_ADVANCE</td>\n", "      <td>android</td>\n", "      <td>1</td>\n", "      <td>288</td>\n", "      <td>50</td>\n", "      <td>300</td>\n", "      <td>175</td>\n", "      <td>61</td>\n", "      <td>6</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "      <td>46</td>\n", "      <td>46</td>\n", "      <td>132</td>\n", "      <td>132</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>AE</td>\n", "      <td>AUTO_ADVANCE</td>\n", "      <td>android</td>\n", "      <td>1</td>\n", "      <td>369</td>\n", "      <td>20</td>\n", "      <td>400</td>\n", "      <td>175</td>\n", "      <td>79</td>\n", "      <td>8</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>11</td>\n", "      <td>58</td>\n", "      <td>58</td>\n", "      <td>169</td>\n", "      <td>169</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>AE</td>\n", "      <td>CONTENT_INTERSTITIAL</td>\n", "      <td>android</td>\n", "      <td>20</td>\n", "      <td>237</td>\n", "      <td>100</td>\n", "      <td>250</td>\n", "      <td>175</td>\n", "      <td>48</td>\n", "      <td>5</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "      <td>39</td>\n", "      <td>39</td>\n", "      <td>110</td>\n", "      <td>110</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>AE</td>\n", "      <td>CONTENT_INTERSTITIAL</td>\n", "      <td>android</td>\n", "      <td>20</td>\n", "      <td>277</td>\n", "      <td>50</td>\n", "      <td>300</td>\n", "      <td>175</td>\n", "      <td>56</td>\n", "      <td>6</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>8</td>\n", "      <td>45</td>\n", "      <td>45</td>\n", "      <td>128</td>\n", "      <td>128</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2131</th>\n", "      <td>SI</td>\n", "      <td>AUTO_ADVANCE</td>\n", "      <td>web</td>\n", "      <td>149</td>\n", "      <td>20</td>\n", "      <td>20</td>\n", "      <td>400</td>\n", "      <td>175</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2132</th>\n", "      <td>KE</td>\n", "      <td>AUTO_ADVANCE</td>\n", "      <td>web</td>\n", "      <td>149</td>\n", "      <td>20</td>\n", "      <td>20</td>\n", "      <td>400</td>\n", "      <td>175</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2133</th>\n", "      <td>BE</td>\n", "      <td>AUTO_ADVANCE</td>\n", "      <td>web</td>\n", "      <td>149</td>\n", "      <td>20</td>\n", "      <td>20</td>\n", "      <td>400</td>\n", "      <td>175</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2134</th>\n", "      <td>NG</td>\n", "      <td>AUTO_ADVANCE</td>\n", "      <td>web</td>\n", "      <td>149</td>\n", "      <td>20</td>\n", "      <td>20</td>\n", "      <td>400</td>\n", "      <td>175</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2135</th>\n", "      <td>TR</td>\n", "      <td>AUTO_ADVANCE</td>\n", "      <td>web</td>\n", "      <td>149</td>\n", "      <td>20</td>\n", "      <td>20</td>\n", "      <td>400</td>\n", "      <td>175</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10680 rows × 27 columns</p>\n", "</div>"], "text/plain": ["     country  distribution_channel platform_type  bucket_id  top_k  l_bound  \\\n", "0         AE          AUTO_ADVANCE       android          1    244      100   \n", "1         AE          AUTO_ADVANCE       android          1    288       50   \n", "2         AE          AUTO_ADVANCE       android          1    369       20   \n", "3         AE  CONTENT_INTERSTITIAL       android         20    237      100   \n", "4         AE  CONTENT_INTERSTITIAL       android         20    277       50   \n", "...      ...                   ...           ...        ...    ...      ...   \n", "2131      SI          AUTO_ADVANCE           web        149     20       20   \n", "2132      KE          AUTO_ADVANCE           web        149     20       20   \n", "2133      BE          AUTO_ADVANCE           web        149     20       20   \n", "2134      NG          AUTO_ADVANCE           web        149     20       20   \n", "2135      TR          AUTO_ADVANCE           web        149     20       20   \n", "\n", "      u_bound    L  swipe  landing_page_view  ...  app_addcart  \\\n", "0         250  175     52                  5  ...            1   \n", "1         300  175     61                  6  ...            1   \n", "2         400  175     79                  8  ...            1   \n", "3         250  175     48                  5  ...            1   \n", "4         300  175     56                  6  ...            1   \n", "...       ...  ...    ...                ...  ...          ...   \n", "2131      400  175      1                  1  ...            1   \n", "2132      400  175      1                  1  ...            1   \n", "2133      400  175      1                  1  ...            1   \n", "2134      400  175      1                  1  ...            1   \n", "2135      400  175      1                  1  ...            1   \n", "\n", "      video_views_15_sec  video_views  pixel_purchase  lat_pixel_purchase  \\\n", "0                      2            8              39                  39   \n", "1                      2            9              46                  46   \n", "2                      2           11              58                  58   \n", "3                      2            7              39                  39   \n", "4                      2            8              45                  45   \n", "...                  ...          ...             ...                 ...   \n", "2131                   1            1               1                   1   \n", "2132                   1            1               1                   1   \n", "2133                   1            1               1                   1   \n", "2134                   1            1               1                   1   \n", "2135                   1            1               1                   1   \n", "\n", "      pixel_purchase_7_0  lat_pixel_purchase_7_0  amazon_swipe  hce_swipe  T  \n", "0                    112                     112             1          1  1  \n", "1                    132                     132             1          1  1  \n", "2                    169                     169             1          1  1  \n", "3                    110                     110             1          1  1  \n", "4                    128                     128             1          1  1  \n", "...                  ...                     ...           ...        ... ..  \n", "2131                   1                       1             1          1  5  \n", "2132                   1                       1             1          1  5  \n", "2133                   1                       1             1          1  5  \n", "2134                   1                       1             1          1  5  \n", "2135                   1                       1             1          1  5  \n", "\n", "[10680 rows x 27 columns]"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["df_quotas_query_mapping_combined"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"data": {"text/plain": ["Table(TableReference(DatasetReference('sc-bq-gcs-billingonly', 'temp_datascience'), 'opt_retrieval_quota_20250628'))"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["opt_output_table = f\"{PROJECT}.{DATASET}.{opt_retrieval_quota_table}\"\n", "\n", "job_config = bigquery.LoadJobConfig(\n", "    write_disposition=\"WRITE_TRUNCATE\",\n", ")\n", "\n", "job = client.load_table_from_dataframe(\n", "    df_quotas_query_mapping_combined,\n", "    opt_output_table,\n", "    job_config = job_config,\n", ")\n", "job.result()\n", "\n", "# 2) Fetch the newly‐created table\n", "table = client.get_table(opt_output_table)\n", "\n", "# 3) Set its expiration (UTC) to now + retention_days\n", "table.expires = datetime.now() + timedelta(days=retention_days)\n", "\n", "# 4) Push that update\n", "client.update_table(table, [\"expires\"])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}