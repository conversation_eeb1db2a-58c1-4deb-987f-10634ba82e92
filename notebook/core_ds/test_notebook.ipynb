{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"id": "afvQi8tDRS9J"}, "outputs": [], "source": ["from google.cloud import bigquery\n", "import pandas as pd\n", "import numpy as np\n", "from scipy.optimize import curve_fit, least_squares, minimize\n", "import matplotlib.pyplot as plt\n", "from scipy.stats import f\n", "from datetime import datetime, timedelta\n", "from scipy import sparse\n", "import pickle\n", "import gc\n", "import os\n", "import itertools"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Parameters and setups"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "ptWKET3NQYa5", "tags": ["parameters"]}, "outputs": [], "source": ["# Parameters\n", "GCLOUD_PROJECT = \"snap-ads-debug\"\n", "# ## for production\n", "# PROJECT = 'snapads-ranking'\n", "# DATASET = 'retrieval_optimization'\n", "# retention_days = 360\n", "\n", "## for testing\n", "PROJECT = 'sc-bq-gcs-billingonly'\n", "DATASET = 'temp_datascience'\n", "log_text = 'test adding'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": "client = bigquery.Client(project = GCLOUD_PROJECT)"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["query_raw = f\"\"\"\n", "INSERT INTO\n", "  `{PROJECT}.{DATASET}.yjiang2_debug_log` (\n", "    log_text\n", "  )\n", "\n", "select\n", "  '{{log_text}}' as log_text\n", "\"\"\"\n", "\n", "query = query_raw.format(log_text=log_text)\n", "job = client.query(query)\n", "job.result()"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}