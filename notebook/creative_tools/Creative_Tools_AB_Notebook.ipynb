{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Creative Tools AB Notebook"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Questions or feedback? Please don't hesitate to reach out to <PERSON> (atan2@) or <PERSON><PERSON> (bpatel@).**\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Specify study details\n", "STUDY_NAME = 'MUSIC_UGC_MAY_CONTAIN_ATTRIBUTION_M2__147732'\n", "\n", "# The start date must match the study(current version) start date in the study config.\n", "STUDY_START_DATE = '20250703'\n", "STUDY_END_DATE = '20250704'\n", "\n", "RETRO_START_DATE = None\n", "RETRO_END_DATE = None\n", "\n", "# ANALYSIS_START_DATE = '20211208'\n", "ANALYSIS_START_DATE = STUDY_START_DATE\n", "CONTROL_ID = '2'\n", "TREATMENT_IDS = ['3']\n", "\n", "COHORT_DATE = STUDY_END_DATE\n", "# COHORT_DATE = '20200302'\n", "\n", "# Possible values: country, age_group, user_start_month, device_name(top devices), study_language\n", "USER_BREAKDOWN_LIST = [\n", "#     'device_maker',\n", "#    'device_cluster',\n", "#     'l_90_country',\n", "#     'study_language',\n", "#     'days_since_creation',\n", "#     'device_model',\n", "#    'communication_engagement_status'\n", "#    'app_engagement_status',\n", "]\n", "\n", "METRICS_GROUP_CONFIG = [\n", "#      \"chat_sticker\",\n", "#    \"preview_sticker\",\n", " #   \"preview_ct_overall\",\n", "    # \"preview_ct_detail\",\n", "#     \"music_overall\",\n", "    \"music_detail\",\n", "#   \"creative_tools_downstream\",\n", "#   \"creative_tools_downstream_t1\",\n", "#    \"creative_tools_downstream_t2\"\n", "]\n", "\n", "quantiles = ['50', '90'] \n", "\n", "PREVIEW_CT_BREAKDOWNS = [\n", "    \"snap_action_type\",\n", "#     \"media_type\",\n", "#     \"source\",\n", "#     \"camera\"\n", "]\n", "\n", "COUNTRIES = [\n", "#     \"US\",\n", "]\n", "\n", "OVERWRITE = False\n", "INCLUDE_DAILY_METRICS = False\n", "MATERIALIZE_MAPPING_TABLE = False\n", "USE_BATCH_BQ_PRIORITY = False\n", "PIVOT_RESULTS = True\n", "COMPUTE_RETRO_AA = False"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Imports\n", "from __future__ import division, unicode_literals, print_function\n", "import os\n", "VELLUM = any(['VELLUM' in k for k in os.environ.keys()])\n", "if VELLUM:\n", "    pip_output = !pip install banjo\n", "\n", "import seaborn as sns\n", "import pandas as pd\n", "import logging\n", "logging.basicConfig()\n", "logging.getLogger().setLevel(logging.ERROR)\n", "\n", "%matplotlib inline\n", "%config InlineBackend.figure_format = 'retina'\n", "\n", "from banjo import utils\n", "from banjo import abtest # see: go/pya\n", "from banjo.abtest.report import (\n", "    Metric, MetricTable, Report, get_quest_metric_table, CustomReport, \n", "    get_abtest_console_metric_table, QuantileReport\n", ")\n", "\n", "from banjo import abtest\n", "from banjo.abtest.field_breakdown_metric_table import FieldBreakdownMetricTable\n", "from banjo.abtest.cohort_report import CohortReport\n", "from banjo.abtest.quest import TIER_ONE\n", "\n", "from banjo.teams.product import sticker_camera as sticker\n", "from banjo.teams.product import preview_creative_tools as preview\n", "from banjo.teams.product import music_metrics as music\n", "from banjo.teams.product import lens_explorer_metrics as le\n", "from banjo.teams.product import creative_tools_downstream as downstream \n", "\n", "PROJECT = 'sc-bq-gcs-billingonly'\n", "DATASET = 'temp_abtest'\n", "\n", "\n", "from IPython.core.display import display, HTML\n", "display(HTML(\"<style>.output_html.rendered_html table { font-size:8pt;}</style>\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ensure certain items passed from husky are lists\n", "def ensure_list(parameter):\n", "    if isinstance(parameter, list):\n", "        return parameter\n", "    else:\n", "        return [parameter]\n", "\n", "TREATMENT_IDS = ensure_list(TREATMENT_IDS)\n", "COUNTRIES = ensure_list(COUNTRIES)\n", "USER_BREAKDOWN_LIST = ensure_list(USER_BREAKDOWN_LIST)\n", "METRICS_GROUP_CONFIG = ensure_list(METRICS_GROUP_CONFIG)\n", "PREVIEW_CT_BREAKDOWNS = ensure_list(PREVIEW_CT_BREAKDOWNS)\n", "\n", "BQ_PRIORITY = 'BATCH' if USE_BATCH_BQ_PRIORITY else 'INTERACTIVE'\n", "\n", "# # Get study name\n", "# study_name_sql = \"\"\"\n", "#     SELECT\n", "#       name,\n", "#       exp_id\n", "#     FROM\n", "#       [sc-analytics:report_search.ab_console_study_config]\n", "#     WHERE\n", "#       study_name = '{study_name}'\n", "#       AND exp_id IN ('{exp_ids}')\n", "# \"\"\"\n", "\n", "# EXP_NAMES = None\n", "\n", "# df_exp_names = utils.gbq.read_gbq(\n", "#     study_name_sql.format(\n", "#         study_name=STUDY_NAME, exp_ids=\"', '\".join([CONTROL_ID] + TREATMENT_IDS)\n", "#     ),\n", "#     project_id=PROJECT)\n", "# if not EXP_NAMES and not df_exp_names.empty:\n", "#     EXP_NAMES = dict(zip(df_exp_names.exp_id, df_exp_names.name))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["############ Add metrics ############"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["############# Chat sticker metrics + print section name #############\n", "chat_sticker_metric_tables = {}\n", "# some newly added metrics are only available after a certain date\n", "if STUDY_END_DATE>=\"20210729\":\n", "    chat_sticker_metric_tables.update({sticker.chat_sticker_funnel_metrics(STUDY_START_DATE, STUDY_END_DATE): \n", "                                      \"Chat Sticker Funnel Metrics\"})\n", "    chat_sticker_metric_tables.update({sticker.sticker_favorite_metrics(STUDY_START_DATE, STUDY_END_DATE):\n", "                                       \"Sticker Favorite Metrics (Data After 07/29/2021)\" })\n", "else:\n", "    chat_sticker_metric_tables.update({sticker.chat_sticker_funnel_metrics_old(STUDY_START_DATE, STUDY_END_DATE): \n", "                                      \"Chat Sticker Funnel Metrics\"})\n", "\n", "# other chat ct metrics\n", "chat_sticker_metric_tables.update({\n", "    sticker.chat_sticker_drawer_metrics(STUDY_START_DATE, STUDY_END_DATE): \"Chat Sticker Drawer Metrics\",\n", "    sticker.chat_sticker_drawer_perf_metrics(STUDY_START_DATE, STUDY_END_DATE): \"Chat Sticker Drawer Perf Metrics\",\n", "    sticker.chat_sticker_tab_session_metrics(STUDY_START_DATE, STUDY_END_DATE): \"Chat Sticker Drawer Tab Session Metrics\",\n", "})\n", "\n", "\n", "############# Preview sticker metrics + print section name #############\n", "preview_sticker_metric_tables = {\n", "    sticker.sticker_picker_metrics(STUDY_START_DATE, STUDY_END_DATE): \"Preview Sticker Pick Metrics\",\n", "    sticker.sticker_picker_search_metrics(STUDY_START_DATE, STUDY_END_DATE): \"Preview Sticker Search Metrics\",\n", "    sticker.sticker_picker_search_detail_metrics(STUDY_START_DATE, STUDY_END_DATE): \"Preview Sticker Search Detail Metrics\",\n", "    sticker.preview_sticker_drawer_perf_metrics(STUDY_START_DATE, STUDY_END_DATE): \"Preview Sticker Picker Performance Metrics\",\n", "    sticker.preview_sticker_impression_metrics(STUDY_START_DATE, STUDY_END_DATE): \"Preview Sticker Impression Metrics\",\n", "    sticker.info_sticker_carousel_metrics(STUDY_START_DATE, STUDY_END_DATE): \"Preview Info Sticker Carousel Metrics\",\n", "    sticker.preview_sticker_pick_by_tab_metrics(STUDY_START_DATE, STUDY_END_DATE): \"Preview Sticker Pick by Tab Metrics\",\n", "    sticker.preview_sticker_tab_session_metrics(STUDY_START_DATE, STUDY_END_DATE): \"Preview Sticker Picker Tab Session Metrics\",\n", "}\n", "\n", "############# Preview CT metrics + print section name #############\n", "\n", "# some additional UCO metrics are only available after 20211126\n", "if STUDY_END_DATE>=\"20211126\": \n", "    snap_action_ct_metrics_generator = preview.snap_action_ct_metrics_generator\n", "    snap_action_ct_edit_count_metrics_generator = preview.snap_action_ct_edit_count_metrics_generator\n", "else: \n", "    snap_action_ct_metrics_generator = preview.snap_action_ct_metrics_generator_old\n", "    snap_action_ct_edit_count_metrics_generator = preview.snap_action_ct_edit_count_metrics_generator_old\n", "\n", "downstream_direct_send_conversion_metrics_generator = downstream.downstream_direct_send_conversion_metrics_generator\n", "downstream_story_post_conversion_metrics_generator = downstream.downstream_story_post_conversion_metrics_generator\n", "downstream_direct_send_conversion_t2_metrics_generator = downstream.downstream_direct_send_conversion_t2_metrics_generator\n", "downstream_story_post_conversion_t2_metrics_generator = downstream.downstream_story_post_conversion_t2_metrics_generator\n", "\n", "# Add send/save/post metrics\n", "if \"snap_action_type\" in PREVIEW_CT_BREAKDOWNS: \n", "    PREVIEW_CT_BREAKDOWNS.remove(\"snap_action_type\") # remove this item since it's not a field level breakdown\n", "    preview_ct_overall_metric_tables = {\n", "        preview.preview_action_event_metrics(STUDY_START_DATE, STUDY_END_DATE): \"CT from Action Events Metrics\",\n", "        snap_action_ct_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                              \"snap_action\"): \"Snap Action with CT Metrics\",\n", "        snap_action_ct_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"snap_send\"): \"Snap Send with CT Metrics\",\n", "        snap_action_ct_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"snap_save\"): \"Snap Save with CT Metrics\",\n", "        snap_action_ct_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"story_post\"): \"Story Post with CT Metrics\",\n", "        snap_action_ct_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"spotlight_submission\"): \"Spotlight Submission with CT Metrics\",\n", "    }\n", "    preview_ct_detail_metric_tables = {\n", "        snap_action_ct_edit_count_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"snap_action\"): \"Snap Action - CT Edit Metrics\",\n", "        snap_action_ct_edit_count_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"snap_send\"): \"Snap Send - CT Edit Metrics\",\n", "        snap_action_ct_edit_count_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"snap_save\"): \"Snap Save - CT Edit Metrics\",\n", "        snap_action_ct_edit_count_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"story_post\"): \"Story Post - CT Edit Metrics\",\n", "        snap_action_ct_edit_count_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"spotlight_submission\"): \"Spotlight Submission - CT Edit Metrics\",\n", "        preview.snap_action_ct_edit_time_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"snap_action\"): \"Snap Action - CT Edit Time Metrics\",\n", "        preview.snap_action_ct_edit_time_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"snap_send\"): \"Snap Send - CT Edit Time Metrics\",\n", "        preview.snap_action_ct_edit_time_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"snap_save\"): \"Snap Save - CT Edit Time Metrics\",\n", "        preview.snap_action_ct_edit_time_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"story_post\"): \"Story Post - CT Edit Time Metrics\",\n", "        preview.snap_action_ct_edit_time_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"spotlight_submission\"): \"Spotlight Submission - CT Edit Time Metrics\",\n", "        preview.snap_action_filter_conversion_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"snap_action\"): \"Snap Action - Filter Conversion Rate Metrics\",\n", "        preview.snap_action_filter_conversion_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"snap_send\"): \"Snap Send - Filter Conversion Rate Metrics\",\n", "        preview.snap_action_filter_conversion_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"snap_save\"): \"Snap Save - Filter Conversion Rate Metrics\",\n", "        preview.snap_action_filter_conversion_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"story_post\"): \"Story Post - Filter Conversion Rate Metrics\",\n", "        preview.snap_action_filter_conversion_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"spotlight_submission\"): \"Spotlight Submission - Filter Conversion Rate Metrics\",\n", "        preview.snap_action_caption_detail_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"snap_action\"): \"Snap Action - Detailed Caption Metrics\",\n", "        preview.snap_action_caption_detail_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"snap_send\"): \"Snap Send - Detailed Caption Metrics\",\n", "        preview.snap_action_caption_detail_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"snap_save\"): \"Snap Save - Detailed Caption Metrics\",\n", "        preview.snap_action_caption_detail_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"story_post\"): \"Story Post - Detailed Caption Metrics\",\n", "        preview.snap_action_caption_detail_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"spotlight_submission\"): \"Spotlight Submission - Detailed Caption Metrics\",\n", "        preview.snap_action_user_tagging_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"snap_action\"): \"Snap Action - User Tagging Metrics\",\n", "        preview.snap_action_user_tagging_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"snap_send\"): \"Snap Send - User Tagging Metrics\",\n", "        preview.snap_action_user_tagging_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"snap_save\"): \"Snap Save - User Tagging Metrics\",\n", "        preview.snap_action_user_tagging_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"story_post\"): \"Story Post - User Tagging Metrics\",\n", "        preview.snap_action_user_tagging_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"spotlight_submission\"): \"Spotlight Submission - User Tagging Metrics\",\n", "        le.uco_lens_explorer_feed_metrics(STUDY_START_DATE, STUDY_END_DATE): \"Post Capture Lens Explorer Metrics\"\n", "    }\n", "\n", "else: \n", "    preview_ct_overall_metric_tables = {\n", "        snap_action_ct_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                              \"snap_action\"): \"Snap Action with CT Metrics\",\n", "    }\n", "    preview_ct_detail_metric_tables = {\n", "        snap_action_ct_edit_count_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"snap_action\"): \"Snap Action - CT Edit Metrics\",\n", "        preview.snap_action_ct_edit_time_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"snap_action\"): \"Snap Action - CT Edit Time Metrics\",\n", "        preview.snap_action_filter_conversion_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"snap_action\"): \"Snap Action - Filter Conversion Rate Metrics\",\n", "        preview.snap_action_caption_detail_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"snap_action\"): \"Snap Action - Detailed Caption Metrics\",\n", "        preview.snap_action_user_tagging_metrics_generator(STUDY_START_DATE, STUDY_END_DATE, \n", "                                                            [[bd] for bd in PREVIEW_CT_BREAKDOWNS], \n", "                                                  \"snap_action\"): \"Snap Action - User Tagging Metrics\",\n", "        le.uco_lens_explorer_feed_metrics(STUDY_START_DATE, STUDY_END_DATE): \"Post Capture Lens Explorer Metrics\"\n", "        \n", "    }\n", "\n", "############# Downstream CT Metrics ##############\n", "downstream_ct_metric_tables = {\n", "    downstream_direct_send_conversion_metrics_generator(STUDY_START_DATE, STUDY_END_DATE): \"CT Direct Send Conversion Downstream Metrics\",\n", "    downstream_story_post_conversion_metrics_generator(STUDY_START_DATE, STUDY_END_DATE): \"CT Story Post Conversion Downstream Metrics\"\n", "}\n", "\n", "downstream_ct_t2_metric_tables = {\n", "    downstream_direct_send_conversion_t2_metrics_generator(STUDY_START_DATE, STUDY_END_DATE): \"Tier 2 CT Direct Send Conversion Downstream Metrics\",\n", "    downstream_story_post_conversion_t2_metrics_generator(STUDY_START_DATE, STUDY_END_DATE): \"Tier 2 CT Story Post Conversion Downstream Metrics\"\n", "}\n", "\n", "############# Music metrics + print section name #############\n", "music_overall_metric_tables = {\n", "    music.music_overall(STUDY_START_DATE, STUDY_END_DATE): \"Music Overall Funnel Metrics\",\n", "}\n", "music_detail_metric_tables = {\n", "    music.music_licensed(STUDY_START_DATE, STUDY_END_DATE): \"Music Licensed Funnel Metrics\",\n", "    music.music_ugc(STUDY_START_DATE, STUDY_END_DATE): \"Music UGC Funnel Metrics\",\n", "    music.music_spotlight_sounds(STUDY_START_DATE, STUDY_END_DATE): \"Music Spotlight Sounds Funnel Metrics\",\n", "    music.music_pick_conversion_by_tabs(STUDY_START_DATE, STUDY_END_DATE): \"Music Conversion by Tabs Metrics\",\n", "    music.music_playlist(STUDY_START_DATE, STUDY_END_DATE): \"Music Playlist Engagement Metrics\",\n", "    music.music_picks_by_sections(STUDY_START_DATE, STUDY_END_DATE): \"Music Picks by Sections Metrics\",\n", "    music.music_recommendations(STUDY_START_DATE, STUDY_END_DATE): \"Music Recommendations\",\n", "    music.music_picker_latency(STUDY_START_DATE, STUDY_END_DATE): \"Music Picker Latency\",\n", "    music.music_request(STUDY_START_DATE, STUDY_END_DATE):\"Music Request\",\n", "}\n", "\n", "\n", "# mapping between metric config and functions\n", "metric_config_mapping = {\n", "        \"chat_sticker\": chat_sticker_metric_tables,\n", "        \"preview_sticker\": preview_sticker_metric_tables,\n", "        \"preview_ct_overall\": preview_ct_overall_metric_tables,\n", "        \"preview_ct_detail\": preview_ct_detail_metric_tables,\n", "        \"music_overall\": music_overall_metric_tables,\n", "        \"music_detail\": music_detail_metric_tables,\n", "        \"creative_tools_downstream\": downstream_ct_metric_tables,\n", "        \"creative_tools_downstream_t1\": downstream_ct_metric_tables,\n", "        \"creative_tools_downstream_t2\": downstream_ct_t2_metric_tables,\n", "\n", "        \n", "}\n", "\n", "# create the empty aggregate metric table list\n", "metric_tables = []\n", "metric_full_dict = {}\n", "\n", "# constructuring the aggregate list by combining normal MetricTables and fieldBreakdownMetricTables\n", "for metric_group in METRICS_GROUP_CONFIG:\n", "    \n", "    metric_temp_dict = {}\n", "    \n", "    for metric_table, name in metric_config_mapping[metric_group].items():\n", "    \n", "        metric_tables = metric_tables + [metric_table]\n", "        # save the metrics included in this function, so that we can automate report printing later\n", "        metric_temp_dict.update({name: [metric.col for metric in metric_table.metrics]})\n", "\n", "    metric_full_dict.update({metric_group: metric_temp_dict})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# specify which metric section names are quantile metrics (for report printing purposes)\n", "quantile_metric_section_print_name_list = [\n", "    \"Chat Sticker Drawer Perf <PERSON>s\",\n", "    \"Preview Sticker Picker Performance Metrics\",\n", "]\n", "\n", "# specify which metric section names are metrics with field level breakdowns (for report printing purposes)\n", "field_breakdown_metric_section_print_name_list = [\n", "    \"CT from Action Events Metrics\",\n", "    \"Snap Action with CT Metrics\",\n", "    \"Snap Send with CT Metrics\",\n", "    \"Snap Save with CT Metrics\",\n", "    \"Story Post with CT Metrics\",\n", "    \"Spotlight Submission with CT Metrics\",\n", "    \"Snap Action - CT Edit Metrics\",\n", "    \"Snap Send - CT Edit Metrics\",\n", "    \"Snap Save - CT Edit Metrics\",\n", "    \"Story Post - CT Edit Metrics\",\n", "    \"Spotlight Submission - CT Edit Metrics\",\n", "    \"Snap Action - CT Edit Time Metrics\",\n", "    \"Snap Send - CT Edit Time Metrics\",\n", "    \"Snap Save - CT Edit Time Metrics\",\n", "    \"Story Post - CT Edit Time Metrics\",\n", "    \"Spotlight Submission - CT Edit Time Metrics\",\n", "    \"Snap Action - Filter Conversion Rate Metrics\",\n", "    \"Snap Send - Filter Conversion Rate Metrics\",\n", "    \"Snap Save - Filter Conversion Rate Metrics\",\n", "    \"Story Post - Filter Conversion Rate Metrics\",\n", "    \"Spotlight Submission - Filter Conversion Rate Metrics\",\n", "    \"Snap Action - Detailed Caption Metrics\",\n", "    \"Snap Send - Detailed Caption Metrics\",\n", "    \"Snap Save - Detailed Caption Metrics\",\n", "    \"Story Post - Detailed Caption Metrics\",\n", "    \"Spotlight Submission - Detailed Caption Metrics\",\n", "    \"Snap Action - Detailed Caption Metrics\",\n", "    \"Snap Send - Detailed Caption Metrics\",\n", "    \"Snap Save - Detailed Caption Metrics\",\n", "    \"Story Post - Detailed Caption Metrics\",\n", "    \"Spotlight Submission - Detailed Caption Metrics\",\n", "    \"Snap Action - User Tagging Metrics\",\n", "    \"Snap Send - User Tagging Metrics\",\n", "    \"Snap Save - User Tagging Metrics\",\n", "    \"Story Post - User Tagging Metrics\",\n", "    \"Spotlight Submission - User Tagging Metrics\",\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# set up A/A config\n", "if COMPUTE_RETRO_AA:\n", "    for mt in metric_tables:\n", "        if not isinstance(mt, FieldBreakdownMetricTable):\n", "            mt.aa = True"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# OVERWRITE = True\n", "# MATERIALIZE_MAPPING_TABLE = True\n", "COHORT_DATE = STUDY_END_DATE\n", "report = CohortReport(\n", "    study_name=STUDY_NAME,\n", "    study_start_date=STUDY_START_DATE,\n", "    study_end_date=STUDY_END_DATE,\n", "    aa_start_date=RETRO_START_DATE,\n", "    aa_end_date=RETRO_END_DATE,\n", "    metric_tables=metric_tables,\n", "    control_id=CONTROL_ID,\n", "    treatment_ids=TREATMENT_IDS,               \n", "    user_group_bys=USER_BREAKDOWN_LIST,\n", "    bq_project=PROJECT,  \n", "    dest_dataset=DATASET,\n", "    cohort_definition_date=COHORT_DATE,\n", "    materializing_mapping_table=MATERIALIZE_MAPPING_TABLE,\n", "    overwrite_mapping_table=OVERWRITE,\n", "    bq_priority=BQ_PRIORITY,\n", "    quantiles=quantiles,\n", "    bq_dialect='standard',\n", "    excel_output=True,\n", "    \n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run the joins and calculate the results\n", "report.execute(\n", "    overwrite=OVERWRITE,\n", "    group_filters={\n", "        \"l_90_country\": COUNTRIES,\n", "    },\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report.configurations['pivot_table'] = True\n", "report.configurations['stat_fmtr'] = (\n", "#     \"{pct_diff:,.2f}% (→{avg_treatment:,.3f}, {p_value_formatted})\"\n", "    \"{pct_diff:,.2f}% ({avg_control:,.3}→{avg_treatment:,.3}, {p_value_formatted})\"\n", ")\n", "\n", "# formatting of the report\n", "\n", "fmtr = \"{pct_diff:,.2f}% ({avg_control:,.3}→{avg_treatment:,.3}, {p_value_formatted})\"\n", "quantile_fmtr = \"{pct_diff:,.2f}% ({quantile_control:,.3}→{quantile_treatment:,.3}, {p_value_formatted})\"\n", "if not PIVOT_RESULTS:\n", "    quantile_fmtr = \"P{quantile}: \" + quantile_fmtr"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Print the report\n", "abtest.report.notebook_print(\"Study Name: {}\".format(report.study_name), \"h3\")\n", "abtest.report.notebook_print(\"Control ID: {}\".format(report.control_id), \"strong\")\n", "abtest.report.notebook_print(\"Treatment ID(s): {}\".format(\", \".join(report.treatment_ids)), \"strong\")\n", "abtest.report.notebook_print(\n", "    \"Study running period: {} - {}\".format(report.study_start_date, report.study_end_date), \n", "    \"strong\"\n", ")\n", "abtest.report.notebook_print(\n", "    \"Report analysis period: {} - {}\".format(STUDY_START_DATE, STUDY_END_DATE), \n", "    \"strong\"\n", ")\n", "abtest.report.notebook_print(\n", "    \"Breakdowns: <br> {}\".format(\n", "        \"<br>\".join(\", \".join(breakdown) for breakdown in report.user_group_by_list)\n", "    ), \n", "    \"strong\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for metric_group in METRICS_GROUP_CONFIG: \n", "            \n", "    for metric_print_name, metric_list in metric_full_dict[metric_group].items():\n", "            \n", "        # printing the non-field level breakdown metrics\n", "        if metric_print_name not in field_breakdown_metric_section_print_name_list:\n", "            \n", "            # Generate Non-UU Metrics\n", "            abtest.report.notebook_print(metric_print_name, \"h2\")\n", "\n", "            display_cfg = {\n", "                \"daily\": [],\n", "                \"cumulative\": [\"table\"],\n", "            }\n", "\n", "            if 'CT Direct Send Conversion Downstream Metrics' not in metric_print_name and 'CT Story Post Conversion Downstream Metrics' not in metric_print_name and 'Tier 2 CT Direct Send Conversion Downstream Metrics' not in metric_print_name and 'Tier 2 CT Story Post Conversion Downstream Metrics' not in metric_print_name: \n", "                report.generate_report(\n", "                    format_pvalue=True,\n", "                    extra_table_cols=['avg_control', 'avg_treatment', 'count_control', 'count_treatment'],\n", "                    metric_filters = {\n", "                        'metrics': [metric for metric in metric_list if \"uu\" not in metric]\n", "                    },\n", "                    display_config = display_cfg,\n", "                    stat_fmtr=fmtr if metric_print_name not in quantile_metric_section_print_name_list else quantile_fmtr,\n", "                );\n", "            else:\n", "                report.generate_report(\n", "                    format_pvalue=True,\n", "                    extra_table_cols=['avg_control', 'avg_treatment', 'count_control', 'count_treatment'],\n", "                    metric_filters = {\n", "                        'metrics': [metric for metric in metric_list if \"uu\" not in metric and \"count\" not in metric]\n", "                    },\n", "                    display_config = display_cfg,\n", "                    stat_fmtr=fmtr if metric_print_name not in quantile_metric_section_print_name_list else quantile_fmtr,\n", "                );\n", "                \n", "                \n", "            \n", "            # Generate UU Metrics if any\n", "            if [metric for metric in metric_list if \"uu\" in metric]:\n", "\n", "                abtest.report.notebook_print(metric_print_name + \" - UU Metrics\", \"h2\")\n", "\n", "                display_cfg = {\n", "                    \"daily\": [],\n", "                    \"cumulative\": [\"table\"],\n", "                }\n", "\n", "                if 'CT Direct Send Conversion Downstream Metrics' not in metric_print_name and 'CT Story Post Conversion Downstream Metrics' not in metric_print_name and 'Tier 2 CT Direct Send Conversion Downstream Metrics' not in metric_print_name and 'Tier 2 CT Story Post Conversion Downstream Metrics' not in metric_print_name: \n", "                     report.generate_report(\n", "                        format_pvalue=True,\n", "                        extra_table_cols=['avg_control', 'avg_treatment', 'count_control', 'count_treatment'],\n", "                        metric_filters = {\n", "                            'metrics': [metric for metric in metric_list if \"uu\" in metric]\n", "                        },\n", "                        display_config = display_cfg,\n", "                        stat_fmtr=fmtr if metric_print_name not in quantile_metric_section_print_name_list else quantile_fmtr,\n", "                    );\n", "                else:\n", "                    report.generate_report(\n", "                        format_pvalue=True,\n", "                        extra_table_cols=['avg_control', 'avg_treatment', 'count_control', 'count_treatment'],\n", "                        metric_filters = {\n", "                            'metrics': [metric for metric in metric_list if \"avg_uu\" in metric]\n", "                        },\n", "                        display_config = display_cfg,\n", "                        stat_fmtr=fmtr if metric_print_name not in quantile_metric_section_print_name_list else quantile_fmtr,\n", "                    );\n", "\n", "        # printing the field level breakdown metrics (preview metrics)\n", "        else: \n", "            \n", "            # remove this item since it's not a real field level breakdown\n", "            if \"snap_action_type\" in PREVIEW_CT_BREAKDOWNS:\n", "                \n", "                display_cfg = {\n", "                    \"daily\": [],\n", "                    \"cumulative\": [\"table\"],\n", "                }\n", "                \n", "                if 'CT from Action Events Metrics' in metric_print_name: \n", "                    report.ab_printer.print_text(metric_print_name, 'h2')\n", "                    report.generate_report(\n", "                        format_pvalue=True,\n", "                        extra_table_cols=['avg_control', 'avg_treatment', 'count_control', 'count_treatment'],\n", "                        metric_filters = {\n", "                            'metrics': [metric for metric in metric_list if \"uu\" not in metric]\n", "                        },\n", "                        display_config = display_cfg,\n", "                        stat_fmtr=fmtr if metric_print_name not in quantile_metric_section_print_name_list else quantile_fmtr,\n", "                    );\n", "                \n", "                PREVIEW_CT_BREAKDOWNS.remove(\"snap_action_type\") \n", "            \n", "            # include one table for overall results\n", "            breakdowns = [\"Overall\"] + PREVIEW_CT_BREAKDOWNS\n", "            \n", "            for breakdown in breakdowns:\n", "\n", "                group_vars = []\n", "                results = report.get_results(\n", "                    \"cumulative\",\n", "                    group_vars=group_vars,\n", "                    exclude_field_breakdown=False,\n", "                    metric_filters={'metric': metric_list,\n", "                                    'metric_breakdown_dimensions': [breakdown]}\n", "                )\n", "\n", "                report.ab_printer.print_text(metric_print_name + \" - By {breakdown}\".format(breakdown=breakdown), 'h2')\n", "                report.visualize(\n", "                        results,\n", "                        group_vars=group_vars,\n", "                        format_pvalue=True,\n", "                        show_table=True,\n", "                        table_format='table',\n", "                        should_pivot_table=True,        \n", "                        pivot_table_rows=['Date', 'Metric', 'metric_breakdown_values'] + group_vars[:-1],\n", "                        pivot_table_cols=['control_id', 'treatment_id'] + group_vars[-1:],    \n", "                        stat_fmtr=fmtr if metric_print_name not in quantile_metric_section_print_name_list \n", "                                        else quantile_fmtr,\n", "                );\n", "                \n", "                # print user level breakdowns for field breakdown metric tables\n", "                if len(USER_BREAKDOWN_LIST)>0:\n", "                    for user_group_bys in report.user_group_by_list:\n", "                        report.ab_printer.print_text('Breakdown by user groups: {}'.format(\", \".join(user_group_bys)),'h5')\n", "                        group_vars = []\n", "                        results = report.get_results(\n", "                            \"cumulative\",\n", "                            group_vars=user_group_bys,\n", "                            exclude_field_breakdown=False,\n", "                            metric_filters={'metrics': metric_list,\n", "                                            'metric_breakdown_dimensions': [breakdown]}\n", "                        )\n", "\n", "                        report.visualize(\n", "                                results,\n", "                                group_vars=group_vars,\n", "                                format_pvalue=True,\n", "                                show_table=True,\n", "                                table_format='table',\n", "                                should_pivot_table=True,        \n", "                                pivot_table_rows=['Date', 'Metric', 'metric_breakdown_values'] + group_vars[:-1],\n", "                                pivot_table_cols=['control_id', 'treatment_id', user_group_bys[0]] + group_vars[-1:],    \n", "                            );"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VELLUM = True\n", "\n", "# Show the link to download \n", "if VELLUM:\n", "    excel_file_url = report.upload_excel_output_to_gcs()\n", "if excel_file_url:\n", "    display(HTML(\n", "        \"<p><strong><a href='{}' target='__blank'>Click to download this report in excel format</a>. \"\n", "        \"You can upload it to Google Drive and open Google Sheets.</strong></p>\".format(excel_file_url)\n", "    ))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"anaconda-cloud": {}, "celltoolbar": "Tags", "kernelspec": {"display_name": "py38", "language": "python", "name": "py38"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 4}