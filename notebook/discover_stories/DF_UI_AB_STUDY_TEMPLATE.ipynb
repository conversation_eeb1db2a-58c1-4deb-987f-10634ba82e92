{"cells": [{"cell_type": "markdown", "metadata": {"toc": true}, "source": ["<h1>Table of Contents<span class=\"tocSkip\"></span></h1>\n", "<div class=\"toc\"><ul class=\"toc-item\"><li><span><a href=\"#Results\" data-toc-modified-id=\"Results-1\"><span class=\"toc-item-num\">1&nbsp;&nbsp;</span>Results</a></span><ul class=\"toc-item\"><li><span><a href=\"#Key-Metrics\" data-toc-modified-id=\"Key-Metrics-1.1\"><span class=\"toc-item-num\">1.1&nbsp;&nbsp;</span>Key Metrics</a></span></li><li><span><a href=\"#All-Metrics\" data-toc-modified-id=\"All-Metrics-1.2\"><span class=\"toc-item-num\">1.2&nbsp;&nbsp;</span>All Metrics</a></span></li><li><span><a href=\"#Meta-Analysis\" data-toc-modified-id=\"Meta-Analysis-1.3\"><span class=\"toc-item-num\">1.3&nbsp;&nbsp;</span>Meta Analysis</a></span></li><li><span><a href=\"#Key-Metrics-Daily-Trend\" data-toc-modified-id=\"Key-Metrics-Daily-Trend-1.4\"><span class=\"toc-item-num\">1.4&nbsp;&nbsp;</span>Key Metrics Daily Trend</a></span></li></ul></li><li><span><a href=\"#Story-Winners-and-Losers\" data-toc-modified-id=\"Story-Winners-and-Losers-2\"><span class=\"toc-item-num\">2&nbsp;&nbsp;</span>Story Winners and Losers</a></span></li></ul></div>"]}, {"cell_type": "code", "execution_count": 74, "metadata": {"ExecuteTime": {"end_time": "2025-04-23T18:47:57.529364Z", "start_time": "2025-04-23T18:47:57.511343Z"}, "scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/jp/cf4xyhn55mb6znzsfrrtfj_h0000gn/T/ipykernel_44806/454101038.py:40: DeprecationWarning: Importing display from IPython.core.display is deprecated since IPython 7.14, please import from IPython display\n", "  from IPython.core.display import display, HTML\n"]}, {"data": {"text/html": ["<style>.output_html.rendered_html table { font-size:8pt;}</style>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Imports\n", "from __future__ import division, unicode_literals, print_function\n", "from collections import OrderedDict\n", "from datetime import datetime,timedelta\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import os\n", "if any(['VELLUM' in k for k in os.environ.keys()]):\n", "    pip_output = !pip install banjo\n", "\n", "import seaborn as sns\n", "import logging\n", "import json\n", "\n", "logging.basicConfig()\n", "logging.getLogger().setLevel(logging.ERROR)\n", "\n", "from google.auth import _default\n", "_default._LOGGER.setLevel(logging.ERROR)\n", "_default._warn_about_problematic_credentials = lambda _: None\n", "from google.cloud import bigquery\n", "%matplotlib inline\n", "%config InlineBackend.figure_format = 'retina'\n", "\n", "from banjo import abtest, utils # see: go/pya\n", "from banjo.abtest.report import (\n", "    Metric, MetricTable, Report, get_quest_metric_table,\n", "    get_abtest_console_metric_table,\n", ")\n", "from banjo.abtest.cheetah_report import CheetahReport\n", "from banjo.abtest.cohort_report import CohortReport\n", "from banjo.abtest.cohort_report import CustomReport\n", "from banjo.abtest.field_breakdown_metric_table import FieldBreakdownMetricTable\n", "\n", "from banjo.abtest.quest import TIER_ONE\n", "\n", "PROJECT = 'content-husky'\n", "\n", "# PROJECT = 'context-dev'\n", "sns.set(style='whitegrid', font_scale=1.5)\n", "\n", "from IPython.core.display import display, HTML\n", "display(HTML(\"<style>.output_html.rendered_html table { font-size:8pt;}</style>\"))\n", "\n", "from banjo.teams.personalization.abtest import friend_feed_ab_metrics as fam\n", "from banjo.teams.personalization.abtest import friend_feed_ab_utils as fau\n", "from banjo.teams.personalization.abtest import discover_feed_metrics as dfm\n", "from banjo.teams.personalization.abtest import page_session_metrics as psm\n", "from banjo.teams.personalization.abtest import discover_feed_ab_utils as dfab\n", "from banjo.teams.personalization.abtest import df_user_funnel_metrics as dfuf\n", "from banjo.teams.personalization.abtest import mixed_feed_ab_metrics as mfm\n", "from banjo.teams.personalization.abtest import spotlight_ab_metrics as sabm\n", "from banjo.teams.personalization.abtest import story_metrics as sm\n", "from banjo.teams.personalization.abtest import story_post_send_metrics as spsm\n", "from banjo.teams.personalization.abtest import winners_losers\n", "from banjo.teams.personalization.abtest import update_metrics_name as umn\n", "from banjo.teams.personalization.abtest import content_freshness_diversity_metrics as cfdm\n", "from banjo.utils.gbq import check_job_status"]}, {"cell_type": "code", "execution_count": 75, "metadata": {"ExecuteTime": {"end_time": "2025-04-23T18:47:57.543243Z", "start_time": "2025-04-23T18:47:57.541502Z"}}, "outputs": [], "source": ["# # FOR DEBUGGING PURPOSE ONLY\n", "# from importlib import reload\n", "# reload(dfm);reload(dfab);reload(sm);reload(cfdm);reload(umn)\n", "# # umn.print_metric_list(report)"]}, {"cell_type": "code", "execution_count": 76, "metadata": {"ExecuteTime": {"end_time": "2025-04-23T18:47:57.567729Z", "start_time": "2025-04-23T18:47:57.566278Z"}}, "outputs": [], "source": ["##################\n", "## Notebook Config\n", "##################"]}, {"cell_type": "code", "execution_count": 77, "metadata": {"ExecuteTime": {"end_time": "2025-04-23T18:47:57.598338Z", "start_time": "2025-04-23T18:47:57.589815Z"}, "tags": ["parameters"]}, "outputs": [], "source": ["# ToDo: only keep UI parameters in this cell. Not other metric lists.\n", "\n", "# Specify study details\n", "STUDY_NAME = 'SIMPLE_SNAPCHAT_IOS_PARENT_2__126959'\n", "STUDY_START_DATE = '20250210'\n", "STUDY_END_DATE = '20250211'\n", "\n", "RETRO_START_DATE = None\n", "RETRO_END_DATE = None\n", "FIX_COHORT_END_DATE = None\n", "USE_ALLOCATION_MAPPING = False\n", "\n", "# Determine whether the study is in the ranking A/B pipeline\n", "IS_RANKING_PIPELINE = False\n", "\n", "COHORT_DATE = None\n", "\n", "USER_BREAKDOWN_LIST = []\n", "\n", "# Determine whether we need to apply country filter\n", "IS_COUNTRY_FILTER = False # ToDo: this is hardcoded, not a UI input, therefore CustomReport is never triggered, only CohortReport. Decide if this is the way forward and simplify code.\n", "\n", "COUNTRIES = [ \n", "        \"BR\",\n", "        \"FR\",\n", "        \"DE\",\n", "        \"IN\",\n", "        \"SA\",\n", "        \"GB\",\n", "        \"US\"\n", "]\n", "\n", "CONTROL_ID = '1' #\n", "TREATMENT_IDS = ['2']\n", "\n", "SHOW_TREND_PLOT_IN_ABSOLUTE = False\n", "\n", "INCLUDE_DAILY_RESULTS = True\n", "\n", "EXTRA_AB_CONSOLE_METRICS = []\n", "\n", "OVERWRITE = False\n", "SKIP_EXPORT = False\n", "\n", "KEY_METRIC_TYPE = 'story' #  'story',\n", "\n", "METRICS_GROUP_CONFIG = [\n", "#     'app_metrics',\n", "#     'df_session_metrics',\n", "    'topline_metrics',\n", "#     'df_story_metrics_by_section',\n", "#     'story_view_depth_metrics',\n", "#     'story_view_navigation_metrics',\n", "#     'df_story_metrics_by_item_type',\n", "#     'pSuggestive_metrics',\n", "#     'freshness_metrics',\n", "#     'upnext_metrics',\n", "#     'df_nfs_impression_tap_ctr_metrics',\n", "#     'df_fs_impression_tap_ctr_metrics',\n", "#     'carousel_impression_tap_ctr_metrics',\n", "#     'interaction_metrics',\n", "#     'notification_opt_metrics',\n", "#     'df_story_metrics_by_page_type',\n", "#     'df_story_metrics_by_page_tab_type',\n", "#     'df_story_metrics_by_feed_type',\n", "#     'story_metrics_by_item_type_and_page_tab_type',\n", "#     'story_metrics_by_item_type_and_section_type',\n", "#     'story_metrics_by_item_type_and_feed_type',\n", "#     'story_metrics_by_pos',\n", "#     'story_engagement_metrics',\n", "#     'addt_story_engagement_metrics',    \n", "#     'story_engagement_positional_metrics',\n", "#     'df_friend_story_ranking_metrics',\n", "#     'story_funnel_metrics',\n", "#     'performance_metrics', \n", "#     'extra_ab_console_metrics',\n", "#     'custom_story_creation_deletion_metrics', \n", "#     'deep_metrics',\n", "#     'df_imp_share_metrics',\n", "#     'df_session_over_session_overlap_metrics',\n", "#     'df_day_over_day_overlap_metrics',\n", "#     'l14_metrics',\n", "#     'story_reply_metrics',\n", "#     'publisher_attachment_metrics',\n", "#     'opera_session_metrics',\n", "#     'story_impression_and_ctr_by_position',\n", "#     \"creator_metrics_overall\",\n", "#     \"creator_metrics_by_creator_tier\",\n", "#     \"creator_metrics_by_creator_subtier\",\n", "#     \"creator_metrics_by_content_type\",\n", "#     \"creator_metrics_by_feed_type\",\n", "#     \"creator_metrics_by_ca_score\",\n", "#     \"creator_metrics_by_good_bad_creator\",\n", "#     \"idau_metrics\",\n", "#     \"discover_feed_stories_carousel_performance_metrics\",\n", "#     \"revenue_weighted_view_time_metrics\"\n", "\n", "]\n", "\n", "story_sections = [\n", "    'DF_SUBSCRIPTION',\n", "    'DF_FOR_YOU',\n", "    'DF_FY_EXPLORATION',\n", "    'DF_FRIENDS_FOF',\n", "    'DF_FRIENDS_MY',\n", "    'SF_SPOTLIGHT',\n", "    'BADGING',\n", "    'NOTIFICATION',\n", "    'DEEP_LINK',\n", "    'IN_APP_NOTIFICATION',\n", "    'CHAT',\n", "    'SEARCH',\n", "    'PROFILE',\n", "    'MAP'\n", "    'FRIENDS_FEED',\n", "    'STORIES_CAROUSEL_CHAT_TAB',\n", "    'DF_FY_EXPLORATION',\n", "    'DF_FRIENDS',\n", "    'OTHER'\n", "]\n", "\n", "topline_slices = [\n", "    'ALL'\n", "]\n", "\n", "topline_metrics = [\n", "    'app_active_day',\n", "    'df_page_session',\n", "    'df_page_session_w_view',\n", "    'content_story_view',\n", "    'content_story_view_120plus_seconds_active_day',\n", "    'friend_story_view',\n", "    'non_friend_story_view',\n", "    'spotlight_story_snap_view',\n", "    'friend_story_reply_active_day',\n", "    'friend_story_reply',\n", "    'non_spotlight_story_reply_or_comment',\n", "    'non_friend_story_reply_or_comment',\n", "    'content_combined_ad_impression',\n", "    'friend_story_combined_ad_impression',\n", "    'non_friend_story_combined_ad_impression',\n", "    'spotlight_story_combined_ad_impression',\n", "    'friend_story_snap_post',\n", "    'content_story_view_time',\n", "    'non_friend_story_view_time',\n", "    'friend_story_snap_view_time',\n", "    'spotlight_story_view_time',\n", "    'non_friend_ugc_story_view',\n", "    'non_friend_ugc_story_view_time',\n", "    'non_friend_premium_story_view',\n", "    'non_friend_premium_story_view_time',\n", "]\n", "\n", "story_item_type_subscribed = [\n", "    'non_friend_story_subscribed',\n", "    'non_friend_story_nonsubscribed',\n", "    'ugc_subscribed',\n", "    'pc_subscribed',\n", "    'ugc_nonsubscribed',\n", "    'pc_nonsubscribed',\n", "    'spotlight_subscribed',\n", "    'official_subscribed',\n", "    'popular_subscribed',\n", "    'publisher_subscribed',\n", "    'public_subscribed',\n", "    'show_subscribed',\n", "    'original_subscribed',\n", "    'spotlight_subscribed',\n", "    'saved_stories_subscribed',\n", "    'saved_stories_public_subscribed',\n", "    'saved_stories_official_subscribed',\n", "    'spotlight_nonsubscribed',\n", "    'official_nonsubscribed',\n", "    'popular_nonsubscribed',\n", "    'publisher_nonsubscribed',\n", "    'public_nonsubscribed',\n", "    'show_nonsubscribed',\n", "    'original_nonsubscribed',\n", "    'spotlight_nonsubscribed',\n", "    'saved_stories_nonsubscribed',\n", "    'saved_stories_public_nonsubscribed',\n", "    'saved_stories_official_nonsubscribed',\n", "]\n", "\n", "story_item_types = [\n", "    'OFFICIAL',\n", "    'POPULAR',\n", "    'PUBLIC',\n", "    'PROMOTED',\n", "    'PUBLISHER',\n", "    'SHOW',\n", "    'ORIGINAL',\n", "    'SAVED_STORIES',\n", "    'FRIEND',\n", "    'PRIVATE',\n", "    'CUSTOM',\n", "    'SHARED',\n", "    'SHARED_COMMUNITY',\n", "    'SPOTLIGHT',\n", "    'NON_FRIEND_STORY',\n", "    'NON_FRIEND_STORY_PC',\n", "    'NON_FRIEND_STORY_UGC',\n", "    'OTHER'\n", "]\n", "\n", "story_feed_types = [\n", "    'MIXED_FEED',\n", "    'OTHER_DISCOVER_FEED',\n", "    'OTHER_SPOTLIGHT_FEED',\n", "    'OTHER_OTHER',\n", "]\n", "\n", "story_page_types = [\n", "    'DISCOVER_FEED',\n", "    'SPOTLIGHT_FEED',\n", "    'PUBLIC_PROFILE',\n", "    'CONTENT_DEEP_LINK',\n", "    'FRIEND_PROFILE',\n", "    'CHAT_FEED',\n", "    'CHAT',\n", "    'SEARCH',\n", "    'CONTEXT_MENU',\n", "    'OTHER'\n", "]\n", "\n", "story_page_tab_types = [\n", "    'DISCOVER',\n", "    'SPOTLIGHT',\n", "    'CHAT',\n", "    'CAMERA',\n", "    'MAPS',\n", "    'SPOTLIGHT_OR_DISCOVER',\n", "    'CHAT_OR_MAPS',\n", "    'OTHER'\n", "]\n", "\n", "df_story_signals = [\n", "    'impression',\n", "    'long_impression',\n", "    'story_view',\n", "    'story_view_from_notification',\n", "#     'opt_into_notification',\n", "#     'opt_out_of_notification',\n", "    'snap_view',\n", "    'story_view_time',\n", "#     'quality_story_view_time',\n", "    'quality_story_view',\n", "    'under_half_second_story_view',\n", "    '30_plus_second_story_view',\n", "    '5_plus_second_story_view',\n", "    'under_5_second_story_view',\n", "    'story_complete_view',\n", "#     'auto_advance_story_view',\n", "    'swipe_story_view',\n", "#     'share_externally',\n", "#    'ctr',\n", "    'report',\n", "#    'quality_story_view_ratio',\n", "#    'zero_view_time_story_view_ratio',\n", "    'story_view_subscribed',\n", "    'story_view_nonsubscribed',\n", "    'story_view_time_subscribed',\n", "    'story_view_time_nonsubscribed',\n", "    'default_metrics',\n", "    'extended_subscription_metrics',\n", "#    'extended_story_quality_metrics'\n", "]\n", "\n", "imp_share_sections = [\n", "    'df_friends',\n", "    'df_subscriptions',\n", "    'df_for_you',\n", "    'spotlight_5th',\n", "]\n", "\n", "imp_share_signals = [\n", "    'item_top1_inequality_index',\n", "    'item_top4_inequality_index',\n", "    'account_top1_inequality_index',\n", "    'account_top4_inequality_index',\n", "    'item_top1_unique_items',\n", "    'item_top4_unique_items',\n", "    'account_top1_unique_items',\n", "    'account_top4_unique_items',\n", "    'item_top1_top_share',\n", "    'item_top4_top_share',\n", "    'account_top1_top_share',\n", "    'account_top4_top_share',\n", "    'pct_single_top1_item',\n", "    'pct_single_top4_item',\n", "    'pct_single_top1_account',\n", "    'pct_single_top4_account',\n", "]\n", "\n", "sos_overlap_sections = [\n", "    'df_friends',\n", "    'df_subscriptions',\n", "    'df_for_you',\n", "    'spotlight_5th',\n", "]\n", "\n", "dod_overlap_sections = [\n", "    'df_friends',\n", "    'df_subscriptions',\n", "    'df_for_you',\n", "    'spotlight_5th',\n", "]\n", "\n", "sos_overlap_signals = [\n", "    'item_top1_overlap',\n", "    'item_top4_overlap',\n", "    'item_top4_rankdiff',\n", "    'account_top1_overlap',\n", "    'account_top4_overlap',\n", "    'account_top4_rankdiff',\n", "    'pct_top4_nonexistent',\n", "    'page_session_counts',\n", "    'active_days',\n", "]\n", "\n", "dod_overlap_signals = [\n", "    'item_top1_overlap_dod',\n", "    'item_top4_overlap_dod',\n", "    'account_top1_overlap_dod',\n", "    'account_top4_overlap_dod',\n", "]\n", "\n", "ctr_metric_list = [\n", "    'long_impression_pos_0',\n", "    'long_impression_pos_1',\n", "    'long_impression_pos_2',\n", "    'long_impression_pos_3',\n", "    'long_impression_pos_4_plus',\n", "    'story_views_pos_0',\n", "    'story_views_pos_1',\n", "    'story_views_pos_2',\n", "    'story_views_pos_3',\n", "    'story_views_pos_4_plus',\n", "    'ctr_pos_0',\n", "    'ctr_pos_1',\n", "    'ctr_pos_2',\n", "    'ctr_pos_3',\n", "    'ctr_pos_4_plus',\n", "]\n", "ctr_item_type_list = [\n", "    'friend',\n", "    'private',\n", "    # 'my',\n", "    'shared',\n", "    'community',\n", "]\n", "friend_story_type_list = [\n", "    'all_friend',\n", "    'df_friend',\n", "    'my_story',\n", "    'private_story',\n", "    'shared_story',\n", "    'community_story',\n", "]\n", "\n", "\n", "USE_BATCH_BQ_PRIORITY = True\n", "QUANTILES = ['50', '90']\n", "\n", "IS_RANKING_PIPELINE = False\n", "\n", "COMPUTE_RETRO_AA = False\n", "CUPED = False\n", "CUSTOM_MAPPING = False\n", "CUSTOM_MAPPING_SQL = \"\"\n", "CUSTOM_MAPPING_SQL_COLUMNS = \"\"\n", "PERIODS = None # [\"CUPED\", \"AA\", \"AB\"]\n", "\n", "\n", "WL_KEY = None\n", "SHOW_WINNERS_LOSERS = \"no_winners_losers\"\n", "WL_FILTER = None\n", "WL_DIMENSIONS = None\n", "WL_STORY_ORDER_BY = []\n", "WL_STORY_DISPLAY_METRICS = None\n", "WL_TILE_ORDER_BY = []\n", "WL_TILE_DISPLAY_METRICS = None\n", "CONTROL_THRESHOLD = None\n", "TREATMENT_THRESHOLD = None\n", "WL_ENGAGEMENT_TYPE = None"]}, {"cell_type": "code", "execution_count": 78, "metadata": {"ExecuteTime": {"end_time": "2025-04-23T18:47:57.613896Z", "start_time": "2025-04-23T18:47:57.612003Z"}}, "outputs": [], "source": ["starting_date = datetime.strptime(STUDY_START_DATE, \"%Y%m%d\")\n", "ending_date = datetime.strptime(STUDY_END_DATE, \"%Y%m%d\")\n", "\n", "date_14_days_before_end = ending_date - <PERSON><PERSON><PERSON>(days=14)\n", "\n", "if starting_date < date_14_days_before_end:\n", "    raise ValueError(\"Please limit the Husky run to 14 days for performance and cost purposes\")"]}, {"cell_type": "code", "execution_count": 79, "metadata": {"ExecuteTime": {"end_time": "2025-04-23T18:47:57.625131Z", "start_time": "2025-04-23T18:47:57.622980Z"}}, "outputs": [], "source": ["default_metrics_list = [\"story_view\", \"snap_view\", \"story_view_time\"]\n", "extended_subscription_metrics_list = [\"impression_subscribed\", \"impression_nonsubscribed\", \"long_impression_subscribed\", \"long_impression_nonsubscribed\", \"tap_story_view_subscribed\", \"tap_story_view_nonsubscribed\", \"favorite_subscribed\", \"favorite_nonsubscribed\"]\n", "\n", "if 'default_metrics' in df_story_signals:\n", "    df_story_signals.extend([item for item in default_metrics_list if item not in df_story_signals])\n", "\n", "if 'extended_subscription_metrics' in df_story_signals:\n", "    df_story_signals.extend([item for item in extended_subscription_metrics_list if item not in df_story_signals])"]}, {"cell_type": "code", "execution_count": 80, "metadata": {"ExecuteTime": {"end_time": "2025-04-23T18:47:57.650776Z", "start_time": "2025-04-23T18:47:57.648848Z"}}, "outputs": [], "source": ["story_page_tab_types = [\"SPOTLIGHT_TAB\" if element == \"SPOTLIGHT\" else (\"CHAT_TAB\" if element == \"CHAT\" else element) for element in story_page_tab_types]\n", "story_page_types = [\"SEARCH_PAGE_TYPE\" if element == \"SEARCH\" else (\"CHAT_PAGE_TYPE\" if element == \"CHAT\" else element) for element in story_page_types]\n", "story_sections = [\"SEARCH_SECTION\" if element == \"SEARCH\" else (\"CHAT_SECTION\" if element == \"CHAT\" else element) for element in story_sections]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## testing params ##\n", "# STUDY_NAME = \"DF_MKTPLC_FY_SUBS_6__91942\"\n", "# STUDY_VERSION = \"1\"\n", "# STUDY_START_DATE = \"20240423\"\n", "# STUDY_END_DATE = \"20240423\"\n", "# CONTROL_ID = \"1\"\n", "# TREATMENT_IDS = [\"2\"]\n", "#\n", "# USER_BREAKDOWN_LIST = []\n", "# KEY_METRIC_TYPE = \"discover_feed\"\n", "# COUNTRIES = [\"FR\", \"DE\", \"IN\", \"SA\", \"GB\"]\n", "# METRICS_GROUP_CONFIG = [\"content_sharing_metrics\", \"df_story_metrics_by_section\"]\n", "# SHOW_WINNERS_LOSERS = \"no_winners_losers\"\n", "# WL_ORDER_BY = \"num_snap_views\"\n", "# WL_DISPLAY_METRICS = [\"num_snap_views\", \"story_view_time\", \"net_subscription\", \"num_viewers\", \"num_long_impression\", \"num_story_views\"]\n", "# COHORT_DATE = \"20240409\"\n", "# df_story_sections = [\"df_non_friend_sections\", \"df_subscription_section\", \"df_for_you_section\"]\n", "# df_story_item_types = [\"official\", \"popular\", \"public\", \"original\", \"show\", \"publisher\", \"saved_stories\", \"saved_stories_official\", \"saved_stories_public\", \"promoted\", \"friend\", \"pc\", \"ugc\"]\n", "# df_story_item_type_status = [\"subscribed\", \"ugc_subscribed\", \"pc_subscribed\", \"df_official_subscribed\", \"df_popular_subscribed\", \"df_public_subscribed\", \"df_original_subscribed\", \"df_show_subscribed\", \"df_publisher_subscribed\", \"recommended\", \"ugc_recommended\", \"pc_recommended\", \"df_official_recommended\", \"df_popular_recommended\", \"df_original_recommended\", \"df_saved_stories_recommended\", \"df_saved_stories_public_recommended\", \"df_saved_stories_official_recommended\", \"df_show_recommended\", \"df_publisher_recommended\"]\n", "# content_types = [\"ugc_subscribed\", \"pc_subscribed\", \"subscribed\", \"ugc_recommended\", \"pc_recommended\", \"recommended\"]\n", "# df_story_signals = [\"impression\", \"long_impression\", \"story_view\", \"snap_view\", \"story_view_time\", \"quality_story_view_time\", \"accidental_story_view\", \"skip_story_view\", \"quality_story_view\", \"deep_story_view\", \"long_story_view\", \"short_story_view\", \"story_complete_view\", \"tap_story_view\", \"favorite\", \"hide\", \"unfavorite\", \"share\", \"subscription\", \"unsubscription\", \"share_externally\", \"ctr\", \"quality_story_view_ratio\", \"zero_view_time_story_view_ratio\"]\n", "# imp_share_sections = [\"df_subscriptions\", \"df_for_you\"]\n", "# sos_overlap_sections = [\"df_subscriptions\", \"df_for_you\"]\n", "# dod_overlap_sections = [\"df_subscriptions\", \"df_for_you\"]\n", "#\n", "# USE_BATCH_BQ_PRIORITY = False\n", "# PROJECT = 'sc-bq-gcs-billingonly'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# from datetime import datetime\n", "# from pytz import timezone\n", "# import pytz\n", "# import time\n", "\n", "# nw = datetime.now(tz=pytz.utc)\n", "# nw = nw.astimezone(timezone('US/Pacific'))\n", "# today1pm = nw.replace(hour=13, minute=0, second=0, microsecond=0)\n", "# today8am = nw.replace(hour=8, minute=0, second=0, microsecond=0)\n", "# if today8am < nw < today1pm:\n", "#     time.sleep((today1pm-nw).total_seconds())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["abtest.report.notebook_print(\"Content A/B Test Analysis Template\", \"h1\")\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This notebook template is based on cloud notebook (go/vellum) and Snap's internal Python analytics library ([banjo](http://go.sc-corp.net/pya)). The result is consistent with ab console with visualizations and result summaries. The output consists of the following information\n", "  * Summary Tables with Visualizations\n", "  * Breakdowns by user cohorts\n", "\n", "**You can generate A/B analysis report using this notebook at go/husky.**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["study_start_date = STUDY_START_DATE\n", "study_end_date   = STUDY_END_DATE"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#################\n", "## Metrics Config\n", "#################"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON> may pass an empty string\n", "if FIX_COHORT_END_DATE is not None and FIX_COHORT_END_DATE.strip() == \"\":\n", "    FIX_COHORT_END_DATE = None\n", "\n", "# Convert to PST midnight\n", "STUDY_START_TS_PST = pd.to_datetime(study_start_date)\n", "STUDY_END_TS_PST = pd.to_datetime(study_end_date)\n", "FIX_COHORT_END_TS_PST = pd.to_datetime(FIX_COHORT_END_DATE)\n", "\n", "# Ensure certain items passed from husky are lists\n", "def ensure_list(parameter):\n", "    if isinstance(parameter, list):\n", "        return parameter\n", "    else:\n", "        return [parameter]\n", "\n", "story_sections = ensure_list(story_sections)\n", "story_item_types = ensure_list(story_item_types)\n", "story_page_types = ensure_list(story_page_types)\n", "topline_metrics = ensure_list(topline_metrics)\n", "story_page_tab_types = ensure_list(story_page_tab_types)\n", "story_item_type_subscribed = ensure_list(story_item_type_subscribed)\n", "df_story_signals = ensure_list(df_story_signals)\n", "\n", "if WL_KEY == 'story_id':\n", "    WL_ORDER_BY = WL_STORY_ORDER_BY\n", "    WL_DISPLAY_METRICS = WL_STORY_DISPLAY_METRICS\n", "elif <PERSON>EY == 'tile_id':\n", "    WL_ORDER_BY = WL_TILE_ORDER_BY\n", "    WL_DISPLAY_METRICS = WL_TILE_DISPLAY_METRICS\n", "else:\n", "    WL_ORDER_BY = None\n", "    WL_DISPLAY_METRICS = None\n", "\n", "for ratio_metric in [\n", "        metric for metric in dfm.DEFAULT_METRICS if metric.get('metric_type', '') == 'ratio'\n", "    ]:\n", "    if ratio_metric['metric_name'] in df_story_signals:\n", "        for metric_component in ['numerator', 'denominator']:\n", "            if ratio_metric[metric_component] not in df_story_signals:\n", "                df_story_signals.append(ratio_metric[metric_component])\n", "\n", "METRICS_GROUP_CONFIG = ensure_list(METRICS_GROUP_CONFIG)\n", "TREATMENT_IDS = ensure_list(TREATMENT_IDS)\n", "COUNTRIES = ensure_list(COUNTRIES)\n", "WL_DIMENSIONS = ensure_list(WL_DIMENSIONS) if WL_DIMENSIONS is not None else None\n", "WL_DISPLAY_METRICS = ensure_list(WL_DISPLAY_METRICS)\n", "WL_ORDER_BY = ensure_list(WL_ORDER_BY)\n", "ALL_METRICS_GROUPS = {}\n", "WL_ENGAGEMENT_TYPE = ensure_list(WL_ENGAGEMENT_TYPE) if WL_ENGAGEMENT_TYPE is not None else None\n", "\n", "BQ_PRIORITY = \"BATCH\" if USE_BATCH_BQ_PRIORITY else \"INTERACTIVE\"\n", "\n", "IS_RANKING_PIPELINE = bool(IS_RANKING_PIPELINE)\n", "USE_ALLOCATION_MAPPING = bool(USE_ALLOCATION_MAPPING)\n", "\n", "COMPUTE_RETRO_AA = bool(COMPUTE_RETRO_AA)\n", "CUPED = bool(CUPED)\n", "CUSTOM_MAPPING = bool(CUSTOM_MAPPING)\n", "\n", "if CUSTOM_MAPPING is True and not CUSTOM_MAPPING_SQL:\n", "    raise ValueError(\"Custom Mapping SQL has to be filled when Custom Mapping is Selected.\")\n", "CUSTOM_MAPPING_SQL_COLUMNS = [s.strip() for s in CUSTOM_MAPPING_SQL_COLUMNS.split(',')]\n", "\n", "if PERIODS is None and CUPED is True:\n", "    PERIODS = [\"CUPED\"]\n", "<PERSON><PERSON> is None and not CUPED:\n", "    PERIODS = None\n", "else:\n", "    PERIODS = ensure_list(PERIODS)\n", "\n", "\n", "if WL_FILTER:\n", "    try:\n", "        WL_FILTER = json.loads(WL_FILTER)\n", "        sql_conditions = []\n", "        for key, value in WL_FILTER.items():\n", "            if isinstance(value, list):\n", "                # If the value is a list, use the IN syntax\n", "                value_list = ', '.join(f'\"{v}\"' for v in value)\n", "                condition = f\"{key} IN ({value_list})\"\n", "            else:\n", "                condition = f\"{key} = '{value}'\"\n", "            sql_conditions.append(condition)\n", "\n", "        filter_string = \"AND \" + \" AND \".join(sql_conditions)\n", "    except json.JSONDecodeError:\n", "        raise ValueError(\"Filter is not in JSON Format\")\n", "else:\n", "    filter_string = \"\"\n", "\n", "if SHOW_WINNERS_LOSERS != \"no_winners_losers\":\n", "    if not WL_DISPLAY_METRICS:\n", "        raise ValueError(\"WL_DISPLAY_METRICS is not Provided\")\n", "    if CONTROL_THRESHOLD and isinstance(CONTROL_THRESHOLD, str):\n", "        CONTROL_THRESHOLD = int(CONTROL_THRESHOLD)\n", "    if TREATMENT_THRESHOLD and isinstance(TREATMENT_THRESHOLD, str):\n", "        TREATMENT_THRESHOLD = int(TREATMENT_THRESHOLD)\n", "    if WL_ORDER_BY[0] not in WL_DISPLAY_METRICS:\n", "        raise ValueError(\"Order Metrics should be part of Display Metrics\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if USE_ALLOCATION_MAPPING:\n", "    display(HTML(\n", "        \"\"\"\n", "        <h4> Allocation logging: </h4>\n", "        <p>The analysis is done using server allocation logging as you requested.\n", "        This means a user is included in the study as long as the user is targeted\n", "        by the A/B service regardless whether the code path using the A/B setting is triggered\n", "        for this user.\n", "        </p>\n", "        <p>\n", "        If your feature affects most Snapchat users, this analysis is close to the\n", "        exposure logging based analysis shown on the A/B console.\n", "\n", "        If your feature only affects a small subset of Snapchat users, this analysis\n", "        may show diluted impact of your feature.\n", "        </p>\n", "        \"\"\"\n", "\n", "    ))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### App Metrics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 'app_metrics' in METRICS_GROUP_CONFIG:\n", "    ALL_METRICS_GROUPS['app_metrics'] = dfab.get_app_metrics(study_start_date, study_end_date, use_quest=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Discover Metrics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["job_config = bigquery.QueryJobConfig(\n", "    priority=BQ_PRIORITY\n", ")\n", "\n", "client = bigquery.Client(PROJECT)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cur_time = datetime.now().strftime(\"%Y%m%d_%H%M%S\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# if CUPED:\n", "#     date_query = 'SELECT MIN(_TABLE_SUFFIX) AS min_date FROM `sc-portal.usermap_cumulative.{experiment}__*`'.format(experiment = STUDY_NAME)\n", "#     date_job = client.query(date_query)\n", "#     result = date_job.result().to_dataframe()\n", "#     aa_end = str(result['min_date'][0])\n", "#     aa_end_date = datetime.strptime(aa_end, '%Y%m%d')\n", "#     aa_start_date = aa_end_date - timed<PERSON>ta(days=7)\n", "#     aa_start = aa_start_date.strftime('%Y%m%d')\n", "#     query_str = \"WHERE _TABLE_SUFFIX BETWEEN '{start_date}' AND '{end_date}' OR _TABLE_SUFFIX BETWEEN '{aa_start}' AND '{aa_end}') a\".format(start_date = STUDY_START_DATE[2:], end_date = STUDY_END_DATE[2:], aa_start = aa_start, aa_end = aa_end)\n", "# else:\n", "#     query_str = \"WHERE _TABLE_SUFFIX BETWEEN '{start_date}' AND '{end_date}') a\".format(start_date = STUDY_START_DATE[2:], end_date = STUDY_END_DATE[2:])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if RETRO_START_DATE and RETRO_END_DATE and COMPUTE_RETRO_AA:\n", "    query_str = \"WHERE _TABLE_SUFFIX BETWEEN '{start_date}' AND '{end_date}' OR _TABLE_SUFFIX BETWEEN '{aa_start_date}' AND '{aa_end_date}') a\".format(start_date = STUDY_START_DATE[2:], end_date = STUDY_END_DATE[2:], aa_start_date = RETRO_START_DATE[2:], aa_end_date = RETRO_END_DATE[2:])\n", "else:\n", "    query_str = \"WHERE _TABLE_SUFFIX BETWEEN '{start_date}' AND '{end_date}') a\".format(start_date = STUDY_START_DATE[2:], end_date = STUDY_END_DATE[2:])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["table_query = \"\"\"\n", "CREATE OR REPLACE TABLE `sc-bq-gcs-billingonly.temp_abtest.content_husky_temp_{experiment}_{curtime}`\n", "CLUSTER BY ghost_user_id\n", "OPTIONS(\n", "  expiration_timestamp = TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL 2 DAY)\n", ")\n", "AS\n", "SELECT a.* FROM\n", "(SELECT\n", "  *,\n", "  _TABLE_SUFFIX as date_suffix\n", "  FROM `sc-analytics.report_discover_feed.user_item_type_level_interaction_20*`\n", "\"\"\" + query_str + \" JOIN (SELECT DISTINCT ghost_user_id FROM `sc-portal.usermap_cumulative.{experiment}__{end_date}`) b ON a.ghost_user_id = b.ghost_user_id\"\n", "\n", "query = table_query.format(experiment = STUDY_NAME, curtime = cur_time, end_date = STUDY_END_DATE)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query_job = client.query(query, job_config=job_config)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["filtered_template = dfm.DISCOVER_FEED_METRICS_TEMPLATE_USER_FILTERED.format(table_name = \"content_husky_temp_\" + STUDY_NAME + \"_\" + cur_time)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def _filter_config(configs, metric_prefixes, by_key=\"slice\"):\n", "    return [config for config in configs if config[by_key] in metric_prefixes]\n", "\n", "ALL_METRICS_GROUPS['df_session_metrics'] = dfm.discover_feed_page_session_metrics(start_date=study_start_date, end_date=study_end_date)\n", "negative_df_session_metrics = ['discover_feed_abandoned_sessions', 'discover_feed_abandoned_no_scroll_sessions',\n", "                              'discover_feed_no_long_imp_sessions']\n", "for metric in ALL_METRICS_GROUPS['df_session_metrics'][0].metrics:\n", "    if metric.col in negative_df_session_metrics:\n", "        metric.desired_direction = '<negative>'\n", "\n", "ALL_METRICS_GROUPS['topline_metrics'] = \\\n", "    dfm.discover_feed_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              sql_template=dfm.TOPLINE_USER_METRICS_TEMPLATE_USER,\n", "                              slices = _filter_config(dfm.TOPLINE_SLICES, topline_slices),\n", "                              metric_config_list=_filter_config(dfm.TOPLINE_METRICS, topline_metrics, by_key=\"metric_name\"),\n", "                              metric_group='topline_metrics',\n", "                              bq_dialect='standard',\n", "                             )\n", "ALL_METRICS_GROUPS['df_story_metrics_by_section'] = \\\n", "    dfm.discover_feed_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              sql_template=filtered_template,\n", "                              slices=_filter_config(dfm.SECTION_SLICES, story_sections),\n", "                              metric_config_list=_filter_config(dfm.DEFAULT_METRICS, df_story_signals, by_key=\"metric_name\"),\n", "                              metric_group='df_story_metrics_by_section',\n", "                              bq_dialect='standard',\n", "                             )\n", "ALL_METRICS_GROUPS['df_story_metrics_by_item_type'] = \\\n", "    dfm.discover_feed_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              sql_template=filtered_template,\n", "                              slices=_filter_config(dfm.ITEM_TYPE_SLICES, story_item_types),\n", "                              metric_config_list=_filter_config(dfm.DEFAULT_METRICS, df_story_signals, by_key=\"metric_name\"),\n", "                              metric_group='df_story_metrics_by_item_type',\n", "                              bq_dialect='standard',\n", "                              )\n", "ALL_METRICS_GROUPS['df_story_metrics_by_feed_type'] = \\\n", "    dfm.discover_feed_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              sql_template=filtered_template,\n", "                              slices=_filter_config(dfm.FEED_TYPE_SLICES, story_feed_types),\n", "                              metric_config_list=_filter_config(dfm.DEFAULT_METRICS, df_story_signals, by_key=\"metric_name\"),\n", "                              metric_group='df_story_metrics_by_feed_type',\n", "                              bq_dialect='standard',\n", "                              )\n", "ALL_METRICS_GROUPS['df_story_metrics_by_page_type'] = \\\n", "    dfm.discover_feed_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              sql_template=filtered_template,\n", "                              slices=_filter_config(dfm.PAGE_TYPE_SLICES, story_page_types),\n", "                              metric_config_list=_filter_config(dfm.DEFAULT_METRICS, df_story_signals, by_key=\"metric_name\"),\n", "                              metric_group='df_story_metrics_by_page_type',\n", "                              bq_dialect='standard',\n", "                              )\n", "ALL_METRICS_GROUPS['df_story_metrics_by_page_tab_type'] = \\\n", "    dfm.discover_feed_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              sql_template=filtered_template,\n", "                              slices=_filter_config(dfm.PAGE_TAB_TYPE_SLICES, story_page_tab_types),\n", "                              metric_config_list=_filter_config(dfm.DEFAULT_METRICS, df_story_signals, by_key=\"metric_name\"),\n", "                              metric_group='df_story_metrics_by_page_tab_type',\n", "                              bq_dialect='standard',\n", "                              )\n", "ALL_METRICS_GROUPS['story_metrics_by_item_type_and_page_tab_type'] = \\\n", "    dfm.discover_feed_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              sql_template=filtered_template,\n", "                              slices=dfm.ITEM_PAGE_TAB_SLICES,\n", "                              metric_config_list=_filter_config(dfm.DEFAULT_METRICS, df_story_signals, by_key=\"metric_name\"),\n", "                              metric_group='story_metrics_by_item_type_and_page_tab_type',\n", "                              bq_dialect='standard',\n", "                            )\n", "ALL_METRICS_GROUPS['story_metrics_by_item_type_and_section_type'] = \\\n", "    dfm.discover_feed_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              sql_template=filtered_template,\n", "                              slices=dfm.ITEM_SECTION_SLICES,\n", "                              metric_config_list=_filter_config(dfm.DEFAULT_METRICS, df_story_signals, by_key=\"metric_name\"),\n", "                              metric_group='story_metrics_by_item_type_and_section_type',\n", "                              bq_dialect='standard',\n", "                            )\n", "ALL_METRICS_GROUPS['story_metrics_by_item_type_and_feed_type'] = \\\n", "    dfm.discover_feed_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              sql_template=filtered_template,\n", "                              slices=dfm.ITEM_FEED_SLICES,\n", "                              metric_config_list=_filter_config(dfm.DEFAULT_METRICS, df_story_signals, by_key=\"metric_name\"),\n", "                              metric_group='story_metrics_by_item_type_and_feed_type',\n", "                              bq_dialect='standard',\n", "                            )\n", "\n", "ALL_METRICS_GROUPS['story_view_depth_metrics'] = dfm.discover_feed_metrics(\n", "    start_date=study_start_date,\n", "    end_date=study_end_date,\n", "    sql_template=filtered_template,\n", "    metric_config_list=dfm.STORY_VIEW_DEPTH_METRICS,\n", "    slices=dfm.PAGE_TYPE_SECTION_ITEM_TYPE_SLICES,\n", "    metric_group='story_view_depth_metrics',\n", "    bq_dialect='standard'\n", ")\n", "\n", "ALL_METRICS_GROUPS['story_view_navigation_metrics'] = dfm.discover_feed_metrics(\n", "    start_date=study_start_date,\n", "    end_date=study_end_date,\n", "    sql_template=filtered_template,\n", "    metric_config_list=dfm.STORY_VIEW_NAVIGATION_METRICS,\n", "    slices=dfm.PAGE_TYPE_SECTION_ITEM_TYPE_SLICES,\n", "    metric_group='story_view_navigation_metrics',\n", "    bq_dialect='standard'\n", ")\n", "\n", "\n", "ALL_METRICS_GROUPS['story_metrics_by_pos'] = dfm.discover_feed_metrics(\n", "    start_date=study_start_date,\n", "    end_date=study_end_date,\n", "    sql_template=filtered_template,\n", "    metric_config_list=dfm.BY_POSITION_METRICS,\n", "    slices=dfm.PAGE_TYPE_SECTION_ITEM_TYPE_SLICES,\n", "    metric_group='story_metrics_by_pos',\n", "    bq_dialect='standard'\n", ")\n", "\n", "ALL_METRICS_GROUPS['pSuggestive_metrics'] = dfm.discover_feed_metrics(\n", "    start_date=study_start_date,\n", "    end_date=study_end_date,\n", "    sql_template=filtered_template,\n", "    metric_config_list=dfm.PSUGGESTIVE_METRICS,\n", "    slices=[e for e in dfm.PAGE_TYPE_SECTION_ITEM_TYPE_SLICES if e['slice'] in [\n", "           # 'discover_feed_nfs',\n", "           'spotlight_feed_all_content',\n", "           # 'discover_feed_for_you_section_premium',\n", "           'spotlight_feed_premium',\n", "           'spotlight_feed_spotlight']]\n", "          + [{'slice': 'spotlight_feed_ugc_ephemeral', 'metric_filter': \"page_type ='SPOTLIGHT_FEED' AND item_type in ('PUBLIC', 'OFFICIAL', 'POPULAR')\"}]\n", "          + [{'slice': 'spotlight_feed_ugc_saved', 'metric_filter': \"page_type ='SPOTLIGHT_FEED' AND item_type in ('SAVED_STORY_OFFICIAL', 'SAVED_STORY_PUBLIC', 'SAVED_STORY')\"}]\n", "          # + [{'slice': 'discover_feed_for_you_section_ugc_ephemeral', 'metric_filter': \"COALESCE(triggering_section, section) = 'DF_FOR_YOU' AND page_type = 'DISCOVER_FEED' AND item_type in ('PUBLIC', 'OFFICIAL', 'POPULAR')\"}]\n", "          # + [{'slice': 'discover_feed_for_you_section_ugc_ephemeral_temp', 'metric_filter': \"COALESCE(triggering_section, section) = 'DF_FOR_YOU' AND item_type in ('PUBLIC', 'OFFICIAL', 'POPULAR')\"}]\n", "          # + [{'slice': 'discover_feed_for_you_section_ugc_saved', 'metric_filter': \"COALESCE(triggering_section, section) = 'DF_FOR_YOU' AND page_type = 'DISCOVER_FEED' AND item_type in ('SAVED_STORY_OFFICIAL', 'SAVED_STORY_PUBLIC', 'SAVED_STORY')\"}]\n", "    ,\n", "    metric_group='pSuggestive_metrics',\n", "    bq_dialect='standard'\n", ")\n", "\n", "for metric in ALL_METRICS_GROUPS['pSuggestive_metrics'][0].metrics:\n", "   metric.desired_direction = '<negative>'\n", "\n", "ALL_METRICS_GROUPS['freshness_metrics'] = dfm.discover_feed_metrics(\n", "    start_date=study_start_date,\n", "    end_date=study_end_date,\n", "    sql_template=filtered_template,\n", "    metric_config_list=dfm.FRESHNESS_METRICS,\n", "    slices=[e for e in dfm.PAGE_TYPE_SECTION_ITEM_TYPE_SLICES if e['slice'] in ['discover_feed_for_you_section_premium', 'discover_feed_subs_section_premium', 'spotlight_feed_premium', 'spotlight_feed_spotlight']] + [{'slice': 'spotlight_feed_non_ephemeral_content', 'metric_filter': \"page_type ='SPOTLIGHT_FEED' AND item_type in ('PU<PERSON>ISHER', 'SHOW', 'ORIGIN<PERSON>', 'SAVED_STORY', 'SAVED_STORY_PUBLIC', 'SAVED_STORY_OFFICIAL', 'COMMUNITY')\"}] + [{'slice': 'all_non_ephemeral_content', 'metric_filter': \"item_type in ('PUBLISHER', 'SHOW', 'OR<PERSON>IN<PERSON>', 'SAVED_STORY', 'SAVED_STORY_PUBLIC', 'SAVED_STORY_OFFICIAL', 'COMMUNITY')\"}],\n", "    metric_group='freshness_metrics',\n", "    bq_dialect='standard'\n", ")\n", "\n", "ALL_METRICS_GROUPS['upnext_metrics'] = dfm.discover_feed_metrics(\n", "    start_date=study_start_date,\n", "    end_date=study_end_date,\n", "    sql_template=filtered_template,\n", "    metric_config_list=dfm.UPNEXT_METRICS,\n", "    slices=[e for e in dfm.ITEM_TYPE_SLICES if e['slice'] in ['PUBLISHER','SHOW','ORIGINAL','OFFICIAL','POPULAR','SAVED_STORIES']],\n", "    metric_group='upnext_metrics',\n", "    bq_dialect='standard'\n", ")\n", "\n", "ALL_METRICS_GROUPS['df_nfs_impression_tap_ctr_metrics'] = dfm.discover_feed_metrics(\n", "    start_date=study_start_date,\n", "    end_date=study_end_date,\n", "    sql_template=filtered_template,\n", "    metric_config_list=dfm.IMPRESSION_TAP_CTR_METRICS,\n", "    slices=[e for e in dfm.ITEM_TYPE_SLICES if e['slice'] in ['NON_FRIEND_STORY','PUBLISHER','SHOW','ORIGINAL','OFFICIAL','POPULAR','PUBLIC','SAVED_STORIES', 'PROMOTED']] + [e for e in dfm.PAGE_TYPE_SECTION_ITEM_TYPE_SLICES if e['slice'] in ['discover_feed_nfs', 'discover_feed_for_you_section_nfs',  'discover_feed_subs_section_nfs']],\n", "    metric_group='df_nfs_impression_tap_ctr_metrics',\n", "    bq_dialect='standard'\n", ")\n", "\n", "ALL_METRICS_GROUPS['df_fs_impression_tap_ctr_metrics'] = dfm.discover_feed_metrics(\n", "    start_date=study_start_date,\n", "    end_date=study_end_date,\n", "    sql_template=filtered_template,\n", "    metric_config_list=[e for e in dfm.IMPRESSION_TAP_CTR_METRICS if not e['metric_name'].endswith('subscribed')],\n", "    slices=dfm.DF_FS_IMPRESSION_TAP_CTR_SLICES,\n", "    metric_group='df_fs_impression_tap_ctr_metrics',\n", "    bq_dialect='standard'\n", ")\n", "\n", "ALL_METRICS_GROUPS['carousel_impression_tap_ctr_metrics'] = dfm.discover_feed_metrics(\n", "    start_date=study_start_date,\n", "    end_date=study_end_date,\n", "    sql_template=filtered_template,\n", "    metric_config_list=dfm.IMPRESSION_TAP_CTR_METRICS,\n", "    slices=dfm.CAROUSEL_IMPRESSION_TAP_CTR_SLICES,\n", "    metric_group='carousel_impression_tap_ctr_metrics',\n", "    bq_dialect='standard'\n", ")\n", "\n", "ALL_METRICS_GROUPS['interaction_metrics'] = dfm.discover_feed_metrics(\n", "    start_date=study_start_date,\n", "    end_date=study_end_date,\n", "    sql_template=filtered_template,\n", "    metric_config_list=dfm.INTERACTION_METRICS,\n", "    slices=dfm.ITEM_TYPE_SLICES,\n", "    metric_group='interaction_metrics',\n", "    bq_dialect='standard'\n", ")\n", "\n", "ALL_METRICS_GROUPS['notification_opt_metrics'] = dfm.discover_feed_metrics(\n", "    start_date=study_start_date,\n", "    end_date=study_end_date,\n", "    sql_template=filtered_template,\n", "    metric_config_list=dfm.NOTIFICATION_OPT_METRICS,\n", "    slices=[e for e in dfm.PAGE_TYPE_SECTION_ITEM_TYPE_SLICES if e['slice'] not in ['chat_feed_story_carousel_section_fof', 'spotlight_feed_spotlight', 'discover_feed_friends_section_fof']],\n", "    metric_group='notification_opt_metrics',\n", "    bq_dialect='standard'\n", ")\n", "\n", "ALL_METRICS_GROUPS['content_sharing_metrics'] = dfm.content_sharing_metrics(\n", "    start_date=study_start_date,\n", "    end_date=study_end_date,\n", "    metric_table_name = 'content_sharing'\n", ")\n", "\n", "ALL_METRICS_GROUPS['df_imp_share_metrics'] = \\\n", "    cfdm.discover_feed_imp_share_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              section_config=_filter_config(cfdm.IMP_SHARE_SECTION_SLICES, imp_share_sections),\n", "                              metrics_config=_filter_config(cfdm.IMP_SHARE_METRICS, imp_share_signals, 'metric_name'),\n", "                              metric_table_name='df_imp_share_metrics'\n", "                             )\n", "ALL_METRICS_GROUPS['df_session_over_session_overlap_metrics'] = \\\n", "    cfdm.discover_feed_overlap_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              section_config=_filter_config(cfdm.OVERLAP_SECTION_SLICES, sos_overlap_sections),\n", "                              metrics_config=_filter_config(cfdm.OVERLAP_METRICS, sos_overlap_signals, 'metric_name'),\n", "                              metric_table_name='df_session_over_session_overlap_metrics'\n", "                             )\n", "ALL_METRICS_GROUPS['df_day_over_day_overlap_metrics'] = \\\n", "    cfdm.discover_feed_dod_overlap_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              section_config=_filter_config(cfdm.DoD_OVERLAP_SECTION_SLICES, dod_overlap_sections),\n", "                              metrics_config=_filter_config(cfdm.DoD_OVERLAP_METRICS, dod_overlap_signals, 'metric_name'),\n", "                              metric_table_name='df_day_over_day_overlap_metrics'\n", "                             )\n", "ALL_METRICS_GROUPS['opera_session_metrics'] = \\\n", "    dfm.discover_feed_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              sql_template=dfm.OPERA_SESSION_METRICS_TEMPLATE,\n", "                              slices=dfm.OPERA_SESSION_SLICES,\n", "                              metric_config_list=dfm.OPERA_SESSION_METRICS,\n", "                              metric_group='opera_session_metrics'\n", "                              )\n", "ALL_METRICS_GROUPS['discover_feed_stories_carousel_performance_metrics'] = \\\n", "    psm.page_session_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              metrics_config=psm.DF_MC_SESSION_HEALTH_CONFIG,\n", "                              metric_group_name='DF_MC_HEALTH'\n", "                              )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["non_friend_content_new_user_retention = []\n", "\n", "for ln in (1, 7, 14, 28):\n", "    non_friend_content_new_user_retention.append(\n", "        Metric(col=f'new_day_{ln}_retained', name=f'New Non-Friend Content Viewers ({ln} Days Ago) Retained Count', dist='cont'\n", "               , desired_direction='<positive>'))\n", "    non_friend_content_new_user_retention.append(\n", "        Metric(col=f'new_day_0_{ln}_day_ago', name=f'New Non-Friend Content Viewers ({ln} Days Ago) Overall Count', dist='cont'\n", "               , desired_direction='<positive>'))\n", "\n", "    non_friend_content_new_user_retention.append(\n", "        Metric(col=f'new_day_{ln}_retention_rate',\n", "             name=f'New Non-Friend Content Viewers ({ln} Days Ago) Retention Rate',\n", "             dist='ratio',\n", "             denominator=f'new_day_0_{ln}_day_ago',\n", "             numerator=f'new_day_{ln}_retained',\n", "             desired_direction='<positive>')\n", "    )\n", "\n", "    if ln in (14, 28):\n", "        non_friend_content_new_user_retention.append(\n", "        Metric(col=f'new_day_{ln}_graduated', name=f'New Non-Friend Content Viewers ({ln} Days Ago) Graduated to Healthy+ Count'\n", "               , dist='cont', desired_direction='<positive>'))\n", "\n", "        non_friend_content_new_user_retention.append(\n", "            Metric(col=f'new_day_{ln}_graduation_rate',\n", "                 name=f'New Non-Friend Content Viewers ({ln} Days Ago) Graduation Rate to Healthy+',\n", "                 dist='ratio',\n", "                 denominator=f'new_day_0_{ln}_day_ago',\n", "                 numerator=f'new_day_{ln}_graduated',\n", "                 desired_direction='<positive>')\n", "        )\n", "\n", "\n", "# generate sql query\n", "def generate_non_friend_content_new_user_retention_sql(study_start_date, study_end_date):\n", "    return \"\"\"\n", "        SELECT\n", "            ghost_user_id,\n", "            TIMESTAMP(DATE_ADD(PARSE_DATE('%Y%m%d', '{study_end_date}'), INTERVAL 1 day)) as ts,\n", "            max(case when yesterday_non_friend_content_user_segment = 'new_day_0' then 1 else null end) as new_day_0_1_day_ago,\n", "            max(case when yesterday_non_friend_content_user_segment = 'new_day_0'\n", "                                                                    then non_friend_content_retained else null end) as new_day_1_retained,\n", "            max(case when days_ago_7_non_friend_content_user_segment = 'new_day_0' then 1 else null end) as new_day_0_7_day_ago,\n", "            max(case when days_ago_7_non_friend_content_user_segment = 'new_day_0'\n", "                                                                    then non_friend_content_retained else null end) as new_day_7_retained,\n", "            max(case when days_ago_14_non_friend_content_user_segment = 'new_day_0' then 1 else null end) as new_day_0_14_day_ago,\n", "            max(case when days_ago_14_non_friend_content_user_segment = 'new_day_0'\n", "                                                                    then non_friend_content_retained else null end) as new_day_14_retained,\n", "            max(case when days_ago_14_non_friend_content_user_segment = 'new_day_0'\n", "                    and today_non_friend_content_user_segment in ('healthy', 'power') then 1\n", "                when days_ago_14_non_friend_content_user_segment = 'new_day_0'\n", "                    and today_non_friend_content_user_segment not in ('healthy', 'power') then 0 else null end) as new_day_14_graduated,\n", "            max(case when days_ago_28_non_friend_content_user_segment = 'new_day_0' then 1 else null end) as new_day_0_28_day_ago,\n", "            max(case when days_ago_28_non_friend_content_user_segment = 'new_day_0'\n", "                                                                    then non_friend_content_retained else null end) as new_day_28_retained,\n", "            max(case when days_ago_28_non_friend_content_user_segment = 'new_day_0'\n", "                    and today_non_friend_content_user_segment in ('healthy', 'power') then 1\n", "                when days_ago_28_non_friend_content_user_segment = 'new_day_0'\n", "                    and today_non_friend_content_user_segment not in ('healthy', 'power') then 0 else null end) as new_day_28_graduated,\n", "        FROM  `sc-analytics.report_content.user_level_retention_*`\n", "        WHERE _TABLE_SUFFIX BETWEEN '{study_start_date}' AND '{study_end_date}'\n", "        GROUP BY ghost_user_id\n", "    \"\"\".format(study_start_date=study_start_date,study_end_date=study_end_date)\n", "\n", "\n", "ALL_METRICS_GROUPS['non_friend_content_new_user_retention'] = [MetricTable(\n", "    sql_callable=generate_non_friend_content_new_user_retention_sql,\n", "    bq_dialect='standard',\n", "    metrics=non_friend_content_new_user_retention,\n", "    name='non_friend_content_new_user_retention'\n", ")]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["non_friend_content_activated_user_retention = []\n", "\n", "for ln in (1, 7, 14, 28):\n", "    non_friend_content_activated_user_retention.append(\n", "        Metric(col=f'activated_day_{ln}_retained', name=f'Activated Non-Friend Content Viewers ({ln} Days Ago) Retained Count', dist='cont'\n", "               , desired_direction='<positive>'))\n", "    non_friend_content_activated_user_retention.append(\n", "        Metric(col=f'activated_day_0_{ln}_day_ago', name=f'Activated Non-Friend Content Viewers ({ln} Days Ago) Overall Count', dist='cont'\n", "               , desired_direction='<positive>'))\n", "\n", "    non_friend_content_activated_user_retention.append(\n", "        Metric(col=f'activated_day_{ln}_retention_rate',\n", "             name=f'Activated Non-Friend Content Viewers ({ln} Days Ago) Retention Rate',\n", "             dist='ratio',\n", "             denominator=f'activated_day_0_{ln}_day_ago',\n", "             numerator=f'activated_day_{ln}_retained',\n", "             desired_direction='<positive>')\n", "    )\n", "\n", "    if ln in (14, 28):\n", "        non_friend_content_activated_user_retention.append(\n", "        Metric(col=f'activated_day_{ln}_graduated', name=f'Activated Non-Friend Content Viewers ({ln} Days Ago) Graduated to Healthy+ Count'\n", "               , dist='cont', desired_direction='<positive>'))\n", "\n", "        non_friend_content_activated_user_retention.append(\n", "            Metric(col=f'activated_day_{ln}_graduation_rate',\n", "                 name=f'Activated Non-Friend Content Viewers ({ln} Days Ago) Graduation Rate to Healthy+',\n", "                 dist='ratio',\n", "                 denominator=f'activated_day_0_{ln}_day_ago',\n", "                 numerator=f'activated_day_{ln}_graduated',\n", "                 desired_direction='<positive>')\n", "        )\n", "\n", "# generate sql query\n", "def generate_non_friend_content_activated_user_retention_sql(study_start_date, study_end_date):\n", "    return \"\"\"\n", "        with first_activated_dates as (\n", "            SELECT\n", "                ghost_user_id,\n", "                min(case when yesterday_non_friend_content_user_segment = 'activated_day_0' then _TABLE_SUFFIX end) as first_activated_1_day_ago,\n", "                min(case when days_ago_7_non_friend_content_user_segment = 'activated_day_0' then _TABLE_SUFFIX end) as first_activated_7_day_ago,\n", "                min(case when days_ago_14_non_friend_content_user_segment = 'activated_day_0' then _TABLE_SUFFIX end) as first_activated_14_day_ago,\n", "                min(case when days_ago_28_non_friend_content_user_segment = 'activated_day_0' then _TABLE_SUFFIX end) as first_activated_28_day_ago\n", "            FROM  `sc-analytics.report_content.user_level_retention_*`\n", "            WHERE _TABLE_SUFFIX BETWEEN '{study_start_date}' AND '{study_end_date}'\n", "            GROUP BY ghost_user_id\n", "        )\n", "        SELECT\n", "            ul_retention.ghost_user_id,\n", "            TIMESTAMP(DATE_ADD(PARSE_DATE('%Y%m%d', '{study_end_date}'), INTERVAL 1 day)) as ts,\n", "            max(case when yesterday_non_friend_content_user_segment = 'activated_day_0' and\n", "                ul_retention._TABLE_SUFFIX = first_activated_dates.first_activated_1_day_ago\n", "                                                                                    then 1 else null end) as activated_day_0_1_day_ago,\n", "            max(case when yesterday_non_friend_content_user_segment = 'activated_day_0' and\n", "                ul_retention._TABLE_SUFFIX = first_activated_dates.first_activated_1_day_ago\n", "                                                                then non_friend_content_retained else null end) as activated_day_1_retained,\n", "            max(case when days_ago_7_non_friend_content_user_segment = 'activated_day_0' and\n", "                ul_retention._TABLE_SUFFIX = first_activated_dates.first_activated_7_day_ago\n", "                                                                                    then 1 else null end) as activated_day_0_7_day_ago,\n", "            max(case when days_ago_7_non_friend_content_user_segment = 'activated_day_0' and\n", "                ul_retention._TABLE_SUFFIX = first_activated_dates.first_activated_7_day_ago\n", "                                                                then non_friend_content_retained else null end) as activated_day_7_retained,\n", "            max(case when days_ago_14_non_friend_content_user_segment = 'activated_day_0' and\n", "                ul_retention._TABLE_SUFFIX = first_activated_dates.first_activated_14_day_ago\n", "                                                                                    then 1 else null end) as activated_day_0_14_day_ago,\n", "            max(case when days_ago_14_non_friend_content_user_segment = 'activated_day_0' and\n", "                ul_retention._TABLE_SUFFIX = first_activated_dates.first_activated_14_day_ago\n", "                                                                then non_friend_content_retained else null end) as activated_day_14_retained,\n", "            max(case when days_ago_14_non_friend_content_user_segment = 'activated_day_0'\n", "                    and ul_retention._TABLE_SUFFIX = first_activated_dates.first_activated_14_day_ago\n", "                    and today_non_friend_content_user_segment in ('healthy', 'power') then 1\n", "                when days_ago_14_non_friend_content_user_segment = 'activated_day_0'\n", "                    and ul_retention._TABLE_SUFFIX = first_activated_dates.first_activated_14_day_ago\n", "                    and today_non_friend_content_user_segment not in ('healthy', 'power') then 0 else null end) as activated_day_14_graduated,\n", "            max(case when days_ago_28_non_friend_content_user_segment = 'activated_day_0' and\n", "                ul_retention._TABLE_SUFFIX = first_activated_dates.first_activated_28_day_ago\n", "                                                                                    then 1 else null end) as activated_day_0_28_day_ago,\n", "            max(case when days_ago_28_non_friend_content_user_segment = 'activated_day_0' and\n", "                ul_retention._TABLE_SUFFIX = first_activated_dates.first_activated_28_day_ago\n", "                                                                then non_friend_content_retained else null end) as activated_day_28_retained,\n", "            max(case when days_ago_28_non_friend_content_user_segment = 'activated_day_0'\n", "                    and ul_retention._TABLE_SUFFIX = first_activated_dates.first_activated_28_day_ago\n", "                    and today_non_friend_content_user_segment in ('healthy', 'power') then 1\n", "                when days_ago_28_non_friend_content_user_segment = 'activated_day_0'\n", "                    and ul_retention._TABLE_SUFFIX = first_activated_dates.first_activated_28_day_ago\n", "                    and today_non_friend_content_user_segment not in ('healthy', 'power') then 0 else null end) as activated_day_28_graduated,\n", "        FROM  `sc-analytics.report_content.user_level_retention_*` ul_retention\n", "            left join first_activated_dates\n", "                on ul_retention.ghost_user_id = first_activated_dates.ghost_user_id\n", "        WHERE _TABLE_SUFFIX BETWEEN '{study_start_date}' AND '{study_end_date}'\n", "        GROUP BY ghost_user_id\n", "    \"\"\".format(study_start_date=study_start_date,study_end_date=study_end_date)\n", "\n", "\n", "ALL_METRICS_GROUPS['non_friend_content_activated_user_retention'] = [MetricTable(\n", "    sql_callable=generate_non_friend_content_activated_user_retention_sql,\n", "    bq_dialect='standard',\n", "    metrics=non_friend_content_activated_user_retention,\n", "    name='non_friend_content_activated_user_retention'\n", ")]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Story Metrics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ALL_METRICS_GROUPS['story_engagement_metrics'] = \\\n", "    dfm.discover_feed_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              sql_template=sm.STORY_ENGAGEMENT_METRICS_TEMPLATE,\n", "                              slices=sm.STORY_ENGAGEMENT_SLICES,\n", "                              metric_config_list=sm.STORY_ENGAGEMENT_METRICS,\n", "                              metric_group='story_engagement_metrics'\n", "    )\n", "ALL_METRICS_GROUPS['addt_story_engagement_metrics'] = \\\n", "    dfm.discover_feed_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              sql_template=sm.STORY_ENGAGEMENT_METRICS_TEMPLATE,\n", "                              slices=sm.ADDT_STORY_ENGAGEMENT_SLICES,\n", "                              metric_config_list=sm.STORY_ENGAGEMENT_METRICS,\n", "                              metric_group='addt_story_engagement_metrics'\n", "    )\n", "ALL_METRICS_GROUPS['story_engagement_positional_metrics'] = \\\n", "    dfm.discover_feed_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              sql_template=sm.STORY_ENGAGEMENT_METRICS_TEMPLATE,\n", "                              slices=[e for e in sm.STORY_ENGAGEMENT_SLICES if e['slice'] == 'df_friend'],\n", "                              metric_config_list=sm.STORY_ENGAGEMENT_POSITIONAL_METRICS,\n", "                              metric_group='story_engagement_positional_metrics'\n", "    )\n", "# ALL_METRICS_GROUPS['df_friend_story_ranking_metrics']  = \\\n", "#     sm.story_funnel_metrics(study_start_date, study_end_date,\n", "#                             table_name='story_engagement_user_level_',\n", "#                             metrics_config=sm.DF_FRIEND_STORY_POS_INTERMEDIATE_METRICS,\n", "#                             ratio_metrics=sm.DF_FRIEND_STORY_RANKING_METRICS,\n", "#                             name='df_friend_story_ranking_metrics'\n", "#     )\n", "# todo move to parameters section\n", "ctr_metric_list = [\n", "    'long_impression_pos_0',\n", "    'long_impression_pos_1',\n", "    'long_impression_pos_2',\n", "    'long_impression_pos_3',\n", "    'long_impression_pos_4_plus',\n", "    'story_views_pos_0',\n", "    'story_views_pos_1',\n", "    'story_views_pos_2',\n", "    'story_views_pos_3',\n", "    'story_views_pos_4_plus',\n", "    'ctr_pos_0',\n", "    'ctr_pos_1',\n", "    'ctr_pos_2',\n", "    'ctr_pos_3',\n", "    'ctr_pos_4_plus',\n", "]\n", "ctr_item_type_list = [\n", "    'friend',\n", "    'private',\n", "    # 'my',\n", "    'shared',\n", "    'community',\n", "]\n", "friend_story_type_list = [\n", "    'all_friend',\n", "    'df_friend',\n", "    'my_story',\n", "    'private_story',\n", "    'shared_story',\n", "    'community_story',\n", "]\n", "ALL_METRICS_GROUPS['df_friend_story_ranking_metrics']  = \\\n", "    dfm.discover_feed_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              sql_template=sm.STORY_ENGAGEMENT_METRICS_TEMPLATE,\n", "                              slices=_filter_config(sm.STORY_ENGAGEMENT_SLICES, friend_story_type_list),\n", "                              metric_config_list=sm.DF_FRIEND_STORY_POS_METRICS,\n", "                              metric_group='df_friend_story_ranking_metrics'\n", "    )\n", "# todo add UI\n", "ALL_METRICS_GROUPS['story_impression_and_ctr_by_position']  = \\\n", "    dfm.discover_feed_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              sql_template=filtered_template,\n", "                              slices=_filter_config(dfm.ITEM_TYPE_SLICES, ctr_item_type_list),\n", "                              metric_config_list=_filter_config(dfm.BY_POSITION_METRICS, ctr_metric_list, by_key=\"metric_name\"),\n", "                              metric_group='story_impression_and_ctr_by_position',\n", "                              bq_dialect='standard'\n", "     )\n", "ALL_METRICS_GROUPS['story_post_metrics'] = \\\n", "    dfm.discover_feed_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              sql_template=sm.STORY_POST_METRICS_TEMPLATE,\n", "                              slices=sm.SNAP_POST_SLICES,\n", "                              metric_config_list=sm.SNAP_POST_METRICS,\n", "                              metric_group='story_post_metrics'\n", "    )\n", "ALL_METRICS_GROUPS['story_reply_metrics'] = \\\n", "    dfm.discover_feed_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              sql_template=sm.STORY_REPLY_METRICS_TEMPLATE,\n", "                              slices=sm.STORY_REPLY_SLICES,\n", "                              metric_config_list=sm.STORY_REPLY_METRICS,\n", "                              metric_group='story_reply_metrics'\n", "    )\n", "ALL_METRICS_GROUPS['publisher_attachment_metrics'] = \\\n", "    dfm.discover_feed_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              sql_template=sm.PUBLISHER_ATTACHMENT_METRICS_TEMPLATE,\n", "                              slices=sm.PUBLISHER_ATTACHMENT_SLICES,\n", "                              metric_config_list=sm.PUBLISHER_ATTACHMENT_METRICS,\n", "                              metric_group='publisher_attachment_metrics'\n", "    )\n", "ALL_METRICS_GROUPS['story_post_failure_metrics'] = spsm.failure_metrics(study_start_date, study_end_date)\n", "ALL_METRICS_GROUPS['story_post_latency_metrics'] = spsm.latency_metrics(study_start_date, study_end_date)\n", "ALL_METRICS_GROUPS['story_funnel_metrics'] = sm.story_funnel_metrics(study_start_date, study_end_date)\n", "ALL_METRICS_GROUPS['custom_story_creation_deletion_metrics']  = \\\n", "    sm.story_funnel_metrics(study_start_date, study_end_date,\n", "                            table_name='custom_story_creation_user_level_',\n", "                            metrics_config=sm.CUSTOM_STORY_CREATION_DELETION_METRICS,\n", "                            ratio_metrics=sm.CUSTOM_STORY_CREATION_DELETION_RATIO_METRICS,\n", "                            name='custom_story_creation_deletion_metrics'\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Performance Metrics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#Playback Metrics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 'performance_metrics' in METRICS_GROUP_CONFIG:\n", "    ab_quest_metrics = abtest.ABQuestMetricCatalog(\n", "        study_start_date, study_end_date\n", "    ).get_catalog()\n", "\n", "    ALL_METRICS_GROUPS['performance_metrics'] = [\n", "        fam.get_ff_ab_metrics_table(\n", "            start_ts=STUDY_START_TS_PST,\n", "            end_ts=STUDY_END_TS_PST,\n", "            table_name='ops_app',\n", "            core_metrics=[],\n", "            aux_metrics=['application_crash'],\n", "            ratio_metric_pairs=[],\n", "            daily=True),\n", "        ab_quest_metrics.content_delivery_cd_network_request_user,\n", "    ]\n", "\n", "    for metric in ALL_METRICS_GROUPS['performance_metrics'][0].metrics:\n", "        metric.desired_direction = '<negative>'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_playback_sql(query_table, start_dt, end_dt):\n", "    return \"\"\"\n", "    SELECT\n", "        ghost_user_id,\n", "        TIMESTAMP(DATE(DATE_ADD(event_time, -8, 'HOUR'))) as ts,\n", "        SUM(view_time_ms) as view_time_ms,\n", "        SUM(stall_duration_on_start_ms) as stall_duration_on_start_ms,\n", "        SUM(mid_playback_stall_duration_total_ms) as mid_stalls_ms,\n", "        (SUM(mid_playback_stall_duration_total_ms) + SUM(stall_duration_on_start_ms))/SUM(view_time_ms) as buffering_pct,\n", "        SUM(mid_playback_stall_count) as buffering_count,\n", "        SUM(stalled_on_start)/COUNT(*) as stall_ratio,\n", "        SUM(IF(stalled_on_exit=true, 1, 0))/COUNT(*) as pct_exit_on_loading\n", "\n", "    FROM\n", "    TABLE_DATE_RANGE([{query_table}_],\n", "\tTIMESTAMP(CONCAT(\"{start_dt}\", \" 00:00:00\")),\n", "\tTIMESTAMP(CONCAT(\"{end_dt}\", \" 00:00:00\")))\n", "\n", "    WHERE 1=1\n", "        AND os_type = \"iOS\"\n", "        AND event_name = \"OPERA_SNAP_PLAYBACK\"\n", "        AND media_type IN (\"VIDEO\", \"VIDEO_NO_SOUND\")\n", "        AND view_time_ms < 3600000\n", "        AND view_time_ms > 0\n", "    GROUP BY 1,2\n", "\n", "    \"\"\".format(query_table=query_table, start_dt=start_dt, end_dt=end_dt)\n", "\n", "playback_metrics = [Metric(col='view_time_ms', name='Watch Time',\n", "                    dist='cont'),\n", "                    Metric(col='stall_duration_on_start_ms', name='Time to First Frame',\n", "                    dist='cont', desired_direction='<negative>'),\n", "                    Metric(col='mid_stalls_ms', name='Buffering Time',\n", "                    dist='cont', desired_direction='<negative>'),\n", "                    Metric(col='buffering_pct', name='Buffering Ratio',\n", "                    dist='cont', desired_direction='<negative>'),\n", "                    Metric(col='buffering_count', name='Buffering Count',\n", "                    dist='cont', desired_direction='<negative>'),\n", "                    Metric(col='stall_ratio', name='Startup Stall Ratio',\n", "                    dist='cont', desired_direction='<negative>'),\n", "                    Metric(col='pct_exit_on_loading', name='Pct Exits on Loading',\n", "                    dist='cont', desired_direction='<negative>')]\n", "\n", "query_table = \"sc-analytics:prod_analytics_ops_playback.daily_events\"\n", "ALL_METRICS_GROUPS['playback_metrics'] = [MetricTable(\n", "    sql=generate_playback_sql(query_table=query_table, start_dt=study_start_date, end_dt=study_end_date),\n", "    metrics=playback_metrics,\n", "    name='playback_metrics'\n", ")]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# messagaing l14 metrics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# metrics = []\n", "l14_metrics = []\n", "sum_l14_query = []\n", "\n", "\n", "l14_mapping = [#\"replies_sent\":\"Replies Sent\",\n", "    (\"friend_story_story_view\",\"Friend Story Story View\"),\n", "    (\"user_story_story_view\",\"User Story Story View\"),\n", "    (\"private_story_story_view\",\"Private Story Story View\"),\n", "    (\"friend_story_snap_view\",\"Friend Story Snap View\"),\n", "    (\"user_story_snap_view\",\"User Story Snap View\"),\n", "    (\"private_story_snap_view\",\"Private Story Snap View\"),\n", "    (\"story_snap_time_viewed\",\"Stroy Snap Time Viewed\")]\n", "\n", "bucket_mapping = {\n", "    \"l14_0\":\"l14_00\",\n", "    \"l14_1_6\":\"l14_01_06\",\n", "    \"l14_7_13\":\"l14_07_13\",\n", "    \"l14_14\":\"l14_14\",\n", "    \"self\":\"l14_self\",\n", "}\n", "\n", "for i in ['l14_0','l14_1_6','l14_7_13','l14_14','self']:\n", "    for j,name in l14_mapping:\n", "#         metrics.append(f'{j}_{i}')\n", "        sum_l14_query.append(f' SUM({j}_{i}) AS {j}_{bucket_mapping[i]}')\n", "        l14_metrics.append(Metric(col=f'{j}_{bucket_mapping[i]}',name=name + f\" L14 {bucket_mapping[i]}\", dist='cont', desired_direction='<positive>'))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# rshen: adding friending closeness l14 metrics:\n", "# def: each user has 15 distinct l14 score: friends they talk for x days in the past 14 days (0,1,2,...14)\n", "\n", "# generate sql query\n", "def generate_l14_sql(study_start_date, study_end_date, sum_l14_query):\n", "    return \"\"\"\n", "        SELECT\n", "            ghost_user_id,\n", "            TIMESTAMP(DATE_ADD(PARSE_DATE('%Y%m%d', '{study_end_date}'), INTERVAL 1 day)) as ts,\n", "            {sum_l14_query}\n", "        FROM `sc-analytics.report_discover_feed.messaging_l14_story_view_user_level_*`\n", "        WHERE _TABLE_SUFFIX BETWEEN '{study_start_date}' AND '{study_end_date}'\n", "        GROUP BY ghost_user_id\n", "    \"\"\".format(study_start_date=study_start_date,study_end_date=study_end_date,sum_l14_query=sum_l14_query)\n", "\n", "\n", "ALL_METRICS_GROUPS['l14_metrics'] = [MetricTable(\n", "    sql=generate_l14_sql(study_start_date=study_start_date,\n", "                         study_end_date=study_end_date,\n", "                         sum_l14_query=\",\".join(sum_l14_query)),\n", "    bq_dialect='standard',\n", "    metrics=l14_metrics,\n", "    name='l14_metrics'\n", ")]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Extra AB Console Metrics\n", "\n", "# unclear if this works but it's not referenced anywhere"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 'extra_ab_console_metrics' in METRICS_GROUP_CONFIG:\n", "    ALL_METRICS_GROUPS['extra_ab_console_metrics'] = [\n", "        ab_metric_catalog.get(metric_name) for metric_name in EXTRA_AB_CONSOLE_METRICS\n", "        if metric_name in abtest.ABMetricCatalog(study_start_date, study_end_date).get_catalog_dict()\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### premium content deep metrics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if (STUDY_END_TS_PST -STUDY_START_TS_PST).days >= 7:\n", "    start_date = (STUDY_START_TS_PST + timedelta(days=6))\n", "    start_date = start_date.strftime('%Y%m%d')\n", "    ALL_METRICS_GROUPS['deep_metrics'] = dfm.premium_content_deep_user_metrics(start_date=start_date, end_date=study_end_date)\n", "else:\n", "    if 'deep_metrics' in METRICS_GROUP_CONFIG:\n", "        METRICS_GROUP_CONFIG.remove('deep_metrics')\n", "        display(HTML(\"<h3> Deep Metrics need experiment running 7-day or longger </h3>\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Creator Metrics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ALL_METRICS_GROUPS['creator_metrics_overall'] = \\\n", "    dfm.discover_feed_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              sql_template=dfm.FRESHNESS_CREATOR_TEMPLATE_QUERY,\n", "                              slices=[{\n", "                                  'slice':'OVERALL',\n", "                                  'metric_filter': 'ghost_user_id IS NOT NULL'\n", "                              }],\n", "                              metric_config_list=dfm.CREATOR_FRESHNESS_METRICS,\n", "                              metric_group='creator_metrics_overall',\n", "                             )\n", "ALL_METRICS_GROUPS['creator_metrics_by_creator_tier'] = \\\n", "    dfm.discover_feed_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              sql_template=dfm.FRESHNESS_CREATOR_TEMPLATE_QUERY,\n", "                              slices=dfm.CREATOR_TIER_SLICES,\n", "                              metric_config_list=dfm.CREATOR_FRESHNESS_METRICS,\n", "                              metric_group='creator_metrics_by_creator_tier',\n", "                             )\n", "ALL_METRICS_GROUPS['creator_metrics_by_creator_subtier'] = \\\n", "    dfm.discover_feed_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              sql_template=dfm.FRESHNESS_CREATOR_TEMPLATE_QUERY,\n", "                              slices=dfm.CREATOR_SUBTIER_SLICES,\n", "                              metric_config_list=dfm.CREATOR_FRESHNESS_METRICS,\n", "                              metric_group='creator_metrics_by_creator_subtier',\n", "                             )\n", "ALL_METRICS_GROUPS['creator_metrics_by_content_type'] = \\\n", "    dfm.discover_feed_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              sql_template=dfm.FRESHNESS_CREATOR_TEMPLATE_QUERY,\n", "                              slices=dfm.CONTENT_TYPE_SLICES,\n", "                              metric_config_list=dfm.CREATOR_FRESHNESS_METRICS,\n", "                              metric_group='creator_metrics_by_content_type',\n", "                             )\n", "ALL_METRICS_GROUPS['creator_metrics_by_feed_type'] = \\\n", "    dfm.discover_feed_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              sql_template=dfm.FRESHNESS_CREATOR_TEMPLATE_QUERY,\n", "                              slices=dfm.FRESHNESS_FEED_TYPE_SLICES,\n", "                              metric_config_list=dfm.CREATOR_FRESHNESS_METRICS,\n", "                              metric_group='creator_metrics_by_feed_type',\n", "                             )\n", "ALL_METRICS_GROUPS['creator_metrics_by_ca_score'] = \\\n", "    dfm.discover_feed_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              sql_template=dfm.FRESHNESS_CREATOR_TEMPLATE_QUERY,\n", "                              slices=dfm.CA_SCORE_SLICES,\n", "                              metric_config_list=dfm.CREATOR_FRESHNESS_METRICS,\n", "                              metric_group='creator_metrics_by_ca_score',\n", "                             )\n", "ALL_METRICS_GROUPS['creator_metrics_by_good_bad_creator'] = \\\n", "    dfm.discover_feed_metrics(start_date=study_start_date,\n", "                              end_date=study_end_date,\n", "                              sql_template=dfm.FRESHNESS_CREATOR_TEMPLATE_QUERY,\n", "                              slices=dfm.GOOD_BAD_CREATOR_SLICES,\n", "                              metric_config_list=dfm.CREATOR_FRESHNESS_METRICS,\n", "                              metric_group='creator_metrics_by_good_bad_creator',\n", "                             )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### yyan5 Adding iDAU proxy metrics to predict inspired potential future posting DAU\n", "# idau metric init\n", "idau_metrics = [\n", "    Metric(col='idau_1p', name='iDAU 1P', dist='cont', desired_direction='<positive>'),\n", "    Metric(col='idau_2p', name='iDAU 2P', dist='cont', desired_direction='<positive>'),\n", "    Metric(col='idau_3p', name='iDAU 3P', dist='cont', desired_direction='<positive>'),\n", "    Metric(col='idau_4p', name='iDAU 4P', dist='cont', desired_direction='<positive>'),\n", "    Metric(col='idau_5p', name='iDAU 5P', dist='cont', desired_direction='<positive>')\n", "]\n", "\n", "# sql to read the metric val\n", "### note that table inspired_potential_posting_dau_proxy_metric is designed 3d-delayed to avoid any upstream failures\n", "def generate_idau_sql(study_start_date, study_end_date):\n", "    return \"\"\"SELECT\n", "        ghost_user_id,\n", "        TIMESTAMP(DATE_ADD(PARSE_DATE('%Y%m%d', '{study_end_date}'), INTERVAL 1 day)) AS ts,\n", "        SUM(ippdau_1p) AS idau_1p,\n", "        SUM(ippdau_2p) AS idau_2p,\n", "        SUM(ippdau_3p) AS idau_3p,\n", "        SUM(ippdau_4p) AS idau_4p,\n", "        SUM(ippdau_5p) AS idau_5p\n", "      FROM\n", "        `sc-analytics.report_creator.inspired_potential_posting_dau_proxy_metric_*`\n", "      WHERE\n", "        _TABLE_SUFFIX BETWEEN '{study_start_date}'\n", "        AND '{study_end_date}'\n", "      GROUP BY\n", "        1\n", "    \"\"\".format(study_start_date=study_start_date, study_end_date=study_end_date)\n", "\n", "# add metric into the metric group\n", "ALL_METRICS_GROUPS['idau_metrics'] = [\n", "    MetricTable(\n", "        sql=generate_idau_sql(study_start_date=study_start_date, study_end_date=study_end_date),\n", "        metrics=idau_metrics,\n", "        name='idau_metrics',\n", "        bq_dialect='standard'\n", "    )\n", "]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### revenue weighted view time (rwvt)\n", "# rwvt metric init\n", "revenue_weighted_view_time_metrics = [\n", "    Metric(col='total_revenue_weighted_view_time', name='Total Revenue Weighted View Time', dist='cont', desired_direction='<positive>'),\n", "    Metric(col='nfs_revenue_weighted_view_time', name='NFS Revenue Weighted View Time', dist='cont', desired_direction='<positive>'),\n", "    Metric(col='fs_revenue_weighted_view_time', name='FS Revenue Weighted View Time', dist='cont', desired_direction='<positive>'),\n", "    Metric(col='spotlight_revenue_weighted_view_time', name='Spotlight Revenue Weighted View Time', dist='cont', desired_direction='<positive>'),\n", "]\n", "\n", "# sql to read the metric val\n", "def generate_rwvt_sql(study_start_date, study_end_date):\n", "    return \"\"\"SELECT\n", "        ghost_user_id,\n", "        TIMESTAMP(DATE_ADD(PARSE_DATE('%Y%m%d', '{study_end_date}'), INTERVAL 1 day)) AS ts,\n", "        SUM(total_revenue_weighted_view_time) AS total_revenue_weighted_view_time,\n", "        SUM(nfs_revenue_weighted_view_time) AS nfs_revenue_weighted_view_time,\n", "        SUM(fs_revenue_weighted_view_time) AS fs_revenue_weighted_view_time,\n", "        SUM(spotlight_revenue_weighted_view_time) AS spotlight_revenue_weighted_view_time,\n", "      FROM\n", "        `sc-analytics.prod_responsible_monetization.rev_weighted_view_time_*`\n", "      WHERE\n", "        _TABLE_SUFFIX BETWEEN '{study_start_date}'\n", "        AND '{study_end_date}'\n", "      GROUP BY ALL\n", "    \"\"\".format(study_start_date=study_start_date, study_end_date=study_end_date)\n", "\n", "\n", "# add metric into the metric group\n", "ALL_METRICS_GROUPS['revenue_weighted_view_time_metrics'] = [\n", "    MetricTable(\n", "        sql=generate_rwvt_sql(study_start_date=study_start_date, study_end_date=study_end_date),\n", "        metrics=revenue_weighted_view_time_metrics,\n", "        name='revenue_weighted_view_time_metrics',\n", "        bq_dialect='standard'\n", "    )\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["### Combine metrics according to config"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["metric_tables = [\n", "    metric_table\n", "    for sublist in [ALL_METRICS_GROUPS[mt] for mt in ALL_METRICS_GROUPS if mt in METRICS_GROUP_CONFIG]\n", "    for metric_table in sublist\n", "]\n", "\n", "for mt in metric_tables:\n", "    mt.daily = INCLUDE_DAILY_RESULTS\n", "    if (COMPUTE_RETRO_AA or CUPED) and not (IS_RANKING_PIPELINE) and mt.sql_callable is not None:\n", "        mt.aa, mt.cuped = COMPUTE_RETRO_AA, CUPED\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["##############################\n", "## Report Config and Execution\n", "##############################"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["###################\n", "## Configure Report\n", "###################"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if SHOW_WINNERS_LOSERS == \"no_winners_losers\":\n", "    # # Construct the report object\n", "    # if RETRO_START_DATE and RETRO_END_DATE:\n", "    #     for mt in metric_tables:\n", "    #         mt.hourly_granularity = False\n", "    #\n", "    #     exp_ids = [CONTROL_ID]+TREATMENT_IDS\n", "    #     breakdown_clauses = []\n", "    #     for breakdown_dimension in CohortReport.cohort_fields:\n", "    #        if (breakdown_dimension.name in USER_BREAKDOWN_LIST\n", "    #            and (breakdown_dimension.start_date is None or breakdown_dimension.start_date <= COHORT_DATE)):\n", "    #\n", "    #            breakdown_clauses.append('{} AS {}'.format(breakdown_dimension.sql, breakdown_dimension.name))\n", "    #\n", "    #     custom_mapping_sql = dfab.create_retro_mapping_table(\n", "    #         STUDY_NAME,\n", "    #         exp_ids,\n", "    #         STUDY_START_DATE,\n", "    #         STUDY_END_DATE,\n", "    #         RETRO_START_DATE,\n", "    #         RETRO_END_DATE,\n", "    #         IS_RANKING_PIPELINE,\n", "    #         breakdown_clauses,\n", "    #         COHORT_DATE\n", "    #     )\n", "    #     report = CustomReport(\n", "    #         study_name=STUDY_NAME,\n", "    #         study_start_date=RETRO_START_DATE,\n", "    #         study_end_date=RETRO_END_DATE,\n", "    #         metric_tables=metric_tables,\n", "    #         control_id=CONTROL_ID,\n", "    #         treatment_ids=TREATMENT_IDS,\n", "    #         user_group_bys=USER_BREAKDOWN_LIST,\n", "    #         bq_client=bigquery.Client(PROJECT),\n", "    #         bq_project='sc-bq-gcs-billingonly',\n", "    #         dest_dataset='temp_abtest',\n", "    #         custom_mapping_sql=custom_mapping_sql,\n", "    #         bq_priority=BQ_PRIORITY,\n", "    #         excel_output=True,\n", "    #     )\n", "    if IS_RANKING_PIPELINE:\n", "        report = CheetahReport(\n", "            study_name=STUDY_NAME,\n", "            study_start_ts_pst=STUDY_START_TS_PST,\n", "            study_end_ts_pst=STUDY_END_TS_PST,\n", "            fixed_cohort_recruitment_end_ts_pst=FIX_COHORT_END_TS_PST,\n", "            metric_tables=metric_tables,\n", "            control_id=CONTROL_ID,\n", "            treatment_ids=TREATMENT_IDS,\n", "            user_group_bys=USER_BREAKDOWN_LIST,\n", "            bq_client=bigquery.Client(PROJECT),\n", "            bq_project='sc-bq-gcs-billingonly',\n", "            dest_dataset='temp_abtest',\n", "            cohort_definition_date=COHORT_DATE,\n", "            materializing_mapping_table=True,\n", "            overwrite_mapping_table=OVERWRITE,\n", "            frequency='D',\n", "            exp_id_to_name=None,\n", "            bq_priority=BQ_PRIORITY,\n", "            quantiles=QUANTILES,\n", "            excel_output=True,\n", "        )\n", "    elif IS_COUNTRY_FILTER:\n", "        breakdown_clauses = []\n", "        for breakdown_dimension in CohortReport.cohort_fields:\n", "           if (breakdown_dimension.name in USER_BREAKDOWN_LIST\n", "               and (breakdown_dimension.start_date is None or breakdown_dimension.start_date <= COHORT_DATE)):\n", "               breakdown_clauses.append('{} AS {}'.format(breakdown_dimension.sql, breakdown_dimension.name))\n", "        custom_mapping_sql = \"\"\"\n", "          SELECT\n", "            study.ghost_user_id AS ghost_user_id,\n", "            study.exp_id AS exp_id,\n", "            study.ts AS ts,\n", "            study.exposure_ts AS exposure_ts,\n", "            {cohort_fields}\n", "          FROM (\n", "            SELECT\n", "              raw.ghost_user_id AS ghost_user_id,\n", "              raw.exp_id AS exp_id,\n", "              PARSE_TIMESTAMP('%Y%m%d', raw.ts) AS ts,\n", "              mask.exposure_ts AS exposure_ts\n", "            FROM ( (\n", "                SELECT\n", "                  SUBSTR(_TABLE_SUFFIX, -8) AS ts,\n", "                  ghost_user_id,\n", "                  exp_id,\n", "                FROM\n", "                  `sc-portal.usermap_cumulative.{study_name}__*`\n", "                WHERE\n", "                  SAFE_CAST(SUBSTR(_TABLE_SUFFIX, 0, 8) AS INT64) > 0\n", "                  AND SUBSTR(_TABLE_SUFFIX, -8) BETWEEN '{study_start_date}'\n", "                  AND '{study_end_date}' and country IN ({include_country}))) AS raw\n", "            LEFT JOIN (\n", "              SELECT\n", "                ghost_user_id,\n", "                exp_id,\n", "                MIN(TIMESTAMP_MILLIS(CAST(initial_timestamp AS INT64))) AS exposure_ts\n", "              FROM\n", "                `sc-portal.usermap_cumulative.{study_name}__{study_end_date}`\n", "              GROUP BY\n", "                ghost_user_id,\n", "                exp_id) AS mask\n", "            ON\n", "              raw.ghost_user_id = mask.ghost_user_id\n", "              AND raw.exp_id = mask.exp_id) AS study\n", "          LEFT JOIN\n", "            `sc-analytics.report_search.user_cohorts_{cohort_date}` AS cohort\n", "          ON\n", "            study.ghost_user_id = cohort.ghost_user_id\n", "        \"\"\"\n", "        report = CustomReport(\n", "            study_name=STUDY_NAME,\n", "            study_start_date=STUDY_START_DATE,\n", "            study_end_date=STUDY_END_DATE,\n", "            aa_start_date=RETRO_START_DATE,\n", "            aa_end_date=RETRO_END_DATE,\n", "            metric_tables=metric_tables,\n", "            control_id=CONTROL_ID,\n", "            treatment_ids=TREATMENT_IDS,\n", "            user_group_bys=USER_BREAKDOWN_LIST,\n", "            bq_client=bigquery.Client(PROJECT),\n", "            bq_project='sc-bq-gcs-billingonly',\n", "            dest_dataset='temp_abtest',\n", "            custom_mapping_sql=custom_mapping_sql.format(study_start_date=STUDY_START_DATE,\n", "                                                         study_end_date=STUDY_END_DATE,\n", "                                                         cohort_date=COHORT_DATE,\n", "                                                         study_name=STUDY_NAME,\n", "                                                         cohort_fields=',\\n'.join(breakdown_clauses),\n", "                                    include_country=','.join([\"'{0}'\".format(x) for x in COUNTRIES])),\n", "            overwrite_mapping_table=True,\n", "            bq_dialect='standard',\n", "        )\n", "    else:\n", "        if CUSTOM_MAPPING:\n", "            USER_BREAKDOWN_LIST = CUSTOM_MAPPING_SQL_COLUMNS\n", "            report = CustomReport(\n", "                study_name=STUDY_NAME,\n", "                study_start_date=study_start_date,\n", "                study_end_date=study_end_date,\n", "                aa_start_date=RETRO_START_DATE,\n", "                aa_end_date=RETRO_END_DATE,\n", "                metric_tables=metric_tables,\n", "                control_id=CONTROL_ID,\n", "                treatment_ids=TREATMENT_IDS,\n", "                user_group_bys=CUSTOM_MAPPING_SQL_COLUMNS,\n", "                bq_client=bigquery.Client(PROJECT),\n", "                bq_project='sc-bq-gcs-billingonly',\n", "                dest_dataset='temp_abtest',\n", "                materializing_mapping_table=True,\n", "                overwrite_mapping_table=OVERWRITE,\n", "                exp_id_to_name=None,\n", "                bq_priority=BQ_PRIORITY,\n", "                quantiles=QUANTILES,\n", "                bq_dialect='standard',\n", "                excel_output=True,\n", "                custom_mapping_sql=CUSTOM_MAPPING_SQL\n", "            )\n", "        else:\n", "            report = CohortReport(\n", "                study_name=STUDY_NAME,\n", "                study_start_date=study_start_date,\n", "                study_end_date=study_end_date,\n", "                aa_start_date=RETRO_START_DATE,\n", "                aa_end_date=RETRO_END_DATE,\n", "                metric_tables=metric_tables,\n", "                control_id=CONTROL_ID,\n", "                treatment_ids=TREATMENT_IDS,\n", "                user_group_bys=USER_BREAKDOWN_LIST,\n", "                bq_client=bigquery.Client(PROJECT),\n", "                bq_project='sc-bq-gcs-billingonly',\n", "                dest_dataset='temp_abtest',\n", "                cohort_definition_date=COHORT_DATE,\n", "                materializing_mapping_table=True,\n", "                overwrite_mapping_table=OVERWRITE,\n", "                exp_id_to_name=None,\n", "                bq_priority=BQ_PRIORITY,\n", "                quantiles=QUANTILES,\n", "                excel_output=True,\n", "                mapping_table_ver=\"allocation\" if USE_ALLOCATION_MAPPING else \"v3\",\n", "            )\n", "\n", "    report.configurations[\"pivot_table\"] = True\n", "    report.configurations['stat_fmtr'] = (\n", "        \"{pct_diff:,.2f}% ({avg_control:,.3}→{avg_treatment:,.3}, {p_value_formatted})\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["abtest.report.notebook_print(\"Study Name: {}\".format(STUDY_NAME), \"h3\")\n", "display(HTML(\"<a target=\\\"_blank\\\" and rel=\\\"noopener noreferrer\\\" href=\\\"https://ab-test-console-dot-sc-portal.appspot.com/v2#/studyId/{study_name}\\\">Study config on AB Console</a>\".format(\n", "    study_name=STUDY_NAME.split('__')[0])))\n", "\n", "abtest.report.notebook_print(\"Control ID: {}\".format(CONTROL_ID), \"strong\")\n", "abtest.report.notebook_print(\"Treatment ID(s): {}\".format(\", \".join(TREATMENT_IDS)), \"strong\")\n", "abtest.report.notebook_print(\n", "    \"Breakdowns: <br> {}\".format(\n", "        \"<br>\".join(\", \".join(breakdown) for breakdown in USER_BREAKDOWN_LIST)\n", "    ),\n", "    \"strong\"\n", ")\n", "\n", "if RETRO_START_DATE and RETRO_END_DATE and COMPUTE_RETRO_AA:\n", "    abtest.report.notebook_print(\n", "        \"User enrollment period: {} - {}\".format(STUDY_START_DATE, STUDY_END_DATE),\n", "        \"strong\"\n", "    )\n", "    abtest.report.notebook_print(\n", "        \"Retro AA Analysis period: {} - {}\".format(RETRO_START_DATE, RETRO_END_DATE),\n", "        \"strong\"\n", "    )\n", "else:\n", "    abtest.report.notebook_print(\n", "        \"Analysis period: {} - {}\".format(STUDY_START_DATE, STUDY_END_DATE),\n", "        \"strong\"\n", "    )\n", "\n", "if FIX_COHORT_END_DATE is not None:\n", "    abtest.report.notebook_print(\"In users entering study on or before {}\".format(FIX_COHORT_END_DATE), \"strong\")\n", "\n", "if IS_RANKING_PIPELINE:\n", "    abtest.report.notebook_print(\"Using Ranking AB Mapping Table\", \"strong\")\n", "else:\n", "    abtest.report.notebook_print(\"Using AB Console Mapping Table\", \"strong\")\n", "\n", "if IS_COUNTRY_FILTER:\n", "    abtest.report.notebook_print(\"Data is filtered to the following countries:<br> {}\".format(\n", "        \"<br>\".join(breakdown for breakdown in COUNTRIES)\n", "    ),\n", "    \"strong\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#################\n", "## Execute Report\n", "#################"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if SHOW_WINNERS_LOSERS == 'no_winners_losers':\n", "    status = check_job_status(query_job, wait_until_done = True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if SHOW_WINNERS_LOSERS == 'no_winners_losers':\n", "    # Run the joins and calculate the results\n", "    group_filters = {}\n", "    if not CUSTOM_MAPPING:\n", "        group_filters = {\n", "            \"df_non_friend_story_engagement_status\": [\"0 - Idle\", \"1 - Casual\", \"2 - Regular\", \"3 - Power\", \"4 - Outlier\"],\n", "            \"l_90_country\": COUNTRIES\n", "        }\n", "\n", "    report.execute(\n", "        overwrite=OVERWRITE,\n", "        group_filters=group_filters,\n", "        cumulative_trend=False,\n", "        skip_export=SKIP_EXPORT\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if SHOW_WINNERS_LOSERS == 'no_winners_losers':\n", "    table_query = \"\"\"\n", "    DROP TABLE `sc-bq-gcs-billingonly.temp_datascience.content_husky_temp_{experiment}_{curtime}`\n", "    \"\"\"\n", "\n", "    query = table_query.format(experiment = STUDY_NAME, curtime = cur_time)\n", "    drop = client.query(query, job_config=job_config)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### All Metrics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_header_str(metric_group, sep=\" \"):\n", "    return metric_group.replace(\"_\", sep).upper()\n", "\n", "display(HTML(\"<h2> Metric Groups </h2>\"))\n", "display(HTML(\"<h3> Overall </h3>\"))\n", "for metric_group in METRICS_GROUP_CONFIG:\n", "    display(HTML(\"<a href=\\\"#{html_id}-{breakdown_str}\\\">{header}</a>\".format(\n", "        html_id=get_header_str(metric_group, \"-\"),\n", "        breakdown_str=\"OVERALL\",\n", "        header=get_header_str(metric_group))))\n", "if len(USER_BREAKDOWN_LIST)>0:\n", "    display(HTML(\"<h3> Breakdowns </h3>\"))\n", "    for metric_group in METRICS_GROUP_CONFIG:\n", "        display(HTML(\"<a href=\\\"#{html_id}-{breakdown_str}\\\">{header}</a>\".format(\n", "            html_id=get_header_str(metric_group, \"-\"),\n", "            breakdown_str='BREAKDOWNS',\n", "            header=get_header_str(metric_group))))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if SHOW_WINNERS_LOSERS == 'no_winners_losers':\n", "    display_flags = [\n", "        (True, False),\n", "        (<PERSON><PERSON><PERSON>, True)\n", "    ]\n", "    def get_breakdown_str(show_all, show_breakdowns):\n", "        if show_all:\n", "            return \"OVERALL\"\n", "        if show_breakdowns:\n", "            return \"BREAKDOWNS\"\n", "\n", "    for show_all, show_breakdowns in display_flags:\n", "        for metric_group in METRICS_GROUP_CONFIG:\n", "            header=metric_group.replace(\"_\", \" \").upper()\n", "            metric_names = [str(mt.col) for mt_list in ALL_METRICS_GROUPS[metric_group] for mt in mt_list.metrics]\n", "            unsorted_metric_names = metric_names[:]\n", "            metric_names.sort()\n", "\n", "            should_display = metric_names and (show_all or USER_BREAKDOWN_LIST)\n", "            if should_display: ## to support customized metric list such as extra ab console metrics\n", "                display(HTML(\"<{tag} id='{html_id}-{breakdown_str}'>{header}</{tag}>\".format(\n", "                    header=get_header_str(metric_group),\n", "                    tag='h2',\n", "                    html_id=get_header_str(metric_group, \"-\"),\n", "                    breakdown_str=get_breakdown_str(show_all, show_breakdowns)\n", "                )))\n", "                if metric_group in ['topline_metrics','df_story_metrics_by_section', 'df_story_metrics_by_item_type',\n", "                                    'df_story_metrics_by_page_type', 'df_story_metrics_by_page_tab_type', 'df_story_metrics_by_feed_type',\n", "                                    'story_metrics_by_item_type_and_page_tab_type', 'story_metrics_by_item_type_and_section_type',\n", "                                    'story_metrics_by_item_type_and_feed_type',\n", "                                    'extended_story_metrics', 'story_metrics_by_pos', 'story_view_depth_metrics','story_view_navigation_metrics', 'pSuggestive_metrics', 'freshness_metrics', 'upnext_metrics', 'df_nfs_impression_tap_ctr_metrics', 'df_fs_impression_tap_ctr_metrics', 'carousel_impression_tap_ctr_metrics', 'story_impression_and_ctr_by_position', 'interaction_metrics', 'notification_opt_metrics'\n", "                                    \"creator_metrics_overall\",\n", "                                    \"creator_metrics_by_creator_tier\",\n", "                                    \"creator_metrics_by_creator_subtier\",\n", "                                    \"creator_metrics_by_content_type\",\n", "                                    \"creator_metrics_by_feed_type\",\n", "                                    \"creator_metrics_by_ca_score\",\n", "                                    \"creator_metrics_by_good_bad_creator\"\n", "                                   ]:\n", "                    dfab.visualize_df_metrics(report, metric_group, show_title=False,\n", "                                              show_all=show_all, show_breakdowns=show_breakdowns, display_period=PERIODS, aa=COMPUTE_RETRO_AA)\n", "                elif metric_group in ['story_engagement_metrics','addt_story_engagement_metrics','story_post_metrics', 'story_reply_metrics',\n", "                                     'publisher_attachment_metrics','opera_session_metrics']:\n", "                    dfab.visualize_df_metrics(report, metric_group, show_title=False,\n", "                                              show_all=show_all, show_breakdowns=show_breakdowns, display_period=PERIODS)\n", "                elif metric_group in ['story_engagement_positional_metrics']:\n", "                    report = umn.update_metric_display_names(report, umn.STORY_ENGAGEMENT_POSITIONAL_METRICS)\n", "                    report.generate_report(\n", "                        format_pvalue=True,\n", "                        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "                        display_config={'cumulative': ['table'], 'daily': []},\n", "                        metric_filters={'metrics': umn.STORY_ENGAGEMENT_POSITIONAL_METRICS.keys(), 'reorder':True},\n", "                        show_all=show_all,\n", "                        show_breakdowns=show_breakdowns,\n", "                        display_period=PERIODS\n", "                    );\n", "                elif metric_group in ['df_friend_story_ranking_metrics']:\n", "                    metrics_full_name = []\n", "                    for story_type in friend_story_type_list:\n", "                        for metric in [metric.col for metric in sm.DF_FRIEND_STORY_RANKING_METRICS]:\n", "                            metrics_full_name.append(story_type + \"_num_\" + metric)\n", "                    dfab.visualize_df_metrics(report, metric_group, metadata_filters={}, metric_filters={'metrics': metrics_full_name}, show_title=False,\n", "                                              show_all=show_all, show_breakdowns=show_breakdowns, display_period=PERIODS)\n", "                elif metric_group in ['communication_metrics']:\n", "                    report.generate_report(\n", "                        format_pvalue=True,\n", "                        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "                        display_config={'cumulative': ['table'], 'daily': []},\n", "                        metric_filters={'metrics': fam.snap_core_metrics+fam.snap_aux_metrics},\n", "                        show_all=show_all,\n", "                        show_breakdowns=show_breakdowns,\n", "                        display_period=PERIODS\n", "                    );\n", "                    report.generate_report(\n", "                        format_pvalue=True,\n", "                        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "                        display_config={'cumulative': ['table'], 'daily': []},\n", "                        metric_filters={'metrics': fam.chat_core_metrics+fam.chat_aux_metrics+fam.chat_ratio_metrics},\n", "                        show_all=show_all,\n", "                        show_breakdowns=show_breakdowns,\n", "                        display_period=PERIODS\n", "                    );\n", "                elif metric_group in ['story_post_latency_metrics']:\n", "                    last_pivot_table_val = report.configurations[\"pivot_table\"]\n", "                    # TODO: support pivot tables for quantile metrics so we don't have to do this\n", "                    report.configurations[\"pivot_table\"] = False\n", "\n", "                    report.generate_report(\n", "                        format_pvalue=True,\n", "                        extra_table_cols=['treatment_id', 'count_control'],\n", "                        facet_bys=['treatment_id', 'quantile'],\n", "                        display_config={'cumulative': ['table'], 'daily': []},\n", "                        metric_filters={'metrics': metric_names},\n", "                        stat_fmtr=\"'P{quantile}':: {pct_diff:,.2f}% ({quantile_control:,.3}→{quantile_treatment:,.3}, {p_value_formatted})\",\n", "                        show_all=show_all,\n", "                        show_breakdowns=show_breakdowns,\n", "                        display_period=PERIODS\n", "                    )\n", "                    report.configurations[\"pivot_table\"] = last_pivot_table_val\n", "                elif metric_group in ['story_post_failure_metrics']:\n", "                    report.ab_printer.print_text('Story Post Key Failure Metrics', 'h3')\n", "                    failure_metric_key = [m for mt in ALL_METRICS_GROUPS['story_post_failure_metrics'] for m in mt.cumulative_metrics if '_fail' not in m and '_fatal' not in m]\n", "                    report.generate_report(\n", "                        format_pvalue=True,\n", "                        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "                        display_config={'cumulative': ['table'], 'daily': []},\n", "                        metric_filters={'metrics': failure_metric_key},\n", "                        show_all=show_all,\n", "                        show_breakdowns=show_breakdowns,\n", "                        display_period=PERIODS\n", "                    )\n", "\n", "                    report.ab_printer.print_text('Story Post Failed and Fatal Attribution Metrics', 'h3')\n", "                    failure_metric_reason = [m for mt in ALL_METRICS_GROUPS['story_post_failure_metrics'] for m in mt.cumulative_metrics if '_fail' in m or '_fatal' in m]\n", "                    report.generate_report(\n", "                        format_pvalue=True,\n", "                        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "                        display_config={'cumulative': ['table'], 'daily': []},\n", "                        metric_filters={'metrics': failure_metric_reason},\n", "                        show_all=show_all,\n", "                        show_breakdowns=show_breakdowns,\n", "                        display_period=PERIODS\n", "                    )\n", "                elif metric_group in ['df_imp_share_metrics']:\n", "                    report = umn.update_metric_display_names(report, umn.DF_IMP_SHARE_METRICS)\n", "                    report.generate_report(\n", "                        format_pvalue=True,\n", "                        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "                        display_config={'cumulative': ['table'], 'daily': []},\n", "                        metric_filters={'metrics': umn.DF_IMP_SHARE_METRICS.keys(), 'reorder':True},\n", "                        show_all=show_all,\n", "                        show_breakdowns=show_breakdowns,\n", "                        display_period=PERIODS\n", "                    );\n", "                elif metric_group in ['df_session_over_session_overlap_metrics']:\n", "                    report = umn.update_metric_display_names(report, umn.DF_SoS_OVERLAP_METRICS)\n", "                    report.generate_report(\n", "                        format_pvalue=True,\n", "                        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "                        display_config={'cumulative': ['table'], 'daily': []},\n", "                        metric_filters={'metrics': umn.DF_SoS_OVERLAP_METRICS.keys(), 'reorder':True},\n", "                        show_all=show_all,\n", "                        show_breakdowns=show_breakdowns,\n", "                        display_period=PERIODS\n", "                    );\n", "                elif metric_group in ['df_day_over_day_overlap_metrics']:\n", "                    report = umn.update_metric_display_names(report, umn.DF_DoD_OVERLAP_METRICS )\n", "                    report.generate_report(\n", "                        format_pvalue=True,\n", "                        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "                        display_config={'cumulative': ['table'], 'daily': []},\n", "                        metric_filters={'metrics': umn.DF_DoD_OVERLAP_METRICS.keys(), 'reorder':True},\n", "                        show_all=show_all,\n", "                        show_breakdowns=show_breakdowns,\n", "                        display_period=PERIODS\n", "                    );\n", "                elif metric_group in ['content_sharing_metrics', 'non_friend_content_new_user_retention',\n", "                                      'non_friend_content_activated_user_retention','discover_feed_stories_carousel_performance_metrics']:\n", "                    report.generate_report(\n", "                        format_pvalue=True,\n", "                        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "                        display_config={'cumulative': ['table'], 'daily': []},\n", "                        metric_filters={'metrics': unsorted_metric_names, 'reorder': True},\n", "                        show_all=show_all,\n", "                        show_breakdowns=show_breakdowns,\n", "                        display_period=PERIODS\n", "                    );\n", "                elif metric_group in ['idau_metrics']:\n", "                    report.generate_report(\n", "                        format_pvalue=True,\n", "                        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "                        display_config={'cumulative': ['table'], 'daily': []},\n", "                        metric_filters={'metrics': ['idau_1p', 'idau_2p', 'idau_3p', 'idau_4p', 'idau_5p']},\n", "                        show_all=show_all,\n", "                        show_breakdowns=show_breakdowns,\n", "                        display_period=PERIODS\n", "                    );\n", "                elif metric_group in ['revenue_weighted_view_time_metrics']:\n", "                    report.generate_report(\n", "                        format_pvalue=True,\n", "                        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "                        display_config={'cumulative': ['table'], 'daily': []},\n", "                        metric_filters={'metrics': ['total_revenue_weighted_view_time', 'nfs_revenue_weighted_view_time', 'fs_revenue_weighted_view_time', 'spotlight_revenue_weighted_view_time']},\n", "                        show_all=show_all,\n", "                        show_breakdowns=show_breakdowns,\n", "                        display_period=PERIODS\n", "                    )   \n", "                else:\n", "                    report.generate_report(\n", "                        format_pvalue=True,\n", "                        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "                        display_config={'cumulative': ['table'], 'daily': []},\n", "                        metric_filters={'metrics': metric_names},\n", "                        show_all=show_all,\n", "                        show_breakdowns=show_breakdowns,\n", "                        display_period=PERIODS\n", "                    )\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if SHOW_WINNERS_LOSERS == 'no_winners_losers':\n", "    HTML(\"\"\"\n", "    <b>Result available in link:</b><br>\n", "    <a href='{url}'>{url}</a><br>\n", "    (xlsx can be directly imported to Google Sheets)\n", "    \"\"\".format(url=report.upload_excel_output_to_gcs()))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Meta Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Try Shepherd Meta Analysis (beta) to visualize the metric daily trend and the %Delta change against historic data. More details can be found [here](https://docs.google.com/document/d/1N_SMwYuSxKipqJQ9lCiWuaF3oYsm74nhbR70uXef11k/edit#heading=h.qq7bq9uxwz9s)."]}, {"cell_type": "code", "execution_count": 68, "metadata": {"ExecuteTime": {"end_time": "2025-04-23T17:53:20.026882Z", "start_time": "2025-04-23T17:53:20.023988Z"}, "lines_to_next_cell": 2}, "outputs": [{"data": {"text/html": ["<h3><a href=\"https://shepherd-dot-vellum-dev.gae.sc-corp.net/meta-analysis/tab-mr/ios_opera_single_snap_player_for_direct_snap__113428\">Shepherd Historic Metric Range for ios_opera_single_snap_player_for_direct_snap__113428</a><h3>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if SHOW_WINNERS_LOSERS == 'no_winners_losers':\n", "    if IS_RANKING_PIPELINE:\n", "        display(HTML(\"<h3><a href=\\\"https://shepherd-dot-vellum-dev.gae.sc-corp.net/meta-analysis/tab-le/{study_name}\\\">Shepherd Daily Metric Trend for {study_name}</a><h3>\".format(study_name=STUDY_NAME)))\n", "    display(HTML(\"<h3><a href=\\\"https://shepherd-dot-vellum-dev.gae.sc-corp.net/meta-analysis/tab-mr/{study_name}\\\">Shepherd Historic Metric Range for {study_name}</a><h3>\".format(study_name=STUDY_NAME)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["###  Key Metrics Daily Trend"]}, {"cell_type": "code", "execution_count": 69, "metadata": {"ExecuteTime": {"end_time": "2025-04-23T17:53:20.042798Z", "start_time": "2025-04-23T17:53:20.040923Z"}}, "outputs": [], "source": ["addt_trend_metrics = [\n", "    'df_subscription_section_num_story_view',\n", "    'df_subscription_section_num_story_view_time',\n", "    'df_for_you_section_num_story_view',\n", "    'df_for_you_section_num_story_view_time',\n", "    'friend_story_snap_delete',\n", "    'friend_story_snap_delete_active_days',\n", "    'friend_story_snap_post_net_delete',\n", "    'friend_story_snap_post_net_delete_active_days',\n", "    ]"]}, {"cell_type": "code", "execution_count": 70, "metadata": {"ExecuteTime": {"end_time": "2025-04-23T17:53:20.072741Z", "start_time": "2025-04-23T17:53:20.065786Z"}}, "outputs": [], "source": ["if SHOW_WINNERS_LOSERS == 'no_winners_losers':\n", "    daily = report.get_results('daily', group_vars=[])\n", "    if daily is not None:\n", "        daily['metric_field'] = daily['metric']\n", "        daily['metric'] = daily['metric'].replace(report.metric_name_mapping)\n", "        trend_metrics = list(dict.fromkeys(list(addt_trend_metrics)))\n", "        exp_ids = daily.treatment_id.unique()\n", "\n", "        for metric in trend_metrics:\n", "            for treatment_id in exp_ids:\n", "                if metric not in daily.metric_field.unique():\n", "                    continue\n", "                df_to_plot = daily.loc[\n", "                    (daily.metric_field == metric)\n", "                    & (daily.treatment_id == treatment_id),\n", "                ].copy()\n", "                title = \"Trend in {}: {} vs {}\".format(\n", "                    df_to_plot.metric.iloc[0],\n", "                    treatment_id,\n", "                    daily.control_id.unique()[0],\n", "                )\n", "                report.ab_printer.print_text(\n", "                    title,\n", "                    'h4'\n", "                )\n", "                g = abtest.plots.deltatrendplot(\n", "                    df_to_plot,\n", "                    date='exp_ds',\n", "                    point='pct_diff',\n", "                    low='pct_diff_lo',\n", "                    high='pct_diff_hi',\n", "                    metric='metric',\n", "                    aspect=2.8,\n", "                    size=5,\n", "                    title=title,\n", "                    facet_hue=None,\n", "                )\n", "                plt.show()\n", "\n", "                if SHOW_TREND_PLOT_IN_ABSOLUTE:\n", "                    g = abtest.plots.trendplot(\n", "                        df_to_plot,\n", "                        date='exp_ds',\n", "                        measures=['avg_treatment', 'avg_control'],\n", "                        metric='metric',\n", "                        size=5,\n", "                        aspect=2.8,\n", "                        title=title,\n", "                    )\n", "                    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["----\n", "## Story Winners and Losers\n", "\n", "All winners and losers below are top and bottom stories/publishers ordered by average per user snap views. To get the total gain and loss of impression and views, multiple the difference by discover feed DAU. Impressions are long impressions. Only stories with p-values of < 0.1 are included. CTR is story views divided by long impressions.\n", "\n", "You can choose to generate the results for either the last day of the study (faster and enough to get some ideas about how the study changes stories ranking) or for the entire period of the study (slower).\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if SHOW_WINNERS_LOSERS != 'no_winners_losers':\n", "    WL_CONFIG = {\n", "        \"story_id\":\"sc-analytics.report_discover_feed.story_viewer_level_interaction_20*\",\n", "        \"tile_id\":\"sc-analytics.report_discover_feed.tile_viewer_level_interaction_20*\"\n", "    }\n", "    def query_condition_by_engagement_type(engagement_type):\n", "        if engagement_type == 'UGC':\n", "            return \"\"\"\n", "            AND item_type IN ('PUBL<PERSON>', 'POPULA<PERSON>', 'OFFICIAL', 'SAVED_STORY', 'SAVED_STORY_PUBLIC', 'SAVED_STORY_OFFICIAL')\n", "            \"\"\"\n", "        elif engagement_type == 'Spotlight':\n", "            return \"\"\"\n", "            AND item_type = 'COMMUNITY'\n", "            \"\"\"\n", "        elif engagement_type == 'Premium_content':\n", "            return \"\"\"\n", "            AND item_type IN ('PU<PERSON><PERSON>H<PERSON>','SHOW','ORIGINAL')\n", "            \"\"\"\n", "        else:\n", "            return \"\"\"\n", "            AND item_type IN ('PUBLIC', 'POPULAR', 'OFFICIAL')\n", "            \"\"\"\n", "\n", "\n", "    def sql_callable(start_date, end_date):\n", "        return \"\"\"\n", "        SELECT\n", "            ghost_user_id,\n", "            {id_key},\n", "            PARSE_TIMESTAMP(\"%Y%m%d\", CONCAT(\"20\", _TABLE_SUFFIX)) AS ts,\n", "            {metrics}\n", "        FROM\n", "            `{source}`\n", "        WHERE\n", "            CONCAT(\"20\", _TABLE_SUFFIX) BETWEEN \"{start_date}\" AND \"{end_date}\"\n", "            {filter_string}\n", "        GROUP BY\n", "            ALL\n", "    \"\"\".format(\n", "            start_date=start_date,\n", "            end_date=end_date,\n", "            id_key=WL_KEY,\n", "            metrics=\", \".join([f\"SUM({metric}) AS {metric}\" for metric in WL_DISPLAY_METRICS]),\n", "            source= WL_CONFIG[WL_KEY],\n", "            filter_string=\"\\n\".join([query_condition_by_engagement_type(engagement_type) for engagement_type in WL_ENGAGEMENT_TYPE] if WL_ENGAGEMENT_TYPE else \"\"),\n", "    )\n", "    breakdown_threshold = {\n", "        \"breakdown_column\":<PERSON><PERSON>_<PERSON>EY,\n", "        \"metric\":WL_ORDER_BY[0] if WL_ORDER_BY else None,\n", "        \"control\":CONTROL_ID if CONTROL_THRESHOLD else None,\n", "        \"treatment\":TREATMENT_IDS if TREATMENT_THRESHOLD else None,\n", "        \"control_threshold\":CONTROL_THRESHOLD,\n", "        \"treatment_threshold\":TREATMENT_THRESHOLD,\n", "        }\n", "    wl_metric_table = FieldBreakdownMetricTable(\n", "        sql=None,\n", "        sql_callable=sql_callable,\n", "        metrics=[Metric(metric, dist=\"cont\") for metric in WL_DISPLAY_METRICS],\n", "        name=\"content_winners_and_losers\",\n", "        breakdowns=[[WL_KEY]],\n", "        bq_dialect=\"standard\",\n", "        daily=False,\n", "        inner_join_with_mapping=True,\n", "        breakdown_threshold=breakdown_threshold\n", "    )\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if SHOW_WINNERS_LOSERS == 'no_winners_losers':\n", "    report.ab_printer.print_text(\"Story winners and losers skipped\", 'strong')\n", "else:\n", "    if SHOW_WINNERS_LOSERS == 'l7_winners_losers':\n", "        wl_start_date = max([\n", "            STUDY_START_DATE,\n", "            (datetime.strptime(STUDY_END_DATE, \"%Y%m%d\") - timedelta(days=6)).strftime(\"%Y%m%d\")\n", "        ])\n", "    elif <PERSON>_WINNERS_LOSERS == 'l1_winners_losers':\n", "        wl_start_date = STUDY_END_DATE\n", "    else:  # Use last day\n", "        wl_start_date = STUDY_START_DATE\n", "    wl_end_date = STUDY_END_DATE\n", "\n", "    report_wl = CohortReport(\n", "        study_name=STUDY_NAME,\n", "        study_start_date=wl_start_date,\n", "        study_end_date=wl_end_date,\n", "        metric_tables=[wl_metric_table],\n", "        control_id=CONTROL_ID,\n", "        treatment_ids=TREATMENT_IDS,\n", "        bq_project='sc-bq-gcs-billingonly',\n", "        bq_client=bigquery.Client(PROJECT),\n", "        dest_dataset='temp_abtest',\n", "        cohort_definition_date=COHORT_DATE,\n", "        materializing_mapping_table=True,\n", "        overwrite_mapping_table=OVERWRITE,\n", "        bq_priority=BQ_PRIORITY,\n", "        )\n", "\n", "    report_wl.configurations['stat_fmtr'] = (\n", "        \"{pct_diff:,.2f}% ({avg_control:,.3}→{avg_treatment:,.3}, {p_value_formatted})\"\n", "    )\n", "\n", "    report_wl.execute(\n", "        overwrite=True,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def storyteller_link_from_content_id(content_level):\n", "    if content_level == 'tile_id':\n", "        return f\"\"\"concat(\"https://storyteller.sc-corp.net/snap?story_id=\", {content_level}, \"&pgdm=false\")\"\"\"\n", "    elif content_level == 'story_id':\n", "        return f\"\"\"concat(\"https://storyteller.sc-corp.net/story?story_id=35%3A%3A\", {content_level}, \"%3A%3A0\")\"\"\"\n", "\n", "if SHOW_WINNERS_LOSERS != 'no_winners_losers':\n", "    report_wl.ab_printer.print_text(\n", "        \"Winners and Losers from {} to {}\".format(\n", "            wl_start_date,\n", "            wl_end_date,\n", "        ),\n", "        'h2'\n", "    )\n", "    enrichment_sql = \"\"\"\n", "    SELECT\n", "      {id_key},\n", "      MAX(creator_user_id),\n", "      MAX({story_teller_link}) AS story_teller_link,\n", "      MAX(CASE\n", "            WHEN item_type IN ('PU<PERSON><PERSON>', 'POPULA<PERSON>', 'OFFICIAL', 'SAVED_STORY', 'SAVED_STORY_PUBLIC', 'SAVED_STORY_OFFICIAL')\n", "              THEN 'UGC'\n", "            WHEN item_type = 'COMMUNITY'\n", "              THEN 'Spotlight'\n", "            WHEN item_type IN ('PUBLISHER', 'SHOW', 'ORIGINAL')\n", "              THEN 'Premium_content'\n", "            ELSE 'Other'\n", "          END) AS engagement_type,\n", "      {dimensions}\n", "    FROM\n", "      `{source}`\n", "    WHERE\n", "      CONCAT(\"20\", _TABLE_SUFFIX) = \"{wl_start_date}\"\n", "      AND {id_key} IS NOT NULL\n", "      {filter_string}\n", "    GROUP BY\n", "      1\n", "    \"\"\".format(\n", "        wl_start_date=wl_start_date,\n", "        id_key=WL_KEY,\n", "        source=WL_CONFIG[WL_KEY],\n", "        dimensions=\", \".join([f\"MAX({dimension}) AS {dimension}\" for dimension in WL_DIMENSIONS if dimension != \"engagement_type\"]) if WL_DIMENSIONS else \"\",\n", "        filter_string=\"\\n\".join([query_condition_by_engagement_type(engagement_type) for engagement_type in WL_ENGAGEMENT_TYPE] if WL_ENGAGEMENT_TYPE else \"\"),\n", "        story_teller_link=storyteller_link_from_content_id(WL_KEY)\n", "    )\n", "\n", "    winners_losers_results = report_wl.get_results(\n", "        \"cumulative\",\n", "        exclude_field_breakdown=False,\n", "    )\n", "    if WL_DIMENSIONS:\n", "        for breakdowns in WL_DIMENSIONS:\n", "            if breakdowns == \"overall\":\n", "                report_wl.generate_winners_losers_report(\n", "                    winners_losers_dimensions=[WL_KEY],\n", "                    order_by_metrics=WL_ORDER_BY,\n", "                    display_metrics=WL_DISPLAY_METRICS,\n", "                    enrichment_group_bys=None,\n", "                    p_value_threshold=0.1,\n", "                    enrichment_sql=enrichment_sql,\n", "                    winners_losers_results=winners_losers_results,\n", "                    n_winners=25,\n", "                    n_losers=25\n", "                )\n", "            else:\n", "                report_wl.generate_winners_losers_report(\n", "                    winners_losers_dimensions=[WL_KEY],\n", "                    order_by_metrics=WL_ORDER_BY,\n", "                    display_metrics=WL_DISPLAY_METRICS,\n", "                    enrichment_group_bys=[breakdowns],\n", "                    p_value_threshold=0.1,\n", "                    enrichment_sql=enrichment_sql,\n", "                    winners_losers_results=winners_losers_results,\n", "                    n_winners=25,\n", "                    n_losers=25\n", "                )\n", "    else:\n", "        report_wl.generate_winners_losers_report(\n", "            winners_losers_dimensions=[WL_KEY],\n", "            order_by_metrics=WL_ORDER_BY,\n", "            display_metrics=WL_DISPLAY_METRICS,\n", "            enrichment_group_bys=None,\n", "            p_value_threshold=0.1,\n", "            enrichment_sql=enrichment_sql,\n", "            winners_losers_results=winners_losers_results,\n", "                    n_winners=25,\n", "                    n_losers=25\n", "        )\n"]}], "metadata": {"anaconda-cloud": {}, "celltoolbar": "Tags", "finalized": {"timestamp": 1568312574522, "trusted": true}, "hide_input": false, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}, "require": {"paths": {"buttons.colvis": "https://cdn.datatables.net/buttons/1.5.6/js/buttons.colVis.min", "buttons.flash": "https://cdn.datatables.net/buttons/1.5.6/js/buttons.flash.min", "buttons.html5": "https://cdn.datatables.net/buttons/1.5.6/js/buttons.html5.min", "buttons.print": "https://cdn.datatables.net/buttons/1.5.6/js/buttons.print.min", "d3": "https://cdnjs.cloudflare.com/ajax/libs/d3/5.9.2/d3.min", "datatables.net": "https://cdn.datatables.net/1.10.18/js/jquery.dataTables", "datatables.net-buttons": "https://cdn.datatables.net/buttons/1.5.6/js/dataTables.buttons.min", "datatables.responsive": "https://cdn.datatables.net/responsive/2.2.2/js/dataTables.responsive.min", "datatables.scroller": "https://cdn.datatables.net/scroller/2.0.0/js/dataTables.scroller.min", "datatables.select": "https://cdn.datatables.net/select/1.3.0/js/dataTables.select.min", "jszip": "https://cdnjs.cloudflare.com/ajax/libs/jszip/2.5.0/jszip.min", "pdfmake": "https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/pdfmake.min", "vfsfonts": "https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/vfs_fonts"}, "shim": {"buttons.colvis": {"deps": ["j<PERSON><PERSON>", "datatables.net-buttons"]}, "buttons.flash": {"deps": ["j<PERSON><PERSON>", "datatables.net-buttons"]}, "buttons.html5": {"deps": ["j<PERSON><PERSON>", "datatables.net-buttons"]}, "buttons.print": {"deps": ["j<PERSON><PERSON>", "datatables.net-buttons"]}, "datatables.net": {"exports": "$.fn.dataTable"}, "datatables.net-buttons": {"deps": ["datatables.net"]}, "pdfmake": {"deps": ["datatables.net"]}, "vfsfonts": {"deps": ["datatables.net"]}}}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {"height": "calc(100% - 180px)", "left": "10px", "top": "150px", "width": "165px"}, "toc_section_display": true, "toc_window_display": true}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 4}