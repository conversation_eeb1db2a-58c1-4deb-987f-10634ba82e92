{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# A/B Study Analysis\n", "\n", "This analysis report includes metrics from the A/B Console Quest V2 pipelines. The analysis includes the results broken down user factors, which helps understanding metrics movements."]}, {"cell_type": "markdown", "metadata": {}, "source": ["This report is generated using Snap's internal Python analytic lib ([banjo](http://go.sc-corp.net/pya)). [The results are largely consistent with AB Dashboard](https://wiki.sc-corp.net/display/DS/banjo.abtest+module+FAQ#banjo.abtestmoduleFAQ-Q:Whydoesn'ttheresultsmatchA/Bconsole?) and include additional breakdowns and metrics not available on A/B Dashboard. Please reach out to the report owners at #datascience-tools for questions and support. \n", "  * Summary Tables with Visualizations\n", "  * Metrics Breakdowns\n", "  * Time Series Plot to show the trend\n", "  * Support for adhoc metrics and breakdowns\n", "\n", "It also can be found on [<PERSON><PERSON>](https://vellum.sc-corp.net/services/exporter/ab_deep_dive/)\n", "\n", "## About this cohort deep dive notebook\n", "Frequently, we want to know the source of the difference for the most important metrics. This notebook offers the standard breakdown available from the [user_cohorts](https://bigquery.cloud.google.com/table/sc-analytics:report_growth.user_cohorts_20171123) table.\n", "\n", "Because we are doing a lot more comparisons when we break down the users by many dimensions, we need to keep in mind the [multiple comparison problem](https://en.wikipedia.org/wiki/Multiple_comparisons_problem) when we intepret the p-value and make conclusions.\n", "\n", "To use this notebook:\n", "* Specify a list of lists in `USER_BREAKDOWN_LIST`. For example: `USER_BREAKDOWN_LIST = [['os_type'], ['is_popular_user'], ['os_type', 'is_popular_user']]` will provide breakdowns by os, by user popularity and finally by both factors.  \n", "* Specify just a few most important metrics to include in `metric_tables`. \n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# import files\n", "from __future__ import division, unicode_literals, print_function\n", "import collections\n", "import os\n", "VELLUM = any(['VELLUM' in k for k in os.environ.keys()])\n", "if VELLUM:\n", "    pip_output = !pip install banjo\n", "\n", "import re\n", "import seaborn as sns\n", "import pandas as pd\n", "import logging\n", "logging.basicConfig()\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "%matplotlib inline\n", "%config InlineBackend.figure_format = 'retina'\n", "\n", "from banjo import abtest, utils # see: go/pya\n", "#from banjo.abtest.cohort_report import CohortReport\n", "from banjo.abtest.report import CustomReport\n", "from banjo.abtest.analysis import reshape_aggregated\n", "from datetime import date\n", "from datetime import timedelta\n", "import pandas_gbq\n", "\n", "PROJECT = 'sc-bq-gcs-billingonly'\n", "sns.set(style='whitegrid', font_scale=1.5)\n", "\n", "from IPython.core.display import display, HTML\n", "display(HTML(\"<style>.output_html.rendered_html table { font-size:8pt;}</style>\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true, "tags": ["parameters"]}, "outputs": [], "source": ["# Specify study details\n", "# ASK FOR ITT STUDY EXP ID NOT STUDY NAME\n", "ITT_STUDY_EXP_ID = 'da6d08a2-dafe-454a-90bf-04568983f20a'\n", "ITT_STUDY_SEED_STRING='' # ITT_STUDY_SEED_STRING has to be '' here\n", "ITT_STUDY_AUDIENCE_TABLE='' # ITT_STUDY_AUDIENCE_TABLE has to be '' here\n", "\n", "STUDY_START_DATE = '20000101' # has to be '20000101' here\n", "STUDY_END_DATE = '20000101' # has to be '20000101' here\n", "RETRO_START_DATE = '20000101' # has to be '20000101' here\n", "RETRO_END_DATE = '20000101' # has to be '20000101' here\n", "CAMPAIGN_START_DATE = '20000101' # has to be '20000101' here\n", "\n", "# Specify expected experiment proportions as a dict. The proportions are\n", "# used to check whether sample sizes match configured proportions.\n", "# REMOVE FROM DROP DOWN SINCE ALWAYS THEM SAME\n", "CONTROL_ID = 'CONTROL'\n", "TREATMENT_IDS = ['TEST']\n", "EXP_NAMES = {'CONTROL':'CONTROL','TEST':'TEST'}\n", "             \n", "ab_console_metrics = []  # A/B Quest V2\n", "\n", "ab_console_metric_groups = []  # placeholder for later select by metric_group\n", "\n", "ab_console_dash_group_ids = []  # A/B console metric group ids\n", "\n", "compass_group_ids = []  # compass metric groups used on A/B Dashboard\n", "\n", "AB_CONSOLE_DEFAULT_METRICS = [\n", "#     \"app_active_day_user\",\n", "#     \"snap_direct_snap_create_user\",\n", "#     \"app_app_session_time_user\",\n", "#    \"snap_filter_lens_swipe_user\",    \n", "#     \"chat_chat_send_user\",\n", "#     \"chat_chat_view_user\",\n", "#     \"snap_direct_snap_save_user\",\n", "#     \"snap_direct_snap_send_user\",\n", "#     \"snap_direct_snap_view_user\",\n", "#     \"discover_feed_discover_feed_non_friend_story_view_and_time_viewed\",\n", "#     \"story_friend_story_snap_post\",\n", "#     \"story_friend_story_snap_view\",\n", "#     \"story_friend_story_story_view\",\n", "#     \"camera_camera_cold_startup_latency_event\",\n", "#     \"camera_camera_warm_startup_latency_event\"\n", "]\n", "\n", "USER_BREAKDOWN_LIST = []\n", "COUNTRIES = []\n", "DEVICE_MAKERS = [\n", "    \"Apple\",\n", "    \"Samsung\",\n", "    \"Huawei\",\n", "    \"Op<PERSON>\",\n", "    \"Xiaomi\",\n", "    \"Motorola\",\n", "    \"LGE\",\n", "    \"Vivo\",\n", "    \"Redmi\",\n", "    \"Tecno\",\n", "    \"Google\",\n", "    \"Infinix\",\n", "    \"TCT (Alcatel)\",\n", "    \"Sony\",\n", "    \"ZTE\",\n", "    \"Nokia\",\n", "    \"OnePlus\",\n", "    \"Asus\",\n", "    \"Realme\",\n", "    \"Lenovo\",\n", "    \"Itel\",\n", "]\n", "\n", "quantiles = ['50', '90']\n", "\n", "OVERWRITE = False\n", "PIVOT_RESULTS = True\n", "DAILY_TREND = False\n", "COHORT_DATE = None\n", "INCLUDE_CORE_METRICS = False\n", "INCLUDE_REVENUE_METRICS = False\n", "USE_BROAD_MAPPING = False\n", "CALCULATE_INCREMENTAL_METRICS = False\n", "USE_BATCH_BQ_PRIORITY = False\n", "\n", "report_key = None\n", "\n", "DASHBOARD_BACKFILL = False\n", "USE_ALLOCATION_MAPPING = False\n", "COMPUTE_RETRO_AA = False\n", "OUTPUT_TO_BIGQUERY = False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["SEED_STRING = ITT_STUDY_SEED_STRING\n", "audience_table = ITT_STUDY_AUDIENCE_TABLE\n", "STUDY_NAME = ITT_STUDY_EXP_ID\n", "# create list\n", "def ensure_list(parameter):\n", "    if isinstance(parameter, list):\n", "        return parameter\n", "    else:\n", "        return [parameter]\n", "\n", "TREATMENT_IDS = ensure_list(TREATMENT_IDS)\n", "\n", "ab_console_metrics = ensure_list(ab_console_metrics)   \n", "ab_console_metrics = [\n", "    metric for metric in AB_CONSOLE_DEFAULT_METRICS if metric not in ab_console_metrics\n", "] + ab_console_metrics\n", "\n", "ab_console_metrics = ensure_list(ab_console_metrics)\n", "ab_console_metric_groups = ensure_list(ab_console_metric_groups)\n", "ab_console_dash_group_ids = ensure_list(ab_console_dash_group_ids)\n", "compass_group_ids = ensure_list(compass_group_ids)\n", "USER_BREAKDOWN_LIST = ensure_list(USER_BREAKDOWN_LIST)\n", "USER_BREAKDOWN_LIST.append('app_engagement_status')\n", "COUNTRIES = ensure_list(COUNTRIES)\n", "quantiles = ensure_list(quantiles)\n", "\n", "PIVOT_RESULTS = bool(PIVOT_RESULTS)\n", "BQ_PRIORITY = \"INTERACTIVE\"\n", "USE_ALLOCATION_MAPPING = bool(USE_ALLOCATION_MAPPING)\n", "COMPUTE_RETRO_AA = bool(COMPUTE_RETRO_AA)\n", "INCLUDE_REVENUE_METRICS = bool(INCLUDE_REVENUE_METRICS)\n", "USE_BROAD_MAPPING = bool(USE_BROAD_MAPPING)\n", "CALCULATE_INCREMENTAL_METRICS = bool(CALCULATE_INCREMENTAL_METRICS)\n", "\n", "# decide if print daily trend\n", "daily_display = []\n", "if DAILY_TREND:\n", "    daily_display = ['trend']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if USE_ALLOCATION_MAPPING:\n", "    display(HTML(\n", "        \"\"\"\n", "        <h4> Allocation logging: </h4>\n", "        <p>The analysis is done using server allocation logging as you requested.\n", "        This means a user is included in the study as long as the user is targeted \n", "        by the A/B service regardless whether the code path using the A/B setting is triggered\n", "        for this user. \n", "        </p>\n", "        <p>\n", "        If your feature affects most Snapchat users, this analysis is close to the \n", "        exposure logging based analysis shown on the A/B console.\n", "        \n", "        If your feature only affects a small subset of Snapchat users, this analysis\n", "        may show diluted impact of your feature.\n", "        </p>\n", "        \"\"\"\n", "        \n", "    ))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try: \n", "    #Get STUDY_START_DATE\n", "    temp_STUDY_START_DATE = pandas_gbq.read_gbq(\n", "        '''select study_start_date from `sc-product-datascience.temp_reports.house_ads_live_campaigns_temp` where experiment_id = \"{exp_id}\"'''.format(exp_id = ITT_STUDY_EXP_ID),\n", "                use_bqstorage_api = True, \n", "                project_id = 'sc-bq-gcs-billingonly')\n", "    \n", "    temp_STUDY_START_DATE = temp_STUDY_START_DATE.iloc[0,0]\n", "    temp_STUDY_START_DATE = temp_STUDY_START_DATE.replace(\".0\",\"\")\n", "    if STUDY_START_DATE == '20000101':\n", "        STUDY_START_DATE = temp_STUDY_START_DATE\n", "    \n", "    #Get STUDY_END_DATE\n", "    temp_STUDY_END_DATE = pandas_gbq.read_gbq(\n", "        '''select study_end_date from `sc-product-datascience.temp_reports.house_ads_live_campaigns_temp` where experiment_id = \"{exp_id}\"'''.format(exp_id = ITT_STUDY_EXP_ID),\n", "                use_bqstorage_api = True, \n", "                project_id = 'sc-bq-gcs-billingonly')\n", "    \n", "    temp_STUDY_END_DATE = temp_STUDY_END_DATE.iloc[0,0]\n", "    temp_STUDY_END_DATE = temp_STUDY_END_DATE.replace(\".0\",\"\")\n", "    if STUDY_END_DATE == '20000101':\n", "        STUDY_END_DATE = temp_STUDY_END_DATE\n", "    \n", "    #Get CAMPAIGN_START_DATE\n", "    temp_CAMPAIGN_START_DATE = pandas_gbq.read_gbq(\n", "        '''select CAMPAIGN_START_DATE from `sc-product-datascience.temp_reports.house_ads_live_campaigns_temp` where experiment_id = \"{exp_id}\"'''.format(exp_id = ITT_STUDY_EXP_ID),\n", "                use_bqstorage_api = True, \n", "                project_id = 'sc-bq-gcs-billingonly')\n", "    \n", "    temp_CAMPAIGN_START_DATE = temp_CAMPAIGN_START_DATE.iloc[0,0]\n", "    temp_CAMPAIGN_START_DATE = temp_CAMPAIGN_START_DATE.replace(\".0\",\"\")\n", "    if CAMPAIGN_START_DATE == '20000101':\n", "        CAMPAIGN_START_DATE = temp_CAMPAIGN_START_DATE\n", "    \n", "    #Get RETRO_START_DATE\n", "    temp_RETRO_START_DATE = pandas_gbq.read_gbq(\n", "        '''select RETRO_START_DATE from `sc-product-datascience.temp_reports.house_ads_live_campaigns_temp` where experiment_id = \"{exp_id}\"'''.format(exp_id = ITT_STUDY_EXP_ID),\n", "                use_bqstorage_api = True, \n", "                project_id = 'sc-bq-gcs-billingonly')\n", "    \n", "    temp_RETRO_START_DATE = temp_RETRO_START_DATE.iloc[0,0]\n", "    temp_RETRO_START_DATE = temp_RETRO_START_DATE.replace(\".0\",\"\")\n", "    if RETRO_START_DATE == '20000101':\n", "        RETRO_START_DATE = temp_RETRO_START_DATE\n", "    \n", "    #Get RETRO_END_DATE\n", "    temp_RETRO_END_DATE = pandas_gbq.read_gbq(\n", "        '''select R<PERSON><PERSON>_END_DATE from `sc-product-datascience.temp_reports.house_ads_live_campaigns_temp` where experiment_id = \"{exp_id}\"'''.format(exp_id = ITT_STUDY_EXP_ID),\n", "                use_bqstorage_api = True, \n", "                project_id = 'sc-bq-gcs-billingonly')\n", "    \n", "    temp_RETRO_END_DATE = temp_RETRO_END_DATE.iloc[0,0]\n", "    temp_RETRO_END_DATE = temp_RETRO_END_DATE.replace(\".0\",\"\")\n", "    if RETRO_END_DATE == '20000101':\n", "        RETRO_END_DATE = temp_RETRO_END_DATE\n", "        \n", "    #Get compass_group_ids\n", "    temp_compass_group_ids = pandas_gbq.read_gbq(\n", "        '''select ab_metrics from `sc-product-datascience.temp_reports.house_ads_live_campaigns_temp` where experiment_id = \"{exp_id}\"'''.format(exp_id = ITT_STUDY_EXP_ID),\n", "                use_bqstorage_api = True, \n", "                project_id = 'sc-bq-gcs-billingonly')\n", "    \n", "    temp_compass_group_ids = temp_compass_group_ids.iloc[0,0]\n", "    temp_compass_group_ids = temp_compass_group_ids.replace(\" \",\"\")\n", "    temp_compass_group_ids = temp_compass_group_ids.split(',')\n", "    if compass_group_ids == []:\n", "        compass_group_ids = temp_compass_group_ids\n", "    \n", "    \n", "except:\n", "    STUDY_START_DATE = STUDY_START_DATE\n", "    STUDY_END_DATE = STUDY_END_DATE\n", "    CAMPAIGN_START_DATE = CAMPAIGN_START_DATE\n", "    RETRO_START_DATE = RETRO_START_DATE\n", "    RETRO_END_DATE = RETRO_END_DATE\n", "    compass_group_ids = compass_group_ids"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# AB metrics section\n", "# combine current function with select by metric_group\n", "ab_console_metrics_catalog = abtest.ABQuestMetricCatalog(STUDY_START_DATE, STUDY_END_DATE,\n", "                                                         aa_start_date=RETRO_START_DATE, aa_end_date=RETRO_END_DATE,bq_priority=BQ_PRIORITY)\n", "\n", "# Keep only the metrics passed from the Husky UI\n", "ab_console_metric_tables = ab_console_metrics_catalog.get_filtered_metric_tables(\n", "    ab_console_metric_groups, ab_console_metrics, dash_group_ids=ab_console_dash_group_ids,\n", "    compass_group_ids=compass_group_ids,\n", "    include_core_metrics=False,\n", "    # bq_priority=BQ_PRIORITY,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["metric_tables = ab_console_metric_tables\n", "if not DAILY_TREND:\n", "    for metric_table in metric_tables:\n", "        metric_table.daily = False\n", "\n", "if \"ab/bitmoji\" in compass_group_ids:\n", "    temp_metric_tables=[]\n", "    for j, metric_table in enumerate(metric_tables):\n", "        if ('builder' in metric_table.name or 'usable' in metric_table.name or 'bounce' in metric_table.name or 'time' in metric_table.name) == False:\n", "#        if 'split' in metric_table.name  == False:\n", "            temp_metric_tables.append(metric_tables[j])\n", "    metric_tables=temp_metric_tables\n", "\n", "if STUDY_START_DATE<=\"20241028\" or RETRO_START_DATE<=\"20241028\" :\n", "    temp_metric_tables=[]\n", "    for j, metric_table in enumerate(metric_tables):\n", "        if ('story_content_story_view' in metric_table.name) == False:\n", "            temp_metric_tables.append(metric_tables[j])\n", "    metric_tables=temp_metric_tables\n", "\n", "temp_metric_tables=[]\n", "for i, metric_table in enumerate(metric_tables):\n", "    number_quantile_metric=0\n", "    for metric in metric_table.metrics:\n", "        if metric.dist == 'quantile':\n", "            number_quantile_metric=number_quantile_metric+1\n", "    if number_quantile_metric==0:\n", "        temp_metric_tables.append(metric_tables[i])\n", "metric_tables=temp_metric_tables\n", "\n", "temp_metric_tables=[]\n", "for m, metric_table in enumerate(metric_tables):\n", "    number_metric=0\n", "    for metric in metric_table.metrics:\n", "        number_metric=number_metric+1\n", "    if number_metric<=40:\n", "        temp_metric_tables.append(metric_tables[m])\n", "metric_tables=temp_metric_tables"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["METRICS_NAME_MAPPING = collections.OrderedDict([\n", "            \n", "])\n", "\n", "if COMPUTE_RETRO_AA:\n", "    for mt in metric_tables:\n", "        mt.aa = True\n", "\n", "for mt in metric_tables:\n", "    for metric in mt.metrics:\n", "        if metric.col in METRICS_NAME_MAPPING:\n", "            metric.name = METRICS_NAME_MAPPING[metric.col]\n", "\n", "if INCLUDE_REVENUE_METRICS:\n", "    from banjo.teams.product import house_ads_revenue\n", "    if COMPUTE_RETRO_AA:\n", "        house_ad_revenue = [house_ads_revenue.house_ads_revenue_metrics(RETRO_START_DATE, STUDY_END_DATE)]\n", "    else:\n", "        house_ad_revenue = [house_ads_revenue.house_ads_revenue_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "    metric_tables = metric_tables + house_ad_revenue"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if USE_BROAD_MAPPING:\n", "    try: \n", "        #Get audience_table\n", "        temp_audience_table = pandas_gbq.read_gbq(\n", "            '''select COALESCE(audience_table,'nan') from `sc-product-datascience.temp_reports.house_ads_live_campaigns_temp` where experiment_id = \"{exp_id}\"'''.format(exp_id = ITT_STUDY_EXP_ID),\n", "                            use_bqstorage_api = True, \n", "                            project_id = 'sc-bq-gcs-billingonly')\n", "        \n", "        temp_audience_table = temp_audience_table.iloc[0,0]\n", "        if temp_audience_table == 'nan':\n", "            temp_audience_table = ''    \n", "        if audience_table == '':\n", "            audience_table = temp_audience_table\n", "        #Get seed      \n", "        temp_seed = pandas_gbq.read_gbq(\n", "            '''select COALESCE(seed,'') from `sc-product-datascience.temp_reports.house_ads_live_campaigns_temp` where experiment_id = \"{exp_id}\"'''.format(exp_id = ITT_STUDY_EXP_ID),\n", "                            use_bqstorage_api = True, \n", "                            project_id = 'sc-bq-gcs-billingonly')\n", "        \n", "        temp_seed = temp_seed.iloc[0,0]\n", "        if SEED_STRING == '':\n", "            SEED_STRING = temp_seed\n", "    except:\n", "        audience_table = audience_table\n", "        SEED_STRING = SEED_STRING"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get Experiment Names - No longer needed as the report class handles this\n", "if not EXP_NAMES:\n", "    EXP_NAMES = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["custom_mapping_sql = \"\"\"\n", "WITH ab_mapping_table AS (\n", "      SELECT\n", "        ghost_user_id,\n", "        itt_group AS exp_id,\n", "        if(\n", "        MIN(PARSE_TIMESTAMP(\"%Y%m%d\", CONCAT(_TABLE_SUFFIX)))<=PARSE_TIMESTAMP(\"%Y%m%d\", '{STUDY_START_DATE}'),\n", "PARSE_TIMESTAMP(\"%Y%m%d\", '{STUDY_START_DATE}'),\n", "MIN(PARSE_TIMESTAMP(\"%Y%m%d\", CONCAT(_TABLE_SUFFIX)))\n", "       ) AS exposure_ds,\n", "        (TIMESTAMP_TRUNC(TIMESTAMP_MILLIS(MIN(SAFE_CAST(server_timestamp AS INT64))),DAY)) AS exposure_ts,\n", "      FROM `sc-analytics.report_growth.house_ads_itt_user_mapping_*`\n", "      WHERE itt_experiment_id = '{STUDY_NAME}'\n", "      AND _TABLE_SUFFIX BETWEEN '{CAMPAIGN_START_DATE}' AND '{STUDY_END_DATE}'\n", "      GROUP BY\n", "        ghost_user_id, exp_id\n", "    )\n", "    SELECT\n", "      ab_mapping_table.*,\n", "      my_breakdown_dimensions.locale,\n", "      my_breakdown_dimensions.l_90_country,\n", "      my_breakdown_dimensions.os_type,\n", "      my_breakdown_dimensions.reported_age_bucket,\n", "      my_breakdown_dimensions.user_persona_v3,\n", "      my_breakdown_dimensions.has_bitmoji,\n", "      my_breakdown_dimensions.story_post_L7,\n", "      my_breakdown_dimensions.with_snapchat_plus,\n", "      CASE\n", "when COALESCE(my_breakdown_dimensions.app_L7,0) between 0 and 1 then 'l7:0-1'\n", "when COALESCE(my_breakdown_dimensions.app_L7,0) between 0 and 6 then 'l7:2-6'\n", "when COALESCE(my_breakdown_dimensions.app_L7,0) between 0 and 7 then 'l7:7'\n", "end AS app_engagement_status,\n", "    FROM\n", "      ab_mapping_table\n", "    LEFT JOIN `sc-analytics.report_search.user_cohorts_{CAMPAIGN_START_DATE}`my_breakdown_dimensions\n", "    USING (ghost_user_id)  \n", "\"\"\".format(\n", "            STUDY_NAME=STUDY_NAME,\n", "            STUDY_START_DATE=STUDY_START_DATE,\n", "            STUDY_END_DATE=STUDY_END_DATE,\n", "            CAMPAIGN_START_DATE=CAMPAIGN_START_DATE)\n", "\n", "###### USE BROAD MAPPING #######\n", "from google.cloud import storage, bigquery\n", "if USE_BROAD_MAPPING:\n", "    if audience_table:\n", "        ghost_ids_table_sql=\"\"\"\n", ",\n", "ghost_ids as (\n", "select distinct ghost_id as ghost_user_id\n", "from {table}\n", "join (SELECT * from `sc-targeting-measurement.user_ids.ghost_id_2*`\n", "WHERE _table_suffix = (SELECT max(_table_suffix) from `sc-targeting-measurement.user_ids.ghost_id_2*`)\n", ")\n", "      on userID = user_id\n", ")\n", "\"\"\".format(table=audience_table)\n", "\n", "    js_get_ab_slices_udf = \"\"\"\n", "CREATE TEMP FUNCTION ittSlice(value STRING) RETURNS INT64 LANGUAGE js \n", "OPTIONS (library=[\n", "        \"gs://report-growth/house-ads/itt-seedfinder/goog.math.Long.js\",\n", "        \"gs://report-growth/house-ads/itt-seedfinder/bignumber.js\",\n", "        \"gs://report-growth/house-ads/itt-seedfinder/murmurhash3js.min.js\"]) \n", "AS \\\"\"\"\n", "    function consistentHash(input, buckets) {\n", "        var candidate = 0;\n", "        var state = input;\n", "        var multiplier = new goog.math.Long.fromString(\"2862933555777941757\");\n", "        var one = new goog.math.Long.fromInt(1);\n", "        while (true) {\n", "            state = state.multiply(multiplier).add(one);\n", "            var nextDouble = state.shiftRightUnsigned(33).add(one).toNumber() / 2147483648;\n", "            var next = parseInt( (candidate + 1) / nextDouble, 10);\n", "            if (next >= 0 && next < buckets) {\n", "                candidate = next;\n", "            } else {\n", "                return candidate;\n", "            }\n", "        }\n", "    }\n", "    var seed = 0;\n", "    var bucket = 100;\n", "    var hashed128BitsHex = murmurHash3.x64.hash128(value, seed);\n", "    var bits64Hex = hashed128BitsHex.substr(0, hashed128BitsHex.toString().length / 2)\n", "    var long = new goog.math.Long.fromString(new BigNumber(bits64Hex, 16).toString(10));\n", "    return consistentHash(long, bucket);\n", "\\\"\"\"; \n", "\"\"\"\n", "\n", "    user_seed_string_score_sql = \"\"\"\n", "SELECT\n", "mapping.ghost_user_id,\n", "min(min_ts) AS min_ts,\n", "ANY_VALUE(ittSlice(CONCAT(said, '{seed_string}'))) AS user_seed_string_score,\n", "FROM\n", "(  \n", "SELECT\n", "ghost_user_id,\n", "min(timestamp(PARSE_DATE('%Y%m%d',_TABLE_SUFFIX))) AS min_ts,\n", "FROM `sc-analytics.report_growth.house_ads_itt_broad_user_mapping_*`a\n", "WHERE _TABLE_SUFFIX BETWEEN '{CAMPAIGN_START_DATE}' AND '{STUDY_END_DATE}'\n", "GROUP BY ghost_user_id\n", ")mapping\n", "join `sc-targeting-measurement.user_ids.said_userid_standard_view` user\n", "ON mapping.ghost_user_id = user.ghost_id\n", "GROUP BY ghost_user_id\n", "\"\"\".format(\n", "    seed_string=SEED_STRING,\n", "    CAMPAIGN_START_DATE=CAMPAIGN_START_DATE,\n", "    STUDY_END_DATE=STUDY_END_DATE)\n", "\n", "    user_seed_string_score_sql = js_get_ab_slices_udf + user_seed_string_score_sql\n", "    # bigquery.Client('sc-bq-gcs-billingonly').query(user_seed_string_score_sql)\n", "    utils.gbq.submit_sync_query(\n", "        user_seed_string_score_sql,\n", "        project_id='sc-product-datascience',\n", "        dest_dataset_id='user_acquisition',\n", "        dest_table_name='user_seed_string_score_' + STUDY_NAME + '_' + STUDY_START_DATE + '_' + STUDY_END_DATE,\n", "        write_disposition=\"WRITE_TRUNCATE\",\n", "        dialect=\"standard\",\n", "        priority=\"INTERACTIVE\",\n", "        retention_days=30\n", "    )\n", "\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if USE_BROAD_MAPPING:\n", "    custom_mapping_sql = \"\"\"\n", "WITH\n", "itt_group_score AS\n", "(\n", "SELECT\n", "itt_group,\n", "user_seed_string_score AS seed_string_score,\n", "FROM `sc-analytics.report_growth.house_ads_itt_user_mapping_2*`a\n", "join `sc-product-datascience.user_acquisition.{user_seed_string_score_table_name}` user\n", "ON a.ghost_user_id = user.ghost_user_id\n", "\n", "WHERE itt_experiment_id = '{STUDY_NAME}'\n", "AND PARSE_DATE('%Y%m%d',concat('2',a._TABLE_SUFFIX)) BETWEEN\n", "DATE_ADD(PARSE_DATE('%Y%m%d','{STUDY_START_DATE}'), INTERVAL 0 day) and\n", "DATE_ADD(PARSE_DATE('%Y%m%d','{STUDY_END_DATE}'), INTERVAL 0 day)\n", "and MOD(date_diff(PARSE_DATE('%Y%m%d',concat('2',a._TABLE_SUFFIX)),PARSE_DATE('%Y%m%d','{STUDY_END_DATE}'),day), 10) = 0\n", "AND itt_group is not null\n", "\n", "GROUP BY 1,2),\n", "\n", "itt_group_country AS\n", "(\n", "SELECT\n", "l_90_country AS itt_group_l_90_country,\n", "FROM `sc-analytics.report_growth.house_ads_itt_user_mapping_2*`a\n", "JOIN `sc-analytics.report_search.user_cohorts_{STUDY_END_DATE}`b\n", "ON a.ghost_user_id=b.ghost_user_id\n", "\n", "WHERE itt_experiment_id = '{STUDY_NAME}'\n", "AND PARSE_DATE('%Y%m%d',concat('2',a._TABLE_SUFFIX)) BETWEEN\n", "DATE_ADD(PARSE_DATE('%Y%m%d','{STUDY_START_DATE}'), INTERVAL 0 day) and\n", "DATE_ADD(PARSE_DATE('%Y%m%d','{STUDY_END_DATE}'), INTERVAL 0 day)\n", "and MOD(date_diff(PARSE_DATE('%Y%m%d',concat('2',a._TABLE_SUFFIX)),PARSE_DATE('%Y%m%d','{STUDY_END_DATE}'),day), 10) = 0\n", "and itt_group ='TEST'\n", "\n", "GROUP BY itt_group_l_90_country),\n", "\n", "\n", "ab_mapping_table AS (  \n", "SELECT\n", "ghost_user_id,\n", "min_ts AS exposure_ts,\n", "if(\n", "min_ts<=PARSE_TIMESTAMP(\"%Y%m%d\", '{STUDY_START_DATE}'),\n", "PARSE_TIMESTAMP(\"%Y%m%d\", '{STUDY_START_DATE}'),\n", "min_ts\n", ") AS exposure_ds,\n", "(CASE\n", " WHEN SAFE_CAST(user_seed_string_score AS STRING) in (SELECT SAFE_CAST(seed_string_score AS STRING) FROM itt_group_score WHERE itt_group=\"TEST\")\n", " AND SAFE_CAST(user_seed_string_score AS STRING) not in (SELECT SAFE_CAST(seed_string_score AS STRING) FROM itt_group_score WHERE itt_group=\"CONTROL\")\n", " THEN 'TEST'\n", " WHEN SAFE_CAST(user_seed_string_score AS STRING) in (SELECT SAFE_CAST(seed_string_score AS STRING) FROM itt_group_score WHERE itt_group=\"CONTROL\")\n", " AND SAFE_CAST(user_seed_string_score AS STRING) not in (SELECT SAFE_CAST(seed_string_score AS STRING) FROM itt_group_score WHERE itt_group=\"TEST\")\n", " THEN 'CONTROL'\n", " END) AS exp_id,\n", "FROM `sc-product-datascience.user_acquisition.{user_seed_string_score_table_name}`\n", ")\n", "\n", "{ghost_ids_table}\n", "\n", "SELECT  \n", "ghost_user_id,\n", "exp_id,\n", "exposure_ts,\n", "exposure_ds,\n", "my_breakdown_dimensions.locale,\n", "      my_breakdown_dimensions.l_90_country,\n", "      my_breakdown_dimensions.os_type,\n", "      my_breakdown_dimensions.reported_age_bucket,\n", "      my_breakdown_dimensions.user_persona_v3,\n", "      my_breakdown_dimensions.has_bitmoji,\n", "      my_breakdown_dimensions.story_post_L7,\n", "      my_breakdown_dimensions.with_snapchat_plus,\n", "      CASE\n", "when COALESCE(my_breakdown_dimensions.app_L7,0) between 0 and 1 then 'l7:0-1'\n", "when COALESCE(my_breakdown_dimensions.app_L7,0) between 0 and 6 then 'l7:2-6'\n", "when COALESCE(my_breakdown_dimensions.app_L7,0) between 0 and 7 then 'l7:7'\n", "end AS app_engagement_status,\n", "FROM  \n", "ab_mapping_table\n", "{ghost_ids_table_join}\n", "LEFT JOIN `sc-analytics.report_search.user_cohorts_{CAMPAIGN_START_DATE}`my_breakdown_dimensions\n", "    USING (ghost_user_id)\n", "    \n", "WHERE exp_id IN ('CONTROL','TEST')\n", "AND l_90_country IN (SELECT itt_group_l_90_country FROM itt_group_country)\n", "\"\"\".format(\n", "            STUDY_NAME=STUDY_NAME,\n", "            STUDY_START_DATE=STUDY_START_DATE,\n", "            STUDY_END_DATE=STUDY_END_DATE,\n", "            CAMPAIGN_START_DATE=CAMPAIGN_START_DATE,\n", "            seed_string=SEED_STRING,\n", "            user_seed_string_score_table_name='user_seed_string_score_' + STUDY_NAME + '_' + STUDY_START_DATE + '_' + STUDY_END_DATE,\n", "            ghost_ids_table=ghost_ids_table_sql if audience_table != '' else \"\",\n", "            ghost_ids_table_join=\"JOIN ghost_ids USING (ghost_user_id)\" if audience_table != '' else \"\"\n", ")\n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Construct the report object\n", "report = CustomReport(\n", "    study_name=STUDY_NAME,\n", "    study_start_date=STUDY_START_DATE,\n", "    aa_start_date=RETRO_START_DATE,\n", "    aa_end_date=RETRO_END_DATE,\n", "    study_end_date=STUDY_END_DATE,\n", "    metric_tables=metric_tables,\n", "    quantiles=quantiles,\n", "    control_id=CONTROL_ID,\n", "    treatment_ids=TREATMENT_IDS,\n", "    user_group_bys=USER_BREAKDOWN_LIST,\n", "    bq_project='sc-bq-gcs-billingonly',  \n", "    dest_dataset='temp_abtest',\n", "    overwrite_mapping_table=OVERWRITE,\n", "    exp_id_to_name=EXP_NAMES,    \n", "    bq_priority=BQ_PRIORITY,\n", "    report_key=report_key,\n", "    excel_output=True,\n", "    mapping_table_ver=\"allocation\" if USE_ALLOCATION_MAPPING else \"v3\",\n", "    custom_mapping_sql=custom_mapping_sql,\n", "    bq_dialect='standard',\n", "    old_mapping_format=False,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["group_filters = {}\n", "if len(COUNTRIES) > 0:\n", "    if 'study_country' in USER_BREAKDOWN_LIST or 'country' in USER_BREAKDOWN_LIST:\n", "        group_filters['study_country'] = COUNTRIES\n", "        group_filters['country'] = COUNTRIES\n", "        # https://github.sc-corp.net/Snapchat/pyanalytics/blob/2383917e818fb71b6582998ecce3e9f5b3ecf213/banjo/abtest/cohort_report.py#L504\n", "    if 'l_90_country' in USER_BREAKDOWN_LIST:\n", "        group_filters['l_90_country'] = COUNTRIES\n", "        \n", "group_filters[\"device_maker\"] = DEVICE_MAKERS        "]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["# Run the joins and calculate the results\n", "report.execute(\n", "    overwrite=OVERWRITE,\n", "    skip_export=True,\n", "    group_filters=group_filters,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export to BQ for dashboard\n", "results_key = report.get_results_key('cumulative', None)\n", "sufficient_stats = report._sufficient_stats[results_key]\n", "\n", "if DASHBOARD_BACKFILL:\n", "    backfill_table_name = 'ab_deep_dive_backfill.{}_{}_{}'.format(\n", "        STUDY_NAME,\n", "        STUDY_START_DATE,\n", "        STUDY_END_DATE\n", "    )\n", "    reshaped_stats = []\n", "    for stat in sufficient_stats:\n", "        reshaped_stat = reshape_aggregated(stat.stats)\n", "        reshaped_stat['metric_name'] = reshaped_stat['variable']\n", "        if \"quantile_vars\" in stat.func_args:\n", "            reshaped_stat['metric_name'] = re.sub(\n", "                r\"_[0-9]+$\",\n", "                \"\",\n", "                stat.func_args[\"quantile_vars\"][0]\n", "            )\n", "        reshaped_stats.append(reshaped_stat)\n", "    backfill_stats = pd.concat(reshaped_stats)\n", "    # print(backfill_stats.shape)\n", "    backfill_stats.to_gbq(backfill_table_name, verbose=False, if_exists='replace')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report.configurations['pivot_table'] = PIVOT_RESULTS\n", "report.configurations['stat_fmtr'] = (\n", "    \"{pct_diff:,.2f}% ({avg_control:,.3}→{avg_treatment:,.3}, {p_value:.4})\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Report print headers\n", "abtest.report.notebook_print(\"Study Name: <a target='_blank' href='https://ab.sc-corp.net/v2/experiment/{0}'>{0}</a>\".format(report.study_name), \"h3\")\n", "abtest.report.notebook_print(\"Seed String: {}\".format(SEED_STRING),\"strong\")\n", "abtest.report.notebook_print(\"Use Broad Mapping: {}\".format(USE_BROAD_MAPPING),\"strong\")\n", "abtest.report.notebook_print(\"Audience Table: {}\".format(audience_table),\"strong\")\n", "abtest.report.notebook_print(\"Control ID: {}\".format(report.control_id), \"strong\")\n", "abtest.report.notebook_print(\"Treatment ID(s): {}\".format(\", \".join(report.treatment_ids)), \"strong\")\n", "abtest.report.notebook_print(\n", "    \"Analysis period: {} - {}\".format(STUDY_START_DATE, STUDY_END_DATE), \n", "    \"strong\"\n", ")\n", "\n", "\n", "abtest.report.notebook_print(\n", "    \"Quantiles compared for quantile metrics: {}\".format(\n", "        \", \".join(quantile for quantile in report.quantiles)\n", "    ),\n", "    \"strong\"\n", ")\n", "\n", "abtest.report.notebook_print(\n", "    \"Breakdowns: <br> {}\".format(\n", "        \"<br>\".join(\", \".join(breakdown) for breakdown in report.user_group_by_list)\n", "    ), \n", "    \"strong\"\n", ")\n", "\n", "if USE_BROAD_MAPPING:\n", "    if audience_table:\n", "        abtest.report.notebook_print(\n", "            \"\\nUsing broader map filtered down to the users during seed split:\",\n", "            \"h3\"\n", "        )\n", "    else:\n", "        abtest.report.notebook_print(\n", "            \"\\nUsing broader exposure map:\",\n", "            \"h3\"\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# filter output metrics\n", "def get_metric_names(metric_tables, quantiles=False):\n", "    return [\n", "        metric.col\n", "        for metric_table in metric_tables\n", "        for metric in metric_table.metrics\n", "        if metric_table.quantile_metrics == quantiles\n", "    ]\n", "\n", "\n", "ab_quantile_metrics_name = get_metric_names(ab_console_metric_tables, quantiles=True)\n", "ab_regular_metrics_name = get_metric_names(ab_console_metric_tables, quantiles=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if ab_regular_metrics_name:\n", "    report.ab_printer.print_text(\"A/B Console Metrics - Counts, Ratios and Timespent\", \"h2\")\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': []},\n", "        metric_filters={\n", "            'metrics': ab_regular_metrics_name\n", "        },\n", "        group_filters=group_filters\n", "    );\n", "    if DAILY_TREND:\n", "        # only show trend for overall\n", "        report.generate_report_skeleton(\n", "            group_bys=None,\n", "            group_filters=None,\n", "            format_pvalue=True,\n", "            extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "            display_config={'cumulative': [], 'daily': daily_display},\n", "            metric_filters={\n", "                'metrics': ab_regular_metrics_name\n", "            },\n", "        )       "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["quantile_fmtr = (\n", "    \"{pct_diff:,.2f}% ({quantile_control:,.3}→{quantile_treatment:,.3}, {p_value:.4})<br>\"\n", "    \"Event count: {pct_diff_event_count:,.2f}% \"\n", "    \"({avg_volume_control:,.3}→{avg_volume_treatment:,.3}, {p_value_event_count:.4})\"\n", ")\n", "if not PIVOT_RESULTS:\n", "    quantile_fmtr = \"P{quantile}: \" + quantile_fmtr\n", "\n", "#if ab_quantile_metrics_name:\n", "if 0==1:\n", "    report.ab_printer.print_text(\"A/B Console Metrics - Quantiles\", \"h2\")\n", "    report.generate_report(\n", "        extra_table_cols=[\n", "            'control_id', 'treatment_id',\n", "            'count_control', 'count_treatment',\n", "            'volume_control', 'volume_treatment',\n", "        ],\n", "        format_pvalue=True,\n", "        metric_filters={\n", "            'metrics': ab_quantile_metrics_name\n", "        },\n", "        group_filters=group_filters,\n", "        display_config={\"cumulative\": [\"table\"], \"daily\": []},\n", "        stat_fmtr=quantile_fmtr,\n", "    );\n", "    if DAILY_TREND:\n", "        # only show trend for overall\n", "        report.generate_report_skeleton(\n", "            facet_bys=[\"quantile\"],\n", "            group_bys=None,\n", "            group_filters=None,\n", "            format_pvalue=True,\n", "            extra_table_cols=[\n", "                'control_id', 'treatment_id',\n", "                'count_control', 'count_treatment',\n", "                'volume_control', 'volume_treatment',\n", "            ],\n", "            display_config={'cumulative': [], 'daily': daily_display},\n", "            metric_filters={\n", "                'metrics': ab_quantile_metrics_name\n", "            },\n", "            stat_fmtr=quantile_fmtr,\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if INCLUDE_REVENUE_METRICS:\n", "    report.configurations['pivot_table'] = PIVOT_RESULTS\n", "    report.configurations['stat_fmtr'] = (\n", "          \"{pct_diff:,.2f}% ({avg_control:,.4f}→{avg_treatment:,.4f}, {p_value_formatted})\"\n", "\n", "    )\n", "    house_ad_revenue_metrics = [m for mt in house_ad_revenue for m in mt.cumulative_metrics]\n", "    # house ads revenue metrics\n", "    report.ab_printer.print_text(\"Revenue Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': house_ad_revenue_metrics\n", "        },\n", "    );"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if VELLUM:\n", "    excel_file_url = report.upload_excel_output_to_gcs()\n", "    if excel_file_url:\n", "        display(HTML(\n", "            \"<p><strong><a href='{}' target='__blank'>Click to download tables in excel format</a>. \"\n", "            \"You can upload it to Google Drive and open in Google Sheets.</strong></p>\".format(excel_file_url)\n", "        ))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report.ab_printer.print_text(\"Calculate Incremental Metrics\", 'h2')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_rollout_impact(df_metric):\n", "    assert df_metric.shape[0] == 1\n", "    \n", "    df = df_metric.copy()\n", "    \n", "    avg_c = df['avg_control'].values[0]\n", "    avg_t = df['avg_treatment'].values[0]\n", "    n_c = df['count_control'].values[0]\n", "    n_t = df['count_treatment'].values[0]\n", "    p_val= df['p_value'].values[0]\n", "    \n", "    rollout_impact = (avg_t - avg_c) * (n_t)\n", "    #rollout_impact_daily = rollout_impact / n_days\n", "    # print(f\"Incremental Metrics: {round(rollout_impact, 2)}; p-value: {round(p_val, 2)}\")    \n", "    return round(rollout_impact, 2)\n", "    \n", "    #print(f\"(Absolute) Rollout Impact (Daily): {round(rollout_impact_daily, 2)}\")\n", "    #print(f\"(Absolute) Rollout Impact (Annually): {round(rollout_impact_daily * 365, 2)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_incrementality(report, COMPUTE_RETRO_AA = False):\n", "    incremental_metrics_df = pd.DataFrame(columns = ['study_name', 'study_start_date', 'study_end_date', 'breakdown', 'breakdown_value', 'exposure_date', 'metric', 'metric_name', 'treatment_id', 'period', 'count_control', 'count_treatment', 'sum_control', 'sum_treatment',\n", "    'avg_control', 'avg_treatment', 'p_value', 'rollout_impact'])   \n", "\n", "    i = 0\n", "\n", "    for breakdown in report.results.keys():\n", "        df_results = report.results[breakdown]\n", "\n", "        metrics_rollout_impact = df_results['metric']\n", "        treatment_ids_rollout_impact = TREATMENT_IDS\n", "\n", "        metric_names = pd.DataFrame(columns = ['metric', 'metric_name'])\n", "\n", "        row_counter = 0\n", "        for metric_table in metric_tables:\n", "            for metric in metric_table.metrics:\n", "                metric_names.loc[row_counter] = [metric.col, metric.name]\n", "                row_counter += 1\n", "\n", "        df_results = pd.merge(df_results, metric_names, how = 'inner', on = 'metric')\n", "        # print(df_results)\n", "        if COMPUTE_RETRO_AA == False:\n", "            df_results['period'] = 'AB'\n", "\n", "        for treatment_id in treatment_ids_rollout_impact:\n", "            for metric in metrics_rollout_impact.unique():\n", "                for period in df_results[(df_results['metric'] == metric) & (df_results['treatment_id'] == treatment_id)].period.unique():            \n", "                    try:\n", "                        select_rows = (df_results['metric'] == metric) & (df_results['treatment_id'] == treatment_id) & (df_results['period'] == period)\n", "                        df_results_selected = df_results.loc[select_rows][['exp_ds', 'metric_name', 'count_control', 'sum_control', 'count_treatment', 'sum_treatment', 'avg_control', 'avg_treatment', 'p_value', 'breakdown_values']]\n", "\n", "                        #treatment_target_ratio = df_study_info.loc[df_study_info['exp_id'] == treatment_id]['pct_population'].values[0]\n", "                        #control_target_ratio = df_study_info.loc[df_study_info['exp_id'] == CONTROL_ID]['pct_population'].values[0]\n", "                        for breakdown_value in df_results_selected.breakdown_values.unique():\n", "\n", "                            df_breakdown = df_results_selected[df_results_selected.breakdown_values == breakdown_value]                                    \n", "\n", "                            if 'daily' in breakdown:\n", "                                 for d in df_breakdown.exp_ds.unique():\n", "                                        df_breakdown_day = df_breakdown[df_breakdown.exp_ds == d]\n", "                                        # df_breakdown_day['exp_ds'] = pd.to_datetime(df_breakdown_day['exp_ds'])\n", "                                        rollout_impact = get_rollout_impact(df_breakdown_day.drop(columns = ['metric_name', 'breakdown_values']))\n", "\n", "                                        row = [STUDY_NAME, STUDY_START_DATE, STUDY_END_DATE, breakdown, breakdown_value, df_breakdown_day['exp_ds'].values[0], metric, df_breakdown_day['metric_name'].values[0], treatment_id, period,                             \n", "                                                 df_breakdown_day['count_control'].values[0], \n", "                                                 df_breakdown_day['count_treatment'].values[0] , \n", "                                                 df_breakdown_day['sum_control'].values[0], \n", "                                                 df_breakdown_day['sum_treatment'].values[0], \n", "                                                 df_breakdown_day['avg_control'].values[0], \n", "                                                 df_breakdown_day['avg_treatment'].values[0], \n", "                                                 df_breakdown_day['p_value'].values[0],  \n", "                                                 rollout_impact]\n", "                                        incremental_metrics_df.loc[i] = row\n", "                                        i += 1\n", "\n", "                            else:\n", "                                rollout_impact = get_rollout_impact(df_breakdown.drop(columns = ['metric_name', 'breakdown_values']))\n", "\n", "                                row = [STUDY_NAME, STUDY_START_DATE, STUDY_END_DATE, breakdown, breakdown_value, df_breakdown['exp_ds'].values[0], metric, df_breakdown['metric_name'].values[0], treatment_id, period,                             \n", "                                     df_breakdown['count_control'].values[0], \n", "                                     df_breakdown['count_treatment'].values[0] , \n", "                                     df_breakdown['sum_control'].values[0], \n", "                                     df_breakdown['sum_treatment'].values[0], \n", "                                     df_breakdown['avg_control'].values[0], \n", "                                     df_breakdown['avg_treatment'].values[0], \n", "                                     df_breakdown['p_value'].values[0],  \n", "                                     rollout_impact]\n", "                                incremental_metrics_df.loc[i] = row\n", "                                i += 1                                    \n", "\n", "\n", "                    except:\n", "                        pass # print(f\"Incremental estimation failed for {metric}\")    \n", "    return incremental_metrics_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if CALCULATE_INCREMENTAL_METRICS:\n", "    \n", "    incremental_metrics_df = calculate_incrementality(report, COMPUTE_RETRO_AA)\n", "\n", "    pd.options.display.float_format = '{:.3f}'.format\n", "    display(HTML(incremental_metrics_df.to_html(\n", "            header=True,\n", "            index=True,\n", "            escape=False)))\n", "\n", "    # For BigQ<PERSON>y Read\n", "    from google.cloud import storage, bigquery\n", "    import google.auth\n", "\n", "    #######  CREATE BQ CLIENT ########\n", "    client = bigquery.Client(project=\"sc-bq-gcs-billingonly\")\n", "\n", "    def write_to_gbq(df_to_upload, table):\n", "\n", "        job_config = bigquery.LoadJobConfig(\n", "            # below is equivalent to if_exists=\"replace\"\n", "            write_disposition=\"WRITE_TRUNCATE\",\n", "        )\n", "\n", "        bq_table = table\n", "\n", "        job = client.load_table_from_dataframe(\n", "            df_to_upload, \n", "            bq_table , \n", "            job_config=job_config\n", "        ) \n", "\n", "        # Make an API request.\n", "        print(bq_table)\n", "        return job.result()\n", "\n", "    if USE_BROAD_MAPPING:\n", "       output_table = 'sc-product-datascience.user_acquisition.ITT_STUDY_' + STUDY_NAME + '_' + STUDY_START_DATE + '_' + STUDY_END_DATE + '_broad_mapping_incrementality_results'\n", "    else:\n", "       output_table = 'sc-product-datascience.user_acquisition.ITT_STUDY_' + STUDY_NAME + '_' + STUDY_START_DATE + '_' + STUDY_END_DATE + '_incrementality_results'\n", "\n", "    if OUTPUT_TO_BIGQUERY:\n", "       write_to_gbq(incremental_metrics_df, output_table)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"anaconda-cloud": {}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}