{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["from datetime import timedelta\n", "import pandas as pd\n", "import os\n", "if any(['VELLUM' in k for k in os.environ.keys()]):\n", "    pip_output = !pip install banjo\n", "\n", "# import seaborn as sns\n", "import logging\n", "\n", "logging.basicConfig()\n", "logging.getLogger().setLevel(logging.ERROR)\n", "\n", "from google.auth import _default\n", "from google.cloud import bigquery\n", "_default._LOGGER.setLevel(logging.ERROR)\n", "_default._warn_about_problematic_credentials = lambda _: None\n", "\n", "%matplotlib inline\n", "%config InlineBackend.figure_format = 'retina'\n", "\n", "from banjo import abtest # see: go/pya\n", "from banjo.abtest.report import ( Metric, MetricTable )\n", "from banjo.abtest.field_breakdown_metric_table import FieldBreakdownMetricTable\n", "from banjo.abtest.cohort_report import CohortReport\n", "\n", "PROJECT = 'content-husky' # 'sc-bq-gcs-billingonly'\n", "\n", "from IPython.core.display import display, HTML\n", "display(HTML(\"<style>.output_html.rendered_html table { font-size:8pt;}</style>\"))\n", "\n", "from banjo.teams.personalization.abtest import discover_feed_ab_utils as dfab\n", "from banjo.teams.personalization.abtest import discover_feed_metrics as dfm\n", "\n", "from banjo.teams.product.lens_ranking_metrics import metrics_config\n", "import warnings\n", "warnings.filterwarnings(\"ignore\", category=RuntimeWarning) "]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["##################\n", "## Notebook Config\n", "##################"]}, {"cell_type": "code", "execution_count": 59, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Specify study details\n", "STUDY_NAME = ''\n", "STUDY_START_DATE = ''\n", "STUDY_END_DATE = ''\n", "RETRO_START_DATE = None\n", "RETRO_END_DATE = None\n", "\n", "COHORT_DATE = None\n", "\n", "USER_BREAKDOWN_LIST = [\n", "]\n", "\n", "COUNTRIES = [\n", "]\n", "\n", "CONTROL_ID = '61' #\n", "TREATMENT_IDS = ['62']\n", "\n", "SHOW_AA_CUPED_RESULTS = \"no_aa_cuped_\"\n", "INCLUDE_DAILY_RESULTS = True\n", "SHOW_METRIC_COMPARISONS = \"Yes\"\n", "\n", "OVERWRITE = False\n", "SKIP_EXPORT = False\n", "\n", "METRICS_GROUP_CONFIG = [\n", "    'creator_user_overall_metrics',\n", "    # 'creator_user_lens_type_metrics',\n", "    # 'creator_user_num_lenses_metrics',\n", "    # 'creator_user_num_rankable_lenses_metrics',\n", "    # 'creator_user_creator_l90_country_metrics',\n", "    # 'creator_user_creator_follower_bucket_metrics',\n", "    # 'creator_user_creator_days_since_creation_metrics',\n", "    # 'creator_user_mas_type_metrics',\n", "    # 'creator_user_creator_top1_taxonomy_metrics',\n", "    # 'creator_user_source_application_metrics',\n", "    # 'creator_user_sln_type_metrics',\n", "    # 'creator_user_is_lcr_participated_metrics',\n", "    # 'creator_user_is_lcr_rewarded_metrics'\n", "]\n", "\n", "overall = [\n", "    'OVERALL'\n", "]\n", "\n", "lens_type = [\n", "    'Comminuty',\n", "    'Oraganic'\n", "]\n", "\n", "num_lenses = [\n", "    'bkt_1',\n", "    'bkt_2_5',\n", "    'bkt_6_10',\n", "    'bkt_10_plus'\n", "]\n", "\n", "num_rankable_lenses = [\n", "    'bkt__1',\n", "    'bkt__2_5',\n", "    'bkt__6_10',\n", "    'bkt__10_plus'\n", "]\n", "\n", "creator_l90_country = [\n", "    'US',\n", "    'GB',\n", "    'FR',\n", "    'SA',\n", "    'CA',\n", "    'DE',\n", "    'AU',\n", "    'NL',\n", "    'NO',\n", "    'SE',\n", "    'AE',\n", "    'IN',\n", "    'ROW'\n", "]\n", "\n", "creator_follower_bucket = [\n", "    'bkt_100', \n", "    'bkt_100_1K', \n", "    'bkt_1K_5K', \n", "    'bkt_5K_10K', \n", "    'bkt_10K_100K', \n", "    'bkt_100K_1M', \n", "    'bkt_1M'\n", "]\n", "\n", "creator_days_since_creation = [\n", "    'bkt_0', \n", "    'bkt_1_14', \n", "    'bkt_15_30', \n", "    'bkt_31_90', \n", "    'bkt_91_365', \n", "    'bkt_365'\n", "]\n", "\n", "mas_type = [\n", "    'NEW_MAS',\n", "    'RESURRECTED_MAS',\n", "    'CHURNED_MAS',\n", "    'RETAINED_MAS'\n", "]\n", "\n", "creator_top1_taxonomy = [\n", "    'Movies_TV_Shows',\n", "    'Games',\n", "    'Funny',\n", "    'Color_Light',\n", "    'Backgrounds',\n", "    'Self_Expression',\n", "    'Characters',\n", "    'Appearance',\n", "    'No_Applicable_Category',\n", "    'Camera_Styles'\n", "]\n", "\n", "source_application = [\n", "    'LensApplier',\n", "    'FinalizeBBGLensTool',\n", "    'SnapAI',\n", "    'ProfileManager',\n", "    'LensStudioWeb',\n", "    'LensStudioMobile',\n", "    'LensWebBuilder',\n", "    'LensStudio'\n", "]\n", "\n", "sln_type = [\n", "    'Creator',\n", "    'Developer',\n", "    'Partner'\n", "]\n", "\n", "is_lcr_participated = [\n", "    'Yes_',\n", "    'No_'\n", "]\n", "\n", "is_lcr_rewarded = [\n", "    'Yes',\n", "    'No'\n", "]\n", "\n", "USE_BATCH_BQ_PRIORITY = \"False\"\n", "\n", "\n", "# Winners and Losers\n", "SHOW_WINNERS_LOSERS = \"l7_winners_losers\" # no_winners_losers - l7_winners_losers\n", "WL_ORDER_BY = [\"lens_weighted_actions_count\"]\n", "WL_DISPLAY_METRICS = [\n", "    \"lens_weighted_actions_count\",\n", "    # \"lens_icon_impression_count\",\n", "    # \"lens_swipe_count\",\n", "    # \"lens_play_count\",\n", "    # \"lens_play_6_to_30_sec_count\",\n", "    # \"lens_play_30_plus_sec_count\",\n", "    # \"lens_snap_create_count\",\n", "    # \"lens_send_count\",\n", "    # \"lens_save_count\",\n", "    # \"lens_post_count\",\n", "    # \"lens_share_count\",\n", "    # \"lens_action_count\",\n", "    # \"lens_spin_count\",\n", "\n", "    # \"swipes_lens_tenure_1_month_count\",\n", "    # \"swipes_lens_tenure_1_to_3_month_count\",\n", "    # \"swipes_lens_tenure_3_to_6_month_count\",\n", "    # \"swipes_lens_tenure_1_to_6_month_count\",\n", "    # \"swipes_lens_tenure_6_to_12_month_count\",\n", "    # \"swipes_lens_tenure_12_month_plus_count\",\n", "\n", "    # \"lens_infocard_open_count\",\n", "    # \"lens_infocard_favorite_count\",\n", "    # \"lens_infocard_unfavorite_count\",\n", "    # \"lens_button_favorite_count\",\n", "    # \"lens_button_unfavorite_count\",\n", "    # \"lens_removal_count\",\n", "    # \"lens_report_count\",\n", "    # \"lens_infocard_view_profile_count\",\n", "    # \"lens_infocard_more_lenses_count\",\n", "    # \"lens_infocard_copy_link_count\",\n", "    # \"lens_infocard_export_count\",\n", "    # \"lens_infocard_share_inchat_count\",\n", "    # \"lens_infocard_subscribe_count\",\n", "    # \"lens_infocard_viewtopics_count\"\n", "]\n", "WL_BREAKDOWN_LIST = [\n", "    # 'is_organic_creator',\n", "    # 'creator_l90_country',\n", "    # 'creator_days_since_creation',\n", "    # 'mas_type',\n", "    # 'creator_top1_taxonomy',\n", "    # 'source_application',\n", "    # 'sln_type',\n", "    # 'is_lcr_participated',\n", "    # 'is_lcr_rewarded'\n", "]\n", "EQUAL_DOWN_SAMPLE = None\n", "TOP_N = \"50000\""]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [], "source": ["test_or_prod = \"PROD\" #TEST\n", "if test_or_prod == \"TEST\":\n", "    test_or_prod_query =  \"AND MOD(ABS(FARM_FINGERPRINT(lens_user_ghost_user_id)), 1000) = 0\"\n", "else:\n", "    test_or_prod_query = \"\"\n"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [], "source": ["#################\n", "## Metrics Config\n", "#################"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["study_start_date = STUDY_START_DATE\n", "study_end_date   = STUDY_END_DATE\n", "COHORT_DATE = (pd.to_datetime(STUDY_START_DATE) + timedelta(days=-1)).strftime(\"%Y%m%d\") if not COHORT_DATE else COHORT_DATE\n", "\n", "# Ensure certain items passed from husky are lists\n", "def ensure_list(parameter):\n", "    if isinstance(parameter, list):\n", "        return parameter\n", "    else:\n", "        return [parameter]\n", "\n", "overall = ensure_list(overall)\n", "lens_type = ensure_list(lens_type)\n", "num_lenses = ensure_list(num_lenses)\n", "num_rankable_lenses = ensure_list(num_rankable_lenses)\n", "creator_l90_country = ensure_list(creator_l90_country)\n", "creator_follower_bucket = ensure_list(creator_follower_bucket)\n", "creator_days_since_creation = ensure_list(creator_days_since_creation)\n", "mas_type = ensure_list(mas_type)\n", "creator_top1_taxonomy = ensure_list(creator_top1_taxonomy)\n", "source_application = ensure_list(source_application)\n", "sln_type = ensure_list(sln_type)\n", "is_lcr_participated = ensure_list(is_lcr_participated)\n", "is_lcr_rewarded = ensure_list(is_lcr_rewarded)\n", "\n", "METRICS_GROUP_CONFIG = ensure_list(METRICS_GROUP_CONFIG)\n", "TREATMENT_IDS = ensure_list(TREATMENT_IDS)\n", "COUNTRIES = ensure_list(COUNTRIES)\n", "\n", "ALL_METRICS_GROUPS = {}\n", "\n", "BQ_PRIORITY = \"BATCH\" if USE_BATCH_BQ_PRIORITY == \"True\" else \"INTERACTIVE\"\n", "\n", "WL_ORDER_BY = ensure_list(WL_ORDER_BY)\n", "TREATMENT_IDS = ensure_list(TREATMENT_IDS)\n", "USER_BREAKDOWN_LIST = ensure_list(USER_BREAKDOWN_LIST)\n", "if EQUAL_DOWN_SAMPLE:\n", "    equal_down_sample = int(EQUAL_DOWN_SAMPLE)\n", "else:\n", "    equal_down_sample = EQUAL_DOWN_SAMPLE\n", "top_n = int(TOP_N)\n", "WL_BREAKDOWN_LIST = ensure_list(WL_BREAKDOWN_LIST)\n", "WL_DISPLAY_METRICS = ensure_list(WL_DISPLAY_METRICS)\n", "WL_DISPLAY_METRICS = list(set(WL_DISPLAY_METRICS).union(WL_ORDER_BY))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["### App Metrics"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["CREATOR_USER_AB_TEMPLATE_USER = \"\"\"\n", "    SELECT ghost_user_id, ts,\n", "      {inner_metric_columns}\n", "    FROM (\n", "        SELECT lens_user_ghost_user_id AS ghost_user_id,\n", "               TIMESTAMP(DATE(event_date)) AS ts,\n", "\n", "            --- Breakdowns ---\n", "            is_organic_creator,\n", "            num_lenses_cohort,\n", "            num_rankable_lenses_cohort,\n", "            creator_l90_country,\n", "            creator_follower_bucket,\n", "            creator_days_since_creation,\n", "            mas_type,\n", "            creator_top1_taxonomy,\n", "            source_application,\n", "            sln_type,\n", "            is_lcr_participated,\n", "            is_lcr_rewarded,\n", "\n", "            --- <PERSON> ---\n", "            --- --- Lens Core Metrics\n", "            sum(lens_weighted_actions) as lens_weighted_actions_count,\n", "            sum(lens_icon_impression_count) as lens_icon_impression_count,\n", "            sum(lens_swipe_count) as lens_swipe_count,\n", "            sum(lens_play_count) as lens_play_count,\n", "            sum(lens_play_6_to_30_sec_count) as lens_play_6_to_30_sec_count,\n", "            sum(lens_play_30_plus_sec_count) as lens_play_30_plus_sec_count,\n", "            sum(lens_snap_create_count) as lens_snap_create_count,\n", "            sum(lens_send_count) as lens_send_count,\n", "            sum(lens_save_count) as lens_save_count,\n", "            sum(lens_post_count) as lens_post_count,\n", "            sum(lens_share_count) as lens_share_count,\n", "            sum(lens_action_count) as lens_action_count,\n", "            sum(lens_spin_count) as lens_spin_count,\n", "\n", "            --- --- Lens Freshness Metrics\n", "            sum(swipes_lens_tenure_1_month) as swipes_lens_tenure_1_month_count,\n", "            sum(swipes_lens_tenure_1_to_3_month) as swipes_lens_tenure_1_to_3_month_count,\n", "            sum(swipes_lens_tenure_3_to_6_month) as swipes_lens_tenure_3_to_6_month_count,\n", "            sum(swipes_lens_tenure_1_to_6_month) as swipes_lens_tenure_1_to_6_month_count,\n", "            sum(swipes_lens_tenure_6_to_12_month) as swipes_lens_tenure_6_to_12_month_count,\n", "            sum(swipes_lens_tenure_12_month_plus) as swipes_lens_tenure_12_month_plus_count,\n", "\n", "            --- --- Lens Info Card Metrics \n", "            sum(lens_infocard_open_count) as lens_infocard_open_count,\n", "            sum(lens_infocard_favorite_count) as lens_infocard_favorite_count,\n", "            sum(lens_infocard_unfavorite_count) as lens_infocard_unfavorite_count,\n", "            sum(lens_button_favorite_count) as lens_button_favorite_count,\n", "            sum(lens_button_unfavorite_count) as lens_button_unfavorite_count,\n", "            sum(lens_removal_count) as lens_removal_count,\n", "            sum(lens_report_count) as lens_report_count,\n", "            sum(lens_infocard_view_profile_count) as lens_infocard_view_profile_count,\n", "            sum(lens_infocard_more_lenses_count) as lens_infocard_more_lenses_count,\n", "            sum(lens_infocard_copy_link_count) as lens_infocard_copy_link_count,\n", "            sum(lens_infocard_export_count) as lens_infocard_export_count,\n", "            sum(lens_infocard_share_inchat_count) as lens_infocard_share_inchat_count,\n", "            sum(lens_infocard_subscribe_count) as lens_infocard_subscribe_count,\n", "            sum(lens_infocard_viewtopics_count) as lens_infocard_viewtopics_count     \n", "        FROM\n", "        `sc-analytics.report_lens.lens_user_creator_cohort_lvl_agg_*`\n", "        WHERE _TABLE_SUFFIX BETWEEN '{start_date}' AND '{end_date}'\n", "        GROUP BY ALL\n", "    )\n", "    GROUP BY\n", "      1,\n", "      2\n", "\"\"\"\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["CREATOR_USER_METRICS = [ {'metric_name': f'{k}', 'sum_columns': f'{k}', 'if_filter': ''} for k, _ in metrics_config['lens_user_creator_cohort'].items() if k.endswith('_count')]\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def _filter_config(configs, metric_prefixes, by_key=\"slice\"):\n", "    return [config for config in configs if config[by_key] in metric_prefixes]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creator User Metric Breakdown"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# Creator User Metrics Overall\n", "if 'creator_user_overall_metrics' in METRICS_GROUP_CONFIG:\n", "    ALL_METRICS_GROUPS['creator_user_overall_metrics'] = \\\n", "        dfm.discover_feed_metrics(start_date=study_start_date,\n", "                                end_date=study_end_date,\n", "                                sql_template=CREATOR_USER_AB_TEMPLATE_USER,\n", "                                slices=_filter_config([{'slice': 'OVERALL', 'metric_filter': '''ghost_user_id IS NOT NULL'''}], overall),\n", "                                metric_config_list=CREATOR_USER_METRICS,\n", "                                metric_group='creator_user_overall_metrics',\n", "                                bq_dialect='standard'\n", "                                )"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# Creator User Metrics x Lens Type\n", "if 'creator_user_lens_type_metrics' in METRICS_GROUP_CONFIG:\n", "    LENS_TYPE_SLICES = [\n", "        {'slice': '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n", "        'metric_filter': '''\n", "        (is_organic_creator = 0)\n", "        '''},\n", "        {'slice': 'Oraganic',\n", "        'metric_filter': '''\n", "        (is_organic_creator = 1)\n", "        '''}\n", "    ]\n", "\n", "    ALL_METRICS_GROUPS['creator_user_lens_type_metrics'] = \\\n", "        dfm.discover_feed_metrics(start_date=study_start_date,\n", "                                end_date=study_end_date,\n", "                                sql_template=CREATOR_USER_AB_TEMPLATE_USER,\n", "                                slices=_filter_config(LENS_TYPE_SLICES, lens_type),\n", "                                metric_config_list=CREATOR_USER_METRICS,\n", "                                metric_group='creator_user_lens_type_metrics',\n", "                                bq_dialect='standard'\n", "                                )"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# Creator User Metrics x Num Lenses\n", "if 'creator_user_num_lenses_metrics' in METRICS_GROUP_CONFIG:\n", "    slice_mapping = {\n", "        'bkt_1': '1',\n", "        'bkt_2_5': '2-5',\n", "        'bkt_6_10': '6-10',\n", "        'bkt_10_plus': '10_plus'\n", "    }\n", "\n", "    NUM_LENS_SLICES = [{'slice': f'{i}','metric_filter': f\"(num_lenses_cohort = '{slice_mapping[i]}')\"} for i in num_lenses]\n", "\n", "    ALL_METRICS_GROUPS['creator_user_num_lenses_metrics'] = \\\n", "        dfm.discover_feed_metrics(start_date=study_start_date,\n", "                                end_date=study_end_date,\n", "                                sql_template=CREATOR_USER_AB_TEMPLATE_USER,\n", "                                slices=_filter_config(NUM_LENS_SLICES, num_lenses),\n", "                                metric_config_list=CREATOR_USER_METRICS,\n", "                                metric_group='creator_user_num_lenses_metrics',\n", "                                bq_dialect='standard'\n", "                                )"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# Creator User Metrics x Num Rankable Lenses\n", "if 'creator_user_num_rankable_lenses_metrics' in METRICS_GROUP_CONFIG:\n", "    slice_mapping = {\n", "        'bkt__1': '1',\n", "        'bkt__2_5': '2-5',\n", "        'bkt__6_10': '6-10',\n", "        'bkt__10_plus': '10_plus'\n", "    }\n", "\n", "    NUM_LENS_RANKABLE_SLICES = [{'slice': f'{i}','metric_filter': f\"(num_rankable_lenses_cohort = '{slice_mapping[i]}')\"} for i in num_rankable_lenses]\n", "\n", "    ALL_METRICS_GROUPS['creator_user_num_rankable_lenses_metrics'] = \\\n", "        dfm.discover_feed_metrics(start_date=study_start_date,\n", "                                end_date=study_end_date,\n", "                                sql_template=CREATOR_USER_AB_TEMPLATE_USER,\n", "                                slices=_filter_config(NUM_LENS_RANKABLE_SLICES, num_rankable_lenses),\n", "                                metric_config_list=CREATOR_USER_METRICS,\n", "                                metric_group='creator_user_num_rankable_lenses_metrics',\n", "                                bq_dialect='standard'\n", "                                )"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["# Creator User Metrics x Creator L90 Country\n", "if 'creator_user_creator_l90_country_metrics' in METRICS_GROUP_CONFIG:\n", "    CREATOR_L90_COUNTRY_SLICES = [\n", "        {'slice': 'US',\n", "        'metric_filter': '''\n", "        (creator_l90_country = 'US')\n", "        '''},\n", "        {'slice': 'GB',\n", "        'metric_filter': '''\n", "        (creator_l90_country = 'GB')\n", "        '''},\n", "        {'slice': 'FR',\n", "        'metric_filter': '''\n", "        (creator_l90_country = 'FR')\n", "        '''},\n", "        {'slice': 'SA',\n", "        'metric_filter': '''\n", "        (creator_l90_country = 'SA')\n", "        '''},\n", "        {'slice': 'CA',\n", "        'metric_filter': '''\n", "        (creator_l90_country = 'CA')\n", "        '''},\n", "        {'slice': 'DE',\n", "        'metric_filter': '''\n", "        (creator_l90_country = 'DE')\n", "        '''},\n", "        {'slice': 'AU',\n", "        'metric_filter': '''\n", "        (creator_l90_country = 'AU')\n", "        '''},\n", "        {'slice': 'NL',\n", "        'metric_filter': '''\n", "        (creator_l90_country = 'NL')\n", "        '''},\n", "        {'slice': 'NO',\n", "        'metric_filter': '''\n", "        (creator_l90_country = 'NO')\n", "        '''},\n", "        {'slice': 'SE',\n", "        'metric_filter': '''\n", "        (creator_l90_country = 'SE')\n", "        '''},\n", "        {'slice': 'AE',\n", "        'metric_filter': '''\n", "        (creator_l90_country = 'AE')\n", "        '''},\n", "        {'slice': 'IN',\n", "        'metric_filter': '''\n", "        (creator_l90_country = 'IN')\n", "        '''},\n", "        {'slice': 'ROW',\n", "        'metric_filter': '''\n", "        (creator_l90_country NOT IN ('US', 'GB', 'FR', 'SA', 'CA', 'DE', 'AU', 'NL', 'NO', 'SE', 'AE', 'IN'))\n", "        '''}\n", "    ]\n", "\n", "    ALL_METRICS_GROUPS['creator_user_creator_l90_country_metrics'] = \\\n", "        dfm.discover_feed_metrics(start_date=study_start_date,\n", "                                end_date=study_end_date,\n", "                                sql_template=CREATOR_USER_AB_TEMPLATE_USER,\n", "                                slices=_filter_config(CREATOR_L90_COUNTRY_SLICES, creator_l90_country),\n", "                                metric_config_list=CREATOR_USER_METRICS,\n", "                                metric_group='creator_user_creator_l90_country_metrics',\n", "                                bq_dialect='standard'\n", "                                )"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# Creator User Metrics x Creator Follower Bucket\n", "if 'creator_user_creator_follower_bucket_metrics' in METRICS_GROUP_CONFIG:\n", "    slice_mapping = {\n", "        'bkt_100': '<100',\n", "        'bkt_100_1K': '100-1K',\n", "        'bkt_1K_5K': '1K-5K',\n", "        'bkt_5K_10K': '5K-10K',\n", "        'bkt_10K_100K': '10K-100K',\n", "        'bkt_100K_1M': '100K-1M',\n", "        'bkt_1M': '1M+'\n", "    }\n", "\n", "    CREATOR_FOLLOWER_BUCKET_SLICES = [{'slice': f'{i}','metric_filter': f\"(creator_follower_bucket = '{slice_mapping[i]}')\"} for i in creator_follower_bucket]\n", "\n", "    ALL_METRICS_GROUPS['creator_user_creator_follower_bucket_metrics'] = \\\n", "        dfm.discover_feed_metrics(start_date=study_start_date,\n", "                                end_date=study_end_date,\n", "                                sql_template=CREATOR_USER_AB_TEMPLATE_USER,\n", "                                slices=_filter_config(CREATOR_FOLLOWER_BUCKET_SLICES, creator_follower_bucket),\n", "                                metric_config_list=CREATOR_USER_METRICS,\n", "                                metric_group='creator_user_creator_follower_bucket_metrics',\n", "                                bq_dialect='standard'\n", "                                )"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# Creator User Metrics x Creator Days Since Creation\n", "if 'creator_user_creator_days_since_creation_metrics' in METRICS_GROUP_CONFIG:\n", "    slice_mapping = {\n", "        'bkt_0': '0',\n", "        'bkt_1_14': '1-14',\n", "        'bkt_15_30': '15-30',\n", "        'bkt_31_90': '31-90',\n", "        'bkt_91_365': '91-365',\n", "        'bkt_365': '365+',\n", "    }\n", "\n", "    CREATOR_DAYS_SINCE_CREATION_SLICES = [{'slice': f'{i}','metric_filter': f\"(creator_days_since_creation = '{slice_mapping[i]}')\"} for i in creator_days_since_creation]\n", "\n", "    ALL_METRICS_GROUPS['creator_user_creator_days_since_creation_metrics'] = \\\n", "        dfm.discover_feed_metrics(start_date=study_start_date,\n", "                                end_date=study_end_date,\n", "                                sql_template=CREATOR_USER_AB_TEMPLATE_USER,\n", "                                slices=_filter_config(CREATOR_DAYS_SINCE_CREATION_SLICES, creator_days_since_creation),\n", "                                metric_config_list=CREATOR_USER_METRICS,\n", "                                metric_group='creator_user_creator_days_since_creation_metrics',\n", "                                bq_dialect='standard'\n", "                                )"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# Creator User Metrics x MAS Type\n", "if 'creator_user_mas_type_metrics' in METRICS_GROUP_CONFIG:\n", "    MAS_TYPE_SLICES = [{'slice': f'{i}',\n", "                        'metric_filter': f'''\n", "                        (mas_type = '{i}')\n", "                        '''} for i in mas_type]\n", "\n", "    ALL_METRICS_GROUPS['creator_user_mas_type_metrics'] = \\\n", "        dfm.discover_feed_metrics(start_date=study_start_date,\n", "                                end_date=study_end_date,\n", "                                sql_template=CREATOR_USER_AB_TEMPLATE_USER,\n", "                                slices=_filter_config(MAS_TYPE_SLICES, mas_type),\n", "                                metric_config_list=CREATOR_USER_METRICS,\n", "                                metric_group='creator_user_mas_type_metrics',\n", "                                bq_dialect='standard'\n", "                                )"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["# Creator User Metrics x Creator Top1 Taxonomy\n", "if 'creator_user_creator_top1_taxonomy_metrics' in METRICS_GROUP_CONFIG:\n", "    slice_mapping = {\n", "        'Movies_TV_Shows': 'Movies & TV Shows',\n", "        'Games': 'Games',\n", "        'Funny': 'Funny',\n", "        'Color_Light': 'Color & Light',\n", "        'Backgrounds': 'Backgrounds',\n", "        'Self_Expression': 'Self-Expression',\n", "        'Characters': 'Characters',\n", "        'Appearance': 'Appearance',\n", "        'No_Applicable_Category': 'No Applicable Category',\n", "        'Camera_Styles': 'Camera Styles'\n", "    }\n", "\n", "    CREATOR_TOP1_TAXONOMY_SLICES = [{'slice': f'{i}','metric_filter': f\"(creator_top1_taxonomy = '{slice_mapping[i]}')\"} for i in creator_top1_taxonomy]\n", "\n", "    ALL_METRICS_GROUPS['creator_user_creator_top1_taxonomy_metrics'] = \\\n", "        dfm.discover_feed_metrics(start_date=study_start_date,\n", "                                end_date=study_end_date,\n", "                                sql_template=CREATOR_USER_AB_TEMPLATE_USER,\n", "                                slices=_filter_config(CREATOR_TOP1_TAXONOMY_SLICES, creator_top1_taxonomy),\n", "                                metric_config_list=CREATOR_USER_METRICS,\n", "                                metric_group='creator_user_creator_top1_taxonomy_metrics',\n", "                                bq_dialect='standard'\n", "                                )"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["# Creator User Metrics x Source Application\n", "if 'creator_user_source_application_metrics' in METRICS_GROUP_CONFIG:\n", "    SOURCE_APPLICATION_SLICES = [{'slice': f'{i}',\n", "                                    'metric_filter': f'''\n", "                                    (source_application = '{i}')\n", "                                    '''} for i in source_application]\n", "\n", "    ALL_METRICS_GROUPS['creator_user_source_application_metrics'] = \\\n", "        dfm.discover_feed_metrics(start_date=study_start_date,\n", "                                end_date=study_end_date,\n", "                                sql_template=CREATOR_USER_AB_TEMPLATE_USER,\n", "                                slices=_filter_config(SOURCE_APPLICATION_SLICES, source_application),\n", "                                metric_config_list=CREATOR_USER_METRICS,\n", "                                metric_group='creator_user_source_application_metrics',\n", "                                bq_dialect='standard'\n", "                                )"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["# Creator User Metrics x SLN Type\n", "if 'creator_user_sln_type_metrics' in METRICS_GROUP_CONFIG:\n", "    SLN_TYPE_SLICES = [{'slice': f'{i}',\n", "                                    'metric_filter': f'''\n", "                                    (sln_type = '{i}')\n", "                                    '''} for i in sln_type]\n", "\n", "    ALL_METRICS_GROUPS['creator_user_sln_type_metrics'] = \\\n", "        dfm.discover_feed_metrics(start_date=study_start_date,\n", "                                end_date=study_end_date,\n", "                                sql_template=CREATOR_USER_AB_TEMPLATE_USER,\n", "                                slices=_filter_config(SLN_TYPE_SLICES, sln_type),\n", "                                metric_config_list=CREATOR_USER_METRICS,\n", "                                metric_group='creator_user_sln_type_metrics',\n", "                                bq_dialect='standard'\n", "                                )"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["# Creator User Metrics x Is LCR Participated\n", "if 'creator_user_is_lcr_participated_metrics' in METRICS_GROUP_CONFIG:\n", "    IS_LCR_PARTICIPATED_SLICES = [\n", "        {'slice': 'No_',\n", "        'metric_filter': '''\n", "        (is_lcr_participated = 0)\n", "        '''},\n", "        {'slice': 'Yes_',\n", "        'metric_filter': '''\n", "        (is_lcr_participated = 1)\n", "        '''}\n", "    ]\n", "\n", "    ALL_METRICS_GROUPS['creator_user_is_lcr_participated_metrics'] = \\\n", "        dfm.discover_feed_metrics(start_date=study_start_date,\n", "                                end_date=study_end_date,\n", "                                sql_template=CREATOR_USER_AB_TEMPLATE_USER,\n", "                                slices=_filter_config(IS_LCR_PARTICIPATED_SLICES, is_lcr_participated),\n", "                                metric_config_list=CREATOR_USER_METRICS,\n", "                                metric_group='creator_user_is_lcr_participated_metrics',\n", "                                bq_dialect='standard'\n", "                                )"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["# Creator User Metrics x Is LCR Rewarded\n", "if 'creator_user_is_lcr_rewarded_metrics' in METRICS_GROUP_CONFIG:\n", "    IS_LCR_REWARDED_SLICES = [\n", "        {'slice': 'No',\n", "        'metric_filter': '''\n", "        (is_lcr_rewarded = 0)\n", "        '''},\n", "        {'slice': 'Yes',\n", "        'metric_filter': '''\n", "        (is_lcr_rewarded = 1)\n", "        '''}\n", "    ]\n", "\n", "    ALL_METRICS_GROUPS['creator_user_is_lcr_rewarded_metrics'] = \\\n", "        dfm.discover_feed_metrics(start_date=study_start_date,\n", "                                end_date=study_end_date,\n", "                                sql_template=CREATOR_USER_AB_TEMPLATE_USER,\n", "                                slices=_filter_config(IS_LCR_REWARDED_SLICES, is_lcr_rewarded),\n", "                                metric_config_list=CREATOR_USER_METRICS,\n", "                                metric_group='creator_user_is_lcr_rewarded_metrics',\n", "                                bq_dialect='standard'\n", "                                )"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["metric_tables = [\n", "    metric_table\n", "    for sublist in [ALL_METRICS_GROUPS[mt] for mt in ALL_METRICS_GROUPS if mt in METRICS_GROUP_CONFIG]\n", "    for metric_table in sublist\n", "]\n", "\n", "for mt in metric_tables:\n", "    mt.daily = INCLUDE_DAILY_RESULTS\n", "    mt.inner_join_with_mapping = True"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["##############################\n", "## Report Config and Execution\n", "##############################"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["###################\n", "## Configure Report\n", "###################"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["# Construct the report object\n", "report = CohortReport(\n", "        study_name=STUDY_NAME,\n", "        study_start_date=study_start_date,\n", "        study_end_date=study_end_date,\n", "        metric_tables=metric_tables,\n", "        control_id=CONTROL_ID,\n", "        treatment_ids=TREATMENT_IDS,\n", "        user_group_bys=USER_BREAKDOWN_LIST,\n", "        bq_project='sc-bq-gcs-billingonly',\n", "        bq_client=bigquery.Client(PROJECT),\n", "        dest_dataset='temp_abtest',\n", "        cohort_definition_date=COHORT_DATE,\n", "        materializing_mapping_table=True,\n", "        overwrite_mapping_table=OVERWRITE,\n", "        exp_id_to_name=None,\n", "        bq_priority=BQ_PRIORITY,\n", "        excel_output=True,\n", "        downsample_to=equal_down_sample,\n", "    )\n", "\n", "report.configurations[\"pivot_table\"] = True\n", "report.configurations['stat_fmtr'] = (\n", "    \"{pct_diff:,.2f}% ({avg_control:,.3}→{avg_treatment:,.3}, {p_value_formatted})\"\n", ")"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["#################\n", "## Execute Report\n", "#################"]}, {"cell_type": "code", "execution_count": null, "metadata": {"jupyter": {"is_executing": true}}, "outputs": [], "source": ["def get_header_str(metric_group, sep=\" \"):\n", "    return metric_group.replace(\"_\", sep).upper()\n", "\n", "def get_breakdown_str(show_all, show_breakdowns):\n", "    if show_all:\n", "        return \"OVERALL\"\n", "    if show_breakdowns:\n", "        return \"BREAKDOWNS\"\n", "\n", "# Run the joins and calculate the results\n", "if SHOW_METRIC_COMPARISONS == \"no_metrics_comparisons\":\n", "    report.ab_printer.print_text(\"Metric Comparisons Skipped\", 'strong')\n", "else:\n", "    abtest.report.notebook_print(\"Study Name: {}\".format(report.study_name), \"h3\")\n", "    abtest.report.notebook_print(\"Control ID: {}\".format(report.control_id), \"strong\")\n", "    abtest.report.notebook_print(\"Treatment ID(s): {}\".format(\", \".join(report.treatment_ids)), \"strong\")\n", "    abtest.report.notebook_print(\n", "        \"Breakdowns: <br> {}\".format(\n", "            \"<br>\".join(\", \".join(breakdown) for breakdown in report.user_group_by_list)\n", "        ),\n", "        \"strong\"\n", "    )\n", "    report.execute(\n", "        overwrite=OVERWRITE,\n", "        group_filters={\n", "            },\n", "        cumulative_trend=False,\n", "        skip_export=SKIP_EXPORT\n", "    )\n", "    abtest.report.notebook_print(\"Results\", \"h2\")\n", "    abtest.report.notebook_print(\"All Metrics\", \"h3\")\n", "\n", "    display(HTML(\"<h2> Metric Groups </h2>\"))\n", "    display(HTML(\"<h3> Overall </h3>\"))\n", "    for metric_group in METRICS_GROUP_CONFIG:\n", "        display(HTML(\"<a href=\\\"#{html_id}-{breakdown_str}\\\">{header}</a>\".format(\n", "            html_id=get_header_str(metric_group, \"-\"),\n", "            breakdown_str=\"OVERALL\",\n", "            header=get_header_str(metric_group))))\n", "    if len(USER_BREAKDOWN_LIST)>0:\n", "        display(HTML(\"<h3> Breakdowns </h3>\"))\n", "        for metric_group in METRICS_GROUP_CONFIG:\n", "            display(HTML(\"<a href=\\\"#{html_id}-{breakdown_str}\\\">{header}</a>\".format(\n", "                html_id=get_header_str(metric_group, \"-\"),\n", "                breakdown_str='BREAKDOWNS',\n", "                header=get_header_str(metric_group))))\n", "\n", "    display_flags = [\n", "        (True, False),\n", "        (<PERSON><PERSON><PERSON>, True)\n", "    ]\n", "\n", "    for show_all, show_breakdowns in display_flags:\n", "        for metric_group in METRICS_GROUP_CONFIG:\n", "            header=metric_group.replace(\"_\", \" \").upper()\n", "            metric_names = [str(mt.col) for mt_list in ALL_METRICS_GROUPS[metric_group] for mt in mt_list.metrics]\n", "            metric_names.sort()\n", "            should_display = metric_names and (show_all or USER_BREAKDOWN_LIST)\n", "            if should_display: ## to support customized metric list such as extra ab console metrics\n", "                display(HTML(\"<{tag} id='{html_id}-{breakdown_str}'>{header}</{tag}>\".format(\n", "                    header=get_header_str(metric_group),\n", "                    tag='h2',\n", "                    html_id=get_header_str(metric_group, \"-\"),\n", "                    breakdown_str=get_breakdown_str(show_all, show_breakdowns)\n", "                )))\n", "                if metric_group in [\n", "                        'creator_user_overall_metrics',\n", "                        'creator_user_lens_type_metrics',\n", "                        'creator_user_num_lenses_metrics',\n", "                        'creator_user_num_rankable_lenses_metrics',\n", "                        'creator_user_creator_l90_country_metrics',\n", "                        'creator_user_creator_follower_bucket_metrics',\n", "                        'creator_user_creator_days_since_creation_metrics',\n", "                        'creator_user_mas_type_metrics',\n", "                        'creator_user_creator_top1_taxonomy_metrics',\n", "                        'creator_user_source_application_metrics',\n", "                        'creator_user_sln_type_metrics',\n", "                        'creator_user_is_lcr_participated_metrics',\n", "                        'creator_user_is_lcr_rewarded_metrics'\n", "                    ]:\n", "                    dfab.visualize_df_metrics(report, metric_group, show_title=False,\n", "                                              show_all=show_all, show_breakdowns=show_breakdowns)\n", "                else:\n", "                    report.generate_report(\n", "                        format_pvalue=True,\n", "                        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "                        display_config={'cumulative': ['table'], 'daily': []},\n", "                        metric_filters={'metrics': metric_names},\n", "                        show_all=show_all,\n", "                        show_breakdowns=show_breakdowns\n", "                    );"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creator AA & CUPED\n", "\n", "You report contains results using [the CUPED variance reduction method](https://exp-platform.com/cuped/) (labeled CUPED in the tables). This method requires that there is no carryover effect from the previous study versions. If this study version reuses the same randomization seed as the previous versions, the CUPED results are invalid.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def sql_callable(start_date, end_date):\n", "            sql = sql_aa_cuped.format(\n", "                    start=start_date,\n", "                    end=end_date,\n", "                    )\n", "            return sql\n", "\n", "if SHOW_AA_CUPED_RESULTS == \"no_aa_cuped\":\n", "    report.ab_printer.print_text(\"AA / CUPED skipped\", 'strong')\n", "else:\n", "    ab_metric_list = [metric['sum_columns'] for metric in CREATOR_USER_METRICS]\n", "    query_metrics_list = \", \".join([f\"SUM({metric}) AS {metric}\" for metric in ab_metric_list])\n", "    sql_aa_cuped = f\"\"\"\n", "    SELECT ghost_user_id, ts,\n", "      {query_metrics_list}\n", "    FROM (\n", "        SELECT lens_user_ghost_user_id AS ghost_user_id,\n", "               TIMESTAMP(DATE(event_date)) AS ts,\n", "\n", "            --- Breakdowns ---\n", "            is_organic_creator,\n", "            num_lenses_cohort,\n", "            num_rankable_lenses_cohort,\n", "            creator_l90_country,\n", "            creator_follower_bucket,\n", "            creator_days_since_creation,\n", "            mas_type,\n", "            creator_top1_taxonomy,\n", "            source_application,\n", "            sln_type,\n", "            is_lcr_participated,\n", "            is_lcr_rewarded,\n", "\n", "            --- <PERSON> ---\n", "            --- --- Lens Core Metrics\n", "            sum(lens_weighted_actions) as lens_weighted_actions_count,\n", "            sum(lens_icon_impression_count) as lens_icon_impression_count,\n", "            sum(lens_swipe_count) as lens_swipe_count,\n", "            sum(lens_play_count) as lens_play_count,\n", "            sum(lens_play_6_to_30_sec_count) as lens_play_6_to_30_sec_count,\n", "            sum(lens_play_30_plus_sec_count) as lens_play_30_plus_sec_count,\n", "            sum(lens_snap_create_count) as lens_snap_create_count,\n", "            sum(lens_send_count) as lens_send_count,\n", "            sum(lens_save_count) as lens_save_count,\n", "            sum(lens_post_count) as lens_post_count,\n", "            sum(lens_share_count) as lens_share_count,\n", "            sum(lens_action_count) as lens_action_count,\n", "            sum(lens_spin_count) as lens_spin_count,\n", "\n", "            --- --- Lens Freshness Metrics\n", "            sum(swipes_lens_tenure_1_month) as swipes_lens_tenure_1_month_count,\n", "            sum(swipes_lens_tenure_1_to_3_month) as swipes_lens_tenure_1_to_3_month_count,\n", "            sum(swipes_lens_tenure_3_to_6_month) as swipes_lens_tenure_3_to_6_month_count,\n", "            sum(swipes_lens_tenure_1_to_6_month) as swipes_lens_tenure_1_to_6_month_count,\n", "            sum(swipes_lens_tenure_6_to_12_month) as swipes_lens_tenure_6_to_12_month_count,\n", "            sum(swipes_lens_tenure_12_month_plus) as swipes_lens_tenure_12_month_plus_count,\n", "            \n", "            --- --- Lens Info Card Metrics \n", "            sum(lens_infocard_open_count) as lens_infocard_open_count,\n", "            sum(lens_infocard_favorite_count) as lens_infocard_favorite_count,\n", "            sum(lens_infocard_unfavorite_count) as lens_infocard_unfavorite_count,\n", "            sum(lens_button_favorite_count) as lens_button_favorite_count,\n", "            sum(lens_button_unfavorite_count) as lens_button_unfavorite_count,\n", "            sum(lens_removal_count) as lens_removal_count,\n", "            sum(lens_report_count) as lens_report_count,\n", "            sum(lens_infocard_view_profile_count) as lens_infocard_view_profile_count,\n", "            sum(lens_infocard_more_lenses_count) as lens_infocard_more_lenses_count,\n", "            sum(lens_infocard_copy_link_count) as lens_infocard_copy_link_count,\n", "            sum(lens_infocard_export_count) as lens_infocard_export_count,\n", "            sum(lens_infocard_share_inchat_count) as lens_infocard_share_inchat_count,\n", "            sum(lens_infocard_subscribe_count) as lens_infocard_subscribe_count,\n", "            sum(lens_infocard_viewtopics_count) as lens_infocard_viewtopics_count     \n", "        FROM\n", "        `sc-analytics.report_lens.lens_user_creator_cohort_lvl_agg_*`\n", "        WHERE _TABLE_SUFFIX BETWEEN '{{start}}' AND '{{end}}'\n", "        GROUP BY ALL\n", "    )\n", "    GROUP BY 1, 2\n", "\"\"\"\n", "    ab_metric_list = [metric['sum_columns'] for metric in CREATOR_USER_METRICS]\n", "    mt_aa_cuped = MetricTable(\n", "        sql=None,\n", "        metrics=[abtest.metric.Metric(\n", "                col=metric,\n", "                dist=\"cont\",\n", "                cumulative=True,\n", "                daily=False\n", "            ) for metric in ab_metric_list],\n", "        name=CREATOR_USER_METRICS[0]['sum_columns'] + \"_metric_table\", # use the first metric name as the metric table name\n", "        bq_dialect=\"standard\",\n", "        quantile_metrics=False,\n", "        cuped=True,\n", "        aa=True,\n", "        sql_callable=sql_callable,\n", "        )\n", "    report_aa_cuped = CohortReport(\n", "        study_name=STUDY_NAME,\n", "        study_start_date=STUDY_START_DATE,\n", "        study_end_date=STUDY_END_DATE,\n", "        aa_start_date=RETRO_START_DATE,\n", "        aa_end_date=RETRO_END_DATE,\n", "        metric_tables=[mt_aa_cuped],\n", "        control_id=CONTROL_ID,\n", "        treatment_ids=TREATMENT_IDS,\n", "        user_group_bys=USER_BREAKDOWN_LIST,\n", "        bq_project='sc-bq-gcs-billingonly',\n", "        bq_client=bigquery.Client(PROJECT),\n", "        dest_dataset='temp_abtest',\n", "        cohort_definition_date=COHORT_DATE,\n", "        materializing_mapping_table=True,\n", "        overwrite_mapping_table=OVERWRITE,\n", "        exp_id_to_name=None,\n", "        bq_priority=BQ_PRIORITY,\n", "        excel_output=True,\n", "        downsample_to=equal_down_sample,\n", "    )\n", "    report_aa_cuped.execute(\n", "        overwrite=OVERWRITE,\n", "        group_filters={\n", "            },\n", "        cumulative_trend=False,\n", "        skip_export=SKIP_EXPORT\n", "    )\n", "    report_aa_cuped.configurations['pivot_table'] = True\n", "    report_aa_cuped.configurations['stat_fmtr'] = (\n", "    #     \"{pct_diff:,.2f}% (→{avg_treatment:,.3f}, {p_value_formatted})\"\n", "        \"{pct_diff:,.2f}% ({avg_control:,.3}→{avg_treatment:,.3}, {p_value_formatted})\"\n", "    )\n", "    # formatting of the report\n", "    fmtr = \"{pct_diff:,.2f}% ({avg_control:,.3}→{avg_treatment:,.3}, {p_value_formatted})\"\n", "    # Print the report\n", "    abtest.report.notebook_print(\"Study Name: {}\".format(report_aa_cuped.study_name), \"h3\")\n", "    abtest.report.notebook_print(\"Control ID: {}\".format(report_aa_cuped.control_id), \"strong\")\n", "    abtest.report.notebook_print(\"Treatment ID(s): {}\".format(\", \".join(report_aa_cuped.treatment_ids)), \"strong\")\n", "    abtest.report.notebook_print(\n", "        \"Study running period: {} - {}\".format(report_aa_cuped.study_start_date, report_aa_cuped.study_end_date),\n", "        \"strong\"\n", "    )\n", "    abtest.report.notebook_print(\n", "        \"Report analysis period: {} - {}\".format(STUDY_START_DATE, STUDY_END_DATE),\n", "        \"strong\"\n", "    )\n", "    abtest.report.notebook_print(\n", "        \"Breakdowns: <br> {}\".format(\n", "            \"<br>\".join(\", \".join(breakdown) for breakdown in report_aa_cuped.user_group_by_list)\n", "        ),\n", "        \"strong\"\n", "    )\n", "    abtest.report.notebook_print(\"Custom Metrics\", \"h2\")\n", "    display_cfg = {\n", "        \"daily\": [],\n", "        \"cumulative\": [\"table\"],\n", "    }\n", "    report_aa_cuped.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['avg_control', 'avg_treatment', 'count_control', 'count_treatment'],\n", "        metric_filters = {\n", "            'metrics': [metric['sum_columns'] for metric in CREATOR_USER_METRICS]\n", "        },\n", "        display_config = display_cfg,\n", "        stat_fmtr=fmtr,\n", "    )\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["fds\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Creator Winners and Losers\n", "All winners and losers below are top and bottom creators ordered by average per user snap views. Impressions are long impressions. Only creators with p-values of < 0.1 are included. CTR is story views divided by long impressions.\n", "\n", "You can choose to generate the results for either the last day of the study (faster and enough to get some ideas about how the study changes stories ranking) or for the entire period of the study (slower)."]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["def creator_sql(start_date, end_date):\n", "    return \"\"\"\n", "  SELECT lens_creator_ghost_user_id,\n", "        lens_user_ghost_user_id AS ghost_user_id,\n", "        TIMESTAMP(DATE(event_date)) AS ts,\n", "              --- <PERSON> ---\n", "              --- --- Lens Core Metrics\n", "              sum(lens_weighted_actions) as lens_weighted_actions_count,\n", "              sum(lens_icon_impression_count) as lens_icon_impression_count,\n", "              sum(lens_swipe_count) as lens_swipe_count,\n", "              sum(lens_play_count) as lens_play_count,\n", "              sum(lens_play_6_to_30_sec_count) as lens_play_6_to_30_sec_count,\n", "              sum(lens_play_30_plus_sec_count) as lens_play_30_plus_sec_count,\n", "              sum(lens_snap_create_count) as lens_snap_create_count,\n", "              sum(lens_send_count) as lens_send_count,\n", "              sum(lens_save_count) as lens_save_count,\n", "              sum(lens_post_count) as lens_post_count,\n", "              sum(lens_share_count) as lens_share_count,\n", "              sum(lens_action_count) as lens_action_count,\n", "              sum(lens_spin_count) as lens_spin_count,\n", "\n", "              --- --- Lens Freshness Metrics\n", "              sum(swipes_lens_tenure_1_month) as swipes_lens_tenure_1_month_count,\n", "              sum(swipes_lens_tenure_1_to_3_month) as swipes_lens_tenure_1_to_3_month_count,\n", "              sum(swipes_lens_tenure_3_to_6_month) as swipes_lens_tenure_3_to_6_month_count,\n", "              sum(swipes_lens_tenure_1_to_6_month) as swipes_lens_tenure_1_to_6_month_count,\n", "              sum(swipes_lens_tenure_6_to_12_month) as swipes_lens_tenure_6_to_12_month_count,\n", "              sum(swipes_lens_tenure_12_month_plus) as swipes_lens_tenure_12_month_plus_count,\n", "              \n", "              --- --- Lens Info Card Metrics \n", "              sum(lens_infocard_open_count) as lens_infocard_open_count,\n", "              sum(lens_infocard_favorite_count) as lens_infocard_favorite_count,\n", "              sum(lens_infocard_unfavorite_count) as lens_infocard_unfavorite_count,\n", "              sum(lens_button_favorite_count) as lens_button_favorite_count,\n", "              sum(lens_button_unfavorite_count) as lens_button_unfavorite_count,\n", "              sum(lens_removal_count) as lens_removal_count,\n", "              sum(lens_report_count) as lens_report_count,\n", "              sum(lens_infocard_view_profile_count) as lens_infocard_view_profile_count,\n", "              sum(lens_infocard_more_lenses_count) as lens_infocard_more_lenses_count,\n", "              sum(lens_infocard_copy_link_count) as lens_infocard_copy_link_count,\n", "              sum(lens_infocard_export_count) as lens_infocard_export_count,\n", "              sum(lens_infocard_share_inchat_count) as lens_infocard_share_inchat_count,\n", "              sum(lens_infocard_subscribe_count) as lens_infocard_subscribe_count,\n", "              sum(lens_infocard_viewtopics_count) as lens_infocard_viewtopics_count            \n", "  FROM `sc-analytics.report_lens.lens_creator_user_lvl_interaction_full_*`\n", "  WHERE _TABLE_SUFFIX BETWEEN '{start}' AND '{end}'\n", "    AND lens_creator_ghost_user_id IS NOT NULL\n", "    {test_or_prod_query}\n", "  GROUP BY ALL\n", "\"\"\".format(\n", "start=start_date,\n", "end=end_date,\n", "test_or_prod_query=test_or_prod_query,)"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [], "source": ["breakdown_top_n = {\n", "    \"breakdown_column\":\"lens_creator_ghost_user_id\",\n", "    \"top_n\":top_n,\n", "    \"metric\":WL_ORDER_BY[0],\n", "    \"order_by_exp_id\":CONTROL_ID\n", "    }\n", "creator_metric_table = FieldBreakdownMetricTable(\n", "    sql=None,\n", "    sql_callable=creator_sql,\n", "    metrics=[Metric(metric, dist=\"cont\") for metric in WL_DISPLAY_METRICS],\n", "    name=\"creator_winners_losers\",\n", "    breakdowns=[['lens_creator_ghost_user_id']],\n", "    bq_dialect=\"standard\",\n", "    daily=False,\n", "    inner_join_with_mapping=True,\n", "    breakdown_top_n=breakdown_top_n\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "if SHOW_WINNERS_LOSERS == 'no_winners_losers':\n", "    report.ab_printer.print_text(\"Story winners and losers skipped\", 'strong')\n", "else:\n", "    if SHOW_WINNERS_LOSERS == 'l7_winners_losers':\n", "        wl_start_date = max([\n", "            report.study_start_date,\n", "            (pd.to_datetime(report.study_end_date) - timed<PERSON><PERSON>(days=6)).strftime(\"%Y%m%d\")\n", "        ])\n", "    else:  # Use last day\n", "        wl_start_date = report.study_end_date\n", "    wl_end_date = report.study_end_date\n", "\n", "    report_wl = CohortReport(\n", "        study_name=STUDY_NAME,\n", "        study_start_date=wl_start_date,\n", "        study_end_date=wl_end_date,\n", "        metric_tables=[creator_metric_table],\n", "        control_id=CONTROL_ID,\n", "        treatment_ids=TREATMENT_IDS,\n", "        bq_project='sc-bq-gcs-billingonly',\n", "        bq_client=bigquery.Client(PROJECT),\n", "        dest_dataset='temp_abtest',\n", "        cohort_definition_date=COHORT_DATE,\n", "        materializing_mapping_table=True,\n", "        overwrite_mapping_table=OVERWRITE,\n", "        bq_priority=BQ_PRIORITY,\n", "        downsample_to=equal_down_sample,\n", "        )\n", "\n", "    report_wl.configurations['stat_fmtr'] = report.configurations['stat_fmtr']\n", "\n", "    report_wl.execute(\n", "        overwrite=True,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if SHOW_WINNERS_LOSERS != 'no_winners_losers':\n", "    report.ab_printer.print_text(\n", "        \"Winners and Losers from {} to {}\".format(\n", "            wl_start_date,\n", "            wl_end_date,\n", "        ),\n", "        'h2'\n", "    )\n", "\n", "    enrichment_sql = f\"\"\"\n", "            SELECT lens_creator_ghost_user_id,\n", "                    MAX(is_organic_creator) AS is_organic_creator,\n", "                    MAX(creator_l90_country) AS creator_l90_country,\n", "                    MAX(creator_days_since_creation) AS creator_days_since_creation,\n", "                    MAX(mas_type) AS mas_type,\n", "                    MAX(creator_top1_taxonomy) AS creator_top1_taxonomy,\n", "                    MAX(source_application) AS source_application,\n", "                    MAX(sln_type) AS sln_type,\n", "                    MAX(is_lcr_participated) AS is_lcr_participated,\n", "                    MAX(is_lcr_rewarded) AS is_lcr_rewarded\n", "            FROM `sc-analytics.report_lens.lens_creator_user_lvl_interaction_full_*`\n", "            WHERE _TABLE_SUFFIX BETWEEN \"{wl_start_date}\" AND \"{wl_start_date}\"\n", "              AND lens_creator_ghost_user_id IS NOT NULL\n", "            GROUP BY lens_creator_ghost_user_id\n", "    \"\"\"\n", "    winners_losers_results = report_wl.get_results(\n", "        \"cumulative\",\n", "        exclude_field_breakdown=False,\n", "    )\n", "    if WL_BREAKDOWN_LIST:\n", "        for breakdowns in WL_BREAKDOWN_LIST:\n", "            if breakdowns == \"overall\":\n", "                report_wl.generate_winners_losers_report(\n", "                    winners_losers_dimensions=['lens_creator_ghost_user_id'],\n", "                    order_by_metrics=WL_ORDER_BY,\n", "                    display_metrics=WL_DISPLAY_METRICS,\n", "                    enrichment_group_bys=None,\n", "                    p_value_threshold=0.1,\n", "                    enrichment_sql=enrichment_sql,\n", "                    winners_losers_results=winners_losers_results,\n", "                    n_winners=25,\n", "                    n_losers=25\n", "                )\n", "            else:\n", "                report_wl.generate_winners_losers_report(\n", "                    winners_losers_dimensions=['lens_creator_ghost_user_id'],\n", "                    order_by_metrics=WL_ORDER_BY,\n", "                    display_metrics=WL_DISPLAY_METRICS,\n", "                    enrichment_group_bys=[breakdowns],\n", "                    p_value_threshold=0.1,\n", "                    enrichment_sql=enrichment_sql,\n", "                    winners_losers_results=winners_losers_results,\n", "                    n_winners=25,\n", "                    n_losers=25\n", "                )\n", "    else:\n", "        report_wl.generate_winners_losers_report(\n", "            winners_losers_dimensions=['lens_creator_ghost_user_id'],\n", "            order_by_metrics=WL_ORDER_BY,\n", "            display_metrics=WL_DISPLAY_METRICS,\n", "            enrichment_group_bys=None,\n", "            p_value_threshold=0.1,\n", "            enrichment_sql=enrichment_sql,\n", "            winners_losers_results=winners_losers_results,\n", "                    n_winners=25,\n", "                    n_losers=25\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"anaconda-cloud": {}, "celltoolbar": "Tags", "finalized": {"timestamp": 1568312574522, "trusted": true}, "hide_input": false, "kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}, "require": {"paths": {"buttons.colvis": "https://cdn.datatables.net/buttons/1.5.6/js/buttons.colVis.min", "buttons.flash": "https://cdn.datatables.net/buttons/1.5.6/js/buttons.flash.min", "buttons.html5": "https://cdn.datatables.net/buttons/1.5.6/js/buttons.html5.min", "buttons.print": "https://cdn.datatables.net/buttons/1.5.6/js/buttons.print.min", "d3": "https://cdnjs.cloudflare.com/ajax/libs/d3/5.9.2/d3.min", "datatables.net": "https://cdn.datatables.net/1.10.18/js/jquery.dataTables", "datatables.net-buttons": "https://cdn.datatables.net/buttons/1.5.6/js/dataTables.buttons.min", "datatables.responsive": "https://cdn.datatables.net/responsive/2.2.2/js/dataTables.responsive.min", "datatables.scroller": "https://cdn.datatables.net/scroller/2.0.0/js/dataTables.scroller.min", "datatables.select": "https://cdn.datatables.net/select/1.3.0/js/dataTables.select.min", "jszip": "https://cdnjs.cloudflare.com/ajax/libs/jszip/2.5.0/jszip.min", "pdfmake": "https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/pdfmake.min", "vfsfonts": "https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/vfs_fonts"}, "shim": {"buttons.colvis": {"deps": ["j<PERSON><PERSON>", "datatables.net-buttons"]}, "buttons.flash": {"deps": ["j<PERSON><PERSON>", "datatables.net-buttons"]}, "buttons.html5": {"deps": ["j<PERSON><PERSON>", "datatables.net-buttons"]}, "buttons.print": {"deps": ["j<PERSON><PERSON>", "datatables.net-buttons"]}, "datatables.net": {"exports": "$.fn.dataTable"}, "datatables.net-buttons": {"deps": ["datatables.net"]}, "pdfmake": {"deps": ["datatables.net"]}, "vfsfonts": {"deps": ["datatables.net"]}}}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {"height": "calc(100% - 180px)", "left": "10px", "top": "150px", "width": "165px"}, "toc_section_display": true, "toc_window_display": true}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 4}