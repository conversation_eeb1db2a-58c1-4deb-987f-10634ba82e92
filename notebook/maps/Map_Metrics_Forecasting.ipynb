{"cells": [{"cell_type": "code", "execution_count": 1, "id": "4c4c9056-cdf4-44b1-be26-2fc87712dd9f", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/opt/anaconda3/envs/py38/lib/python3.8/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "/Users/<USER>/opt/anaconda3/envs/py38/lib/python3.8/site-packages/google/cloud/bigquery/__init__.py:237: FutureWarning: %load_ext google.cloud.bigquery is deprecated. Install bigquery-magics package and use `%load_ext bigquery_magics`, instead.\n", "  warnings.warn(\n"]}], "source": ["from datetime import datetime\n", "\n", "import numpy as np\n", "import pandas as pd\n", "from prophet import Prophet\n", "from banjo.utils import gbq\n", "\n", "import pandas_gbq\n", "from google.cloud import bigquery\n", "%load_ext google.cloud.bigquery"]}, {"cell_type": "code", "execution_count": 2, "id": "464b3384-a3e7-43c7-b1d8-c3f9d33c9ce3", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["parameters"]}, "outputs": [], "source": ["PROJECT_ID = 'sc-bq-maps-adhoc'\n", "\n", "DATE = '2025-07-14'\n", "DATE_NODASH = '20250714'\n", "PREV_DATE_NODASH = '20250110'"]}, {"cell_type": "code", "execution_count": 3, "id": "72f52681-ae79-4591-a0ab-5e0958deac45", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["def run_query(query, dialect='standard'):\n", "    df = pandas_gbq.read_gbq(\n", "        query,\n", "        project_id=PROJECT_ID,\n", "        dialect=dialect,\n", "    )\n", "    return df"]}, {"cell_type": "code", "execution_count": 4, "id": "012001c0-1a27-495d-8a1e-aa1c1923f847", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["def forecast(df, region, params, **args):\n", "    \"\"\"\n", "    This function\n", "    \"\"\"\n", "    if params.get(\"metric_change_ds\"):\n", "        metric_change = pd.DataFrame(\n", "            [\n", "                {\n", "                    \"holiday\": \"metric_change\",\n", "                    \"ds\": params[\"metric_change_ds\"],\n", "                    \"lower_window\": params[\"metric_change_lower_window\"],\n", "                    \"ds_upper\": params[\"metric_change_ds_upper\"],\n", "                }\n", "            ]\n", "        )\n", "        for t_col in [\"ds\", \"ds_upper\"]:\n", "            metric_change[t_col] = pd.to_datetime(metric_change[t_col])\n", "        metric_change[\"upper_window\"] = (metric_change[\"ds_upper\"] - metric_change[\"ds\"]).dt.days\n", "        m = Prophet(seasonality_mode=\"multiplicative\", holidays=metric_change)\n", "    else:\n", "        m = Prophet(seasonality_mode=\"multiplicative\")\n", "    if region == \"US\":\n", "        m.add_country_holidays(country_name=\"US\")\n", "    m.fit(df)\n", "    future = m.make_future_dataframe(periods=485)\n", "    forecast_result = m.predict(future)\n", "    forecast_result = forecast_result[forecast_result[\"ds\"] >= \"2023-01-01\"]\n", "    df[\"is_forecast\"] = False\n", "    df['ds'] = pd.to_datetime(df['ds'])\n", "    forecast_result = forecast_result.merge(df, how=\"left\", on=\"ds\")\n", "    return forecast_result"]}, {"cell_type": "code", "execution_count": 5, "id": "95d3cb63-f37c-4cb3-b003-9430dc7f3975", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["def process_forecast_result(df, region, metric_display_name):\n", "    df[\"ds\"] = pd.to_datetime(df[\"ds\"])\n", "    today = pd.to_datetime(DATE)\n", "    df[\"domain_area\"] = \"Map\"\n", "    df[\"region\"] = region\n", "    df[\"metric_name\"] = metric_display_name\n", "    df[\"is_forecast\"].fillna(True, inplace=True)\n", "    df.rename(\n", "        index=str,\n", "        columns={\n", "            \"ds\": \"date\",\n", "            \"y\": \"actual_value\",\n", "            \"yhat\": \"forecast_mid\",\n", "            \"yhat_lower\": \"forecast_lower\",\n", "            \"yhat_upper\": \"forecast_upper\",\n", "        },\n", "        inplace=True,\n", "    )\n", "    df[\"forecast_evaluation_date\"] = pd.to_datetime(DATE)\n", "    df[\"date_quarter\"] = df[\"date\"].dt.to_period(\"Q\").astype(str)\n", "    return df"]}, {"cell_type": "code", "execution_count": 6, "id": "baba4a12-99b3-4964-8529-e32b625b694c", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["def set_goals(df):\n", "    df[\"goal\"] = np.nan\n", "    df.sort_values(by=\"date\", inplace=True)\n", "    df[\"forecast_mid_original_eoq\"] = df.groupby(\"date_quarter\")[\"forecast_mid_original\"].transform(\"last\")\n", "    df.loc[df[\"date_quarter\"] == \"2025Q1\", \"goal\"] = df[\"forecast_mid_original_eoq\"] * 1.001\n", "    df.loc[df[\"date_quarter\"] == \"2025Q2\", \"goal\"] = df[\"forecast_mid_original_eoq\"] * 1.004\n", "    df.loc[df[\"date_quarter\"] == \"2025Q3\", \"goal\"] = df[\"forecast_mid_original_eoq\"] * 1.007\n", "    df.loc[df[\"date_quarter\"] == \"2025Q4\", \"goal\"] = df[\"forecast_mid_original_eoq\"] * 1.01\n", "    # updated Q3 & Q4 goals for Map Location Sharing DAU metric\n", "    # More details: https://docs.google.com/document/d/12AW1jj3FQzkyxE06YCLmjh5vIGxmbCHeUEl_PgDu8qg/edit?tab=t.0\n", "    df.loc[\n", "        (df[\"date_quarter\"] == \"2025Q3\") &\n", "        (df[\"metric_name\"] == \"Map Location Sharing DAU\") &\n", "        (df[\"region\"] == \"Global\"),\n", "    \"goal\"\n", "    ] = 114500000\n", "    df.loc[\n", "        (df[\"date_quarter\"] == \"2025Q3\") &\n", "        (df[\"metric_name\"] == \"Map Location Sharing DAU\") &\n", "        (df[\"region\"] == \"US\"),\n", "    \"goal\"\n", "    ] = 27400000\n", "    df.loc[\n", "        (df[\"date_quarter\"] == \"2025Q4\") &\n", "        (df[\"metric_name\"] == \"Map Location Sharing DAU\") &\n", "        (df[\"region\"] == \"Global\"),\n", "    \"goal\"\n", "    ] = 116400000\n", "    df.loc[\n", "        (df[\"date_quarter\"] == \"2025Q4\") &\n", "        (df[\"metric_name\"] == \"Map Location Sharing DAU\") &\n", "        (df[\"region\"] == \"US\"),\n", "    \"goal\"\n", "    ] = 26900000\n", "    return df"]}, {"cell_type": "code", "execution_count": 7, "id": "3660529f-c60e-40ac-883d-b09a50097468", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["PREV_FORECAST_RESULT_QUERY = '''\n", "SELECT\n", "    DATE(date) AS date,\n", "    region,\n", "    metric_name,\n", "    COALESCE(forecast_mid_original, forecast_mid) AS forecast_mid_original,\n", "    COALESCE(forecast_lower_original, forecast_lower) AS forecast_lower_original,\n", "    COALESCE(forecast_upper_original, forecast_upper) AS forecast_upper_original\n", "  FROM\n", "    `sc-analytics.report_maps.maps_forecasting_{prev_date_nodash}`\n", "'''.format(prev_date_nodash=PREV_DATE_NODASH)"]}, {"cell_type": "code", "execution_count": 8, "id": "d3354840-efd4-45d6-b357-f4d14c668aa6", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["MAPS_KEY_METRICS_AGGREGATED_BY_DAY_QUERY = '''\n", "SELECT\n", "    DATE(event_date) AS ds,\n", "    country AS region,\n", "    FORMAT_DATE('Q%Q-%Y', event_date) AS quarter_year,\n", "    SUM(MAP_MAP_OPEN_UU) AS map_dau\n", "  FROM\n", "    `sc-analytics.report_maps.maps_key_metrics_aggregated_*`\n", "  WHERE\n", "    _TABLE_SUFFIX BETWEEN '20230101' AND '{date_nodash}'\n", "  GROUP BY 1, 2, 3\n", "'''.format(date_nodash=DATE_NODASH)"]}, {"cell_type": "code", "execution_count": 9, "id": "b2fe184e-a41a-40f4-bd8b-17ca9bdea9f9", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["MAPS_LOCATION_DEVICE_SHARING_SETTING_BY_DATE_QUERY = '''\n", "SELECT\n", "    DATE(event_date) AS ds,\n", "    country AS region,\n", "    FORMAT_DATE('Q%Q-%Y', event_date) AS quarter_year,\n", "    SUM(CASE WHEN \n", "      location_sharing_setting IN ('ALL_FRIENDS','CUSTOM_FRIENDS','BLACKLIST_MODE') AND (device_location_permission_granted=TRUE OR device_location_permission_granted IS NULL)\n", "    THEN 1 ELSE 0 END) AS map_location_sharing_dau\n", "  FROM\n", "    `sc-analytics.report_maps.dau_maps_location_device_sharing_setting_*`\n", "  WHERE\n", "    _TABLE_SUFFIX BETWEEN '20230101' AND '{date_nodash}'\n", "  GROUP BY 1, 2, 3\n", "'''.format(date_nodash=DATE_NODASH)"]}, {"cell_type": "code", "execution_count": 10, "id": "1d89595e-cb5b-43d5-8d65-415d2c364a9e", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["forecasting_checklist = {\n", "    \"maps_key_metrics_aggregated_by_day\": {\n", "        \"query\": MAPS_KEY_METRICS_AGGREGATED_BY_DAY_QUERY,\n", "        \"metric_list\": [\n", "            {\n", "                \"metric_name\": \"map_dau\",\n", "                \"metric_display_name\": \"Map DAU\",\n", "                \"region_list\": [\"Global\", \"US\"],\n", "                \"parameters\": {}\n", "            }\n", "        ]\n", "    },\n", "    \"maps_location_device_sharing_setting_by_date\": {\n", "        \"query\": MAPS_LOCATION_DEVICE_SHARING_SETTING_BY_DATE_QUERY,\n", "        \"metric_list\": [\n", "            {\n", "                \"metric_name\": \"map_location_sharing_dau\",\n", "                \"metric_display_name\": \"Map Location Sharing DAU\",\n", "                \"region_list\": [\"Global\", \"US\"],\n", "                \"parameters\": {\n", "                    \"metric_change_ds\": \"2024-05-01\",\n", "                    \"metric_change_lower_window\": 0,\n", "                    \"metric_change_ds_upper\": \"2024-09-15\"\n", "                }\n", "            }\n", "        ]\n", "    },\n", "}"]}, {"cell_type": "code", "execution_count": 11, "id": "5273cb7a-8219-4b9b-b1c8-fbd74cbd0c3e", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading: 100%|\u001b[32m███████████████████████████████████████████████████████████████\u001b[0m|\u001b[0m\n"]}], "source": ["# get previous day's forecast result\n", "\n", "prev_forecast_result = run_query(PREV_FORECAST_RESULT_QUERY)"]}, {"cell_type": "code", "execution_count": 12, "id": "e5ffa37d-0245-42f4-82cd-de477a96a3c7", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downloading: 100%|\u001b[32m███████████████████████████████████████████████████████████████\u001b[0m|\u001b[0m"]}, {"name": "stderr", "output_type": "stream", "text": ["15:28:56 - cmdstanpy - INFO - Chain [1] start processing\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n"]}, {"name": "stderr", "output_type": "stream", "text": ["15:28:57 - cmdstanpy - INFO - Chain [1] done processing\n", "15:28:57 - cmdstanpy - INFO - Chain [1] start processing\n", "15:28:57 - cmdstanpy - INFO - Chain [1] done processing\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloading: 100%|\u001b[32m███████████████████████████████████████████████████████████████\u001b[0m|\u001b[0m\n"]}, {"name": "stderr", "output_type": "stream", "text": ["15:37:54 - cmdstanpy - INFO - Chain [1] start processing\n", "15:37:54 - cmdstanpy - INFO - Chain [1] done processing\n", "15:37:55 - cmdstanpy - INFO - Chain [1] start processing\n", "15:37:56 - cmdstanpy - INFO - Chain [1] done processing\n"]}], "source": ["maps_forecast_result = []\n", "\n", "for checklist in forecasting_checklist.keys():\n", "    df = run_query(forecasting_checklist[checklist]['query'])\n", "    for metric in forecasting_checklist[checklist]['metric_list']:\n", "        metric_name = metric['metric_name']\n", "        metric_display_name = metric['metric_display_name']\n", "        params = metric['parameters']\n", "        for region in metric['region_list']:\n", "            if region == 'Global':\n", "                data = df.groupby('ds', as_index=False).agg({metric_name: 'sum'})\n", "            else:\n", "                data = df[df['region']==region].copy()\n", "            data.rename(index=str, columns={metric_name: 'y'}, inplace=True)\n", "            data = data[data['ds'] <= pd.to_datetime(DATE)]\n", "\n", "            forecast_result = forecast(data, region, params)\n", "            forecast_result = process_forecast_result(forecast_result, region, metric_display_name)\n", "            prev_day_result = prev_forecast_result[prev_forecast_result['metric_name']==metric_display_name].copy()\n", "            prev_day_result.drop('metric_name', axis=1, inplace=True)\n", "            prev_day_result['date'] = pd.to_datetime(prev_day_result['date'])\n", "            forecast_result = forecast_result.merge(prev_day_result, how='left', on=['date', 'region'])\n", "            forecast_result = set_goals(forecast_result)\n", "            \n", "            forecast_result = forecast_result[[\n", "                'date',\n", "                'domain_area',\n", "                'metric_name',\n", "                'region',\n", "                'is_forecast',\n", "                'actual_value',\n", "                'forecast_evaluation_date',\n", "                'forecast_mid',\n", "                'forecast_lower',\n", "                'forecast_upper',\n", "                'goal',\n", "                'forecast_mid_original',\n", "                'forecast_lower_original',\n", "                'forecast_upper_original'\n", "            ]]\n", "            maps_forecast_result.append(forecast_result)\n", "\n", "maps_forecast_result = pd.concat(maps_forecast_result)"]}, {"cell_type": "code", "execution_count": 13, "id": "b15e921e-2dcc-45b1-99c4-a99427b91d29", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["forecast_result = gbq.to_gbq(\n", "    df=maps_forecast_result, \n", "    project_id=PROJECT_ID,\n", "    dest_dataset_id=\"report_maps\", \n", "    dest_project_id=\"sc-analytics\", \n", "    dest_table_name=f\"maps_forecasting_{DATE_NODASH}\"\n", ")"]}, {"cell_type": "code", "execution_count": 14, "id": "25ce4bc7-0b01-4a10-964c-8837976397f4", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>date</th>\n", "      <th>domain_area</th>\n", "      <th>metric_name</th>\n", "      <th>region</th>\n", "      <th>is_forecast</th>\n", "      <th>actual_value</th>\n", "      <th>forecast_evaluation_date</th>\n", "      <th>forecast_mid</th>\n", "      <th>forecast_lower</th>\n", "      <th>forecast_upper</th>\n", "      <th>goal</th>\n", "      <th>forecast_mid_original</th>\n", "      <th>forecast_lower_original</th>\n", "      <th>forecast_upper_original</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2023-01-01</td>\n", "      <td>Map</td>\n", "      <td>Map DAU</td>\n", "      <td>Global</td>\n", "      <td>False</td>\n", "      <td>75051387</td>\n", "      <td>2025-07-14</td>\n", "      <td>7.442172e+07</td>\n", "      <td>7.298891e+07</td>\n", "      <td>7.600328e+07</td>\n", "      <td>NaN</td>\n", "      <td>7.404826e+07</td>\n", "      <td>7.247999e+07</td>\n", "      <td>7.558532e+07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2023-01-02</td>\n", "      <td>Map</td>\n", "      <td>Map DAU</td>\n", "      <td>Global</td>\n", "      <td>False</td>\n", "      <td>72050814</td>\n", "      <td>2025-07-14</td>\n", "      <td>7.169923e+07</td>\n", "      <td>7.026055e+07</td>\n", "      <td>7.324400e+07</td>\n", "      <td>NaN</td>\n", "      <td>7.163878e+07</td>\n", "      <td>7.014526e+07</td>\n", "      <td>7.324419e+07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2023-01-03</td>\n", "      <td>Map</td>\n", "      <td>Map DAU</td>\n", "      <td>Global</td>\n", "      <td>False</td>\n", "      <td>70912931</td>\n", "      <td>2025-07-14</td>\n", "      <td>7.131105e+07</td>\n", "      <td>6.964320e+07</td>\n", "      <td>7.282521e+07</td>\n", "      <td>NaN</td>\n", "      <td>7.131159e+07</td>\n", "      <td>6.972042e+07</td>\n", "      <td>7.288802e+07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023-01-04</td>\n", "      <td>Map</td>\n", "      <td>Map DAU</td>\n", "      <td>Global</td>\n", "      <td>False</td>\n", "      <td>70531059</td>\n", "      <td>2025-07-14</td>\n", "      <td>7.137873e+07</td>\n", "      <td>6.988018e+07</td>\n", "      <td>7.285414e+07</td>\n", "      <td>NaN</td>\n", "      <td>7.142945e+07</td>\n", "      <td>6.980655e+07</td>\n", "      <td>7.304639e+07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2023-01-05</td>\n", "      <td>Map</td>\n", "      <td>Map DAU</td>\n", "      <td>Global</td>\n", "      <td>False</td>\n", "      <td>70783910</td>\n", "      <td>2025-07-14</td>\n", "      <td>7.197238e+07</td>\n", "      <td>7.042742e+07</td>\n", "      <td>7.358907e+07</td>\n", "      <td>NaN</td>\n", "      <td>7.206563e+07</td>\n", "      <td>7.051959e+07</td>\n", "      <td>7.378284e+07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>2023-01-06</td>\n", "      <td>Map</td>\n", "      <td>Map DAU</td>\n", "      <td>Global</td>\n", "      <td>False</td>\n", "      <td>73650792</td>\n", "      <td>2025-07-14</td>\n", "      <td>7.499773e+07</td>\n", "      <td>7.346285e+07</td>\n", "      <td>7.657872e+07</td>\n", "      <td>NaN</td>\n", "      <td>7.500058e+07</td>\n", "      <td>7.336427e+07</td>\n", "      <td>7.647445e+07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>2023-01-07</td>\n", "      <td>Map</td>\n", "      <td>Map DAU</td>\n", "      <td>Global</td>\n", "      <td>False</td>\n", "      <td>74172322</td>\n", "      <td>2025-07-14</td>\n", "      <td>7.618167e+07</td>\n", "      <td>7.451031e+07</td>\n", "      <td>7.762085e+07</td>\n", "      <td>NaN</td>\n", "      <td>7.607692e+07</td>\n", "      <td>7.443795e+07</td>\n", "      <td>7.763190e+07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>2023-01-08</td>\n", "      <td>Map</td>\n", "      <td>Map DAU</td>\n", "      <td>Global</td>\n", "      <td>False</td>\n", "      <td>71368621</td>\n", "      <td>2025-07-14</td>\n", "      <td>7.322800e+07</td>\n", "      <td>7.175176e+07</td>\n", "      <td>7.479172e+07</td>\n", "      <td>NaN</td>\n", "      <td>7.314671e+07</td>\n", "      <td>7.159666e+07</td>\n", "      <td>7.465941e+07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>2023-01-09</td>\n", "      <td>Map</td>\n", "      <td>Map DAU</td>\n", "      <td>Global</td>\n", "      <td>False</td>\n", "      <td>69295337</td>\n", "      <td>2025-07-14</td>\n", "      <td>7.039450e+07</td>\n", "      <td>6.893757e+07</td>\n", "      <td>7.204243e+07</td>\n", "      <td>NaN</td>\n", "      <td>7.060646e+07</td>\n", "      <td>6.897851e+07</td>\n", "      <td>7.213582e+07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>2023-01-10</td>\n", "      <td>Map</td>\n", "      <td>Map DAU</td>\n", "      <td>Global</td>\n", "      <td>False</td>\n", "      <td>68799177</td>\n", "      <td>2025-07-14</td>\n", "      <td>6.992232e+07</td>\n", "      <td>6.823078e+07</td>\n", "      <td>7.138785e+07</td>\n", "      <td>NaN</td>\n", "      <td>7.017129e+07</td>\n", "      <td>6.861093e+07</td>\n", "      <td>7.185030e+07</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        date domain_area metric_name  region  is_forecast  actual_value  \\\n", "0 2023-01-01         Map     Map DAU  Global        False      75051387   \n", "1 2023-01-02         Map     Map DAU  Global        False      72050814   \n", "2 2023-01-03         Map     Map DAU  Global        False      70912931   \n", "3 2023-01-04         Map     Map DAU  Global        False      70531059   \n", "4 2023-01-05         Map     Map DAU  Global        False      70783910   \n", "5 2023-01-06         Map     Map DAU  Global        False      73650792   \n", "6 2023-01-07         Map     Map DAU  Global        False      74172322   \n", "7 2023-01-08         Map     Map DAU  Global        False      71368621   \n", "8 2023-01-09         Map     Map DAU  Global        False      69295337   \n", "9 2023-01-10         Map     Map DAU  Global        False      68799177   \n", "\n", "  forecast_evaluation_date  forecast_mid  forecast_lower  forecast_upper  \\\n", "0               2025-07-14  7.442172e+07    7.298891e+07    7.600328e+07   \n", "1               2025-07-14  7.169923e+07    7.026055e+07    7.324400e+07   \n", "2               2025-07-14  7.131105e+07    6.964320e+07    7.282521e+07   \n", "3               2025-07-14  7.137873e+07    6.988018e+07    7.285414e+07   \n", "4               2025-07-14  7.197238e+07    7.042742e+07    7.358907e+07   \n", "5               2025-07-14  7.499773e+07    7.346285e+07    7.657872e+07   \n", "6               2025-07-14  7.618167e+07    7.451031e+07    7.762085e+07   \n", "7               2025-07-14  7.322800e+07    7.175176e+07    7.479172e+07   \n", "8               2025-07-14  7.039450e+07    6.893757e+07    7.204243e+07   \n", "9               2025-07-14  6.992232e+07    6.823078e+07    7.138785e+07   \n", "\n", "   goal  forecast_mid_original  forecast_lower_original  \\\n", "0   NaN           7.404826e+07             7.247999e+07   \n", "1   NaN           7.163878e+07             7.014526e+07   \n", "2   NaN           7.131159e+07             6.972042e+07   \n", "3   NaN           7.142945e+07             6.980655e+07   \n", "4   NaN           7.206563e+07             7.051959e+07   \n", "5   NaN           7.500058e+07             7.336427e+07   \n", "6   NaN           7.607692e+07             7.443795e+07   \n", "7   NaN           7.314671e+07             7.159666e+07   \n", "8   NaN           7.060646e+07             6.897851e+07   \n", "9   NaN           7.017129e+07             6.861093e+07   \n", "\n", "   forecast_upper_original  \n", "0             7.558532e+07  \n", "1             7.324419e+07  \n", "2             7.288802e+07  \n", "3             7.304639e+07  \n", "4             7.378284e+07  \n", "5             7.647445e+07  \n", "6             7.763190e+07  \n", "7             7.465941e+07  \n", "8             7.213582e+07  \n", "9             7.185030e+07  "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["maps_forecast_result.head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "3409ebfc-7e5c-413b-bce3-7f4091b1aff7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py38", "language": "python", "name": "py38"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}}, "nbformat": 4, "nbformat_minor": 5}