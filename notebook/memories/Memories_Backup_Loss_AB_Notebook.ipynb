{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Memories Save Loss & Backup Loss AB Notebook"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Specify study details\n", "STUDY_NAME = 'MDP_MEDIA_EXPORT_LEVEL_LOW_END__50531'\n", "STUDY_START_DATE = '20230401'\n", "STUDY_END_DATE = '20230402'\n", "\n", "ANALYSIS_START_DATE = STUDY_START_DATE\n", "\n", "CONTROL_ID = '11'\n", "TREATMENT_IDS = ['12']\n", "EXP_NAMES = {\n", "}\n", "\n", "COUNTRIES = []\n", "\n", "# The date to use for user breakdown attributes. Defaults to two days before study start date.\n", "COHORT_DATE = None\n", "\n", "USER_BREAKDOWN_LIST = []\n", "\n", "OVERWRITE = True\n", "INCLUDE_DAILY_METRICS = True\n", "MATERIALIZE_MAPPING_TABLE = True\n", "\n", "# Running config\n", "USE_BATCH_BQ_PRIORITY = False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ensure certain items passed from husky are lists\n", "def ensure_list(parameter):\n", "    if isinstance(parameter, list):\n", "        return parameter\n", "    else:\n", "        return [parameter]\n", "\n", "TREATMENT_IDS = ensure_list(TREATMENT_IDS)\n", "COUNTRIES = ensure_list(COUNTRIES)\n", "BQ_PRIORITY = \"BATCH\" if USE_BATCH_BQ_PRIORITY else \"INTERACTIVE\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from __future__ import division, unicode_literals, print_function\n", "import os\n", "if any(['VELLUM' in k for k in os.environ.keys()]):\n", "    pip_output = !pip install banjo\n", "\n", "import seaborn as sns\n", "import pandas as pd\n", "import logging\n", "logging.basicConfig()\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "%matplotlib inline\n", "%config InlineBackend.figure_format = 'retina'\n", "\n", "from banjo import abtest # see: go/pya\n", "from banjo.abtest.report import (\n", "    Metric, MetricTable, Report, get_quest_metric_table, CustomReport, \n", "    get_abtest_console_metric_table, QuantileReport\n", ")\n", "from banjo.abtest.cohort_report import CohortReport\n", "from banjo.abtest.quest import TIER_ONE\n", "\n", "try:\n", "    from banjo.teams.product import memories_save_backup_loss as mm_loss\n", "    from banjo.teams.product import memories_backup_incomplete as mm_backup_incomplete\n", "except ImportError:\n", "    GIT_DIR = os.path.join(os.environ['HOME'])\n", "    os.chdir(GIT_DIR)\n", "    print(\"Upgrade banjo to 0.14.0+, which packages all these scripts\")\n", "    import memories_backup_loss as mm_backup_loss\n", "   \n", "    \n", "PROJECT = 'sc-bq-gcs-billingonly'\n", "sns.set(style='whitegrid', font_scale=1.5)\n", "\n", "from IPython.core.display import display, HTML\n", "display(HTML(\"<style>.output_html.rendered_html table { font-size:8pt;}</style>\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Add metrics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Threshold in days used for labeling a backup as incomplete\n", "thresholds=[0,2,3,7,14,30,90]\n", "memories_backup_incomplete_rate = [mm_backup_incomplete.memories_backup_incomplete_rate_metrics(t, STUDY_START_DATE, STUDY_END_DATE) for t in thresholds]\n", "memories_backup_error_rate=[mm_backup_incomplete.memories_backup_error_rate_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "memories_save_loss = [mm_loss.memories_save_loss_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "memories_save_loss_uu = [mm_loss.memories_save_loss_uu_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "camera_roll_save_loss = [mm_loss.camera_roll_save_loss_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "# gallery_save_event_memories_save_loss = [mm_loss.gallery_save_event_memories_save_loss_metrics(STUDY_START_DATE, STUDY_END_DATE)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Construct the report object\n", "metric_tables = memories_backup_incomplete_rate + memories_backup_error_rate + \\\n", "                memories_save_loss + memories_save_loss_uu + \\\n", "                camera_roll_save_loss # + gallery_save_event_memories_save_loss\n", "\n", "report = CohortReport(\n", "    study_name=STUDY_NAME,\n", "    study_start_date=STUDY_START_DATE,\n", "    study_end_date=STUDY_END_DATE,\n", "    metric_tables=metric_tables,\n", "    control_id=CONTROL_ID,\n", "    treatment_ids=TREATMENT_IDS,\n", "    user_group_bys=USER_BREAKDOWN_LIST,\n", "    cohort_definition_date=COHORT_DATE,\n", "    materializing_mapping_table=MATERIALIZE_MAPPING_TABLE,\n", "    bq_project='sc-bq-gcs-billingonly',  \n", "    dest_dataset='temp_abtest',\n", "    bq_priority=BQ_PRIORITY,\n", "    excel_output=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": []}, "outputs": [], "source": ["# Run the joins and calculate the results\n", "report.execute(\n", "    overwrite=OVERWRITE, \n", "    group_filters={\"l_90_country\": COUNTRIES}\n", "    # False: if the table to save the joined data already exists, we\n", "    #        will assume the job has been previously run and reuse the results\n", "    # True: always rerun the join and overwrite the table\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report.configurations['pivot_table'] = True\n", "report.configurations['stat_fmtr'] = (\n", "    \"{pct_diff:,.2f}% ({avg_control:,.3}→{avg_treatment:,.3}, {p_value_formatted})\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["memories_backup_incomplete_rate = [m for mt in memories_backup_incomplete_rate for m in mt.cumulative_metrics]\n", "memories_backup_error_rate = [m for mt in memories_backup_error_rate for m in mt.cumulative_metrics]\n", "memories_save_loss = [m for mt in memories_save_loss for m in mt.cumulative_metrics]\n", "memories_save_loss_uu = [m for mt in memories_save_loss_uu for m in mt.cumulative_metrics]\n", "camera_roll_save_loss = [m for mt in camera_roll_save_loss for m in mt.cumulative_metrics]\n", "# gallery_save_event_memories_save_loss = [m for mt in gallery_save_event_memories_save_loss for m in mt.cumulative_metrics]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["abtest.report.notebook_print(\"Study Name: {}\".format(report.study_name), \"h3\")\n", "abtest.report.notebook_print(\"Control ID: {}\".format(report.control_id), \"strong\")\n", "abtest.report.notebook_print(\"Treatment ID(s): {}\".format(\", \".join(report.treatment_ids)), \"strong\")\n", "abtest.report.notebook_print(\n", "    \"Study running period: {} - {}\".format(report.study_start_date, report.study_end_date), \n", "    \"strong\"\n", ")\n", "abtest.report.notebook_print(\n", "    \"Report analysis period: {} - {}\".format(STUDY_START_DATE, STUDY_END_DATE), \n", "    \"strong\"\n", ")\n", "abtest.report.notebook_print(\n", "    \"Breakdowns: <br> {}\".format(\n", "        \"<br>\".join(\", \".join(breakdown) for breakdown in report.user_group_by_list)\n", "    ), \n", "    \"strong\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Group Metrics\n", "report.ab_printer.print_text(\"Memories Save Loss Metrics\", 'h2')\n", "report.ab_printer.print_text(\"Memories Save Loss rate measures the percentage of Memories save action that failed. It contains instances of users saving to Memories only or saving to both Memories and Camera Roll. Here we calculated two save loss rates using different blizzard events.\", 'h')\n", "report.ab_printer.print_text(\" • Save Loss Rate (V1) is calculated based on capture sessions with DIRECT_SNAP_ACTION where with_gallery = True without DIRECT_SNAP_SAVE\", 'h')\n", "report.ab_printer.print_text(\" • Save Loss Rate (V2) is calculated based on capture sessions with DIRECT_SNAP_ACTION where with_gallery = True and GALLERY_SAVE_EVENT where step = START without GALLERY_SAVE_EVENT where step = FINISH\", 'h')\n", "report.ab_printer.print_text(\" • Operational Save Loss is calculated based on capture sessions with GALLERY_SAVE_EVENT where step = START without GALLERY_SAVE_EVENT where step = FINISH.\", 'h')\n", "report.ab_printer.print_text(\" • Gallery Save Event Start Loss Rate is calculated based on capture sessions with DIRECT_SNAP_ACTION where with_gallery = True without GALLERY_SAVE_EVENT where step = START.\", 'h')\n", "report.generate_report(\n", "    format_pvalue=True,\n", "    extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "    display_config={'cumulative': ['table'], \n", "                    'daily': [] #['trend']\n", "                   },\n", "    metric_filters = {\n", "        'metrics': memories_save_loss\n", "    }\n", ");"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Group Metrics\n", "report.ab_printer.print_text(\"Memories Save Loss UU Metrics\", 'h2')\n", "report.ab_printer.print_text(\"This section covers the memories save loss metrics at the UU (Unique User) level\", \"h\")\n", "report.generate_report(\n", "    format_pvalue=True,\n", "    extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "    display_config={'cumulative': ['table'], \n", "                    'daily': [] #['trend']\n", "                   },\n", "    metric_filters = {\n", "        'metrics': memories_save_loss_uu\n", "    }\n", ");"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Group Metrics\n", "report.ab_printer.print_text(\"Memories Backup Incomplete Metrics\", 'h2')\n", "report.generate_report(\n", "    format_pvalue=True,\n", "    extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "    display_config={'cumulative': ['table'], 'daily': []},\n", "    metric_filters = {\n", "        'metrics': memories_backup_incomplete_rate\n", "    }\n", ");"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Group Metrics\n", "report.ab_printer.print_text(\"Memories Backup Error Metrics\", 'h2')\n", "report.generate_report(\n", "    format_pvalue=True,\n", "    extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "    display_config={'cumulative': ['table'], 'daily': []},\n", "    metric_filters = {\n", "        'metrics': memories_backup_error_rate\n", "    }\n", ");"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report.ab_printer.print_text(\"Camera Roll Save Loss Metrics\", 'h2') # change to Camera Roll Save Loss Metrics\n", "report.generate_report(\n", "    format_pvalue=True,\n", "    extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "    display_config={'cumulative': ['table'], \n", "                    'daily': [] #['trend']\n", "                   },\n", "    metric_filters = {\n", "        'metrics': camera_roll_save_loss\n", "    }\n", ");"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# report.ab_printer.print_text(\"Gallery Save Event Memories Save Loss Metrics\", 'h2') # change to Camera Roll Save Loss Metrics\n", "# report.generate_report(\n", "#     format_pvalue=True,\n", "#     extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "#     display_config={'cumulative': ['table'], \n", "#                     'daily': [] #['trend']\n", "#                    },\n", "#     metric_filters = {\n", "#         'metrics': gallery_save_event_memories_save_loss\n", "#     }\n", "# );"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["VELLUM = True\n", "\n", "# Show the link to download \n", "if VELLUM:\n", "    excel_file_url = report.upload_excel_output_to_gcs()\n", "if excel_file_url:\n", "    display(HTML(\n", "        \"<p><strong><a href='{}' target='__blank'>Click to download this report in excel format</a>. \"\n", "        \"You can upload it to Google Drive and open Google Sheets.</strong></p>\".format(excel_file_url)\n", "    ))\n"]}], "metadata": {"anaconda-cloud": {}, "celltoolbar": "Tags", "kernelspec": {"display_name": "py310", "language": "python", "name": "py310"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.6"}}, "nbformat": 4, "nbformat_minor": 4}