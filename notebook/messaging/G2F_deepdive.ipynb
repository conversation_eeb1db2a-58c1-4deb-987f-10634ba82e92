{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Imports\n", "from __future__ import division, unicode_literals, print_function\n", "import pandas as pd\n", "import os\n", "if any(['VELLUM' in k for k in os.environ.keys()]):\n", "    pip_output = !pip install banjo\n", "\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import logging\n", "logging.basicConfig()\n", "logging.getLogger().setLevel(logging.ERROR)\n", "\n", "%matplotlib inline\n", "%config InlineBackend.figure_format = 'retina'\n", "\n", "import banjo\n", "from banjo import abtest, utils # see: go/pya\n", "from banjo.abtest.report import (\n", "    Metric, MetricTable, Report, QuantileReport, get_quest_metric_table,\n", "    get_abtest_console_metric_table, CustomReport,\n", ")\n", "from banjo.abtest.cohort_report import CohortReport\n", "from banjo.abtest.quest import TIER_ONE\n", "\n", "try:\n", "    from banjo.teams.product import friend_feed as ff\n", "\n", "except ImportError:\n", "    GIT_DIR = os.path.join(os.environ['HOME'])\n", "    os.chdir(GIT_DIR)\n", "    print(\"Upgrade banjo to 0.14.0+, which packages all these scripts\")\n", "    import friend_feed as ff\n", "\n", "PROJECT = 'sc-bq-gcs-billingonly'\n", "sns.set(style='whitegrid', font_scale=1.5)\n", "\n", "from IPython.core.display import display, HTML\n", "display(HTML(\"<style>.output_html.rendered_html table { font-size:8pt;}</style>\"))"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# import banjo\n", "# banjo.__version__\n", "# print(banjo.__version__)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# import friend_feed as ff"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["### Set up analysis\n", "#### Specify A/B testing study details"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["STUDY_NAME = 'ARROYO_PRESENCE'\n", "STUDY_START_DATE = '20210120'\n", "STUDY_END_DATE = '20210120'\n", "CONTROL_ID = '100'\n", "TREATMENT_IDS = ['98']\n", "\n", "USER_BREAKDOWN_LIST = [] #'device_name','study_device_cluster','study_country','app_L7','device_model','os_version'\n", "\n", "METRICS_GROUP_CONFIG = ['friend_feed_ready_overstat_metrics',\n", "                     'friend_feed_ready_latency_metrics',\n", "                     'g2f_success_latency',\n", "                     'g2f_ratio_and_failure_metrics',\n", "                     'g2f_sub_latencies',\n", "                     'g2f_failure_latency',\n", "                     'g2f_metrics_notification',\n", "                     'g2f_failure_reason_breakdown',\n", "                     'chat_display_ready_latency_metrics',\n", "                     'chat_display_ready_failure_rate',\n", "                     'chat_display_ready_failure_reason_breakdown'\n", "                    ]\n", "\n", "QUANTILES = ['50','90','99']\n", "OVERWRITE = True\n", "PIVOT_RESULTS = True\n", "DAILY_TREND = False\n", "EXP_NAMES = {\n", "}"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# <PERSON>\n", "# Ensure certain items passed from husky are lists\n", "USE_BATCH_BQ_PRIORITY = True\n", "def ensure_list(parameter):\n", "    if isinstance(parameter, list):\n", "        return parameter\n", "    else:\n", "        return [parameter]\n", "TREATMENT_IDS = ensure_list(TREATMENT_IDS)\n", "\n", "BQ_PRIORITY = \"BATCH\" if USE_BATCH_BQ_PRIORITY else \"INTERACTIVE\"\n", "ALL_METRICS_GROUPS = {}\n", "\n", "# decide if print daily trend\n", "daily_display = []\n", "if DAILY_TREND:\n", "    daily_display = ['trend']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ALL_METRICS_GROUPS['friend_feed_ready_overstat_metrics']=[ff.friend_feed_ready_overstat_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['friend_feed_ready_latency_metrics']=[ff.friend_feed_ready_latency_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['g2f_success_latency'] = [ff.g2f_success_latency(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['g2f_ratio_and_failure_metrics'] = [ff.g2f_ratio_and_failure_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['g2f_sub_latencies'] = [ff.g2f_sub_latencies(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['g2f_failure_latency'] = [ff.g2f_failure_latency(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['g2f_metrics_notification'] = [ff.g2f_metrics_notification(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['g2f_failure_reason_breakdown']=[ff.g2f_failure_reason_breakdown(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['chat_display_ready_latency_metrics']=[ff.chat_display_ready_latency_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['chat_display_ready_failure_rate']=[ff.chat_display_ready_failure_rate(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['chat_display_ready_failure_reason_breakdown']=[ff.chat_display_ready_failure_reason_breakdown(STUDY_START_DATE, STUDY_END_DATE)]"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["metric_tables = [\n", "    metric_table \n", "    for sublist in [ALL_METRICS_GROUPS[mt] for mt in ALL_METRICS_GROUPS if mt in METRICS_GROUP_CONFIG]\n", "    for metric_table in sublist\n", "]\n", "# print(metric_tables)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Get Experiment Names\n", "study_name_sql = \"\"\"\n", "    SELECT\n", "      name,\n", "      exp_id, \n", "      study_version\n", "    FROM\n", "      `sc-analytics.report_search.ab_console_study_config*`\n", "    WHERE\n", "      study_name = '{study_name}'\n", "      AND exp_id IN ('{exp_ids}')\n", "\"\"\"\n", "df_exp_names = utils.gbq.read_gbq(\n", "    study_name_sql.format(\n", "        study_name=STUDY_NAME, exp_ids=\"', '\".join([CONTROL_ID] + TREATMENT_IDS),\n", "    ),\n", "    dialect='standard',\n", "    project_id=PROJECT)\n", "if not EXP_NAMES and not df_exp_names.empty:\n", "    EXP_NAMES = dict(zip(df_exp_names.exp_id, df_exp_names.name))"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["report = CohortReport(\n", "    study_name=STUDY_NAME,\n", "    study_start_date=STUDY_START_DATE,\n", "    study_end_date=STUDY_END_DATE,\n", "    metric_tables=metric_tables,\n", "    control_id=CONTROL_ID,\n", "    treatment_ids=TREATMENT_IDS,\n", "    user_group_bys=USER_BREAKDOWN_LIST,\n", "    bq_project='sc-bq-gcs-billingonly',  \n", "    dest_dataset='temp_abtest',\n", "    quantiles=QUANTILES,\n", "    materializing_mapping_table=True,\n", "    overwrite_mapping_table=True,  \n", "    exp_id_to_name=EXP_NAMES,\n", "    bq_priority=BQ_PRIORITY,\n", ")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<h3>Study Name: ARROYO_PRESENCE</h3>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<strong>Control ID: 100</strong>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<strong>Treatment ID(s): 98</strong>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<strong>Analysis period: 20210120 - 20210120</strong>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<strong>Breakdowns: <br> </strong>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["abtest.report.notebook_print(\"Study Name: {}\".format(report.study_name), \"h3\")\n", "abtest.report.notebook_print(\"Control ID: {}\".format(report.control_id), \"strong\")\n", "abtest.report.notebook_print(\"Treatment ID(s): {}\".format(\", \".join(report.treatment_ids)), \"strong\")\n", "abtest.report.notebook_print(\n", "    \"Analysis period: {} - {}\".format(report.analysis_start_date, report.study_end_date), \n", "    \"strong\"\n", ")\n", "abtest.report.notebook_print(\n", "    \"Breakdowns: <br> {}\".format(\n", "        \"<br>\".join(\", \".join(breakdown) for breakdown in report.user_group_by_list)\n", "    ), \n", "    \"strong\"\n", ")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.local/lib/python3.6/site-packages/banjo/abtest/stat_funcs.py:286: RuntimeWarning: invalid value encountered in double_scalars\n", "  difference = 100 * (delta_quantile / c_q)\n", "/home/<USER>/.local/lib/python3.6/site-packages/banjo/abtest/stat_funcs.py:62: RuntimeWarning: invalid value encountered in double_scalars\n", "  t_val = (c_mean - t_mean) / np.sqrt(c_variation / c_n + t_variation / t_n)\n", "/home/<USER>/.local/lib/python3.6/site-packages/banjo/abtest/stat_funcs.py:64: RuntimeWarning: invalid value encountered in double_scalars\n", "  (c_variation ** 2 / (c_n ** 2 * (c_n - 1)) + t_variation ** 2 / (t_n ** 2 * (t_n - 1)))\n", "/home/<USER>/.local/lib/python3.6/site-packages/banjo/abtest/stat_funcs.py:67: RuntimeWarning: invalid value encountered in double_scalars\n", "  difference = 100 * (delta_mean / c_mean)\n", "/home/<USER>/.local/lib/python3.6/site-packages/banjo/abtest/stat_funcs.py:353: RuntimeWarning: divide by zero encountered in double_scalars\n", "  return (1 / (x_mean * x_mean)) * (y_var / n_y) + \\\n", "/home/<USER>/.local/lib/python3.6/site-packages/banjo/abtest/stat_funcs.py:353: RuntimeWarning: invalid value encountered in double_scalars\n", "  return (1 / (x_mean * x_mean)) * (y_var / n_y) + \\\n", "/home/<USER>/.local/lib/python3.6/site-packages/banjo/abtest/stat_funcs.py:354: RuntimeWarning: invalid value encountered in double_scalars\n", "  ((y_mean * y_mean) / (x_mean * x_mean * x_mean * x_mean)) * (x_var / n_x)\n", "/home/<USER>/.local/lib/python3.6/site-packages/banjo/abtest/stat_funcs.py:67: RuntimeWarning: divide by zero encountered in double_scalars\n", "  difference = 100 * (delta_mean / c_mean)\n", "/home/<USER>/.local/lib/python3.6/site-packages/banjo/abtest/stat_funcs.py:354: RuntimeWarning: divide by zero encountered in double_scalars\n", "  ((y_mean * y_mean) / (x_mean * x_mean * x_mean * x_mean)) * (x_var / n_x)\n", "/home/<USER>/.local/lib/python3.6/site-packages/banjo/abtest/stat_funcs.py:286: RuntimeWarning: invalid value encountered in double_scalars\n", "  difference = 100 * (delta_quantile / c_q)\n", "/home/<USER>/.local/lib/python3.6/site-packages/banjo/abtest/stat_funcs.py:62: RuntimeWarning: invalid value encountered in double_scalars\n", "  t_val = (c_mean - t_mean) / np.sqrt(c_variation / c_n + t_variation / t_n)\n", "/home/<USER>/.local/lib/python3.6/site-packages/banjo/abtest/stat_funcs.py:64: RuntimeWarning: invalid value encountered in double_scalars\n", "  (c_variation ** 2 / (c_n ** 2 * (c_n - 1)) + t_variation ** 2 / (t_n ** 2 * (t_n - 1)))\n", "/home/<USER>/.local/lib/python3.6/site-packages/banjo/abtest/stat_funcs.py:67: RuntimeWarning: invalid value encountered in double_scalars\n", "  difference = 100 * (delta_mean / c_mean)\n", "/home/<USER>/.local/lib/python3.6/site-packages/banjo/abtest/stat_funcs.py:353: RuntimeWarning: divide by zero encountered in double_scalars\n", "  return (1 / (x_mean * x_mean)) * (y_var / n_y) + \\\n", "/home/<USER>/.local/lib/python3.6/site-packages/banjo/abtest/stat_funcs.py:353: RuntimeWarning: invalid value encountered in double_scalars\n", "  return (1 / (x_mean * x_mean)) * (y_var / n_y) + \\\n", "/home/<USER>/.local/lib/python3.6/site-packages/banjo/abtest/stat_funcs.py:354: RuntimeWarning: invalid value encountered in double_scalars\n", "  ((y_mean * y_mean) / (x_mean * x_mean * x_mean * x_mean)) * (x_var / n_x)\n", "/home/<USER>/.local/lib/python3.6/site-packages/banjo/abtest/stat_funcs.py:67: RuntimeWarning: divide by zero encountered in double_scalars\n", "  difference = 100 * (delta_mean / c_mean)\n", "/home/<USER>/.local/lib/python3.6/site-packages/banjo/abtest/stat_funcs.py:354: RuntimeWarning: divide by zero encountered in double_scalars\n", "  ((y_mean * y_mean) / (x_mean * x_mean * x_mean * x_mean)) * (x_var / n_x)\n"]}], "source": ["# Run the joins and calculate the results\n", "report.execute(\n", "    overwrite=OVERWRITE\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ratio_metric_tables=[]\n", "latency_metric_tables=[]\n", "detailed_metrics=[]\n", "for metrics in metric_tables: \n", "    if metrics.name==\"friend_feed_ready_overstat_metrics\" or metrics.name==\"g2f_ratio_and_failure_metrics\" or metrics.name==\"chat_display_ready_failure_rate\": \n", "        ratio_metric_tables.append(metrics)\n", "    if metrics.name==\"g2f_failure_reason_breakdown\" or metrics.name==\"chat_display_ready_failure_reason_breakdown\":\n", "        detailed_metrics.append(metrics)\n", "    if metrics.name==\"friend_feed_ready_latency_metrics\" or metrics.name==\"g2f_success_latency\" or metrics.name==\"g2f_sub_latencies\" or metrics.name==\"g2f_failure_latency\" or metrics.name==\"g2f_metrics_notification\" or metrics.name==\"chat_display_ready_latency_metrics\":\n", "        latency_metric_tables.append(metrics)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["report.configurations[\"pivot_table\"] = True\n", "# quantile_fmtr = \"{pct_diff:,.2f}% ({quantile_control:,.10}→{quantile_treatment:,.10}, {p_value_formatted})\"\n", "# if not PIVOT_RESULTS:\n", "#     quantile_fmtr = \"P{quantile}: \" + quantile_fmtr "]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<h2>Friend Feed Ready Overstat Metrics</h2>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5>Overall</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5>Comparison of user overall (cumulative) metric values during the study period</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p><style  type=\"text/css\" >\n", "    #T_06c42c84_6ccb_11eb_9c43_32e124840315row7_col0 {\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #f89999;\n", "        }</style><table id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315\" ><thead>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Statistic</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level1\" >control_id</th>        <th class=\"col_heading level1 col0\" >100 (Control)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level2\" >treatment_id</th>        <th class=\"col_heading level2 col0\" >98 (ArroyoDuplex)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level3\" >Sample Sizes</th>        <th class=\"col_heading level3 col0\" >Treatment=6,639,404<br>\n", "Control=6,643,389<br>\n", "No SSPM (p=0.2742)</th>    </tr>    <tr>        <th class=\"index_name level0\" >Date</th>        <th class=\"index_name level1\" >Metric</th>        <th class=\"blank\" ></th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315level0_row0\" class=\"row_heading level0 row0\" rowspan=18>01/20 to 01/20</th>\n", "                        <th id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315level1_row0\" class=\"row_heading level1 row0\" >Friend Feed Ready Event Count</th>\n", "                        <td id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315row0_col0\" class=\"data row0 col0\" >0.05% (30.808→30.825, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315level1_row1\" class=\"row_heading level1 row1\" >FFR Cold Start Count</th>\n", "                        <td id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315row1_col0\" class=\"data row1 col0\" >0.21% (6.9625→6.9774, < 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315level1_row2\" class=\"row_heading level1 row2\" >FFR Cold Start Ratio</th>\n", "                        <td id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315row2_col0\" class=\"data row2 col0\" >0.16% (0.22599→0.22636, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315level1_row3\" class=\"row_heading level1 row3\" >FFR Warm Start Count</th>\n", "                        <td id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315row3_col0\" class=\"data row3 col0\" >0.01% (23.846→23.847, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315level1_row4\" class=\"row_heading level1 row4\" >FFR Warm Start Ratio</th>\n", "                        <td id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315row4_col0\" class=\"data row4 col0\" >-0.05% (0.77401→0.77364, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315level1_row5\" class=\"row_heading level1 row5\" >FFR Sync Not Complete (includes failure and user bailed)</th>\n", "                        <td id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315row5_col0\" class=\"data row5 col0\" >0.12% (0.75462→0.7555, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315level1_row6\" class=\"row_heading level1 row6\" >FFR Sync Not Complete Rate (includes failure and user bailed)</th>\n", "                        <td id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315row6_col0\" class=\"data row6 col0\" >0.06% (0.024494→0.024509, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315level1_row7\" class=\"row_heading level1 row7\" >FFR Cold Sync Not Complete (includes failure and user bailed)</th>\n", "                        <td id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315row7_col0\" class=\"data row7 col0\" >0.63% (0.17908→0.1802, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315level1_row8\" class=\"row_heading level1 row8\" >FFR Cold Sync Not Complete Rate (includes failure and user bailed)</th>\n", "                        <td id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315row8_col0\" class=\"data row8 col0\" >0.41% (0.02572→0.025826, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315level1_row9\" class=\"row_heading level1 row9\" >FFR Warm Sync Not Complete (includes failure and user bailed)</th>\n", "                        <td id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315row9_col0\" class=\"data row9 col0\" >-0.04% (0.57554→0.5753, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315level1_row10\" class=\"row_heading level1 row10\" >FFR Warm Sync Not Complete Rate (includes failure and user bailed)</th>\n", "                        <td id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315row10_col0\" class=\"data row10 col0\" >-0.05% (0.024136→0.024124, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315level1_row11\" class=\"row_heading level1 row11\" >Greater than 0 Convos Synced Count</th>\n", "                        <td id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315row11_col0\" class=\"data row11 col0\" >0.09% (11.021→11.032, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315level1_row12\" class=\"row_heading level1 row12\" >Greater than 0 Convos Synced Rate (includes failure and user bailed)</th>\n", "                        <td id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315row12_col0\" class=\"data row12 col0\" >0.09% (0.46218→0.46259, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315level1_row13\" class=\"row_heading level1 row13\" >Stale Feed Count</th>\n", "                        <td id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315row13_col0\" class=\"data row13 col0\" >0.02% (6.9896→6.9907, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315level1_row14\" class=\"row_heading level1 row14\" >Stale Feed Ratio (primary overstat metric)</th>\n", "                        <td id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315row14_col0\" class=\"data row14 col0\" >-0.04% (0.22687→0.22679, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315level1_row15\" class=\"row_heading level1 row15\" >Slow Feed Count</th>\n", "                        <td id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315row15_col0\" class=\"data row15 col0\" >-0.06% (1.6843→1.6832, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315level1_row16\" class=\"row_heading level1 row16\" >Slow Feed Load Ratio (secondary overstat metric)</th>\n", "                        <td id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315row16_col0\" class=\"data row16 col0\" >-0.12% (0.05467→0.054606, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315level1_row17\" class=\"row_heading level1 row17\" >Total Convos Synced</th>\n", "                        <td id=\"T_06c42c84_6ccb_11eb_9c43_32e124840315row17_col0\" class=\"data row17 col0\" >0.03% (20.495→20.5, ≥ 0.1)</td>\n", "            </tr>\n", "    </tbody></table></p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h2>G2F Ratio And Failure Metrics</h2>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5>Overall</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5>Comparison of user overall (cumulative) metric values during the study period</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p><style  type=\"text/css\" >\n", "    #T_06fb47a0_6ccb_11eb_9c43_32e124840315row16_col0 {\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #99cc99;\n", "        }</style><table id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315\" ><thead>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Statistic</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level1\" >control_id</th>        <th class=\"col_heading level1 col0\" >100 (Control)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level2\" >treatment_id</th>        <th class=\"col_heading level2 col0\" >98 (ArroyoDuplex)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level3\" >Sample Sizes</th>        <th class=\"col_heading level3 col0\" >Treatment=6,639,404<br>\n", "Control=6,643,389<br>\n", "No SSPM (p=0.2742)</th>    </tr>    <tr>        <th class=\"index_name level0\" >Date</th>        <th class=\"index_name level1\" >Metric</th>        <th class=\"blank\" ></th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level0_row0\" class=\"row_heading level0 row0\" rowspan=25>01/20 to 01/20</th>\n", "                        <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level1_row0\" class=\"row_heading level1 row0\" >G2FF Event Count</th>\n", "                        <td id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315row0_col0\" class=\"data row0 col0\" >0.03% (37.701→37.712, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level1_row1\" class=\"row_heading level1 row1\" >G2FF Cold Start Count</th>\n", "                        <td id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315row1_col0\" class=\"data row1 col0\" >0.18% (8.2594→8.2739, < 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level1_row2\" class=\"row_heading level1 row2\" >G2FF Cold Start Ratio</th>\n", "                        <td id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315row2_col0\" class=\"data row2 col0\" >0.15% (0.21908→0.2194, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level1_row3\" class=\"row_heading level1 row3\" >G2FF Warm Start Count</th>\n", "                        <td id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315row3_col0\" class=\"data row3 col0\" >0.21% (0.75296→0.75456, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level1_row4\" class=\"row_heading level1 row4\" >G2FF Warm Start Ratio</th>\n", "                        <td id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315row4_col0\" class=\"data row4 col0\" >0.15% (0.21908→0.2194, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level1_row5\" class=\"row_heading level1 row5\" >G2FF Hot Start Count</th>\n", "                        <td id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315row5_col0\" class=\"data row5 col0\" >-0.01% (28.49→28.486, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level1_row6\" class=\"row_heading level1 row6\" >G2FF Hot Start Ratio</th>\n", "                        <td id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315row6_col0\" class=\"data row6 col0\" >-0.04% (0.75568→0.75535, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level1_row7\" class=\"row_heading level1 row7\" >G2FF Failure Count (excluding user bailed)</th>\n", "                        <td id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315row7_col0\" class=\"data row7 col0\" >0.07% (0.46197→0.46231, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level1_row8\" class=\"row_heading level1 row8\" >G2FF Failure Rate (excluding user bailed)</th>\n", "                        <td id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315row8_col0\" class=\"data row8 col0\" >0.04% (0.012254→0.012259, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level1_row9\" class=\"row_heading level1 row9\" >G2FF Cold Failure Count (excluding user bailed)</th>\n", "                        <td id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315row9_col0\" class=\"data row9 col0\" >0.10% (0.1002→0.10029, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level1_row10\" class=\"row_heading level1 row10\" >G2FF Cold Failure Rate (excluding user bailed)</th>\n", "                        <td id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315row10_col0\" class=\"data row10 col0\" >-0.08% (0.012131→0.012122, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level1_row11\" class=\"row_heading level1 row11\" >G2FF Warm Failure Count (excluding user bailed)</th>\n", "                        <td id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315row11_col0\" class=\"data row11 col0\" >0.48% (0.0064741→0.0065051, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level1_row12\" class=\"row_heading level1 row12\" >G2FF Warm Failure Rate (excluding user bailed)</th>\n", "                        <td id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315row12_col0\" class=\"data row12 col0\" >0.27% (0.0085982→0.0086211, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level1_row13\" class=\"row_heading level1 row13\" >G2FF Hot Failure Count (excluding user bailed)</th>\n", "                        <td id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315row13_col0\" class=\"data row13 col0\" >0.04% (0.35312→0.35327, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level1_row14\" class=\"row_heading level1 row14\" >G2FF Hot Failure Rate (excluding user bailed)</th>\n", "                        <td id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315row14_col0\" class=\"data row14 col0\" >0.06% (0.012395→0.012402, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level1_row15\" class=\"row_heading level1 row15\" >G2FF User Bailed Count</th>\n", "                        <td id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315row15_col0\" class=\"data row15 col0\" >-0.21% (0.84942→0.84764, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level1_row16\" class=\"row_heading level1 row16\" >G2FF User Bailed Rate</th>\n", "                        <td id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315row16_col0\" class=\"data row16 col0\" >-0.24% (0.022531→0.022477, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level1_row17\" class=\"row_heading level1 row17\" >G2FF Cold User Bailed Count</th>\n", "                        <td id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315row17_col0\" class=\"data row17 col0\" >-0.07% (0.15289→0.15278, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level1_row18\" class=\"row_heading level1 row18\" >G2FF Cold User Bailed Rate</th>\n", "                        <td id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315row18_col0\" class=\"data row18 col0\" >-0.25% (0.018511→0.018465, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level1_row19\" class=\"row_heading level1 row19\" >G2FF Warm User Bailed Count</th>\n", "                        <td id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315row19_col0\" class=\"data row19 col0\" >-0.12% (0.021584→0.021558, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level1_row20\" class=\"row_heading level1 row20\" >G2FF Warm User Bailed Rate</th>\n", "                        <td id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315row20_col0\" class=\"data row20 col0\" >-0.33% (0.028665→0.02857, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level1_row21\" class=\"row_heading level1 row21\" >G2FF Hot User Bailed Count</th>\n", "                        <td id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315row21_col0\" class=\"data row21 col0\" >-0.23% (0.67032→0.66874, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level1_row22\" class=\"row_heading level1 row22\" >G2FF Hot User Bailed Rate</th>\n", "                        <td id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315row22_col0\" class=\"data row22 col0\" >-0.22% (0.023528→0.023476, < 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level1_row23\" class=\"row_heading level1 row23\" >G2FF With Notification Count</th>\n", "                        <td id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315row23_col0\" class=\"data row23 col0\" >-0.71% (243.01→241.29, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315level1_row24\" class=\"row_heading level1 row24\" >G2FF With Notification Rate</th>\n", "                        <td id=\"T_06fb47a0_6ccb_11eb_9c43_32e124840315row24_col0\" class=\"data row24 col0\" >-0.74% (6.4458→6.3983, ≥ 0.1)</td>\n", "            </tr>\n", "    </tbody></table></p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["report.configurations['stat_fmtr'] = (\n", "    \"{pct_diff:,.2f}% ({avg_control:,.5}→{avg_treatment:,.5}, {p_value_formatted})\") \n", "for metrics in ratio_metric_tables:\n", "    abtest.report.notebook_print(metrics.name.replace('_', ' ').title(), \"h2\")\n", "    report.generate_report(\n", "        extra_table_cols=['treatment_id',\n", "                          'avg_control', 'avg_treatment',\n", "                          'count_control', 'count_treatment',\n", "                         ],\n", "        facet_bys=['treatment_id'],\n", "        format_pvalue=True,\n", "        metric_filters={\"metric\": metrics.cumulative_metrics},\n", "        display_config={\"cumulative\": [\"table\"], \"daily\": daily_display}\n", "    ); "]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<h2>Friend Feed Ready Latency Metrics</h2>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5>Overall</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5>Comparison of user overall (cumulative) metric values during the study period</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p><style  type=\"text/css\" >\n", "    #T_07618704_6ccb_11eb_9c43_32e124840315row2_col0 {\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #99cc99;\n", "        }    #T_07618704_6ccb_11eb_9c43_32e124840315row6_col0 {\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #f56666;\n", "        }    #T_07618704_6ccb_11eb_9c43_32e124840315row19_col0 {\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #008000;\n", "        }    #T_07618704_6ccb_11eb_9c43_32e124840315row20_col0 {\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #99cc99;\n", "        }    #T_07618704_6ccb_11eb_9c43_32e124840315row25_col0 {\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #008000;\n", "        }    #T_07618704_6ccb_11eb_9c43_32e124840315row26_col0 {\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #339933;\n", "        }    #T_07618704_6ccb_11eb_9c43_32e124840315row43_col0 {\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #99cc99;\n", "        }    #T_07618704_6ccb_11eb_9c43_32e124840315row53_col0 {\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #99cc99;\n", "        }</style><table id=\"T_07618704_6ccb_11eb_9c43_32e124840315\" ><thead>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank\" ></th>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Statistic</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank\" ></th>        <th class=\"index_name level1\" >control_id</th>        <th class=\"col_heading level1 col0\" >100 (Control)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank\" ></th>        <th class=\"index_name level2\" >treatment_id</th>        <th class=\"col_heading level2 col0\" >98 (ArroyoDuplex)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank\" ></th>        <th class=\"index_name level3\" >Sample Sizes</th>        <th class=\"col_heading level3 col0\" >Treatment=6,639,404<br>\n", "Control=6,643,389<br>\n", "No SSPM (p=0.2742)</th>    </tr>    <tr>        <th class=\"index_name level0\" >Date</th>        <th class=\"index_name level1\" >Metric</th>        <th class=\"index_name level2\" >quantile</th>        <th class=\"blank\" ></th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level0_row0\" class=\"row_heading level0 row0\" rowspan=54>01/20 to 01/20</th>\n", "                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level1_row0\" class=\"row_heading level1 row0\" rowspan=3>FFR First Feed Entry Time</th>\n", "                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row0\" class=\"row_heading level2 row0\" >50</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row0_col0\" class=\"data row0 col0\" >0.00% (7.54e+02→7.54e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row1\" class=\"row_heading level2 row1\" >90</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row1_col0\" class=\"data row1 col0\" >0.10% (9.25e+03→9.26e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row2\" class=\"row_heading level2 row2\" >99</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row2_col0\" class=\"data row2 col0\" >-0.23% (1.18e+05→1.17e+05, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level1_row3\" class=\"row_heading level1 row3\" rowspan=3>FFR First Feed Entry Time Cold</th>\n", "                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row3\" class=\"row_heading level2 row3\" >50</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row3_col0\" class=\"data row3 col0\" >0.00% (8.94e+02→8.94e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row4\" class=\"row_heading level2 row4\" >90</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row4_col0\" class=\"data row4 col0\" >0.12% (1.02e+04→1.02e+04, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row5\" class=\"row_heading level2 row5\" >99</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row5_col0\" class=\"data row5 col0\" >-0.19% (1.17e+05→1.16e+05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level1_row6\" class=\"row_heading level1 row6\" rowspan=3>FFR First Feed Entry Time Warm</th>\n", "                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row6\" class=\"row_heading level2 row6\" >50</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row6_col0\" class=\"data row6 col0\" >0.14% (7.25e+02→7.26e+02, < 0.01)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row7\" class=\"row_heading level2 row7\" >90</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row7_col0\" class=\"data row7 col0\" >0.15% (8.95e+03→8.96e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row8\" class=\"row_heading level2 row8\" >99</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row8_col0\" class=\"data row8 col0\" >-0.21% (1.18e+05→1.18e+05, < 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level1_row9\" class=\"row_heading level1 row9\" rowspan=3>FFR First Feed Entry Render Time</th>\n", "                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row9\" class=\"row_heading level2 row9\" >50</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row9_col0\" class=\"data row9 col0\" >0.00% (42.0→42.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row10\" class=\"row_heading level2 row10\" >90</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row10_col0\" class=\"data row10 col0\" >0.00% (1.98e+02→1.98e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row11\" class=\"row_heading level2 row11\" >99</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row11_col0\" class=\"data row11 col0\" >0.22% (8.95e+02→8.97e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level1_row12\" class=\"row_heading level1 row12\" rowspan=3>FFR First Feed Entry Render Time Cold</th>\n", "                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row12\" class=\"row_heading level2 row12\" >50</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row12_col0\" class=\"data row12 col0\" >0.00% (73.0→73.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row13\" class=\"row_heading level2 row13\" >90</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row13_col0\" class=\"data row13 col0\" >0.00% (3.1e+02→3.1e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row14\" class=\"row_heading level2 row14\" >99</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row14_col0\" class=\"data row14 col0\" >0.46% (1.3e+03→1.31e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level1_row15\" class=\"row_heading level1 row15\" rowspan=3>FFR First Feed Entry Render Time Warm</th>\n", "                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row15\" class=\"row_heading level2 row15\" >50</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row15_col0\" class=\"data row15 col0\" >0.00% (34.0→34.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row16\" class=\"row_heading level2 row16\" >90</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row16_col0\" class=\"data row16 col0\" >0.00% (1.6e+02→1.6e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row17\" class=\"row_heading level2 row17\" >99</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row17_col0\" class=\"data row17 col0\" >0.00% (7.5e+02→7.5e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level1_row18\" class=\"row_heading level1 row18\" rowspan=3>FFR Sync Render Time</th>\n", "                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row18\" class=\"row_heading level2 row18\" >50</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row18_col0\" class=\"data row18 col0\" >0.00% (31.0→31.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row19\" class=\"row_heading level2 row19\" >90</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row19_col0\" class=\"data row19 col0\" >-0.53% (1.89e+02→1.88e+02, < 0.0001)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row20\" class=\"row_heading level2 row20\" >99</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row20_col0\" class=\"data row20 col0\" >-0.58% (1.39e+03→1.38e+03, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level1_row21\" class=\"row_heading level1 row21\" rowspan=3>FFR Sync Render Time Cold</th>\n", "                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row21\" class=\"row_heading level2 row21\" >50</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row21_col0\" class=\"data row21 col0\" >0.00% (59.0→59.0, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row22\" class=\"row_heading level2 row22\" >90</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row22_col0\" class=\"data row22 col0\" >0.00% (2.78e+02→2.78e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row23\" class=\"row_heading level2 row23\" >99</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row23_col0\" class=\"data row23 col0\" >0.17% (1.78e+03→1.78e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level1_row24\" class=\"row_heading level1 row24\" rowspan=3>FFR Sync Render Time Warm</th>\n", "                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row24\" class=\"row_heading level2 row24\" >50</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row24_col0\" class=\"data row24 col0\" >0.00% (24.0→24.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row25\" class=\"row_heading level2 row25\" >90</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row25_col0\" class=\"data row25 col0\" >-0.67% (1.5e+02→1.49e+02, < 0.0001)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row26\" class=\"row_heading level2 row26\" >99</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row26_col0\" class=\"data row26 col0\" >-0.99% (1.22e+03→1.2e+03, < 0.001)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level1_row27\" class=\"row_heading level1 row27\" rowspan=3>FFR Feed Entry Time after Sync</th>\n", "                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row27\" class=\"row_heading level2 row27\" >50</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row27_col0\" class=\"data row27 col0\" >0.12% (8.05e+02→8.06e+02, < 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row28\" class=\"row_heading level2 row28\" >90</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row28_col0\" class=\"data row28 col0\" >0.35% (1.06e+04→1.06e+04, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row29\" class=\"row_heading level2 row29\" >99</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row29_col0\" class=\"data row29 col0\" >-0.20% (1.14e+05→1.13e+05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level1_row30\" class=\"row_heading level1 row30\" rowspan=3>FFR Feed Entry Time after Sync Cold</th>\n", "                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row30\" class=\"row_heading level2 row30\" >50</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row30_col0\" class=\"data row30 col0\" >0.00% (9.6e+02→9.6e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row31\" class=\"row_heading level2 row31\" >90</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row31_col0\" class=\"data row31 col0\" >0.13% (1.22e+04→1.22e+04, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row32\" class=\"row_heading level2 row32\" >99</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row32_col0\" class=\"data row32 col0\" >-0.23% (1.16e+05→1.16e+05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level1_row33\" class=\"row_heading level1 row33\" rowspan=3>FFR Feed Entry Time after Sync Warm</th>\n", "                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row33\" class=\"row_heading level2 row33\" >50</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row33_col0\" class=\"data row33 col0\" >0.00% (7.63e+02→7.63e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row34\" class=\"row_heading level2 row34\" >90</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row34_col0\" class=\"data row34 col0\" >0.43% (9.96e+03→1e+04, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row35\" class=\"row_heading level2 row35\" >99</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row35_col0\" class=\"data row35 col0\" >-0.25% (1.13e+05→1.12e+05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level1_row36\" class=\"row_heading level1 row36\" rowspan=3>FFR Sync Latency</th>\n", "                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row36\" class=\"row_heading level2 row36\" >50</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row36_col0\" class=\"data row36 col0\" >0.00% (5.36e+02→5.36e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row37\" class=\"row_heading level2 row37\" >90</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row37_col0\" class=\"data row37 col0\" >-0.19% (2.11e+03→2.11e+03, < 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row38\" class=\"row_heading level2 row38\" >99</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row38_col0\" class=\"data row38 col0\" >-0.06% (8.12e+03→8.11e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level1_row39\" class=\"row_heading level1 row39\" rowspan=3>FFR Sync Latency Cold</th>\n", "                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row39\" class=\"row_heading level2 row39\" >50</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row39_col0\" class=\"data row39 col0\" >0.00% (7.59e+02→7.59e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row40\" class=\"row_heading level2 row40\" >90</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row40_col0\" class=\"data row40 col0\" >0.10% (3.15e+03→3.15e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row41\" class=\"row_heading level2 row41\" >99</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row41_col0\" class=\"data row41 col0\" >0.07% (1.16e+04→1.16e+04, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level1_row42\" class=\"row_heading level1 row42\" rowspan=3>FFR Sync Latency Warm</th>\n", "                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row42\" class=\"row_heading level2 row42\" >50</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row42_col0\" class=\"data row42 col0\" >0.00% (4.76e+02→4.76e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row43\" class=\"row_heading level2 row43\" >90</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row43_col0\" class=\"data row43 col0\" >-0.22% (1.81e+03→1.8e+03, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row44\" class=\"row_heading level2 row44\" >99</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row44_col0\" class=\"data row44 col0\" >-0.15% (6.69e+03→6.68e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level1_row45\" class=\"row_heading level1 row45\" rowspan=3>Friend Feed Entry to Sync Latency in Stale Feed Scenario (i.e. time spent waiting on feed for sync to complete)</th>\n", "                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row45\" class=\"row_heading level2 row45\" >50</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row45_col0\" class=\"data row45 col0\" >0.00% (6.25e+02→6.25e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row46\" class=\"row_heading level2 row46\" >90</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row46_col0\" class=\"data row46 col0\" >-0.18% (2.71e+03→2.7e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row47\" class=\"row_heading level2 row47\" >99</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row47_col0\" class=\"data row47 col0\" >-0.18% (1.35e+04→1.34e+04, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level1_row48\" class=\"row_heading level1 row48\" rowspan=3>Friend Feed Entry to Sync Latency Cold in Stale Feed Scenario</th>\n", "                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row48\" class=\"row_heading level2 row48\" >50</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row48_col0\" class=\"data row48 col0\" >-0.14% (7.07e+02→7.06e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row49\" class=\"row_heading level2 row49\" >90</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row49_col0\" class=\"data row49 col0\" >0.06% (3.6e+03→3.6e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row50\" class=\"row_heading level2 row50\" >99</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row50_col0\" class=\"data row50 col0\" >0.20% (1.72e+04→1.72e+04, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level1_row51\" class=\"row_heading level1 row51\" rowspan=3>Friend Feed Entry to Sync Latency Warm in Stale Feed Scenario</th>\n", "                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row51\" class=\"row_heading level2 row51\" >50</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row51_col0\" class=\"data row51 col0\" >0.00% (6.04e+02→6.04e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row52\" class=\"row_heading level2 row52\" >90</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row52_col0\" class=\"data row52 col0\" >-0.25% (2.41e+03→2.40e+03, < 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_07618704_6ccb_11eb_9c43_32e124840315level2_row53\" class=\"row_heading level2 row53\" >99</th>\n", "                        <td id=\"T_07618704_6ccb_11eb_9c43_32e124840315row53_col0\" class=\"data row53 col0\" >-0.56% (1.19e+04→1.19e+04, < 0.05)</td>\n", "            </tr>\n", "    </tbody></table></p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h2>G2F Success Latency</h2>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5>Overall</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5>Comparison of user overall (cumulative) metric values during the study period</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p><style  type=\"text/css\" >\n", "    #T_078c449e_6ccb_11eb_9c43_32e124840315row0_col0 {\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #99cc99;\n", "        }</style><table id=\"T_078c449e_6ccb_11eb_9c43_32e124840315\" ><thead>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank\" ></th>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Statistic</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank\" ></th>        <th class=\"index_name level1\" >control_id</th>        <th class=\"col_heading level1 col0\" >100 (Control)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank\" ></th>        <th class=\"index_name level2\" >treatment_id</th>        <th class=\"col_heading level2 col0\" >98 (ArroyoDuplex)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank\" ></th>        <th class=\"index_name level3\" >Sample Sizes</th>        <th class=\"col_heading level3 col0\" >Treatment=6,639,404<br>\n", "Control=6,643,389<br>\n", "No SSPM (p=0.2742)</th>    </tr>    <tr>        <th class=\"index_name level0\" >Date</th>        <th class=\"index_name level1\" >Metric</th>        <th class=\"index_name level2\" >quantile</th>        <th class=\"blank\" ></th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_078c449e_6ccb_11eb_9c43_32e124840315level0_row0\" class=\"row_heading level0 row0\" rowspan=12>01/20 to 01/20</th>\n", "                        <th id=\"T_078c449e_6ccb_11eb_9c43_32e124840315level1_row0\" class=\"row_heading level1 row0\" rowspan=3>G2FF Success Latency</th>\n", "                        <th id=\"T_078c449e_6ccb_11eb_9c43_32e124840315level2_row0\" class=\"row_heading level2 row0\" >50</th>\n", "                        <td id=\"T_078c449e_6ccb_11eb_9c43_32e124840315row0_col0\" class=\"data row0 col0\" >-0.15% (6.49e+02→6.48e+02, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_078c449e_6ccb_11eb_9c43_32e124840315level2_row1\" class=\"row_heading level2 row1\" >90</th>\n", "                        <td id=\"T_078c449e_6ccb_11eb_9c43_32e124840315row1_col0\" class=\"data row1 col0\" >-0.13% (2.36e+03→2.36e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_078c449e_6ccb_11eb_9c43_32e124840315level2_row2\" class=\"row_heading level2 row2\" >99</th>\n", "                        <td id=\"T_078c449e_6ccb_11eb_9c43_32e124840315row2_col0\" class=\"data row2 col0\" >0.00% (8.85e+03→8.85e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_078c449e_6ccb_11eb_9c43_32e124840315level1_row3\" class=\"row_heading level1 row3\" rowspan=3>G2FF Cold Success Latency</th>\n", "                        <th id=\"T_078c449e_6ccb_11eb_9c43_32e124840315level2_row3\" class=\"row_heading level2 row3\" >50</th>\n", "                        <td id=\"T_078c449e_6ccb_11eb_9c43_32e124840315row3_col0\" class=\"data row3 col0\" >0.00% (1.01e+03→1.01e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_078c449e_6ccb_11eb_9c43_32e124840315level2_row4\" class=\"row_heading level2 row4\" >90</th>\n", "                        <td id=\"T_078c449e_6ccb_11eb_9c43_32e124840315row4_col0\" class=\"data row4 col0\" >0.05% (3.67e+03→3.68e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_078c449e_6ccb_11eb_9c43_32e124840315level2_row5\" class=\"row_heading level2 row5\" >99</th>\n", "                        <td id=\"T_078c449e_6ccb_11eb_9c43_32e124840315row5_col0\" class=\"data row5 col0\" >0.02% (1.31e+04→1.31e+04, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_078c449e_6ccb_11eb_9c43_32e124840315level1_row6\" class=\"row_heading level1 row6\" rowspan=3>G2FF Warm Success Latency</th>\n", "                        <th id=\"T_078c449e_6ccb_11eb_9c43_32e124840315level2_row6\" class=\"row_heading level2 row6\" >50</th>\n", "                        <td id=\"T_078c449e_6ccb_11eb_9c43_32e124840315row6_col0\" class=\"data row6 col0\" >0.00% (7.42e+02→7.42e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_078c449e_6ccb_11eb_9c43_32e124840315level2_row7\" class=\"row_heading level2 row7\" >90</th>\n", "                        <td id=\"T_078c449e_6ccb_11eb_9c43_32e124840315row7_col0\" class=\"data row7 col0\" >0.00% (3.13e+03→3.13e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_078c449e_6ccb_11eb_9c43_32e124840315level2_row8\" class=\"row_heading level2 row8\" >99</th>\n", "                        <td id=\"T_078c449e_6ccb_11eb_9c43_32e124840315row8_col0\" class=\"data row8 col0\" >0.59% (1.24e+04→1.25e+04, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_078c449e_6ccb_11eb_9c43_32e124840315level1_row9\" class=\"row_heading level1 row9\" rowspan=3>G2FF Hot Success Latency</th>\n", "                        <th id=\"T_078c449e_6ccb_11eb_9c43_32e124840315level2_row9\" class=\"row_heading level2 row9\" >50</th>\n", "                        <td id=\"T_078c449e_6ccb_11eb_9c43_32e124840315row9_col0\" class=\"data row9 col0\" >0.00% (5.4e+02→5.4e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_078c449e_6ccb_11eb_9c43_32e124840315level2_row10\" class=\"row_heading level2 row10\" >90</th>\n", "                        <td id=\"T_078c449e_6ccb_11eb_9c43_32e124840315row10_col0\" class=\"data row10 col0\" >-0.21% (1.94e+03→1.93e+03, < 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_078c449e_6ccb_11eb_9c43_32e124840315level2_row11\" class=\"row_heading level2 row11\" >99</th>\n", "                        <td id=\"T_078c449e_6ccb_11eb_9c43_32e124840315row11_col0\" class=\"data row11 col0\" >-0.22% (6.92e+03→6.9e+03, < 0.1)</td>\n", "            </tr>\n", "    </tbody></table></p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h2>G2F Sub Latencies</h2>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5>Overall</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5>Comparison of user overall (cumulative) metric values during the study period</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p><style  type=\"text/css\" >\n", "    #T_086f3f74_6ccb_11eb_9c43_32e124840315row0_col0 {\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #66b366;\n", "        }    #T_086f3f74_6ccb_11eb_9c43_32e124840315row13_col0 {\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #99cc99;\n", "        }    #T_086f3f74_6ccb_11eb_9c43_32e124840315row22_col0 {\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #99cc99;\n", "        }    #T_086f3f74_6ccb_11eb_9c43_32e124840315row23_col0 {\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #99cc99;\n", "        }    #T_086f3f74_6ccb_11eb_9c43_32e124840315row37_col0 {\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #f89999;\n", "        }    #T_086f3f74_6ccb_11eb_9c43_32e124840315row49_col0 {\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #f89999;\n", "        }    #T_086f3f74_6ccb_11eb_9c43_32e124840315row65_col0 {\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #f56666;\n", "        }    #T_086f3f74_6ccb_11eb_9c43_32e124840315row94_col0 {\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #ff0000;\n", "        }    #T_086f3f74_6ccb_11eb_9c43_32e124840315row101_col0 {\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #f89999;\n", "        }    #T_086f3f74_6ccb_11eb_9c43_32e124840315row122_col0 {\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #66b366;\n", "        }</style><table id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315\" ><thead>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank\" ></th>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Statistic</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank\" ></th>        <th class=\"index_name level1\" >control_id</th>        <th class=\"col_heading level1 col0\" >100 (Control)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank\" ></th>        <th class=\"index_name level2\" >treatment_id</th>        <th class=\"col_heading level2 col0\" >98 (ArroyoDuplex)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank\" ></th>        <th class=\"index_name level3\" >Sample Sizes</th>        <th class=\"col_heading level3 col0\" >Treatment=6,639,404<br>\n", "Control=6,643,389<br>\n", "No SSPM (p=0.2742)</th>    </tr>    <tr>        <th class=\"index_name level0\" >Date</th>        <th class=\"index_name level1\" >Metric</th>        <th class=\"index_name level2\" >quantile</th>        <th class=\"blank\" ></th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level0_row0\" class=\"row_heading level0 row0\" rowspan=150>01/20 to 01/20</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row0\" class=\"row_heading level1 row0\" rowspan=3>G2FF Begin Recording Cold</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row0\" class=\"row_heading level2 row0\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row0_col0\" class=\"data row0 col0\" >-0.35% (2.86e+02→2.85e+02, < 0.01)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row1\" class=\"row_heading level2 row1\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row1_col0\" class=\"data row1 col0\" >-0.34% (8.73e+02→8.7e+02, < 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row2\" class=\"row_heading level2 row2\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row2_col0\" class=\"data row2 col0\" >-0.13% (2.98e+03→2.98e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row3\" class=\"row_heading level1 row3\" rowspan=3>G2FF Begin Recording Warm</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row3\" class=\"row_heading level2 row3\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row3_col0\" class=\"data row3 col0\" >-0.99% (2.03e+02→2.01e+02, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row4\" class=\"row_heading level2 row4\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row4_col0\" class=\"data row4 col0\" >-1.29% (7.73e+02→7.63e+02, < 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row5\" class=\"row_heading level2 row5\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row5_col0\" class=\"data row5 col0\" >0.54% (4.09e+03→4.11e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row6\" class=\"row_heading level1 row6\" rowspan=3>G2FF Begin Recording Hot</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row6\" class=\"row_heading level2 row6\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row6_col0\" class=\"data row6 col0\" >-0.18% (5.58e+02→5.57e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row7\" class=\"row_heading level2 row7\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row7_col0\" class=\"data row7 col0\" >-0.23% (1.28e+03→1.28e+03, < 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row8\" class=\"row_heading level2 row8\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row8_col0\" class=\"data row8 col0\" >-0.21% (2.88e+03→2.87e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row9\" class=\"row_heading level1 row9\" rowspan=3>G2FF Wait Till FF Cold</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row9\" class=\"row_heading level2 row9\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row9_col0\" class=\"data row9 col0\" >0.00% (3.0→3.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row10\" class=\"row_heading level2 row10\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row10_col0\" class=\"data row10 col0\" >0.00% (17.0→17.0, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row11\" class=\"row_heading level2 row11\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row11_col0\" class=\"data row11 col0\" >0.00% (68.0→68.0, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row12\" class=\"row_heading level1 row12\" rowspan=3>G2FF Wait Till FF Warm</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row12\" class=\"row_heading level2 row12\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row12_col0\" class=\"data row12 col0\" >0.00% (1.0→1.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row13\" class=\"row_heading level2 row13\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row13_col0\" class=\"data row13 col0\" >-7.69% (13.0→12.0, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row14\" class=\"row_heading level2 row14\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row14_col0\" class=\"data row14 col0\" >-5.00% (1.2e+02→1.14e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row15\" class=\"row_heading level1 row15\" rowspan=3>G2FF Wait Till FF Hot</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row15\" class=\"row_heading level2 row15\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row15_col0\" class=\"data row15 col0\" >0.00% (1.0→1.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row16\" class=\"row_heading level2 row16\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row16_col0\" class=\"data row16 col0\" >0.00% (14.0→14.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row17\" class=\"row_heading level2 row17\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row17_col0\" class=\"data row17 col0\" >0.00% (93.0→93.0, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row18\" class=\"row_heading level1 row18\" rowspan=3>G2FF Wait Till Sync Feed Cold</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row18\" class=\"row_heading level2 row18\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row18_col0\" class=\"data row18 col0\" >0.00% (2.68e+02→2.68e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row19\" class=\"row_heading level2 row19\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row19_col0\" class=\"data row19 col0\" >0.14% (7.34e+02→7.35e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row20\" class=\"row_heading level2 row20\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row20_col0\" class=\"data row20 col0\" >0.00% (2.8e+03→2.8e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row21\" class=\"row_heading level1 row21\" rowspan=3>G2FF Wait Till Sync Feed Warm</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row21\" class=\"row_heading level2 row21\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row21_col0\" class=\"data row21 col0\" >0.00% (30.0→30.0, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row22\" class=\"row_heading level2 row22\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row22_col0\" class=\"data row22 col0\" >-0.70% (2.87e+02→2.85e+02, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row23\" class=\"row_heading level2 row23\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row23_col0\" class=\"data row23 col0\" >-1.45% (2.14e+03→2.11e+03, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row24\" class=\"row_heading level1 row24\" rowspan=3>G2FF Wait Till Sync Feed Hot</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row24\" class=\"row_heading level2 row24\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row24_col0\" class=\"data row24 col0\" >0.00% (7.0→7.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row25\" class=\"row_heading level2 row25\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row25_col0\" class=\"data row25 col0\" >0.00% (95.0→95.0, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row26\" class=\"row_heading level2 row26\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row26_col0\" class=\"data row26 col0\" >0.00% (3.38e+02→3.38e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row27\" class=\"row_heading level1 row27\" rowspan=3>G2FF Sync Feed Cold</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row27\" class=\"row_heading level2 row27\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row27_col0\" class=\"data row27 col0\" >0.00% (4.38e+02→4.38e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row28\" class=\"row_heading level2 row28\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row28_col0\" class=\"data row28 col0\" >-0.06% (1.67e+03→1.67e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row29\" class=\"row_heading level2 row29\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row29_col0\" class=\"data row29 col0\" >-0.19% (1.03e+04→1.02e+04, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row30\" class=\"row_heading level1 row30\" rowspan=3>G2FF Sync Feed Warm</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row30\" class=\"row_heading level2 row30\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row30_col0\" class=\"data row30 col0\" >0.00% (4.17e+02→4.17e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row31\" class=\"row_heading level2 row31\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row31_col0\" class=\"data row31 col0\" >-0.12% (1.70e+03→1.7e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row32\" class=\"row_heading level2 row32\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row32_col0\" class=\"data row32 col0\" >0.98% (9.92e+03→1e+04, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row33\" class=\"row_heading level1 row33\" rowspan=3>G2FF Sync Feed Hot</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row33\" class=\"row_heading level2 row33\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row33_col0\" class=\"data row33 col0\" >0.00% (3.25e+02→3.25e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row34\" class=\"row_heading level2 row34\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row34_col0\" class=\"data row34 col0\" >-0.08% (1.26e+03→1.26e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row35\" class=\"row_heading level2 row35\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row35_col0\" class=\"data row35 col0\" >-0.28% (5.66e+03→5.65e+03, < 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row36\" class=\"row_heading level1 row36\" rowspan=3>G2FF Process Sync Feed Response Cold</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row36\" class=\"row_heading level2 row36\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row36_col0\" class=\"data row36 col0\" >0.00% (6.0→6.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row37\" class=\"row_heading level2 row37\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row37_col0\" class=\"data row37 col0\" >0.62% (1.61e+02→1.62e+02, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row38\" class=\"row_heading level2 row38\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row38_col0\" class=\"data row38 col0\" >0.09% (1.08e+03→1.09e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row39\" class=\"row_heading level1 row39\" rowspan=3>G2FF Process Sync Feed Response Warm</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row39\" class=\"row_heading level2 row39\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row39_col0\" class=\"data row39 col0\" >0.00% (7.0→7.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row40\" class=\"row_heading level2 row40\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row40_col0\" class=\"data row40 col0\" >-0.67% (1.5e+02→1.49e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row41\" class=\"row_heading level2 row41\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row41_col0\" class=\"data row41 col0\" >-0.78% (1.16e+03→1.15e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row42\" class=\"row_heading level1 row42\" rowspan=3>G2FF Process Sync Feed Response Hot</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row42\" class=\"row_heading level2 row42\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row42_col0\" class=\"data row42 col0\" >0.00% (2.0→2.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row43\" class=\"row_heading level2 row43\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row43_col0\" class=\"data row43 col0\" >0.00% (30.0→30.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row44\" class=\"row_heading level2 row44\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row44_col0\" class=\"data row44 col0\" >0.00% (4.05e+02→4.05e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row45\" class=\"row_heading level1 row45\" rowspan=3>G2FF Propogate Change to UI Cold</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row45\" class=\"row_heading level2 row45\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row45_col0\" class=\"data row45 col0\" >0.00% (11.0→11.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row46\" class=\"row_heading level2 row46\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row46_col0\" class=\"data row46 col0\" >0.00% (2.22e+02→2.22e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row47\" class=\"row_heading level2 row47\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row47_col0\" class=\"data row47 col0\" >-0.17% (1.21e+03→1.20e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row48\" class=\"row_heading level1 row48\" rowspan=3>G2FF Propogate Change to UI Warm</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row48\" class=\"row_heading level2 row48\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row48_col0\" class=\"data row48 col0\" >0.00% (4.0→4.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row49\" class=\"row_heading level2 row49\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row49_col0\" class=\"data row49 col0\" >1.17% (2.57e+02→2.6e+02, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row50\" class=\"row_heading level2 row50\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row50_col0\" class=\"data row50 col0\" >0.90% (1.44e+03→1.46e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row51\" class=\"row_heading level1 row51\" rowspan=3>G2FF Propogate Change to UI Hot</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row51\" class=\"row_heading level2 row51\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row51_col0\" class=\"data row51 col0\" >0.00% (3.0→3.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row52\" class=\"row_heading level2 row52\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row52_col0\" class=\"data row52 col0\" >0.00% (69.0→69.0, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row53\" class=\"row_heading level2 row53\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row53_col0\" class=\"data row53 col0\" >0.00% (6.38e+02→6.38e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row54\" class=\"row_heading level1 row54\" rowspan=3>G2FF Batch Conversation Fetch Cold</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row54\" class=\"row_heading level2 row54\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row54_col0\" class=\"data row54 col0\" >nan% (0.0→0.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row55\" class=\"row_heading level2 row55\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row55_col0\" class=\"data row55 col0\" >0.00% (7.0→7.0, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row56\" class=\"row_heading level2 row56\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row56_col0\" class=\"data row56 col0\" >0.00% (6.14e+02→6.14e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row57\" class=\"row_heading level1 row57\" rowspan=3>G2FF Batch Conversation Fetch Warm</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row57\" class=\"row_heading level2 row57\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row57_col0\" class=\"data row57 col0\" >nan% (0.0→0.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row58\" class=\"row_heading level2 row58\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row58_col0\" class=\"data row58 col0\" >0.00% (1.0→1.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row59\" class=\"row_heading level2 row59\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row59_col0\" class=\"data row59 col0\" >1.20% (5.83e+02→5.9e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row60\" class=\"row_heading level1 row60\" rowspan=3>G2FF Batch Conversation Fetch Hot</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row60\" class=\"row_heading level2 row60\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row60_col0\" class=\"data row60 col0\" >nan% (0.0→0.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row61\" class=\"row_heading level2 row61\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row61_col0\" class=\"data row61 col0\" >0.00% (1.0→1.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row62\" class=\"row_heading level2 row62\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row62_col0\" class=\"data row62 col0\" >0.00% (3.68e+02→3.68e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row63\" class=\"row_heading level1 row63\" rowspan=3>G2FF Process Individual Sources Cold</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row63\" class=\"row_heading level2 row63\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row63_col0\" class=\"data row63 col0\" >0.00% (1.0→1.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row64\" class=\"row_heading level2 row64\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row64_col0\" class=\"data row64 col0\" >0.00% (61.0→61.0, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row65\" class=\"row_heading level2 row65\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row65_col0\" class=\"data row65 col0\" >1.55% (9.06e+02→9.2e+02, < 0.01)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row66\" class=\"row_heading level1 row66\" rowspan=3>G2FF Process Individual Sources Warm</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row66\" class=\"row_heading level2 row66\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row66_col0\" class=\"data row66 col0\" >0.00% (1.0→1.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row67\" class=\"row_heading level2 row67\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row67_col0\" class=\"data row67 col0\" >0.00% (50.0→50.0, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row68\" class=\"row_heading level2 row68\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row68_col0\" class=\"data row68 col0\" >0.00% (6.19e+02→6.19e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row69\" class=\"row_heading level1 row69\" rowspan=3>G2FF Process Individual Sources Hot</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row69\" class=\"row_heading level2 row69\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row69_col0\" class=\"data row69 col0\" >nan% (0.0→0.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row70\" class=\"row_heading level2 row70\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row70_col0\" class=\"data row70 col0\" >0.00% (6.0→6.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row71\" class=\"row_heading level2 row71\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row71_col0\" class=\"data row71 col0\" >0.00% (1.35e+02→1.35e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row72\" class=\"row_heading level1 row72\" rowspan=3>G2FF Process Feed Items Cold</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row72\" class=\"row_heading level2 row72\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row72_col0\" class=\"data row72 col0\" >0.00% (16.0→16.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row73\" class=\"row_heading level2 row73\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row73_col0\" class=\"data row73 col0\" >0.00% (2.86e+02→2.86e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row74\" class=\"row_heading level2 row74\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row74_col0\" class=\"data row74 col0\" >0.80% (1.88e+03→1.9e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row75\" class=\"row_heading level1 row75\" rowspan=3>G2FF Process Feed Items Warm</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row75\" class=\"row_heading level2 row75\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row75_col0\" class=\"data row75 col0\" >0.00% (16.0→16.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row76\" class=\"row_heading level2 row76\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row76_col0\" class=\"data row76 col0\" >0.47% (2.13e+02→2.14e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row77\" class=\"row_heading level2 row77\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row77_col0\" class=\"data row77 col0\" >0.00% (1.81e+03→1.81e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row78\" class=\"row_heading level1 row78\" rowspan=3>G2FF Process Feed Items Hot</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row78\" class=\"row_heading level2 row78\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row78_col0\" class=\"data row78 col0\" >0.00% (17.0→17.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row79\" class=\"row_heading level2 row79\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row79_col0\" class=\"data row79 col0\" >0.00% (1.4e+02→1.4e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row80\" class=\"row_heading level2 row80\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row80_col0\" class=\"data row80 col0\" >0.23% (8.74e+02→8.76e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row81\" class=\"row_heading level1 row81\" rowspan=3>G2FF Ranking Cold</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row81\" class=\"row_heading level2 row81\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row81_col0\" class=\"data row81 col0\" >nan% (0.0→0.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row82\" class=\"row_heading level2 row82\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row82_col0\" class=\"data row82 col0\" >0.00% (3.0→3.0, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row83\" class=\"row_heading level2 row83\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row83_col0\" class=\"data row83 col0\" >0.00% (11.0→11.0, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row84\" class=\"row_heading level1 row84\" rowspan=3>G2FF Ranking Warm</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row84\" class=\"row_heading level2 row84\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row84_col0\" class=\"data row84 col0\" >nan% (0.0→0.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row85\" class=\"row_heading level2 row85\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row85_col0\" class=\"data row85 col0\" >0.00% (2.0→2.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row86\" class=\"row_heading level2 row86\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row86_col0\" class=\"data row86 col0\" >0.00% (15.0→15.0, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row87\" class=\"row_heading level1 row87\" rowspan=3>G2FF Ranking Hot</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row87\" class=\"row_heading level2 row87\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row87_col0\" class=\"data row87 col0\" >nan% (0.0→0.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row88\" class=\"row_heading level2 row88\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row88_col0\" class=\"data row88 col0\" >0.00% (3.0→3.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row89\" class=\"row_heading level2 row89\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row89_col0\" class=\"data row89 col0\" >0.00% (13.0→13.0, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row90\" class=\"row_heading level1 row90\" rowspan=3>G2FF Reload Table Warm</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row90\" class=\"row_heading level2 row90\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row90_col0\" class=\"data row90 col0\" >0.00% (31.0→31.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row91\" class=\"row_heading level2 row91\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row91_col0\" class=\"data row91 col0\" >-0.48% (2.07e+02→2.06e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row92\" class=\"row_heading level2 row92\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row92_col0\" class=\"data row92 col0\" >-0.67% (7.47e+02→7.42e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row93\" class=\"row_heading level1 row93\" rowspan=3>G2FF Reload Table Hot</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row93\" class=\"row_heading level2 row93\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row93_col0\" class=\"data row93 col0\" >0.00% (25.0→25.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row94\" class=\"row_heading level2 row94\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row94_col0\" class=\"data row94 col0\" >0.83% (1.2e+02→1.21e+02, < 0.0001)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row95\" class=\"row_heading level2 row95\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row95_col0\" class=\"data row95 col0\" >0.00% (3.97e+02→3.97e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row96\" class=\"row_heading level1 row96\" rowspan=3>G2FF Sync Arroyo Feed Cold</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row96\" class=\"row_heading level2 row96\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row96_col0\" class=\"data row96 col0\" >0.00% (5.33e+02→5.33e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row97\" class=\"row_heading level2 row97\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row97_col0\" class=\"data row97 col0\" >0.00% (1.77e+03→1.77e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row98\" class=\"row_heading level2 row98\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row98_col0\" class=\"data row98 col0\" >-0.14% (8.65e+03→8.64e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row99\" class=\"row_heading level1 row99\" rowspan=3>G2FF Sync Arroyo Feed Warm</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row99\" class=\"row_heading level2 row99\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row99_col0\" class=\"data row99 col0\" >0.00% (5.49e+02→5.49e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row100\" class=\"row_heading level2 row100\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row100_col0\" class=\"data row100 col0\" >0.00% (1.98e+03→1.98e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row101\" class=\"row_heading level2 row101\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row101_col0\" class=\"data row101 col0\" >1.25% (9.12e+03→9.24e+03, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row102\" class=\"row_heading level1 row102\" rowspan=3>G2FF Sync Arroyo Feed Hot</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row102\" class=\"row_heading level2 row102\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row102_col0\" class=\"data row102 col0\" >0.00% (3.89e+02→3.89e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row103\" class=\"row_heading level2 row103\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row103_col0\" class=\"data row103 col0\" >-0.08% (1.32e+03→1.32e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row104\" class=\"row_heading level2 row104\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row104_col0\" class=\"data row104 col0\" >-0.23% (5.53e+03→5.52e+03, < 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row105\" class=\"row_heading level1 row105\" rowspan=3>G2FF Process Arroyo Sync Feed Response Cold</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row105\" class=\"row_heading level2 row105\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row105_col0\" class=\"data row105 col0\" >0.00% (5.0→5.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row106\" class=\"row_heading level2 row106\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row106_col0\" class=\"data row106 col0\" >0.00% (1.36e+02→1.36e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row107\" class=\"row_heading level2 row107\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row107_col0\" class=\"data row107 col0\" >0.00% (1.03e+03→1.03e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row108\" class=\"row_heading level1 row108\" rowspan=3>G2FF Process Arroyo Sync Feed Response Warm</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row108\" class=\"row_heading level2 row108\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row108_col0\" class=\"data row108 col0\" >0.00% (5.0→5.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row109\" class=\"row_heading level2 row109\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row109_col0\" class=\"data row109 col0\" >-1.02% (98.0→97.0, < 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row110\" class=\"row_heading level2 row110\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row110_col0\" class=\"data row110 col0\" >-0.95% (8.46e+02→8.38e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row111\" class=\"row_heading level1 row111\" rowspan=3>G2FF Process Arroyo Sync Feed Response Hot</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row111\" class=\"row_heading level2 row111\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row111_col0\" class=\"data row111 col0\" >0.00% (2.0→2.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row112\" class=\"row_heading level2 row112\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row112_col0\" class=\"data row112 col0\" >0.00% (30.0→30.0, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row113\" class=\"row_heading level2 row113\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row113_col0\" class=\"data row113 col0\" >-0.26% (3.91e+02→3.9e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row114\" class=\"row_heading level1 row114\" rowspan=3>G2FF Process Arroyo Sources Cold</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row114\" class=\"row_heading level2 row114\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row114_col0\" class=\"data row114 col0\" >nan% (0.0→0.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row115\" class=\"row_heading level2 row115\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row115_col0\" class=\"data row115 col0\" >0.00% (1.3e+02→1.3e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row116\" class=\"row_heading level2 row116\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row116_col0\" class=\"data row116 col0\" >-0.08% (1.32e+03→1.32e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row117\" class=\"row_heading level1 row117\" rowspan=3>G2FF Process Arroyo Sources Warm</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row117\" class=\"row_heading level2 row117\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row117_col0\" class=\"data row117 col0\" >nan% (0.0→0.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row118\" class=\"row_heading level2 row118\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row118_col0\" class=\"data row118 col0\" >0.00% (31.0→31.0, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row119\" class=\"row_heading level2 row119\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row119_col0\" class=\"data row119 col0\" >0.15% (6.81e+02→6.82e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row120\" class=\"row_heading level1 row120\" rowspan=3>G2FF Process Arroyo Sources Hot</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row120\" class=\"row_heading level2 row120\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row120_col0\" class=\"data row120 col0\" >nan% (0.0→0.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row121\" class=\"row_heading level2 row121\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row121_col0\" class=\"data row121 col0\" >0.00% (14.0→14.0, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row122\" class=\"row_heading level2 row122\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row122_col0\" class=\"data row122 col0\" >-1.13% (2.66e+02→2.63e+02, < 0.01)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row123\" class=\"row_heading level1 row123\" rowspan=3>G2FF Process Arroyo Feed Items Cold</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row123\" class=\"row_heading level2 row123\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row123_col0\" class=\"data row123 col0\" >0.00% (18.0→18.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row124\" class=\"row_heading level2 row124\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row124_col0\" class=\"data row124 col0\" >0.32% (3.09e+02→3.1e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row125\" class=\"row_heading level2 row125\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row125_col0\" class=\"data row125 col0\" >0.77% (2.2e+03→2.22e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row126\" class=\"row_heading level1 row126\" rowspan=3>G2FF Process Arroyo Feed Items Warm</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row126\" class=\"row_heading level2 row126\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row126_col0\" class=\"data row126 col0\" >0.00% (16.0→16.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row127\" class=\"row_heading level2 row127\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row127_col0\" class=\"data row127 col0\" >0.00% (2.09e+02→2.09e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row128\" class=\"row_heading level2 row128\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row128_col0\" class=\"data row128 col0\" >-0.55% (1.81e+03→1.8e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row129\" class=\"row_heading level1 row129\" rowspan=3>G2FF Process Arroyo Feed Items Hot</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row129\" class=\"row_heading level2 row129\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row129_col0\" class=\"data row129 col0\" >0.00% (20.0→20.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row130\" class=\"row_heading level2 row130\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row130_col0\" class=\"data row130 col0\" >0.00% (1.45e+02→1.45e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row131\" class=\"row_heading level2 row131\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row131_col0\" class=\"data row131 col0\" >-0.12% (8.53e+02→8.52e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row132\" class=\"row_heading level1 row132\" rowspan=3>G2FF Arroyo Ranking Cold</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row132\" class=\"row_heading level2 row132\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row132_col0\" class=\"data row132 col0\" >nan% (0.0→0.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row133\" class=\"row_heading level2 row133\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row133_col0\" class=\"data row133 col0\" >0.00% (2.0→2.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row134\" class=\"row_heading level2 row134\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row134_col0\" class=\"data row134 col0\" >0.00% (11.0→11.0, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row135\" class=\"row_heading level1 row135\" rowspan=3>G2FF Arroyo Ranking Warm</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row135\" class=\"row_heading level2 row135\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row135_col0\" class=\"data row135 col0\" >nan% (0.0→0.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row136\" class=\"row_heading level2 row136\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row136_col0\" class=\"data row136 col0\" >0.00% (2.0→2.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row137\" class=\"row_heading level2 row137\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row137_col0\" class=\"data row137 col0\" >0.00% (14.0→14.0, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row138\" class=\"row_heading level1 row138\" rowspan=3>G2FF Arroyo Ranking Hot</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row138\" class=\"row_heading level2 row138\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row138_col0\" class=\"data row138 col0\" >nan% (0.0→0.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row139\" class=\"row_heading level2 row139\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row139_col0\" class=\"data row139 col0\" >0.00% (3.0→3.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row140\" class=\"row_heading level2 row140\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row140_col0\" class=\"data row140 col0\" >0.00% (13.0→13.0, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row141\" class=\"row_heading level1 row141\" rowspan=3>G2FF Reload Arroyo Table Cold</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row141\" class=\"row_heading level2 row141\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row141_col0\" class=\"data row141 col0\" >0.00% (28.0→28.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row142\" class=\"row_heading level2 row142\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row142_col0\" class=\"data row142 col0\" >0.00% (1.2e+02→1.2e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row143\" class=\"row_heading level2 row143\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row143_col0\" class=\"data row143 col0\" >-0.24% (4.2e+02→4.19e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row144\" class=\"row_heading level1 row144\" rowspan=3>G2FF Reload Arroyo Table Warm</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row144\" class=\"row_heading level2 row144\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row144_col0\" class=\"data row144 col0\" >0.00% (30.0→30.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row145\" class=\"row_heading level2 row145\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row145_col0\" class=\"data row145 col0\" >-0.51% (1.96e+02→1.95e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row146\" class=\"row_heading level2 row146\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row146_col0\" class=\"data row146 col0\" >-1.09% (7.37e+02→7.29e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level1_row147\" class=\"row_heading level1 row147\" rowspan=3>G2FF Reload Arroyo Table Hot</th>\n", "                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row147\" class=\"row_heading level2 row147\" >50</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row147_col0\" class=\"data row147 col0\" >0.00% (24.0→24.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row148\" class=\"row_heading level2 row148\" >90</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row148_col0\" class=\"data row148 col0\" >0.00% (1.13e+02→1.13e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315level2_row149\" class=\"row_heading level2 row149\" >99</th>\n", "                        <td id=\"T_086f3f74_6ccb_11eb_9c43_32e124840315row149_col0\" class=\"data row149 col0\" >0.00% (3.77e+02→3.77e+02, ≥ 0.1)</td>\n", "            </tr>\n", "    </tbody></table></p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h2>G2F Failure Latency</h2>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5>Overall</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5>Comparison of user overall (cumulative) metric values during the study period</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p><style  type=\"text/css\" >\n", "</style><table id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315\" ><thead>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank\" ></th>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Statistic</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank\" ></th>        <th class=\"index_name level1\" >control_id</th>        <th class=\"col_heading level1 col0\" >100 (Control)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank\" ></th>        <th class=\"index_name level2\" >treatment_id</th>        <th class=\"col_heading level2 col0\" >98 (ArroyoDuplex)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank\" ></th>        <th class=\"index_name level3\" >Sample Sizes</th>        <th class=\"col_heading level3 col0\" >Treatment=6,639,404<br>\n", "Control=6,643,389<br>\n", "No SSPM (p=0.2742)</th>    </tr>    <tr>        <th class=\"index_name level0\" >Date</th>        <th class=\"index_name level1\" >Metric</th>        <th class=\"index_name level2\" >quantile</th>        <th class=\"blank\" ></th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315level0_row0\" class=\"row_heading level0 row0\" rowspan=12>01/20 to 01/20</th>\n", "                        <th id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315level1_row0\" class=\"row_heading level1 row0\" rowspan=3>G2FF Failure Latency</th>\n", "                        <th id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315level2_row0\" class=\"row_heading level2 row0\" >50</th>\n", "                        <td id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315row0_col0\" class=\"data row0 col0\" >0.00% (2.19e+03→2.19e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315level2_row1\" class=\"row_heading level2 row1\" >90</th>\n", "                        <td id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315row1_col0\" class=\"data row1 col0\" >0.00% (1.24e+04→1.24e+04, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315level2_row2\" class=\"row_heading level2 row2\" >99</th>\n", "                        <td id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315row2_col0\" class=\"data row2 col0\" >-0.20% (7.94e+04→7.93e+04, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315level1_row3\" class=\"row_heading level1 row3\" rowspan=3>G2FF Cold Failure Latency</th>\n", "                        <th id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315level2_row3\" class=\"row_heading level2 row3\" >50</th>\n", "                        <td id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315row3_col0\" class=\"data row3 col0\" >-0.17% (2.88e+03→2.87e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315level2_row4\" class=\"row_heading level2 row4\" >90</th>\n", "                        <td id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315row4_col0\" class=\"data row4 col0\" >-0.05% (2.12e+04→2.12e+04, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315level2_row5\" class=\"row_heading level2 row5\" >99</th>\n", "                        <td id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315row5_col0\" class=\"data row5 col0\" >0.33% (1.1e+05→1.1e+05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315level1_row6\" class=\"row_heading level1 row6\" rowspan=3>G2FF Warm Failure Latency</th>\n", "                        <th id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315level2_row6\" class=\"row_heading level2 row6\" >50</th>\n", "                        <td id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315row6_col0\" class=\"data row6 col0\" >-0.13% (2.38e+03→2.37e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315level2_row7\" class=\"row_heading level2 row7\" >90</th>\n", "                        <td id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315row7_col0\" class=\"data row7 col0\" >0.11% (2.44e+04→2.44e+04, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315level2_row8\" class=\"row_heading level2 row8\" >99</th>\n", "                        <td id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315row8_col0\" class=\"data row8 col0\" >-1.95% (1.47e+05→1.44e+05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315level1_row9\" class=\"row_heading level1 row9\" rowspan=3>G2FF Hot Failure Latency</th>\n", "                        <th id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315level2_row9\" class=\"row_heading level2 row9\" >50</th>\n", "                        <td id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315row9_col0\" class=\"data row9 col0\" >0.00% (2.1e+03→2.1e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315level2_row10\" class=\"row_heading level2 row10\" >90</th>\n", "                        <td id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315row10_col0\" class=\"data row10 col0\" >0.03% (9.03e+03→9.03e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315level2_row11\" class=\"row_heading level2 row11\" >99</th>\n", "                        <td id=\"T_08a41e1a_6ccb_11eb_9c43_32e124840315row11_col0\" class=\"data row11 col0\" >0.06% (6.65e+04→6.66e+04, ≥ 0.1)</td>\n", "            </tr>\n", "    </tbody></table></p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h2>G2F Metrics Notification</h2>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5>Overall</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5>Comparison of user overall (cumulative) metric values during the study period</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p><style  type=\"text/css\" >\n", "    #T_08dfd342_6ccb_11eb_9c43_32e124840315row1_col0 {\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #66b366;\n", "        }    #T_08dfd342_6ccb_11eb_9c43_32e124840315row10_col0 {\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #008000;\n", "        }</style><table id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315\" ><thead>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank\" ></th>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Statistic</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank\" ></th>        <th class=\"index_name level1\" >control_id</th>        <th class=\"col_heading level1 col0\" >100 (Control)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank\" ></th>        <th class=\"index_name level2\" >treatment_id</th>        <th class=\"col_heading level2 col0\" >98 (ArroyoDuplex)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank\" ></th>        <th class=\"index_name level3\" >Sample Sizes</th>        <th class=\"col_heading level3 col0\" >Treatment=6,639,404<br>\n", "Control=6,643,389<br>\n", "No SSPM (p=0.2742)</th>    </tr>    <tr>        <th class=\"index_name level0\" >Date</th>        <th class=\"index_name level1\" >Metric</th>        <th class=\"index_name level2\" >quantile</th>        <th class=\"blank\" ></th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level0_row0\" class=\"row_heading level0 row0\" rowspan=24>01/20 to 01/20</th>\n", "                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level1_row0\" class=\"row_heading level1 row0\" rowspan=3>G2FF Success Latency with Notification</th>\n", "                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level2_row0\" class=\"row_heading level2 row0\" >50</th>\n", "                        <td id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315row0_col0\" class=\"data row0 col0\" >-0.15% (6.88e+02→6.87e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level2_row1\" class=\"row_heading level2 row1\" >90</th>\n", "                        <td id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315row1_col0\" class=\"data row1 col0\" >-0.42% (2.37e+03→2.36e+03, < 0.01)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level2_row2\" class=\"row_heading level2 row2\" >99</th>\n", "                        <td id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315row2_col0\" class=\"data row2 col0\" >0.05% (8.71e+03→8.71e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level1_row3\" class=\"row_heading level1 row3\" rowspan=3>G2FF Cold Success Latency with Notification</th>\n", "                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level2_row3\" class=\"row_heading level2 row3\" >50</th>\n", "                        <td id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315row3_col0\" class=\"data row3 col0\" >-0.18% (1.09e+03→1.08e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level2_row4\" class=\"row_heading level2 row4\" >90</th>\n", "                        <td id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315row4_col0\" class=\"data row4 col0\" >-0.18% (3.4e+03→3.39e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level2_row5\" class=\"row_heading level2 row5\" >99</th>\n", "                        <td id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315row5_col0\" class=\"data row5 col0\" >-0.03% (1.19e+04→1.19e+04, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level1_row6\" class=\"row_heading level1 row6\" rowspan=3>G2FF Warm Success Latency with Notification</th>\n", "                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level2_row6\" class=\"row_heading level2 row6\" >50</th>\n", "                        <td id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315row6_col0\" class=\"data row6 col0\" >0.00% (6.91e+02→6.91e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level2_row7\" class=\"row_heading level2 row7\" >90</th>\n", "                        <td id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315row7_col0\" class=\"data row7 col0\" >-0.07% (2.9e+03→2.9e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level2_row8\" class=\"row_heading level2 row8\" >99</th>\n", "                        <td id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315row8_col0\" class=\"data row8 col0\" >1.53% (1.16e+04→1.18e+04, < 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level1_row9\" class=\"row_heading level1 row9\" rowspan=3>G2FF Hot Success Latency with Notification</th>\n", "                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level2_row9\" class=\"row_heading level2 row9\" >50</th>\n", "                        <td id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315row9_col0\" class=\"data row9 col0\" >-0.17% (5.81e+02→5.8e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level2_row10\" class=\"row_heading level2 row10\" >90</th>\n", "                        <td id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315row10_col0\" class=\"data row10 col0\" >-0.66% (1.96e+03→1.95e+03, < 0.0001)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level2_row11\" class=\"row_heading level2 row11\" >99</th>\n", "                        <td id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315row11_col0\" class=\"data row11 col0\" >-0.24% (7.09e+03→7.07e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level1_row12\" class=\"row_heading level1 row12\" rowspan=3>G2FF Success Latency without Notification</th>\n", "                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level2_row12\" class=\"row_heading level2 row12\" >50</th>\n", "                        <td id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315row12_col0\" class=\"data row12 col0\" >0.00% (6.56e+02→6.56e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level2_row13\" class=\"row_heading level2 row13\" >90</th>\n", "                        <td id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315row13_col0\" class=\"data row13 col0\" >-0.08% (2.61e+03→2.60e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level2_row14\" class=\"row_heading level2 row14\" >99</th>\n", "                        <td id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315row14_col0\" class=\"data row14 col0\" >-0.05% (1.23e+04→1.23e+04, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level1_row15\" class=\"row_heading level1 row15\" rowspan=3>G2FF Cold Success Latency without Notification</th>\n", "                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level2_row15\" class=\"row_heading level2 row15\" >50</th>\n", "                        <td id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315row15_col0\" class=\"data row15 col0\" >0.10% (1.03e+03→1.03e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level2_row16\" class=\"row_heading level2 row16\" >90</th>\n", "                        <td id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315row16_col0\" class=\"data row16 col0\" >0.10% (4.11e+03→4.11e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level2_row17\" class=\"row_heading level2 row17\" >99</th>\n", "                        <td id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315row17_col0\" class=\"data row17 col0\" >-0.05% (1.93e+04→1.93e+04, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level1_row18\" class=\"row_heading level1 row18\" rowspan=3>G2FF Warm Success Latency without Notification</th>\n", "                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level2_row18\" class=\"row_heading level2 row18\" >50</th>\n", "                        <td id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315row18_col0\" class=\"data row18 col0\" >0.00% (7.97e+02→7.97e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level2_row19\" class=\"row_heading level2 row19\" >90</th>\n", "                        <td id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315row19_col0\" class=\"data row19 col0\" >-0.14% (3.66e+03→3.65e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level2_row20\" class=\"row_heading level2 row20\" >99</th>\n", "                        <td id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315row20_col0\" class=\"data row20 col0\" >-0.37% (2.26e+04→2.25e+04, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level1_row21\" class=\"row_heading level1 row21\" rowspan=3>G2FF Hot Success Latency without Notification</th>\n", "                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level2_row21\" class=\"row_heading level2 row21\" >50</th>\n", "                        <td id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315row21_col0\" class=\"data row21 col0\" >0.00% (5.41e+02→5.41e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level2_row22\" class=\"row_heading level2 row22\" >90</th>\n", "                        <td id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315row22_col0\" class=\"data row22 col0\" >-0.09% (2.18e+03→2.18e+03, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                        <th id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315level2_row23\" class=\"row_heading level2 row23\" >99</th>\n", "                        <td id=\"T_08dfd342_6ccb_11eb_9c43_32e124840315row23_col0\" class=\"data row23 col0\" >-0.27% (9.34e+03→9.32e+03, ≥ 0.1)</td>\n", "            </tr>\n", "    </tbody></table></p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["report.configurations['stat_fmtr'] = (\n", "    \"{pct_diff:,.2f}% ({quantile_control:,.3}→{quantile_treatment:,.3}, {p_value_formatted})\") \n", "for metrics in latency_metric_tables:\n", "    abtest.report.notebook_print(metrics.name.replace('_', ' ').title(), \"h2\")\n", "    report.generate_report(\n", "        extra_table_cols=['treatment_id', 'quantile', \n", "                          'quantile_control', 'quantile_treatment',\n", "                          'volume_control', 'volume_treatment',\n", "                          'count_control', 'count_treatment',\n", "                          ],\n", "        facet_bys=['treatment_id', 'quantile'],\n", "        format_pvalue=True,\n", "        metric_filters={\"metric\": metrics.cumulative_metrics},\n", "        display_config={\"cumulative\": [\"table\"], \"daily\": daily_display}\n", "    );\n", "    "]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<h2>G2F Failure Reason Breakdown</h2>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5>Overall</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5>Comparison of user overall (cumulative) metric values during the study period</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p><style  type=\"text/css\" >\n", "    #T_0928016c_6ccb_11eb_9c43_32e124840315row4_col0 {\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #f89999;\n", "        }    #T_0928016c_6ccb_11eb_9c43_32e124840315row24_col0 {\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #99cc99;\n", "        }</style><table id=\"T_0928016c_6ccb_11eb_9c43_32e124840315\" ><thead>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Statistic</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level1\" >control_id</th>        <th class=\"col_heading level1 col0\" >100 (Control)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level2\" >treatment_id</th>        <th class=\"col_heading level2 col0\" >98 (ArroyoDuplex)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level3\" >Sample Sizes</th>        <th class=\"col_heading level3 col0\" >Treatment=6,639,404<br>\n", "Control=6,643,389<br>\n", "No SSPM (p=0.2742)</th>    </tr>    <tr>        <th class=\"index_name level0\" >Date</th>        <th class=\"index_name level1\" >Metric</th>        <th class=\"blank\" ></th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level0_row0\" class=\"row_heading level0 row0\" rowspan=38>01/20 to 01/20</th>\n", "                        <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row0\" class=\"row_heading level1 row0\" >Unavailable</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row0_col0\" class=\"data row0 col0\" >0.19% (nan→nan, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row1\" class=\"row_heading level1 row1\" >Duplicate Request</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row1_col0\" class=\"data row1 col0\" >-0.24% (nan→nan, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row2\" class=\"row_heading level1 row2\" >Unauthorized</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row2_col0\" class=\"data row2 col0\" >1.20% (nan→nan, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row3\" class=\"row_heading level1 row3\" >Timeout</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row3_col0\" class=\"data row3 col0\" >0.21% (nan→nan, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row4\" class=\"row_heading level1 row4\" >Internal Error</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row4_col0\" class=\"data row4 col0\" >11.01% (nan→nan, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row5\" class=\"row_heading level1 row5\" >Invalid Operation</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row5_col0\" class=\"data row5 col0\" >nan% (nan→nan, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row6\" class=\"row_heading level1 row6\" >Cancelled</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row6_col0\" class=\"data row6 col0\" >0.72% (nan→nan, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row7\" class=\"row_heading level1 row7\" >Database Error</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row7_col0\" class=\"data row7 col0\" >-3.99% (nan→nan, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row8\" class=\"row_heading level1 row8\" >Storage Full</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row8_col0\" class=\"data row8 col0\" >-1.55% (nan→nan, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row9\" class=\"row_heading level1 row9\" >Database Corrupt</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row9_col0\" class=\"data row9 col0\" >350.92% (nan→nan, < 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row10\" class=\"row_heading level1 row10\" >Native Conversation Sync</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row10_col0\" class=\"data row10 col0\" >1.68% (nan→nan, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row11\" class=\"row_heading level1 row11\" >Cannot Connect to Host</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row11_col0\" class=\"data row11 col0\" >-44.41% (nan→nan, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row12\" class=\"row_heading level1 row12\" >Bad Server Response</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row12_col0\" class=\"data row12 col0\" >-0.86% (nan→nan, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row13\" class=\"row_heading level1 row13\" >Cancelled UFS</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row13_col0\" class=\"data row13 col0\" >5.62% (nan→nan, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row14\" class=\"row_heading level1 row14\" >Internet Disconnected</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row14_col0\" class=\"data row14 col0\" >-2.77% (nan→nan, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row15\" class=\"row_heading level1 row15\" >Connection Lost</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row15_col0\" class=\"data row15 col0\" >nan% (nan→nan, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row16\" class=\"row_heading level1 row16\" >Timeout UFS</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row16_col0\" class=\"data row16 col0\" >-24.95% (nan→nan, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row17\" class=\"row_heading level1 row17\" >Cannot Connect to Internet</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row17_col0\" class=\"data row17 col0\" >33.41% (nan→nan, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row18\" class=\"row_heading level1 row18\" >Network Changed</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row18_col0\" class=\"data row18 col0\" >11.83% (nan→nan, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row19\" class=\"row_heading level1 row19\" >Hostname not Resolved</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row19_col0\" class=\"data row19 col0\" >-14.56% (nan→nan, < 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row20\" class=\"row_heading level1 row20\" >Other</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row20_col0\" class=\"data row20 col0\" >4.94% (nan→nan, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row21\" class=\"row_heading level1 row21\" >Quic Protocol Failed</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row21_col0\" class=\"data row21 col0\" >16.74% (nan→nan, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row22\" class=\"row_heading level1 row22\" >Connection Closed</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row22_col0\" class=\"data row22 col0\" >6.39% (nan→nan, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row23\" class=\"row_heading level1 row23\" >Timed Out</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row23_col0\" class=\"data row23 col0\" >-5.45% (nan→nan, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row24\" class=\"row_heading level1 row24\" >Connection Refused</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row24_col0\" class=\"data row24 col0\" >-46.40% (nan→nan, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row25\" class=\"row_heading level1 row25\" >Connection Reset</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row25_col0\" class=\"data row25 col0\" >-15.02% (nan→nan, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row26\" class=\"row_heading level1 row26\" >Address Unreachable</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row26_col0\" class=\"data row26 col0\" >4.03% (nan→nan, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row27\" class=\"row_heading level1 row27\" >Client Certificate Rejected</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row27_col0\" class=\"data row27 col0\" >nan% (nan→nan, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row28\" class=\"row_heading level1 row28\" >Cannot Parse Response</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row28_col0\" class=\"data row28 col0\" >nan% (nan→nan, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row29\" class=\"row_heading level1 row29\" >Secure Connection Failed</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row29_col0\" class=\"data row29 col0\" >nan% (nan→nan, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row30\" class=\"row_heading level1 row30\" >Connection Timed Out</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row30_col0\" class=\"data row30 col0\" >13.28% (nan→nan, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row31\" class=\"row_heading level1 row31\" >Cannot Find Host</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row31_col0\" class=\"data row31 col0\" >nan% (nan→nan, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row32\" class=\"row_heading level1 row32\" >Quic Crypto Symmetric Key Setup Failed</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row32_col0\" class=\"data row32 col0\" >nan% (nan→nan, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row33\" class=\"row_heading level1 row33\" >Calls Active</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row33_col0\" class=\"data row33 col0\" >nan% (nan→nan, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row34\" class=\"row_heading level1 row34\" >International Roaming Off</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row34_col0\" class=\"data row34 col0\" >nan% (nan→nan, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row35\" class=\"row_heading level1 row35\" >Data Not Allowed</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row35_col0\" class=\"data row35 col0\" >nan% (nan→nan, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row36\" class=\"row_heading level1 row36\" >Cannot Decode Raw Data</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row36_col0\" class=\"data row36 col0\" >nan% (nan→nan, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0928016c_6ccb_11eb_9c43_32e124840315level1_row37\" class=\"row_heading level1 row37\" >http Proxy Connection Failure</th>\n", "                        <td id=\"T_0928016c_6ccb_11eb_9c43_32e124840315row37_col0\" class=\"data row37 col0\" >nan% (nan→nan, nan)</td>\n", "            </tr>\n", "    </tbody></table></p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["report.configurations['stat_fmtr'] = (\n", "    \"{pct_diff:,.2f}% ({quantile_control:,.3}→{quantile_treatment:,.3}, {p_value_formatted})\") \n", "for metrics in detailed_metrics:\n", "    abtest.report.notebook_print(metrics.name.replace('_', ' ').title(), \"h2\")\n", "    report.generate_report(\n", "        extra_table_cols=['treatment_id', 'quantile', \n", "                          'quantile_control', 'quantile_treatment',\n", "                          'volume_control', 'volume_treatment',\n", "                          'count_control', 'count_treatment',\n", "                          ],\n", "        facet_bys=['treatment_id', 'quantile'],\n", "        format_pvalue=True,\n", "        metric_filters={\"metric\": metrics.cumulative_metrics},\n", "        display_config={\"cumulative\": [\"table\"], \"daily\": []}\n", "    );\n", "    "]}], "metadata": {"celltoolbar": "Tags", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}