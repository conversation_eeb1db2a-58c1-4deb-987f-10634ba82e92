{"cells": [{"metadata": {}, "cell_type": "markdown", "source": "# Messaging AB Notebook"}, {"metadata": {}, "cell_type": "markdown", "source": "## Notebook Config"}, {"metadata": {"tags": ["parameters"]}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# Specify study details\n", "STUDY_NAME = 'SPONSORED_SNAPS__125868'\n", "STUDY_START_DATE = '20250126'\n", "STUDY_END_DATE = '20250126'\n", "RETRO_START_DATE = '20250114'\n", "RETRO_END_DATE = '20250115'\n", "CONTROL_ID = '1'\n", "TREATMENT_IDS = ['3']\n", "\n", "EXP_NAMES = {\n", "}\n", "\n", "# The date to use for user breakdown attributes. Defaults to two days before study start date.\n", "COHORT_DATE = None\n", "CUMULATIVE_TREND= False\n", "OVERWRITE = False\n", "DAILY_TREND = False\n", "MATERIALIZE_MAPPING_TABLE = True\n", "COMPUTE_RETRO_AA = False\n", "CUPED = False\n", "\n", "USER_BREAKDOWN_LIST = []\n", "# 'communication_engagement_status'\n", "quantiles = ['10','25','50','90']\n", "\n", "# Running config\n", "# BQ_PRIORITY = 'INTERACTIVE'\n", "\n", "# Need to make 'engagement metrics' fixed and not able to unselect\n", "METRICS_GROUP_CONFIG = [\n", "#       'chat_metrics',\n", "#       'chat_detail_metrics',\n", "#       'snap_detail_metrics',\n", "#       'snap_save_delete_metrics',\n", "#       'chat_save_metrics',\n", "#       'new_chat_page',\n", "#       'voice_notes',\n", "#       'share_extension_metrics',\n", "#       'non_friend_metrics',\n", "#       'chat_erase_mode_metrics',\n", "#       'friend_feed_shortcut_metrics',\n", "#       'relationship_closeness_metrics',\n", "#       'notification_platform_metrics_overall',\n", "#       'notification_platform_metrics_by_type',\n", "#       'notification_to_message_ready_failure_metrics',\n", "#       'notification_to_message_ready_latency_metrics',\n", "#       'notification_os_breakdown_metrics',\n", "#       'friend_feed_state_metrics',\n", "#       'friend_feed_state_detail_metrics',\n", "#       'friend_feed_state_cell_position_metrics',\n", "      'sponsored_snap_metrics' \n", "]\n", "\n", "\n", "# Running config\n", "USE_BATCH_BQ_PRIORITY = True\n", "\n", "SUPPLEMENT_NOTIF_TYPES = \"\""]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["import warnings\n", "warnings.filterwarnings(\"ignore\")"]}, {"metadata": {}, "cell_type": "markdown", "source": "## Imports"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["from __future__ import division, unicode_literals, print_function\n", "import os\n", "if any(['VELLUM' in k for k in os.environ.keys()]):\n", "    pip_output = !pip install banjo\n", "\n", "import seaborn as sns\n", "import pandas as pd\n", "import logging\n", "import numpy as np\n", "import collections\n", "logging.basicConfig()\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "%matplotlib inline\n", "%config InlineBackend.figure_format = 'retina'\n", "\n", "from banjo import abtest, utils\n", "from banjo.abtest.report import (\n", "    Metric, MetricTable, Report, get_quest_metric_table, CustomReport, \n", "    get_abtest_console_metric_table\n", ")\n", "from banjo.abtest.cohort_report import CohortReport\n", "from banjo.abtest.quest import TIER_ONE\n", "from banjo.abtest.field_breakdown_metric_table import FieldBreakdownMetricTable\n", "\n", "try:\n", "    from banjo.teams.product import messaging as msg\n", "\n", "except ImportError:\n", "    GIT_DIR = os.path.join(os.environ['HOME'])\n", "    os.chdir(GIT_DIR)\n", "    print(\"Upgrade banjo to 0.14.0+, which packages all these scripts\")\n", "\n", "# try:\n", "#     from banjo.teams.product import camera_growth as cam\n", "\n", "# except ImportError:\n", "#     GIT_DIR = os.path.join(os.environ['HOME'])\n", "#     os.chdir(GIT_DIR)\n", "#     print(\"Upgrade banjo to 0.14.0+, which packages all these scripts\")\n", "#     import camera_growth as cam\n", "    \n", "    \n", "PROJECT = 'sc-bq-gcs-billingonly'\n", "sns.set(style='whitegrid', font_scale=1.5)\n", "\n", "from IPython.core.display import display, HTML\n", "display(HTML(\"<style>.output_html.rendered_html table { font-size:8pt;}</style>\"))\n", "\n", "# decide if print daily trend\n", "daily_display = []\n", "if DAILY_TREND:\n", "    daily_display = ['trend']\n", "\n", "\n", "metrics_group_config_additional=METRICS_GROUP_CONFIG.copy()\n", "\n", "if 'friend_feed_state_metrics' in METRICS_GROUP_CONFIG:\n", "    metrics_group_config_additional.append('friend_feed_state_convo_metrics')\n", "    metrics_group_config_additional.append('friend_feed_state_active_day_metrics')\n", "    metrics_group_config_additional.append('friend_feed_page_view_metrics')\n", "    metrics_group_config_additional.append('friend_feed_ppv_metrics')\n", "\n", "else:\n", "    pass\n", "\n", "    "]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "# import messaging as msg"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["from banjo.teams.product.messaging import (\n", "# from messaging import (\n", "    chat_metrics,\n", "    chat_detail_metrics,\n", "    snap_detail_metrics,\n", "    snap_save_delete_metrics,\n", "    chat_save_metrics,\n", "    new_chat_page,\n", "    voice_notes,\n", "    create_share_extension_metrics,\n", "    share_extension_metrics,\n", "    non_friend_metrics,\n", "    chat_erase_mode_metrics,\n", "    friend_feed_shortcut_metrics,\n", "    relationship_closeness_metrics,\n", "    notification_platform_metrics_overall,\n", "    notification_platform_metrics_by_type,\n", "    notification_to_message_ready_failure_metrics,\n", "    notification_to_message_ready_latency_metrics,\n", "    notification_os_breakdown_metrics,\n", "    friend_feed_state_metrics,\n", "    friend_feed_state_detail_metrics,\n", "    friend_feed_state_cell_position_metrics,\n", "    friend_feed_state_convo_metrics,\n", "    friend_feed_state_active_day_metrics,\n", "    friend_feed_page_view_metrics,\n", "    friend_feed_ppv_metrics,\n", "    sponsored_snap_metrics\n", ")"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# Ensure certain items passed from husky are lists\n", "def ensure_list(parameter):\n", "    if isinstance(parameter, list):\n", "        return parameter\n", "    else:\n", "        return [parameter]\n", "\n", "TREATMENT_IDS = ensure_list(TREATMENT_IDS)\n", "USER_BREAKDOWN_LIST = ensure_list(USER_BREAKDOWN_LIST)\n", "BQ_PRIORITY = \"BATCH\" if USE_BATCH_BQ_PRIORITY else \"INTERACTIVE\"\n", "METRICS_GROUP_CONFIG = ensure_list(METRICS_GROUP_CONFIG)   \n", "metrics_group_config_additional = ensure_list(metrics_group_config_additional)  \n", "\n", "ALL_METRICS_GROUPS = {}\n", "\n", "chat_ai_breakdowns = [['correspondent']]\n", "notif_type_breakdowns = [['notif_type']]\n", "COMPUTE_RETRO_AA = bool(COMPUTE_RETRO_AA)"]}, {"metadata": {}, "cell_type": "markdown", "source": "### Add metrics"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["ALL_METRICS_GROUPS['chat_metrics'] = [msg.chat_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['chat_detail_metrics'] = [msg.chat_detail_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['snap_detail_metrics']=[msg.snap_detail_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['snap_save_delete_metrics']=[msg.create_snap_save_delete_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['chat_save_metrics'] = [msg.create_chat_save_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['new_chat_page']=[msg.new_chat_page(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['voice_notes']=[msg.voice_notes(STUDY_START_DATE, STUDY_END_DATE)]+[msg.voice_notes_other(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['share_extension_metrics']=[msg.create_share_extension_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['non_friend_metrics']=[msg.non_friend_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['chat_erase_mode_metrics']=[msg.chat_erase_mode_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['friend_feed_shortcut_metrics']=[msg.friend_feed_shortcut_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['relationship_closeness_metrics']=[msg.relationship_closeness_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['notification_platform_metrics_overall']=[msg.notification_platform_metrics_overall(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['notification_platform_metrics_by_type']=[msg.notification_platform_metrics_by_type(STUDY_START_DATE, STUDY_END_DATE, notif_type_breakdowns, SUPPLEMENT_NOTIF_TYPES)]\n", "ALL_METRICS_GROUPS['notification_to_message_ready_failure_metrics']=[msg.notification_to_message_ready_failure_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['notification_os_breakdown_metrics']=[msg.notification_os_breakdown_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['friend_feed_state_metrics']=[msg.friend_feed_state_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['friend_feed_state_convo_metrics']=[msg.friend_feed_state_convo_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['friend_feed_state_active_day_metrics']=[msg.friend_feed_state_active_day_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['friend_feed_state_detail_metrics']=[msg.friend_feed_state_detail_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['friend_feed_state_cell_position_metrics']=[msg.friend_feed_state_cell_position_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['friend_feed_page_view_metrics']=[msg.friend_feed_page_view_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['friend_feed_ppv_metrics']=[msg.friend_feed_ppv_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['sponsored_snap_metrics']=[msg.sponsored_snap_metrics(STUDY_START_DATE, STUDY_END_DATE)]"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# SQL Metric Tables\n", "metric_mt_dic = collections.defaultdict(list)\n", "metric_list_dic = collections.defaultdict(list)\n", "\n", "sql_metrics = {\n", "    'chat_metrics': chat_metrics,\n", "    'chat_detail_metrics': chat_detail_metrics,\n", "    'snap_detail_metrics': snap_detail_metrics,\n", "#     'snap_save_delete_metrics': snap_save_delete_metrics,\n", "#     'chat_save_metrics': chat_save_metrics,\n", "    'new_chat_page': new_chat_page,\n", "    'voice_notes': voice_notes,\n", "#     'share_extension_metrics': share_extension_metrics,\n", "#     'create_share_extension_metrics': create_share_extension_metrics,\n", "    'non_friend_metrics': non_friend_metrics,\n", "    'chat_erase_mode_metrics': chat_erase_mode_metrics,\n", "    'friend_feed_shortcut_metrics': friend_feed_shortcut_metrics,\n", "    'relationship_closeness_metrics': relationship_closeness_metrics,\n", "#     'notification_platform_metrics': notification_platform_metrics,\n", "    'notification_to_message_ready_failure_metrics': notification_to_message_ready_failure_metrics,\n", "#     'notification_to_message_ready_latency_metrics': notification_to_message_ready_latency_metrics,\n", "    'notification_os_breakdown_metrics': notification_os_breakdown_metrics,\n", "    'friend_feed_state_metrics': friend_feed_state_metrics,\n", "    'friend_feed_state_detail_metrics': friend_feed_state_detail_metrics,\n", "    'friend_feed_state_cell_position_metrics': friend_feed_state_cell_position_metrics,\n", "    'friend_feed_state_convo_metrics': friend_feed_state_convo_metrics,\n", "    'friend_feed_state_active_day_metrics': friend_feed_state_active_day_metrics,\n", "    'friend_feed_page_view_metrics': friend_feed_page_view_metrics,\n", "    'friend_feed_ppv_metrics': friend_feed_ppv_metrics,\n", "    'sponsored_snap_metrics': sponsored_snap_metrics\n", "}\n", "\n", "get_metric_cols = lambda mt: [metric.col for metric in mt.metrics]\n", "\n", "def get_sql_metric_tables(metric_name, metric_function):\n", "    metric_function = metric_function if isinstance(metric_function, list) else [metric_function]\n", "    metrics_mt = [\n", "#         func(STUDY_START_DATE, STUDY_END_DATE, STUDY_NAME) if metric_name in ['content', 'carousel', 'carousel_deep_dive']\n", "#         else \n", "        func(STUDY_START_DATE, STUDY_END_DATE)\n", "        for func in metric_function\n", "    ]\n", "\n", "    # Notifs Performance and Opt In/Out use FieldBreakdownMetricTable() which doesn't support AA and CUPED\n", "    if metric_name not in []:\n", "        for mt in metrics_mt:\n", "            mt.aa, mt.cuped = COMPUTE_RETRO_AA, CUPED\n", "\n", "    metric_mt_dic[metric_name] = metrics_mt\n", "    metric_list_dic[metric_name] = [col for mt in metrics_mt for col in get_metric_cols(mt)]\n", "\n", "for metric_name, metric_function in sql_metrics.items():\n", "    if metric_name in METRICS_GROUP_CONFIG or metric_name in metrics_group_config_additional:\n", "        get_sql_metric_tables(metric_name, metric_function)"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["metric_tables = [\n", "    metric_table \n", "    for sublist in [ALL_METRICS_GROUPS[mt] for mt in ALL_METRICS_GROUPS if mt in metrics_group_config_additional]\n", "    for metric_table in sublist\n", "]\n", "metric_tables.extend(value for values in metric_mt_dic.values() for value in values)\n", "# print(metric_tables)"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "# print(metric_tables)"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# Get Experiment Names\n", "study_name_sql = \"\"\"\n", "    SELECT\n", "      name,\n", "      exp_id, \n", "      study_version\n", "    FROM\n", "      `sc-analytics.report_search.ab_console_study_config*`\n", "    WHERE\n", "      study_name = '{study_name}'\n", "      AND exp_id IN ('{exp_ids}')\n", "\"\"\"\n", "df_exp_names = utils.gbq.read_gbq(\n", "    study_name_sql.format(\n", "        study_name=STUDY_NAME, exp_ids=\"', '\".join([CONTROL_ID] + TREATMENT_IDS),\n", "    ),\n", "    dialect='standard',\n", "    project_id=PROJECT)\n", "if not EXP_NAMES and not df_exp_names.empty:\n", "    EXP_NAMES = dict(zip(df_exp_names.exp_id, df_exp_names.name))"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["report = CohortReport(\n", "    study_name=STUDY_NAME,\n", "    study_start_date=STUDY_START_DATE,\n", "    study_end_date=STUDY_END_DATE,\n", "    aa_start_date=RETRO_START_DATE,\n", "    aa_end_date=RETRO_END_DATE,\n", "    metric_tables=metric_tables,\n", "    control_id=CONTROL_ID,\n", "    treatment_ids=TREATMENT_IDS,\n", "    user_group_bys=USER_BREAKDOWN_LIST,\n", "    bq_project='sc-bq-gcs-billingonly',  \n", "    dest_dataset='temp_abtest',\n", "    cohort_definition_date=COHORT_DATE,\n", "    materializing_mapping_table=MATERIALIZE_MAPPING_TABLE,\n", "    overwrite_mapping_table=OVERWRITE,\n", "    bq_priority=USE_BATCH_BQ_PRIORITY,\n", "    exp_id_to_name=EXP_NAMES,\n", ")\n", "\n", "report.configurations['pivot_table'] = True\n", "report.configurations['stat_fmtr'] = (\n", "    \"{pct_diff:,.2f}% ({avg_control:,.3}→{avg_treatment:,.3}, {p_value_formatted})\"\n", ")\n", "\n", "# Run the joins and calculate the results\n", "report.execute(\n", "   overwrite=OVERWRITE, \n", "   cumulative_trend=CUMULATIVE_TREND,\n", ")"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["import collections\n", "\n", "METRICS_NAME_MAPPING = collections.OrderedDict([\n", "    [\"DIRECT_SNAP_CREATE\",\"Direct Snap Create\"],\n", "    [\"DIRECT_SNAP_CREATE_CAMERA\",\"Snap Create from Camera\"],\n", "    [\"DIRECT_SNAP_CREATE_FEED\",\"Snap Create from DTTR\"],\n", "    [\"DIRECT_SNAP_CREATE_FEED_SNAP_REPLY\",\"Snap Create from Feed Reply Button\"],\n", "    [\"DIRECT_SNAP_CREATE_IN_CHAT\",\"Snap Create in Chat Page\"],\n", "    [\"DIRECT_SNAP_CREATE_IN_CHAT_from_ACTION_MENU\",\"Snap Create in Chat Page from Action Menu \"],\n", "    [\"DIRECT_SNAP_CREATE_IN_CHAT_from_DOUBLE_TAP\",\"Snap Create from in Chat Page Double Tap \"],\n", "    [\"DIRECT_SNAP_CREATE_ADD_TO_STORY\",\"Snap Create from Add to Story \"],\n", "    [\"DIRECT_SNAP_CREATE_MINI_PROFILE\",\"Snap Create from Mini Profile\"],\n", "    [\"DIRECT_SNAP_CREATE_SEARCH\",\"Snap Create from Search\"],\n", "    [\"DIRECT_SNAP_CREATE_with_SNAP_REPLY_STICKER\",\"Snap Create from Snap Reply Sticker\"],\n", "    [\"DIRECT_SNAP_SEND\",\"Direct Snap Send\"],\n", "    [\"DIRECT_SNAP_SEND_CAMERA\",\"Snap Send from Camera\"],\n", "    [\"DIRECT_SNAP_SEND_FEED\",\"Snap Send from DTTR\"],\n", "    [\"DIRECT_SNAP_SEND_FEED_SNAP_REPLY\",\"Snap Send from Feed Reply Button\"],\n", "    [\"DIRECT_SNAP_SEND_IN_CHAT\",\"Snap Send in Chat Page\"],\n", "    [\"DIRECT_SNAP_SEND_IN_CHAT_from_ACTION_MENU\",\"Snap Send in Chat Page from Action Menu \"],\n", "    [\"DIRECT_SNAP_SEND_IN_CHAT_from_DOUBLE_TAP\",\"Snap Send from in Chat Page Double Tap \"],\n", "    [\"DIRECT_SNAP_SEND_MINI_PROFILE\",\"Snap Send from Mini Profile\"],\n", "    [\"DIRECT_SNAP_SEND_SEARCH\",\"Snap Send from Search\"],\n", "    [\"DIRECT_SNAP_SEND_with_SNAP_REPLY_STICKER\",\"Snap Send from Snap Reply Sticker\"],\n", "    [\"DIRECT_SNAP_CREATE_UU\",\"Snap Creator\"],\n", "    [\"DIRECT_SNAP_CREATE_CAMERA_UU\",\"Snap Creator from Camera\"],\n", "    [\"DIRECT_SNAP_CREATE_FEED_UU\",\"Snap Creator from DTTR\"],\n", "    [\"DIRECT_SNAP_CREATE_FEED_SNAP_REPLY_UU\",\"Snap Creator from Feed Reply Button\"],\n", "    [\"DIRECT_SNAP_CREATE_IN_CHAT_UU\",\"Snap Creator in Chat Page\"],\n", "    [\"DIRECT_SNAP_CREATE_IN_CHAT_from_ACTION_MENU_UU\",\"Snap Creator in Chat Page from Action Menu \"],\n", "    [\"DIRECT_SNAP_CREATE_IN_CHAT_from_DOUBLE_TAP_UU\",\"Snap Creator from in Chat Page Double Tap \"],\n", "    [\"DIRECT_SNAP_CREATE_ADD_TO_STORY_UU\",\"Snap Creator from Add to Story \"],\n", "    [\"DIRECT_SNAP_CREATE_MINI_PROFILE_UU\",\"Snap Creator from Mini Profile\"],\n", "    [\"DIRECT_SNAP_CREATE_SEARCH_UU\",\"Snap Creator from Search\"],\n", "    [\"DIRECT_SNAP_CREATE_with_SNAP_REPLY_STICKER_UU\",\"Snap Creator from Snap Reply Sticker\"],\n", "    [\"DIRECT_SNAP_SEND_UU\",\"Snap Sender\"],\n", "    [\"DIRECT_SNAP_SEND_CAMERA_UU\",\"Snap Sender from Camera\"],\n", "    [\"DIRECT_SNAP_SEND_FEED_UU\",\"Snap Sender from DTTR\"],\n", "    [\"DIRECT_SNAP_SEND_FEED_SNAP_REPLY_UU\",\"Snap Sender from Feed Reply Button\"],\n", "    [\"DIRECT_SNAP_SEND_IN_CHAT_UU\",\"Snap Sender in Chat Page\"],\n", "    [\"DIRECT_SNAP_SEND_IN_CHAT_from_ACTION_MENU_UU\",\"Snap Sender in Chat Page from Action Menu \"],\n", "    [\"DIRECT_SNAP_SEND_IN_CHAT_from_DOUBLE_TAP_UU\",\"Snap Sender from in Chat Page Double Tap \"],\n", "    [\"DIRECT_SNAP_SEND_MINI_PROFILE_UU\",\"Snap Sender from Mini Profile\"],\n", "    [\"DIRECT_SNAP_SEND_SEARCH_UU\",\"Snap Sender from Search\"],\n", "    [\"DIRECT_SNAP_SEND_with_SNAP_REPLY_STICKER_UU\",\"Snap Sender from Snap Reply Sticker\"],\n", "    [\"DIRECT_SNAP_VIEW_FROM_FEED_UU\",\"Snap Viewer from Feed\"],\n", "    [\"DIRECT_SNAP_VIEW_FROM_CHAT_UU\",\"Snap Viewer from Chat\"],\n", "])\n", "\n", "for mt in report.metric_tables:\n", "    for metric in mt.metrics:\n", "        if metric.col in METRICS_NAME_MAPPING:\n", "            metric.name = METRICS_NAME_MAPPING[metric.col]"]}, {"metadata": {}, "cell_type": "markdown", "source": "## Generate report"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["abtest.report.notebook_print(\"Study Name: {}\".format(report.study_name), \"h3\")\n", "abtest.report.notebook_print(\"Control ID: {}\".format(report.control_id), \"strong\")\n", "abtest.report.notebook_print(\"Treatment ID(s): {}\".format(\", \".join(report.treatment_ids)), \"strong\")\n", "abtest.report.notebook_print(\n", "    \"Analysis period: {} - {}\".format(report.analysis_start_date, report.study_end_date), \n", "    \"strong\"\n", ")\n", "abtest.report.notebook_print(\n", "    \"Breakdowns: <br> {}\".format(\n", "        \"<br>\".join(\", \".join(breakdown) for breakdown in report.user_group_by_list)\n", "    ), \n", "    \"strong\"\n", ")"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["def get_header_str(metric_group, sep=\" \"):\n", "    return metric_group.replace(\"_\", sep).upper()\n", "\n", "display(HTML(\"<h2> Metric Groups </h2>\"))\n", "display(HTML(\"<h3> Overall </h3>\"))\n", "for metric_group in metrics_group_config_additional:\n", "    display(HTML(\"<a href=\\\"#{html_id}-{breakdown_str}\\\">{header}</a>\".format(\n", "        html_id=get_header_str(metric_group, \"-\"), \n", "        breakdown_str=\"OVERALL\",\n", "        header=get_header_str(metric_group))))\n", "if len(USER_BREAKDOWN_LIST)>0:\n", "    display(HTML(\"<h3> Breakdowns </h3>\"))\n", "    for metric_group in metrics_group_config_additional:\n", "        display(HTML(\"<a href=\\\"#{html_id}-{breakdown_str}\\\">{header}</a>\".format(\n", "            html_id=get_header_str(metric_group, \"-\"), \n", "            breakdown_str='BREAKDOWNS',\n", "            header=get_header_str(metric_group))))"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'chat_metrics' in METRICS_GROUP_CONFIG: \n", "\n", "    chat_metrics = [m for mt in ALL_METRICS_GROUPS['chat_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    # Chat Detail Metrics\n", "    report.ab_printer.print_text(\"Chat Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': chat_metrics\n", "        },\n", "    );\n", "\n", "else: \n", "    pass     "]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'chat_detail_metrics' in METRICS_GROUP_CONFIG: \n", "\n", "    chat_detail_metrics = [m for mt in ALL_METRICS_GROUPS['chat_detail_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    # Chat Detail Metrics\n", "    report.ab_printer.print_text(\"Chat Detail Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': chat_detail_metrics\n", "        },\n", "    );\n", "\n", "else: \n", "    pass     "]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'snap_detail_metrics' in METRICS_GROUP_CONFIG: \n", "\n", "    snap_detail_metrics = [m for mt in ALL_METRICS_GROUPS['snap_detail_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    # Detailed Snap Engagement Metrics\n", "    report.ab_printer.print_text(\"Snap Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': snap_detail_metrics\n", "        },\n", "    );\n", "\n", "else: \n", "    pass  "]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'snap_save_delete_metrics' in METRICS_GROUP_CONFIG: \n", "    \n", "    snap_save_delete_metrics = [m for mt in ALL_METRICS_GROUPS['snap_save_delete_metrics'] for m in mt.cumulative_metrics ]\n", "    report.ab_printer.print_text(\"Snap Save and Delete Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': snap_save_delete_metrics\n", "        },\n", "    );           "]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'snap_save_delete_metrics' in METRICS_GROUP_CONFIG: \n", "\n", "    snap_save_delete_metrics = [m for mt in ALL_METRICS_GROUPS['snap_save_delete_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    # Snap Save Detail Metrics\n", "    report.ab_printer.print_text(\"Saving Snap and Delete Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': snap_save_delete_metrics\n", "        },\n", "    );\n", "\n", "else: \n", "    pass     "]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'chat_save_metrics' in METRICS_GROUP_CONFIG: \n", "    \n", "    chat_save_metrics = [m for mt in ALL_METRICS_GROUPS['chat_save_metrics'] for m in mt.cumulative_metrics ]\n", "    report.ab_printer.print_text(\"Chat Save, Unsave and Delete Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': chat_save_metrics\n", "        },\n", "    );  "]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'new_chat_page' in METRICS_GROUP_CONFIG:\n", "\n", "    new_chat_page = [m for mt in ALL_METRICS_GROUPS['new_chat_page'] for m in mt.cumulative_metrics]\n", "    \n", "    # New Chat Page\n", "    report.ab_printer.print_text(\"New Chat Page\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': new_chat_page\n", "        },\n", "    );\n", "\n", "else: \n", "    pass "]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'voice_notes' in METRICS_GROUP_CONFIG:\n", "\n", "    voice_notes = [m for mt in ALL_METRICS_GROUPS['voice_notes'] for m in mt.cumulative_metrics]\n", "    \n", "    # Voice Notes\n", "    report.ab_printer.print_text(\"Voice Notes\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': voice_notes\n", "        },\n", "    );\n", "\n", "else: \n", "    pass "]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'share_extension_metrics' in METRICS_GROUP_CONFIG: \n", "        report.ab_printer.print_text(\"Share Extension Metrics\", 'h2')\n", "        share_extension_metrics = [m for mt in ALL_METRICS_GROUPS['share_extension_metrics'] for m in mt.cumulative_metrics if 'via' not in m]\n", "        report.generate_report(\n", "            format_pvalue=True,\n", "            extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "            display_config={'cumulative': ['table'], 'daily': daily_display},\n", "            metric_filters = {\n", "                'metrics': share_extension_metrics\n", "            },\n", "        ); \n", "\n", "else: \n", "    pass "]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'non_friend_metrics' in METRICS_GROUP_CONFIG:\n", "\n", "    non_friend_metrics = [m for mt in ALL_METRICS_GROUPS['non_friend_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    report.ab_printer.print_text(\"Messaging Metrics for Non Friend Users\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': non_friend_metrics\n", "        },\n", "    );\n", "\n", "else: \n", "    pass "]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'chat_erase_mode_metrics' in METRICS_GROUP_CONFIG:\n", "\n", "    chat_erase_mode_metrics = [m for mt in ALL_METRICS_GROUPS['chat_erase_mode_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    report.ab_printer.print_text(\"Chat Erase Mode Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': chat_erase_mode_metrics\n", "        },\n", "    );\n", "\n", "else: \n", "    pass "]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'friend_feed_shortcut_metrics' in METRICS_GROUP_CONFIG:\n", "\n", "    friend_feed_shortcut_metrics = [m for mt in ALL_METRICS_GROUPS['friend_feed_shortcut_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    report.ab_printer.print_text(\"Friend Feed Shortcut Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': friend_feed_shortcut_metrics\n", "        },\n", "    );\n", "\n", "else: \n", "    pass "]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'relationship_closeness_metrics' in METRICS_GROUP_CONFIG:\n", "\n", "    relationship_closeness_metrics = [m for mt in ALL_METRICS_GROUPS['relationship_closeness_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    report.ab_printer.print_text(\"Relationship Closeness Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': relationship_closeness_metrics\n", "        },\n", "    );\n", "\n", "else: \n", "    pass \n"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'notification_platform_metrics_overall' in METRICS_GROUP_CONFIG:\n", "\n", "    notification_platform_metrics_overall = [m for mt in ALL_METRICS_GROUPS['notification_platform_metrics_overall'] for m in mt.cumulative_metrics]\n", "    \n", "    report.ab_printer.print_text(\"Notification Platform Overall Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': notification_platform_metrics_overall\n", "        },\n", "    );\n", "\n", "else: \n", "    pass \n"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'notification_platform_metrics_by_type' in METRICS_GROUP_CONFIG: \n", "    \n", "    group_vars = []\n", "    # metric_breakdown=report.results[\"cumulative\"].metric_breakdown_values.unique()[:-1]\n", "    # print(metric_breakdown)\n", "    metric_breakdown = np.array([\"notif_type\"])\n", "\n", "    notification_platform_metrics_by_type = [m for mt in ALL_METRICS_GROUPS['notification_platform_metrics_by_type'] for m in mt.cumulative_metrics]\n", "\n", "    notif_platform = report.get_results(\n", "        \"cumulative\",\n", "        group_vars=group_vars,\n", "        exclude_field_breakdown=False,\n", "        metric_filters={'metrics': notification_platform_metrics_by_type,\n", "                            'metric_breakdown_dimensions':metric_breakdown}\n", "        )\n", "\n", "    report.ab_printer.print_text(\"Notification Platform Metrics By Type\", 'h2')\n", "    report.visualize(\n", "        notif_platform,\n", "        group_vars=group_vars,\n", "        format_pvalue=True,\n", "        show_table=True,\n", "        table_format='table',\n", "        should_pivot_table=True,        \n", "        pivot_table_rows=['Date', 'Metric', 'metric_breakdown_values'] + group_vars[:-1],\n", "        pivot_table_cols=['control_id', 'treatment_id'] + group_vars[-1:],    \n", "        );\n", "\n", "    report.configurations[\"pivot_table\"] = True\n", "    if len(USER_BREAKDOWN_LIST)>0:\n", "        for user_group_bys in report.user_group_by_list:\n", "            report.ab_printer.print_text('Breakdown by user groups: {}'.format(\", \".join(user_group_bys)),'h5')\n", "            group_vars = []\n", "            notif_platform = report.get_results(\n", "                \"cumulative\",\n", "                group_vars=user_group_bys,\n", "                exclude_field_breakdown=False,\n", "                metric_filters={'metrics': notification_platform_metrics_by_type,\n", "                                'metric_breakdown_dimensions':metric_breakdown}\n", "            )\n", "\n", "            report.visualize(\n", "                notif_platform,\n", "                group_vars=group_vars,\n", "                format_pvalue=True,\n", "                show_table=True,\n", "                table_format='table',\n", "                should_pivot_table=True,        \n", "                pivot_table_rows=['Date', 'Metric', 'metric_breakdown_values'] + group_vars[:-1],\n", "                pivot_table_cols=['control_id', 'treatment_id', user_group_bys[0]] + group_vars[-1:],    \n", "            );"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'notification_to_message_ready_failure_metrics' in METRICS_GROUP_CONFIG:\n", "\n", "    notification_to_message_ready_failure_metrics = [m for mt in ALL_METRICS_GROUPS['notification_to_message_ready_failure_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    report.ab_printer.print_text(\"Notification to Message Ready Failure Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': notification_to_message_ready_failure_metrics\n", "        },\n", "    );\n", "\n", "else: \n", "    pass "]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'notification_os_breakdown_metrics' in METRICS_GROUP_CONFIG:\n", "\n", "    notification_os_breakdown_metrics = [m for mt in ALL_METRICS_GROUPS['notification_os_breakdown_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    report.ab_printer.print_text(\"Notification Web vs. Mobile Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': notification_os_breakdown_metrics\n", "        },\n", "    );\n", "\n", "else: \n", "    pass "]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'friend_feed_state_metrics' in metrics_group_config_additional:\n", "\n", "    friend_feed_state_metrics = [m for mt in ALL_METRICS_GROUPS['friend_feed_state_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    report.ab_printer.print_text(\"Friend Feed State Metrics - Friend Fe<PERSON> Page Sessions\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': friend_feed_state_metrics\n", "        },\n", "    );\n", "\n", "else: \n", "    pass "]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'friend_feed_state_convo_metrics' in metrics_group_config_additional:\n", "\n", "    friend_feed_state_convo_metrics = [m for mt in ALL_METRICS_GROUPS['friend_feed_state_convo_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    report.ab_printer.print_text(\"Friend Feed State Metrics - Friend Feed Convo and Cell State\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': friend_feed_state_convo_metrics\n", "        },\n", "    );\n", "\n", "else: \n", "    pass "]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'friend_feed_state_active_day_metrics' in metrics_group_config_additional:\n", "\n", "    friend_feed_state_active_day_metrics = [m for mt in ALL_METRICS_GROUPS['friend_feed_state_active_day_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    report.ab_printer.print_text(\"Friend Feed State Metrics - Friend Feed State Active Day\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': friend_feed_state_active_day_metrics\n", "        },\n", "    );\n", "\n", "else: \n", "    pass "]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'friend_feed_page_view_metrics' in metrics_group_config_additional:\n", "\n", "    friend_feed_page_view_metrics = [m for mt in ALL_METRICS_GROUPS['friend_feed_page_view_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    report.ab_printer.print_text(\"Friend Feed Page View Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': friend_feed_page_view_metrics\n", "        },\n", "    );\n", "\n", "else: \n", "    pass "]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'friend_feed_state_detail_metrics' in METRICS_GROUP_CONFIG:\n", "\n", "    friend_feed_state_detail_metrics = [m for mt in ALL_METRICS_GROUPS['friend_feed_state_detail_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    report.ab_printer.print_text(\"Friend Feed State Detailed Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': friend_feed_state_detail_metrics\n", "        },\n", "    );\n", "\n", "else: \n", "    pass "]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'friend_feed_state_cell_position_metrics' in METRICS_GROUP_CONFIG:\n", "\n", "    friend_feed_state_cell_position_metrics = [m for mt in ALL_METRICS_GROUPS['friend_feed_state_cell_position_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    report.ab_printer.print_text(\"Friend Feed State Cell Position Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': friend_feed_state_cell_position_metrics\n", "        },\n", "    );\n", "\n", "else: \n", "    pass "]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'friend_feed_ppv_metrics' in metrics_group_config_additional:\n", "\n", "    friend_feed_ppv_metrics = [m for mt in ALL_METRICS_GROUPS['friend_feed_ppv_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    report.ab_printer.print_text(\"Page Page View Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': friend_feed_ppv_metrics\n", "        },\n", "    );\n", "\n", "else: \n", "    pass "]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'sponsored_snap_metrics' in METRICS_GROUP_CONFIG:\n", "\n", "    sponsored_snap_metrics = [m for mt in ALL_METRICS_GROUPS['sponsored_snap_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    report.ab_printer.print_text(\"Sponsored Snap Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': sponsored_snap_metrics\n", "        },\n", "    );\n", "\n", "else: \n", "    pass "]}, {"metadata": {}, "cell_type": "markdown", "source": "# Quantile Metrics"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ""}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# ALL_METRICS_GROUPS['chat_send_to_view_time_metrics'] = [msg.chat_send_to_view_time_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "\n", "# if 'chat_send_to_view_time_metrics' in METRICS_GROUP_CONFIG: \n", "    \n", "#     report_quantile = CohortReport(\n", "#         study_name=STUDY_NAME,\n", "#         study_start_date=STUDY_START_DATE,\n", "#         study_end_date=STUDY_END_DATE,\n", "#         metric_tables=ALL_METRICS_GROUPS['chat_send_to_view_time_metrics'],\n", "#         control_id=CONTROL_ID,\n", "#         treatment_ids=TREATMENT_IDS,\n", "#         bq_project='sc-bq-gcs-billingonly',  \n", "#         dest_dataset='temp_abtest',\n", "#         quantiles=quantiles,\n", "#     )\n", "    \n", "#     report_quantile.configurations['stat_fmtr'] = (\n", "#     \"'P{quantile}':: {pct_diff:,.2f}% ({quantile_control:,.3}→{quantile_treatment:,.3}, {p_value_formatted})\"\n", "#     )\n", "    \n", "#     report_quantile.execute(\n", "#     overwrite=False,\n", "#     )\n", "\n", "# else:\n", "#     pass"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# if 'chat_send_to_view_time_metrics' in METRICS_GROUP_CONFIG: \n", "#     # Chat Send-to-View Time Metrics\n", "#     chat_send_to_view_time_metrics = [m for mt in ALL_METRICS_GROUPS['chat_send_to_view_time_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "#     report.ab_printer.print_text(\"Chat Send-to-View Time Metrics\", 'h2')\n", "#     report_quantile.generate_report(\n", "#         extra_table_cols=['treatment_id', 'quantile', \n", "#                           'quantile_control', 'quantile_treatment',\n", "#                           'count_control', \n", "#                          ],\n", "#         facet_bys=['treatment_id', 'quantile'],\n", "#         format_pvalue=True,\n", "#     );\n", "\n", "# else:\n", "#     pass"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["ALL_METRICS_GROUPS['notification_to_message_ready_latency_metrics'] = [msg.notification_to_message_ready_latency_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "\n", "if 'notification_to_message_ready_latency_metrics' in METRICS_GROUP_CONFIG: \n", "    \n", "    report_quantile = CohortReport(\n", "        study_name=STUDY_NAME,\n", "        study_start_date=STUDY_START_DATE,\n", "        study_end_date=STUDY_END_DATE,\n", "        metric_tables=ALL_METRICS_GROUPS['notification_to_message_ready_latency_metrics'],\n", "        control_id=CONTROL_ID,\n", "        treatment_ids=TREATMENT_IDS,\n", "        bq_project='sc-bq-gcs-billingonly',  \n", "        dest_dataset='temp_abtest',\n", "        quantiles=quantiles,\n", "    )\n", "    \n", "    report_quantile.configurations['stat_fmtr'] = (\n", "    \"'P{quantile}':: {pct_diff:,.2f}% ({quantile_control:,.3}→{quantile_treatment:,.3}, {p_value_formatted})\"\n", "    )\n", "    \n", "    report_quantile.execute(\n", "    overwrite=False,\n", "    )\n", "\n", "else:\n", "    pass"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if 'notification_to_message_ready_latency_metrics' in METRICS_GROUP_CONFIG: \n", "    # N2X Latency Metrics\n", "    notification_to_message_ready_latency_metrics = [m for mt in ALL_METRICS_GROUPS['notification_to_message_ready_latency_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    report.ab_printer.print_text(\"Notification to Message Ready Latency Metrics\", 'h2')\n", "    report_quantile.generate_report(\n", "        extra_table_cols=['treatment_id', 'quantile', \n", "                          'quantile_control', 'quantile_treatment',\n", "                          'count_control', \n", "                         ],\n", "        facet_bys=['treatment_id', 'quantile'],\n", "        format_pvalue=True,\n", "    );\n", "\n", "else:\n", "    pass"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# report.ab_printer.print_text(\"Sample Sizes\", 'h2')\n", "# report.generate_report(\n", "#     format_pvalue=True,\n", "#     display_config={'cumulative': ['table'], 'daily': []},\n", "#     metric_filters={'metrics': ['tier1_active_days'], 'regex': True},\n", "#     stat_fmtr=\"Users in control: {count_control:,.0f} <br>Users in treatment: {count_treatment:,.0f}\", \n", "# );"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ""}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ""}], "metadata": {"anaconda-cloud": {}, "celltoolbar": "Tags", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}