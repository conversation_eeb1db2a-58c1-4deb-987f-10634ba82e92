{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Messaging Frieds Feed AB Notebook"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Notebook Config"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Specify study details\n", "STUDY_NAME = 'FF_CONDENSED_CELL_ANDROID__118019'\n", "STUDY_START_DATE = '20241210'\n", "STUDY_END_DATE = '20241210'\n", "CONTROL_ID = '1'\n", "TREATMENT_IDS = ['2']\n", "\n", "EXP_NAMES = {\n", "}\n", "\n", "# The date to use for user breakdown attributes. Defaults to two days before study start date.\n", "COHORT_DATE = None\n", "CUMULATIVE_TREND= False\n", "OVERWRITE = False\n", "DAILY_TREND = False\n", "MATERIALIZE_MAPPING_TABLE = True\n", "\n", "USER_BREAKDOWN_LIST = []\n", "# 'communication_engagement_status'\n", "quantiles = ['10','25','50','90']\n", "\n", "# Running config\n", "# BQ_PRIORITY = 'INTERACTIVE'\n", "\n", "# Need to make 'engagement metrics' fixed and not able to unselect\n", "METRICS_GROUP_CONFIG = [\n", "      'friend_feed_state_metrics',\n", "      'friend_feed_state_detail_metrics',\n", "      'friend_feed_state_cell_position_metrics',\n", "      'friend_feed_state_convo_metrics',\n", "      'friend_feed_state_active_day_metrics',\n", "      'friend_feed_page_view_metrics',\n", "      'friend_feed_ppv_metrics']\n", "\n", "# Running config\n", "USE_BATCH_BQ_PRIORITY = True\n", "\n", "SUPPLEMENT_NOTIF_TYPES = \"\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["import warnings\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:banjo.teams.product.messaging:Messaging metrics imported.  Check the actual queries run to ensure correctness\n"]}, {"data": {"text/html": ["<style>.output_html.rendered_html table { font-size:8pt;}</style>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from __future__ import division, unicode_literals, print_function\n", "import os\n", "if any(['VELLUM' in k for k in os.environ.keys()]):\n", "    pip_output = !pip install banjo\n", "\n", "import seaborn as sns\n", "import pandas as pd\n", "import logging\n", "import numpy as np\n", "logging.basicConfig()\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "%matplotlib inline\n", "%config InlineBackend.figure_format = 'retina'\n", "\n", "from banjo import abtest, utils\n", "from banjo.abtest.report import (\n", "    Metric, MetricTable, Report, get_quest_metric_table, CustomReport, \n", "    get_abtest_console_metric_table\n", ")\n", "from banjo.abtest.cohort_report import CohortReport\n", "from banjo.abtest.quest import TIER_ONE\n", "from banjo.abtest.field_breakdown_metric_table import FieldBreakdownMetricTable\n", "\n", "try:\n", "    from banjo.teams.product import messaging as msg\n", "\n", "except ImportError:\n", "    GIT_DIR = os.path.join(os.environ['HOME'])\n", "    os.chdir(GIT_DIR)\n", "    print(\"Upgrade banjo to 0.14.0+, which packages all these scripts\")\n", "\n", "# try:\n", "#     from banjo.teams.product import camera_growth as cam\n", "\n", "# except ImportError:\n", "#     GIT_DIR = os.path.join(os.environ['HOME'])\n", "#     os.chdir(GIT_DIR)\n", "#     print(\"Upgrade banjo to 0.14.0+, which packages all these scripts\")\n", "#     import camera_growth as cam\n", "    \n", "    \n", "PROJECT = 'sc-bq-gcs-billingonly'\n", "sns.set(style='whitegrid', font_scale=1.5)\n", "\n", "from IPython.core.display import display, HTML\n", "display(HTML(\"<style>.output_html.rendered_html table { font-size:8pt;}</style>\"))\n", "\n", "# decide if print daily trend\n", "daily_display = []\n", "if DAILY_TREND:\n", "    daily_display = ['trend']\n", "    "]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:messaging:Messaging metrics imported.  Check the actual queries run to ensure correctness\n"]}], "source": ["# import messaging as msg"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Ensure certain items passed from husky are lists\n", "def ensure_list(parameter):\n", "    if isinstance(parameter, list):\n", "        return parameter\n", "    else:\n", "        return [parameter]\n", "\n", "TREATMENT_IDS = ensure_list(TREATMENT_IDS)\n", "USER_BREAKDOWN_LIST = ensure_list(USER_BREAKDOWN_LIST)\n", "BQ_PRIORITY = \"BATCH\" if USE_BATCH_BQ_PRIORITY else \"INTERACTIVE\"\n", "METRICS_GROUP_CONFIG = ensure_list(METRICS_GROUP_CONFIG)   \n", "# metrics_group_config_additional = ensure_list(metrics_group_config_additional)  \n", "\n", "ALL_METRICS_GROUPS = {}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Add metrics"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["ALL_METRICS_GROUPS['friend_feed_state_metrics']=[msg.friend_feed_state_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['friend_feed_state_convo_metrics']=[msg.friend_feed_state_convo_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['friend_feed_state_active_day_metrics']=[msg.friend_feed_state_active_day_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['friend_feed_state_detail_metrics']=[msg.friend_feed_state_detail_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['friend_feed_state_cell_position_metrics']=[msg.friend_feed_state_cell_position_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['friend_feed_page_view_metrics']=[msg.friend_feed_page_view_metrics(STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['friend_feed_ppv_metrics']=[msg.friend_feed_ppv_metrics(STUDY_START_DATE, STUDY_END_DATE)]"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["metric_tables = [\n", "    metric_table \n", "    for sublist in [ALL_METRICS_GROUPS[mt] for mt in ALL_METRICS_GROUPS if mt in METRICS_GROUP_CONFIG]\n", "    for metric_table in sublist\n", "]\n", "# print(metric_tables)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# Get Experiment Names\n", "study_name_sql = \"\"\"\n", "    SELECT\n", "      name,\n", "      exp_id, \n", "      study_version\n", "    FROM\n", "      `sc-analytics.report_search.ab_console_study_config*`\n", "    WHERE\n", "      study_name = '{study_name}'\n", "      AND exp_id IN ('{exp_ids}')\n", "\"\"\"\n", "df_exp_names = utils.gbq.read_gbq(\n", "    study_name_sql.format(\n", "        study_name=STUDY_NAME, exp_ids=\"', '\".join([CONTROL_ID] + TREATMENT_IDS),\n", "    ),\n", "    dialect='standard',\n", "    project_id=PROJECT)\n", "if not EXP_NAMES and not df_exp_names.empty:\n", "    EXP_NAMES = dict(zip(df_exp_names.exp_id, df_exp_names.name))"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:banjo.abtest.report:Overwrite study_start_date to 20241115 from metadata. analysis_start_date still uses the supplied value of 20241210\n", "WARNING:banjo.abtest.report:Cohort definition date not provided, defaulting to one day before study start date 20241114\n", "WARNING:root:5 duplicated rows dropped\n", "WARNING:root:5 duplicated rows dropped\n"]}], "source": ["report = CohortReport(\n", "    study_name=STUDY_NAME,\n", "    study_start_date=STUDY_START_DATE,\n", "    study_end_date=STUDY_END_DATE,\n", "    metric_tables=metric_tables,\n", "    control_id=CONTROL_ID,\n", "    treatment_ids=TREATMENT_IDS,\n", "    user_group_bys=USER_BREAKDOWN_LIST,\n", "    bq_project='sc-bq-gcs-billingonly',  \n", "    dest_dataset='temp_abtest',\n", "    cohort_definition_date=COHORT_DATE,\n", "    materializing_mapping_table=MATERIALIZE_MAPPING_TABLE,\n", "    overwrite_mapping_table=OVERWRITE,\n", "    bq_priority=USE_BATCH_BQ_PRIORITY,\n", "    exp_id_to_name=EXP_NAMES,\n", ")\n", "\n", "report.configurations['pivot_table'] = True\n", "report.configurations['stat_fmtr'] = (\n", "    \"{pct_diff:,.2f}% ({avg_control:,.3}→{avg_treatment:,.3}, {p_value_formatted})\"\n", ")\n", "\n", "# Run the joins and calculate the results\n", "report.execute(\n", "   overwrite=OVERWRITE, \n", "   cumulative_trend=CUMULATIVE_TREND,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate report"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<h3 >Study Name: FF_CONDENSED_CELL_ANDROID__118019</h3>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<strong >Control ID: 1</strong>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<strong >Treatment ID(s): 2</strong>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<strong >Analysis period: 20241210 - 20241210</strong>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<strong >Breakdowns: <br> </strong>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["abtest.report.notebook_print(\"Study Name: {}\".format(report.study_name), \"h3\")\n", "abtest.report.notebook_print(\"Control ID: {}\".format(report.control_id), \"strong\")\n", "abtest.report.notebook_print(\"Treatment ID(s): {}\".format(\", \".join(report.treatment_ids)), \"strong\")\n", "abtest.report.notebook_print(\n", "    \"Analysis period: {} - {}\".format(report.analysis_start_date, report.study_end_date), \n", "    \"strong\"\n", ")\n", "abtest.report.notebook_print(\n", "    \"Breakdowns: <br> {}\".format(\n", "        \"<br>\".join(\", \".join(breakdown) for breakdown in report.user_group_by_list)\n", "    ), \n", "    \"strong\"\n", ")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<h2> Metric Groups </h2>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3> Overall </h3>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<a href=\"#FRIEND-FEED-STATE-METRICS-OVERALL\">FRIEND FEED STATE METRICS</a>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<a href=\"#FRIEND-FEED-STATE-DETAIL-METRICS-OVERALL\">FRIEND FEED STATE DETAIL METRICS</a>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<a href=\"#FRIEND-FEED-STATE-CELL-POSITION-METRICS-OVERALL\">FRIEND FEED STATE CELL POSITION METRICS</a>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<a href=\"#FRIEND-FEED-STATE-CONVO-METRICS-OVERALL\">FRIEND FEED STATE CONVO METRICS</a>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<a href=\"#FRIEND-FEED-STATE-ACTIVE-DAY-METRICS-OVERALL\">FRIEND FEED STATE ACTIVE DAY METRICS</a>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<a href=\"#FRIEND-FEED-PAGE-VIEW-METRICS-OVERALL\">FRIEND FEED PAGE VIEW METRICS</a>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<a href=\"#FRIEND-FEED-PPV-METRICS-OVERALL\">FRIEND FEED PPV METRICS</a>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def get_header_str(metric_group, sep=\" \"):\n", "    return metric_group.replace(\"_\", sep).upper()\n", "\n", "display(HTML(\"<h2> Metric Groups </h2>\"))\n", "display(HTML(\"<h3> Overall </h3>\"))\n", "for metric_group in METRICS_GROUP_CONFIG:\n", "    display(HTML(\"<a href=\\\"#{html_id}-{breakdown_str}\\\">{header}</a>\".format(\n", "        html_id=get_header_str(metric_group, \"-\"), \n", "        breakdown_str=\"OVERALL\",\n", "        header=get_header_str(metric_group))))\n", "if len(USER_BREAKDOWN_LIST)>0:\n", "    display(HTML(\"<h3> Breakdowns </h3>\"))\n", "    for metric_group in METRICS_GROUP_CONFIG:\n", "        display(HTML(\"<a href=\\\"#{html_id}-{breakdown_str}\\\">{header}</a>\".format(\n", "            html_id=get_header_str(metric_group, \"-\"), \n", "            breakdown_str='BREAKDOWNS',\n", "            header=get_header_str(metric_group))))"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<h2 >Friend Feed State Metrics - Friend Feed Page Sessions</h2>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Overall</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Comparison of user overall (cumulative) metric values during the study period</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p ><style  type=\"text/css\" >\n", "#T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54row13_col0{\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #006400;\n", "        }</style><table id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54\" ><thead>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Statistic</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level1\" >control_id</th>        <th class=\"col_heading level1 col0\" >1 (Control)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level2\" >treatment_id</th>        <th class=\"col_heading level2 col0\" >2 (54 with normal ring)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level3\" >Sample Sizes</th>        <th class=\"col_heading level3 col0\" >Treatment=3,020,134<br>\n", "Control=3,019,015<br>\n", "No SSPM (p=0.6489)</th>    </tr>    <tr>        <th class=\"index_name level0\" >Date</th>        <th class=\"index_name level1\" >Metric</th>        <th class=\"blank\" ></th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54level0_row0\" class=\"row_heading level0 row0\" rowspan=23>12/10 to 12/10</th>\n", "                        <th id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54level1_row0\" class=\"row_heading level1 row0\" >Ff Page Session Count</th>\n", "                        <td id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54row0_col0\" class=\"data row0 col0\" >0.49% (0.293→0.294, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54level1_row1\" class=\"row_heading level1 row1\" >App Session Count W Ff Page Open</th>\n", "                        <td id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54row1_col0\" class=\"data row1 col0\" >0.78% (0.217→0.218, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54level1_row2\" class=\"row_heading level1 row2\" >Ff Page Session Per App Session</th>\n", "                        <td id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54row2_col0\" class=\"data row2 col0\" >-0.29% (1.35→1.35, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54level1_row3\" class=\"row_heading level1 row3\" >Ff Page Session W No Unread Convo</th>\n", "                        <td id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54row3_col0\" class=\"data row3 col0\" >-1.35% (0.122→0.12, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54level1_row4\" class=\"row_heading level1 row4\" >Ff Page Session W 1 Plus Unread Convo</th>\n", "                        <td id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54row4_col0\" class=\"data row4 col0\" >1.69% (0.175→0.178, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54level1_row5\" class=\"row_heading level1 row5\" >Ff Page Session W No Unread Chat</th>\n", "                        <td id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54row5_col0\" class=\"data row5 col0\" >-0.30% (0.211→0.21, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54level1_row6\" class=\"row_heading level1 row6\" >Ff Page Session W 1 Plus Unread Chat</th>\n", "                        <td id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54row6_col0\" class=\"data row6 col0\" >2.25% (0.0862→0.0881, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54level1_row7\" class=\"row_heading level1 row7\" >Ff Page Session W 6 Plus Unread Chat</th>\n", "                        <td id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54row7_col0\" class=\"data row7 col0\" >-4.98% (0.00367→0.00349, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54level1_row8\" class=\"row_heading level1 row8\" >Ff Page Session W No Unread Snap</th>\n", "                        <td id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54row8_col0\" class=\"data row8 col0\" >-0.29% (0.163→0.162, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54level1_row9\" class=\"row_heading level1 row9\" >Ff Page Session W 1 Plus Unread Snap</th>\n", "                        <td id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54row9_col0\" class=\"data row9 col0\" >1.32% (0.134→0.136, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54level1_row10\" class=\"row_heading level1 row10\" >Ff Page Session W 6 Plus Unread Snap</th>\n", "                        <td id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54row10_col0\" class=\"data row10 col0\" >1.73% (0.0694→0.0706, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54level1_row11\" class=\"row_heading level1 row11\" >Ff Page Session W No Visible Cell</th>\n", "                        <td id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54row11_col0\" class=\"data row11 col0\" >0.15% (0.0234→0.0235, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54level1_row12\" class=\"row_heading level1 row12\" >Ff Page Session W 1 Plus Visible Cell</th>\n", "                        <td id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54row12_col0\" class=\"data row12 col0\" >0.36% (0.273→0.274, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54level1_row13\" class=\"row_heading level1 row13\" >Ff Page Session W 11 Plus Visible Cell</th>\n", "                        <td id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54row13_col0\" class=\"data row13 col0\" >145.04% (0.0231→0.0567, < 0.0001)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54level1_row14\" class=\"row_heading level1 row14\" >Ff Page Session W Page View Id 1</th>\n", "                        <td id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54row14_col0\" class=\"data row14 col0\" >-0.53% (0.0276→0.0275, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54level1_row15\" class=\"row_heading level1 row15\" >Ff Page Session W Page View Id 2</th>\n", "                        <td id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54row15_col0\" class=\"data row15 col0\" >0.66% (0.0543→0.0546, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54level1_row16\" class=\"row_heading level1 row16\" >Ff Page Session W Page View Id 3 Plus</th>\n", "                        <td id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54row16_col0\" class=\"data row16 col0\" >0.57% (0.211→0.212, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54level1_row17\" class=\"row_heading level1 row17\" >Ff Page Session W 1 Convo Pinned</th>\n", "                        <td id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54row17_col0\" class=\"data row17 col0\" >1.63% (0.0899→0.0913, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54level1_row18\" class=\"row_heading level1 row18\" >Ff Page Session W 2 Convo Pinned</th>\n", "                        <td id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54row18_col0\" class=\"data row18 col0\" >5.25% (0.0177→0.0186, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54level1_row19\" class=\"row_heading level1 row19\" >Ff Page Session W 3 Convo Pinned</th>\n", "                        <td id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54row19_col0\" class=\"data row19 col0\" >2.52% (0.0149→0.0152, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54level1_row20\" class=\"row_heading level1 row20\" >Ff Page Session W 4 Plus Convo Pinned</th>\n", "                        <td id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54row20_col0\" class=\"data row20 col0\" >-4.82% (0.0105→0.00995, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54level1_row21\" class=\"row_heading level1 row21\" >Ff Page Session App Ui 5 Tab</th>\n", "                        <td id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54row21_col0\" class=\"data row21 col0\" >0.17% (0.282→0.282, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54level1_row22\" class=\"row_heading level1 row22\" >Ff Page Session App Ui 3 Tab</th>\n", "                        <td id=\"T_0c283770_bbf9_11ef_aa96_6ebf32c5fb54row22_col0\" class=\"data row22 col0\" >3.09% (0.0147→0.0152, ≥ 0.1)</td>\n", "            </tr>\n", "    </tbody></table></p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if 'friend_feed_state_metrics' in METRICS_GROUP_CONFIG:\n", "\n", "    friend_feed_state_metrics = [m for mt in ALL_METRICS_GROUPS['friend_feed_state_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    report.ab_printer.print_text(\"Friend Feed State Metrics - Friend Fe<PERSON> Page Sessions\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': friend_feed_state_metrics\n", "        },\n", "    );\n", "\n", "else: \n", "    pass "]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<h2 >Friend Feed State Detailed Metrics</h2>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Overall</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Comparison of user overall (cumulative) metric values during the study period</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p ><style  type=\"text/css\" >\n", "#T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row3_col0,#T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row10_col0,#T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row41_col0,#T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row45_col0,#T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row46_col0,#T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row61_col0{\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #99CC99;\n", "        }#T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row32_col0,#T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row51_col0{\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #F89999;\n", "        }#T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row33_col0,#T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row34_col0,#T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row35_col0,#T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row36_col0,#T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row37_col0{\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #B10101;\n", "        }#T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row38_col0,#T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row39_col0,#T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row53_col0{\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #006400;\n", "        }</style><table id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54\" ><thead>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Statistic</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level1\" >control_id</th>        <th class=\"col_heading level1 col0\" >1 (Control)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level2\" >treatment_id</th>        <th class=\"col_heading level2 col0\" >2 (54 with normal ring)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level3\" >Sample Sizes</th>        <th class=\"col_heading level3 col0\" >Treatment=3,020,134<br>\n", "Control=3,019,015<br>\n", "No SSPM (p=0.6489)</th>    </tr>    <tr>        <th class=\"index_name level0\" >Date</th>        <th class=\"index_name level1\" >Metric</th>        <th class=\"blank\" ></th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level0_row0\" class=\"row_heading level0 row0\" rowspan=70>12/10 to 12/10</th>\n", "                        <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row0\" class=\"row_heading level1 row0\" >Ff Page Session W No New Unread Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row0_col0\" class=\"data row0 col0\" >-0.51% (0.19→0.189, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row1\" class=\"row_heading level1 row1\" >Ff Page Session W 1 New Unread Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row1_col0\" class=\"data row1 col0\" >1.74% (0.0541→0.0551, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row2\" class=\"row_heading level1 row2\" >Ff Page Session W 2 New Unread Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row2_col0\" class=\"data row2 col0\" >2.58% (0.0141→0.0145, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row3\" class=\"row_heading level1 row3\" >Ff Page Session W 3 New Unread Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row3_col0\" class=\"data row3 col0\" >3.92% (0.00672→0.00698, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row4\" class=\"row_heading level1 row4\" >Ff Page Session W 4 New Unread Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row4_col0\" class=\"data row4 col0\" >0.02% (0.0042→0.0042, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row5\" class=\"row_heading level1 row5\" >Ff Page Session W 5 New Unread Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row5_col0\" class=\"data row5 col0\" >1.42% (0.00316→0.00321, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row6\" class=\"row_heading level1 row6\" >Ff Page Session W 6 Plus New Unread Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row6_col0\" class=\"data row6 col0\" >2.72% (0.0244→0.025, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row7\" class=\"row_heading level1 row7\" >Ff Page Session W No Unread Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row7_col0\" class=\"data row7 col0\" >-1.35% (0.122→0.12, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row8\" class=\"row_heading level1 row8\" >Ff Page Session W 1 Unread Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row8_col0\" class=\"data row8 col0\" >1.24% (0.0534→0.0541, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row9\" class=\"row_heading level1 row9\" >Ff Page Session W 2 Unread Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row9_col0\" class=\"data row9 col0\" >2.55% (0.0203→0.0208, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row10\" class=\"row_heading level1 row10\" >Ff Page Session W 3 Unread Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row10_col0\" class=\"data row10 col0\" >5.38% (0.0114→0.012, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row11\" class=\"row_heading level1 row11\" >Ff Page Session W 4 Unread Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row11_col0\" class=\"data row11 col0\" >1.90% (0.00801→0.00816, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row12\" class=\"row_heading level1 row12\" >Ff Page Session W 5 Unread Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row12_col0\" class=\"data row12 col0\" >1.75% (0.00641→0.00652, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row13\" class=\"row_heading level1 row13\" >Ff Page Session W 6 Plus Unread Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row13_col0\" class=\"data row13 col0\" >1.18% (0.0755→0.0764, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row14\" class=\"row_heading level1 row14\" >Ff Page Session W No Unread Chat Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row14_col0\" class=\"data row14 col0\" >-0.30% (0.211→0.21, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row15\" class=\"row_heading level1 row15\" >Ff Page Session W 1 Unread Chat Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row15_col0\" class=\"data row15 col0\" >2.78% (0.0603→0.062, < 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row16\" class=\"row_heading level1 row16\" >Ff Page Session W 2 Unread Chat Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row16_col0\" class=\"data row16 col0\" >2.61% (0.0136→0.014, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row17\" class=\"row_heading level1 row17\" >Ff Page Session W 3 Unread Chat Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row17_col0\" class=\"data row17 col0\" >4.13% (0.00475→0.00495, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row18\" class=\"row_heading level1 row18\" >Ff Page Session W 4 Unread Chat Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row18_col0\" class=\"data row18 col0\" >-6.98% (0.00257→0.00239, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row19\" class=\"row_heading level1 row19\" >Ff Page Session W 5 Unread Chat Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row19_col0\" class=\"data row19 col0\" >5.89% (0.00125→0.00133, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row20\" class=\"row_heading level1 row20\" >Ff Page Session W 6 Plus Unread Chat Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row20_col0\" class=\"data row20 col0\" >-4.98% (0.00367→0.00349, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row21\" class=\"row_heading level1 row21\" >Ff Page Session W No Unread Snap Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row21_col0\" class=\"data row21 col0\" >-0.29% (0.163→0.162, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row22\" class=\"row_heading level1 row22\" >Ff Page Session W 1 Unread Snap Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row22_col0\" class=\"data row22 col0\" >-0.69% (0.0314→0.0311, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row23\" class=\"row_heading level1 row23\" >Ff Page Session W 2 Unread Snap Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row23_col0\" class=\"data row23 col0\" >2.61% (0.0132→0.0135, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row24\" class=\"row_heading level1 row24\" >Ff Page Session W 3 Unread Snap Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row24_col0\" class=\"data row24 col0\" >2.66% (0.00865→0.00888, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row25\" class=\"row_heading level1 row25\" >Ff Page Session W 4 Unread Snap Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row25_col0\" class=\"data row25 col0\" >3.21% (0.00636→0.00656, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row26\" class=\"row_heading level1 row26\" >Ff Page Session W 5 Unread Snap Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row26_col0\" class=\"data row26 col0\" >0.12% (0.00542→0.00543, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row27\" class=\"row_heading level1 row27\" >Ff Page Session W 6 Plus Unread Snap Convo</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row27_col0\" class=\"data row27 col0\" >1.73% (0.0694→0.0706, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row28\" class=\"row_heading level1 row28\" >Ff Page Session W No Visible Cell</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row28_col0\" class=\"data row28 col0\" >0.15% (0.0234→0.0235, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row29\" class=\"row_heading level1 row29\" >Ff Page Session W 1 Visible Cell</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row29_col0\" class=\"data row29 col0\" >-5.02% (0.00212→0.00201, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row30\" class=\"row_heading level1 row30\" >Ff Page Session W 2 Visible Cells</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row30_col0\" class=\"data row30 col0\" >-5.20% (0.00465→0.00441, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row31\" class=\"row_heading level1 row31\" >Ff Page Session W 3 Visible Cells</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row31_col0\" class=\"data row31 col0\" >-2.17% (0.00607→0.00594, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row32\" class=\"row_heading level1 row32\" >Ff Page Session W 4 Visible Cells</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row32_col0\" class=\"data row32 col0\" >-11.27% (0.00558→0.00495, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row33\" class=\"row_heading level1 row33\" >Ff Page Session W 5 Visible Cells</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row33_col0\" class=\"data row33 col0\" >-23.58% (0.00603→0.00461, < 0.0001)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row34\" class=\"row_heading level1 row34\" >Ff Page Session W 6 Visible Cells</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row34_col0\" class=\"data row34 col0\" >-37.20% (0.00995→0.00625, < 0.0001)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row35\" class=\"row_heading level1 row35\" >Ff Page Session W 7 Visible Cells</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row35_col0\" class=\"data row35 col0\" >-43.69% (0.0238→0.0134, < 0.0001)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row36\" class=\"row_heading level1 row36\" >Ff Page Session W 8 Visible Cells</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row36_col0\" class=\"data row36 col0\" >-42.45% (0.0732→0.0421, < 0.0001)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row37\" class=\"row_heading level1 row37\" >Ff Page Session W 9 Visible Cells</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row37_col0\" class=\"data row37 col0\" >-10.01% (0.0756→0.0681, < 0.0001)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row38\" class=\"row_heading level1 row38\" >Ff Page Session W 10 Visible Cells</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row38_col0\" class=\"data row38 col0\" >53.26% (0.0426→0.0653, < 0.0001)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row39\" class=\"row_heading level1 row39\" >Ff Page Session W 11 Plus Visible Cells</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row39_col0\" class=\"data row39 col0\" >145.04% (0.0231→0.0567, < 0.0001)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row40\" class=\"row_heading level1 row40\" >Ff Page Session W No Visible Unread Chat Cell</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row40_col0\" class=\"data row40 col0\" >-0.76% (0.224→0.223, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row41\" class=\"row_heading level1 row41\" >Ff Page Session W 1 Visible Unread Chat Cell</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row41_col0\" class=\"data row41 col0\" >3.70% (0.0562→0.0583, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row42\" class=\"row_heading level1 row42\" >Ff Page Session W 2 Visible Unread Chat Cells</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row42_col0\" class=\"data row42 col0\" >2.11% (0.0107→0.0109, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row43\" class=\"row_heading level1 row43\" >Ff Page Session W 3 Visible Unread Chat Cells</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row43_col0\" class=\"data row43 col0\" >6.66% (0.00295→0.00314, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row44\" class=\"row_heading level1 row44\" >Ff Page Session W 4 Visible Unread Chat Cells</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row44_col0\" class=\"data row44 col0\" >4.35% (0.00117→0.00122, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row45\" class=\"row_heading level1 row45\" >Ff Page Session W 5 Visible Unread Chat Cells</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row45_col0\" class=\"data row45 col0\" >19.41% (0.000465→0.000555, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row46\" class=\"row_heading level1 row46\" >Ff Page Session W 6 Plus Visible Unread Chat Cells</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row46_col0\" class=\"data row46 col0\" >23.06% (0.000376→0.000462, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row47\" class=\"row_heading level1 row47\" >Ff Page Session W No Visible Unread Snap Cell</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row47_col0\" class=\"data row47 col0\" >-1.00% (0.177→0.175, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row48\" class=\"row_heading level1 row48\" >Ff Page Session W 1 Visible Unread Snap Cell</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row48_col0\" class=\"data row48 col0\" >-0.77% (0.0352→0.0349, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row49\" class=\"row_heading level1 row49\" >Ff Page Session W 2 Visible Unread Snap Cells</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row49_col0\" class=\"data row49 col0\" >-2.01% (0.0182→0.0178, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row50\" class=\"row_heading level1 row50\" >Ff Page Session W 3 Visible Unread Snap Cells</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row50_col0\" class=\"data row50 col0\" >-2.60% (0.014→0.0136, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row51\" class=\"row_heading level1 row51\" >Ff Page Session W 4 Visible Unread Snap Cells</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row51_col0\" class=\"data row51 col0\" >-5.25% (0.012→0.0114, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row52\" class=\"row_heading level1 row52\" >Ff Page Session W 5 Visible Unread Snap Cells</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row52_col0\" class=\"data row52 col0\" >-2.26% (0.0108→0.0106, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row53\" class=\"row_heading level1 row53\" >Ff Page Session W 6 Plus Visible Unread Snap Cells</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row53_col0\" class=\"data row53 col0\" >16.22% (0.0288→0.0335, < 0.0001)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row54\" class=\"row_heading level1 row54\" >Ff Page Session W In Call</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row54_col0\" class=\"data row54 col0\" >-0.62% (0.00257→0.00255, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row55\" class=\"row_heading level1 row55\" >Ff Page Session Cell State Read Chat</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row55_col0\" class=\"data row55 col0\" >0.47% (0.194→0.195, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row56\" class=\"row_heading level1 row56\" >Ff Page Session Cell State Read Snap Slient</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row56_col0\" class=\"data row56 col0\" >9.82% (0.00797→0.00875, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row57\" class=\"row_heading level1 row57\" >Ff Page Session Cell State New Snap Slient</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row57_col0\" class=\"data row57 col0\" >1.39% (0.106→0.108, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row58\" class=\"row_heading level1 row58\" >Ff Page Session Cell State New Snap Audio</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row58_col0\" class=\"data row58 col0\" >1.00% (0.0942→0.0952, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row59\" class=\"row_heading level1 row59\" >Ff Page Session Cell State Read Snap Audio</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row59_col0\" class=\"data row59 col0\" >1.61% (0.105→0.107, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row60\" class=\"row_heading level1 row60\" >Ff Page Session Cell State New Chat</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row60_col0\" class=\"data row60 col0\" >1.30% (0.107→0.108, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row61\" class=\"row_heading level1 row61\" >Ff Page Session Cell State Read Reaction</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row61_col0\" class=\"data row61 col0\" >11.55% (0.00918→0.0102, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row62\" class=\"row_heading level1 row62\" >Ff Page Session Cell State New Snap And Chat</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row62_col0\" class=\"data row62 col0\" >5.43% (0.00215→0.00227, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row63\" class=\"row_heading level1 row63\" >Ff Page Session Cell State Missed Call</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row63_col0\" class=\"data row63 col0\" >-7.66% (0.00425→0.00393, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row64\" class=\"row_heading level1 row64\" >Ff Page Session Cell State Typing</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row64_col0\" class=\"data row64 col0\" >4.88% (0.00774→0.00812, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row65\" class=\"row_heading level1 row65\" >Ff Page Session Cell State Active Call</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row65_col0\" class=\"data row65 col0\" >-0.15% (0.00288→0.00287, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row66\" class=\"row_heading level1 row66\" >Ff Page Session Cell State New Reaction</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row66_col0\" class=\"data row66 col0\" >8.43% (0.00236→0.00256, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row67\" class=\"row_heading level1 row67\" >Ff Page Session Cell State Incoming Call</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row67_col0\" class=\"data row67 col0\" >55.50% (5.96e-06→9.27e-06, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row68\" class=\"row_heading level1 row68\" >Ff Page Session Cell State Is Sent By User</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row68_col0\" class=\"data row68 col0\" >0.48% (0.242→0.243, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54level1_row69\" class=\"row_heading level1 row69\" >Ff Page Session Cell State Is Received By User</th>\n", "                        <td id=\"T_0f67a416_bbf9_11ef_aa96_6ebf32c5fb54row69_col0\" class=\"data row69 col0\" >0.35% (0.271→0.272, ≥ 0.1)</td>\n", "            </tr>\n", "    </tbody></table></p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if 'friend_feed_state_detail_metrics' in METRICS_GROUP_CONFIG:\n", "\n", "    friend_feed_state_detail_metrics = [m for mt in ALL_METRICS_GROUPS['friend_feed_state_detail_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    report.ab_printer.print_text(\"Friend Feed State Detailed Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': friend_feed_state_detail_metrics\n", "        },\n", "    );\n", "\n", "else: \n", "    pass "]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["<h2 >Friend Feed State Metrics - Friend Feed Convo and Cell State</h2>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Overall</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Comparison of user overall (cumulative) metric values during the study period</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p ><style  type=\"text/css\" >\n", "#T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row11_col0,#T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row13_col0,#T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row14_col0,#T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row16_col0{\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #006400;\n", "        }#T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row12_col0,#T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row19_col0,#T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row21_col0,#T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row28_col0{\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #99CC99;\n", "        }#T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row15_col0{\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #66B366;\n", "        }</style><table id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54\" ><thead>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Statistic</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level1\" >control_id</th>        <th class=\"col_heading level1 col0\" >1 (Control)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level2\" >treatment_id</th>        <th class=\"col_heading level2 col0\" >2 (54 with normal ring)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level3\" >Sample Sizes</th>        <th class=\"col_heading level3 col0\" >Treatment=3,020,134<br>\n", "Control=3,019,015<br>\n", "No SSPM (p=0.6489)</th>    </tr>    <tr>        <th class=\"index_name level0\" >Date</th>        <th class=\"index_name level1\" >Metric</th>        <th class=\"blank\" ></th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level0_row0\" class=\"row_heading level0 row0\" rowspan=30>12/10 to 12/10</th>\n", "                        <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row0\" class=\"row_heading level1 row0\" >Ff Page Session Count</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row0_col0\" class=\"data row0 col0\" >0.49% (0.293→0.294, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row1\" class=\"row_heading level1 row1\" >Total Unread Convo</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row1_col0\" class=\"data row1 col0\" >1.38% (1.82→1.84, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row2\" class=\"row_heading level1 row2\" >Total New Unread Convo</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row2_col0\" class=\"data row2 col0\" >8.78% (0.491→0.534, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row3\" class=\"row_heading level1 row3\" >Total Unread Chat Convo</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row3_col0\" class=\"data row3 col0\" >-5.38% (0.168→0.159, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row4\" class=\"row_heading level1 row4\" >Total Unread Snap Convo</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row4_col0\" class=\"data row4 col0\" >1.90% (1.64→1.67, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row5\" class=\"row_heading level1 row5\" >Total Pinned Convo</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row5_col0\" class=\"data row5 col0\" >1.08% (0.212→0.215, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row6\" class=\"row_heading level1 row6\" >Total Unread Convo Per Ff Session</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row6_col0\" class=\"data row6 col0\" >0.89% (6.21→6.26, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row7\" class=\"row_heading level1 row7\" >Total New Unread Convo Per Ff Session</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row7_col0\" class=\"data row7 col0\" >8.25% (1.67→1.81, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row8\" class=\"row_heading level1 row8\" >Total Unread Chat Convo Per Ff Session</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row8_col0\" class=\"data row8 col0\" >-5.83% (0.573→0.54, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row9\" class=\"row_heading level1 row9\" >Total Unread Snap Convo Per Ff Session</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row9_col0\" class=\"data row9 col0\" >1.41% (5.59→5.67, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row10\" class=\"row_heading level1 row10\" >Total Pinned Convo Per Ff Session</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row10_col0\" class=\"data row10 col0\" >0.60% (0.724→0.729, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row11\" class=\"row_heading level1 row11\" >Total Visible Cells</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row11_col0\" class=\"data row11 col0\" >8.65% (2.27→2.47, < 0.0001)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row12\" class=\"row_heading level1 row12\" >Total Visible Unread Chat Cells</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row12_col0\" class=\"data row12 col0\" >4.66% (0.0959→0.1, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row13\" class=\"row_heading level1 row13\" >Total Visible Unread Snap Cells</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row13_col0\" class=\"data row13 col0\" >8.89% (0.428→0.466, < 0.0001)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row14\" class=\"row_heading level1 row14\" >Total Visible Cells Per Ff Session</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row14_col0\" class=\"data row14 col0\" >8.13% (7.75→8.38, < 0.0001)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row15\" class=\"row_heading level1 row15\" >Total Visible Unread Chat Cells Per Ff Session</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row15_col0\" class=\"data row15 col0\" >4.15% (0.327→0.341, < 0.01)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row16\" class=\"row_heading level1 row16\" >Total Visible Unread Snap Cells Per Ff Session</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row16_col0\" class=\"data row16 col0\" >8.36% (1.46→1.58, < 0.0001)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row17\" class=\"row_heading level1 row17\" >Cell Count W State Read Chat</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row17_col0\" class=\"data row17 col0\" >0.15% (0.554→0.555, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row18\" class=\"row_heading level1 row18\" >Cell Count W State Read Snap Slient</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row18_col0\" class=\"data row18 col0\" >9.08% (0.0555→0.0605, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row19\" class=\"row_heading level1 row19\" >Cell Count W State New Snap Slient</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row19_col0\" class=\"data row19 col0\" >5.76% (0.417→0.441, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row20\" class=\"row_heading level1 row20\" >Cell Count W State New Snap Audio</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row20_col0\" class=\"data row20 col0\" >2.97% (0.508→0.524, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row21\" class=\"row_heading level1 row21\" >Cell Count W State Read Snap Audio</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row21_col0\" class=\"data row21 col0\" >9.24% (0.227→0.248, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row22\" class=\"row_heading level1 row22\" >Cell Count W State New Chat</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row22_col0\" class=\"data row22 col0\" >-1.66% (0.259→0.255, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row23\" class=\"row_heading level1 row23\" >Cell Count W State Read Reaction</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row23_col0\" class=\"data row23 col0\" >24.41% (0.0121→0.0151, < 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row24\" class=\"row_heading level1 row24\" >Cell Count W State New Snap And Chat</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row24_col0\" class=\"data row24 col0\" >2.59% (0.00494→0.00507, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row25\" class=\"row_heading level1 row25\" >Cell Count W State Missed Call</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row25_col0\" class=\"data row25 col0\" >-9.31% (0.00554→0.00503, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row26\" class=\"row_heading level1 row26\" >Cell Count W State Typing</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row26_col0\" class=\"data row26 col0\" >8.86% (0.00882→0.00961, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row27\" class=\"row_heading level1 row27\" >Cell Count W State Active Call</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row27_col0\" class=\"data row27 col0\" >25.97% (0.00438→0.00552, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row28\" class=\"row_heading level1 row28\" >Cell Count W State New Reaction</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row28_col0\" class=\"data row28 col0\" >28.36% (0.00258→0.00332, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54level1_row29\" class=\"row_heading level1 row29\" >Cell Count W State Incoming Call</th>\n", "                        <td id=\"T_11f44f0e_bbf9_11ef_aa96_6ebf32c5fb54row29_col0\" class=\"data row29 col0\" >55.50% (5.96e-06→9.27e-06, ≥ 0.1)</td>\n", "            </tr>\n", "    </tbody></table></p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if 'friend_feed_state_convo_metrics' in METRICS_GROUP_CONFIG:\n", "\n", "    friend_feed_state_convo_metrics = [m for mt in ALL_METRICS_GROUPS['friend_feed_state_convo_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    report.ab_printer.print_text(\"Friend Feed State Metrics - Friend Feed Convo and Cell State\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': friend_feed_state_convo_metrics\n", "        },\n", "    );\n", "\n", "else: \n", "    pass "]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<h2 >Friend Feed State Metrics - Friend Feed State Active Day</h2>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Overall</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Comparison of user overall (cumulative) metric values during the study period</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p ><style  type=\"text/css\" >\n", "#T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54row12_col0{\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #006400;\n", "        }</style><table id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54\" ><thead>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Statistic</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level1\" >control_id</th>        <th class=\"col_heading level1 col0\" >1 (Control)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level2\" >treatment_id</th>        <th class=\"col_heading level2 col0\" >2 (54 with normal ring)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level3\" >Sample Sizes</th>        <th class=\"col_heading level3 col0\" >Treatment=3,020,134<br>\n", "Control=3,019,015<br>\n", "No SSPM (p=0.6489)</th>    </tr>    <tr>        <th class=\"index_name level0\" >Date</th>        <th class=\"index_name level1\" >Metric</th>        <th class=\"blank\" ></th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54level0_row0\" class=\"row_heading level0 row0\" rowspan=22>12/10 to 12/10</th>\n", "                        <th id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54level1_row0\" class=\"row_heading level1 row0\" >Ff Page Session Active Day</th>\n", "                        <td id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54row0_col0\" class=\"data row0 col0\" >-0.32% (0.032→0.0319, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54level1_row1\" class=\"row_heading level1 row1\" >App Session W Ff Page Open Active Day</th>\n", "                        <td id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54row1_col0\" class=\"data row1 col0\" >-0.35% (0.0323→0.0322, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54level1_row2\" class=\"row_heading level1 row2\" >Ff Page Session W No Unread Convo Active Day</th>\n", "                        <td id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54row2_col0\" class=\"data row2 col0\" >-0.75% (0.0193→0.0191, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54level1_row3\" class=\"row_heading level1 row3\" >Ff Page Session W 1 Plus Unread Convo Active Day</th>\n", "                        <td id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54row3_col0\" class=\"data row3 col0\" >0.18% (0.026→0.0261, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54level1_row4\" class=\"row_heading level1 row4\" >Ff Page Session W No Unread Chat Active Day</th>\n", "                        <td id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54row4_col0\" class=\"data row4 col0\" >-0.35% (0.0278→0.0277, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54level1_row5\" class=\"row_heading level1 row5\" >Ff Page Session W 1 Plus Unread Chat Active Day</th>\n", "                        <td id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54row5_col0\" class=\"data row5 col0\" >0.14% (0.0157→0.0158, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54level1_row6\" class=\"row_heading level1 row6\" >Ff Page Session W 6 Plus Unread Chat Active Day</th>\n", "                        <td id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54row6_col0\" class=\"data row6 col0\" >-1.82% (0.000593→0.000582, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54level1_row7\" class=\"row_heading level1 row7\" >Ff Page Session W No Unread Snap Active Day</th>\n", "                        <td id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54row7_col0\" class=\"data row7 col0\" >-0.80% (0.0219→0.0217, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54level1_row8\" class=\"row_heading level1 row8\" >Ff Page Session W 1 Plus Unread Snap Active Day</th>\n", "                        <td id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54row8_col0\" class=\"data row8 col0\" >0.38% (0.0214→0.0214, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54level1_row9\" class=\"row_heading level1 row9\" >Ff Page Session W 6 Plus Unread Snap Active Day</th>\n", "                        <td id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54row9_col0\" class=\"data row9 col0\" >0.81% (0.011→0.0111, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54level1_row10\" class=\"row_heading level1 row10\" >Ff Page Session W No Visible Cell Active Day</th>\n", "                        <td id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54row10_col0\" class=\"data row10 col0\" >0.01% (0.00827→0.00827, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54level1_row11\" class=\"row_heading level1 row11\" >Ff Page Session W 1 Plus Visible Cell Active Day</th>\n", "                        <td id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54row11_col0\" class=\"data row11 col0\" >-0.15% (0.0304→0.0304, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54level1_row12\" class=\"row_heading level1 row12\" >Ff Page Session W 11 Plus Visible Cell Active Day</th>\n", "                        <td id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54row12_col0\" class=\"data row12 col0\" >101.61% (0.00571→0.0115, < 0.0001)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54level1_row13\" class=\"row_heading level1 row13\" >Ff Page Session W Page View Id 1 Active Day</th>\n", "                        <td id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54row13_col0\" class=\"data row13 col0\" >-0.36% (0.0102→0.0102, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54level1_row14\" class=\"row_heading level1 row14\" >Ff Page Session W Page View Id 2 Active Day</th>\n", "                        <td id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54row14_col0\" class=\"data row14 col0\" >0.20% (0.0194→0.0194, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54level1_row15\" class=\"row_heading level1 row15\" >Ff Page Session W Page View Id 3 Plus Active Day</th>\n", "                        <td id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54row15_col0\" class=\"data row15 col0\" >-0.34% (0.0249→0.0248, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54level1_row16\" class=\"row_heading level1 row16\" >Ff Page Session W 1 Convo Pinned Active Day</th>\n", "                        <td id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54row16_col0\" class=\"data row16 col0\" >-0.49% (0.0102→0.0102, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54level1_row17\" class=\"row_heading level1 row17\" >Ff Page Session W 2 Convo Pinned Active Day</th>\n", "                        <td id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54row17_col0\" class=\"data row17 col0\" >2.16% (0.00143→0.00146, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54level1_row18\" class=\"row_heading level1 row18\" >Ff Page Session W 3 Convo Pinned Active Day</th>\n", "                        <td id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54row18_col0\" class=\"data row18 col0\" >1.26% (0.000995→0.00101, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54level1_row19\" class=\"row_heading level1 row19\" >Ff Page Session W 4 Plus Convo Pinned Active Day</th>\n", "                        <td id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54row19_col0\" class=\"data row19 col0\" >1.94% (0.000568→0.000579, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54level1_row20\" class=\"row_heading level1 row20\" >Ff Page Session App Ui 5 Tab Active Day</th>\n", "                        <td id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54row20_col0\" class=\"data row20 col0\" >-0.25% (0.0303→0.0302, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54level1_row21\" class=\"row_heading level1 row21\" >Ff Page Session App Ui 3 Tab Active Day</th>\n", "                        <td id=\"T_13f7dc12_bbf9_11ef_aa96_6ebf32c5fb54row21_col0\" class=\"data row21 col0\" >-2.07% (0.00194→0.0019, ≥ 0.1)</td>\n", "            </tr>\n", "    </tbody></table></p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if 'friend_feed_state_active_day_metrics' in METRICS_GROUP_CONFIG:\n", "\n", "    friend_feed_state_active_day_metrics = [m for mt in ALL_METRICS_GROUPS['friend_feed_state_active_day_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    report.ab_printer.print_text(\"Friend Feed State Metrics - Friend Feed State Active Day\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': friend_feed_state_active_day_metrics\n", "        },\n", "    );\n", "\n", "else: \n", "    pass "]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/html": ["<h2 >Friend Feed Page View Metrics</h2>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Overall</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Comparison of user overall (cumulative) metric values during the study period</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p ><style  type=\"text/css\" >\n", "</style><table id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54\" ><thead>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Statistic</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level1\" >control_id</th>        <th class=\"col_heading level1 col0\" >1 (Control)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level2\" >treatment_id</th>        <th class=\"col_heading level2 col0\" >2 (54 with normal ring)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level3\" >Sample Sizes</th>        <th class=\"col_heading level3 col0\" >Treatment=3,020,134<br>\n", "Control=3,019,015<br>\n", "No SSPM (p=0.6489)</th>    </tr>    <tr>        <th class=\"index_name level0\" >Date</th>        <th class=\"index_name level1\" >Metric</th>        <th class=\"blank\" ></th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level0_row0\" class=\"row_heading level0 row0\" rowspan=49>12/10 to 12/10</th>\n", "                        <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row0\" class=\"row_heading level1 row0\" >Feed Page View Session Count</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row0_col0\" class=\"data row0 col0\" >0.04% (3.94→3.94, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row1\" class=\"row_heading level1 row1\" >Total Time Viewed Sec</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row1_col0\" class=\"data row1 col0\" >-34.31% (3.68e+02→2.42e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row2\" class=\"row_heading level1 row2\" >Time Viewed Sec Per Ff Session</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row2_col0\" class=\"data row2 col0\" >-34.34% (93.4→61.3, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row3\" class=\"row_heading level1 row3\" >Feed Page View Session Time Viewed Sec 0 1</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row3_col0\" class=\"data row3 col0\" >-0.28% (0.178→0.177, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row4\" class=\"row_heading level1 row4\" >Feed Page View Session Time Viewed Sec 1 2</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row4_col0\" class=\"data row4 col0\" >0.34% (0.32→0.321, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row5\" class=\"row_heading level1 row5\" >Feed Page View Session Time Viewed Sec 2 5</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row5_col0\" class=\"data row5 col0\" >-0.12% (0.523→0.522, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row6\" class=\"row_heading level1 row6\" >Feed Page View Session Time Viewed Sec 5 10</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row6_col0\" class=\"data row6 col0\" >-0.11% (0.54→0.539, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row7\" class=\"row_heading level1 row7\" >Feed Page View Session Time Viewed Sec 10 30</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row7_col0\" class=\"data row7 col0\" >-0.02% (1.05→1.05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row8\" class=\"row_heading level1 row8\" >Feed Page View Session Time Viewed Sec 30 60</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row8_col0\" class=\"data row8 col0\" >0.06% (0.609→0.609, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row9\" class=\"row_heading level1 row9\" >Feed Page View Session Time Viewed Sec 60 300</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row9_col0\" class=\"data row9 col0\" >0.35% (0.615→0.617, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row10\" class=\"row_heading level1 row10\" >Feed Page View Session Time Viewed Sec 300 600</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row10_col0\" class=\"data row10 col0\" >0.14% (0.0656→0.0657, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row11\" class=\"row_heading level1 row11\" >Feed Page View Session Time Viewed Sec 600 Plus</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row11_col0\" class=\"data row11 col0\" >-0.51% (0.0395→0.0393, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row12\" class=\"row_heading level1 row12\" >Time Viewed Sec Per Ff Session 0 1</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row12_col0\" class=\"data row12 col0\" >-0.32% (0.0451→0.045, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row13\" class=\"row_heading level1 row13\" >Time Viewed Sec Per Ff Session 1 2</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row13_col0\" class=\"data row13 col0\" >0.30% (0.0811→0.0813, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row14\" class=\"row_heading level1 row14\" >Time Viewed Sec Per Ff Session 2 5</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row14_col0\" class=\"data row14 col0\" >-0.16% (0.133→0.132, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row15\" class=\"row_heading level1 row15\" >Time Viewed Sec Per Ff Session 5 10</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row15_col0\" class=\"data row15 col0\" >-0.15% (0.137→0.137, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row16\" class=\"row_heading level1 row16\" >Time Viewed Sec Per Ff Session 10 30</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row16_col0\" class=\"data row16 col0\" >-0.06% (0.267→0.267, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row17\" class=\"row_heading level1 row17\" >Time Viewed Sec Per Ff Session 30 60</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row17_col0\" class=\"data row17 col0\" >0.02% (0.154→0.155, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row18\" class=\"row_heading level1 row18\" >Time Viewed Sec Per Ff Session 60 300</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row18_col0\" class=\"data row18 col0\" >0.31% (0.156→0.156, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row19\" class=\"row_heading level1 row19\" >Time Viewed Sec Per Ff Session 300 600</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row19_col0\" class=\"data row19 col0\" >0.10% (0.0166→0.0166, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row20\" class=\"row_heading level1 row20\" >Time Viewed Sec Per Ff Session 600 Plus</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row20_col0\" class=\"data row20 col0\" >-0.55% (0.01→0.00997, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row21\" class=\"row_heading level1 row21\" >Total Num Pull To Refresh</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row21_col0\" class=\"data row21 col0\" >4.77% (0.0213→0.0223, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row22\" class=\"row_heading level1 row22\" >Num Pull To Refresh Per Ff Session</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row22_col0\" class=\"data row22 col0\" >4.73% (0.00541→0.00567, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row23\" class=\"row_heading level1 row23\" >Feed Page View Session Num Pull To Refresh 0</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row23_col0\" class=\"data row23 col0\" >0.03% (3.93→3.93, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row24\" class=\"row_heading level1 row24\" >Feed Page View Session Num Pull To Refresh 1</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row24_col0\" class=\"data row24 col0\" >2.37% (0.00576→0.0059, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row25\" class=\"row_heading level1 row25\" >Feed Page View Session Num Pull To Refresh 2</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row25_col0\" class=\"data row25 col0\" >4.45% (0.00177→0.00184, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row26\" class=\"row_heading level1 row26\" >Feed Page View Session Num Pull To Refresh 3 Plus</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row26_col0\" class=\"data row26 col0\" >2.36% (0.002→0.00205, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row27\" class=\"row_heading level1 row27\" >Num Pull To Refresh Per Ff Session 0</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row27_col0\" class=\"data row27 col0\" >-0.01% (0.998→0.998, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row28\" class=\"row_heading level1 row28\" >Num Pull To Refresh Per Ff Session 1</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row28_col0\" class=\"data row28 col0\" >2.33% (0.00146→0.00149, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row29\" class=\"row_heading level1 row29\" >Num Pull To Refresh Per Ff Session 2</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row29_col0\" class=\"data row29 col0\" >4.40% (0.000448→0.000468, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row30\" class=\"row_heading level1 row30\" >Num Pull To Refresh Per Ff Session 3 Plus</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row30_col0\" class=\"data row30 col0\" >2.32% (0.000507→0.000519, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row31\" class=\"row_heading level1 row31\" >Total Time Viewed Sec After P2R</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row31_col0\" class=\"data row31 col0\" >1.30% (0.53→0.536, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row32\" class=\"row_heading level1 row32\" >Time Viewed Sec After P2R Per Ff Session</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row32_col0\" class=\"data row32 col0\" >1.26% (0.134→0.136, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row33\" class=\"row_heading level1 row33\" >Feed Page View Session Time Viewed Sec After P2R 0 2</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row33_col0\" class=\"data row33 col0\" >10.01% (0.00141→0.00155, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row34\" class=\"row_heading level1 row34\" >Feed Page View Session Time Viewed Sec After P2R 2 5</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row34_col0\" class=\"data row34 col0\" >5.68% (0.00151→0.0016, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row35\" class=\"row_heading level1 row35\" >Feed Page View Session Time Viewed Sec After P2R 5 10</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row35_col0\" class=\"data row35 col0\" >0.50% (0.00129→0.0013, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row36\" class=\"row_heading level1 row36\" >Feed Page View Session Time Viewed Sec After P2R 10 30</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row36_col0\" class=\"data row36 col0\" >0.97% (0.00215→0.00217, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row37\" class=\"row_heading level1 row37\" >Feed Page View Session Time Viewed Sec After P2R 30 60</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row37_col0\" class=\"data row37 col0\" >-1.38% (0.00131→0.00129, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row38\" class=\"row_heading level1 row38\" >Feed Page View Session Time Viewed Sec After P2R 60 120</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row38_col0\" class=\"data row38 col0\" >-0.66% (0.00091→0.000904, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row39\" class=\"row_heading level1 row39\" >Feed Page View Session Time Viewed Sec After P2R 120 600</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row39_col0\" class=\"data row39 col0\" >3.86% (0.000832→0.000865, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row40\" class=\"row_heading level1 row40\" >Feed Page View Session Time Viewed Sec After P2R 600 Plus</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row40_col0\" class=\"data row40 col0\" >-0.04% (0.000115→0.000115, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row41\" class=\"row_heading level1 row41\" >Time Viewed Sec After P2R Per Ff Session 0 2</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row41_col0\" class=\"data row41 col0\" >9.97% (0.000358→0.000393, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row42\" class=\"row_heading level1 row42\" >Time Viewed Sec After P2R Per Ff Session 2 5</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row42_col0\" class=\"data row42 col0\" >5.64% (0.000384→0.000405, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row43\" class=\"row_heading level1 row43\" >Time Viewed Sec After P2R Per Ff Session 5 10</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row43_col0\" class=\"data row43 col0\" >0.46% (0.000328→0.000329, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row44\" class=\"row_heading level1 row44\" >Time Viewed Sec After P2R Per Ff Session 10 30</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row44_col0\" class=\"data row44 col0\" >0.92% (0.000544→0.000549, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row45\" class=\"row_heading level1 row45\" >Time Viewed Sec After P2R Per Ff Session 30 60</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row45_col0\" class=\"data row45 col0\" >-1.42% (0.000332→0.000327, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row46\" class=\"row_heading level1 row46\" >Time Viewed Sec After P2R Per Ff Session 60 120</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row46_col0\" class=\"data row46 col0\" >-0.70% (0.000231→0.000229, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row47\" class=\"row_heading level1 row47\" >Time Viewed Sec After P2R Per Ff Session 120 600</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row47_col0\" class=\"data row47 col0\" >3.82% (0.000211→0.000219, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54level1_row48\" class=\"row_heading level1 row48\" >Time Viewed Sec After P2R Per Ff Session 600 Plus</th>\n", "                        <td id=\"T_15c1eefc_bbf9_11ef_aa96_6ebf32c5fb54row48_col0\" class=\"data row48 col0\" >-0.08% (2.92e-05→2.91e-05, ≥ 0.1)</td>\n", "            </tr>\n", "    </tbody></table></p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if 'friend_feed_page_view_metrics' in METRICS_GROUP_CONFIG:\n", "\n", "    friend_feed_page_view_metrics = [m for mt in ALL_METRICS_GROUPS['friend_feed_page_view_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    report.ab_printer.print_text(\"Friend Feed Page View Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': friend_feed_page_view_metrics\n", "        },\n", "    );\n", "\n", "else: \n", "    pass "]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/html": ["<h2 >Page Page View Metrics</h2>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Overall</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Comparison of user overall (cumulative) metric values during the study period</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p ><style  type=\"text/css\" >\n", "</style><table id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54\" ><thead>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Statistic</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level1\" >control_id</th>        <th class=\"col_heading level1 col0\" >1 (Control)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level2\" >treatment_id</th>        <th class=\"col_heading level2 col0\" >2 (54 with normal ring)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level3\" >Sample Sizes</th>        <th class=\"col_heading level3 col0\" >Treatment=3,020,134<br>\n", "Control=3,019,015<br>\n", "No SSPM (p=0.6489)</th>    </tr>    <tr>        <th class=\"index_name level0\" >Date</th>        <th class=\"index_name level1\" >Metric</th>        <th class=\"blank\" ></th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54level0_row0\" class=\"row_heading level0 row0\" rowspan=19>12/10 to 12/10</th>\n", "                        <th id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54level1_row0\" class=\"row_heading level1 row0\" >Uu W 1 Plus Friends Tab Session</th>\n", "                        <td id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54row0_col0\" class=\"data row0 col0\" >-0.01% (0.398→0.398, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54level1_row1\" class=\"row_heading level1 row1\" >Uu W 1 Plus Chat Page Session</th>\n", "                        <td id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54row1_col0\" class=\"data row1 col0\" >0.11% (0.299→0.299, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54level1_row2\" class=\"row_heading level1 row2\" >Uu W 1 Plus Friends Feed Session</th>\n", "                        <td id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54row2_col0\" class=\"data row2 col0\" >-0.02% (0.398→0.397, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54level1_row3\" class=\"row_heading level1 row3\" >Uu W 1 Plus Sec Chat Page View</th>\n", "                        <td id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54row3_col0\" class=\"data row3 col0\" >0.11% (0.297→0.297, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54level1_row4\" class=\"row_heading level1 row4\" >Uu W 1 Plus Sec Friends Feed View</th>\n", "                        <td id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54row4_col0\" class=\"data row4 col0\" >0.02% (0.39→0.39, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54level1_row5\" class=\"row_heading level1 row5\" >Total View Time Sec</th>\n", "                        <td id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54row5_col0\" class=\"data row5 col0\" >0.26% (6.85e+02→6.87e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54level1_row6\" class=\"row_heading level1 row6\" >View Time Sec Friends Feed</th>\n", "                        <td id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54row6_col0\" class=\"data row6 col0\" >nan% (0.0→0.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54level1_row7\" class=\"row_heading level1 row7\" >View Time Sec Chat</th>\n", "                        <td id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54row7_col0\" class=\"data row7 col0\" >0.37% (1.64e+02→1.64e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54level1_row8\" class=\"row_heading level1 row8\" >View Time Sec Call</th>\n", "                        <td id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54row8_col0\" class=\"data row8 col0\" >-0.51% (49.1→48.9, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54level1_row9\" class=\"row_heading level1 row9\" >View Time Sec Friends Tab</th>\n", "                        <td id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54row9_col0\" class=\"data row9 col0\" >0.11% (3.36e+02→3.36e+02, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54level1_row10\" class=\"row_heading level1 row10\" >Uu W 0 1 Chat Page Session</th>\n", "                        <td id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54row10_col0\" class=\"data row10 col0\" >0.23% (0.0534→0.0535, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54level1_row11\" class=\"row_heading level1 row11\" >Uu W 1 5 Chat Page Session</th>\n", "                        <td id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54row11_col0\" class=\"data row11 col0\" >0.21% (0.23→0.231, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54level1_row12\" class=\"row_heading level1 row12\" >Uu W 5 10 Chat Page Session</th>\n", "                        <td id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54row12_col0\" class=\"data row12 col0\" >0.10% (0.187→0.187, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54level1_row13\" class=\"row_heading level1 row13\" >Uu W 10 15 Chat Page Session</th>\n", "                        <td id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54row13_col0\" class=\"data row13 col0\" >0.03% (0.144→0.144, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54level1_row14\" class=\"row_heading level1 row14\" >Uu W 15 20 Chat Page Session</th>\n", "                        <td id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54row14_col0\" class=\"data row14 col0\" >0.13% (0.118→0.118, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54level1_row15\" class=\"row_heading level1 row15\" >Uu W 20 Plus Chat Page Session</th>\n", "                        <td id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54row15_col0\" class=\"data row15 col0\" >0.04% (0.184→0.184, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54level1_row16\" class=\"row_heading level1 row16\" >Uu W 0 1 Friends Feed Session</th>\n", "                        <td id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54row16_col0\" class=\"data row16 col0\" >-0.28% (0.149→0.148, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54level1_row17\" class=\"row_heading level1 row17\" >Uu W 1 5 Friends Feed Session</th>\n", "                        <td id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54row17_col0\" class=\"data row17 col0\" >-0.14% (0.317→0.316, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54level1_row18\" class=\"row_heading level1 row18\" >Uu W 5 Plus Friends Feed Session</th>\n", "                        <td id=\"T_17828242_bbf9_11ef_aa96_6ebf32c5fb54row18_col0\" class=\"data row18 col0\" >0.10% (0.328→0.328, ≥ 0.1)</td>\n", "            </tr>\n", "    </tbody></table></p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if 'friend_feed_ppv_metrics' in METRICS_GROUP_CONFIG:\n", "\n", "    friend_feed_ppv_metrics = [m for mt in ALL_METRICS_GROUPS['friend_feed_ppv_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    report.ab_printer.print_text(\"Page Page View Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': friend_feed_ppv_metrics\n", "        },\n", "    );\n", "\n", "else: \n", "    pass "]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/html": ["<h2 >Friend Feed State Cell Position Metrics</h2>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Overall</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Comparison of user overall (cumulative) metric values during the study period</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p ><style  type=\"text/css\" >\n", "#T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row7_col0,#T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row30_col0,#T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row39_col0,#T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row43_col0{\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #99CC99;\n", "        }#T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row33_col0{\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #F56666;\n", "        }#T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row44_col0,#T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row63_col0{\n", "            color: white;\n", "            font-weight: bold;\n", "            background-color: #F89999;\n", "        }</style><table id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54\" ><thead>    <tr>        <th class=\"blank\" ></th>        <th class=\"blank level0\" ></th>        <th class=\"col_heading level0 col0\" >Statistic</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level1\" >control_id</th>        <th class=\"col_heading level1 col0\" >1 (Control)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level2\" >treatment_id</th>        <th class=\"col_heading level2 col0\" >2 (54 with normal ring)</th>    </tr>    <tr>        <th class=\"blank\" ></th>        <th class=\"index_name level3\" >Sample Sizes</th>        <th class=\"col_heading level3 col0\" >Treatment=3,020,134<br>\n", "Control=3,019,015<br>\n", "No SSPM (p=0.6489)</th>    </tr>    <tr>        <th class=\"index_name level0\" >Date</th>        <th class=\"index_name level1\" >Metric</th>        <th class=\"blank\" ></th>    </tr></thead><tbody>\n", "                <tr>\n", "                        <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level0_row0\" class=\"row_heading level0 row0\" rowspan=92>12/10 to 12/10</th>\n", "                        <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row0\" class=\"row_heading level1 row0\" >Ff Page Session Count</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row0_col0\" class=\"data row0 col0\" >0.49% (0.293→0.294, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row1\" class=\"row_heading level1 row1\" >App Session Count W Ff Page Open</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row1_col0\" class=\"data row1 col0\" >0.78% (0.217→0.218, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row2\" class=\"row_heading level1 row2\" >Cell Position 0 State New Snap Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row2_col0\" class=\"data row2 col0\" >0.58% (0.00649→0.00653, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row3\" class=\"row_heading level1 row3\" >Cell Position 0 State New Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row3_col0\" class=\"data row3 col0\" >0.37% (0.00464→0.00466, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row4\" class=\"row_heading level1 row4\" >Cell Position 0 State New Snap And Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row4_col0\" class=\"data row4 col0\" >-14.97% (1.68e-05→1.43e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row5\" class=\"row_heading level1 row5\" >Cell Position 0 State Read Snap Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row5_col0\" class=\"data row5 col0\" >-1.84% (0.000595→0.000584, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row6\" class=\"row_heading level1 row6\" >Cell Position 0 State Read Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row6_col0\" class=\"data row6 col0\" >0.11% (0.00937→0.00938, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row7\" class=\"row_heading level1 row7\" >Cell Position 0 State Read Reaction Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row7_col0\" class=\"data row7 col0\" >22.62% (2.16e-05→2.65e-05, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row8\" class=\"row_heading level1 row8\" >Cell Position 0 State New Reaction Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row8_col0\" class=\"data row8 col0\" >6.34% (6.48e-05→6.89e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row9\" class=\"row_heading level1 row9\" >Cell Position 0 State Call Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row9_col0\" class=\"data row9 col0\" >-2.42% (5.9e-05→5.76e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row10\" class=\"row_heading level1 row10\" >Cell Position 0 State Typing Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row10_col0\" class=\"data row10 col0\" >-0.02% (0.000155→0.000155, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row11\" class=\"row_heading level1 row11\" >Cell Position 1 State New Snap Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row11_col0\" class=\"data row11 col0\" >1.10% (0.00831→0.0084, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row12\" class=\"row_heading level1 row12\" >Cell Position 1 State New Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row12_col0\" class=\"data row12 col0\" >1.24% (0.00411→0.00416, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row13\" class=\"row_heading level1 row13\" >Cell Position 1 State New Snap And Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row13_col0\" class=\"data row13 col0\" >-2.54% (3.14e-05→3.06e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row14\" class=\"row_heading level1 row14\" >Cell Position 1 State Read Snap Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row14_col0\" class=\"data row14 col0\" >-1.63% (0.00125→0.00123, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row15\" class=\"row_heading level1 row15\" >Cell Position 1 State Read Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row15_col0\" class=\"data row15 col0\" >0.05% (0.0024→0.0024, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row16\" class=\"row_heading level1 row16\" >Cell Position 1 State Read Reaction Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row16_col0\" class=\"data row16 col0\" >4.16% (4.36e-05→4.54e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row17\" class=\"row_heading level1 row17\" >Cell Position 1 State New Reaction Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row17_col0\" class=\"data row17 col0\" >-11.01% (5.88e-05→5.24e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row18\" class=\"row_heading level1 row18\" >Cell Position 1 State Call Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row18_col0\" class=\"data row18 col0\" >2.88% (6.77e-05→6.97e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row19\" class=\"row_heading level1 row19\" >Cell Position 1 State Typing Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row19_col0\" class=\"data row19 col0\" >-2.79% (9.98e-05→9.7e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row20\" class=\"row_heading level1 row20\" >Cell Position 2 State New Snap Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row20_col0\" class=\"data row20 col0\" >1.22% (0.00767→0.00776, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row21\" class=\"row_heading level1 row21\" >Cell Position 2 State New Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row21_col0\" class=\"data row21 col0\" >-0.18% (0.00278→0.00277, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row22\" class=\"row_heading level1 row22\" >Cell Position 2 State New Snap And Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row22_col0\" class=\"data row22 col0\" >1.67% (2.75e-05→2.8e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row23\" class=\"row_heading level1 row23\" >Cell Position 2 State Read Snap Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row23_col0\" class=\"data row23 col0\" >0.18% (0.00146→0.00147, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row24\" class=\"row_heading level1 row24\" >Cell Position 2 State Read Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row24_col0\" class=\"data row24 col0\" >0.56% (0.00278→0.00279, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row25\" class=\"row_heading level1 row25\" >Cell Position 2 State Read Reaction Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row25_col0\" class=\"data row25 col0\" >2.60% (4.73e-05→4.86e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row26\" class=\"row_heading level1 row26\" >Cell Position 2 State New Reaction Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row26_col0\" class=\"data row26 col0\" >8.85% (2.37e-05→2.58e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row27\" class=\"row_heading level1 row27\" >Cell Position 2 State Call Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row27_col0\" class=\"data row27 col0\" >-1.00% (3.38e-05→3.34e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row28\" class=\"row_heading level1 row28\" >Cell Position 2 State Typing Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row28_col0\" class=\"data row28 col0\" >9.92% (1.85e-05→2.03e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row29\" class=\"row_heading level1 row29\" >Cell Position 3 State New Snap Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row29_col0\" class=\"data row29 col0\" >0.90% (0.00728→0.00735, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row30\" class=\"row_heading level1 row30\" >Cell Position 3 State New Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row30_col0\" class=\"data row30 col0\" >3.17% (0.00216→0.00223, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row31\" class=\"row_heading level1 row31\" >Cell Position 3 State New Snap And Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row31_col0\" class=\"data row31 col0\" >12.06% (2.43e-05→2.73e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row32\" class=\"row_heading level1 row32\" >Cell Position 3 State Read Snap Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row32_col0\" class=\"data row32 col0\" >-1.07% (0.00148→0.00146, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row33\" class=\"row_heading level1 row33\" >Cell Position 3 State Read Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row33_col0\" class=\"data row33 col0\" >-4.01% (0.00276→0.00265, < 0.01)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row34\" class=\"row_heading level1 row34\" >Cell Position 3 State Read Reaction Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row34_col0\" class=\"data row34 col0\" >2.38% (4.52e-05→4.63e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row35\" class=\"row_heading level1 row35\" >Cell Position 3 State New Reaction Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row35_col0\" class=\"data row35 col0\" >22.82% (1.38e-05→1.7e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row36\" class=\"row_heading level1 row36\" >Cell Position 3 State Call Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row36_col0\" class=\"data row36 col0\" >3.76% (3.35e-05→3.48e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row37\" class=\"row_heading level1 row37\" >Cell Position 3 State Typing Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row37_col0\" class=\"data row37 col0\" >4.05% (1.57e-05→1.63e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row38\" class=\"row_heading level1 row38\" >Cell Position 4 State New Snap Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row38_col0\" class=\"data row38 col0\" >0.98% (0.00693→0.007, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row39\" class=\"row_heading level1 row39\" >Cell Position 4 State New Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row39_col0\" class=\"data row39 col0\" >3.21% (0.00183→0.00189, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row40\" class=\"row_heading level1 row40\" >Cell Position 4 State New Snap And Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row40_col0\" class=\"data row40 col0\" >-7.78% (2.5e-05→2.31e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row41\" class=\"row_heading level1 row41\" >Cell Position 4 State Read Snap Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row41_col0\" class=\"data row41 col0\" >1.17% (0.00148→0.00149, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row42\" class=\"row_heading level1 row42\" >Cell Position 4 State Read Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row42_col0\" class=\"data row42 col0\" >-2.18% (0.00262→0.00257, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row43\" class=\"row_heading level1 row43\" >Cell Position 4 State Read Reaction Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row43_col0\" class=\"data row43 col0\" >21.56% (3.86e-05→4.7e-05, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row44\" class=\"row_heading level1 row44\" >Cell Position 4 State New Reaction Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row44_col0\" class=\"data row44 col0\" >-25.81% (1.3e-05→9.63e-06, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row45\" class=\"row_heading level1 row45\" >Cell Position 4 State Call Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row45_col0\" class=\"data row45 col0\" >-12.07% (3.24e-05→2.85e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row46\" class=\"row_heading level1 row46\" >Cell Position 4 State Typing Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row46_col0\" class=\"data row46 col0\" >11.27% (8e-06→8.9e-06, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row47\" class=\"row_heading level1 row47\" >Cell Position 5 State New Snap Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row47_col0\" class=\"data row47 col0\" >0.82% (0.0066→0.00665, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row48\" class=\"row_heading level1 row48\" >Cell Position 5 State New Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row48_col0\" class=\"data row48 col0\" >3.15% (0.00156→0.00161, < 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row49\" class=\"row_heading level1 row49\" >Cell Position 5 State New Snap And Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row49_col0\" class=\"data row49 col0\" >-1.69% (2.1e-05→2.06e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row50\" class=\"row_heading level1 row50\" >Cell Position 5 State Read Snap Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row50_col0\" class=\"data row50 col0\" >1.92% (0.00145→0.00148, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row51\" class=\"row_heading level1 row51\" >Cell Position 5 State Read Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row51_col0\" class=\"data row51 col0\" >-2.62% (0.00251→0.00245, < 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row52\" class=\"row_heading level1 row52\" >Cell Position 5 State Read Reaction Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row52_col0\" class=\"data row52 col0\" >6.41% (3.84e-05→4.09e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row53\" class=\"row_heading level1 row53\" >Cell Position 5 State New Reaction Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row53_col0\" class=\"data row53 col0\" >24.96% (5.11e-06→6.38e-06, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row54\" class=\"row_heading level1 row54\" >Cell Position 5 State Call Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row54_col0\" class=\"data row54 col0\" >4.64% (2.3e-05→2.41e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row55\" class=\"row_heading level1 row55\" >Cell Position 5 State Typing Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row55_col0\" class=\"data row55 col0\" >-7.31% (2.94e-07→2.73e-07, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row56\" class=\"row_heading level1 row56\" >Cell Position 6 State New Snap Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row56_col0\" class=\"data row56 col0\" >0.32% (0.00628→0.0063, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row57\" class=\"row_heading level1 row57\" >Cell Position 6 State New Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row57_col0\" class=\"data row57 col0\" >2.26% (0.00143→0.00146, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row58\" class=\"row_heading level1 row58\" >Cell Position 6 State New Snap And Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row58_col0\" class=\"data row58 col0\" >12.49% (1.79e-05→2.02e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row59\" class=\"row_heading level1 row59\" >Cell Position 6 State Read Snap Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row59_col0\" class=\"data row59 col0\" >0.51% (0.00144→0.00145, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row60\" class=\"row_heading level1 row60\" >Cell Position 6 State Read Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row60_col0\" class=\"data row60 col0\" >0.17% (0.00241→0.00241, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row61\" class=\"row_heading level1 row61\" >Cell Position 6 State Read Reaction Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row61_col0\" class=\"data row61 col0\" >-4.28% (3.46e-05→3.31e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row62\" class=\"row_heading level1 row62\" >Cell Position 6 State New Reaction Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row62_col0\" class=\"data row62 col0\" >31.23% (4.18e-06→5.48e-06, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row63\" class=\"row_heading level1 row63\" >Cell Position 6 State Call Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row63_col0\" class=\"data row63 col0\" >-24.85% (2.4e-05→1.8e-05, < 0.05)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row64\" class=\"row_heading level1 row64\" >Cell Position 6 State Typing Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row64_col0\" class=\"data row64 col0\" >-69.77% (8.42e-08→2.55e-08, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row65\" class=\"row_heading level1 row65\" >Cell Position 7 State New Snap Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row65_col0\" class=\"data row65 col0\" >0.80% (0.00599→0.00604, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row66\" class=\"row_heading level1 row66\" >Cell Position 7 State New Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row66_col0\" class=\"data row66 col0\" >2.71% (0.00131→0.00134, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row67\" class=\"row_heading level1 row67\" >Cell Position 7 State New Snap And Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row67_col0\" class=\"data row67 col0\" >2.97% (1.93e-05→1.98e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row68\" class=\"row_heading level1 row68\" >Cell Position 7 State Read Snap Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row68_col0\" class=\"data row68 col0\" >1.59% (0.00137→0.00139, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row69\" class=\"row_heading level1 row69\" >Cell Position 7 State Read Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row69_col0\" class=\"data row69 col0\" >-1.29% (0.00236→0.00233, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row70\" class=\"row_heading level1 row70\" >Cell Position 7 State Read Reaction Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row70_col0\" class=\"data row70 col0\" >-7.29% (3.28e-05→3.04e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row71\" class=\"row_heading level1 row71\" >Cell Position 7 State New Reaction Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row71_col0\" class=\"data row71 col0\" >21.55% (2.91e-06→3.54e-06, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row72\" class=\"row_heading level1 row72\" >Cell Position 7 State Call Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row72_col0\" class=\"data row72 col0\" >19.39% (1.9e-05→2.27e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row73\" class=\"row_heading level1 row73\" >Cell Position 7 State Typing Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row73_col0\" class=\"data row73 col0\" >-87.79% (2.63e-08→3.21e-09, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row74\" class=\"row_heading level1 row74\" >Cell Position 8 State New Snap Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row74_col0\" class=\"data row74 col0\" >-0.06% (0.00582→0.00582, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row75\" class=\"row_heading level1 row75\" >Cell Position 8 State New Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row75_col0\" class=\"data row75 col0\" >2.14% (0.00131→0.00134, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row76\" class=\"row_heading level1 row76\" >Cell Position 8 State New Snap And Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row76_col0\" class=\"data row76 col0\" >7.53% (1.65e-05→1.78e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row77\" class=\"row_heading level1 row77\" >Cell Position 8 State Read Snap Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row77_col0\" class=\"data row77 col0\" >-1.74% (0.00136→0.00133, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row78\" class=\"row_heading level1 row78\" >Cell Position 8 State Read Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row78_col0\" class=\"data row78 col0\" >1.22% (0.00225→0.00228, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row79\" class=\"row_heading level1 row79\" >Cell Position 8 State Read Reaction Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row79_col0\" class=\"data row79 col0\" >-16.74% (3.09e-05→2.57e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row80\" class=\"row_heading level1 row80\" >Cell Position 8 State New Reaction Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row80_col0\" class=\"data row80 col0\" >6.28% (3.28e-06→3.48e-06, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row81\" class=\"row_heading level1 row81\" >Cell Position 8 State Call Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row81_col0\" class=\"data row81 col0\" >11.60% (1.88e-05→2.1e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row82\" class=\"row_heading level1 row82\" >Cell Position 8 State Typing Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row82_col0\" class=\"data row82 col0\" >nan% (0.0→0.0, nan)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row83\" class=\"row_heading level1 row83\" >Cell Position 9 State New Snap Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row83_col0\" class=\"data row83 col0\" >0.24% (0.00565→0.00566, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row84\" class=\"row_heading level1 row84\" >Cell Position 9 State New Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row84_col0\" class=\"data row84 col0\" >1.05% (0.00136→0.00137, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row85\" class=\"row_heading level1 row85\" >Cell Position 9 State New Snap And Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row85_col0\" class=\"data row85 col0\" >-5.10% (1.66e-05→1.58e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row86\" class=\"row_heading level1 row86\" >Cell Position 9 State Read Snap Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row86_col0\" class=\"data row86 col0\" >1.45% (0.0013→0.00132, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row87\" class=\"row_heading level1 row87\" >Cell Position 9 State Read Chat Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row87_col0\" class=\"data row87 col0\" >-1.38% (0.00227→0.00224, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row88\" class=\"row_heading level1 row88\" >Cell Position 9 State Read Reaction Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row88_col0\" class=\"data row88 col0\" >10.90% (2.4e-05→2.67e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row89\" class=\"row_heading level1 row89\" >Cell Position 9 State New Reaction Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row89_col0\" class=\"data row89 col0\" >25.72% (2.16e-06→2.71e-06, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row90\" class=\"row_heading level1 row90\" >Cell Position 9 State Call Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row90_col0\" class=\"data row90 col0\" >14.14% (1.68e-05→1.91e-05, ≥ 0.1)</td>\n", "            </tr>\n", "            <tr>\n", "                                <th id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54level1_row91\" class=\"row_heading level1 row91\" >Cell Position 9 State Typing Per Ff Session</th>\n", "                        <td id=\"T_19c66b2c_bbf9_11ef_aa96_6ebf32c5fb54row91_col0\" class=\"data row91 col0\" >nan% (0.0→0.0, nan)</td>\n", "            </tr>\n", "    </tbody></table></p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if 'friend_feed_state_cell_position_metrics' in METRICS_GROUP_CONFIG:\n", "\n", "    friend_feed_state_cell_position_metrics = [m for mt in ALL_METRICS_GROUPS['friend_feed_state_cell_position_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "    report.ab_printer.print_text(\"Friend Feed State Cell Position Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': daily_display},\n", "        metric_filters = {\n", "            'metrics': friend_feed_state_cell_position_metrics\n", "        },\n", "    );\n", "\n", "else: \n", "    pass "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"anaconda-cloud": {}, "celltoolbar": "Tags", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}