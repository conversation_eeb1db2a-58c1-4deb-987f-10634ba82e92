{"cells": [{"cell_type": "code", "execution_count": 2, "id": "cab3d076", "metadata": {"is_executing": true}, "outputs": [{"data": {"text/html": ["<style>.container { width:100% !important; }</style>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import functools\n", "import json\n", "import matplotlib.pyplot as plt\n", "import matplotlib.style as style\n", "import numpy as np\n", "import os\n", "import pandas as pd\n", "import scipy.stats as stats\n", "import seaborn as sns\n", "import socket\n", "import sys\n", "import time\n", "\n", "from itertools import combinations\n", "import re\n", "\n", "from collections import OrderedDict\n", "from datetime import datetime, timedelta, date\n", "from functools import reduce\n", "\n", "from IPython.core.display import display, HTML\n", "# from IPython import display\n", "from IPython.display import Image\n", "from banjo import abtest, utils\n", "\n", "style.use('fivethirtyeight')\n", "sns.set(color_codes=True)\n", "%config InlineBackend.figure_format = 'retina'\n", "%matplotlib inline\n", "\n", "display(HTML(\"<style>.container { width:100% !important; }</style>\"))\n", "\n", "import warnings\n", "# warnings.filterwarnings('ignore')\n", "\n", "good_direction_default = \"up\"\n", "IS_TESTING = True"]}, {"cell_type": "code", "execution_count": 3, "id": "3e9ea03a", "metadata": {}, "outputs": [], "source": ["quantile = 'p50' #-- comment out, input from front end"]}, {"cell_type": "code", "execution_count": 4, "id": "3e088037", "metadata": {"tags": []}, "outputs": [], "source": ["def batch_read_gbq(sqls):\n", "    return utils.gbq.batch_read_gbq(\n", "        sqls, project_id=\"sc-bq-gcs-billingonly\", dialect=\"standard\", priority=\"BATCH\", use_bqstorage_api=True, parallel=True, parallel_concurrency=20,\n", "    )\n", "\n", "\n", "def read_gbq(sql):\n", "    return batch_read_gbq([sql])[0]\n", "\n", "\n", "def date_str_add(date_str, days):\n", "    return (datetime.strptime(date_str, \"%Y%m%d\") + timedelta(days=days)).strftime(\"%Y%m%d\")\n", "\n", "\n", "def display_html(string):\n", "    display(HTML(string))\n", "\n", "\n", "def background_color_df(val, good_direction, thresholds=[3, 1, -3, -1], cast_to_float=True):\n", "    \"\"\" Pandas Background Color: 4 shades of red to green according to thresholds provided\n", "    :param val: str representation of percentages\n", "    :param thresholds: percentage thresholds\n", "    :return: background color for pandas dataframe\n", "    \"\"\"\n", "    if cast_to_float:\n", "        nval = float(re.sub('%', '', val))\n", "    else:\n", "        nval = val\n", "    if (good_direction == \"up\" and nval > thresholds[0]) or (good_direction == \"down\" and nval < thresholds[3]):\n", "        return ('background-color: rgb(102, 179, 102)')\n", "    elif (good_direction == \"up\" and nval > thresholds[1]) or (good_direction == \"down\" and nval < thresholds[2]):\n", "        return ('background-color: rgb(153, 204, 153)')\n", "    elif (good_direction == \"up\" and nval < thresholds[2]) or (good_direction == \"down\" and nval > thresholds[1]):\n", "        return ('background-color: rgb(245, 102, 102)')\n", "    elif (good_direction == \"up\" and nval < thresholds[3]) or (good_direction == \"down\" and nval > thresholds[0]):\n", "        return ('background-color: rgb(248, 153, 153)')\n", "    return ('')\n", "\n", "\n", "background_color_df_float = functools.partial(background_color_df, cast_to_float=False)\n", "\n", "\n", "def period_format(period):\n", "    if period[-8:] == period[:8]:\n", "        return period[:8]\n", "    else:\n", "        return period\n", "\n", "\n", "def to_period_over_period_table(\n", "        data,\n", "        date_field='',\n", "        dimension_fields=[],\n", "        dimension_order=[],\n", "        target_field='',\n", "        min_value=0,\n", "        max_value=10000000000,\n", "        ascending=False,\n", "        period_1=\"period_1\",\n", "        period_2=\"period_2\"\n", "):\n", "    \"\"\"Creates a period over period table given a pandas dataframe with breakdowns and numeric values over time\n", "    :param data: pandas dataframe with at least 4 weeks of data\n", "    :param dimension_field: Breakdown value column\n", "    :param dimension_order: dimension order\n", "    :param target_field: Numerical value column\n", "    :param min_value: Minimum value in target_field\n", "    :param max_value: Maximum value in target_field\n", "    :param period_1: date range of period 1\n", "    :param period_2: date range of period 2\n", "    :return: pandas df\n", "    \"\"\"\n", "\n", "    dimension_field = 'combined'\n", "    data[dimension_field] = data[dimension_fields].apply(lambda row: '|'.join(row.values.astype(str)), axis=1)\\\n", "        if dimension_fields else ''\n", "\n", "    # dim table\n", "    data_dim = data.groupby([date_field, dimension_field] + dimension_fields).sum().reset_index()\n", "\n", "    # check if data available for specified period\n", "    if 'period_1' not in set(data_dim.period):\n", "        raise ValueError(\"Data is not availabe for period {}\".format(period_1))\n", "    if 'period_2' not in set(data_dim.period):\n", "        raise ValueError(\"Data is not availabe for period {}\".format(period_2))\n", "    if 'period_1_last_year' not in set(data_dim.period) or 'period_1_last_year' not in set(data_dim.period):\n", "        warnings.warn('Data are not available for the same period last year, so failed to conduct YOY comparison.')\n", "\n", "    data_dim['dimension_order'] = 0\n", "    # reverse dimension order, prepare sort by dimension_order and period_2 descendingly\n", "    dimension_order = dimension_order[::-1]\n", "    for i in range(len(dimension_order)):\n", "        data_dim.loc[data_dim['dimension'] == dimension_order[i], 'dimension_order'] = i\n", "\n", "    # pivot and sort by dimension_order\n", "    data_pop_pivot = pd.pivot_table(\n", "        data_dim, index=['dimension_order']+dimension_fields, columns='period', values=target_field\n", "        ).sort_values(by=['dimension_order', 'period_2'], ascending=False).reset_index(level=0, drop=True)\n", "\n", "    # ensure no div 0 errors # TODO: will revisit this rule\n", "    data_pop_pivot['period_1'] = data_pop_pivot['period_1'].apply(\n", "        lambda x: x if x >= 1 else 1)\n", "\n", "    data_pop_pivot['diff% between 2 periods'] = (100 * (\n", "            data_pop_pivot['period_2'] - data_pop_pivot['period_1']\n", "    ) / data_pop_pivot['period_1']).apply(lambda x: '%.2f%%' % x)\n", "\n", "\n", "    if 'period_1_last_year' in data_pop_pivot.columns and 'period_2_last_year' in data_pop_pivot.columns:\n", "        \n", "        data_pop_pivot['investigation period YOY'] = (\n", "            safe_divide(data_pop_pivot['period_2'], data_pop_pivot['period_2_last_year'])-1\n", "        ).apply(lambda x: '%.2f%%' % x)\n", "        \n", "        data_pop_pivot['period_1_last_year'] = data_pop_pivot['period_1_last_year'].apply(\n", "            lambda x: x if x >= 1 else 1)\n", "\n", "        data_pop_pivot['diff% between 2 periods 1 year ago'] = (100 * (\n", "            data_pop_pivot['period_2_last_year'] - data_pop_pivot['period_1_last_year']\n", "            ) / data_pop_pivot['period_1_last_year']).apply(lambda x: '%.2f%%' % x)\n", "\n", "        data_pop_pivot = data_pop_pivot[\n", "            ['period_1', 'period_2', 'diff% between 2 periods',# 'period_1_last_year', 'period_2_last_year',\n", "             'diff% between 2 periods 1 year ago', 'investigation period YOY']\n", "        ]\n", "        data_pop_pivot = data_pop_pivot.rename(columns={\n", "            \"period_1\": period_1,\n", "            \"period_2\": period_2,\n", "           # \"period_1_last_year\": period_1_last_year,\n", "           # \"period_2_last_year\": period_2_last_year\n", "        })\n", "    elif 'period_1' in data_pop_pivot.columns and 'period_2' in data_pop_pivot.columns:\n", "        data_pop_pivot = data_pop_pivot[['period_1', 'period_2', 'diff% between 2 periods']]\n", "        data_pop_pivot = data_pop_pivot.rename(columns={\n", "            \"period_1\": period_1,\n", "            \"period_2\": period_2\n", "        })\n", "    else:\n", "        raise ValueError(\"Data is not available\")\n", "\n", "    # apply formatting\n", "    for col in [period_1, period_2]:\n", "        data_pop_pivot[col] = data_pop_pivot[col].apply(lambda x: \"{:,.0f}\".format(x))\n", "\n", "    return data_pop_pivot\n", "\n", "\n", "\n", "def to_period_over_period_table_display(data,\n", "                                        good_direction,\n", "                                        metric_type = \"sum\",\n", "                                        thresholds=[3,1,-3,-1]\n", "                                        ):\n", "    \"\"\"\n", "    :param thresholds: percentage thresholds supplied to background_color_df function\n", "    \"\"\"\n", "    if metric_type == \"sum\":\n", "        column_names = data.columns\n", "    elif metric_type == \"quantile\":\n", "        column_names = data['event_count'].columns\n", "\n", "    if 'diff% between 2 periods 1 year ago' in column_names:\n", "        diff_subset = [\"diff% between 2 periods\", \"diff% between 2 periods 1 year ago\", 'investigation period YOY']\n", "    else:\n", "        diff_subset = [\"diff% between 2 periods\"]\n", "\n", "    if metric_type == \"sum\":\n", "        subset = diff_subset\n", "    elif metric_type == \"quantile\":\n", "        subset = [(i, j) for i in [metric_name, \"event_count\"] for j in diff_subset]\n", "\n", "#   apply coloring\n", "    display(data.style.applymap(\n", "        lambda x: background_color_df(val=x, thresholds=thresholds, good_direction=good_direction),\n", "        subset=subset\n", "    ))\n", "\n", "\n", "\n", "def html_number_color(x, good_direction = good_direction_default):\n", "    if ('-' in x and good_direction == \"up\") or ('-' not in x and good_direction == \"down\"):\n", "        color = 'color=\"rgb(0, 128, 0)\"> '\n", "    else:\n", "        color = 'color=\"rgb(128, 128, 0)\"> '\n", "    return '<font ' + color + x + '</font>'"]}, {"cell_type": "code", "execution_count": 5, "id": "442b7181", "metadata": {"tags": []}, "outputs": [], "source": ["# html utility functions\n", "\n", "def title(title_string, title_size=3):\n", "    \"\"\" HTML title\n", "    :param title_string:\n", "    :param title_size:\n", "    :return:\n", "    \"\"\"\n", "    return \"\"\"\n", "    <div class=\"text_cell_render\"> <h{size} id=\"{id_value}\"> {title_string}\n", "    <a class=\"anchor-link\" href=\"#{id_value}\">¶</a>\n", "    </h{size}>\n", "    </div>\n", "    \"\"\".format(\n", "        title_string=title_string,\n", "        size=title_size,\n", "        id_value=\"{}--{}\".format(\n", "            re.sub(r'[^0-9a-zA-Z]+', '-', title_string),\n", "            datetime.now().strftime(\"%f\")\n", "        )\n", "    )\n", "\n", "def text(text_string):\n", "    \"\"\" HTML text\n", "    :param text_string:\n", "    :return:\n", "    \"\"\"\n", "    return \"\"\"\n", "    <p> {} </p>\n", "    \"\"\".format(text_string)\n", "\n", "\n", "def os_type_format(os_type):\n", "    if os_type.lower() == \"ios\":\n", "        return \"iOS\"\n", "    elif os_type.lower() == \"android\":\n", "        return \"Android\"\n", "    else:\n", "        return \"Not a known os_type\""]}, {"cell_type": "code", "execution_count": 6, "id": "b8acac24", "metadata": {}, "outputs": [], "source": ["def safe_divide(x,y):\n", "    try:\n", "        return x/y\n", "    except ZeroDivisionError:\n", "        return 0"]}, {"cell_type": "code", "execution_count": null, "id": "ebe6c9a3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 7, "id": "889b2192", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameters\n", "period_1_start_date = \"20250401\" # baseline period\n", "period_1_end_date = \"20250403\"\n", "period_2_start_date = \"20250420\" # investigation period\n", "period_2_end_date = \"20250421\"\n", "\n", "metric_source = \"quest_ab\" # ---- \"bigquery\"\n", "# metric_name = \"story_view_time\"\n", "# table_path = \"sc-analytics.report_discover_feed.page_session_level_metrics\"\n", "\n", "metric_type = \"quantile\" # [\"quantile\", \"sum\"]\n", "# quantile = None\n", "\n", "# format: Major____Minor; \n", "quest_metric = \"GHOST_TO_SNAPPABLE____camera_cold_startup_latency\"\n", "\n", "# # format: Major____Minor; \n", "# quest_metric = None # \"CHAT_CHAT_SEND____chat_with_creative_tool_send\" \n", "\n", "source_table_filter = \"\"\n", "user_cohorts_filter = \"\"\n", "\n", "user_breakdown_list = [ # user_cohorts breakdown\n", "\n", "    'device_cluster',\n", "    'l7_network_quality_v2',\n", "    'network_quality',\n", "    'inferred_age_bucket',\n", "    'os_type',\n", "    'l_90_country',\n", "    'major_os_version',\n", "    'device_maker',\n", "    'device_name',\n", "    'user_agg_bandwidth', \n", "    'app_engagement_status',\n", "    'communication_engagement_status',\n", "    \n", "#     'df_non_friend_story_engagement_status',\n", "#     'friend_story_engagement_status',\n", "#         'days_since_creation',\n", "    #    'bidirectional_friend_status',\n", "    #     'is_story_public_user',\n", "] \n", "\n", "# bq table breakdowns\n", "metric_breakdown_list = \"\"\n", "\n", "good_direction = 'down' ## \"up\"\n", "\n", "sections = [\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\"]"]}, {"cell_type": "code", "execution_count": 8, "id": "c0f4467c", "metadata": {}, "outputs": [], "source": ["## test cases paste here\n", "\n", "IS_TESTING = True\n"]}, {"cell_type": "code", "execution_count": 9, "id": "f8293726", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["<ipython-input-9-d402c7c8a772>:28: User<PERSON>arning: Different period lengths. Highly recommand to ues the same length of periods.\n", "  warnings.warn(\"Different period lengths. Highly recommand to ues the same length of periods.\")\n", "<ipython-input-9-d402c7c8a772>:42: Use<PERSON><PERSON><PERSON>ning: This version is in-testing.  Results will be from a 0.1% user sample\n", "  warnings.warn(\"This version is in-testing.  Results will be from a 0.1% user sample\")\n"]}], "source": ["# periods sanity check\n", "# 1. start_date <= end_date (Required)\n", "# 2. period lengths are the same. (Highly recommended)\n", "# 3. periods are not overlapping. (Required. or the overlapping period will be counted as period 1 due to the cascade logic of CASE WHEN.)\n", "\n", "# check 1\n", "if period_1_start_date > period_1_end_date:\n", "    raise ValueError(\"The baseline period start date is suppposed to be less than or equal to the end date.\")\n", "if period_2_start_date > period_2_end_date:\n", "    raise ValueError(\"The investigation period start date is suppposed to be less than or equal to the end date.\")\n", "\n", "# check 2\n", "\n", "period_1_start_date_time = datetime.strptime(period_1_start_date, \"%Y%m%d\")\n", "period_1_end_date_time = datetime.strptime(period_1_end_date, \"%Y%m%d\")\n", "period_1_dates = [period_1_start_date_time + timedelta(days=x) \n", "                  for x in range((period_1_end_date_time-period_1_start_date_time).days + 1)]\n", "period_1_dates_length = len(period_1_dates)\n", "\n", "period_2_start_date_time = datetime.strptime(period_2_start_date, \"%Y%m%d\")\n", "period_2_end_date_time = datetime.strptime(period_2_end_date, \"%Y%m%d\")\n", "period_2_dates = [period_2_start_date_time + timedelta(days=x) \n", "                  for x in range((period_2_end_date_time-period_2_start_date_time).days + 1)]\n", "period_2_dates_length = len(period_2_dates)\n", "\n", "\n", "if period_1_dates_length != period_2_dates_length:\n", "    warnings.warn(\"Different period lengths. Highly recommand to ues the same length of periods.\")\n", "    # will show period lengths in notebook section 1 as well.\n", "\n", "# check 3\n", "if len(list(set(period_1_dates) & set(period_2_dates))) != 0:\n", "    raise ValueError(\"The investigation period and the baseline period are overlapping.\")\n", "\n", "    \n", "# sampling rules    \n", "# sampling 0.1% users when testing\n", "if IS_TESTING: \n", "    # CONCAT('a', ghost_user_id) is to avoid hash function conflict with section 8.2\n", "    sampling_filter = \"AND MOD(COALESCE(abs(FARM_FINGERPRINT(CONCAT('a', ghost_user_id))), 0), 1000) = 0\"\n", "    sampling_ratio = 0.001\n", "    warnings.warn(\"This version is in-testing.  Results will be from a 0.1% user sample\")\n", "# sampling 10%, notify users that sampling will be done to reduce query load when the length of dates combined surpasses 14 days\n", "elif period_1_dates_length + period_2_dates_length > 14:\n", "    sampling_filter = \"AND MOD(COALESCE(abs(FARM_FINGERPRINT(CONCAT('a', ghost_user_id))), 0), 1000) = 0\"\n", "    sampling_ratio = 0.001\n", "    warnings.warn(\"Combined period lengths exceed 14 days.  Results will be from a 0.1% user sample\")\n", "else: \n", "    sampling_filter = \"\"\n", "    sampling_ratio = 0.01\n", "    warnings.warn(\"Results will be from a 1% user sample\")\n"]}, {"cell_type": "code", "execution_count": 10, "id": "dfc6f588", "metadata": {}, "outputs": [], "source": ["period_1_start_date_last_year = str(int(period_1_start_date[:4])-1) + period_1_start_date[4:]\n", "period_1_end_date_last_year = str(int(period_1_end_date[:4])-1) + period_1_end_date[4:]\n", "period_2_start_date_last_year = str(int(period_2_start_date[:4])-1) + period_2_start_date[4:]\n", "period_2_end_date_last_year = str(int(period_2_end_date[:4])-1) + period_2_end_date[4:]\n", "\n", "period_1_last_year=period_format(period_1_start_date_last_year + ' - ' + period_1_end_date_last_year)\n", "period_2_last_year=period_format(period_2_start_date_last_year + ' - ' + period_2_end_date_last_year)\n", "period_1=period_format(period_1_start_date + ' - ' + period_1_end_date)\n", "period_2=period_format(period_2_start_date + ' - ' + period_2_end_date)\n", "\n", "period_2_end_date_28d_ago = (pd.to_datetime(period_2_end_date) - timedelta(days=28)).strftime(\"%Y%m%d\")\n", "period_2_end_date_2d_ago = (pd.to_datetime(period_2_end_date) - timedelta(days=2)).strftime(\"%Y%m%d\")\n", "period__end_date_1d_ago = (pd.to_datetime(period_2_end_date) - timedelta(days=1)).strftime(\"%Y%m%d\")\n", "\n", "if metric_source == \"bigquery\":\n", "    metric_name = metric_name.strip()\n", "    table_path = table_path.strip().replace(\":\", \".\") #support both legacy/standard table name syntax \n", "    table_path = table_path.rstrip(\"_\") # remove suffix '_' in case users accidentally add it.\n", "\n", "source_table_filter = source_table_filter.strip()\n", "user_cohorts_filter = user_cohorts_filter.strip()\n", "\n", "if metric_type == \"quantile\": \n", "    quantile = quantile.strip()\n", "    quantile = quantile[1:] \n", "\n", "try:\n", "    good_direction = good_direction.strip()\n", "except:\n", "    good_direction = good_direction_default\n", "\n", "percentage_thresholds = [3,1,-3,-1]\n", "\n", "\n", "# metric_breakdown_list passed as string from husky UI, need to convert it to list \n", "metric_breakdown_list_error_message = (\n", "    'BQ Table Breakdowns should be a comma-separated value, e.g. os,media_type,device_connectivity. You provided:\\n {}'.format(metric_breakdown_list)\n", ")\n", "if metric_breakdown_list.startswith(\"[\"):  # likely json\n", "    try:\n", "        metric_breakdown_list = json.loads(metric_breakdown_list.replace(\"'\", '\"'))\n", "    except json.JSONDecodeError:\n", "        raise ValueError(metric_breakdown_list_error_message)\n", "\n", "else:  # csv\n", "    metric_breakdown_list = [bd.strip() for bd in metric_breakdown_list.split(\",\")]\n", "    metric_breakdown_list = list(filter(bool, metric_breakdown_list))  # remove empty string elements\n", "    \n", "assert isinstance(metric_breakdown_list, list), metric_breakdown_list_error_message"]}, {"cell_type": "code", "execution_count": 11, "id": "3ae95857", "metadata": {}, "outputs": [], "source": ["# quantile"]}, {"cell_type": "code", "execution_count": 12, "id": "2348d46b", "metadata": {}, "outputs": [], "source": ["# if metric_type == \"quantile\": \n", "# #     quantile = quantile.strip()\n", "#     quantile = quantile[1:] "]}, {"cell_type": "code", "execution_count": 13, "id": "73a62c81", "metadata": {}, "outputs": [], "source": ["# ab_console_metrics_catalog"]}, {"cell_type": "code", "execution_count": 14, "id": "dde37e1a", "metadata": {}, "outputs": [], "source": ["# quest_metric_match"]}, {"cell_type": "code", "execution_count": 15, "id": "42b9477d", "metadata": {}, "outputs": [], "source": ["# quest_metric_detail = quest_metric_match.iloc[0, :].to_dict()\n", "# quest_metric_detail.get(\"metric_type\", \"\").upper()"]}, {"cell_type": "code", "execution_count": 16, "id": "02982228", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:root:The ratio metric _phoenix_mixed_feed_session_depth_per_uu is not supported because its numerator and denominator are defined in different quest_job and stored in different bigquery tables.\n", "WARNING:root:The ratio metric _phoenix_email_campaign_delivery_click_rate is not supported because its numerator and denominator are defined in different quest_job and stored in different bigquery tables.\n", "WARNING:root:The ratio metric _phoenix_email_campaign_submission_click_rate is not supported because its numerator and denominator are defined in different quest_job and stored in different bigquery tables.\n", "WARNING:root:The ratio metric _notif_campaign_linking_notif_campaign_display_rate is not supported because its numerator and denominator are defined in different quest_job and stored in different bigquery tables.\n", "WARNING:root:The ratio metric _notif_campaign_notif_campaign_display_rate is not supported because its numerator and denominator are defined in different quest_job and stored in different bigquery tables.\n", "WARNING:root:The ratio metric _notif_campaign_linking_notif_campaign_end_to_end_open_rate is not supported because its numerator and denominator are defined in different quest_job and stored in different bigquery tables.\n", "WARNING:root:The ratio metric _notif_campaign_linking_notif_campaign_receive_rate is not supported because its numerator and denominator are defined in different quest_job and stored in different bigquery tables.\n", "WARNING:root:The ratio metric _notif_campaign_notif_campaign_receive_rate is not supported because its numerator and denominator are defined in different quest_job and stored in different bigquery tables.\n", "WARNING:root:The ratio metric _notif_campaign_notif_campaign_end_to_end_open_rate is not supported because its numerator and denominator are defined in different quest_job and stored in different bigquery tables.\n", "WARNING:root:The ratio metric _phoenix_search_spotlight_ctr is not supported because its numerator and denominator are defined in different quest_job and stored in different bigquery tables.\n", "WARNING:root:The ratio metric _phoenix_lens_weighted_action_rate is not supported because its numerator and denominator are defined in different quest_job and stored in different bigquery tables.\n", "WARNING:banjo.abtest.ab_console_metrics.ab_metric_catalog:Quantiles metrics of metric table app_app_ios_metrickit_sum are dropped because the table includes non-quantile metrics: ['_app_ios_metrickit_sum_app_ios_metrickit_application_hang_time_ms', '_app_ios_metrickit_sum_app_ios_metrickit_application_hang_count']\n", "WARNING:banjo.abtest.ab_console_metrics.ab_metric_catalog:Quantiles metrics of metric table lens_lens_carousel_usable_latency are dropped because the table includes non-quantile metrics: ['_warm_ghost_to_lens_carousel_usable_latency_warm_ghost_to_lens_carousel_usable_latency', '_ghost_to_lens_carousel_usable_latency_ghost_to_lens_carousel_usable_latency', '_hot_ghost_to_lens_carousel_usable_latency_hot_ghost_to_lens_carousel_usable_latency', '_cold_ghost_to_lens_carousel_usable_latency_cold_ghost_to_lens_carousel_usable_latency', '_lens_carousel_usable_latency_lens_carousel_usable_latency']\n", "WARNING:banjo.abtest.ab_console_metrics.ab_metric_catalog:Quantiles metrics of metric table snap_filter_lens_swipe_event_user are dropped because the table includes non-quantile metrics: ['_lens_apply_delay_latency_lens_explorer_apply_delay_latency']\n", "WARNING:banjo.abtest.ab_console_metrics.ab_metric_catalog:Quantiles metrics of metric table chat_messaging_e2e_latency__recv_view are dropped because the table includes non-quantile metrics: ['_phoenix_snap_image_total_send_latency__recv_view', '_phoenix_snap_video_total_load_latency__recv_view', '_phoenix_snap_video_send_to_load__recv_view', '_phoenix_snap_image_total_load_latency__recv_view', '_phoenix_snap_image_send_to_load__recv_view', '_phoenix_text_send_to_recv__recv_view', '_phoenix_snap_video_total_send_latency__recv_view']\n", "WARNING:banjo.abtest.ab_console_metrics.ab_metric_catalog:Quantiles metrics of metric table chat_messaging_e2e_latency__send_view are dropped because the table includes non-quantile metrics: ['_phoenix_snap_image_total_send_latency__send_view', '_phoenix_snap_image_send_to_load__send_view', '_phoenix_snap_video_total_send_latency__send_view', '_phoenix_snap_video_total_load_latency__send_view', '_phoenix_text_send_to_recv__send_view', '_phoenix_snap_image_total_load_latency__send_view', '_phoenix_snap_video_send_to_load__send_view']\n", "WARNING:banjo.abtest.ab_console_metrics.ab_metric_catalog:Quantiles metrics of metric table notif_notif_send are dropped because the table includes non-quantile metrics: ['_notif_send_notif_display_system_latency', '_notif_send_notif_display_latency', '_notif_send_notif_display_in_app_latency']\n", "WARNING:banjo.abtest.ab_console_metrics.ab_metric_catalog:Quantiles metrics of metric table notif_notif_send_linking are dropped because the table includes non-quantile metrics: ['_phoenix_notif_display_in_app_latency_linking', '_phoenix_notif_display_system_latency_linking', '_phoenix_notif_display_latency_linking']\n", "WARNING:banjo.abtest.ab_console_metrics.ab_metric_catalog:Quantiles metrics of metric table security_fidelius_identity_init_event_user are dropped because the table includes non-quantile metrics: ['_phoenix_security_web_key_count', '_phoenix_security_mobile_key_count', '_phoenix_security_mobile_device_count']\n"]}], "source": ["# pull quest ab metadata\n", "try:\n", "    ab_console_metrics_catalog = abtest.ABQuestMetricCatalog(\n", "        period_2_end_date_1d_ago, period_2_end_date,\n", "    )  \n", "except:\n", "    date_check_ab_metadata = (datetime.today() - timed<PERSON>ta(days=7)).strftime('%Y%m%d') \n", "    ab_console_metrics_catalog = abtest.ABQuestMetricCatalog(\n", "        date_check_ab_metadata, date_check_ab_metadata,\n", "    )  \n", "    \n", "if metric_source == \"quest_ab\":  \n", "    \n", "    quest_metric_match = ab_console_metrics_catalog.metadata.loc[\n", "        ab_console_metrics_catalog.metadata.major_minor == quest_metric\n", "    ]\n", "\n", "    if quest_metric_match.empty: \n", "        raise ValueError(f\"The input quest metric {quest_metric} is not available in the date range\") # TODO: Add checking if metric in BQ table available during the period 1 & period 2\n", "    quest_metric_detail = quest_metric_match.iloc[0, :].to_dict()\n", "    if quest_metric_detail.get(\"metric_type\", \"\").upper() not in [\"SUM\", \"QUANTILE\"]: \n", "        raise ValueError(\"The investigation tool currently only supports SUM and QUANTILE metrics.\")\n", "    \n", "    # set table_path and metric_name\n", "    table_path = \"sc-portal.quest.{}_{}\".format(\n", "        quest_metric_detail[\"quest_section\"], \n", "        quest_metric_detail[\"quest_job\"]\n", "    )\n", "    metric_name = quest_metric_detail[\"quest_measure\"]"]}, {"cell_type": "code", "execution_count": 17, "id": "6c1b663a", "metadata": {}, "outputs": [], "source": ["# table_path"]}, {"cell_type": "code", "execution_count": 18, "id": "b653f505", "metadata": {}, "outputs": [], "source": ["table_path = \"sc-portal.quest.{}_{}\".format(\n", "        quest_metric_detail[\"quest_section\"], \n", "        quest_metric_detail[\"quest_job\"]\n", "    )\n", "metric_name = quest_metric_detail[\"quest_measure\"]"]}, {"cell_type": "code", "execution_count": 19, "id": "6ba04eba", "metadata": {}, "outputs": [], "source": ["# metric_name"]}, {"cell_type": "code", "execution_count": 20, "id": "a27ed3e3", "metadata": {}, "outputs": [], "source": ["# metric_type"]}, {"cell_type": "code", "execution_count": 21, "id": "be320b86", "metadata": {}, "outputs": [], "source": ["# table_path"]}, {"cell_type": "code", "execution_count": 22, "id": "177be6cf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["check1\n"]}], "source": ["# print_check\n", "print('check1')"]}, {"cell_type": "code", "execution_count": 23, "id": "bd722b5b", "metadata": {}, "outputs": [], "source": ["# check metric column data type\n", "if metric_type == \"quantile\":  \n", "    table_catalog, table_schema, table_name_prefix = table_path.split('.')\n", "    table_name = table_name_prefix + \"_\" + period_2_end_date\n", "\n", "    sql_schema = \"\"\"\n", "        SELECT data_type\n", "        FROM `{table_catalog}.{table_schema}.INFORMATION_SCHEMA.COLUMNS`\n", "        WHERE table_catalog = \"{table_catalog}\" AND \n", "          table_schema = \"{table_schema}\" AND\n", "          table_name = \"{table_name}\" AND \n", "          column_name = \"{column_name}\"\n", "    \"\"\".format(\n", "        table_catalog=table_catalog,\n", "        table_schema=table_schema,\n", "        table_name=table_name,\n", "        column_name=metric_name\n", "    )\n", "    df_schema = read_gbq(sql_schema)\n", "\n", "    metric_data_type = df_schema[\"data_type\"][0]\n", "    if metric_data_type in [\"STRUCT<list ARRAY<STRUCT<element INT64>>>\", \"STRUCT<list ARRAY<STRUCT<element FLOAT64>>>\"]:\n", "        if_metric_struct_array = True\n", "    else:\n", "        if_metric_struct_array = False"]}, {"cell_type": "code", "execution_count": 24, "id": "da1c96e5", "metadata": {}, "outputs": [], "source": ["if metric_type == \"quantile\":  \n", "    table_catalog, table_schema, table_name_prefix = table_path.split('.')\n", "    table_name = table_name_prefix + \"_\" + period_2_end_date\n", "\n", "    sql_schema = \"\"\"\n", "        SELECT data_type\n", "        FROM `{table_catalog}.{table_schema}.INFORMATION_SCHEMA.COLUMNS`\n", "        WHERE table_catalog = \"{table_catalog}\" AND \n", "          table_schema = \"{table_schema}\" AND\n", "          table_name = \"{table_name}\" AND \n", "          column_name = \"{column_name}\"\n", "    \"\"\".format(\n", "        table_catalog=table_catalog,\n", "        table_schema=table_schema,\n", "        table_name=table_name,\n", "        column_name=metric_name\n", "    )\n", "# sql_schema"]}, {"cell_type": "code", "execution_count": 25, "id": "46e1fea1", "metadata": {}, "outputs": [], "source": ["# table_catalog\n"]}, {"cell_type": "code", "execution_count": 26, "id": "4de4799a", "metadata": {}, "outputs": [], "source": ["# os_type and device_cluster are default breakdowns for performance metrics, so add them as default breakdowns for quantile metrics\n", "# will revisit this if more quantile metrics are not performance metrics.\n", "\n", "if metric_type == \"quantile\":    \n", "    # find dimension names for os_type in bq table user provided\n", "    os_type_column_names = [\"os_type\", \"platform\"]\n", "    os_type_column_names_sql = \"('\" + \"', '\".join(os_type_column_names) + \"')\"\n", "    \n", "    # priority when os_type/device_clutser not selected by users on husky ui: use os_type/device_clutser in bq table > user_cohorts table. \n", "    sql_find_os_type = \"\"\"\n", "        SELECT column_name\n", "        FROM `{table_catalog}.{table_schema}.INFORMATION_SCHEMA.COLUMNS`\n", "        WHERE table_catalog = \"{table_catalog}\" AND \n", "          table_schema = \"{table_schema}\" AND\n", "          table_name = \"{table_name}\" AND \n", "          column_name IN {os_type_column_names_sql}\n", "    \"\"\".format(\n", "        table_catalog=table_catalog,\n", "        table_schema=table_schema,\n", "        table_name=table_name,\n", "        os_type_column_names_sql=os_type_column_names_sql\n", "    )\n", "    df_find_os_type = read_gbq(sql_find_os_type)\n", "    \n", "    if len(df_find_os_type[\"column_name\"]) > 0 and \"os_type\" not in user_breakdown_list:\n", "        os_type_column_name = df_find_os_type[\"column_name\"][0]\n", "        metric_breakdown_list.append(os_type_column_name)\n", "    else: # if not find os_type in bq table user provided, use os_type in user_cohorts table\n", "        os_type_column_name = \"os_type\"\n", "        user_breakdown_list.append(\"os_type\")\n", "\n", "        \n", "    # find dimension names for device_cluster in bq table user provided\n", "    device_cluster_column_names = [\"device_cluster\", \"cluster\"]\n", "    device_cluster_column_names_sql = \"('\" + \"', '\".join(device_cluster_column_names) + \"')\"\n", "   \n", "    sql_find_device_cluster = \"\"\"\n", "        SELECT column_name\n", "        FROM `{table_catalog}.{table_schema}.INFORMATION_SCHEMA.COLUMNS`\n", "        WHERE table_catalog = \"{table_catalog}\" AND \n", "          table_schema = \"{table_schema}\" AND\n", "          table_name = \"{table_name}\" AND \n", "          column_name IN {device_cluster_column_names_sql}\n", "    \"\"\".format(\n", "        table_catalog=table_catalog,\n", "        table_schema=table_schema,\n", "        table_name=table_name,\n", "        device_cluster_column_names_sql=device_cluster_column_names_sql\n", "    )\n", "    df_find_device_cluster = read_gbq(sql_find_device_cluster)\n", "    \n", "    if len(df_find_device_cluster[\"column_name\"]) > 0 and \"device_cluster\" not in user_breakdown_list:\n", "        device_cluster_column_name = df_find_device_cluster[\"column_name\"][0]\n", "        metric_breakdown_list.append(device_cluster_column_name)\n", "    else: # if not find device_cluster in bq table user provided, use device_cluster in user_cohorts table'\n", "        device_cluster_column_name = \"device_cluster\"\n", "        user_breakdown_list.append(\"device_cluster\")\n", "\n", "metric_breakdown_list = list(set(metric_breakdown_list))\n", "user_breakdown_list = list(set(user_breakdown_list))"]}, {"cell_type": "code", "execution_count": 27, "id": "1ac97233", "metadata": {}, "outputs": [], "source": ["if metric_type == \"quantile\":    \n", "    # find dimension names for os_type in bq table user provided\n", "    os_type_column_names = [\"os_type\", \"platform\"]\n", "    os_type_column_names_sql = \"('\" + \"', '\".join(os_type_column_names) + \"')\"\n", "    \n", "    # priority when os_type/device_clutser not selected by users on husky ui: use os_type/device_clutser in bq table > user_cohorts table. \n", "    sql_find_os_type = \"\"\"\n", "        SELECT column_name\n", "        FROM `{table_catalog}.{table_schema}.INFORMATION_SCHEMA.COLUMNS`\n", "        WHERE table_catalog = \"{table_catalog}\" AND \n", "          table_schema = \"{table_schema}\" AND\n", "          table_name = \"{table_name}\" AND \n", "          column_name IN {os_type_column_names_sql}\n", "    \"\"\".format(\n", "        table_catalog=table_catalog,\n", "        table_schema=table_schema,\n", "        table_name=table_name,\n", "        os_type_column_names_sql=os_type_column_names_sql\n", "    )"]}, {"cell_type": "code", "execution_count": 28, "id": "b8211d7c", "metadata": {}, "outputs": [], "source": ["# sql_find_os_type"]}, {"cell_type": "code", "execution_count": 29, "id": "96e4a4bb", "metadata": {}, "outputs": [], "source": ["# sql_find_device_cluster"]}, {"cell_type": "code", "execution_count": 30, "id": "6ef5c3c3", "metadata": {}, "outputs": [], "source": ["# os_type_column_names"]}, {"cell_type": "code", "execution_count": 31, "id": "f84efb9a", "metadata": {}, "outputs": [], "source": ["# metric_breakdown_list"]}, {"cell_type": "code", "execution_count": 32, "id": "1419d678", "metadata": {}, "outputs": [], "source": ["# user_breakdown_list"]}, {"cell_type": "code", "execution_count": 33, "id": "4acfdfd4", "metadata": {}, "outputs": [], "source": ["device_makers = [\n", "    \"Apple\", \"Samsung\", \"Huawei\", \"Vivo\", \"Oppo\", \"Redmi\", \"Xiaomi\", \"Realme\", \"Tecno\", \"Motorola\", \n", "    \"Infinix\", \"LGE\", \"Itel\", \"OnePlus\", \"POCO\", \"Google\", \"Nokia\", \"ZTE\", \"TCT (Alcatel)\", \"Lenovo\", \n", "    \"Sony\", \"Asus\", \"Lava\", \"W<PERSON>\", \"Condor\", \"Blu\", \"Hisense\", \"T-Mobile\", \"General Mobile\", \"HTC\", \n", "    \"Gionee\", \"TCL\", \"Symphony\", \"Micromax\", \"Blackview\", \"Mobicell\", \"BMobile\", \"Umidigi\", \"Walton\", \n", "    \"Coolpad\", \"Cricket\", \"AT&T\", \"Sharp\", \"Ulefone\", \"Casper\", \"Meizu\", \"Cherry Mobile\", \"Panasonic\", \n", "    \"Crosscall\", \"Lanix\"\n", "]\n", "\n", "# top 20 countries: defined as top 20 sum(is_dau)  \n", "countries = [\n", "    'US', 'IN', 'FR', 'SA', 'GB', 'DE', 'PK', 'IQ', 'CA', 'TR',\n", "    'AU', 'MX', 'EG', 'PL', 'NL', 'SE', 'NG', 'NO', 'PH', 'DZ'\n", "]\n"]}, {"cell_type": "code", "execution_count": 34, "id": "83b8a1ed", "metadata": {}, "outputs": [], "source": ["# todo: \n", "# collapse dimensions with cardinality that's too large. \n", "# define buckets using the continuous variables (quartiles or quintiles)\n", "\n", "user_cohorts_dimensions = {    \n", "    \"app_engagement_status\": [],\n", "#     \"aspect_ratio\": [],\n", "    \"bidirectional_friend_status\": [],\n", "#     \"chat_ct_engagement_status\": [],\n", "    \"communication_engagement_status\": [],\n", "#     \"country_bucket\": [],\n", "    \"days_since_creation\": [],\n", "    \"device_cluster\": [],\n", "    \"device_maker\": device_makers,\n", "    \"df_non_friend_story_engagement_status\": [],\n", "    \"friend_story_engagement_status\": [],\n", "#     \"gender\": [],\n", "    \"inferred_age_bucket\": [],\n", "    \"l_90_country\": countries,\n", "#     \"locale\": [],\n", "    \"network_quality\": [],\n", "    \"os_type\": [],\n", "    \"spotlight_story_engagement_status\": [],\n", "    \"user_agg_bandwidth\": [],\n", "    \"user_persona_v3\": [],\n", "}\n", "\n", "# filter\n", "user_cohorts_dimensions = {\n", "    dimension: user_cohorts_dimensions.get(dimension, [])\n", "    for dimension in user_breakdown_list\n", "}\n", "\n", "user_cohort_dims = []\n", "for dim, values in user_cohorts_dimensions.items():\n", "    if \"|\" in dim: # for user defined dimensions like \"gender|inferred_age_bucket\"\n", "        user_cohort_dim = \"{0} AS {1}\".format(\n", "            \"CONCAT(\" + \", '|', \".join([\"CAST(uc.\"+d + \" AS STRING)\" for d in dim.split(\"|\")]) + \")\",\n", "            dim.replace(\"|\", \"_\")\n", "        ) \n", "    elif not values:\n", "        user_cohort_dim = \"CAST(uc.{0} AS STRING) AS {0}\".format(dim)\n", "    else:\n", "        user_cohort_dim = \"IF(uc.{0} in {1}, CAST(uc.{0} AS STRING), 'Other {0}') AS {0}\".format(dim, tuple(values))                                                                     \n", "    user_cohort_dims.append(user_cohort_dim)\n", "\n", "\n", "all_breakdown_list = list(user_cohorts_dimensions.keys()) + metric_breakdown_list \n", "all_breakdown_list = [breakdown.replace(\"|\", \"_\") for breakdown in all_breakdown_list]\n", "\n", "user_cohorts_dim_names=\",\\n\".join(list(set([item for sublist in [i.split(\"|\") for i in list(user_cohorts_dimensions.keys())] for item in sublist])))"]}, {"cell_type": "code", "execution_count": 35, "id": "5398a8a3", "metadata": {}, "outputs": [], "source": ["# user_cohorts_dim_names, user_cohorts_dimensions"]}, {"cell_type": "code", "execution_count": 36, "id": "a4a45991", "metadata": {}, "outputs": [], "source": ["# table_name"]}, {"cell_type": "code", "execution_count": 37, "id": "3a871a09", "metadata": {}, "outputs": [], "source": ["def create_temp_table(name_prefix, query, metric_name, ds):\n", "    table_name = \"{name_prefix}_{metric_name}_{hash_}_{ds}\".format(\n", "        name_prefix=name_prefix,\n", "        metric_name=metric_name,\n", "        ds=ds,\n", "        hash_=utils.helpers.hash_string(query)\n", "    )\n", "    if not utils.gbq.table_exists(\n", "        project_id=\"sc-bq-gcs-billingonly\",\n", "        dataset=\"temp_abtest\",\n", "        table=table_name\n", "    ):\n", "        utils.gbq.submit_sync_query(\n", "            query=query,\n", "            project_id=\"sc-bq-gcs-billingonly\",\n", "            dest_dataset_id=\"temp_abtest\",\n", "            dest_table_name=table_name,\n", "            write_disposition=\"WRITE_TRUNCATE\",\n", "            priority=\"BATCH\",\n", "            dialect=\"standard\",\n", "        )\n", "    return table_name\n", "    \n", "    \n", "def get_reshape_table_query_sum(\n", "    input_table_name,\n", "    metric_name,\n", "    dimensions,\n", "    ):\n", "    \n", "    subqueries = [\n", "        \"\"\"\n", "      SELECT period,\n", "        \"{dimension}\" AS dimension,\n", "        CAST ({dimension_value} as STRING) AS dimension_values,\n", "        SUM({metric_name}) / {sampling_ratio}  AS {metric_name}, \n", "      FROM `sc-bq-gcs-billingonly.temp_abtest.{input_table_name}` GROUP BY 1,2,3        \n", "        \"\"\".format(\n", "            dimension=dim,\n", "            dimension_value=dim,\n", "            metric_name=metric_name,\n", "            sampling_ratio=sampling_ratio,\n", "            input_table_name=input_table_name            \n", "        ) for dim in dimensions   \n", "    ]\n", "    \n", "    query_template = \"\"\"\n", "        SELECT \n", "          period,\n", "          dimension,\n", "          dimension_values,\n", "          {metric_name},\n", "          SAFE_DIVIDE({metric_name}, SUM({metric_name}) OVER (partition by period, dimension)) AS share \n", "        FROM (\n", "          SELECT period,\n", "            \"all\" as dimension,\n", "            \"all\" as dimension_values,\n", "            SUM({metric_name}) / {sampling_ratio} AS {metric_name}, \n", "          FROM `sc-bq-gcs-billingonly.temp_abtest.{input_table_name}` \n", "          GROUP BY 1,2,3\n", "          UNION ALL\n", "          {sql} \n", "          ORDER BY 2,3,1\n", "        )\n", "    \"\"\"\n", "        \n", "    query = query_template.format(\n", "        input_table_name=input_table_name,\n", "        metric_name=metric_name,\n", "        sampling_ratio=sampling_ratio,\n", "        sql=\" UNION ALL \".join(subqueries)\n", "    )\n", "\n", "    return query"]}, {"cell_type": "code", "execution_count": 38, "id": "2d5e992f", "metadata": {}, "outputs": [], "source": ["# build metric query\n", "metric_dim_names = \",\\n\".join(metric_breakdown_list)\n", "if metric_dim_names: \n", "    metric_dim_names += \",\"\n", "metric_dims = \",\\n\".join(\n", "    [\n", "        \"CAST(metrics.{0} AS STRING) AS {0}\".format(dim)\n", "        for dim in metric_breakdown_list\n", "    ]\n", ")    \n", "if metric_dims: \n", "    metric_dims += \",\"\n", "\n", "user_cohorts_template_dict = {\n", "    \"period_1_start_date\":period_1_start_date,\n", "    \"period_1_end_date\":period_1_end_date,\n", "    \"period_2_start_date\":period_2_start_date,\n", "    \"period_2_end_date\":period_2_end_date,\n", "    \"period_1_start_date_last_year\":period_1_start_date_last_year,\n", "    \"period_1_end_date_last_year\":period_1_end_date_last_year,\n", "    \"period_2_start_date_last_year\":period_2_start_date_last_year,\n", "    \"period_2_end_date_last_year\":period_2_end_date_last_year,\n", "    \"metric_name\":metric_name,\n", "    \"table_path\":table_path,\n", "    \"metric_dims\":metric_dims,\n", "    \"metric_dim_names\":metric_dim_names,\n", "    \"user_cohorts_dims\":\",\\n\".join(user_cohort_dims),\n", "    \"user_cohorts_dim_names\":user_cohorts_dim_names,\n", "    \"group_by_indices\":\", \".join(str(i) for i in range(2, 2 + len(all_breakdown_list))),\n", "    \"source_table_filter\":\"\" if not source_table_filter else \"AND ({})\".format(source_table_filter),\n", "    \"user_cohorts_filter\":\"\" if not user_cohorts_filter else \"AND ({})\".format(user_cohorts_filter),\n", "    \"join_method\":\"LEFT JOIN\" if not user_cohorts_filter else \"JOIN\",\n", "    \"sampling_filter\":sampling_filter\n", "}\n", "\n", "date_filter_4_periods = \"\"\"\n", "        (\n", "            CONCAT(\"20\", _TABLE_SUFFIX) BETWEEN \"{period_1_start_date}\" AND \"{period_1_end_date}\"\n", "            OR\n", "            CONCAT(\"20\", _TABLE_SUFFIX) BETWEEN \"{period_2_start_date}\" AND \"{period_2_end_date}\"\n", "            OR\n", "            CONCAT(\"20\", _TABLE_SUFFIX) BETWEEN \"{period_1_start_date_last_year}\" AND \"{period_1_end_date_last_year}\"\n", "            OR\n", "            CONCAT(\"20\", _TABLE_SUFFIX) BETWEEN \"{period_2_start_date_last_year}\" AND \"{period_2_end_date_last_year}\"\n", "        )\n", "\"\"\".format(**user_cohorts_template_dict) \n", "\n", "date_filter_2_periods = \"\"\"\n", "    (\n", "        CONCAT(\"20\", _TABLE_SUFFIX) BETWEEN \"{period_1_start_date}\" AND \"{period_1_end_date}\"\n", "        OR\n", "        CONCAT(\"20\", _TABLE_SUFFIX) BETWEEN \"{period_2_start_date}\" AND \"{period_2_end_date}\"\n", "    )\n", "\"\"\".format(**user_cohorts_template_dict) \n", "\n", "user_cohorts_template_dict[\"period_filter\"] = date_filter_4_periods\n", "\n", "user_cohorts_source_tables_template = \"\"\"\n", "    WITH metrics AS (\n", "      SELECT\n", "        CONCAT(\"20\", _TABLE_SUFFIX) AS ds,    \n", "        ghost_user_id,\n", "        {metric_dim_names}\n", "        IF(IS_NAN({metric_name}), 0, {metric_name}) AS {metric_name}\n", "         \n", "      FROM \n", "        `{table_path}_20*`\n", "      WHERE\n", "        {period_filter}\n", "        {source_table_filter}\n", "        {sampling_filter}\n", "    ), \n", "    uc AS (\n", "      SELECT\n", "        CONCAT(\"20\", _TABLE_SUFFIX) AS ds,    \n", "        ghost_user_id,\n", "        {user_cohorts_dim_names}\n", "      FROM\n", "        `sc-analytics.report_search.user_cohorts_20*`\n", "      WHERE\n", "        {period_filter}\n", "        {user_cohorts_filter}\n", "        {sampling_filter}\n", "    )\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 39, "id": "f6037612", "metadata": {}, "outputs": [], "source": ["if any(x in sections for x in [\"1\", \"2\", \"5\", \"6\"]):\n", "    # sum build metric query\n", "    if metric_type == \"sum\":\n", "        user_cohorts_join_template = \"\"\"\n", "            SELECT  \n", "                * EXCEPT(user_days),\n", "                {metric_name}_sum / user_days AS {metric_name}\n", "            FROM (\n", "              SELECT \n", "                  CASE WHEN ds BETWEEN \"{period_1_start_date}\" AND \"{period_1_end_date}\"\n", "                      THEN 'period_1'\n", "                      WHEN ds BETWEEN \"{period_2_start_date}\" AND \"{period_2_end_date}\"\n", "                      THEN 'period_2'\n", "                      WHEN ds BETWEEN \"{period_1_start_date_last_year}\" AND \"{period_1_end_date_last_year}\"\n", "                      THEN 'period_1_last_year'\n", "                      WHEN ds BETWEEN \"{period_2_start_date_last_year}\" AND \"{period_2_end_date_last_year}\"\n", "                      THEN 'period_2_last_year'\n", "                      ELSE 'other'\n", "                  END AS period, \n", "                  * EXCEPT(ds, {metric_name}),\n", "                  SUM({metric_name}) AS {metric_name}_sum,  -- calculate the daily SUM over the period \n", "                  CASE WHEN ds BETWEEN \"{period_1_start_date}\" AND \"{period_1_end_date}\"\n", "                      THEN (1+ DATE_DIFF(PARSE_TIMESTAMP(\"%Y%m%d\",\"{period_1_end_date}\"), PARSE_TIMESTAMP(\"%Y%m%d\", \"{period_1_start_date}\"), DAY))\n", "                      WHEN ds BETWEEN \"{period_2_start_date}\" AND \"{period_2_end_date}\"\n", "                      THEN (1+ DATE_DIFF(PARSE_TIMESTAMP(\"%Y%m%d\",\"{period_2_end_date}\"), PARSE_TIMESTAMP(\"%Y%m%d\", \"{period_2_start_date}\"), DAY))\n", "                      WHEN ds BETWEEN \"{period_1_start_date_last_year}\" AND \"{period_1_end_date_last_year}\"\n", "                      THEN (1+ DATE_DIFF(PARSE_TIMESTAMP(\"%Y%m%d\",\"{period_1_end_date_last_year}\"), PARSE_TIMESTAMP(\"%Y%m%d\", \"{period_1_start_date_last_year}\"), DAY))\n", "                      WHEN ds BETWEEN \"{period_2_start_date_last_year}\" AND \"{period_2_end_date_last_year}\"\n", "                      THEN (1+ DATE_DIFF(PARSE_TIMESTAMP(\"%Y%m%d\",\"{period_2_end_date_last_year}\"), PARSE_TIMESTAMP(\"%Y%m%d\", \"{period_2_start_date_last_year}\"), DAY))\n", "                      ELSE 0\n", "                  END AS user_days\n", "              FROM (\n", "                  SELECT\n", "                    metrics.ds,\n", "                    {metric_dims}\n", "                    {user_cohorts_dims},\n", "                    SUM({metric_name}) AS {metric_name}\n", "                  FROM \n", "                    metrics\n", "                  {join_method} \n", "                    uc\n", "                  USING (ghost_user_id, ds)  \n", "                  GROUP BY 1, {group_by_indices}\n", "              )\n", "              GROUP BY 1, {group_by_indices}, user_days\n", "          )\n", "        \"\"\"\n", "        \n", "        user_cohorts_template = user_cohorts_source_tables_template + user_cohorts_join_template \n", "        user_cohorts_query = user_cohorts_template.format(**user_cohorts_template_dict)\n", "    \n", "        breakdown_table_name = create_temp_table(\n", "            \"investigation_breakdowns\", \n", "            user_cohorts_query,\n", "            metric_name=metric_name,\n", "            ds=period_2_end_date,\n", "        )\n", "        \n", "        # A wide table that each dim as a column\n", "        df_reshaped = read_gbq(\n", "            get_reshape_table_query_sum(\n", "                breakdown_table_name,\n", "                metric_name=metric_name,\n", "                dimensions=all_breakdown_list\n", "            )\n", "        )"]}, {"cell_type": "code", "execution_count": 40, "id": "301e2eb8", "metadata": {}, "outputs": [], "source": ["## # quantile: output the unioned user cohort as table df_reshaped"]}, {"cell_type": "code", "execution_count": 41, "id": "ff54b07a", "metadata": {}, "outputs": [], "source": ["# quantile"]}, {"cell_type": "code", "execution_count": null, "id": "5fc71ce3", "metadata": {}, "outputs": [], "source": ["if any(x in sections for x in [\"1\", \"2\", \"5\", \"6\"]) or metric_type == \"quantile\":\n", "    # quantile build metric query\n", "    if metric_type == \"quantile\":\n", "        all_breakdown_list_no_os_type = [dim for dim in all_breakdown_list if dim not in os_type_column_names]\n", "        all_breakdown_list_dimension = all_breakdown_list_no_os_type + ['all']\n", "        all_breakdown_list_dimension_value  = all_breakdown_list_no_os_type + ['\\\"all\\\"']\n", "        \n", "        if if_metric_struct_array:\n", "            subqueries_template = \"\"\"\n", "                (\n", "                  SELECT\n", "                    ds,\n", "                    {os_type_column_name},\n", "                    \"{dimension}\" AS dimension,\n", "                    CAST({dimension_value} AS STRING) AS dimension_values,\n", "                    APPROX_QUANTILES(metric_list.element, 100)[OFFSET({quantile})] AS {metric_name},\n", "                    SUM(ARRAY_LENGTH({metric_name}.list)) event_count\n", "                  FROM \n", "                    metrics \n", "                  JOIN\n", "                    UNNEST({metric_name}.list) metric_list\n", "                  {join_method} \n", "                    uc\n", "                  USING (ghost_user_id, ds)  \n", "                  GROUP BY 1,2,3,4\n", "              )\n", "            \"\"\"\n", "        else:\n", "            #todo: need to support aggregated BQ tables with event_count\n", "            subqueries_template = \"\"\"\n", "                (\n", "                  SELECT\n", "                    ds,\n", "                    {os_type_column_name},\n", "                    \"{dimension}\" AS dimension,\n", "                    CAST({dimension_value} AS STRING) AS dimension_values,\n", "                    APPROX_QUANTILES({metric_name}, 100)[OFFSET({quantile})] AS {metric_name},\n", "                    COUNT({metric_name}) / {sampling_ratio} event_count \n", "                  FROM \n", "                    metrics \n", "                  {join_method} \n", "                    uc\n", "                  USING (ghost_user_id, ds)  \n", "                  GROUP BY 1,2,3,4\n", "              )\n", "            \"\"\"\n", "        \n", "        \n", "        subqueries = [subqueries_template.format(\n", "                dimension=dimension,\n", "                dimension_value=dimension_value,\n", "                quantile=quantile,\n", "                metric_name=metric_name,\n", "                sampling_ratio=sampling_ratio,\n", "                join_method=\"LEFT JOIN\" if not user_cohorts_filter else \"JOIN\",\n", "                os_type_column_name=os_type_column_name\n", "            )\n", "            for (dimension, dimension_value) in zip(all_breakdown_list_dimension, all_breakdown_list_dimension_value)\n", "        ]\n", "    \n", "    \n", "        query_template = user_cohorts_source_tables_template + \"\"\"\n", "                SELECT  \n", "                    * EXCEPT(user_days, {metric_name}_sum, event_count_sum),\n", "                    {metric_name}_sum / user_days AS {metric_name},\n", "                    event_count_sum / user_days AS event_count\n", "                FROM (\n", "                  SELECT \n", "                      dimension,\n", "                      dimension_values,\n", "                      {os_type_column_name},\n", "                      CASE WHEN ds BETWEEN \"{period_1_start_date}\" AND \"{period_1_end_date}\"\n", "                          THEN 'period_1'\n", "                          WHEN ds BETWEEN \"{period_2_start_date}\" AND \"{period_2_end_date}\"\n", "                          THEN 'period_2'\n", "                          WHEN ds BETWEEN \"{period_1_start_date_last_year}\" AND \"{period_1_end_date_last_year}\"\n", "                          THEN 'period_1_last_year'\n", "                          WHEN ds BETWEEN \"{period_2_start_date_last_year}\" AND \"{period_2_end_date_last_year}\"\n", "                          THEN 'period_2_last_year'\n", "                          ELSE 'other'\n", "                      END AS period, \n", "                      CASE WHEN ds BETWEEN \"{period_1_start_date}\" AND \"{period_1_end_date}\"\n", "                          THEN (1+ DATE_DIFF(PARSE_TIMESTAMP(\"%Y%m%d\",\"{period_1_end_date}\"), PARSE_TIMESTAMP(\"%Y%m%d\", \"{period_1_start_date}\"), DAY))\n", "                          WHEN ds BETWEEN \"{period_2_start_date}\" AND \"{period_2_end_date}\"\n", "                          THEN (1+ DATE_DIFF(PARSE_TIMESTAMP(\"%Y%m%d\",\"{period_2_end_date}\"), PARSE_TIMESTAMP(\"%Y%m%d\", \"{period_2_start_date}\"), DAY))\n", "                          WHEN ds BETWEEN \"{period_1_start_date_last_year}\" AND \"{period_1_end_date_last_year}\"\n", "                          THEN (1+ DATE_DIFF(PARSE_TIMESTAMP(\"%Y%m%d\",\"{period_1_end_date_last_year}\"), PARSE_TIMESTAMP(\"%Y%m%d\", \"{period_1_start_date_last_year}\"), DAY))\n", "                          WHEN ds BETWEEN \"{period_2_start_date_last_year}\" AND \"{period_2_end_date_last_year}\"\n", "                          THEN (1+ DATE_DIFF(PARSE_TIMESTAMP(\"%Y%m%d\",\"{period_2_end_date_last_year}\"), PARSE_TIMESTAMP(\"%Y%m%d\", \"{period_2_start_date_last_year}\"), DAY))\n", "                          ELSE 0\n", "                      END AS user_days,\n", "                      SUM({metric_name}) AS {metric_name}_sum,  -- calculate the daily SUM over the period \n", "                      SUM(event_count) AS event_count_sum\n", "                  FROM ({subqueries})\n", "                  GROUP BY 1,2,3,4,5\n", "              )\n", "            \"\"\"\n", "        user_cohorts_template_dict[\"subqueries\"] = \" UNION ALL \".join(subqueries)\n", "        user_cohorts_template_dict[\"os_type_column_name\"] = os_type_column_name\n", "        user_cohorts_template_dict[\"sampling_filter\"]   =      sampling_filter\n", "        \n", "        query = query_template.format(\n", "            **user_cohorts_template_dict\n", "#             sampling_filter = sampling_filter\n", "        )\n", "        \n", "        # A long table that all dim in one column\n", "        df_reshaped = read_gbq(query)\n"]}, {"cell_type": "code", "execution_count": null, "id": "46f51303", "metadata": {}, "outputs": [], "source": ["# print_check\n", "print('check2')"]}, {"cell_type": "code", "execution_count": null, "id": "196b07b5", "metadata": {}, "outputs": [], "source": ["# query"]}, {"cell_type": "code", "execution_count": null, "id": "8b61cbc6", "metadata": {}, "outputs": [], "source": ["# output the unioned user cohort as table df_reshaped"]}, {"cell_type": "code", "execution_count": null, "id": "7ca34281", "metadata": {}, "outputs": [], "source": ["# # query template from above:\n", "# WITH metrics AS (\n", "#   SELECT\n", "#     CONCAT(\"20\", _TABLE_SUFFIX) AS ds,\n", "#     ghost_user_id,\n", "#     {metric_dim_names}\n", "#     IF(IS_NAN({metric_name}), 0, {metric_name}) AS {metric_name}\n", "#   FROM\n", "#     `{table_path}_20*`\n", "#   WHERE\n", "#     {period_filter}\n", "#     {source_table_filter}\n", "#     {sampling_filter}\n", "# ),\n", "\n", "# uc AS (\n", "#   SELECT\n", "#     CONCAT(\"20\", _TABLE_SUFFIX) AS ds,\n", "#     ghost_user_id,\n", "#     {user_cohorts_dim_names}\n", "#   FROM\n", "#     `sc-analytics.report_search.user_cohorts_20*`\n", "#   WHERE\n", "#     {period_filter}\n", "#     {user_cohorts_filter}\n", "#     {sampling_filter}\n", "# )\n", "\n", "# SELECT\n", "#   * EXCEPT(user_days, {metric_name}_sum, event_count_sum),\n", "#   {metric_name}_sum / user_days AS {metric_name},\n", "#   event_count_sum / user_days AS event_count\n", "# FROM (\n", "#   SELECT\n", "#     dimension,\n", "#     dimension_values,\n", "#     {os_type_column_name},\n", "#     CASE\n", "#       WHEN ds BETWEEN \"{period_1_start_date}\" AND \"{period_1_end_date}\" THEN 'period_1'\n", "#       WHEN ds BETWEEN \"{period_2_start_date}\" AND \"{period_2_end_date}\" THEN 'period_2'\n", "#       WHEN ds BETWEEN \"{period_1_start_date_last_year}\" AND \"{period_1_end_date_last_year}\" THEN 'period_1_last_year'\n", "#       WHEN ds BETWEEN \"{period_2_start_date_last_year}\" AND \"{period_2_end_date_last_year}\" THEN 'period_2_last_year'\n", "#       ELSE 'other'\n", "#     END AS period,\n", "#     CASE\n", "#       WHEN ds BETWEEN \"{period_1_start_date}\" AND \"{period_1_end_date}\"\n", "#         THEN (1 + DATE_DIFF(PARSE_TIMESTAMP(\"%Y%m%d\", \"{period_1_end_date}\"), PARSE_TIMESTAMP(\"%Y%m%d\", \"{period_1_start_date}\"), DAY))\n", "#       WHEN ds BETWEEN \"{period_2_start_date}\" AND \"{period_2_end_date}\"\n", "#         THEN (1 + DATE_DIFF(PARSE_TIMESTAMP(\"%Y%m%d\", \"{period_2_end_date}\"), PARSE_TIMESTAMP(\"%Y%m%d\", \"{period_2_start_date}\"), DAY))\n", "#       WHEN ds BETWEEN \"{period_1_start_date_last_year}\" AND \"{period_1_end_date_last_year}\"\n", "#         THEN (1 + DATE_DIFF(PARSE_TIMESTAMP(\"%Y%m%d\", \"{period_1_end_date_last_year}\"), PARSE_TIMESTAMP(\"%Y%m%d\", \"{period_1_start_date_last_year}\"), DAY))\n", "#       WHEN ds BETWEEN \"{period_2_start_date_last_year}\" AND \"{period_2_end_date_last_year}\"\n", "#         THEN (1 + DATE_DIFF(PARSE_TIMESTAMP(\"%Y%m%d\", \"{period_2_end_date_last_year}\"), PARSE_TIMESTAMP(\"%Y%m%d\", \"{period_2_start_date_last_year}\"), DAY))\n", "#       ELSE 0\n", "#     END AS user_days,\n", "#     SUM({metric_name}) AS {metric_name}_sum,  -- calculate the daily SUM over the period\n", "#     SUM(event_count) AS event_count_sum\n", "#   FROM (\n", "#     {subqueries}\n", "#   )\n", "#   GROUP BY 1, 2, 3, 4, 5\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "55440541", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "504e631d", "metadata": {}, "outputs": [], "source": ["# #  subqueries_template\n", "# (\n", "#   SELECT\n", "#     ds,\n", "#     {os_type_column_name},\n", "#     \"{dimension}\" AS dimension,\n", "#     CAST({dimension_value} AS STRING) AS dimension_values,\n", "#     APPROX_QUANTILES({metric_name}, 100)[OFFSET({quantile})] AS {metric_name},\n", "#     COUNT({metric_name}) / {sampling_ratio} AS event_count\n", "#   FROM\n", "#     metrics\n", "#     {join_method} uc\n", "#     USING (ghost_user_id, ds)\n", "# )"]}, {"cell_type": "code", "execution_count": null, "id": "3ac8fe57", "metadata": {}, "outputs": [], "source": ["#         query = query_template.format(\n", "#             **user_cohorts_template_dict\n", "#         )\n", "        \n", "#         # A long table that all dim in one column\n", "#         df_reshaped = read_gbq(query)"]}, {"cell_type": "code", "execution_count": null, "id": "2785b978", "metadata": {}, "outputs": [], "source": ["def to_period_over_period_table_quantile(\n", "        data,\n", "        date_field='',\n", "        dimension_fields=[],\n", "        dimension_order=[],\n", "        target_field='',\n", "        min_value=0,\n", "        max_value=10000000000,\n", "        ascending=False,\n", "        period_1=\"period_1\",\n", "        period_2=\"period_2\"\n", "):\n", "\n", "    data_dim = data.copy().loc[\n", "            (data.dimension.isin(dimension_order))\n", "        ]\n", "    dimension_fields=[\"dimension\",\"dimension_values\"]\n", "    target_field=[metric_name, \"event_count\"]\n", "\n", "    # check if data available for specified period\n", "    if 'period_1' not in set(data_dim.period):\n", "        raise ValueError(\"Data is not availabe for period {}\".format(period_1))\n", "    if 'period_2' not in set(data_dim.period):\n", "        raise ValueError(\"Data is not availabe for period {}\".format(period_2))\n", "    if 'period_1_last_year' not in set(data_dim.period) or 'period_1_last_year' not in set(data_dim.period):\n", "        warnings.warn('Data are not available for the same period last year, so failed to conduct YOY comparison.')\n", "\n", "\n", "    data_pop_pivot = pd.pivot_table(\n", "        data_dim, index=dimension_fields, columns='period', values=target_field\n", "        )#.sort_values(by=['period_2'], ascending=False).reset_index(level=0, drop=True)\n", "\n", "    # ensure no div 0 errors # TODO: will revisit this rule\n", "    data_pop_pivot[metric_name, 'period_1'] = data_pop_pivot[metric_name, 'period_1'].apply(\n", "        lambda x: x if x >= 1 else 1)\n", "\n", "    metric_or_count_list = [metric_name, \"event_count\"]\n", "    for metric_or_count in metric_or_count_list:\n", "        data_pop_pivot[metric_or_count, 'diff% between 2 periods'] = (100 * (\n", "                data_pop_pivot[metric_or_count, 'period_2'] - data_pop_pivot[metric_or_count, 'period_1']\n", "        ) / data_pop_pivot[metric_or_count, 'period_1']).apply(lambda x: '%.2f%%' % x)\n", "\n", "\n", "    if 'period_1_last_year' in data_pop_pivot[metric_name].columns and 'period_2_last_year' in data_pop_pivot[metric_name].columns:\n", "\n", "        for metric_or_count in metric_or_count_list:\n", "            data_pop_pivot[metric_or_count, 'period_1_last_year'] = data_pop_pivot[metric_or_count, 'period_1_last_year'].apply(\n", "                lambda x: x if x >= 1 else 1)\n", "\n", "            data_pop_pivot[metric_or_count, 'diff% between 2 periods 1 year ago'] = (100 * (\n", "                data_pop_pivot[metric_or_count, 'period_2_last_year'] - data_pop_pivot[metric_or_count, 'period_1_last_year']\n", "                ) / data_pop_pivot[metric_or_count, 'period_1_last_year']).apply(lambda x: '%.2f%%' % x)\n", "            \n", "            data_pop_pivot[metric_or_count, 'investigation period YOY'] = (safe_divide(\n", "                data_pop_pivot[metric_or_count, 'period_2'], data_pop_pivot[metric_or_count, 'period_2_last_year'])-1\n", "                ).apply(lambda x: '%.2f%%' % x)\n", "            \n", "        data_pop_pivot = data_pop_pivot[\n", "            [[i,j]\n", "                for i in metric_or_count_list\n", "                for j in ['period_1', 'period_2', 'diff% between 2 periods',\n", "                 'diff% between 2 periods 1 year ago', 'investigation period YOY']\n", "            ]\n", "        ]\n", "        data_pop_pivot = data_pop_pivot.rename(columns={\n", "            \"period_1\": period_1,\n", "            \"period_2\": period_2,\n", "        })\n", "    elif 'period_1' in data_pop_pivot[metric_name].columns and 'period_2' in data_pop_pivot[metric_name].columns: \n", "        data_pop_pivot = data_pop_pivot[\n", "            [[i,j]\n", "                for i in metric_or_count_list\n", "                for j in ['period_1', 'period_2', 'diff% between 2 periods']\n", "            ]\n", "        ]\n", "        data_pop_pivot = data_pop_pivot.rename(columns={\n", "            \"period_1\": period_1,\n", "            \"period_2\": period_2,\n", "        })\n", "\n", "    else:\n", "        raise ValueError(\"Data is not available\")\n", "\n", "    # apply formatting\n", "    for col in [period_1, period_2]:\n", "        for metric_or_count in metric_or_count_list:\n", "            data_pop_pivot[metric_or_count, col] = data_pop_pivot[metric_or_count, col].apply(lambda x: \"{:,.0f}\".format(x))\n", "\n", "    return data_pop_pivot\n"]}, {"cell_type": "code", "execution_count": null, "id": "40bdf5c1", "metadata": {}, "outputs": [], "source": ["if any(x in sections for x in [\"1\", \"2\", \"5\", \"6\"]) or metric_type == \"quantile\":\n", "    # for quantile metrics: check if os_type has both ios and android, this is for lego report html format\n", "    if metric_type == \"quantile\":\n", "        df_reshaped_os_type = [os_type.lower() \n", "                               for os_type in [str(i) for i in list(set(df_reshaped[os_type_column_name]))]  \n", "                               if os_type.lower() in [\"android\", \"ios\"]]\n", "        if_both_os_type = len(df_reshaped_os_type) == 2\n"]}, {"cell_type": "code", "execution_count": null, "id": "dad17bcf", "metadata": {}, "outputs": [], "source": ["def sorter(column): \n", "    \"\"\"Sort dimension_values\"\"\"\n", "    dimension_value_orders = [\"all\"] + [str(i) for i in list(range(20))]\n", "    correspondence = {dimension_value: order for order, dimension_value in enumerate(dimension_value_orders)}\n", "    return column.map(correspondence)\n", "\n", "# remove trivial device clusters\n", "device_cluster_to_remove = {}\n", "device_cluster_to_remove[\"ios\"] = [\"1\", \"2\"]\n", "device_cluster_to_remove[\"android\"] = [\"0\"]\n"]}, {"cell_type": "code", "execution_count": null, "id": "2acd47c0", "metadata": {}, "outputs": [], "source": ["# section 2\n", "if any(x in sections for x in [\"1\", \"2\", \"5\", \"6\"]):\n", "    # Calculate Metric Value Changes by OS and Country\n", "    if metric_type == \"sum\":\n", "        dimension_order = [\"all\", \"os_type\", \"l_90_country\"]\n", "    \n", "        df_pop_change = to_period_over_period_table(\n", "            data = df_reshaped.copy().loc[\n", "                (df_reshaped.dimension.isin(dimension_order))\n", "            ],\n", "            dimension_order = dimension_order,\n", "            date_field='period',\n", "            dimension_fields=[\"dimension\",\"dimension_values\"],\n", "            target_field=metric_name,\n", "            min_value=0,\n", "            max_value=1000000000000000,\n", "            ascending=False,\n", "            period_1=period_1,\n", "            period_2=period_2\n", "        )\n", "        df_pop_change_reindex = df_pop_change.reset_index()\n", "    elif metric_type == \"quantile\":\n", "        dimension_order = [\"all\", device_cluster_column_name]\n", "        df_pop_change_reindex = {}\n", "        for os_type_lower in df_reshaped_os_type:\n", "            df_pop_change_reindex[os_type_lower] = to_period_over_period_table_quantile(\n", "                data = df_reshaped[df_reshaped[os_type_column_name].str.lower() == os_type_lower].copy().loc[\n", "                            (df_reshaped.dimension.isin(dimension_order))\n", "                        ],\n", "                dimension_order = dimension_order,\n", "                date_field='period',\n", "                dimension_fields=[\"dimension\",\"dimension_values\"],\n", "                target_field=[metric_name, \"event_count\"],\n", "                min_value=0,\n", "                max_value=1000000000000000,\n", "                ascending=False,\n", "                period_1=period_1,\n", "                period_2=period_2\n", "            ).reset_index()\n", "            # remove trivial device clusters\n", "            df_pop_change_reindex[os_type_lower] = df_pop_change_reindex[os_type_lower][\n", "                    ~df_pop_change_reindex[os_type_lower][\"dimension_values\"].isin(device_cluster_to_remove[os_type_lower])\n", "                ].sort_values(by='dimension_values', key=sorter).reset_index(drop=True)\n", "          "]}, {"cell_type": "code", "execution_count": null, "id": "fcc7705f", "metadata": {}, "outputs": [], "source": ["# # Section 3: External and Internal Events\n", "# period_1_start_date_hyphen = pd.to_datetime(period_1_start_date).strftime(\"%Y-%m-%d\")\n", "# period_1_end_date_hyphen = pd.to_datetime(period_1_end_date).strftime(\"%Y-%m-%d\")\n", "# period_2_start_date_hyphen = pd.to_datetime(period_2_start_date).strftime(\"%Y-%m-%d\")\n", "# period_2_end_date_hyphen = pd.to_datetime(period_2_end_date).strftime(\"%Y-%m-%d\")\n", "\n", "# sql_holiday = \"\"\"\n", "#     SELECT \n", "#         holiday, \n", "#         holiday_start_ds, \n", "#         holiday_end_ds,\n", "#         regions,\n", "#         CASE \n", "#             WHEN \n", "#                 (\n", "#                     ('{period_1_start_date_hyphen}' BETWEEN holiday_start_ds AND holiday_end_ds) \n", "#                     OR \n", "#                     ('{period_1_end_date_hyphen}' BETWEEN holiday_start_ds AND holiday_end_ds)\n", "#                     OR \n", "#                     ('{period_1_start_date_hyphen}' < holiday_start_ds AND '{period_1_end_date_hyphen}' > holiday_end_ds)\n", "#                 ) AND (\n", "#                     ('{period_2_start_date_hyphen}' BETWEEN holiday_start_ds AND holiday_end_ds) \n", "#                     OR \n", "#                     ('{period_2_end_date_hyphen}' BETWEEN holiday_start_ds AND holiday_end_ds)\n", "#                     OR \n", "#                     ('{period_2_start_date_hyphen}' < holiday_start_ds AND '{period_2_end_date_hyphen}' > holiday_end_ds) \n", "#                 )\n", "#             THEN 'baseline period {period_1} and investigation period {period_2}'\n", "#             WHEN\n", "#                 ('{period_1_start_date_hyphen}' BETWEEN holiday_start_ds AND holiday_end_ds) \n", "#                 OR \n", "#                 ('{period_1_end_date_hyphen}' BETWEEN holiday_start_ds AND holiday_end_ds)\n", "#                 OR\n", "#                 ('{period_1_start_date_hyphen}' < holiday_start_ds AND '{period_1_end_date_hyphen}' > holiday_end_ds)\n", "#             THEN 'baseline period {period_1}'\n", "#             ELSE 'investigation period {period_2}'\n", "#         END holiday_affected_periods\n", "            \n", "#      FROM (\n", "#         SELECT \n", "#             * EXCEPT(region), \n", "#             STRING_AGG(region ORDER BY region) AS regions, \n", "#             DATE_ADD(ds, INTERVAL lower_window day) AS holiday_start_ds,  \n", "#             DATE_ADD(ds, INTERVAL upper_window day) AS holiday_end_ds\n", "#         FROM `sc-analytics.report_external_data.dau_forecast_holidays` \n", "#         GROUP BY ds, holiday, lower_window, upper_window\n", "#     )\n", "#     WHERE \n", "#          ('{period_1_start_date_hyphen}' BETWEEN holiday_start_ds AND holiday_end_ds) OR \n", "#          ('{period_1_end_date_hyphen}' BETWEEN holiday_start_ds AND holiday_end_ds) OR \n", "#          ('{period_1_start_date_hyphen}' < holiday_start_ds AND '{period_1_end_date_hyphen}' > holiday_end_ds) OR\n", "#          ('{period_2_start_date_hyphen}' BETWEEN holiday_start_ds AND holiday_end_ds) OR \n", "#          ('{period_2_end_date_hyphen}' BETWEEN holiday_start_ds AND holiday_end_ds) OR\n", "#          ('{period_2_start_date_hyphen}' < holiday_start_ds AND '{period_2_end_date_hyphen}' > holiday_end_ds) \n", "#      ORDER BY holiday_start_ds DESC\n", "# \"\"\".format(\n", "#     period_1_start_date_hyphen=period_1_start_date_hyphen,\n", "#     period_1_end_date_hyphen=period_1_end_date_hyphen,\n", "#     period_2_start_date_hyphen=period_2_start_date_hyphen,\n", "#     period_2_end_date_hyphen=period_2_end_date_hyphen,\n", "#     period_1=period_1,\n", "#     period_2=period_2\n", "# )\n", "# if \"3\" in sections:\n", "#     df_holiday = read_gbq(sql_holiday)\n", "# else:\n", "#     df_holiday = pd.DataFrame()\n"]}, {"cell_type": "code", "execution_count": null, "id": "849897de", "metadata": {}, "outputs": [], "source": ["## NEW: "]}, {"cell_type": "code", "execution_count": null, "id": "49117777", "metadata": {}, "outputs": [], "source": ["# New Section: Most Impactful A/B Studies by Metric Change\n", "# The query below limitations:\n", "# 1. Supports Quest A/B metric only. \n", "# 2. Does not support source table filters or user cohorts filters\n", "ab_mis_sql_template = \"\"\"    \n", "SELECT\n", " (CASE WHEN lower(targeting_expression) LIKE '%ios%' or LOWER(targeting_expression) like '%ios%' OR LOWER(segment_name) like '%ios%' then 'iOS' \n", " WHEN lower(targeting_expression) LIKE '%android%' or LOWER(targeting_expression) like '%android%' OR LOWER(segment_name) like '%android%' then 'Android' \n", " WHEN lower(targeting_expression) LIKE '%server%' or LOWER(targeting_expression) like '%server%' OR LOWER(segment_name) like '%server%'  then 'server' else 'unknown (need to validate from launch doc)' end) as os_type,\n", " study_namespace, study_name, user_count,pct_difference, full_rollout_impact_per_day,\n", "study_version, treatment_id, mis_date, targeting_expression, segment_name,\n", "ab_console_link, ab_dashboard_link, good_direction, dash_name, study_decision_launch_doc_link, \n", "study_decision_description, study_decision_launch_app_version, study_decision_updated_by, launch_date, launch_treatment_description, launch_treatment_name, segment_id\n", "\n", "from `sc-portal.ab_launch.launch_impact_view` launch_metric_impact \n", "\n", "WHERE  \n", "lower(metric_name_minor) like '{quest_metric_minor}%'   and lower(metric_name_minor) like '%p50%'\n", "and  upper(metric_name_major) = '{quest_metric_major}'\n", "and p_value < 0.05\n", "and \n", "(launch_metric_impact.dim_name ) = 'global' AND (launch_metric_impact.dim_value ) = 'global'  \n", "AND LAUNCH_DATE BETWEEN  PARSE_DATE('%Y%m%d', '{period_1_start_date}') AND  PARSE_DATE('%Y%m%d', '{period_2_end_date}') \n", "ORDER BY mis_date\n", "limit 10000\n", "\"\"\"\n", "\n", "\n", "if metric_source != \"bigquery\":\n", "    quest_metric_major, quest_metric_minor_without_percentile_suffix = quest_metric.split(\"____\")\n", "\n", "    def date_str_diff(date_str_from, date_str_to):\n", "        delta = datetime.strptime(date_str_to, \"%Y%m%d\") - datetime.strptime(date_str_from, \"%Y%m%d\")\n", "        return delta\n", "\n", "    min_date = min([period_1_start_date, period_1_end_date, period_2_start_date, period_2_end_date])\n", "    max_date = max([period_1_start_date, period_1_end_date, period_2_start_date, period_2_end_date])\n", "\n", "    if date_str_diff(min_date, max_date) > <PERSON><PERSON><PERSON>(days=90):\n", "\n", "        ab_mis_date_range_explanation = f\"Most impact A/B studies from date range {period_1_start_date} to {period_1_end_date}, and from {period_2_start_date} to {period_2_end_date}\"\n", "        ab_mis_period_1_start_date = period_1_start_date\n", "        ab_mis_period_1_end_date = period_1_end_date\n", "        ab_mis_period_2_start_date = period_2_start_date\n", "        ab_mis_period_2_end_date = period_2_end_date\n", "    else:\n", "        ab_mis_date_range_explanation = f\"Most impact A/B studies from date range {min_date} to {max_date}\"\n", "        ab_mis_period_1_start_date = min_date\n", "        ab_mis_period_1_end_date = max_date\n", "        ab_mis_period_2_start_date = min_date\n", "        ab_mis_period_2_end_date = max_date\n", "\n", "    if metric_source == \"quest_ab\": \n", "        ab_mis_sql_p50 = ab_mis_sql_template.format(\n", "                quest_metric_major = quest_metric_major,\n", "                quest_metric_minor=quest_metric_minor_without_percentile_suffix,\n", "                period_1_start_date=ab_mis_period_1_start_date,\n", "                period_1_end_date=ab_mis_period_1_end_date,\n", "                period_2_start_date=ab_mis_period_2_start_date,\n", "                period_2_end_date=ab_mis_period_2_end_date\n", "            ).replace('\\\\', '\\\\\\\\')\n", "    elif metric_source == \"bigquery\":  \n", "        ab_mis_sql_p50 = ab_mis_sql_template.format(\n", "#                 metric_name_filter=\"REPLACE(LOWER(metric_minor), ' ', '_') like '%{}'\".format(metric_name),\n", "                period_1_start_date=ab_mis_period_1_start_date,\n", "                period_1_end_date=ab_mis_period_1_end_date,\n", "                period_2_start_date=ab_mis_period_2_start_date,\n", "                period_2_end_date=ab_mis_period_2_end_date\n", "            ).replace('\\\\', '\\\\\\\\')\n", "#     if \"3\" in sections:\n", "    df_ab_impacted_P50 = read_gbq(ab_mis_sql_p50)\n", "#         date_max = max([period_1_start_date, period_1_end_date, period_2_start_date, period_2_end_date])\n", "#         duc_metrics = list(\n", "#             read_gbq(\"SELECT * FROM `sc-analytics.report_app.dau_user_country_{date_max}` LIMIT 1\".format(\n", "#             date_max=date_max)).columns[17:]\n", "#         )\n", "#         if metric_name in duc_metrics or metric_source == \"quest_ab\":\n", "#             df_ab_impacted[\"ab_dash_link\"] = df_ab_impacted[\"ab_dash_link\"].apply(lambda s: f\"<a href='{s}', target='__blank'>link</a>\")\n", "#             dash_metrics = read_gbq(\"SELECT DISTINCT dash_name FROM `sc-analytics.report_search_v2.launch_metric_impact`\")[\"dash_name\"]        \n"]}, {"cell_type": "code", "execution_count": null, "id": "bef29d09", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4eba2e5e", "metadata": {}, "outputs": [], "source": ["## MIS P50"]}, {"cell_type": "code", "execution_count": null, "id": "14a21848", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4ed6e65a", "metadata": {}, "outputs": [], "source": ["# New Section: Most Impactful A/B Studies by Metric Change\n", "# The query below limitations:\n", "# 1. Supports Quest A/B metric only. \n", "# 2. Does not support source table filters or user cohorts filters\n", "ab_mis_sql_template = \"\"\"    \n", "SELECT\n", " (CASE WHEN lower(targeting_expression) LIKE '%ios%' or LOWER(targeting_expression) like '%ios%' OR LOWER(segment_name) like '%ios%' then 'iOS' \n", " WHEN lower(targeting_expression) LIKE '%android%' or LOWER(targeting_expression) like '%android%' OR LOWER(segment_name) like '%android%' then 'Android' \n", " WHEN lower(targeting_expression) LIKE '%server%' or LOWER(targeting_expression) like '%server%' OR LOWER(segment_name) like '%server%'  then 'server' else 'unknown (need to validate from launch doc)' end) as os_type,\n", " study_namespace, study_name, user_count,pct_difference, full_rollout_impact_per_day,\n", "study_version, treatment_id, mis_date, targeting_expression, segment_name,\n", "ab_console_link, ab_dashboard_link, good_direction, dash_name, study_decision_launch_doc_link, \n", "study_decision_description, study_decision_launch_app_version, study_decision_updated_by, launch_date, launch_treatment_description, launch_treatment_name, segment_id\n", "\n", "from `sc-portal.ab_launch.launch_impact_view` launch_metric_impact \n", "\n", "WHERE  \n", "--- lower(metric_name_minor) like '{quest_metric_minor}%'   and lower(metric_name_minor) like '%p50%'\n", "lower(metric_name_minor) like '{quest_metric_minor}%'   and lower(metric_name_minor) like '%p50%'\n", "and  upper(metric_name_major) = '{quest_metric_major}'\n", "and p_value < 0.05\n", "and \n", "(launch_metric_impact.dim_name ) = 'global' AND (launch_metric_impact.dim_value ) = 'global'  \n", "AND LAUNCH_DATE BETWEEN  PARSE_DATE('%Y%m%d', '{period_1_start_date}') AND  PARSE_DATE('%Y%m%d', '{period_2_end_date}') \n", "ORDER BY mis_date\n", "limit 10000\n", "\"\"\"\n", "\n", "\n", "if metric_source != \"bigquery\":\n", "    quest_metric_major, quest_metric_minor_without_percentile_suffix = quest_metric.split(\"____\")\n", "\n", "    def date_str_diff(date_str_from, date_str_to):\n", "        delta = datetime.strptime(date_str_to, \"%Y%m%d\") - datetime.strptime(date_str_from, \"%Y%m%d\")\n", "        return delta\n", "\n", "    min_date = min([period_1_start_date, period_1_end_date, period_2_start_date, period_2_end_date])\n", "    max_date = max([period_1_start_date, period_1_end_date, period_2_start_date, period_2_end_date])\n", "\n", "    if date_str_diff(min_date, max_date) > <PERSON><PERSON><PERSON>(days=90):\n", "\n", "        ab_mis_date_range_explanation = f\"Most impact A/B studies from date range {period_1_start_date} to {period_1_end_date}, and from {period_2_start_date} to {period_2_end_date}\"\n", "        ab_mis_period_1_start_date = period_1_start_date\n", "        ab_mis_period_1_end_date = period_1_end_date\n", "        ab_mis_period_2_start_date = period_2_start_date\n", "        ab_mis_period_2_end_date = period_2_end_date\n", "    else:\n", "        ab_mis_date_range_explanation = f\"Most impact A/B studies from date range {min_date} to {max_date}\"\n", "        ab_mis_period_1_start_date = min_date\n", "        ab_mis_period_1_end_date = max_date\n", "        ab_mis_period_2_start_date = min_date\n", "        ab_mis_period_2_end_date = max_date\n", "\n", "    if metric_source == \"quest_ab\": \n", "        ab_mis_sql_p50 = ab_mis_sql_template.format(\n", "                quest_metric_major = quest_metric_major,\n", "                quest_metric_minor=quest_metric_minor_without_percentile_suffix,\n", "                period_1_start_date=ab_mis_period_1_start_date,\n", "                period_1_end_date=ab_mis_period_1_end_date,\n", "                period_2_start_date=ab_mis_period_2_start_date,\n", "                period_2_end_date=ab_mis_period_2_end_date\n", "            ).replace('\\\\', '\\\\\\\\')\n", "    elif metric_source == \"bigquery\":  \n", "        ab_mis_sql_p50 = ab_mis_sql_template.format(\n", "#                 metric_name_filter=\"REPLACE(LOWER(metric_minor), ' ', '_') like '%{}'\".format(metric_name),\n", "                period_1_start_date=ab_mis_period_1_start_date,\n", "                period_1_end_date=ab_mis_period_1_end_date,\n", "                period_2_start_date=ab_mis_period_2_start_date,\n", "                period_2_end_date=ab_mis_period_2_end_date\n", "            ).replace('\\\\', '\\\\\\\\')\n", "#     if \"3\" in sections:\n", "    df_ab_impacted_P50 = read_gbq(ab_mis_sql_p50)\n", "#         date_max = max([period_1_start_date, period_1_end_date, period_2_start_date, period_2_end_date])\n", "#         duc_metrics = list(\n", "#             read_gbq(\"SELECT * FROM `sc-analytics.report_app.dau_user_country_{date_max}` LIMIT 1\".format(\n", "#             date_max=date_max)).columns[17:]\n", "#         )\n", "#         if metric_name in duc_metrics or metric_source == \"quest_ab\":\n", "#             df_ab_impacted[\"ab_dash_link\"] = df_ab_impacted[\"ab_dash_link\"].apply(lambda s: f\"<a href='{s}', target='__blank'>link</a>\")\n", "#             dash_metrics = read_gbq(\"SELECT DISTINCT dash_name FROM `sc-analytics.report_search_v2.launch_metric_impact`\")[\"dash_name\"]        \n"]}, {"cell_type": "code", "execution_count": null, "id": "7500e583", "metadata": {}, "outputs": [], "source": ["# print_check\n", "print('check3')"]}, {"cell_type": "code", "execution_count": null, "id": "96b59100", "metadata": {}, "outputs": [], "source": ["## MIS P90"]}, {"cell_type": "code", "execution_count": null, "id": "9bb2dbc9", "metadata": {}, "outputs": [], "source": ["# # Most Impactful A/B Studies by Metric Change\n", "# # The query below limitations:\n", "# # 1. Supports Quest A/B metric only. \n", "# # 2. Does not support source table filters or user cohorts filters\n", "# ab_mis_sql_template = \"\"\"    \n", "# SELECT\n", "#  (CASE WHEN lower(targeting_expression) LIKE '%ios%' or LOWER(targeting_expression) like '%ios%' OR LOWER(segment_name) like '%ios%' then 'iOS' \n", "#  WHEN lower(targeting_expression) LIKE '%android%' or LOWER(targeting_expression) like '%android%' OR LOWER(segment_name) like '%android%' then 'Android' \n", "#  WHEN lower(targeting_expression) LIKE '%server%' or LOWER(targeting_expression) like '%server%' OR LOWER(segment_name) like '%server%'  then 'server' else 'unknown (need to validate from launch doc)' end) as os_type,\n", "#  study_namespace, study_name, user_count,pct_difference, full_rollout_impact_per_day,\n", "# study_version, treatment_id, mis_date, targeting_expression, segment_name,\n", "# ab_console_link, ab_dashboard_link, good_direction, dash_name, study_decision_launch_doc_link, \n", "# study_decision_description, study_decision_launch_app_version, study_decision_updated_by, launch_date, launch_treatment_description, launch_treatment_name, segment_id\n", "\n", "# from `sc-portal.ab_launch.launch_impact_view` launch_metric_impact \n", "\n", "# WHERE  \n", "# lower(metric_name_minor) like '{quest_metric_minor}%'   and lower(metric_name_minor) like '%p90%'\n", "# and  upper(metric_name_major) = '{quest_metric_major}'\n", "# and p_value <= 0.01\n", "# and \n", "# (launch_metric_impact.dim_name ) = 'global' AND (launch_metric_impact.dim_value ) = 'global'  \n", "# AND LAUNCH_DATE BETWEEN  PARSE_DATE('%Y%m%d', '{period_1_start_date}') AND  PARSE_DATE('%Y%m%d', '{period_2_end_date}') \n", "# ORDER BY mis_date\n", "# limit 10000\n", "# \"\"\"\n", "\n", "\n", "# if metric_source != \"bigquery\":\n", "#     quest_metric_major, quest_metric_minor_without_percentile_suffix = quest_metric.split(\"____\")\n", "\n", "#     def date_str_diff(date_str_from, date_str_to):\n", "#         delta = datetime.strptime(date_str_to, \"%Y%m%d\") - datetime.strptime(date_str_from, \"%Y%m%d\")\n", "#         return delta\n", "\n", "#     min_date = min([period_1_start_date, period_1_end_date, period_2_start_date, period_2_end_date])\n", "#     max_date = max([period_1_start_date, period_1_end_date, period_2_start_date, period_2_end_date])\n", "\n", "#     if date_str_diff(min_date, max_date) > <PERSON><PERSON><PERSON>(days=90):\n", "\n", "#         ab_mis_date_range_explanation = f\"Most impact A/B studies from date range {period_1_start_date} to {period_1_end_date}, and from {period_2_start_date} to {period_2_end_date}\"\n", "#         ab_mis_period_1_start_date = period_1_start_date\n", "#         ab_mis_period_1_end_date = period_1_end_date\n", "#         ab_mis_period_2_start_date = period_2_start_date\n", "#         ab_mis_period_2_end_date = period_2_end_date\n", "#     else:\n", "#         ab_mis_date_range_explanation = f\"Most impact A/B studies from date range {min_date} to {max_date}\"\n", "#         ab_mis_period_1_start_date = min_date\n", "#         ab_mis_period_1_end_date = max_date\n", "#         ab_mis_period_2_start_date = min_date\n", "#         ab_mis_period_2_end_date = max_date\n", "\n", "#     if metric_source == \"quest_ab\": \n", "#         ab_mis_sql_p90 = ab_mis_sql_template.format(\n", "#                 quest_metric_major = quest_metric_major,\n", "#                 quest_metric_minor=quest_metric_minor_without_percentile_suffix,\n", "#                 period_1_start_date=ab_mis_period_1_start_date,\n", "#                 period_1_end_date=ab_mis_period_1_end_date,\n", "#                 period_2_start_date=ab_mis_period_2_start_date,\n", "#                 period_2_end_date=ab_mis_period_2_end_date\n", "#             ).replace('\\\\', '\\\\\\\\')\n", "#     elif metric_source == \"bigquery\":  \n", "#         ab_mis_sql_p90 = ab_mis_sql_template.format(\n", "# #                 metric_name_filter=\"REPLACE(LOWER(metric_minor), ' ', '_') like '%{}'\".format(metric_name),\n", "#                 period_1_start_date=ab_mis_period_1_start_date,\n", "#                 period_1_end_date=ab_mis_period_1_end_date,\n", "#                 period_2_start_date=ab_mis_period_2_start_date,\n", "#                 period_2_end_date=ab_mis_period_2_end_date\n", "#             ).replace('\\\\', '\\\\\\\\')\n", "# #     if \"3\" in sections:\n", "#     df_ab_impacted_P90 = read_gbq(ab_mis_sql_p90)\n", "        \n", "# #         date_max = max([period_1_start_date, period_1_end_date, period_2_start_date, period_2_end_date])\n", "# #         duc_metrics = list(\n", "# #             read_gbq(\"SELECT * FROM `sc-analytics.report_app.dau_user_country_{date_max}` LIMIT 1\".format(\n", "# #             date_max=date_max)).columns[17:]\n", "# #         )\n", "# #         if metric_name in duc_metrics or metric_source == \"quest_ab\":\n", "# #             df_ab_impacted[\"ab_dash_link\"] = df_ab_impacted[\"ab_dash_link\"].apply(lambda s: f\"<a href='{s}', target='__blank'>link</a>\")\n", "# #             dash_metrics = read_gbq(\"SELECT DISTINCT dash_name FROM `sc-analytics.report_search_v2.launch_metric_impact`\")[\"dash_name\"]        \n"]}, {"cell_type": "code", "execution_count": null, "id": "22c46439", "metadata": {}, "outputs": [], "source": ["# ab_mis_sql"]}, {"cell_type": "code", "execution_count": null, "id": "c1a053b1", "metadata": {}, "outputs": [], "source": ["# SELECT\n", "#   CASE \n", "#     WHEN LOWER(targeting_expression) LIKE '%ios%' THEN 'iOS'\n", "#     WHEN LOWER(targeting_expression) LIKE '%android%' THEN 'Android'\n", "#     WHEN LOWER(targeting_expression) LIKE '%server%' THEN 'server'\n", "#     ELSE 'unknown (need to validate from launch doc)' \n", "#   END AS os_type,\n", "  \n", "#   study_namespace,\n", "#   study_name,\n", "#   user_count,\n", "#   pct_difference,\n", "#   full_rollout_impact_per_day,\n", "#   study_version,\n", "#   treatment_id,\n", "#   mis_date,\n", "#   targeting_expression,\n", "#   segment_name,\n", "#   ab_console_link,\n", "#   ab_dashboard_link,\n", "#   good_direction,\n", "#   dash_name,\n", "#   study_decision_launch_doc_link,\n", "#   study_decision_description,\n", "#   study_decision_launch_app_version,\n", "#   study_decision_updated_by,\n", "#   launch_date,\n", "#   launch_treatment_description,\n", "#   launch_treatment_name,\n", "#   segment_id\n", "\n", "# FROM `sc-portal.ab_launch.launch_impact_view` launch_metric_impact\n", "\n", "# WHERE  \n", "#   LOWER(metric_name_minor) LIKE 'camera_cold_startup_latency%' \n", "#   AND UPPER(metric_name_major) = 'GHOST_TO_SNAPPABLE'\n", "#   AND p_value <= 0.01\n", "#   AND launch_metric_impact.dim_name = 'global'\n", "#   AND launch_metric_impact.dim_value = 'global'  \n", "#   AND launch_date BETWEEN '2025-01-01' AND '2025-05-14'\n", "\n", "# ORDER BY mis_date\n", "# LIMIT 10000;"]}, {"cell_type": "code", "execution_count": null, "id": "80da6216", "metadata": {}, "outputs": [], "source": ["# # Most Impactful A/B Studies by Metric Change\n", "# # The query below limitations:\n", "# # 1. Supports Quest A/B metric only. \n", "# # 2. Does not support source table filters or user cohorts filters\n", "# ab_mis_sql_template = \"\"\"\n", "# WITH metric_impact AS (\n", "#     SELECT\n", "#         mis.mis_date as mis_date, # Date Filter\n", "#         study_info.namespace as namespace,\n", "#         # Study & Treatment Descrption\n", "#         IF(info.segment_id is null,\n", "#               mis.study_name,\n", "#               study_info.segmentation_parent_study_name)\n", "#           as study_name,\n", "#         cast(mis.study_version AS STRING) as study_version,\n", "#         info.segment_name as segment_name,\n", "#         info.segment_id as segment_id,\n", "#         info.segment_version as segment_version,\n", "#         cast(mis.treatment_id AS STRING) as treatment_id,\n", "#         treatment_name,\n", "#         treatment_description,\n", "#         treatment_traffic_pct/100 as treatment_traffic_pct,\n", "#         IF(info.segment_id is null,\n", "#               concat(\"https://ab.sc-corp.net/v2/experiment/\",mis.study_name,\"/\",CAST(mis.study_version AS STRING), \"?utm_source=metric_impact&utm_medium=lego\"),\n", "#               concat(\"https://ab.sc-corp.net/v2/experiment/\",study_info.segmentation_parent_study_name,\"?utm_source=metric_impact&utm_medium=lego\"))\n", "#           as ab_console_link,\n", "#         IF(info.segment_id is null,\n", "#               concat(\"https://ab.sc-corp.net/v2/analysis/\",mis.study_name,\"/\",CAST(mis.study_version AS STRING), \"?utm_source=metric_impact&utm_medium=lego\"),\n", "#               concat(\"https://ab.sc-corp.net/v2/analysis/\",study_info.segmentation_parent_study_name,\"?\", \"segmentId=\",CAST(info.segment_id AS STRING),\"&segmentVersion=\" , CAST(info.segment_version AS STRING), \"&utm_source=metric_impact&utm_medium=lego\"))\n", "#           as ab_dash_link,\n", "\n", "#         study_info.is_segmentation_study as is_segmentation_study,\n", "#         study_info.client_os as client_os,\n", "#         study_info.serve_platforms as serve_platforms,\n", "#         study_info.trigger_option as trigger_option,\n", "#         # Unique identifiers for meta-analysis\n", "#         concat(mis.mis_date,mis.study_name) as study_analysis_date,\n", "#         concat(mis.study_name, CAST(mis.study_version AS STRING)) as unique_study_id,\n", "#         concat(mis.study_name, CAST(mis.study_version AS STRING), CAST(mis.treatment_id AS STRING)) as unique_treatment_id,\n", "#         is_control,\n", "#         IF(info.segment_id is null, false, true) as is_cof_ab,\n", "#         # Metric Description\n", "#         metric_name as full_metric_name,\n", "#         REGEXP_EXTRACT(metric_name, r\"^(.*?)____\") as metric_major,\n", "#         REGEXP_EXTRACT(metric_name, r\".*____(.*)$\") as metric_minor,\n", "#         metric_metadata.unnest_compass_group_ids as dash_group_ids,\n", "#         metric_metadata.dash_group_name as dash_group_name,\n", "#         IF(LENGTH(metric_metadata.dash_name) > 0, metric_metadata.dash_name, metric_name) AS metric_name,\n", "#         metric_metadata.description as metric_description,\n", "#         good_direction as metric_good_direction,\n", "#         # Decison Description\n", "#         decision AS launch_decision,\n", "#         launch_doc_link,\n", "#         PARSE_DATE('%Y-%m-%d', launch_start_date) AS launch_start_date,\n", "#         is_ui_change,\n", "#         mis.treatment_id = cast(launch_decision.launch_treatment_id as string) AS is_launched_treatment,\n", "#         # MIS Metrics\n", "#         avg_per_user,\n", "#         ci_lower_bound,\n", "#         ci_upper_bound,\n", "#         running_days,\n", "#         triggered_impact_pct,\n", "#         user_count,\n", "#         p_value,\n", "#         bh_p_value,\n", "#         full_rollout_impact_per_day,\n", "#         full_rollout_impact_per_day_pct,\n", "#         realized_impact_per_day,\n", "#         realized_impact_per_day_pct,\n", "#         # Meta-analysis metrics\n", "#         1 as cnt,\n", "#         IF(p_value <= 0.05, 1, 0) as pval_0p05_detected,\n", "#         IF(p_value <= 0.01, 1, 0) as pval_0p01_detected,\n", "#         IF(p_value <= 0.001, 1, 0) as pval_0p001_detected,\n", "#         IF(bh_p_value  <= 0.05, 1, 0) as bh_0p05_detected,\n", "#         IF(bh_p_value <= 0.1, 1, 0) as bh_0p1_detected,\n", "#         IF(bh_p_value<= 0.01, 1, 0) as bh_0p01_detected,\n", "#         IF(p_value <= 0.001 and bh_p_value <= 0.05 , 1, 0) as pval_0p001_bh_0p05_detected,\n", "#         IF(p_value> 0.001 and bh_p_value <= 0.05 , 1, 0) as pval_0p001_bh_0p05_disagree_bhdetected,\n", "#         IF(p_value <= 0.001 and bh_p_value > 0.05 , 1, 0) as pval_0p001_bh_0p05_disagree_pdetected,\n", "#         IF(p_value <= 0.05 and bh_p_value <= 0.05 , 1, 0) as pval_0p05_bh_0p05_detected,\n", "#         IF(p_value<= 0.1 and bh_p_value<= 0.05 , 1, 0) as pval_0p1_bh_0p05_detected,\n", "#         info.study_name info_study_name\n", "        \n", "#       FROM # RANKED MIS so that we get only the last impact report\n", "#       (\n", "#         SELECT\n", "#         metric_impact.mis_date as mis_date,\n", "#         metric_impact.study_name as study_name,\n", "#         metric_impact.study_version as study_version,\n", "#         metric_impact.treatment_id as treatment_id,\n", "#         metric_impact.metric_name as metric_name,\n", "#         coalesce(cond.avg_per_user, metric_impact.avg_per_user) as avg_per_user,\n", "#         coalesce(cond.ci_lower_bound, metric_impact.ci_lower_bound) as ci_lower_bound,\n", "#         coalesce(cond.ci_upper_bound, metric_impact.ci_upper_bound) as ci_upper_bound,\n", "#         coalesce(cond.running_days, metric_impact.running_days) as running_days,\n", "#         coalesce(cond.triggered_impact_pct, metric_impact.triggered_impact_pct) as triggered_impact_pct,\n", "#         coalesce(cond.user_count, metric_impact.user_count) as user_count,\n", "#         coalesce(cond.p_value, metric_impact.p_value) as p_value,\n", "#         coalesce(cond.bh_p_value, metric_impact.bh_p_value) as bh_p_value,\n", "#         coalesce(cond.full_rollout_impact_per_day, metric_impact.full_rollout_impact_per_day) as full_rollout_impact_per_day,\n", "#         coalesce(cond.full_rollout_impact_per_day_pct, metric_impact.full_rollout_impact_per_day_pct) as full_rollout_impact_per_day_pct,\n", "#         coalesce(cond.realized_impact_per_day, metric_impact.realized_impact_per_day) as realized_impact_per_day,\n", "#         coalesce(cond.realized_impact_per_day_pct, metric_impact.realized_impact_per_day_pct) as realized_impact_per_day_pct,\n", "#         RANK() OVER (PARTITION BY metric_impact.study_name, metric_impact.study_version ORDER BY metric_impact.running_days DESC)  as rank,\n", "#         FROM\n", "#         (SELECT\n", "#           TIMESTAMP(PARSE_DATE('%Y%m%d', CONCAT(\"2\",_TABLE_SUFFIX))) as mis_date,\n", "#           study_name,\n", "#         study_version,\n", "#         cast(treatment_id as string) as treatment_id,\n", "#         metric_name,\n", "#         avg_per_user,\n", "#         ci_lower_bound,\n", "#         ci_upper_bound,\n", "#         running_days,\n", "#         triggered_impact_pct,\n", "#         user_count,\n", "#         p_value,\n", "#         bh_p_value,\n", "#         full_rollout_impact_per_day,\n", "#         full_rollout_impact_per_day_pct,\n", "#         realized_impact_per_day,\n", "#         realized_impact_per_day_pct\n", "#        FROM `sc-portal.ab_metric_impact.metric_impact_2*`\n", "#         WHERE CONCAT(\"2\",_TABLE_SUFFIX)  BETWEEN '{period_1_start_date}' AND '{period_1_end_date}'\n", "#             OR CONCAT(\"2\",_TABLE_SUFFIX)  BETWEEN '{period_2_start_date}' AND '{period_2_end_date}'\n", "#         ) metric_impact\n", "#        LEFT JOIN\n", "#         (SELECT\n", "#           TIMESTAMP(PARSE_DATE('%Y%m%d', CONCAT(\"2\",_TABLE_SUFFIX))) as mis_date,\n", "#           study_name,\n", "#         study_version,\n", "#         treatment_id,\n", "#         metric_name,\n", "#         treatment_mean as avg_per_user,\n", "#         absolute_conf_lb as ci_lower_bound,\n", "#         absolute_conf_ub as ci_upper_bound,\n", "#         days as running_days,\n", "#         pct_difference as triggered_impact_pct,\n", "#         t_n as user_count,\n", "#         p_value,\n", "#         bh_p_value,\n", "#         full_rollout_impact_per_day,\n", "#         full_rollout_impact_percent as full_rollout_impact_per_day_pct,\n", "#         realized_impact_per_day,\n", "#         realized_impact_percent as realized_impact_per_day_pct\n", "#        FROM `sc-portal.ab_metric_impact.cond_metric_impact_2*`\n", "#        WHERE CONCAT(\"2\",_TABLE_SUFFIX)  BETWEEN '{period_1_start_date}' AND '{period_1_end_date}'\n", "#             OR CONCAT(\"2\",_TABLE_SUFFIX)  BETWEEN '{period_2_start_date}' AND '{period_2_end_date}'\n", "#         ) cond\n", "#         on metric_impact.study_name = cond.study_name and metric_impact.study_version = cond.study_version and metric_impact.treatment_id = cond.treatment_id and metric_impact.metric_name = cond.metric_name and metric_impact.mis_date = cond.mis_date\n", "\n", "#       ) AS mis\n", "\n", "#       LEFT JOIN ( #  <PERSON><PERSON>adata on the date of MIS for metric descripitons\n", "#         SELECT\n", "#           TIMESTAMP(PARSE_DATE('%Y%m%d', CONCAT(\"2\",_TABLE_SUFFIX))) AS meta_date,\n", "#           *\n", "#         FROM `sc-portal.quest.metadata_2*` CROSS JOIN UNNEST(compass_group_ids) as unnest_compass_group_ids\n", "#         WHERE CONCAT(\"2\",_TABLE_SUFFIX)  BETWEEN '{period_1_start_date}' AND '{period_1_end_date}'\n", "#             OR CONCAT(\"2\",_TABLE_SUFFIX)  BETWEEN '{period_2_start_date}' AND '{period_2_end_date}'\n", "#       ) AS metric_metadata\n", "#       ON metric_metadata.major_metric = REGEXP_EXTRACT(mis.metric_name, r\"^(.*?)__\") AND metric_metadata.minor_metric = REGEXP_EXTRACT(metric_name, r\".*__(.*)\") AND mis.mis_date = metric_metadata.meta_date\n", "#       LEFT JOIN # Latest Treatment info for treatment descripitons\n", "#       (\n", "#         SELECT\n", "#           *\n", "#         FROM `sc-portal.ab_metric_impact.study_treatment_info_latest`\n", "#       ) AS info\n", "#       ON mis.treatment_id = cast(info.treatment_id as string) AND mis.study_name = info.study_name\n", "#       JOIN (SELECT * FROM   `sc-portal.ab_metric_impact.study_info_latest`) study_info\n", "#                  ON mis.study_name = study_info.study_name AND mis.study_version = study_info.study_version\n", "#       LEFT JOIN (# Latest Launch Decision table\n", "#         SELECT\n", "#           *\n", "#         FROM\n", "#           `sc-portal.ab_metric_impact.launch_decision_latest`\n", "#         WHERE\n", "#           is_latest\n", "#       ) AS launch_decision\n", "#       ON mis.study_name = launch_decision.study_name AND mis.study_version = launch_decision.study_version\n", "#       WHERE rank = 1 # Filter out to latest MIS for each study/version\n", "# )\n", "    \n", "#     SELECT * EXCEPT(realized_impact_per_day_pct_tmp)\n", "#     FROM(\n", "#     SELECT\n", "#         CAST(mis_date AS DATE) AS ds,\n", "#         metric_name,\n", "#         study_name,\n", "#         treatment_name,\n", "#         CONCAT(LEFT(CAST(ROUND(treatment_traffic_pct, 5)*100 AS STRING), 5), '%') AS treatment_traffic_pct,\n", "#         CONCAT(LEFT(CAST(ROUND(COALESCE(realized_impact_per_day_pct, 0), 5)*100 AS STRING), 5), '%') AS realized_impact_per_day_pct,\n", "#         realized_impact_per_day_pct AS realized_impact_per_day_pct_tmp,\n", "#         CONCAT(LEFT(CAST(ROUND(COALESCE(full_rollout_impact_per_day_pct, 0), 5)*100 AS STRING),5), '%') AS full_rollout_impact_per_day_pct,\n", "#         LEFT(CAST(ROUND(p_value, 5)  AS STRING), 5) AS p_value,\n", "#         CONCAT(LEFT(CAST(ROUND(triggered_impact_pct, 5)*100 AS STRING), 5), '%') AS treatment_effect_pct,\n", "#         -- MAX(launch_doc_link) AS launch_doc_link,\n", "#         MAX(user_count) AS user_count_in_treatment,\n", "#         MAX(ab_dash_link) AS ab_dash_link,\n", "#     FROM metric_impact\n", "#     WHERE (study_name NOT LIKE '%DUM\\\\_%' AND study_name NOT LIKE '%CONSOLE\\\\_V2%' AND study_name NOT LIKE '%\\\\_QA\\\\_%' OR study_name IS NULL)\n", "#         AND (NOT COALESCE(is_control , FALSE)) -- AND (dash_group_ids LIKE '%ab/core\\\\_%')\n", "#         AND ((treatment_traffic_pct > 0 AND treatment_traffic_pct < 100))\n", "#         AND (avg_per_user > 0.005 OR avg_per_user  < -0.005)\n", "#         AND (bh_p_value  <= 0.05)\n", "#         AND {metric_name_filter}\n", "#     GROUP BY 1,2,3,4,5,6,7,8,9,10\n", "#     HAVING user_count_in_treatment > 10000 AND ABS(realized_impact_per_day_pct_tmp) > 0.0001\n", "#     )\n", "#     ORDER BY ABS(realized_impact_per_day_pct_tmp) DESC, ds\n", "#     LIMIT 10\n", "# \"\"\"\n", "\n", "\n", "# if metric_source != \"bigquery\":\n", "#     quest_metric_major, quest_metric_minor_without_percentile_suffix = quest_metric.split(\"____\")\n", "\n", "#     def date_str_diff(date_str_from, date_str_to):\n", "#         delta = datetime.strptime(date_str_to, \"%Y%m%d\") - datetime.strptime(date_str_from, \"%Y%m%d\")\n", "#         return delta\n", "\n", "#     min_date = min([period_1_start_date, period_1_end_date, period_2_start_date, period_2_end_date])\n", "#     max_date = max([period_1_start_date, period_1_end_date, period_2_start_date, period_2_end_date])\n", "\n", "#     if date_str_diff(min_date, max_date) > <PERSON><PERSON><PERSON>(days=90):\n", "\n", "#         ab_mis_date_range_explanation = f\"Most impact A/B studies from date range {period_1_start_date} to {period_1_end_date}, and from {period_2_start_date} to {period_2_end_date}\"\n", "#         ab_mis_period_1_start_date = period_1_start_date\n", "#         ab_mis_period_1_end_date = period_1_end_date\n", "#         ab_mis_period_2_start_date = period_2_start_date\n", "#         ab_mis_period_2_end_date = period_2_end_date\n", "#     else:\n", "#         ab_mis_date_range_explanation = f\"Most impact A/B studies from date range {min_date} to {max_date}\"\n", "#         ab_mis_period_1_start_date = min_date\n", "#         ab_mis_period_1_end_date = max_date\n", "#         ab_mis_period_2_start_date = min_date\n", "#         ab_mis_period_2_end_date = max_date\n", "\n", "#     if metric_source == \"quest_ab\": \n", "#         ab_mis_sql = ab_mis_sql_template.format(\n", "#                 metric_name_filter=(\n", "#                     \"metric_major = '{0}' AND \"\n", "#                     \"(REGEXP_CONTAINS(metric_minor, r'^{1}_[Pp][59]0$') OR metric_minor = '{1}')\"\n", "#                 ).format(\n", "#                     quest_metric_major,\n", "#                     quest_metric_minor_without_percentile_suffix\n", "#                 ),\n", "#                 period_1_start_date=ab_mis_period_1_start_date,\n", "#                 period_1_end_date=ab_mis_period_1_end_date,\n", "#                 period_2_start_date=ab_mis_period_2_start_date,\n", "#                 period_2_end_date=ab_mis_period_2_end_date\n", "#             ).replace('\\\\', '\\\\\\\\')\n", "#     elif metric_source == \"bigquery\":  \n", "#         ab_mis_sql = ab_mis_sql_template.format(\n", "#                 metric_name_filter=\"REPLACE(LOWER(metric_minor), ' ', '_') like '%{}'\".format(metric_name),\n", "#                 period_1_start_date=ab_mis_period_1_start_date,\n", "#                 period_1_end_date=ab_mis_period_1_end_date,\n", "#                 period_2_start_date=ab_mis_period_2_start_date,\n", "#                 period_2_end_date=ab_mis_period_2_end_date\n", "#             ).replace('\\\\', '\\\\\\\\')\n", "#     if \"3\" in sections:\n", "#         df_ab_impacted = read_gbq(ab_mis_sql)\n", "#         date_max = max([period_1_start_date, period_1_end_date, period_2_start_date, period_2_end_date])\n", "#         duc_metrics = list(\n", "#             read_gbq(\"SELECT * FROM `sc-analytics.report_app.dau_user_country_{date_max}` LIMIT 1\".format(\n", "#             date_max=date_max)).columns[17:]\n", "#         )\n", "#         if metric_name in duc_metrics or metric_source == \"quest_ab\":\n", "#             df_ab_impacted[\"ab_dash_link\"] = df_ab_impacted[\"ab_dash_link\"].apply(lambda s: f\"<a href='{s}', target='__blank'>link</a>\")\n", "#             dash_metrics = read_gbq(\"SELECT DISTINCT dash_name FROM `sc-analytics.report_search_v2.launch_metric_impact`\")[\"dash_name\"]        \n"]}, {"cell_type": "code", "execution_count": null, "id": "fbc2929a", "metadata": {}, "outputs": [], "source": ["# print_check\n", "print('check4')"]}, {"cell_type": "code", "execution_count": null, "id": "3c9ea5f5", "metadata": {}, "outputs": [], "source": ["# New Section: Fixed cohort\n", "\n", "# NON-USER INPUTS -------------------------------------------------------------------------------\n", "\n", "# format any metrics that are used in breakdown_features or cohort_restrictions\n", "\n", "# sampling rules    \n", "# sampling 0.1% users when testing\n", "IS_TESTING = True\n", "if IS_TESTING: \n", "    # CONCAT('a', ghost_user_id) is to avoid hash function conflict with section 8.2\n", "    sampling_filter = \"AND MOD(COALESCE(abs(FARM_FINGERPRINT(CONCAT('a', ghost_user_id))), 0), 1000) = 0\"\n", "    sampling_filter2 = \"AND MOD(COALESCE(abs(FARM_FINGERPRINT(CONCAT('a', t.ghost_user_id))), 0), 1000) = 0\"\n", "    sampling_ratio = 0.001\n", "# sampling 1%, notify users that sampling will be done to reduce query load when the length of dates combined surpasses 14 days\n", "elif period_1_dates_length + period_2_dates_length > 14:\n", "    sampling_filter = \"AND MOD(COALESCE(abs(FARM_FINGERPRINT(CONCAT('a', ghost_user_id))), 0), 1000) = 0\"\n", "    sampling_filter2 = \"AND MOD(COALESCE(abs(FARM_FINGERPRINT(CONCAT('a', t.ghost_user_id))), 0), 1000) = 0\"\n", "    sampling_ratio = 0.001\n", "    warnings.warn(\"Combined period lengths exceed 14 days.  Results will be from a 1% user sample\")\n", "else: \n", "    sampling_filter = \"AND MOD(COALESCE(abs(FARM_FINGERPRINT(CONCAT('a', ghost_user_id))), 0), 100) = 0\"\n", "    sampling_filter2 = \"AND MOD(COALESCE(abs(FARM_FINGERPRINT(CONCAT('a', t.ghost_user_id))), 0), 100) = 0\"\n", "    sampling_ratio = 0.01\n", "    warnings.warn(\"Results will be from a 10% user sample\")\n", "#     sampling_filter = \"\"\n", "#     sampling_ratio = 1\n", "\n", " \n", "# optional features to break down by\n", "breakdown_features = []\n", "# fix other features that must be equal on both days for cohorts\n", "cohort_restrictions = []\n", "# apply filters to users\n", "user_filters = \"\"\"{}\"\"\"\n", "\n", "date_filter_start_periods = \"\"\"\n", "        (\n", "            CONCAT(\"20\", _TABLE_SUFFIX) BETWEEN \"{period_1_start_date}\" AND \"{period_1_end_date}\"\n", "        )\n", "\"\"\".format(**user_cohorts_template_dict) \n", "\n", "date_filter_end_periods = \"\"\"\n", "        (\n", "            CONCAT(\"20\", _TABLE_SUFFIX) BETWEEN \"{period_2_start_date}\" AND \"{period_2_end_date}\"\n", "        )\n", "\"\"\".format(**user_cohorts_template_dict) \n", "    \n", "    \n", "\n", "#                 --{user_filters}\n", "#                -- {user_filters_2}\n", "\n", "\n", "# NON-USER INPUTS -------------------------------------------------------------------------------\n", "\n", "# format any metrics that are used in breakdown_features or cohort_restrictions\n", "\n", "# if user_filters == \"\":\n", "#     user_filters = {}\n", "# else:\n", "#     user_filters = eval(user_filters)\n", "#     if \"platform\" in user_filters:\n", "#         user_filters[\"platform\"] = user_filters[\"platform\"].upper()\n", "\n", "if isinstance(breakdown_features, str):\n", "    breakdown_features = [breakdown_features]\n", "    \n", "if isinstance(cohort_restrictions, str):\n", "    cohort_restrictions = [cohort_restrictions]\n", "    \n", "user_metrics = list(set.union(set(cohort_restrictions), set(breakdown_features)))\n", "\n", "def get_query(fix_cohort=False):\n", "    template_SQL = \"\"\"\n", "    WITH\n", "        cohort_start AS (\n", "            SELECT\n", "                ghost_user_id\n", "                \n", "            FROM\n", "                `{table_path}_20*`\n", "            WHERE\n", "                {date_filter_start_periods}\n", "                {sampling_filter}\n", "                AND {metric_name} is not null\n", "            GROUP BY\n", "                1), --{groupby}\n", "\n", "        cohort_end AS (\n", "            SELECT\n", "                ghost_user_id\n", "            FROM\n", "                `{table_path}_20*`\n", "            WHERE\n", "                {date_filter_end_periods}\n", "                {sampling_filter}\n", "                AND {metric_name} is not null\n", "            GROUP BY\n", "                1), --{groupby}\n", "\n", "        active_on_both AS (\n", "            SELECT\n", "                t2.ghost_user_id AS ghost_user_id\n", "            FROM\n", "                cohort_end t2\n", "            JOIN\n", "                cohort_start t1\n", "            ON\n", "                t2.ghost_user_id = t1.ghost_user_id\n", "                {cohort_restrictions}            \n", "                ),\n", "\n", "        events AS (\n", "            SELECT\n", "                case when {date_filter_end_periods} then 'end_period' when {date_filter_start_periods} then 'start_period' end as event_date,\n", "                t.ghost_user_id,\n", "                {metric_name}\n", "            FROM\n", "                `{table_path}_20*` t  \n", "                {fix_cohort}\n", "            WHERE\n", "                ({date_filter_start_periods} or {date_filter_end_periods})\n", "                AND {metric_name} IS NOT NULL\n", "                {sampling_filter2}\n", "                )\n", "\n", "    SELECT\n", "        event_date,\n", "        {breakdown_features}\n", "        APPROX_QUANTILES({metric_name}, 100000)[OFFSET(10000)] AS p10,\n", "        APPROX_QUANTILES({metric_name}, 100000)[OFFSET(50000)] AS p50,\n", "        APPROX_QUANTILES({metric_name}, 100000)[OFFSET(90000)] AS p90,\n", "        COUNT(*) AS count\n", "    FROM\n", "        events\n", "    GROUP BY\n", "        1\n", "        {groupby_2}\n", "    ORDER BY\n", "        1\n", "        {groupby_2}\n", "    \"\"\"\n", "    \n", "    return template_SQL.format( \\\n", "          date_filter_start_periods=date_filter_start_periods,\n", "          date_filter_end_periods=date_filter_end_periods,\n", "          metric_name = metric_name,\n", "          sampling_filter = sampling_filter,\n", "          sampling_filter2 = sampling_filter2,\n", "          user_metrics = \", \".join(user_metrics),\n", "#           user_filters = \"\" if len(user_filters) == 0 else\n", "#               \"WHERE \" + \" AND \".join([i + \" \" + user_filters[i] for i in user_filters.keys()]),\n", "          groupby = \"\" if len(user_metrics) == 0 else\n", "                               \", \" + \", \".join([str(i) for i in range(2, len(user_metrics) + 2)]),\n", "#           user_filters_2 = \"\" if len(user_filters) == 0 else\n", "#               \"AND \" + \" AND \".join([i + \" \" + user_filters[i] for i in user_filters.keys()]),\n", "          table_path = table_path,\n", "          fix_cohort = \"\" if fix_cohort == False else\n", "               \"JOIN active_on_both b ON t.ghost_user_id = b.ghost_user_id\",\n", "          cohort_restrictions = \"\" if len(cohort_restrictions) == 0 else\n", "               \"AND \" + \" AND \".join([\"t2.{metric} = t1.{metric}\".format(metric = m) for m in cohort_restrictions]),\n", "          breakdown_features = \"\", # \"{breakdown_features}\",\n", "          groupby_2 = \"\" #\"{groupby_2}\"\n", "          )"]}, {"cell_type": "code", "execution_count": null, "id": "8121fe3b", "metadata": {}, "outputs": [], "source": ["len(breakdown_features)"]}, {"cell_type": "code", "execution_count": null, "id": "fca54158", "metadata": {}, "outputs": [], "source": ["# if len(breakdown_features) == 0:\n", "#     data1 = read_gbq(get_query(False))\n", "#     data2 = read_gbq(get_query(True))    \n", "#     print(\"Raw cohort:\")\n", "#     print(data1)\n", "#     print(\"Fixed cohort:\")\n", "#     print(data2)\n", "#     # plot raw cohort\n", "#     plt.subplots(1, 2, sharey=\"row\", figsize=(18, 8))\n", "\n", "#     plt.subplot(1, 2, 1)\n", "# #     start = data1[data1[\"event_date\"] == date_start]\n", "# #     end = data1[data1[\"event_date\"] == date_end]\n", "#     start = data1[data1[\"event_date\"] == 'start_period']\n", "#     end = data1[data1[\"event_date\"] == 'end_period']\n", "    \n", "#     plt.title(\"Raw Cohort change between {} and {}\".format(period_1_start_date, period_2_end_date))\n", "#     plt.bar([\"p10\", \"p50\", \"p90\"], [(end[\"p10\"].iloc[0] / start[\"p10\"].iloc[0] - 1) * 100, \\\n", "#                                     (end[\"p50\"].iloc[0] / start[\"p50\"].iloc[0] - 1) * 100, \\\n", "#                                     (end[\"p90\"].iloc[0] / start[\"p90\"].iloc[0] - 1) * 100])\n", "#     plt.ylabel(\"percent change (%)\")\n", "    \n", "#     # plot fixed cohort\n", "#     plt.subplot(1, 2, 2) \n", "#     start = data2[data2[\"event_date\"] == 'start_period']\n", "#     end = data2[data2[\"event_date\"] == 'end_period']\n", "#     plt.title(\"Fixed Cohort change between {} and {}\".format(period_1_start_date, period_2_end_date))\n", "#     plt.bar([\"p10\", \"p50\", \"p90\"], [(end[\"p10\"].iloc[0] / start[\"p10\"].iloc[0] - 1) * 100, \\\n", "#                                     (end[\"p50\"].iloc[0] / start[\"p50\"].iloc[0] - 1) * 100, \\\n", "#                                     (end[\"p90\"].iloc[0] / start[\"p90\"].iloc[0] - 1) * 100], color=\"orange\")\n", "#     plt.ylabel(\"percent change (%)\")\n", "    \n", "#     plt.show()\n", "    \n", "# # else:\n", "# #     for f in breakdown_features:\n", "# #         print(\"\\nBreakdown by {}\\n\".format(f))\n", "# #         data1 = read_gbq(get_query(False))\n", "# #         data2 = read_gbq(get_query(True))    \n", "# # #         data1 = pd.read_gbq(get_query(False).format(breakdown_features=f+\",\", groupby_2=\", \"+f), \\\n", "# # #                             project_id = \"sc-bq-gcs-billingonly\", dialect = \"standard\")\n", "# # #         data2 = pd.read_gbq(get_query(True).format(breakdown_features=f+\",\", groupby_2=\", \"+f), \\\n", "# # #                             project_id = \"sc-bq-gcs-billingonly\", dialect = \"standard\")\n", "# #         print(\"Raw cohort:\")\n", "# #         print(data1)\n", "# #         print()\n", "# #         print(\"Fixed cohort:\")\n", "# #         print(data2)\n", "# # #         categories = list(map(str, set.union(set(data1[f].unique()), set(data2[f].unique()))))\n", "# #         categories = ['p10', 'p50', 'p90']\n", "# #         plt.subplots(2, 2, sharey=\"row\", figsize=(18, 16))\n", "        \n", "# #         # plot raw cohort\n", "# #         change_p50 = []\n", "# #         change_p90 = []\n", "        \n", "# #         for c in categories:\n", "# #             start = data1[(data1[f] == c) & (data1[\"event_date\"] == date_start)]\n", "# #             end = data1[(data1[f] == c) & (data1[\"event_date\"] == date_end)]\n", "# #             if (len(start) == 0) or (len(end) == 0):\n", "# #                 change_p50.append(0)\n", "# #                 change_p90.append(0)\n", "# #             else:\n", "# #                 change_p50.append((end[\"p50\"].iloc[0] / start[\"p50\"].iloc[0] - 1) * 100)\n", "# #                 change_p90.append((end[\"p90\"].iloc[0] / start[\"p90\"].iloc[0] - 1) * 100) \n", "        \n", "# #         plt.subplot(2, 2, 1)    \n", "# #         plt.title(\"Raw Cohort change between {} and {} (p50)\".format(date_start, date_end))\n", "# #         plt.bar(categories, change_p50)\n", "# #         plt.xlabel(f)\n", "# #         plt.xticks(rotation=90)\n", "# #         plt.ylabel(\"percent change p50 (%)\")\n", "\n", "# #         plt.subplot(2, 2, 3)    \n", "# #         plt.title(\"Raw Cohort change between {} and {} (p90)\".format(date_start, date_end))\n", "# #         plt.bar(categories, change_p90)\n", "# #         plt.xlabel(f)\n", "# #         plt.xticks(rotation=90)\n", "# #         plt.ylabel(\"percent change p90 (%)\")\n", "\n", "# #         # plot fixed cohort\n", "# #         change_p50 = []\n", "# #         change_p90 = []\n", "# #         for c in categories:\n", "# #             start = data2[(data2[f] == c) & (data2[\"event_date\"] == date_start)]\n", "# #             end = data2[(data2[f] == c) & (data2[\"event_date\"] == date_end)]\n", "# #             if (len(start) == 0) or (len(end) == 0):\n", "# #                 change_p50.append(0)\n", "# #                 change_p90.append(0)\n", "# #             else:\n", "# #                 change_p50.append((end[\"p50\"].iloc[0] / start[\"p50\"].iloc[0] - 1) * 100)\n", "# #                 change_p90.append((end[\"p90\"].iloc[0] / start[\"p90\"].iloc[0] - 1) * 100) \n", "\n", "# #         plt.subplot(2, 2, 2)    \n", "# #         plt.title(\"Fixed Cohort change between {} and {} (p50)\".format(date_start, date_end))\n", "# #         plt.bar(categories, change_p50, color=\"orange\")\n", "# #         plt.xlabel(f)\n", "# #         plt.xticks(rotation=90)\n", "# #         plt.ylabel(\"percent change p50 (%)\")\n", "\n", "# #         plt.subplot(2, 2, 4)    \n", "# #         plt.title(\"Fixed Cohort change between {} and {} (p90)\".format(date_start, date_end))\n", "# #         plt.bar(categories, change_p90, color=\"orange\")\n", "# #         plt.xlabel(f)\n", "# #         plt.xticks(rotation=90)\n", "# #         plt.ylabel(\"percent change p90 (%)\")\n", "\n", "# #         plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "2f6a8012", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f1329c25", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1fd31242", "metadata": {}, "outputs": [], "source": ["if any(x in sections for x in [\"1\", \"2\", \"5\", \"6\"]):\n", "    # todo: this step is not necessary. we could pivot in the next sql or in pandas.\n", "    if metric_type == \"sum\":\n", "        breakdown_pivot_table_name = create_temp_table(\n", "            \"investigation_breakdowns_pivot\",\n", "            query=\"\"\"\n", "          SELECT\n", "            {dim_names},\n", "            \"all\" AS overall,\n", "            -- dimensions in the source table\n", "            SUM(IF(period = \"period_1\" , {metric_name}, 0)) AS metric_period_1,\n", "            SUM(IF(period = \"period_2\" , {metric_name}, 0)) AS metric_period_2\n", "          FROM \n", "            `sc-bq-gcs-billingonly.temp_abtest.{table_name}`\n", "          GROUP BY {dim_names}\n", "            \"\"\".format(\n", "                table_name=breakdown_table_name,\n", "                metric_name=metric_name,\n", "                dim_names=\",\\n\".join(all_breakdown_list),\n", "            ),\n", "            metric_name=metric_name,\n", "            ds=period_2_end_date,    \n", "        )"]}, {"cell_type": "code", "execution_count": null, "id": "d6f20ac8", "metadata": {}, "outputs": [], "source": ["# print_check\n", "print('check5')"]}, {"cell_type": "code", "execution_count": null, "id": "aedbffbc", "metadata": {}, "outputs": [], "source": ["# query"]}, {"cell_type": "code", "execution_count": null, "id": "10915c63", "metadata": {}, "outputs": [], "source": ["def compute_divergence_by_dims_quantile(df_reshaped):\n", "\n", "    df_divergence = {}\n", "    for os_type in df_reshaped_os_type:\n", "        df_reshaped_tmp = df_reshaped[(df_reshaped[os_type_column_name].str.lower() == os_type) & (df_reshaped[\"period\"].isin([\"period_1\", \"period_2\"]))]\n", "        df_pivot = df_reshaped_tmp.pivot_table(values=[metric_name,'event_count'], index=[\"dimension\", \"dimension_values\"], columns='period', aggfunc=np.sum)\n", "\n", "        df_pivot[metric_name, 'diff%'] = safe_divide(df_pivot[metric_name].period_2, df_pivot[metric_name].period_1)-1\n", "        df_pivot[\"event_count\", 'diff%'] = safe_divide(df_pivot[\"event_count\"].period_2, df_pivot[\"event_count\"].period_1)-1\n", "\n", "        event_count_all_period_1 = float(df_pivot.loc[df_pivot.index.isin([\"all\"], level=1)][\"event_count\", \"period_1\"])\n", "        event_count_all_period_2 = float(df_pivot.loc[df_pivot.index.isin([\"all\"], level=1)][\"event_count\", \"period_2\"])\n", "\n", "        df_pivot[\"event_count_proportion\", \"period_1\"] = df_pivot[\"event_count\", 'period_1'] / event_count_all_period_1\n", "        df_pivot[\"event_count_proportion\", \"period_2\"] = df_pivot[\"event_count\", 'period_2'] / event_count_all_period_2\n", "\n", "        df_pivot.columns = df_pivot.columns.map(' '.join).to_series()\n", "\n", "        df_pivot[\"div\"] = df_pivot[(metric_name +' diff%')] * df_pivot[(\"event_count\" +\" \"+ \"period_2\")]\n", "    \n", "        df_divergence[os_type] = df_pivot\n", "        \n", "    return df_divergence\n", "    \n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "cb075c80", "metadata": {}, "outputs": [], "source": ["def draw_contribution_table_quantile(divergence_table, dimensions, good_direction,\n", "        top_n=50, includes_plot=False, \n", "        period_1=period_1,\n", "        period_2=period_2\n", "        ):\n", "    # compute importance of impact table for quantile metrics\n", "\n", "    if_ascending = (good_direction==\"up\")\n", "    \n", "    df_pivot = divergence_table.loc[~divergence_table.index.isin([\"all\"], level=1)]\n", "    df_pivot = df_pivot.sort_values([\"div\"], ascending=if_ascending).head(top_n)\n", "    df_pivot = df_pivot.drop([metric_name + ' period_1', 'event_count period_1'], axis=1)\n", "    \n", "    df_pivot = df_pivot.rename(columns={\n", "                  metric_name + \" period_2\": \"Metric \" + period_2,\n", "                  \"event_count period_2\":  \"Event Count \" + period_2,\n", "                  \"event_count_proportion period_1\": \"Event Count Proportion \" + period_1,\n", "                  \"event_count_proportion period_2\": \"Event Count Proportion \" + period_2,\n", "                  \"div\": \"Importance of Impact\",\n", "                  \"event_count diff%\": \"Event Count diff%\",\n", "                  metric_name + \" diff%\" : \"Metric diff%\"\n", "                 })\n", "    df_pivot_style = df_pivot.style.format(\n", "        {\n", "            \"Metric diff%\": \"{:.2%}\",\n", "            \"Event Count diff%\": \"{:.2%}\",\n", "            \"Event Count Proportion \" + period_1: \"{:.2%}\",\n", "            \"Event Count Proportion \" + period_2: \"{:.2%}\",\n", "            \"Importance of Impact\": \"{:,.0f}\",\n", "            \"Metric \" + period_2: \"{:,.0f}\",\n", "            \"Event Count \" + period_2: \"{:,.0f}\"\n", "        }\n", "    ).applymap(\n", "        lambda x: background_color_df_float(x, good_direction, [p/100 for p in percentage_thresholds]),\n", "        subset=[\"Metric diff%\", \"Event Count diff%\"]\n", "    ).bar(\n", "        subset=[\"Event Count Proportion \" + period_1, \"Event Count Proportion \" + period_2],\n", "        align='left', color='#ccc',\n", "        vmin=0,\n", "        vmax=1,\n", "    ).bar(\n", "        subset=[\"Importance of Impact\"],\n", "        align='mid', color=['#fa8072', '#72fac4'] if good_direction==\"up\" else ['#72fac4', '#fa8072'],\n", "    )\n", "\n", "    display(df_pivot_style)\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "c03a2cac", "metadata": {}, "outputs": [], "source": ["if any(x in sections for x in [\"1\", \"2\", \"5\", \"6\"]):\n", "    if metric_type == \"sum\":\n", "        query_template = \"\"\"\n", "            SELECT\n", "              {dims},\n", "              \"{dim_names}\" AS dim_names,\n", "              {dim_values} AS dim_values,\n", "              SUM(metric_period_1) AS metric_period_1,\n", "              SUM(metric_period_2) AS metric_period_2\n", "            FROM\n", "              `sc-bq-gcs-billingonly.temp_abtest.%s`\n", "            GROUP BY \n", "              {dims}, dim_names, dim_values\n", "        \"\"\" % breakdown_pivot_table_name\n", "\n", "def compute_divergence_by_dims(always_dimensions, explore_dimensions, n_dims_at_a_time, targets):\n", "    \"\"\"For each unique always_dimensions value, try all subsets of the explore_dimensions\n", "    Compute the the divergence between today's metric and what could have been today's metric\n", "    if the dimension performed similarly to other dimensions\n", "    \n", "    \"\"\"\n", "    explore_dimensions = [dim for dim in explore_dimensions if dim not in always_dimensions]\n", "    combs = combinations(explore_dimensions, n_dims_at_a_time)\n", "    queries = []\n", "    results = []\n", "    for comb in combs:\n", "        comb = sorted(comb)\n", "        query = query_template.format(\n", "            dims=\", \".join(always_dimensions),\n", "            dim_names=\" | \".join(comb),\n", "            dim_values=\"ARRAY_TO_STRING([{}], ' | ')\".format(\", \".join(comb)),\n", "        )\n", "        queries.append(query)\n", "    \n", "\n", "    chunk_size = 50\n", "    query_list_of_lists = [\n", "        queries[start:(min(len(queries), start+chunk_size))]\n", "        for start in range(0, len(queries), chunk_size)\n", "    ]    \n", "    \n", "    \n", "    dfs = utils.gbq.batch_read_gbq(\n", "        [\n", "            \"SELECT * FROM ({})\".format(\"\\nUNION ALL\\n\".join(query_list))\n", "            for query_list in query_list_of_lists \n", "        ],\n", "        parallel=True,\n", "        parallel_concurrency=30,\n", "        priority=\"BATCH\",\n", "        project_id=\"sc-bq-gcs-billingonly\", \n", "        dialect=\"standard\", \n", "        use_bqstorage_api=True\n", "    )\n", "    \n", "    for df_copy in dfs:\n", "        df_summary = df_copy.groupby(always_dimensions + [\"dim_names\", \"dim_values\"])[targets].sum().reset_index()\n", "        df_total = df_copy.groupby(always_dimensions + [\"dim_names\"])[targets].sum().reset_index()\n", "        df_total = df_total.rename(columns={target: target + \"_total\" for target in targets})\n", "        df_summary = df_summary.merge(df_total, on=always_dimensions + [\"dim_names\"])\n", "        df_summary = df_summary.assign(\n", "            proportion_of_total_period_2=df_summary.metric_period_2 / df_summary.metric_period_2_total,\n", "            proportion_of_total_period_1=df_summary.metric_period_1 / df_summary.metric_period_1_total,\n", "            relative_difference=df_summary.metric_period_2 / df_summary.metric_period_1 - 1,\n", "            absolute_difference=df_summary.metric_period_2 - df_summary.metric_period_1,\n", "            relative_difference_total=df_summary.metric_period_2_total / df_summary.metric_period_1_total - 1,\n", "            absolute_difference_total=df_summary.metric_period_2_total - df_summary.metric_period_1_total,\n", "            relative_difference_rest=(df_summary.metric_period_2_total - df_summary.metric_period_2)/(df_summary.metric_period_1_total - df_summary.metric_period_1) - 1,\n", "            absolute_difference_rest=(df_summary.metric_period_2_total - df_summary.metric_period_2) - (df_summary.metric_period_1_total - df_summary.metric_period_1),\n", "            divergence=(\n", "                df_summary.metric_period_2 - df_summary.metric_period_1 * (df_summary.metric_period_2_total)/(df_summary.metric_period_1_total)\n", "            ).round()                       \n", "        )\n", "        \n", "        results.append(df_summary)\n", "      \n", "    results_final = pd.concat(results)\n", "    \n", "    return results_final\n"]}, {"cell_type": "code", "execution_count": null, "id": "6b27fb21", "metadata": {}, "outputs": [], "source": ["# quantile build metric query for 2 dim breakdown\n", "# for quantile metrics, if number of dimensions >= 10, skip this section with warning\n", "if metric_type == \"quantile\" and len(all_breakdown_list) < 10 and \"6\" in sections:\n", "    \n", "    user_cohorts_template_dict[\"period_filter\"] = date_filter_2_periods\n", "    \n", "    combs = combinations(all_breakdown_list_no_os_type, 2)\n", "    combs = [sorted(comb) for comb in combs] \n", "    if if_metric_struct_array:\n", "        subqueries_2_dim_template = \"\"\"\n", "                (\n", "                  SELECT\n", "                    ds,\n", "                    {os_type_column_name},\n", "                    \"{dimension}\" AS dimension,\n", "                    {dimension_value} AS dimension_values,\n", "                    APPROX_QUANTILES(metric_list.element, 100)[OFFSET({quantile})] AS {metric_name},\n", "                    SUM(ARRAY_LENGTH({metric_name}.list)) / {sampling_ratio} AS event_count\n", "                  FROM \n", "                    metrics \n", "                  JOIN\n", "                    UNNEST({metric_name}.list) metric_list\n", "                  {join_method} \n", "                    uc\n", "                  USING (ghost_user_id, ds)  \n", "                  GROUP BY 1,2,3,4\n", "              )\n", "            \"\"\"\n", "    else:\n", "        subqueries_2_dim_template = \"\"\"\n", "                (\n", "                  SELECT\n", "                    ds,\n", "                    {os_type_column_name},\n", "                    \"{dimension}\" AS dimension,\n", "                    {dimension_value} AS dimension_values,\n", "                    APPROX_QUANTILES({metric_name}, 100)[OFFSET({quantile})] AS {metric_name},\n", "                    COUNT({metric_name}) / {sampling_ratio} AS event_count\n", "                  FROM \n", "                    metrics \n", "                  {join_method} \n", "                    uc\n", "                  USING (ghost_user_id, ds)  \n", "                  GROUP BY 1,2,3,4\n", "              )\n", "            \"\"\"\n", "    subqueries_2_dim = [subqueries_2_dim_template.format(\n", "            dimension=\" | \".join(comb),\n", "            dimension_value=\"ARRAY_TO_STRING([{}], ' | ')\".format(\", \".join([\"CAST({} AS STRING)\".format(i) for i in comb])),\n", "            quantile=quantile,\n", "            metric_name=metric_name,\n", "            sampling_ratio=sampling_ratio,\n", "            join_method=\"LEFT JOIN\" if not user_cohorts_filter else \"JOIN\",\n", "            os_type_column_name=os_type_column_name\n", "        )\n", "        for comb in combs \n", "    ] + [subqueries_2_dim_template.format(\n", "            dimension=\"all\",\n", "            dimension_value='\\\"all\\\"',\n", "            quantile=quantile,\n", "            metric_name=metric_name,\n", "            sampling_ratio=sampling_ratio,\n", "            join_method=\"LEFT JOIN\" if not user_cohorts_filter else \"JOIN\",\n", "            os_type_column_name=os_type_column_name\n", "    )]\n", "\n", "    \n", "    query_template = user_cohorts_source_tables_template + \"\"\"\n", "            SELECT  \n", "                * EXCEPT(user_days, {metric_name}_sum, event_count_sum),\n", "                {metric_name}_sum / user_days AS {metric_name},\n", "                event_count_sum / user_days AS event_count\n", "            FROM (\n", "              SELECT \n", "                  dimension,\n", "                  dimension_values,\n", "                  {os_type_column_name},\n", "                  CASE WHEN ds BETWEEN \"{period_1_start_date}\" AND \"{period_1_end_date}\"\n", "                      THEN 'period_1'\n", "                      WHEN ds BETWEEN \"{period_2_start_date}\" AND \"{period_2_end_date}\"\n", "                      THEN 'period_2'\n", "                      ELSE 'other'\n", "                  END AS period, \n", "                  CASE WHEN ds BETWEEN \"{period_1_start_date}\" AND \"{period_1_end_date}\"\n", "                      THEN (1+ DATE_DIFF(PARSE_TIMESTAMP(\"%Y%m%d\",\"{period_1_end_date}\"), PARSE_TIMESTAMP(\"%Y%m%d\", \"{period_1_start_date}\"), DAY))\n", "                      WHEN ds BETWEEN \"{period_2_start_date}\" AND \"{period_2_end_date}\"\n", "                      THEN (1+ DATE_DIFF(PARSE_TIMESTAMP(\"%Y%m%d\",\"{period_2_end_date}\"), PARSE_TIMESTAMP(\"%Y%m%d\", \"{period_2_start_date}\"), DAY))\n", "                      ELSE 0\n", "                  END AS user_days,\n", "                  SUM({metric_name}) AS {metric_name}_sum,  -- calculate the daily SUM over the period \n", "                  SUM(event_count) AS event_count_sum\n", "              FROM ({subqueries})\n", "              GROUP BY 1,2,3,4,5\n", "          )\n", "        \"\"\"\n", "    user_cohorts_template_dict[\"subqueries\"] = \" UNION ALL \".join(subqueries_2_dim)\n", "    user_cohorts_template_dict[\"os_type_column_name\"] = os_type_column_name\n", "    \n", "    query_2_dim = query_template.format(\n", "        **user_cohorts_template_dict\n", "    )\n", "    \n", "    # A long table that all dim in one column\n", "    df_reshaped_2_dim = read_gbq(query_2_dim)\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "adfe3d0d", "metadata": {}, "outputs": [], "source": ["def draw_contribution_table(divergence_table, dimensions, good_direction,\n", "        top_n=50, includes_plot=False, \n", "        period_1=period_1,\n", "        period_2=period_2\n", "        ):\n", "    \n", "    if_ascending = (good_direction==\"up\")\n", "        \n", "    contribution_table = (\n", "        divergence_table\n", "            .assign(\n", "                relative_difference_total=divergence_table.relative_difference_total.apply(\"{:.2%}\".format),\n", "                absolute_difference_total=divergence_table.absolute_difference_total.apply(\"{:,.0f}\".format)\n", "            )            \n", "            .groupby(dimensions + [\"relative_difference_total\", \"absolute_difference_total\", \"dim_names\", \"dim_values\"])\n", "            [[\"metric_period_2\", \"divergence\", \"relative_difference\", \"relative_difference_rest\", \"proportion_of_total_period_2\", \"proportion_of_total_period_1\", ]]\n", "            .sum()\n", "            .sort_values([\"divergence\"], ascending=if_ascending)\n", "            .head(top_n)\n", "    )\n", "    \n", "    contribution_table = contribution_table.rename(columns={col: col.replace(\"_\", \" \").title() for col in contribution_table.columns})\n", "    \n", "    contribution_table = contribution_table.rename(columns={\n", "                      \"Proportion Of Total Period 2\":  \"Proportion Of Total \" + period_2,\n", "                      \"Proportion Of Total Period 1\":  \"Proportion Of Total \" + period_1,\n", "                      \"Metric Period 2\": \"Metric \" + period_2,\n", "                      \"Divergence\": \"Importance of Impact (Divergence)\"\n", "                     })\n", "\n", "    contribution_table_style = contribution_table.style.format(\n", "        {\n", "            \"Relative Difference\": \"{:.2%}\",\n", "            \"Relative Difference Rest\": \"{:.2%}\",\n", "            \"Proportion Of Total \" + period_2: \"{:.2%}\",\n", "            \"Proportion Of Total \" + period_1: \"{:.2%}\",\n", "            \"Importance of Impact (Divergence)\": \"{:,.0f}\",\n", "            \"Metric \" + period_2: \"{:,.0f}\"    \n", "        }\n", "    ).applymap(\n", "        lambda x: background_color_df_float(x, good_direction, [p/100 for p in percentage_thresholds]),\n", "        subset=[\"Relative Difference\", \"Relative Difference Rest\"]\n", "    ).bar(\n", "        subset=[\"Proportion Of Total \" + period_2, \"Proportion Of Total \" + period_1],\n", "        align='left', color='#ccc',\n", "        vmin=0,\n", "        vmax=1,\n", "    ).bar(\n", "        subset=[\"Importance of Impact (Divergence)\"],\n", "        align='mid', color=['#fa8072', '#72fac4'] if good_direction==\"up\" else ['#72fac4', '#fa8072'],\n", "    )\n", "    \n", "    \n", "    if includes_plot:\n", "        import plotly.express as px\n", "\n", "        divergence_table = divergence_table.copy().query(\"proportion_of_total_period_1 >= 0.01\")\n", "        divergence_table = divergence_table.assign(\n", "            divergence_rank=divergence_table.divergence.rank(),\n", "            hover_name=divergence_table.dim_names + \": \" + divergence_table.dim_values,\n", "        )\n", "        fig = px.scatter(\n", "            divergence_table, \n", "            x=\"proportion_of_total_period_1\", \n", "            y=\"relative_difference_rest\",\n", "            color=\"divergence_rank\",\n", "            hover_name=\"hover_name\", \n", "            hover_data={'relative_difference': \":.1%\", 'absolute_difference': \":,.0f\", 'divergence': \":,.0f\"},\n", "        )\n", "        fig.update_layout(\n", "            title=\"% Change after removing the subgroup VS the subgroup size\",\n", "            xaxis=dict(\n", "                tickformat = '.1%',\n", "                title = 'Group Size (Proportion of Contribution)',\n", "                hoverformat = '.1%',\n", "            ), \n", "            yaxis=dict(\n", "                tickformat = '.1%',\n", "                title = '% Change Outside of Subgroup',\n", "                hoverformat = '.1%',\n", "            ), \n", "        )\n", "        fig.show(config={'displaylogo': False})\n", "    \n", "    display(contribution_table_style)\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "31aed926", "metadata": {}, "outputs": [], "source": ["# Waterfall chart functions\n", "\n", "def divergence(x, y):\n", "    x = np.asarray(x)\n", "    y = np.asarray(y)\n", "    overall_change = np.sum(y) / np.sum(x)\n", "    div = y - x * overall_change\n", "    return div\n", "\n", "\n", "def data_wow_diff(data,\n", "                  divergence_table,\n", "                  date_field='',\n", "                  dimension_fields=[],\n", "                  target_field=''\n", "                 ): \n", "\n", "    dimension_field = 'combined'\n", "    #fill in the missing values \n", "    data=data.fillna(\"missing\")\n", "    data[dimension_field] = data[dimension_fields].apply(lambda row: '|'.join(row.values.astype(str)), axis=1)\\\n", "        if dimension_fields else ''\n", "    data[date_field + '_dt'] = data[date_field]\n", "\n", "    # dim table\n", "    data_dim = data.groupby([date_field, dimension_field] + dimension_fields).sum().reset_index()\n", "    dates_to_compare = ['period_1', 'period_2']\n", "\n", "    data_wow = (\n", "        data_dim[\n", "            (data_dim[date_field].apply(lambda x: x in set(dates_to_compare)))\n", "        ]\n", "        .sort_values(dimension_fields + [date_field])\n", "        .fillna(0)\n", "    )\n", "    data_wow['Date'] = data_wow[date_field]\n", "    data_wow_pivot = pd.pivot(data_wow, index=dimension_fields, columns='Date', values=target_field).fillna(0)\n", "\n", "    #generate differences and calculate WoW distrbution differences \n", "    data_wow = data_wow_pivot.reset_index()\n", "    data_wow[\"diff\"]=data_wow['period_2']-data_wow['period_1']\n", "    data_wow = data_wow.rename(columns={\n", "        \"period_1\": period_1, \n", "        \"period_2\": period_2\n", "    })   \n", "    data_wow_diff=data_wow.reset_index()\n", "    \n", "    return data_wow_diff\n", "    \n", "\n", "def contribution_by_cohort(data,\n", "                           divergence_table,\n", "                           good_direction,\n", "                           date_field='',\n", "                           dimension_fields=[],\n", "                           target_field='',\n", "                           current_date=None,\n", "                           ): \n", "\n", "    import plotly.graph_objects as go\n", "    from plotly.subplots import make_subplots\n", "    \n", "    data_wow=data_wow_diff(\n", "        data=data,\n", "        divergence_table=divergence_table,\n", "        date_field=date_field,\n", "        dimension_fields=dimension_fields,\n", "        target_field=metric_name\n", "    )\n", "    \n", "    i = 1\n", "    \n", "    for dimension in divergence_table.sort_values(\"divergence\").dim_names.unique():\n", "        if i <= 3:\n", "            display_html(title('Top {} Interesting Dimension: {}'.format(i, dimension), 4))\n", "        elif i == 4:\n", "            display_html(title('Other Dimensions'.format(dimension), 4))\n", "            display_html(title('Dimension {}'.format(dimension), 5))\n", "        else:\n", "            display_html(title('Dimension {}'.format(dimension), 5))\n", "\n", "        draw_contribution_table(\n", "            divergence_table.loc[(divergence_table.dim_names==dimension), :], \n", "            dimensions=[], \n", "            top_n=50, \n", "            good_direction = good_direction\n", "        )\n", "\n", "        x_value=data_wow.loc[data_wow['dimension']==dimension]['dimension_values'].to_list()\n", "        #ensure string\n", "        x_value=[str(i) for i in x_value]\n", "        measures = [\"relative\"]*len(x_value)\n", "        x_value.append(\"net\")\n", "        measures.append(\"total\")\n", "        # only filter on top 10 \n", "        if len(x_value)>10:\n", "            print(\"%s dimension values too large to show, only top 10 differences are shown\" %dimension)\n", "        else:\n", "            fig = make_subplots(rows=1, cols=3, subplot_titles=[\"vs baseline period\"])\n", "\n", "            y_value=data_wow.loc[data_wow['dimension']==dimension][\"diff\"].to_list()\n", "            net=sum(y_value)\n", "            y_value.append(net)\n", "            fig.append_trace(go.Waterfall(\n", "                orientation = \"v\",\n", "                measure=measures,\n", "                x = x_value,\n", "                textposition = \"outside\",\n", "                y = y_value,\n", "                connector = {\"line\":{\"color\":\"rgb(63, 63, 63)\"}},\n", "            ),row=1,col=1)\n", "\n", "            fig.update_layout(height=400, width=1500, title_text='Dimension %s' %(dimension))\n", "            fig.update_xaxes(type='category')\n", "            fig.show(config={'displaylogo': False})\n", "            \n", "        i += 1\n", "        \n", "    return data_wow"]}, {"cell_type": "code", "execution_count": null, "id": "644efb3b", "metadata": {}, "outputs": [], "source": ["# Generate breakdown table for each dimension for quantile metrics\n", "if any(x in sections for x in [\"1\", \"2\", \"5\", \"6\"]):\n", "    def contribution_by_cohort_quantile(divergence_table):\n", "        divergence_table_by_dimension = {}\n", "        for breakdown in all_breakdown_list_no_os_type:\n", "            divergence_table_by_dimension[breakdown] = {}\n", "            for os_type in df_reshaped_os_type:\n", "                divergence_table_by_dimension[breakdown][os_type] = divergence_table[os_type][divergence_table[os_type].index.isin([breakdown],level=0)]\n", "\n", "        return divergence_table_by_dimension\n"]}, {"cell_type": "code", "execution_count": null, "id": "267e88a2", "metadata": {}, "outputs": [], "source": ["# get the min divergence of each dimension\n", "def worst_divergence(x, y):\n", "    div = divergence(x, y)\n", "    func = np.max if good_direction != \"up\" else np.min\n", "    \n", "    return func(div)\n", "\n", "group_name = \"overall\"\n", "group_value = \"all\"\n", "\n", "if \"5\" in sections:\n", "    if metric_type == \"sum\":\n", "        divergence_table = compute_divergence_by_dims(\n", "            always_dimensions=[group_name], \n", "            explore_dimensions=all_breakdown_list, \n", "            n_dims_at_a_time=1, \n", "            targets=['metric_period_2', 'metric_period_1']\n", "        )\n", "    \n", "        data_wow = data_wow_diff(\n", "                data=df_reshaped.loc[df_reshaped.share > -1, :].copy(),\n", "                divergence_table=divergence_table.copy(),\n", "                date_field='period',\n", "                dimension_fields=[\"dimension\",\"dimension_values\"],\n", "                target_field=metric_name)\n", "    \n", "        chi_dist={}\n", "        for dimension in all_breakdown_list:\n", "            chi_dist[dimension]=worst_divergence(\n", "                data_wow.loc[data_wow[\"dimension\"]==dimension][period_1],\n", "                data_wow.loc[data_wow[\"dimension\"]==dimension][period_2]\n", "            )            \n", "    \n", "        # sort ascending when good_direction == up <-> reverse=False\n", "        sorted_chi_dist = {k: v for k, v in sorted(chi_dist.items(), key=lambda item: item[1], reverse=(good_direction != \"up\"))}\n", "        # {dim:min_divergence for (dim, min_divergence) in [x for x in sorted_chi_dist.items()][:4]}\n", "    \n", "        # top 3 interesting dimension\n", "        dimensions_top3 = list(sorted_chi_dist)[:3]\n", "    \n", "        \n", "    elif metric_type == \"quantile\":\n", "        \n", "        divergence_table = compute_divergence_by_dims_quantile(df_reshaped)\n", "        divergence_table_by_dimension_quantile = contribution_by_cohort_quantile(divergence_table)"]}, {"cell_type": "code", "execution_count": null, "id": "b7a63519", "metadata": {}, "outputs": [], "source": ["# calcualte section 6: Two Dimension Breakdown \n", "if \"6\" in sections:\n", "    if metric_type == \"sum\":\n", "        divergence_table_2 = compute_divergence_by_dims(\n", "            always_dimensions=[group_name], \n", "            explore_dimensions=all_breakdown_list, \n", "            n_dims_at_a_time=2, \n", "            targets=['metric_period_2', 'metric_period_1']\n", "        )\n", "    \n", "    elif metric_type == \"quantile\" and len(all_breakdown_list) < 10:\n", "        divergence_table_2_dim = compute_divergence_by_dims_quantile(df_reshaped_2_dim)\n"]}, {"cell_type": "code", "execution_count": null, "id": "4aff5579", "metadata": {}, "outputs": [], "source": ["# section 7: growth section\n", "os_type_format_list=[] # todo: this naming is confusing, need to be updated or refactor codes.\n", "if metric_type == \"sum\":\n", "    os_type_format_list.append(\"sum\")\n", "else:\n", "    for os_type_raw in df_reshaped_os_type:\n", "        if os_type_raw.lower()=='android':os_type_format_list.append('Android')\n", "        if os_type_raw.lower()=='ios':os_type_format_list.append('iOS')\n", "\n", "user_cohorts_query_join_dict={}\n", "for os_type_formatted in os_type_format_list:\n", "    user_cohorts_query_join = \"\"\"\n", "        JOIN (\n", "          SELECT\n", "            ghost_user_id\n", "          FROM\n", "            `sc-analytics.report_search.user_cohorts_20*`\n", "          WHERE \n", "          -- this section calculate metric total during the period, so use the investigation period end date for user_cohorts filtering\n", "                CONCAT(\"20\", _TABLE_SUFFIX) = \"{period_2_end_date}\" \n", "                {user_cohorts_filter}\n", "                {os_type_user_filter}\n", "                {sampling_filter}\n", "        ) uc\n", "        USING (ghost_user_id)\n", "    \"\"\".format(\n", "        period_2_end_date=period_2_end_date,\n", "        user_cohorts_filter= \"\" if not user_cohorts_filter else \"AND ({})\".format(user_cohorts_filter),\n", "        os_type_user_filter= \"\" if metric_type == \"sum\" else \"AND os_type = '{}'\".format(os_type_formatted),\n", "        sampling_filter=sampling_filter\n", "    )\n", "    user_cohorts_query_join_dict[os_type_formatted]=user_cohorts_query_join"]}, {"cell_type": "code", "execution_count": null, "id": "0fca0655", "metadata": {}, "outputs": [], "source": ["# print_check\n", "print('check6')"]}, {"cell_type": "code", "execution_count": null, "id": "58e1bdc5", "metadata": {}, "outputs": [], "source": ["# section 7.1\n", "sql_steps = \"\"\"\n", "    SELECT uu_overall.period, uu, {metric_name}_overall, active_days\n", "    FROM (-- period uu, metric total sum\n", "        SELECT \n", "            period, \n", "            COUNT(DISTINCT ghost_user_id) / {sampling_ratio} AS uu, \n", "            SUM({metric_name}) / {sampling_ratio} AS {metric_name}_overall\n", "        FROM ( \n", "        -- user period sum\n", "            SELECT \n", "                CASE \n", "                    WHEN CONCAT(\"20\", _TABLE_SUFFIX) BETWEEN \"{period_1_start_date}\" AND \"{period_1_end_date}\" \n", "                        THEN \"baseline_period\"\n", "                    WHEN CONCAT(\"20\", _TABLE_SUFFIX) BETWEEN \"{period_2_start_date}\" AND \"{period_2_end_date}\" \n", "                        THEN \"investigation_period\" \n", "                END period,\n", "                ghost_user_id,\n", "                SUM({metric_name}) AS {metric_name}\n", "            FROM `{table_path}_20*`\n", "            WHERE (\n", "                CONCAT(\"20\", _TABLE_SUFFIX) BETWEEN \"{period_1_start_date}\" AND \"{period_1_end_date}\"\n", "                OR\n", "                CONCAT(\"20\", _TABLE_SUFFIX) BETWEEN \"{period_2_start_date}\" AND \"{period_2_end_date}\" \n", "            ) {source_table_filter}\n", "            {sampling_filter}\n", "            GROUP BY 1,2\n", "        ) \n", "        {user_cohorts_query_join}\n", "        GROUP BY 1\n", "    ) uu_overall\n", "    LEFT JOIN (\n", "        SELECT \n", "            period, \n", "            COUNT(1) active_days\n", "        FROM (\n", "        -- user ds sum\n", "            SELECT \n", "                CONCAT(\"20\", _TABLE_SUFFIX) ds,\n", "                CASE \n", "                    WHEN CONCAT(\"20\", _TABLE_SUFFIX) BETWEEN \"{period_1_start_date}\" AND \"{period_1_end_date}\" \n", "                        THEN \"baseline_period\"\n", "                    WHEN CONCAT(\"20\", _TABLE_SUFFIX) BETWEEN \"{period_2_start_date}\" AND \"{period_2_end_date}\" \n", "                        THEN \"investigation_period\" \n", "                END period,\n", "                ghost_user_id,\n", "                SUM({metric_name}) AS {metric_name}\n", "            FROM `{table_path}_20*`\n", "            WHERE (\n", "                CONCAT(\"20\", _TABLE_SUFFIX) BETWEEN \"{period_1_start_date}\" AND \"{period_1_end_date}\"\n", "                OR\n", "                CONCAT(\"20\", _TABLE_SUFFIX) BETWEEN \"{period_2_start_date}\" AND \"{period_2_end_date}\" \n", "            ) {source_table_filter}  \n", "            {sampling_filter}\n", "            GROUP BY 1,2,3\n", "        ) \n", "        {user_cohorts_query_join}\n", "        GROUP BY 1\n", "    ) active_day \n", "    USING (period)\n", "\"\"\".format(\n", "    period_1_start_date=period_1_start_date,\n", "    period_1_end_date=period_1_end_date,\n", "    period_2_start_date=period_2_start_date,\n", "    period_2_end_date=period_2_end_date,\n", "    metric_name=metric_name,\n", "    table_path=table_path,\n", "    source_table_filter=\"\" if not source_table_filter else \"AND ({})\".format(source_table_filter),\n", "    user_cohorts_query_join=\"\" if not user_cohorts_filter and metric_type == \"sum\" else list(user_cohorts_query_join_dict.values())[0],\n", "    sampling_ratio=sampling_ratio,\n", "    sampling_filter=sampling_filter\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "cb9d85e8", "metadata": {}, "outputs": [], "source": ["# print_check\n", "print('check7')"]}, {"cell_type": "code", "execution_count": null, "id": "d08f4888", "metadata": {}, "outputs": [], "source": ["if \"7\" in sections:\n", "    if metric_type == \"sum\":\n", "        df_steps = read_gbq(sql_steps)\n", "        df_steps[\"step 1: Adoption (uu)\"] = df_steps[\"uu\"]\n", "        df_steps[\"step 2: Frequency (active_days / uu)\"] = df_steps[\"active_days\"]/df_steps[\"uu\"]\n", "        df_steps[\"step 3: Depth (overall / active_days)\"] = df_steps[\"{metric_name}_overall\".format(metric_name=metric_name)]/df_steps[\"active_days\"]\n", "        df_steps[metric_name +'_overall'] = round(df_steps[metric_name +'_overall'], 2)\n", "    \n", "        df_steps_transpose = pd.DataFrame(df_steps.T)\n", "        df_steps_transpose.columns = df_steps_transpose[df_steps_transpose.index == \"period\"].values.tolist()[0]\n", "        df_steps_transpose = df_steps_transpose.filter(\n", "            items = [\n", "               'step 1: Adoption (uu)',\n", "               'step 2: Frequency (active_days / uu)',\n", "               'step 3: Depth (overall / active_days)',\n", "                metric_name+ '_overall'\n", "            ], \n", "            axis=0)\n", "        df_steps_transpose[\"diff%\"] = df_steps_transpose[\"investigation_period\"]/df_steps_transpose[\"baseline_period\"]-1\n", "        df_steps_transpose[\"diff%\"] = [str(round(100*i, 2)) +'%' for i in df_steps_transpose[\"diff%\"]]\n", "    \n", "        column_order_steps = [\"baseline_period\", \"investigation_period\", \"diff%\"]\n", "        df_steps_final = df_steps_transpose[column_order_steps]"]}, {"cell_type": "code", "execution_count": null, "id": "724159f7", "metadata": {}, "outputs": [], "source": ["# print_check\n", "print('check8')"]}, {"cell_type": "code", "execution_count": null, "id": "de4662e1", "metadata": {}, "outputs": [], "source": ["# section 7.2\n", "\n", "# Based on quantile or sum metric and column type, determine the query to aggregate at user level\n", "if metric_type==\"sum\": if_metric_struct_array=None\n", "def user_agg(metric_type, if_metric_struct_array, metric_only=False):\n", "    if metric_only == True:\n", "        if metric_type == \"quantile\":\n", "            user_aggregation=\"\"\"APPROX_QUANTILES({metric_name}, 100)[OFFSET(50)] AS {metric_name}\"\"\".format(metric_name=metric_name)\n", "        if metric_type == \"sum\":\n", "            user_aggregation=\"\"\"SUM({metric_name}) / {sampling_ratio} AS {metric_name}\"\"\".format(metric_name=metric_name, sampling_ratio=sampling_ratio)\n", "    elif metric_only == False:\n", "        if metric_type == \"quantile\" and if_metric_struct_array == True:\n", "            user_aggregation=\"\"\"ghost_user_id, APPROX_QUANTILES(metric_list.element, 100)[OFFSET(50)] AS {metric_name}\n", "                                FROM `{table_path}_20*`, UNNEST({metric_name}.list) metric_list\n", "            \"\"\".format(metric_name=metric_name, table_path=table_path)\n", "        if metric_type == \"quantile\" and if_metric_struct_array == False:\n", "            user_aggregation=\"\"\"ghost_user_id, APPROX_QUANTILES({metric_name}, 100)[OFFSET(50)] AS {metric_name}\n", "                                FROM `{table_path}_20*`\n", "            \"\"\".format(metric_name=metric_name, table_path=table_path)\n", "        if metric_type == \"sum\":\n", "            user_aggregation=\"\"\"ghost_user_id, SUM({metric_name}) AS {metric_name}\n", "                                FROM `{table_path}_20*`\n", "            \"\"\".format(metric_name=metric_name, table_path=table_path)\n", "    return user_aggregation\n", "\n", "# get the quantiles of metric total during the baseline period\n", "sql_metric_baseline_period_threshold_dict={}\n", "if \"7\" in sections:\n", "    for os_type_formatted in os_type_format_list:\n", "        sql_metric_baseline_period_threshold = \"\"\"\n", "            SELECT\n", "                APPROX_QUANTILES({metric_name}, 100)[OFFSET(10)] AS p10,\n", "                APPROX_QUANTILES({metric_name}, 100)[OFFSET(50)] AS p50,\n", "                APPROX_QUANTILES({metric_name}, 100)[OFFSET(90)] AS p90\n", "            FROM(\n", "                SELECT {user_aggregation}\n", "                WHERE CONCAT(\"20\", _TABLE_SUFFIX) BETWEEN \"{period_1_start_date}\" AND \"{period_1_end_date}\"\n", "                    {source_table_filter} \n", "                    \n", "                GROUP BY 1\n", "            ) metric\n", "            {user_cohorts_query_join}\n", "        \"\"\".format(\n", "            period_1_start_date=period_1_start_date,\n", "            period_1_end_date=period_1_end_date,\n", "            metric_name=metric_name,\n", "            table_path=table_path,\n", "            source_table_filter=\"\" if not source_table_filter else \"AND ({})\".format(source_table_filter),\n", "            user_cohorts_query_join=\"\" if not user_cohorts_filter and metric_type == \"sum\" else user_cohorts_query_join_dict[os_type_formatted],\n", "            user_aggregation=user_agg(metric_type, if_metric_struct_array)\n", "        )\n", "        sql_metric_baseline_period_threshold_dict[os_type_formatted]= read_gbq(sql_metric_baseline_period_threshold)"]}, {"cell_type": "code", "execution_count": null, "id": "251e2461", "metadata": {}, "outputs": [], "source": ["# we can revisit this rule, how to tell if a metric is an engagement or performance metric \n", "if \"7\" in sections:\n", "    user_group_name_sql = {}\n", "    if metric_type == \"sum\":\n", "        for os_type_formatted in os_type_format_list:\n", "            user_group_name_sql[os_type_formatted]  = [\"low (<{p10_round})\", \n", "                               \"casual ({p10_round}~{p50_round})\", \n", "                               \"regular ({p50_round}~{p90_round})\", \n", "                               \"power (>={p90_round})\"]\n", "        user_group_name = [\"low (&ltp10)\", \n", "                           \"casual (p10~p50)\", \n", "                           \"regular (p50~p90)\", \n", "                           \"power (&ge;p90)\"]\n", "    elif metric_type == \"quantile\":\n", "        for os_type_formatted in os_type_format_list:\n", "            user_group_name_sql[os_type_formatted] = [\"&ltp10 (<{p10_round})\", \n", "                                   \"p10~p50 ({p10_round}~{p50_round})\", \n", "                                   \"p50~p90 ({p50_round}~{p90_round})\", \n", "                                   \"&ge;p90 (>={p90_round})\"]\n", "        user_group_name = [\"&ltp10\", \n", "                           \"p10~p50\", \n", "                           \"p50~p90\", \n", "                           \"&ge;p90\"]\n", "    \n", "    for os_type_formatted in os_type_format_list:\n", "        user_group_name_sql[os_type_formatted] = [\n", "            user_group.format(\n", "                p10_round=\"{0:.1f}\".format(float(sql_metric_baseline_period_threshold_dict[os_type_formatted]['p10'])),\n", "                p50_round=\"{0:.1f}\".format(float(sql_metric_baseline_period_threshold_dict[os_type_formatted]['p50'])),\n", "                p90_round=\"{0:.1f}\".format(float(sql_metric_baseline_period_threshold_dict[os_type_formatted]['p90'])),\n", "            ) for user_group in user_group_name_sql[os_type_formatted]\n", "        ]"]}, {"cell_type": "code", "execution_count": null, "id": "45a1c86a", "metadata": {}, "outputs": [], "source": ["# print_check\n", "print('check9')"]}, {"cell_type": "code", "execution_count": null, "id": "c321be82", "metadata": {}, "outputs": [], "source": ["if \"7\" in sections:\n", "    sql_growth_dict={}\n", "    for os_type_formatted in os_type_format_list:\n", "        sql_growth = \"\"\"\n", "        -- period uu, metric total sum\n", "            SELECT \n", "                period, \n", "                user_group, \n", "                COUNT(DISTINCT ghost_user_id) / {sampling_ratio} AS uu, \n", "                {user_aggregation_1}\n", "            FROM ( -- user period sum, compared with quantile thresholds\n", "                SELECT \n", "                    ghost_user_id,\n", "                    period,\n", "                    CASE WHEN {metric_name} < {p10} THEN \"{user_group_name_sql[0]}\"\n", "                        WHEN {metric_name} < {p50} THEN \"{user_group_name_sql[1]}\"\n", "                        WHEN {metric_name} < {p90} THEN \"{user_group_name_sql[2]}\"\n", "                        WHEN {metric_name} >= {p90} THEN \"{user_group_name_sql[3]}\"\n", "                            ELSE \"null\"\n", "                    END user_group,\n", "                    {metric_name} \n", "                FROM (\n", "                    SELECT \n", "                        CASE \n", "                            WHEN CONCAT(\"20\", _TABLE_SUFFIX) BETWEEN \"{period_1_start_date}\" AND \"{period_1_end_date}\" \n", "                                THEN \"baseline_period\"\n", "                            WHEN CONCAT(\"20\", _TABLE_SUFFIX) BETWEEN \"{period_2_start_date}\" AND \"{period_2_end_date}\" \n", "                                THEN \"investigation_period\" \n", "                        END period,\n", "                        {user_aggregation_2}\n", "                    WHERE (\n", "                        CONCAT(\"20\", _TABLE_SUFFIX) BETWEEN \"{period_1_start_date}\" AND \"{period_1_end_date}\"\n", "                        OR\n", "                        CONCAT(\"20\", _TABLE_SUFFIX) BETWEEN \"{period_2_start_date}\" AND \"{period_2_end_date}\" \n", "                    ) {source_table_filter}  \n", "                    {sampling_filter}\n", "                    GROUP BY 1,2\n", "                )\n", "                {user_cohorts_query_join}\n", "            )\n", "            WHERE user_group != 'null'\n", "            GROUP BY 1,2\n", "            ORDER BY 1,2\n", "        \"\"\".format(\n", "            period_1_start_date=period_1_start_date,\n", "            period_1_end_date=period_1_end_date,\n", "            period_2_start_date=period_2_start_date,\n", "            period_2_end_date=period_2_end_date,\n", "            metric_name=metric_name,\n", "            table_path=table_path,\n", "            source_table_filter=\"\" if not source_table_filter else \"AND ({})\".format(source_table_filter),\n", "            p10=float(sql_metric_baseline_period_threshold_dict[os_type_formatted]['p10']),\n", "            p50=float(sql_metric_baseline_period_threshold_dict[os_type_formatted]['p50']),\n", "            p90=float(sql_metric_baseline_period_threshold_dict[os_type_formatted]['p90']),\n", "            user_group_name_sql=user_group_name_sql[os_type_formatted],\n", "            user_cohorts_query_join=\"\" if not user_cohorts_filter and metric_type == \"sum\" else user_cohorts_query_join_dict[os_type_formatted],\n", "            quantile=quantile,\n", "            user_aggregation_1=user_agg(metric_type, if_metric_struct_array, metric_only=True),\n", "            user_aggregation_2=user_agg(metric_type, if_metric_struct_array),\n", "            sampling_filter=sampling_filter,\n", "            sampling_ratio=sampling_ratio\n", "        )\n", "        sql_growth_dict[os_type_formatted]= read_gbq(sql_growth)"]}, {"cell_type": "code", "execution_count": null, "id": "f9f1ee4b", "metadata": {}, "outputs": [], "source": ["column_order_growth = [metric_name, 'uu']\n", "sql_growth_final_dict={}\n", "if \"7\" in sections:\n", "    for os_type_formatted in os_type_format_list:\n", "        df_growth_pivot = pd.pivot_table(\n", "                sql_growth_dict[os_type_formatted], \n", "                values=[metric_name, 'uu'], columns=['period'],index=['user_group'], aggfunc=np.sum\n", "            ).sort_values(\n", "                by = [(metric_name, 'baseline_period')], ascending=False\n", "            )\n", "    \n", "        for column in column_order_growth:\n", "            df_growth_pivot[column, 'diff%'] = [\n", "                \"{:.2%}\".format(i) \n", "                for i in (df_growth_pivot[column, 'investigation_period'] - df_growth_pivot[column, 'baseline_period']) / df_growth_pivot[column, 'baseline_period']\n", "            ]\n", "    \n", "        for period in ['investigation_period', 'baseline_period']:\n", "            if sum(df_growth_pivot[\"uu\", period].isnull()) != 0:\n", "                idx = df_growth_pivot[\"uu\", period].isnull( )\n", "                df_growth_pivot[\"uu\", period][idx] = 0\n", "            df_growth_pivot[\"uu\", period] = [int(i) for i in list(df_growth_pivot[\"uu\", period])]\n", "    \n", "    \n", "        for period in ['investigation_period', 'baseline_period']:\n", "            df_growth_pivot[metric_name, period] = [\"{:.2f}\".format(i) for i in df_growth_pivot[metric_name, period]]\n", "    \n", "        sql_growth_final_dict[os_type_formatted] = df_growth_pivot[column_order_growth]"]}, {"cell_type": "code", "execution_count": null, "id": "56fd7bd3", "metadata": {}, "outputs": [], "source": ["# print_check\n", "print('check10')"]}, {"cell_type": "code", "execution_count": null, "id": "2f3eab3d", "metadata": {}, "outputs": [], "source": ["# section 8: Core Metrics Overview\n", "\n", "# subsection: highly correlated metric section data prep\n", "date_x = \"20231101\"\n", "user_type_of_interest = '2.average user'\n", "\n", "curate_metric_query_template = \"\"\"\n", "    SELECT\n", "        MOD(ABS(FARM_FINGERPRINT(ghost_user_id)), 10000) as ghost_user_id_group, \n", "        {metric_calculation}\n", "    FROM `{table_path}_{date_x}` \n", "    GROUP BY 1\n", "    \"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "ecec27f8", "metadata": {}, "outputs": [], "source": ["# handle cases when table_path is not available on date_x\n", "# according to correlation analysis, the correlation still hold with different dates.\n", "if \"8\" in sections:\n", "    table_path_date_x_check_query = \"\"\"\n", "        SELECT *\n", "        FROM `{table_path}_20*` \n", "        WHERE CONCAT('20', _TABLE_SUFFIX) = \"{date_x}\"\n", "        LIMIT 1\n", "    \n", "    \"\"\".format(\n", "        table_path=table_path,\n", "        date_x=date_x\n", "    )\n", "    table_path_date_x_check = read_gbq(table_path_date_x_check_query)\n", "    \n", "    # when data is available on date_x, use date_x\n", "    if len(table_path_date_x_check) != 0: \n", "        date_x_best_effort = date_x\n", "    # when data is NOT available on date_x, and 7d prior to the investigation period is also NOT available\n", "    # use 7d prior to today\n", "    elif (datetime.today() - datetime.strptime(period_2_start_date, \"%Y%m%d\")).days > (180-7):\n", "        date_x_best_effort = (\n", "            datetime.today() - <PERSON><PERSON><PERSON>(days=7)).strftime(\"%Y%m%d\")\n", "    # else, use 7d prior to the investigation period\n", "    else:\n", "        date_x_best_effort = (\n", "            datetime.strptime(period_2_start_date, \"%Y%m%d\") - <PERSON><PERSON><PERSON>(days=7)\n", "        ).strftime(\"%Y%m%d\")\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "daad9bb1", "metadata": {}, "outputs": [], "source": ["# generate the intermediate table for the investigated metric for correlation calculation\n", "metric_intermediate_query_template = \"\"\"\n", "    WITH t_power_users AS (\n", "      SELECT\n", "        ghost_user_id\n", "      FROM\n", "        `sc-analytics.report_search.app_power_user_20*`\n", "      WHERE\n", "        CONCAT(\"20\", _TABLE_SUFFIX) = \"{date_x}\"\n", "        AND is_app_power_user = 1\n", "        {sampling_filter}\n", "    ),\n", "    \n", "    t_users_no_birectional_friend AS (\n", "      SELECT\n", "        ghost_user_id\n", "      FROM\n", "        `sc-analytics.report_search.user_cohorts_20*`\n", "      WHERE\n", "        CONCAT(\"20\", _TABLE_SUFFIX) = \"{date_x}\"\n", "        AND bidirectional_friend_count = 0\n", "        {sampling_filter}\n", "    ),\n", "    \n", "    --bidirectional friends table\n", "    t_bidirectional_friends as (\n", "      SELECT \n", "        MOD(ABS(FARM_FINGERPRINT(t_base.ghost_user_id)), 10000) as ghost_user_id_group,  \n", "        CASE \n", "            WHEN t_pu.ghost_user_id IS NOT NULL THEN '3.app power user'\n", "            WHEN t_nbf.ghost_user_id IS NOT NULL THEN '1.no bidirectional friends'\n", "            ELSE '2.average user'\n", "        END as user_type,\n", "        SUM(IF(bidirectional_friend_count>0,is_mau,0))/SUM(is_mau) as prop_1plus_bidirectional_friend_mau\n", "      FROM (\n", "        SELECT *\n", "        FROM `sc-analytics.report_search.user_cohorts_20*` \n", "        WHERE CONCAT(\"20\", _TABLE_SUFFIX) = \"{date_x}\"\n", "          AND ghost_user_id is not null\n", "          {sampling_filter}\n", "      ) t_base\n", "      LEFT JOIN \n", "        t_power_users t_pu\n", "      ON \n", "        t_base.ghost_user_id = t_pu.ghost_user_id\n", "      LEFT JOIN \n", "        t_users_no_birectional_friend t_nbf\n", "      ON \n", "        t_base.ghost_user_id = t_nbf.ghost_user_id\n", "      GROUP BY \n", "        1,2\n", "    )\n", "    \n", "    SELECT *\n", "    FROM ( \n", "      SELECT \n", "        MOD(ABS(FARM_FINGERPRINT(t_base.ghost_user_id)), 10000) as ghost_user_id_group,  \n", "        CASE \n", "            WHEN t_pu.ghost_user_id IS NOT NULL THEN '3.app power user'\n", "            WHEN t_nbf.ghost_user_id IS NOT NULL THEN '1.no bidirectional friends'\n", "            ELSE '2.average user'\n", "        END as user_type,\n", "        {metric_calculation}\n", "      {from_clause}\n", "      LEFT JOIN \n", "        t_power_users t_pu\n", "      ON \n", "        t_base.ghost_user_id = t_pu.ghost_user_id\n", "      LEFT JOIN \n", "        t_users_no_birectional_friend t_nbf\n", "      ON \n", "        t_base.ghost_user_id = t_nbf.ghost_user_id\n", "      WHERE \n", "        CONCAT(\"20\", _TABLE_SUFFIX) = \"{date_x}\"\n", "      GROUP BY \n", "        1,2\n", "    ) tmp\n", "    -- the line below is the only diff compared to curated metric intermediate table calculation sql\n", "    WHERE user_type = \"{user_type_of_interest}\"\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "675c9658", "metadata": {}, "outputs": [], "source": ["# print_check\n", "print('check11')"]}, {"cell_type": "code", "execution_count": null, "id": "1c0832b7", "metadata": {}, "outputs": [], "source": ["if \"8\" in sections:\n", "    # pull the investigated metric data\n", "    if metric_type == \"quantile\" and if_metric_struct_array == False:\n", "        metric_calculation = \"\"\"\n", "                APPROX_QUANTILES({metric_name}, 100)[OFFSET({quantile})] AS {metric_name} \n", "            \"\"\".format(\n", "                metric_name=metric_name,\n", "                quantile=quantile\n", "            )\n", "        from_clause = \"\"\"\n", "                FROM `{table_path}_20*` t_base\n", "            \"\"\".format(\n", "                    table_path=table_path\n", "                )\n", "        metric_name_full = \"{}_p{}\".format(metric_name, quantile)\n", "        \n", "    elif metric_type == \"quantile\" and if_metric_struct_array == True:\n", "        metric_calculation = \"\"\"\n", "                APPROX_QUANTILES(metric_list.element, 100)[OFFSET({quantile})] AS {metric_name} \n", "            \"\"\".format(\n", "                metric_name=metric_name,\n", "                quantile=quantile\n", "            )\n", "        from_clause = \"\"\"\n", "                FROM `{table_path}_20*` t_base, UNNEST({metric_name}.list) metric_list\n", "            \"\"\".format(\n", "                metric_name=metric_name,\n", "                table_path=table_path\n", "            )\n", "        metric_name_full = \"{}_p{}\".format(metric_name, quantile)    \n", "        \n", "    elif metric_type == \"sum\": \n", "        metric_calculation = \"\"\"\n", "                SUM({metric_name}) / {sampling_ratio} AS {metric_name} \n", "            \"\"\".format(\n", "                metric_name=metric_name,\n", "                sampling_ratio=sampling_ratio\n", "            )\n", "        from_clause = \"\"\"\n", "                FROM `{table_path}_20*` t_base\n", "            \"\"\".format(\n", "                table_path=table_path\n", "            )\n", "        metric_name_full = metric_name\n", "    \n", "    metric_intermediate_query = metric_intermediate_query_template.format(\n", "        metric_name=metric_name,\n", "        from_clause=from_clause,\n", "        table_path=table_path,\n", "        metric_calculation=metric_calculation,\n", "        date_x=date_x_best_effort,\n", "        user_type_of_interest=user_type_of_interest,\n", "        sampling_filter=sampling_filter\n", "    )    \n", "    \n", "    # intermediate table for the investigated metric\n", "    df_metric_intermediate = read_gbq(metric_intermediate_query)\n"]}, {"cell_type": "code", "execution_count": null, "id": "724dade5", "metadata": {}, "outputs": [], "source": ["if \"8\" in sections:\n", "    # pull curated metric intermediate table from bq, and filter by user_type_of_interest\n", "    table_prefix = \"metric_user_type_intermediate\"\n", "    table_id = '{}_{}'.format(\n", "        table_prefix,\n", "        date_x)\n", "    \n", "    df_curated_metrics_selected_user_type_sql =  \"\"\"\n", "        SELECT *\n", "        FROM `{project}.{dataset}.{table_id}`\n", "        WHERE user_type = \"{user_type_of_interest}\"\n", "    \"\"\".format(\n", "        project=\"vellum-sc\",\n", "        dataset=\"configuration\",\n", "        table_id=table_id,\n", "        user_type_of_interest=user_type_of_interest\n", "    )\n", "    PROJECT = 'sc-bq-gcs-billingonly'\n", "    df_curated_metrics_selected_user_type = utils.gbq.read_gbq(\n", "        df_curated_metrics_selected_user_type_sql, project_id=PROJECT, dialect='standard')\n", "    \n", "    curated_metrics = [col \n", "                       for col in df_curated_metrics_selected_user_type.columns \n", "                       if col not in ['ghost_user_id_group', 'user_type']\n", "                      ]\n", "    \n", "    # join the investigated metric with curated metrics\n", "    df_metric_intermediate_full = df_metric_intermediate.merge(\n", "        df_curated_metrics_selected_user_type.drop(columns=[metric_name], errors=\"ignore\"),\n", "        how='outer', \n", "        on=['ghost_user_id_group', \"user_type\"]\n", "    )\n"]}, {"cell_type": "code", "execution_count": null, "id": "e93af6dd", "metadata": {}, "outputs": [], "source": ["if \"8\" in sections:\n", "    # correlation results\n", "    df_corr = []\n", "    corr_threshold = 0.6\n", "    n_high_corr_metrics = 10\n", "    \n", "    for curated_metric in curated_metrics:\n", "        df_corr_tmp = df_metric_intermediate_full[[metric_name, curated_metric]].corr().stack().reset_index()\n", "        df_corr_ind = (df_corr_tmp['level_0'] == metric_name) & (df_corr_tmp['level_1'] != metric_name) \n", "       \n", "        df_corr_tmp = df_corr_tmp[df_corr_ind]\n", "        \n", "        if len(df_corr) == 0:\n", "            df_corr = df_corr_tmp.copy()\n", "        else:\n", "            df_corr = pd.concat([df_corr, df_corr_tmp], axis=0, ignore_index=True)\n", "    \n", "    df_corr.columns = [\"metric\", \"correlated_metric\", \"correlation\"]\n", "    \n", "    high_corr_metrics_df = df_corr[\n", "        (df_corr[\"correlation\"] > corr_threshold) \n", "        &\n", "        (df_corr[\"correlation\"] < 0.995) # exclude the metric itself, sometimes quantile calculations have small diff, so use 0.995\n", "        ][\n", "            [\"correlated_metric\", \"correlation\"]\n", "        ].sort_values(\n", "            by='correlation', ascending=False\n", "        ).head(n_high_corr_metrics)\n", "    \n", "    high_corr_metrics = list(high_corr_metrics_df[\"correlated_metric\"])"]}, {"cell_type": "code", "execution_count": null, "id": "55440a42", "metadata": {}, "outputs": [], "source": ["# print_check\n", "print('check12')"]}, {"cell_type": "code", "execution_count": null, "id": "4f3cca07", "metadata": {}, "outputs": [], "source": ["if \"8\" in sections:\n", "    # both core and highly correalted metrics use quest ab data\n", "    # quest ab event tables have a 180-day retention policy.\n", "    # skip section 8 if any date in the baseline period or the investigation period are more than 180 days prior.\n", "    # todo: find an aggregated quest ab metric that has longer retention. (caveat: may not support user_cohorts_filter for aggregated data source.)\n", "    \n", "    date_180_prior = (datetime.today() - timed<PERSON><PERSON>(days=180)).strftime(\"%Y%m%d\")\n", "    \n", "    if period_1_start_date < date_180_prior or period_2_start_date < date_180_prior:\n", "        show_related_metrics_overview = False \n", "    else:\n", "        show_related_metrics_overview = True"]}, {"cell_type": "code", "execution_count": null, "id": "0f1391bd", "metadata": {}, "outputs": [], "source": ["# print_check\n", "print('check7')"]}, {"cell_type": "code", "execution_count": null, "id": "c78bf928", "metadata": {}, "outputs": [], "source": ["# quest ab perf metrics are already breakdown to android / ios metrics, so we don't need to worry about it in this section\n", "\n", "key_metrics = [# <PERSON><PERSON><PERSON> manually curated this list. full list are quest ab core engagement/perf metrics\n", "    'chat_send',\n", "    'chat_view',\n", "    'chat_send_active_day',\n", "    'chat_view_active_day',\n", "    'lens_filter_lens_swipe',\n", "    'lens_direct_snap_create',\n", "    # 'lens_filter_lens_swipe_active_day',\n", "    # 'lens_direct_snap_create_active_day',\n", "    'map_open',\n", "    # 'map_open_active_day',\n", "    'friend_story_snap_time_viewed',\n", "    'friend_story_snap_view',\n", "    'friend_story_story_view',\n", "    'friend_story_snap_post',\n", "    # 'friend_story_story_view_active_day',\n", "    # 'friend_story_snap_view_active_day',\n", "    # 'friend_story_snap_post_active_day',\n", "    # 'camera_warm_startup_latency',\n", "    'camera_warm_startup_latency',\n", "    # 'camera_cold_startup_latency',\n", "    'camera_cold_startup_latency',\n", "    # 'camera_video_snap_create_latency',\n", "    'camera_video_snap_create_latency',\n", "    # 'camera_image_snap_create_latency',\n", "    'camera_image_snap_create_latency',\n", "    # 'camera_hot_startup_latency',\n", "    'camera_hot_startup_latency',\n", "    # 'camera_startup_latency',\n", "    'camera_startup_latency',\n", "    'direct_snap_create',\n", "    # 'active_day',\n", "    # 'direct_snap_create_active_day',\n", "    # 'app_close_session_time',\n", "    # 'friend_outbound_request_to_new_user_send',\n", "    # 'friend_outbound_request_send',\n", "    # 'friend_inbound_request_accept',\n", "    'direct_snap_view',\n", "    'direct_snap_send',\n", "    'direct_snap_save',\n", "    # 'direct_snap_send_active_day',\n", "    # 'direct_snap_save_active_day',\n", "    # 'direct_snap_view_active_day',\n", "    'app_open',\n", "    'discover_feed_non_friend_story_view',\n", "    'discover_feed_non_friend_story_time_viewed',\n", "    # 'discover_feed_non_friend_story_view_active_day',\n", "    # 'discover_feed_switch_latency',\n", "    # 'discover_feed_switch_latency',\n", "    # 'discover_feed_switch_latency'\n", "]\n", "\n", "# len(key_metrics): 22"]}, {"cell_type": "code", "execution_count": null, "id": "9213bdde", "metadata": {}, "outputs": [], "source": ["if \"8\" in sections:\n", "    related_metric_query_template = \"\"\"\n", "        SELECT\n", "            \"{related_metric_name_full}\" AS metric_name,\n", "            \"{related_metric_join_key}\" AS related_metric_join_key,\n", "            \"{related_metric_good_direction}\" good_direction,\n", "            AVG(IF(ds BETWEEN \"{period_1_start_date}\" AND \"{period_1_end_date}\", metric, NULL)) AS metric_period_1,\n", "            AVG(IF(ds BETWEEN \"{period_2_start_date}\" AND \"{period_2_end_date}\", metric, NULL)) AS metric_period_2\n", "        FROM (\n", "            SELECT \n", "                ds,    \n", "                {metric_calculation}\n", "            FROM ( \n", "                SELECT\n", "                    CONCAT(\"20\", _TABLE_SUFFIX) AS ds,  \n", "                    ghost_user_id,\n", "                    {metric_name}\n", "                FROM `{related_metric_table_path}_20*` \n", "                WHERE\n", "                    {period_filter}\n", "                    {sampling_filter}\n", "            ) metric_data\n", "            {join_method} (\n", "              SELECT\n", "                  ghost_user_id\n", "              FROM\n", "                  `sc-analytics.report_search.user_cohorts_20*`\n", "              WHERE \n", "              -- this section calculate metric total during the period, so use the investigation period end date for user_cohorts filtering\n", "                  CONCAT(\"20\", _TABLE_SUFFIX) = \"{period_2_end_date}\" \n", "                  {user_cohorts_filter}\n", "            ) uc\n", "            USING (ghost_user_id)\n", "            GROUP BY 1\n", "        ) metric_daily\n", "        GROUP BY 1,2\n", "        \"\"\"\n"]}, {"cell_type": "code", "execution_count": null, "id": "60032a3d", "metadata": {}, "outputs": [], "source": ["# print_check\n", "print('check13')"]}, {"cell_type": "code", "execution_count": null, "id": "5cd38cdb", "metadata": {}, "outputs": [], "source": ["if \"8\" in sections:\n", "    def related_metric_compare(metrics):\n", "    \n", "        if metrics == \"key_metrics\":\n", "            metadata_related_metrics = ab_console_metrics_catalog.metadata.loc[\n", "                (ab_console_metrics_catalog.metadata.dash_group_id.isin(['1', '2'])) # core engagement & core perf metric on ab console  \n", "                &\n", "                (ab_console_metrics_catalog.metadata.quest_measure.isin(key_metrics))\n", "            ]\n", "        elif metrics == \"high_corr_metrics\":\n", "            metadata_high_corr_metrics_all = ab_console_metrics_catalog.metadata[\n", "                ab_console_metrics_catalog.metadata[\n", "                    [\"major_metric\", \"minor_metric\"]\n", "                ].astype(str).sum(axis = 1).isin(\n", "                    [i.replace(\"____\",\"\") for i in high_corr_metrics]\n", "                )\n", "            ]\n", "            metadata_related_metrics = metadata_high_corr_metrics_all[\n", "                (metadata_high_corr_metrics_all.metric_type.isin([\"sum\", \"quantile\"]))\n", "            ]\n", "    \n", "        else:\n", "            raise ValueError(\"wrong metrics value\")\n", "        \n", "        if len(metadata_related_metrics) == 0:\n", "            return None   \n", "        \n", "        related_metric_queries = []\n", "        for metric in metadata_related_metrics.iterrows():\n", "            related_metric_table_path = \"sc-portal.quest.{}_{}\".format(\n", "                metric[1][\"quest_section\"], \n", "                metric[1][\"quest_job\"]\n", "            )\n", "            related_metric_name = metric[1][\"quest_measure\"] # \"quest_measure not available for ratio metric. No ratio key metric in the curated list.\n", "            related_metric_metric_type = metric[1][\"metric_type\"]\n", "            related_metric_good_direction = metric[1][\"good_direction\"]\n", "            related_metric_join_key = \"{}____{}\".format(metric[1][\"major_metric\"], metric[1][\"minor_metric\"])\n", "            \n", "            if related_metric_metric_type == \"quantile\":\n", "                related_metric_quantile = metric[1][\"minor_metric\"][-2:]\n", "                related_metric_name_full = \"{} p{}\".format(\n", "                    metric[1][\"dash_name\"], related_metric_quantile)\n", "            elif related_metric_metric_type == \"sum\":\n", "                related_metric_name_full = metric[1][\"dash_name\"]            \n", "    \n", "            # pull key metric data\n", "            if related_metric_metric_type == \"quantile\": \n", "                # related_metric in quest ab table source are all numeric, and no need to check if struct array.\n", "    \n", "                metric_calculation = \"\"\"\n", "                        APPROX_QUANTILES({metric_name}, 100)[OFFSET({quantile})] AS metric \n", "                    \"\"\".format(\n", "                        metric_name=related_metric_name,\n", "                        quantile=related_metric_quantile\n", "                    )\n", "                from_clause = \"\"\"\n", "                        FROM `{table_path}_20*`\n", "                    \"\"\".format(\n", "                            table_path=related_metric_table_path\n", "                        )\n", "    \n", "            elif related_metric_metric_type == \"sum\": \n", "                metric_calculation = \"\"\"\n", "                        SUM({metric_name}) / {sampling_ratio} AS metric \n", "                    \"\"\".format(\n", "                        metric_name=related_metric_name,\n", "                        sampling_ratio=sampling_ratio\n", "                    )\n", "                from_clause = \"\"\"\n", "                        FROM `{table_path}_20*`\n", "                    \"\"\".format(\n", "                        table_path=related_metric_table_path\n", "                    )\n", "    \n", "            related_metric_query = related_metric_query_template.format(\n", "                metric_name=related_metric_name,\n", "                related_metric_good_direction=related_metric_good_direction,\n", "                related_metric_table_path=related_metric_table_path,\n", "                related_metric_name_full=related_metric_name_full,\n", "                related_metric_join_key=related_metric_join_key,\n", "                metric_calculation=metric_calculation,\n", "                period_filter=date_filter_2_periods,\n", "                user_cohorts_filter= \"\" if not user_cohorts_filter else \"AND ({})\".format(user_cohorts_filter),\n", "                join_method=\"LEFT JOIN\" if not user_cohorts_filter else \"JOIN\",\n", "                period_1_start_date=period_1_start_date,\n", "                period_1_end_date=period_1_end_date,\n", "                period_2_start_date=period_2_start_date,\n", "                period_2_end_date=period_2_end_date,\n", "                sampling_filter=sampling_filter\n", "            )    \n", "            related_metric_queries.append(related_metric_query)\n", "    \n", "        df_related_metrics = pd.concat(batch_read_gbq(related_metric_queries), axis=0)\n", "        \n", "        df_related_metrics = df_related_metrics.reset_index(drop=True)\n", "        df_related_metrics[\"diff%\"] = [\n", "            str(i) + \"%\" \n", "            for i in round(df_related_metrics[\"metric_period_2\"] / df_related_metrics[\"metric_period_1\"]*100 - 100, 2)\n", "        ]\n", "        \n", "        # add corr column\n", "        if metrics == \"high_corr_metrics\":\n", "            df_related_metrics = df_related_metrics.merge(\n", "                high_corr_metrics_df, \n", "                how='inner', \n", "                left_on=['related_metric_join_key'], \n", "                right_on=['correlated_metric']\n", "            ).drop(['correlated_metric'], axis=1)\n", "        \n", "        df_related_metrics = df_related_metrics.rename(\n", "            columns={\n", "                \"metric_period_1\": period_1,\n", "                \"metric_period_2\": period_2\n", "            }\n", "        ).drop(\n", "            columns=['related_metric_join_key']\n", "        )\n", "        \n", "        if metrics == \"key_metrics\":\n", "            df_related_metrics = df_related_metrics.sort_values(\n", "                by = ['metric_name']).reset_index(drop=True)\n", "        elif metrics == \"high_corr_metrics\":\n", "            df_related_metrics = df_related_metrics.sort_values(\n", "                by = ['correlation'], ascending=False).reset_index(drop=True)\n", "        \n", "        df_related_metrics[period_1] = df_related_metrics[period_1].apply(lambda x: '%.2f' % x)\n", "        df_related_metrics[period_2] = df_related_metrics[period_2].apply(lambda x: '%.2f' % x)\n", "    \n", "        df_related_metrics_diff_color = [\n", "            background_color_df(\n", "                df_related_metrics[\"diff%\"][i], \n", "                df_related_metrics[\"good_direction\"][i], \n", "                thresholds=[0.03,0.01,-0.03,-0.01]\n", "            )\n", "            for i in range(len(df_related_metrics))\n", "        ]\n", "            \n", "        def style_specific_cell(x):\n", "            # diff% color is condition on good_direction for each key metric\n", "            df_style_specific_cell = pd.DataFrame('', index=x.index, columns=x.columns)\n", "            for i in range(len(df_related_metrics)):\n", "                df_style_specific_cell.iloc[i, 4] = df_related_metrics_diff_color[i]\n", "            return df_style_specific_cell\n", "    \n", "        df_related_metrics_style = df_related_metrics.style.apply(style_specific_cell, axis=None)\n", "    \n", "        return df_related_metrics_style\n"]}, {"cell_type": "code", "execution_count": null, "id": "3606e58d", "metadata": {}, "outputs": [], "source": ["#  metric investigation key findings calculation\n", "period_1_timedelta = datetime.strptime(period_1_end_date, \"%Y%m%d\") - datetime.strptime(period_1_start_date, \"%Y%m%d\") \n", "period_2_timedelta = datetime.strptime(period_2_end_date, \"%Y%m%d\") - datetime.strptime(period_2_start_date, \"%Y%m%d\") \n", "metric_summary_format = {}\n", "if \"1\" in sections:   \n", "    # SUM metric: metric investigation key findings calculation\n", "    if metric_type == \"sum\":\n", "        for period in [\"period_1\", \"period_2\"]:\n", "            metric_summary_format[period] = str(df_pop_change_reindex[df_pop_change_reindex['dimension'] == 'all'][eval(period)][0])\n", "        metric_summary_format[\"diff% between 2 periods\"] = str(df_pop_change_reindex[df_pop_change_reindex['dimension'] == 'all']['diff% between 2 periods'][0]) \n", "        \n", "        try:\n", "            metric_change_android = list(df_pop_change_reindex[df_pop_change_reindex['dimension_values'] == 'Android']['diff% between 2 periods'])[0]\n", "            metric_change_android_float = float(metric_change_android[:-1])/100\n", "            metric_change_android_colored = html_number_color(metric_change_android, good_direction=good_direction)\n", "        except:\n", "            metric_change_android = None\n", "    \n", "        try:\n", "            metric_change_ios = list(df_pop_change_reindex[df_pop_change_reindex['dimension_values'] == 'iOS']['diff% between 2 periods'])[0]\n", "            metric_change_ios_float = float(metric_change_ios[:-1])/100\n", "            metric_change_ios_colored = html_number_color(metric_change_ios, good_direction=good_direction)\n", "        except:\n", "            metric_change_ios = None\n", "    \n", "        df_pop_change_country = df_pop_change_reindex[df_pop_change_reindex['dimension'] == 'l_90_country'][['dimension_values','diff% between 2 periods']]\n", "        df_pop_change_country_dict = {i : j\n", "                                      for i, j in zip(df_pop_change_country['dimension_values'], df_pop_change_country['diff% between 2 periods']) \n", "                                      if 'Other' not in i }\n", "        df_pop_change_country_dict_float = {i: float(j[:-1])/100  for (i, j) in df_pop_change_country_dict.items()}\n", "        sorted_df_pop_change_country_dict = {k: v for k, v in sorted(df_pop_change_country_dict_float.items(), key=lambda item: abs(item[1]))}\n", "    \n", "        country_top3 = list(sorted_df_pop_change_country_dict)[-3:][::-1]    \n", "        country_top3_dict = {i : df_pop_change_country_dict[i] for i in country_top3}\n", "        country_top3_dict_colored = {i:html_number_color(j, good_direction=good_direction) \n", "                                     for (i,j) in country_top3_dict.items()}\n", "    \n", "    # QUANTILE metric: metric investigation key findings calculation\n", "    elif metric_type == \"quantile\":\n", "        \n", "        # get metric value for period 1 vs period 2 with format\n", "        metric_summary = {}\n", "        for period in [\"period_1\", \"period_2\"]:\n", "            metric_summary[period] = {} \n", "            for os_type in df_reshaped_os_type:\n", "                metric_summary[period][os_type] = float(list(\n", "                        df_pop_change_reindex[os_type].loc[df_pop_change_reindex[os_type].dimension.isin([\"all\"])][metric_name][eval(period)]\n", "                        )[0].replace(\",\",\"\")\n", "                )\n", "                \n", "            if if_both_os_type:\n", "                metric_summary_format[period] = ', '.join(\n", "                    [\"{} ({})\".format(metric_summary[period][os_type], os_type_format(os_type)) for os_type in df_reshaped_os_type])\n", "            else:\n", "                metric_summary_format[period] = str(metric_summary[period][df_reshaped_os_type[0]])\n", "        \n", "        metric_summary[\"diff% between 2 periods\"] = {}        \n", "        for os_type in df_reshaped_os_type:\n", "            metric_summary[\"diff% between 2 periods\"][os_type] = list(\n", "                    df_pop_change_reindex[os_type].loc[\n", "                        df_pop_change_reindex[os_type].dimension.isin([\"all\"])][metric_name]['diff% between 2 periods'])[0]\n", "        \n", "        if if_both_os_type:\n", "            metric_summary_format[\"diff% between 2 periods\"] = \", \".join(\n", "                [\"{} ({})\".format(\n", "                    html_number_color(metric_summary[\"diff% between 2 periods\"][os_type]), os_type_format(os_type)) for os_type in df_reshaped_os_type]\n", "                )\n", "        else:\n", "            metric_summary_format[\"diff% between 2 periods\"] = str(html_number_color(metric_summary[\"diff% between 2 periods\"][df_reshaped_os_type[0]]))\n", "        \n", "        try:\n", "            metric_change_android = df_pop_change_reindex[\"android\"].loc[df_pop_change_reindex[\"android\"].dimension.isin([\"all\"])][metric_name]['diff% between 2 periods']\n", "            metric_change_android_float = float(list(metric_change_android)[0][:-1])/100\n", "            metric_change_android_colored = html_number_color(metric_change_android, good_direction=good_direction)\n", "        except:\n", "            metric_change_android = None\n", "    \n", "        try:\n", "            metric_change_ios = df_pop_change_reindex[\"ios\"].loc[df_pop_change_reindex[\"ios\"].dimension.isin([\"all\"])][metric_name]['diff% between 2 periods']\n", "            metric_change_ios_float = float(list(metric_change_ios)[0][:-1])/100\n", "            metric_change_ios_colored = html_number_color(metric_change_ios_float, good_direction=good_direction)\n", "        except:\n", "            metric_change_ios = None\n", "    \n", "        device_cluster_top3 = {}\n", "        device_cluster_top3_dict_colored = {}\n", "        for os_type in df_reshaped_os_type:\n", "    \n", "            df_pop_change_device_cluster = df_pop_change_reindex[os_type].loc[df_pop_change_reindex[os_type].dimension.isin([\"device_cluster\"])]\n", "    \n", "            df_pop_change_device_cluster_dict = {i : j\n", "                                          for i, j in zip(df_pop_change_device_cluster.dimension_values, df_pop_change_device_cluster[\"event_count\",\"diff% between 2 periods\"]) \n", "                                          if 'UNKNOWN' not in i }\n", "            df_pop_change_device_cluster_dict_float = {i: float(j[:-1])/100 for (i, j) in df_pop_change_device_cluster_dict.items()}\n", "    \n", "            sorted_df_pop_change_device_cluster_dict = {k: v for k, v in sorted(df_pop_change_device_cluster_dict_float.items(), key=lambda item: abs(item[1]))}\n", "    \n", "            device_cluster_top3[os_type] = list(sorted_df_pop_change_device_cluster_dict)[-3:][::-1]   \n", "            device_cluster_top3_dict = {i : df_pop_change_device_cluster_dict[i] for i in device_cluster_top3[os_type]}\n", "            device_cluster_top3_dict_colored[os_type] = {i:html_number_color(j, good_direction=good_direction) \n", "                                                         for (i,j) in device_cluster_top3_dict.items()}\n"]}, {"cell_type": "code", "execution_count": null, "id": "12134a41", "metadata": {}, "outputs": [], "source": ["# print_check\n", "print('check14')"]}, {"cell_type": "code", "execution_count": null, "id": "85d4cf23", "metadata": {}, "outputs": [], "source": [" #   ____        _               _     _____                 _ _       \n", " #  / __ \\      | |             | |   |  __ \\               | | |      \n", " # | |  | |_   _| |_ _ __  _   _| |_  | |__) |___  ___ _   _| | |_ ___ \n", " # | |  | | | | | __| '_ \\| | | | __| |  _  // _ \\/ __| | | | | __/ __|\n", " # | |__| | |_| | |_| |_) | |_| | |_  | | \\ \\  __/\\__ \\ |_| | | |_\\__ \\\n", " #  \\____/ \\__,_|\\__| .__/ \\__,_|\\__| |_|  \\_\\___||___/\\__,_|_|\\__|___/\n", " #                  | |                                                \n", " #                  |_|                                                "]}, {"cell_type": "code", "execution_count": null, "id": "997e31c2", "metadata": {}, "outputs": [], "source": ["# notebook display\n", "# title\n", "# display_html('<div class=\"myDiv\">')\n", "display_html(title(\"App Performance Metric Investigation Automation\", 1))\n", "display_html(text(\"Generated time: {}\".format(datetime.now())))\n", "# display_html(\"<p>For any questions or feedback, please submit <b><a href=https://forms.gle/zLY1cyEGiE8ieQFR8 style='color:red;'>Customer Feedback Google Form</a></b>, or reach out to <PERSON><PERSON> (qwang2@), <PERSON> (lwang@), <PERSON> (xhe@), <PERSON> (jlee15@).<br>\")\n", "# display_html(\"<p> How to read the Lego Report: <a href=https://wiki.sc-corp.net/display/DS/Metrics+Investigation+Framework>Wiki</a>\")\n", "\n", "# uncomment below when test in stg\n", "# display(Image(url='https://storage.cloud.google.com/vellum-stg-notebook/metric_investigation/husky_lego_report.png', width = 100, height = 100))\n", "# uncomment below when test in prod\n", "display(Image(url='https://storage.cloud.google.com/vellum-prod-notebook/metric_investigation/husky_perf_report2.png', width = 100, height = 100))\n", "display_html(\"<hr>\")\n", "\n", "        \n", "# section 1\n", "if \"1\" in sections:\n", "    display_html(title(\"Investigation Summary\", 2))\n", "    display_html(text(\"Table: <strong>{}</strong>\".format(table_path)))\n", "    if metric_type == \"sum\": \n", "        display_html(text(\"Metric: <strong>{}</strong>\".format(metric_name)))\n", "    elif metric_type == \"quantile\":\n", "        display_html(text(\"Metric: <strong>{} p{}</strong>\".format(metric_name, quantile)))\n", "    display_html(text(\"Metric Good Direction: <strong>{}</strong>\".format(good_direction)))\n", "    display_html(text(\"Metric Type: <strong>{}</strong>\".format(metric_type)))\n", "        \n", "    display_html(text(\n", "        \"Baseline Period ({}{}): <strong>{}</strong>\".format(\n", "            period_1,\n", "            \", {}-day daily average\".format(period_1_dates_length) if len(period_1)>10 else \"\", # if the period only has 1 date, ignore daily avg comment. 10 is the threshold for the length of the str.\n", "            metric_summary_format[\"period_1\"]\n", "        )))\n", "    display_html(text(\n", "        \"Investigation Period ({}{}): <strong>{}</strong>\".format(\n", "            period_2,\n", "            \", {}-day daily average\".format(period_2_dates_length) if len(period_2)>10 else \"\",\n", "            metric_summary_format[\"period_2\"]\n", "        )))\n", "    \n", "    if sampling_ratio != 1:\n", "        display_html(text(\n", "            \"\"\"\n", "            Note: The sampling ratio {sampling_ratio} applies for the query optimization purpose. \n", "            Meanwhile, multipliers are applied so that metric values and event counts are equal (or very close) to the values without sampling.\n", "            \"\"\".format(sampling_ratio=sampling_ratio)\n", "        ))\n", "    \n", "    if source_table_filter:\n", "        display_html(text(\"Source table filters: <strong>{}</strong>\".format(source_table_filter)))   \n", "        \n", "    if user_cohorts_filter:\n", "        display_html(text(\"User_cohorts table filters: <strong>{}</strong>\".format(user_cohorts_filter)))   \n", "    \n", "    if table_path in [\"sc-analytics.report_app.dau_user_device_country\", \"sc-analytics.report_app.dau_user_country\"]:\n", "        metric_name_link = metric_name.lower()\n", "        table_filter = \"\"\n", "        date_filter = f\"dateRangeOption=Custom&selectedDateRange%5B0%5D={period_2_start_date_hyphen}T00%3A00%3A00.000Z&selectedDateRange%5B1%5D={period_2_end_date_hyphen}T23%3A59%3A59.000Z\"\n", "        if not any(metric_name_link.endswith(end) for end in [\"uu\", \"time\", \"count\"]):\n", "            metric_name_link+=\"_count\"\n", "        if source_table_filter:\n", "            source_table_filter_elements = source_table_filter.split(\"=\")\n", "            filter_category = source_table_filter_elements[0].strip().lower()\n", "            filter_value = source_table_filter_elements[1].strip().replace(\"'\",\"\")\n", "            table_filter = f\"&selectedDimensionCells%5B0%5D.alias={filter_category}&selectedDimensionCells%5B0%5D.value={filter_value}&selectedDimensionCells%5B0%5D.filter=include\"\n", "        link = f\"https://mercury-v2.sc-corp.net/workspaces/scanalytics_duc#{date_filter}{table_filter}&selectedMetrics%5B0%5D={metric_name_link}\"\n", "        display_html(\"<p><a href={link}>Mercury Dashboard for {metric_name}</a><br>\".format(link=link,\n", "                                                                                           metric_name=metric_name))\n", "\n", "            \n", "            \n", "    display_html(\"<hr>\")\n", "    # metric key findings summary\n", "    if (\"2\" in sections) or (\"3\" in sections) or (len(df_holiday) != 0):\n", "        display_html(text(\"Metric investigation key findings:\"))\n", "        display_html(\"<li>Metric Percentage Change ((investigation_period - baseline_period) / baseline_period): {}.</li>\".format(\n", "                        metric_summary_format[\"diff% between 2 periods\"]\n", "        ))\n", "    if \"2\" in sections:\n", "        if metric_type == \"sum\":     \n", "            if metric_change_android is not None and metric_change_ios is not None:\n", "                if metric_change_android == 0 or metric_change_ios == 0:\n", "                    display_html(\"<li>Metric changes for Android: {}, iOS: {}.</li>\".format(\n", "                            metric_change_android_colored, metric_change_ios_colored)\n", "                    )\n", "                elif metric_change_ios_float / metric_change_android_float < 0:\n", "                    display_html(\"<li>Metric change for iOS and Android are in opposite directions, Android: {}, iOS: {}.</li>\".format(\n", "                            metric_change_android_colored, metric_change_ios_colored)\n", "                    )\n", "                elif metric_change_ios_float / metric_change_android_float < 0.9 and abs(metric_change_android_float) - abs(metric_change_ios_float) > 0.001: # edge case 1% vs. 0.9%; 50% vs. 45% are similar\n", "                    display_html(\"<li>Android metric changed more than iOS, Android: {}, iOS: {}.</li>\".format(\n", "                            metric_change_android_colored, metric_change_ios_colored)\n", "                    )\n", "                elif metric_change_android_float / metric_change_ios_float < 0.9 and abs(metric_change_ios_float) - abs(metric_change_android_float) > 0.001:\n", "                    display_html(\"<li>iOS metric changed more than Android, Android: {}, iOS: {}.</li>\".format(\n", "                            metric_change_android_colored, metric_change_ios_colored)\n", "                    )\n", "                else:\n", "                    display_html(\"<li>Android and iOS metric changes are quite similar, Android: {}, iOS: {}.</li>\".format(\n", "                            metric_change_android_colored, metric_change_ios_colored)\n", "                    )\n", "    \n", "    if metric_type == \"sum\":        \n", "        if \"2\" in sections: \n", "            if len(country_top3) == 3:\n", "                display_html(\n", "                    \"<li>Top 3 <strong> countires </strong> with the biggest metric changes (among top 20 DAU countries): {}.</li>\".format(\n", "                            \", \".join([\"<strong>\" + i + \"</strong>\" + country_top3_dict_colored[i] for i in country_top3_dict_colored])\n", "                        )  \n", "                    )\n", "        if \"5\" in sections:    \n", "            if len(dimensions_top3) == 3:\n", "                display_html(\n", "                    \"<li>Top 3 <strong> dimensions </strong> contributed the most: {}. More details in section 5. </li>\".format(\n", "                            \", \".join([\"<strong>\" + d + \"</strong>\" for d in dimensions_top3])\n", "                        )  \n", "                    )\n", "    \n", "    elif metric_type == \"quantile\":\n", "        if \"2\" in sections:\n", "            if device_cluster_top3_dict_colored:\n", "                for os_type in df_reshaped_os_type:\n", "                    display_html(\n", "                        \"<li>Top 3 <strong> {} device_cluster </strong> with the biggest metric changes : {}.</li>\".format(\n", "                                os_type_format(os_type),\n", "                                \", \".join([\"<strong>\" + i + \"</strong>\" + device_cluster_top3_dict_colored[os_type][i] \n", "                                           for i in device_cluster_top3_dict_colored[os_type]])\n", "                            )  \n", "                        )\n", "        \n", "    \n", "#     if len(df_holiday) != 0 and \"3\" in sections:\n", "#         holiday_html = \"<li>Holiday effects:<il>\" \n", "\n", "#         if len([i for i in list(df_holiday[\"holiday_affected_periods\"]) if \"baseline period\" in i]) > 0:\n", "#             baseline_period_ind = [i for i in list(df_holiday[\"holiday_affected_periods\"]) if \"baseline period\" in i][0]\n", "#             df_holiday_period_1 = df_holiday[df_holiday[\"holiday_affected_periods\"] == baseline_period_ind]\n", "\n", "#             holiday_html += '<ul> <li> The baseline period may be affected by the following holidays: </ul>'\n", "#             holiday_html += '<ul><ul>'+  ''.join(\n", "#                                 [\"<li><strong>\" + d[0] + \"</strong> (\" + period_format(d[1].strftime(\"%Y%m%d\") + ' - ' + d[2].strftime(\"%Y%m%d\")) + \") \"+\n", "#                                  \"in the region \" + d[3] + \". </li>\"  for d in df_holiday_period_1.values.tolist()]\n", "#                             ) + '</ul></ul>'\n", "\n", "#         if len([i for i in list(df_holiday[\"holiday_affected_periods\"]) if \"investigation period\" in i]) > 0:\n", "#             investigation_period_ind = [i for i in list(df_holiday[\"holiday_affected_periods\"]) if \"investigation period\" in i][0]\n", "#             df_holiday_period_2 = df_holiday[df_holiday[\"holiday_affected_periods\"] == investigation_period_ind]\n", "\n", "#             holiday_html += '<ul> <li> The investigation period may be affected by the following holidays: </ul>'\n", "#             holiday_html += '<ul><ul>'+  ''.join(\n", "#                                 [\"<li><strong>\" + d[0] + \"</strong> (\" + period_format(d[1].strftime(\"%Y%m%d\") + ' - ' + d[2].strftime(\"%Y%m%d\")) + \") \"+\n", "#                                  \"in the region \" + d[3] + \". </li>\"  for d in df_holiday_period_2.values.tolist()]\n", "#                             ) + '</ul></ul>'\n", "\n", "#         display_html(holiday_html) \n", "        \n", "        \n", "        \n", "    display_html(\"<hr>\")"]}, {"cell_type": "code", "execution_count": null, "id": "b26bc293", "metadata": {}, "outputs": [], "source": ["# section 2\n", "if \"2\" in sections:\n", "    if metric_type == \"sum\":   \n", "        df_section_2_columns = df_pop_change.columns\n", "        display_html(title(\"Metric Value Changes by OS and Country\", 2))\n", "        metric_or_event_count = \"metric\"\n", "        \n", "    elif metric_type == \"quantile\":\n", "        df_section_2_columns = df_pop_change_reindex[df_reshaped_os_type[0]][metric_name].columns\n", "        display_html(title(\"Metric Value Changes by OS and Device Cluster\", 2))\n", "        metric_or_event_count = \"metric or event count\"\n", "    \n", "    display_html(text(\"Column definitions:\"))\n", "    display_html(\"<li> diff% between 2 periods: The difference % for {} daily average between {} and {}.</li>\".format(\n", "        metric_or_event_count, period_1, period_2))\n", "    \n", "        \n", "    if 'diff% between 2 periods 1 year ago' in df_section_2_columns: \n", "        display_html(\"<li> diff% between 2 periods 1 year ago: The difference % for {} daily average between {} and {}.</li>\".format(\n", "            metric_or_event_count, period_1_last_year, period_2_last_year))\n", "        display_html(\"<li> \tinvestigation period YOY: The difference % for {} daily average between {} and {}.</li>\".format(\n", "            metric_or_event_count, period_2_last_year, period_2))\n", "    else:\n", "        display_html(\"<li> diff% between 2 periods 1 year ago is not shown below, since the data between {} and {} is not available. </li>\".format(\n", "            period_1_last_year, period_2_last_year))\n", "    \n", "    if metric_type == \"sum\": \n", "        to_period_over_period_table_display(data=df_pop_change, metric_type=metric_type, good_direction = good_direction)\n", "        \n", "    elif metric_type == \"quantile\":\n", "        for os_type in df_reshaped_os_type:\n", "            display_html(title(\"{}\".format(os_type_format(os_type)), 3))\n", "            to_period_over_period_table_display(data=df_pop_change_reindex[os_type], metric_type=metric_type, good_direction = good_direction)\n", "    \n", "    display_html(\"<hr>\")"]}, {"cell_type": "code", "execution_count": null, "id": "d9133957", "metadata": {}, "outputs": [], "source": ["# # section 3\n", "# if \"3\" in sections:    \n", "#     display_html(title(\"External and Internal Events\", 2))\n", "    \n", "#     display_html(title(\"External Events\", 3))\n", "#     display_html(title(\"Holidays\", 4))\n", "    \n", "# #     if len(df_holiday)==0:  \n", "# #         display_html(text(\"No common holiday is found that coincides with the metric regression.\"))\n", "# #     else:\n", "# #         display_html(text(\"Holidays below coincide with the baseline period or the investigation period.\"))\n", "# #         display(df_holiday)\n", "#     # display_html(\"<hr>\")\n", "    \n", "#     display_html(title(\"Internal Events\", 3))\n", "    \n", "#     if metric_source == \"quest_ab\":\n", "#         dash_metric_name = quest_metric_detail['dash_name']\n", "#         if metric_type == \"quantile\":\n", "#             dash_metric_name = f\"{quest_metric_detail['dash_name']} p{quantile}\"\n", "#         if dash_metric_name in list(dash_metrics):\n", "#             dashboard_metric_display_name = dash_metric_name.replace(\" \", \"+\")\n", "#             baseline_start_date = re.sub(r'(\\d{4})(\\d{2})(\\d{2})', r'\\1%2F\\2%2F\\3', period_1_start_date)\n", "#             baseline_end_date = re.sub(r'(\\d{4})(\\d{2})(\\d{2})', r'\\1%2F\\2%2F\\3', period_1_end_date)\n", "#             investigation_start_date = re.sub(r'(\\d{4})(\\d{2})(\\d{2})', r'\\1%2F\\2%2F\\3', period_2_start_date)\n", "#             investigation_end_date = re.sub(r'(\\d{4})(\\d{2})(\\d{2})', r'\\1%2F\\2%2F\\3', period_2_end_date)\n", "#             dashboard_link_baseline = \"https://looker.sc-corp.net/dashboards/6306?Date+Filter={min_date}+to+{max_date}&Study+Namespace=&P+Value=%3C0.05&Metric+Display+Name={metric_name}\".format(\n", "#                 min_date=baseline_start_date,\n", "#                 max_date=baseline_end_date,\n", "#                 metric_name=dashboard_metric_display_name\n", "#             )\n", "#             dashboard_link_investigation = \"https://looker.sc-corp.net/dashboards/6306?Date+Filter={min_date}+to+{max_date}&Study+Namespace=&P+Value=%3C0.05&Metric+Display+Name={metric_name}\".format(\n", "#                 min_date=investigation_start_date,\n", "#                 max_date=investigation_end_date,\n", "#                 metric_name=dashboard_metric_display_name\n", "#             )\n", "#             display_html(title(\"Impactful AB Launches Dashboard\", 4))\n", "#             display_html(\n", "#                 \"<p><a href='{dashboard_link_baseline}', target='__blank'>Baseline Period</a> & <a href='{dashboard_link_investigation}', target='__blank'>Investigation Period</a> <a href='https://docs.google.com/document/d/1GHpAmEeCWYb1Q-EtoQx_patMJpoUhbWv1wKAuIadrQI/', target='__blank'>(Document) </a>:<br>The Dashboard can be used to explore launch information. Select a metric name, and optionally filter by study namespace or by p-value. The resulting dashboard will show the AB launches that were expected to impact this metric based on pre-launch AB experiment data.</p><br>\".format(\n", "#                     dashboard_link_baseline=dashboard_link_baseline,\n", "#                     dashboard_link_investigation=dashboard_link_investigation\n", "#                 )\n", "#             )\n", "            \n", "#         display_html(title(\"Most Impactful A/B Studies by Metric Change\", 4))              \n", "# #         display_html(\"<p> More info in Mercury dashboard: <a href=https://lk-mercury.sc-corp.net/looks/67>go/mis</a></p>\")\n", "        \n", "#         if source_table_filter or user_cohorts_filter:\n", "#             display_html(text(\"This section does not support source table filters or user cohorts filters. It shows results when no source table / user cohorts filters applies\"))\n", "#         if metric_source == \"bigquery\": \n", "#             display_html(text(\"This report supports A/B metric data from Quest only. For the metric in the BQ table, the report tried mapping metric in BQ the table with the minor metric name in Quest.\"))  \n", "#         if len(df_ab_impacted) == 0:\n", "#             display_html(text(\"No impactful A/B studies by metric change are found.\"))\n", "#         else:\n", "#             display_html(text(ab_mis_date_range_explanation))\n", "#             display_html(\n", "#                 \"<p>The A/B treaments are sorted using the absolute value of <a href='https://wiki.sc-corp.net/pages/viewpage.action?pageId=110194189', target='__blank'> realized impact </a> in descending order.</p>\"\n", "#             )\n", "#             display(df_ab_impacted.style)\n", "               \n", "#     else:\n", "#         display_html(\"<p>A/B impact skipped when the input data source is BigQuery. As a todo, we will allow you to investigate a DUC metric and its A/B impact in one report.</p>\")\n", "            \n", "#     # TODO: A/B Study Traffic Change Impact (doc)\n", "#     # display_html(title(\"A/B Study Traffic Change Impact\", 4))\n", "    \n", "# #     display_html(title(\"App releases\", 4))\n", "# #     display_html(\"<p><a href=https://snapchat-qa-dashboard.gae.sc-corp.net/android>Release Dashboard</a><br>\")\n", "# #     display_html(\"<hr>\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "31b21437", "metadata": {}, "outputs": [], "source": ["#     display_html(title(\"App releases\", 4))\n", "#     display_html(\"https://calendar.google.com/calendar/u/0/embed?src=<EMAIL>&ctz=America/Los_Angeles>Release Timeline</a><br>\")\n", "#     display_html(\"<hr>\")"]}, {"cell_type": "code", "execution_count": null, "id": "08b8e048", "metadata": {}, "outputs": [], "source": ["# df_ab_impacted_P90"]}, {"cell_type": "code", "execution_count": null, "id": "0550eec9", "metadata": {}, "outputs": [], "source": ["# display_html(text(ab_mis_date_range_explanation))\n", "# display_html(\n", "#                 \"<p>The A/B treaments are sorted using the absolute value of <a href='https://wiki.sc-corp.net/pages/viewpage.action?pageId=110194189', target='__blank'> realized impact </a> in descending order.</p>\"\n", "#             )\n", "# # display(df_ab_impacted_P50.style)\n", "# display(df_ab_impacted_P90.style)"]}, {"cell_type": "code", "execution_count": null, "id": "f323cc1a", "metadata": {}, "outputs": [], "source": ["# display_html(text(ab_mis_date_range_explanation))\n", "# display_html(\n", "#                 \"<p>The A/B treaments are sorted using the absolute value of <a href='https://wiki.sc-corp.net/pages/viewpage.action?pageId=110194189', target='__blank'> realized impact </a> in descending order.</p>\"\n", "#             )\n", "# display(df_ab_impacted.style)"]}, {"cell_type": "code", "execution_count": null, "id": "d04c016c", "metadata": {}, "outputs": [], "source": ["# section 4\n", "if \"4\" in sections:\n", "    display_html(title(\"Data Quality Check (To be replaced by ALS report)\", 2))\n", "    display_html(\"<p>Fortnight Looker Dashboard:  <a href=https://lk-search.sc-corp.net/dashboards/433>go/fortnight</a><br>\")\n", "    display_html(\"<hr>\")\n", "\n", "# section 5\n", "if \"5\" in sections:\n", "    display_html(title(\"Key Dimension Breakdown\", 2))\n", "    display_html(text(\"For each of the subgroups to analyze, we summarize their impact using these statistics.\"))\n", "    display_html(text(\"Statistics:\"))\n", "    if metric_type == \"sum\": \n", "        display_html(\"\"\"\n", "        <ul>\n", "            <li>Relative difference total: The relative week-over-week difference overall.</li>\n", "            <li>Absolute difference total: The absolute week-over-week difference overall.</li>\n", "            <li>Relative difference: The relative week-over-week difference in this subgroup.</li>\n", "            <li>Relative difference rest:  The relative week-over-week difference outside of this subgroup.</li>\n", "            <li>Propotion of total: The proportion of metric value in this subgroup relative to overall.</li>\n", "            <li> <strong> Importance of Impact (fka Divergence) </strong>: The \"contribution\" this groups has on the cumulative metric value.\n", "            It's defined as the difference between this group's actual metric value and the expected value if this subgroup behave like overall population.\n", "            <ul>\n", "                <li> Group with large negative (when good_direction = up) or large positive (when good_direction = down) numbers in divergence make large contributions to the metric and have worse changes than the rest of the population.</li>\n", "                <li> Mathematically, it's \"metric value in the baseline period\" * (relative difference in the subgroup - relative difference in the overall population)</li>\n", "            </ul>\n", "        </ul>\n", "        \"\"\")\n", "    elif metric_type == \"quantile\": \n", "        display_html(\"\"\"\n", "        <ul>\n", "            <li>Event count proportion: The proportion of event count in this subgroup relative to overall.</li>\n", "            <li> <strong> Importance of Impact (different definition compared to sum metrics) </strong>: The \"contribution\" this group has.\n", "            <ul>\n", "                <li>The rationale of this statistic is to balance the importance of metric change diff% and the event count contributions.</li>\n", "                <li>Mathematically, for quantile metrics, Importance of Impact = Metric diff% *  Event Count period_2 </li>\n", "            </ul>\n", "        </ul>\n", "        \"\"\")\n", "    \n", "    # # section 5.1\n", "    if metric_type == \"sum\":\n", "        display_html(title(\"Top 3 most interesting dimensions\",3))\n", "        display_html(text(\"Top 3 most interesting dimensions: {}.\".format(', '.join(dimensions_top3))))\n", "        display_html(text(\"An interesting dimension is one that defines a subset (e.g. country=US) of users with the greatest divergence in the wrong direction.\"))\n", "    \n", "    # section 5.2\n", "    display_html(title(\"Importance of Impact Table\", 3))\n", "    display_html(text(\"The table is ranked by Importance of Impact descendingly. Statistics definitions can be found under 5.One Dimension Breakdown.\"))\n", "    \n", "    if metric_type == \"sum\": \n", "        draw_contribution_table(divergence_table, dimensions=[group_name], top_n=50, includes_plot=False, \n", "                            period_1=period_1, period_2=period_2, good_direction=good_direction)\n", "    elif metric_type == \"quantile\": \n", "        for os_type in df_reshaped_os_type:\n", "            display_html(title(\"Importance of Impact Table ({})\".format(os_type_format(os_type)), 4))\n", "            draw_contribution_table_quantile(\n", "                divergence_table=divergence_table[os_type], \n", "                dimensions=[group_name], \n", "                good_direction=good_direction,\n", "                top_n=10, \n", "                includes_plot=False, \n", "                period_1=period_1,\n", "                period_2=period_2\n", "                )\n", "        \n", "    # section 5.3\n", "    if metric_type == \"sum\": \n", "        display_html(title(\"Waterfall Charts\", 3))\n", "        # plot waterfall charts\n", "        df_cohort = contribution_by_cohort(\n", "            data=df_reshaped.loc[df_reshaped.share > -1, :].copy(),\n", "            divergence_table=divergence_table.copy(),\n", "            date_field='period',\n", "            dimension_fields=[\"dimension\",\"dimension_values\"],\n", "            target_field=metric_name,\n", "            good_direction=good_direction\n", "        )\n", "        \n", "    elif metric_type == \"quantile\": \n", "        display_html(title(\"Dimension Breakdowns\", 3))\n", "        \n", "        for breakdown in all_breakdown_list_no_os_type:\n", "            display_html(title(\"Dimension: {}\".format(breakdown), 4))\n", "            \n", "            for os_type in df_reshaped_os_type:\n", "                display_html(text(\"{}:\".format(os_type_format(os_type))))\n", "                draw_contribution_table_quantile(\n", "                        divergence_table=divergence_table_by_dimension_quantile[breakdown][os_type], \n", "                        dimensions=[group_name], \n", "                        good_direction=good_direction,\n", "                        top_n=10, \n", "                        includes_plot=False, \n", "                        period_1=period_1,\n", "                        period_2=period_2\n", "                        )\n", "                \n", "    display_html(\"<hr>\")"]}, {"cell_type": "code", "execution_count": null, "id": "a4af58ef", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6af01edb", "metadata": {}, "outputs": [], "source": ["## MIS Section: MIS Report Output"]}, {"cell_type": "code", "execution_count": null, "id": "f9b05041", "metadata": {}, "outputs": [], "source": ["# df_ab_impacted"]}, {"cell_type": "code", "execution_count": null, "id": "ee94008a", "metadata": {}, "outputs": [], "source": ["        display_html(title(\"Most Impactful A/B Studies by Metric Change\", 4))              \n", "#         display_html(\"<p> More info in Mercury dashboard: <a href=https://lk-mercury.sc-corp.net/looks/67>go/mis</a></p>\")\n", "        \n", "        if source_table_filter or user_cohorts_filter:\n", "            display_html(text(\"This section does not support source table filters or user cohorts filters. It shows results when no source table / user cohorts filters applies\"))\n", "        if metric_source == \"bigquery\": \n", "            display_html(text(\"This report supports A/B metric data from Quest only. For the metric in the BQ table, the report tried mapping metric in BQ the table with the minor metric name in Quest.\"))  \n", "        if len(df_ab_impacted) == 0:\n", "            display_html(text(\"No impactful A/B studies by metric change are found.\"))\n", "        else:\n", "            display_html(text(ab_mis_date_range_explanation))\n", "            display_html(\n", "                \"<p>The A/B treaments are sorted using the absolute value of <a href='https://wiki.sc-corp.net/pages/viewpage.action?pageId=110194189', target='__blank'> realized impact </a> in descending order.</p>\"\n", "            )\n", "            display(df_ab_impacted.style)\n", "               \n", "    else:\n", "        display_html(\"<p>A/B impact skipped when the input data source is BigQuery. As a todo, we will allow you to investigate a DUC metric and its A/B impact in one report.</p>\")\n", "      "]}, {"cell_type": "code", "execution_count": null, "id": "dc0151f4", "metadata": {}, "outputs": [], "source": ["# # section 4\n", "# if \"4\" in sections:\n", "#     display_html(title(\"Data Quality Check (To be replaced by ALS report)\", 2))\n", "#     display_html(\"<p>Fortnight Looker Dashboard:  <a href=https://lk-search.sc-corp.net/dashboards/433>go/fortnight</a><br>\")\n", "#     display_html(\"<hr>\")\n", "\n", "# # section 5\n", "# if \"5\" in sections:\n", "#     display_html(title(\"Key Dimension Breakdown\", 2))\n", "#     display_html(text(\"For each of the subgroups to analyze, we summarize their impact using these statistics.\"))\n", "#     display_html(text(\"Statistics:\"))\n", "#     if metric_type == \"sum\": \n", "#         display_html(\"\"\"\n", "#         <ul>\n", "#             <li>Relative difference total: The relative week-over-week difference overall.</li>\n", "#             <li>Absolute difference total: The absolute week-over-week difference overall.</li>\n", "#             <li>Relative difference: The relative week-over-week difference in this subgroup.</li>\n", "#             <li>Relative difference rest:  The relative week-over-week difference outside of this subgroup.</li>\n", "#             <li>Propotion of total: The proportion of metric value in this subgroup relative to overall.</li>\n", "#             <li> <strong> Importance of Impact (fka Divergence) </strong>: The \"contribution\" this groups has on the cumulative metric value.\n", "#             It's defined as the difference between this group's actual metric value and the expected value if this subgroup behave like overall population.\n", "#             <ul>\n", "#                 <li> Group with large negative (when good_direction = up) or large positive (when good_direction = down) numbers in divergence make large contributions to the metric and have worse changes than the rest of the population.</li>\n", "#                 <li> Mathematically, it's \"metric value in the baseline period\" * (relative difference in the subgroup - relative difference in the overall population)</li>\n", "#             </ul>\n", "#         </ul>\n", "#         \"\"\")\n", "#     elif metric_type == \"quantile\": \n", "#         display_html(\"\"\"\n", "#         <ul>\n", "#             <li>Event count proportion: The proportion of event count in this subgroup relative to overall.</li>\n", "#             <li> <strong> Importance of Impact (different definition compared to sum metrics) </strong>: The \"contribution\" this group has.\n", "#             <ul>\n", "#                 <li>The rationale of this statistic is to balance the importance of metric change diff% and the event count contributions.</li>\n", "#                 <li>Mathematically, for quantile metrics, Importance of Impact = Metric diff% *  Event Count period_2 </li>\n", "#             </ul>\n", "#         </ul>\n", "#         \"\"\")\n", "    \n"]}, {"cell_type": "code", "execution_count": null, "id": "0feb9b93", "metadata": {}, "outputs": [], "source": ["ab_mis_sql_p50_table = read_gbq(ab_mis_sql_p50)\n", "ab_mis_sql_p90_table = read_gbq(ab_mis_sql_p90)"]}, {"cell_type": "code", "execution_count": null, "id": "ef6bd1ae", "metadata": {}, "outputs": [], "source": ["### 1) MIS: Launched AB with impact to P50 Metric within the investigation period"]}, {"cell_type": "code", "execution_count": null, "id": "a84bc765", "metadata": {}, "outputs": [], "source": ["display_html(title(\"1) MIS: Launched AB with impact to P50 Metric within the investigation period\", 4))\n", "# display_html(\"<p>Fortnight Looker Dashboard:  <a href=https://lk-search.sc-corp.net/dashboards/433>go/fortnight</a><br>\")\n", "display(ab_mis_sql_p50_table)\n", "display_html(\"<hr>\")"]}, {"cell_type": "code", "execution_count": null, "id": "dc930c4b", "metadata": {}, "outputs": [], "source": ["ab_mis_sql_p50_table"]}, {"cell_type": "code", "execution_count": null, "id": "05fc6f18", "metadata": {}, "outputs": [], "source": ["### 2) MIS: Launched AB with impact to P90 Metric within the investigation period"]}, {"cell_type": "code", "execution_count": null, "id": "cc530f6c", "metadata": {}, "outputs": [], "source": ["display_html(title(\"2) MIS: Launched AB with impact to P90 Metric within the investigation period\", 4))\n", "# display_html(\"<p>Fortnight Looker Dashboard:  <a href=https://lk-search.sc-corp.net/dashboards/433>go/fortnight</a><br>\")\n", "display_html(\"<hr>\")"]}, {"cell_type": "code", "execution_count": null, "id": "28d79431", "metadata": {}, "outputs": [], "source": ["display(ab_mis_sql_p90_table)"]}, {"cell_type": "code", "execution_count": null, "id": "a1a79060", "metadata": {}, "outputs": [], "source": ["display_html(title(\"App releases\", 4))\n", "display_html(\"https://calendar.google.com/calendar/u/0/embed?src=<EMAIL>&ctz=America/Los_Angeles>Release Timeline</a><br>\")\n", "display_html(\"<hr>\")  "]}, {"cell_type": "code", "execution_count": null, "id": "5d62434e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "97bf5074", "metadata": {}, "outputs": [], "source": ["#     print(\"Raw cohort:\")\n", "#     print(data1)\n", "#     print(\"Fixed cohort:\")\n", "#     print(data2)\n", "#     # plot raw cohort\n", "#     plt.subplots(1, 2, sharey=\"row\", figsize=(18, 8))\n", "\n", "#     plt.subplot(1, 2, 1)\n", "# #     start = data1[data1[\"event_date\"] == date_start]\n", "# #     end = data1[data1[\"event_date\"] == date_end]\n", "#     start = data1[data1[\"event_date\"] == 'start_period']\n", "#     end = data1[data1[\"event_date\"] == 'end_period']\n", "    \n", "#     plt.title(\"Raw Cohort change between {}-{} and {}-{}\".format(period_1_start_date, period_1_end_date, period_2_start_date, period_2_end_date))\n", "#     plt.bar([\"p10\", \"p50\", \"p90\"], [(end[\"p10\"].iloc[0] / start[\"p10\"].iloc[0] - 1) * 100, \\\n", "#                                     (end[\"p50\"].iloc[0] / start[\"p50\"].iloc[0] - 1) * 100, \\\n", "#                                     (end[\"p90\"].iloc[0] / start[\"p90\"].iloc[0] - 1) * 100])\n", "#     plt.ylabel(\"percent change (%)\")\n", "    \n", "#     # plot fixed cohort\n", "#     plt.subplot(1, 2, 2) \n", "#     start = data2[data2[\"event_date\"] == 'start_period']\n", "#     end = data2[data2[\"event_date\"] == 'end_period']\n", "#     plt.title(\"Fixed Cohort change between {}-{} and {}-{}\".format(period_1_start_date, period_1_end_date, period_2_start_date, period_2_end_date))\n", "#     plt.bar([\"p10\", \"p50\", \"p90\"], [(end[\"p10\"].iloc[0] / start[\"p10\"].iloc[0] - 1) * 100, \\\n", "#                                     (end[\"p50\"].iloc[0] / start[\"p50\"].iloc[0] - 1) * 100, \\\n", "#                                     (end[\"p90\"].iloc[0] / start[\"p90\"].iloc[0] - 1) * 100], color=\"orange\")\n", "#     plt.ylabel(\"percent change (%)\")\n", "    \n", "#     plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "c7b184ff", "metadata": {}, "outputs": [], "source": ["display_html(title(\"Metric Movement with Fixed cohort Controled\", 2))"]}, {"cell_type": "code", "execution_count": null, "id": "25d100a3", "metadata": {}, "outputs": [], "source": ["if len(breakdown_features) == 0:\n", "    data1 = read_gbq(get_query(False))\n", "    data2 = read_gbq(get_query(True))    \n", "    print(\"Raw cohort:\")\n", "    print(data1)\n", "    print(\"Fixed cohort:\")\n", "    print(data2)\n", "    # plot raw cohort\n", "    plt.subplots(1, 2, sharey=\"row\", figsize=(18, 8))\n", "\n", "    plt.subplot(1, 2, 1)\n", "#     start = data1[data1[\"event_date\"] == date_start]\n", "#     end = data1[data1[\"event_date\"] == date_end]\n", "    start = data1[data1[\"event_date\"] == 'start_period']\n", "    end = data1[data1[\"event_date\"] == 'end_period']\n", "    \n", "    plt.title(\"Raw Cohort change between {} and {}\".format(period_1_start_date, period_2_end_date))\n", "    plt.bar([\"p10\", \"p50\", \"p90\"], [(end[\"p10\"].iloc[0] / start[\"p10\"].iloc[0] - 1) * 100, \\\n", "                                    (end[\"p50\"].iloc[0] / start[\"p50\"].iloc[0] - 1) * 100, \\\n", "                                    (end[\"p90\"].iloc[0] / start[\"p90\"].iloc[0] - 1) * 100])\n", "    plt.ylabel(\"percent change (%)\")\n", "    \n", "    # plot fixed cohort\n", "    plt.subplot(1, 2, 2) \n", "    start = data2[data2[\"event_date\"] == 'start_period']\n", "    end = data2[data2[\"event_date\"] == 'end_period']\n", "    plt.title(\"Fixed Cohort change between {} and {}\".format(period_1_start_date, period_2_end_date))\n", "    plt.bar([\"p10\", \"p50\", \"p90\"], [(end[\"p10\"].iloc[0] / start[\"p10\"].iloc[0] - 1) * 100, \\\n", "                                    (end[\"p50\"].iloc[0] / start[\"p50\"].iloc[0] - 1) * 100, \\\n", "                                    (end[\"p90\"].iloc[0] / start[\"p90\"].iloc[0] - 1) * 100], color=\"orange\")\n", "    plt.ylabel(\"percent change (%)\")\n", "    \n", "    plt.show()\n", "    \n", "# else:\n", "#     for f in breakdown_features:\n", "#         print(\"\\nBreakdown by {}\\n\".format(f))\n", "#         data1 = read_gbq(get_query(False))\n", "#         data2 = read_gbq(get_query(True))    \n", "# #         data1 = pd.read_gbq(get_query(False).format(breakdown_features=f+\",\", groupby_2=\", \"+f), \\\n", "# #                             project_id = \"sc-bq-gcs-billingonly\", dialect = \"standard\")\n", "# #         data2 = pd.read_gbq(get_query(True).format(breakdown_features=f+\",\", groupby_2=\", \"+f), \\\n", "# #                             project_id = \"sc-bq-gcs-billingonly\", dialect = \"standard\")\n", "#         print(\"Raw cohort:\")\n", "#         print(data1)\n", "#         print()\n", "#         print(\"Fixed cohort:\")\n", "#         print(data2)\n", "# #         categories = list(map(str, set.union(set(data1[f].unique()), set(data2[f].unique()))))\n", "#         categories = ['p10', 'p50', 'p90']\n", "#         plt.subplots(2, 2, sharey=\"row\", figsize=(18, 16))\n", "        \n", "#         # plot raw cohort\n", "#         change_p50 = []\n", "#         change_p90 = []\n", "        \n", "#         for c in categories:\n", "#             start = data1[(data1[f] == c) & (data1[\"event_date\"] == date_start)]\n", "#             end = data1[(data1[f] == c) & (data1[\"event_date\"] == date_end)]\n", "#             if (len(start) == 0) or (len(end) == 0):\n", "#                 change_p50.append(0)\n", "#                 change_p90.append(0)\n", "#             else:\n", "#                 change_p50.append((end[\"p50\"].iloc[0] / start[\"p50\"].iloc[0] - 1) * 100)\n", "#                 change_p90.append((end[\"p90\"].iloc[0] / start[\"p90\"].iloc[0] - 1) * 100) \n", "        \n", "#         plt.subplot(2, 2, 1)    \n", "#         plt.title(\"Raw Cohort change between {} and {} (p50)\".format(date_start, date_end))\n", "#         plt.bar(categories, change_p50)\n", "#         plt.xlabel(f)\n", "#         plt.xticks(rotation=90)\n", "#         plt.ylabel(\"percent change p50 (%)\")\n", "\n", "#         plt.subplot(2, 2, 3)    \n", "#         plt.title(\"Raw Cohort change between {} and {} (p90)\".format(date_start, date_end))\n", "#         plt.bar(categories, change_p90)\n", "#         plt.xlabel(f)\n", "#         plt.xticks(rotation=90)\n", "#         plt.ylabel(\"percent change p90 (%)\")\n", "\n", "#         # plot fixed cohort\n", "#         change_p50 = []\n", "#         change_p90 = []\n", "#         for c in categories:\n", "#             start = data2[(data2[f] == c) & (data2[\"event_date\"] == date_start)]\n", "#             end = data2[(data2[f] == c) & (data2[\"event_date\"] == date_end)]\n", "#             if (len(start) == 0) or (len(end) == 0):\n", "#                 change_p50.append(0)\n", "#                 change_p90.append(0)\n", "#             else:\n", "#                 change_p50.append((end[\"p50\"].iloc[0] / start[\"p50\"].iloc[0] - 1) * 100)\n", "#                 change_p90.append((end[\"p90\"].iloc[0] / start[\"p90\"].iloc[0] - 1) * 100) \n", "\n", "#         plt.subplot(2, 2, 2)    \n", "#         plt.title(\"Fixed Cohort change between {} and {} (p50)\".format(date_start, date_end))\n", "#         plt.bar(categories, change_p50, color=\"orange\")\n", "#         plt.xlabel(f)\n", "#         plt.xticks(rotation=90)\n", "#         plt.ylabel(\"percent change p50 (%)\")\n", "\n", "#         plt.subplot(2, 2, 4)    \n", "#         plt.title(\"Fixed Cohort change between {} and {} (p90)\".format(date_start, date_end))\n", "#         plt.bar(categories, change_p90, color=\"orange\")\n", "#         plt.xlabel(f)\n", "#         plt.xticks(rotation=90)\n", "#         plt.ylabel(\"percent change p90 (%)\")\n", "\n", "#         plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "id": "cf833ca0", "metadata": {}, "outputs": [], "source": ["# # section 6\n", "# if \"6\" in sections:\n", "#     display_html(title(\"Two Dimension Breakdown\", 2))\n", "#     display_html(text(\"The table is ranked by Importance of Impact descendingly. Statistics definitions can be found under 5.One Dimension Breakdown.\"))\n", "#     if metric_type == \"sum\": \n", "#         draw_contribution_table(divergence_table_2, dimensions=[group_name], top_n=50,\n", "#                             period_1=period_1, period_2=period_2, good_direction=good_direction)\n", "        \n", "#     elif metric_type == \"quantile\" and len(all_breakdown_list) < 10: \n", "#         for os_type in df_reshaped_os_type:\n", "#             display_html(title(\"Two Dimension Breakdown ({})\".format(os_type_format(os_type)), 4))\n", "#             draw_contribution_table_quantile(\n", "#                 divergence_table=divergence_table_2_dim[os_type], \n", "#                 dimensions=[group_name], \n", "#                 good_direction=good_direction,\n", "#                 top_n=10, \n", "#                 includes_plot=False, \n", "#                 period_1=period_1,\n", "#                 period_2=period_2\n", "#                 )\n", "#     elif metric_type == \"quantile\" and len(all_breakdown_list) >= 10: \n", "#         display_html(text(\"Number of dimensions are too large (>=10) for conducting 2 dimensions breakdowns.\"))\n", "#         display_html(text(\"If 2 dimension breakdowns are necessary, please choose <10 dimensions, and regenerate the Lego Report.\"))\n", "        \n", "#     display_html(\"<hr>\")"]}, {"cell_type": "code", "execution_count": null, "id": "9c730c77", "metadata": {}, "outputs": [], "source": ["# section 7\n", "if \"7\" in sections:\n", "    display_html(title(\"Growth Section\", 2))\n", "    if period_1_timedelta != period_2_timedelta:\n", "        display_html(text(\"<strong>Warning</strong>: The number of days in the investigation period and the baseline period are not equal.\"))\n", "        display_html(text(\n", "            \"This section is comparing the metric total/uu/etc between 2 periods, and the comparison is valid only when the numbers of days are equal.\"))\n", "    \n", "    else:\n", "        # section 7.1\n", "        if metric_type == \"sum\": \n", "            display_html(title(\"Metric adoption / frequency / depth\", 3))\n", "            display_html(text(\"This section shows adoption / frequency / depth of the metric, and their multiplication is equal to the metric total in the periods.\"))\n", "            display(df_steps_final.style.applymap(\n", "                    lambda x: background_color_df(x, good_direction=good_direction, thresholds=[0.03,0.01,-0.03,-0.01]),\n", "                    subset=['diff%']\n", "                ))\n", "    \n", "        # section 7.2\n", "        display_html(title(\"Check the population shift between user engagement levels\", 3))\n", "        display_html(text(\"This section is to check if the metric movement is due to the population shift between different user engagement levels, which are evaluated using the {stat} of the investigated metric ({metric_name}) during baseline period.\".format(stat='total' if metric_type == \"sum\" else 'median', metric_name=metric_name)))\n", "        display_html(text(\"Users are divided into four groups using three thresholds, which are 10th percentile (p10), 50th percentile (p50), and 90th percentile (p90) of all users’ {stat} {metric_name}.\".format(stat='total' if metric_type == \"sum\" else 'median', metric_name=metric_name)))\n", "        for os_type_formatted in os_type_format_list:\n", "            if metric_type == \"quantile\":\n", "                display_html(text(\"{}:\".format(os_type_formatted)))\n", "            display_html(\"\"\"\n", "            <ul>\n", "                <li>{user_group_name[3]}: Users with baseline period {stat} {metric_name}> p90 ({p90}). </li>\n", "                <li>{user_group_name[2]}: Users with baseline period {stat} {metric_name} between p50 ({p50}) and p90 ({p90}). </li>\n", "                <li>{user_group_name[1]}: Users with baseline period {stat} {metric_name} between p10 ({p10}) and p50 ({p50}). </li>\n", "                <li>{user_group_name[0]}: Users with baseline period {stat} {metric_name}< p10 ({p10}). </li>\n", "            </ul>\n", "            \"\"\".format(metric_name = metric_name,\n", "                       p10=round(float(sql_metric_baseline_period_threshold_dict[os_type_formatted]['p10']), 1),\n", "                       p50=round(float(sql_metric_baseline_period_threshold_dict[os_type_formatted]['p50']), 1),\n", "                       p90=round(float(sql_metric_baseline_period_threshold_dict[os_type_formatted]['p90']), 1),\n", "                       stat='total' if metric_type == \"sum\" else 'median',\n", "                       user_group_name=user_group_name\n", "                      )\n", "            )\n", "            display(sql_growth_final_dict[os_type_formatted].style.applymap(\n", "                    lambda x: background_color_df(x, good_direction=good_direction, thresholds=[0.03,0.01,-0.03,-0.01]),\n", "                    subset=[(column, 'diff%') for column in column_order_growth]\n", "                ))\n", "        display_html(\"<hr>\")"]}, {"cell_type": "code", "execution_count": null, "id": "9b46e278", "metadata": {}, "outputs": [], "source": ["# section 8\n", "if \"8\" in sections:\n", "    display_html(title(\"Core Metrics and Related Metrics Overview\", 2))\n", "    \n", "    if show_related_metrics_overview:\n", "        # section 8.1\n", "        display_html(title(\"Core Metrics Overview\", 3))\n", "        display_html(text(\"Note: In this section, user_cohorts table filter will be applied to core metrics calculation, while source table filters will NOT.\"))\n", "        display_html(text(\"This section shows the core engagement and performance metrics comparison between the baseline period and the investigation period. \\\n", "                          It gives users an overview of the app healthiness, and users can spot potential effects from upper funnel metrics.\"))\n", "        display(related_metric_compare(metrics = \"key_metrics\"))\n", "        \n", "        # section 8.2\n", "        display_html(title(\"Highly Correlated Metrics Overview\", 3))\n", "        display_html(text(\"Reference: <a href=https://docs.google.com/document/d/1ouftDkm9RChhCJImQSK7g5drCa_VAB0RuDxw0QQiDPY/edit#heading=h.f04nxgab7em4> KPI Correlation doc</a>. \"))\n", "        \n", "        # skip this correlation application when there are filters, for time and resouces concerns\n", "        if source_table_filter == '' and user_cohorts_filter == '':\n", "            if len(high_corr_metrics) > 0 or related_metric_compare(metrics = \"high_corr_metrics\") is not None:\n", "                if len(high_corr_metrics) > n_high_corr_metrics:\n", "                    display_html(text(\"* Only shows top {} highly correlated metrics.\".format(\n", "                        n_high_corr_metrics))\n", "                    )\n", "                display_html(text(\n", "                    \"\"\"\n", "                    We calculated the correlations between a curated metric list and the investigated metric, \\\n", "                    and find the highly correlated metric list (corr_threshold = {}). For highly correlated metrics, we compare them between\\\n", "                    the baseline period and the investigation period, and check if they are moving with the investigated metric. \n", "                    <ul>\n", "                    <li>If the highly correlated metrics changed in the same direction as the investigated metric, we will suggest this could be:\n", "                        <ul>\n", "                        <li>feature usage in some user cohorts changed. </li>\n", "                        <li>feature ecosystem changed.</li>\n", "                        <li>events.</li>\n", "                        </ul>\n", "                    <li>If the highly correlated metrics didn’t change in the same direction as the investigated metric, we will suggest this could be:\n", "                        <ul>\n", "                        <li>data pipeline or logging issues.</li>\n", "                        <li>related to the specific metric definitions.</li>\n", "                        </ul>\n", "                    </ul>\n", "                    \"\"\".format(corr_threshold)\n", "                ))\n", "                \n", "                display(related_metric_compare(metrics = \"high_corr_metrics\"))\n", "                    \n", "            else:\n", "                display_html(text(\n", "                    \"We didn't find highly correlated metrics (corr_threshold = {}). \\\n", "                    The list of metrics we checked are defined \\\n", "                    in <a href=https://github.sc-corp.net/Snapchat/pyanalytics/blob/f448b826e1b06b770f6d06202164c54f5ecc2c0c/notebook/metric_investigation/correlation_application_lego_report.ipynb>this notebook</a>, \\\n", "                    including all core engagement / performance Quest AB metrics and key metrics by tab.\".format(\n", "                    corr_threshold)\n", "                ))\n", "    \n", "        else:\n", "            display_html(text(\"Highly Correlated Metrics Overview section is skipped when users apply filters for user_cohorts table or the source table, \\\n", "                for time and resources concerns.\"))\n", "    \n", "    else:\n", "        display_html(text(\"This section will be available when both the baseline period and the investigation period are in the recent 6 months,\\\n", "                          because core metrics and highly correlated metrics data are from Quest AB event tables with 6-month data retention. \"))\n", "        \n", "    display_html(\"<hr>\")"]}, {"cell_type": "code", "execution_count": null, "id": "53cfa323", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "21dc1647", "metadata": {"tags": []}, "outputs": [], "source": ["################ section below are WIP, and will be under 3.2 Internal Events #################"]}, {"cell_type": "code", "execution_count": null, "id": "88a666d8", "metadata": {}, "outputs": [], "source": ["# display_html(title(\"A/B Study Traffic Change Impact\",3))\n", "# display_html(text(\"Statistics:\"))\n", "# display_html(\"\"\"\n", "#     <li>treatment_traffic_pct_diff: Treatment traffic percentage difference between 7 days prior to the exploration date vs. the exploration date.</li>\n", "#     <li>realized_impact_per_day_pct_diff:  Actual metric impact percentage to global metric difference between 7 days prior to the exploration date vs. the exploration date.</li>\n", "#     <li>full_rollout_impact_per_day_pct_diff: Rollout impact percentage to global metric if rolled out to 100% of targeted users difference between 7 days prior to the exploration date vs. the exploration date.</li>\n", "#     <li>treatment_effect_pct_ds: Treatment effect percentage on the exploration date.</li>\n", "#     \"\"\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "805f2f6f", "metadata": {}, "outputs": [], "source": ["# ds_hyphen = pd.to_datetime(ds).strftime(\"%Y-%m-%d\")\n", "# ds_1d_after_hyphen = (pd.to_datetime(ds) + timedelta(days=1)).strftime(\"%Y-%m-%d\")\n", "# ds_6d_ago_hyphen = (pd.to_datetime(ds) - timedelta(days=6)).strftime(\"%Y-%m-%d\")\n", "# ds_7d_ago_hyphen = (pd.to_datetime(ds) - timedelta(days=7)).strftime(\"%Y-%m-%d\")"]}, {"cell_type": "code", "execution_count": null, "id": "1511add3", "metadata": {}, "outputs": [], "source": ["# sql_ab_impacted = \"\"\"\n", "# WITH study_name_treatment_name_scenario_1 AS (\n", "#     SELECT CONCAT(study_name, treatment_name) AS study_name_treatment_name\n", "#     FROM `sc-portal.ab_metric_impact.study_treatment_info_latest`  \n", "#     -- include the edge case that the study launched in the middle of the date 1 and the date 2\n", "#     WHERE serve_start_time > '{ds_7d_ago_hyphen}' AND serve_start_time < '{ds_1d_after_hyphen}' AND treatment_traffic_pct !=0\n", "#         AND serve_end_time >= '{ds_hyphen}'\n", "#         AND CONCAT(study_name, treatment_name) NOT IN (\n", "#             SELECT CONCAT(study_name, treatment_name)\n", "#             FROM `sc-portal.ab_metric_impact.study_treatment_info_latest`       \n", "#             WHERE serve_start_time <= '{ds_7d_ago_hyphen}'\n", "#                 AND serve_end_time >= '{ds_7d_ago_hyphen}' AND serve_end_time < '{ds_hyphen}'\n", "#         )\n", "# ),\n", "\n", "# study_name_treatment_name_scenario_2 AS (\n", "#     SELECT CONCAT(date1.study_name, date1.treatment_name) AS study_name_treatment_name\n", "#     FROM (\n", "#         SELECT *\n", "#         FROM `sc-portal.ab_metric_impact.study_treatment_info_latest`\n", "#         WHERE serve_start_time < '{ds_6d_ago_hyphen}' AND serve_end_time >= '{ds_7d_ago_hyphen}' \n", "#     ) date1 JOIN (\n", "#         SELECT *\n", "#         FROM `sc-portal.ab_metric_impact.study_treatment_info_latest`\n", "#         WHERE  serve_start_time < '{ds_1d_after_hyphen}' AND (serve_end_time >= '{ds_hyphen}' OR \n", "#             serve_end_time IS NULL -- QW TODO: confirm if this means test is not ended\n", "#         ) \n", "#     ) date2\n", "#     ON date1.study_name = date2.study_name AND date1.treatment_name = date2.treatment_name\n", "#         AND date1.treatment_traffic_pct != date2.treatment_traffic_pct\n", "# ),\n", "\n", "# study_name_treatment_name_scenario_3_step1 AS (\n", "#     SELECT study_name, treatment_name, treatment_traffic_pct\n", "#     FROM `sc-portal.ab_metric_impact.study_treatment_info_latest`  \n", "#     -- include the edge case that the study launched in the middle of the date 1 and the date 2\n", "#     WHERE serve_start_time <= '{ds_7d_ago_hyphen}'\n", "#         AND serve_end_time >= '{ds_7d_ago_hyphen}' AND serve_end_time < '{ds_hyphen}'\n", "#         AND CONCAT(study_name, treatment_name) NOT IN (\n", "#             SELECT CONCAT(study_name, treatment_name) \n", "#             FROM `sc-portal.ab_metric_impact.study_treatment_info_latest`  \n", "#             WHERE serve_start_time > '{ds_7d_ago_hyphen}' AND serve_start_time < '{ds_1d_after_hyphen}' \n", "#                 AND serve_end_time >= '{ds_hyphen}'\n", "#         )\n", "#     ORDER BY study_name, treatment_name\n", "# ),\n", "\n", "# metric_impact AS (\n", "#     SELECT\n", "#         mis.mis_date as mis_date, # Date Filter\n", "#         study_info.namespace as namespace,\n", "#         # Study & Treatment Descrption\n", "#         IF(info.segment_id is null,\n", "#               mis.study_name,\n", "#               study_info.segmentation_parent_study_name)\n", "#           as study_name,\n", "#         cast(mis.study_version AS STRING) as study_version,\n", "#         info.segment_name as segment_name,\n", "#         info.segment_id as segment_id,\n", "#         info.segment_version as segment_version,\n", "#         cast(mis.treatment_id AS STRING) as treatment_id,\n", "#         treatment_name,\n", "#         treatment_description,\n", "#         treatment_traffic_pct/100 as treatment_traffic_pct,\n", "#         IF(info.segment_id is null,\n", "#               concat(\"https://ab.sc-corp.net/v2/experiment/\",mis.study_name,\"/\",CAST(mis.study_version AS STRING), \"?utm_source=metric_impact&utm_medium=lk-mercury\"),\n", "#               concat(\"https://ab.sc-corp.net/v2/experiment/\",study_info.segmentation_parent_study_name,\"?utm_source=metric_impact&utm_medium=lk-mercury\"))\n", "#           as ab_console_link,\n", "#         IF(info.segment_id is null,\n", "#               concat(\"https://ab.sc-corp.net/v2/analysis/\",mis.study_name,\"/\",CAST(mis.study_version AS STRING), \"?utm_source=metric_impact&utm_medium=lk-mercury\"),\n", "#               concat(\"https://ab.sc-corp.net/v2/analysis/\",study_info.segmentation_parent_study_name,\"?\", \"segmentId=\",CAST(info.segment_id AS STRING),\"&segmentVersion=\" , CAST(info.segment_version AS STRING), \"&utm_source=metric_impact&utm_medium=lk-mercury\"))\n", "#           as ab_dash_link,\n", "\n", "#         study_info.is_segmentation_study as is_segmentation_study,\n", "#         study_info.client_os as client_os,\n", "#         study_info.serve_platforms as serve_platforms,\n", "#         study_info.trigger_option as trigger_option,\n", "#         # Unique identifiers for meta-analysis\n", "#         concat(mis.mis_date,mis.study_name) as study_analysis_date,\n", "#         concat(mis.study_name, CAST(mis.study_version AS STRING)) as unique_study_id,\n", "#         concat(mis.study_name, CAST(mis.study_version AS STRING), CAST(mis.treatment_id AS STRING)) as unique_treatment_id,\n", "#         is_control,\n", "#         IF(info.segment_id is null, false, true) as is_cof_ab,\n", "#         # Metric Description\n", "#         metric_name as full_metric_name,\n", "#         REGEXP_EXTRACT(metric_name, r\"^(.*?)__\") as metric_major_class,\n", "#         REGEXP_EXTRACT(metric_name, r\".*__(.*)\") as metric_minor,\n", "#         metric_metadata.unnest_compass_group_ids as dash_group_ids,\n", "#         metric_metadata.dash_group_name as dash_group_name,\n", "#         IF(LENGTH(metric_metadata.dash_name) > 0, metric_metadata.dash_name, metric_name) AS metric_name,\n", "#         metric_metadata.description as metric_description,\n", "#         good_direction as metric_good_direction,\n", "#         # Decison Description\n", "#         decision AS launch_decision,\n", "#         launch_doc_link,\n", "#         PARSE_DATE('%Y-%m-%d', launch_start_date) AS launch_start_date,\n", "#         is_ui_change,\n", "#         mis.treatment_id = cast(launch_decision.launch_treatment_id as string) AS is_launched_treatment,\n", "#         # MIS Metrics\n", "#         avg_per_user,\n", "#         ci_lower_bound,\n", "#         ci_upper_bound,\n", "#         running_days,\n", "#         triggered_impact_pct,\n", "#         user_count,\n", "#         p_value,\n", "#         bh_p_value,\n", "#         full_rollout_impact_per_day,\n", "#         full_rollout_impact_per_day_pct,\n", "#         realized_impact_per_day,\n", "#         realized_impact_per_day_pct,\n", "#         # Meta-analysis metrics\n", "#         1 as cnt,\n", "#         IF(p_value <= 0.05, 1, 0) as pval_0p05_detected,\n", "#         IF(p_value <= 0.01, 1, 0) as pval_0p01_detected,\n", "#         IF(p_value <= 0.001, 1, 0) as pval_0p001_detected,\n", "#         IF(bh_p_value  <= 0.05, 1, 0) as bh_0p05_detected,\n", "#         IF(bh_p_value <= 0.1, 1, 0) as bh_0p1_detected,\n", "#         IF(bh_p_value<= 0.01, 1, 0) as bh_0p01_detected,\n", "#         IF(p_value <= 0.001 and bh_p_value <= 0.05 , 1, 0) as pval_0p001_bh_0p05_detected,\n", "#         IF(p_value> 0.001 and bh_p_value <= 0.05 , 1, 0) as pval_0p001_bh_0p05_disagree_bhdetected,\n", "#         IF(p_value <= 0.001 and bh_p_value > 0.05 , 1, 0) as pval_0p001_bh_0p05_disagree_pdetected,\n", "#         IF(p_value <= 0.05 and bh_p_value <= 0.05 , 1, 0) as pval_0p05_bh_0p05_detected,\n", "#         IF(p_value<= 0.1 and bh_p_value<= 0.05 , 1, 0) as pval_0p1_bh_0p05_detected,\n", "#         info.study_name info_study_name\n", "        \n", "#       FROM # RANKED MIS so that we get only the last impact report\n", "#       (\n", "#         SELECT\n", "#         metric_impact.mis_date as mis_date,\n", "#         metric_impact.study_name as study_name,\n", "#         metric_impact.study_version as study_version,\n", "#         metric_impact.treatment_id as treatment_id,\n", "#         metric_impact.metric_name as metric_name,\n", "#         coalesce(cond.avg_per_user, metric_impact.avg_per_user) as avg_per_user,\n", "#         coalesce(cond.ci_lower_bound, metric_impact.ci_lower_bound) as ci_lower_bound,\n", "#         coalesce(cond.ci_upper_bound, metric_impact.ci_upper_bound) as ci_upper_bound,\n", "#         coalesce(cond.running_days, metric_impact.running_days) as running_days,\n", "#         coalesce(cond.triggered_impact_pct, metric_impact.triggered_impact_pct) as triggered_impact_pct,\n", "#         coalesce(cond.user_count, metric_impact.user_count) as user_count,\n", "#         coalesce(cond.p_value, metric_impact.p_value) as p_value,\n", "#         coalesce(cond.bh_p_value, metric_impact.bh_p_value) as bh_p_value,\n", "#         coalesce(cond.full_rollout_impact_per_day, metric_impact.full_rollout_impact_per_day) as full_rollout_impact_per_day,\n", "#         coalesce(cond.full_rollout_impact_per_day_pct, metric_impact.full_rollout_impact_per_day_pct) as full_rollout_impact_per_day_pct,\n", "#         coalesce(cond.realized_impact_per_day, metric_impact.realized_impact_per_day) as realized_impact_per_day,\n", "#         coalesce(cond.realized_impact_per_day_pct, metric_impact.realized_impact_per_day_pct) as realized_impact_per_day_pct,\n", "#         RANK() OVER (PARTITION BY metric_impact.study_name, metric_impact.study_version ORDER BY metric_impact.running_days DESC)  as rank,\n", "#         FROM\n", "#         (SELECT\n", "#           TIMESTAMP(PARSE_DATE('%Y%m%d', CONCAT(\"2\",_TABLE_SUFFIX))) as mis_date,\n", "#           study_name,\n", "#         study_version,\n", "#         cast(treatment_id as string) as treatment_id,\n", "#         metric_name,\n", "#         avg_per_user,\n", "#         ci_lower_bound,\n", "#         ci_upper_bound,\n", "#         running_days,\n", "#         triggered_impact_pct,\n", "#         user_count,\n", "#         p_value,\n", "#         bh_p_value,\n", "#         full_rollout_impact_per_day,\n", "#         full_rollout_impact_per_day_pct,\n", "#         realized_impact_per_day,\n", "#         realized_impact_per_day_pct\n", "#        FROM `sc-portal.ab_metric_impact.metric_impact_2*`\n", "#         WHERE CONCAT(\"2\",_TABLE_SUFFIX)  BETWEEN '{ds_7d_ago}' AND '{ds}'\n", "#         ) metric_impact\n", "#        LEFT JOIN\n", "#         (SELECT\n", "#           TIMESTAMP(PARSE_DATE('%Y%m%d', CONCAT(\"2\",_TABLE_SUFFIX))) as mis_date,\n", "#           study_name,\n", "#         study_version,\n", "#         treatment_id,\n", "#         metric_name,\n", "#         treatment_mean as avg_per_user,\n", "#         absolute_conf_lb as ci_lower_bound,\n", "#         absolute_conf_ub as ci_upper_bound,\n", "#         days as running_days,\n", "#         pct_difference as triggered_impact_pct,\n", "#         t_n as user_count,\n", "#         p_value,\n", "#         bh_p_value,\n", "#         full_rollout_impact_per_day,\n", "#         full_rollout_impact_percent as full_rollout_impact_per_day_pct,\n", "#         realized_impact_per_day,\n", "#         realized_impact_percent as realized_impact_per_day_pct\n", "#        FROM `sc-portal.ab_metric_impact.cond_metric_impact_2*`\n", "#         WHERE CONCAT(\"2\",_TABLE_SUFFIX) BETWEEN '{ds_7d_ago}' AND '{ds}'\n", "#         ) cond\n", "#         on metric_impact.study_name = cond.study_name and metric_impact.study_version = cond.study_version and metric_impact.treatment_id = cond.treatment_id and metric_impact.metric_name = cond.metric_name and metric_impact.mis_date = cond.mis_date\n", "\n", "#       ) AS mis\n", "\n", "#       LEFT JOIN ( #  <PERSON><PERSON>adata on the date of MIS for metric descripitons\n", "#         SELECT\n", "#           TIMESTAMP(PARSE_DATE('%Y%m%d', CONCAT(\"2\",_TABLE_SUFFIX))) AS meta_date,\n", "#           *\n", "#         FROM `sc-portal.quest.metadata_2*` CROSS JOIN UNNEST(compass_group_ids) as unnest_compass_group_ids\n", "#         WHERE CONCAT(\"2\",_TABLE_SUFFIX)  BETWEEN '{ds_7d_ago}' AND '{ds}'\n", "#       ) AS metric_metadata\n", "#       ON metric_metadata.major_metric = REGEXP_EXTRACT(mis.metric_name, r\"^(.*?)__\") AND metric_metadata.minor_metric = REGEXP_EXTRACT(metric_name, r\".*__(.*)\") AND mis.mis_date = metric_metadata.meta_date\n", "#       LEFT JOIN # Latest Treatment info for treatment descripitons\n", "#       (\n", "#         SELECT\n", "#           *\n", "#         FROM `sc-portal.ab_metric_impact.study_treatment_info_latest`\n", "#       ) AS info\n", "#       ON mis.treatment_id = cast(info.treatment_id as string) AND mis.study_name = info.study_name\n", "#       JOIN (SELECT * FROM   `sc-portal.ab_metric_impact.study_info_latest`) study_info\n", "#                  ON mis.study_name = study_info.study_name AND mis.study_version = study_info.study_version\n", "#       LEFT JOIN (# Latest Launch Decision table\n", "#         SELECT\n", "#           *\n", "#         FROM\n", "#           `sc-portal.ab_metric_impact.launch_decision_latest`\n", "#         WHERE\n", "#           is_latest\n", "#       ) AS launch_decision\n", "#       ON mis.study_name = launch_decision.study_name AND mis.study_version = launch_decision.study_version\n", "#       WHERE rank = 1 # Filter out to latest MIS for each study/version\n", "# )\n", "\n", "        \n", "# SELECT \n", "#     CONCAT(LEFT(CAST(ROUND(realized_impact_per_day_pct_diff_tmp, 5)*100 AS STRING), 5), '%') AS realized_impact_per_day_pct_diff,\n", "#     * EXCEPT(realized_impact_per_day_pct_diff_tmp), \n", "# FROM (\n", "# -- scenario 1\n", "#     SELECT\n", "#         study_name,\n", "#         treatment_name,\n", "#         '0' AS treatment_traffic_pct_7d_prior,\n", "#         CONCAT(LEFT(CAST(ROUND(treatment_traffic_pct, 5)*100 AS STRING), 5), '%') AS treatment_traffic_pct_ds,\n", "#         CONCAT(LEFT(CAST(ROUND(treatment_traffic_pct, 5)*100 AS STRING), 5), '%') AS treatment_traffic_pct_diff,\n", "#         COALESCE(realized_impact_per_day_pct, 0) AS realized_impact_per_day_pct_diff_tmp,\n", "#         CONCAT(LEFT(CAST(ROUND(COALESCE(full_rollout_impact_per_day_pct, 0), 5)*100 AS STRING),5), '%') AS full_rollout_impact_per_day_pct_diff,\n", "#        -- LEFT(CAST(ROUND(p_value, 5)  AS STRING), 5) AS p_value,\n", "#         CONCAT(LEFT(CAST(ROUND(triggered_impact_pct, 5)*100 AS STRING), 5), '%') AS treatment_effect_pct_ds,\n", "#        -- launch_doc_link,\n", "#         MAX(user_count) AS user_count,\n", "#         ab_dash_link\n", "#     FROM metric_impact\n", "#     WHERE (study_name NOT LIKE '%DUM\\\\_%' AND study_name NOT LIKE '%CONSOLE\\\\_V2%' AND study_name NOT LIKE '%\\\\_QA\\\\_%' OR study_name IS NULL)\n", "#         AND (NOT COALESCE(is_control , FALSE)) AND (dash_group_ids LIKE '%ab/core\\\\_%')\n", "#         AND ((treatment_traffic_pct > 0 AND treatment_traffic_pct < 100))\n", "#         AND (avg_per_user > 0.005 OR avg_per_user  < -0.005)\n", "#         AND (bh_p_value  <= 0.05)\n", "#         AND REPLACE(LOWER(metric_name), ' ', '_') = '{metric_name}' \n", "#         AND mis_date = TIMESTAMP(PARSE_DATE('%Y%m%d', '{ds}'))\n", "#         AND CONCAT(info_study_name, treatment_name) IN (\n", "#             SELECT study_name_treatment_name\n", "#             FROM study_name_treatment_name_scenario_1\n", "#         )\n", "#     GROUP BY 1,2,3,4,5,6,7,8,10\n", "#     HAVING user_count > 10000 AND ABS(realized_impact_per_day_pct_diff_tmp) > 0.0001\n", "    \n", "#     UNION ALL  \n", "    \n", "# -- scenario 2    \n", "#     SELECT \n", "#         date1.study_name,\n", "#         date1.treatment_name,\n", "#         CONCAT(LEFT(CAST(ROUND(date1.treatment_traffic_pct, 5)*100 AS STRING), 5), '%') AS treatment_traffic_pct_7d_prior,\n", "#         CONCAT(LEFT(CAST(ROUND(date2.treatment_traffic_pct, 5)*100 AS STRING), 5), '%') AS treatment_traffic_pct_ds,\n", "#         CONCAT(LEFT(CAST(ROUND(date2.treatment_traffic_pct - date1.treatment_traffic_pct, 5)*100 AS STRING), 5), '%') AS treatment_traffic_pct_diff,\n", "#         COALESCE(date2.realized_impact_per_day_pct, 0) - COALESCE(date1.realized_impact_per_day_pct, 0) AS realized_impact_per_day_pct_diff_tmp,\n", "#         CONCAT(LEFT(CAST(ROUND(COALESCE(date2.full_rollout_impact_per_day_pct, 0) - COALESCE(date1.full_rollout_impact_per_day_pct, 0), 5)*100 AS STRING), 5), '%') AS full_rollout_impact_per_day_pct_diff,\n", "#         -- LEFT(CAST(ROUND(date2.p_value, 5) AS STRING), 5) AS p_value,\n", "#         CONCAT(LEFT(CAST(ROUND(date2.triggered_impact_pct, 5)*100 AS STRING), 5), '%') AS treatment_effect_pct_ds,\n", "#         --date2.launch_doc_link,\n", "#         MAX(date2.user_count) AS user_count,\n", "#         date2.ab_dash_link\n", "#     FROM (\n", "#         SELECT *\n", "#         FROM metric_impact\n", "#         WHERE (study_name NOT LIKE '%DUM\\\\_%' AND study_name NOT LIKE '%CONSOLE\\\\_V2%' AND study_name NOT LIKE '%\\\\_QA\\\\_%' OR study_name IS NULL) \n", "#             AND (NOT COALESCE(is_control , FALSE)) AND (dash_group_ids LIKE '%ab/core\\\\_%') \n", "#             AND (avg_per_user > 0.005 OR avg_per_user < -0.005) \n", "#             AND (bh_p_value <= 0.05)\n", "#             AND REPLACE(LOWER(metric_name), ' ', '_') = '{metric_name}' \n", "#             AND mis_date = TIMESTAMP(PARSE_DATE('%Y%m%d', '{ds_7d_ago}'))\n", "#             AND CONCAT(info_study_name, treatment_name) IN (\n", "#                 SELECT study_name_treatment_name\n", "#                 FROM study_name_treatment_name_scenario_2\n", "#             )\n", "#     ) date1 JOIN(\n", "#         SELECT *\n", "#         FROM metric_impact\n", "#         WHERE (study_name NOT LIKE '%DUM\\\\_%' AND study_name NOT LIKE '%CONSOLE\\\\_V2%' AND study_name NOT LIKE '%\\\\_QA\\\\_%' OR study_name IS NULL) \n", "#             AND (NOT COALESCE(is_control , FALSE)) AND (dash_group_ids LIKE '%ab/core\\\\_%') \n", "#             AND (avg_per_user  > 0.005 OR avg_per_user < -0.005) \n", "#             AND (bh_p_value <= 0.05)\n", "#             AND REPLACE(LOWER(metric_name), ' ', '_') = '{metric_name}' \n", "#             AND mis_date = TIMESTAMP(PARSE_DATE('%Y%m%d', '{ds}'))\n", "#             AND CONCAT(info_study_name, treatment_name) IN (\n", "#                 SELECT study_name_treatment_name\n", "#                 FROM study_name_treatment_name_scenario_2\n", "#             )\n", "#     ) date2\n", "#     ON date1.info_study_name = date2.info_study_name AND date1.treatment_name = date2.treatment_name\n", "#         AND date1.treatment_traffic_pct != date2.treatment_traffic_pct\n", "#         AND date1.metric_name = date2.metric_name\n", "#     GROUP BY 1,2,3,4,5,6,7,8,10\n", "#     HAVING user_count > 10000 AND ABS(realized_impact_per_day_pct_diff_tmp) > 0.0001\n", "    \n", "#     UNION ALL\n", " \n", "#  -- scenario 3\n", "#     SELECT \n", "#         metric_impact_launch.study_name, \n", "#         metric_impact_launch.treatment_name, \n", "#         CONCAT(LEFT(CAST(ROUND(metric_impact_date1.treatment_traffic_pct , 5)*100 AS STRING), 5), '%') AS treatment_traffic_pct_7d_prior,\n", "#         'launched' AS treatment_traffic_pct_ds,\n", "#         CONCAT(LEFT(CAST(ROUND(metric_impact_launch.treatment_traffic_pct - metric_impact_date1.treatment_traffic_pct, 5)*100 AS STRING), 5), '% (estimated)')  AS treatment_traffic_pct_diff,     \n", "#         COALESCE(metric_impact_launch.realized_impact_per_day_pct, 0) - COALESCE(metric_impact_date1.realized_impact_per_day_pct , 0) AS realized_impact_per_day_pct_diff_tmp,\n", "#         'NULL' AS full_rollout_impact_per_day_pct_diff,\n", "#         'NULL' AS treatment_effect_pct_ds,\n", "#         NULL AS user_count,\n", "#         metric_impact_launch.ab_dash_link\n", "#     FROM (\n", "#         SELECT *\n", "#         FROM metric_impact\n", "#         WHERE (study_name NOT LIKE '%DUM\\\\_%' AND study_name NOT LIKE '%CONSOLE\\\\_V2%' AND study_name NOT LIKE '%\\\\_QA\\\\_%' OR study_name IS NULL)\n", "#         AND (NOT COALESCE(is_control , FALSE)) AND (dash_group_ids LIKE '%ab/core\\\\_%')\n", "#         AND REPLACE(LOWER(metric_name), ' ', '_') = '{metric_name}' \n", "#         AND CONCAT(info_study_name, treatment_name) IN (\n", "#            SELECT CONCAT(study_name, treatment_name)\n", "#            FROM study_name_treatment_name_scenario_3_step1\n", "#        )\n", "#         AND launch_decision = 'Launch'\n", "#         AND is_launched_treatment is True\n", "#         AND launch_start_date BETWEEN '{ds_6d_ago_hyphen}' AND '{ds_hyphen}'    \n", "#     ) metric_impact_launch JOIN (\n", "#         SELECT *\n", "#         FROM metric_impact\n", "#         WHERE (study_name NOT LIKE '%DUM\\\\_%' AND study_name NOT LIKE '%CONSOLE\\\\_V2%' AND study_name NOT LIKE '%\\\\_QA\\\\_%' OR study_name IS NULL)\n", "#             AND (NOT COALESCE(is_control , FALSE)) AND (dash_group_ids LIKE '%ab/core\\\\_%')\n", "#             AND (avg_per_user  > 0.005 OR avg_per_user  < -0.005)\n", "#             AND (bh_p_value  <= 0.05)\n", "#             AND REPLACE(LOWER(metric_name), ' ', '_') = '{metric_name}'\n", "#         AND mis_date = TIMESTAMP(PARSE_DATE('%Y%m%d', '{ds_7d_ago}'))\n", "#     ) metric_impact_date1\n", "#     ON metric_impact_launch.info_study_name = metric_impact_date1.info_study_name\n", "#         AND metric_impact_launch.treatment_name = metric_impact_date1.treatment_name\n", "#         AND metric_impact_launch.treatment_traffic_pct != metric_impact_date1.treatment_traffic_pct\n", "#     GROUP BY 1,2,3,4,5,6,7,8,9,10\n", "#     HAVING ABS(realized_impact_per_day_pct_diff_tmp) > 0.0001\n", "    \n", "#     UNION ALL\n", "    \n", "# -- scenario 4\n", "#     SELECT \n", "#         study_name, \n", "#         treatment_name, \n", "#         CONCAT(LEFT(CAST(ROUND(treatment_traffic_pct , 5)*100 AS STRING), 5), '%') AS treatment_traffic_pct_7d_prior,\n", "#         '0' AS treatment_traffic_pct_ds,\n", "#         CONCAT(LEFT(CAST(ROUND( -treatment_traffic_pct, 5)*100 AS STRING), 5), '%')  AS treatment_traffic_pct_diff,     \n", "#         COALESCE( -realized_impact_per_day_pct , 0) AS realized_impact_per_day_pct_diff_tmp,\n", "#         CONCAT(LEFT(CAST(ROUND( -full_rollout_impact_per_day_pct, 5)*100 AS STRING), 5), '%') AS full_rollout_impact_per_day_pct_diff,\n", "#         'NULL' AS treatment_effect_pct_ds,\n", "#         NULL AS user_count,\n", "#         ab_dash_link\n", "#     FROM metric_impact\n", "#     WHERE (study_name NOT LIKE '%DUM\\\\_%' AND study_name NOT LIKE '%CONSOLE\\\\_V2%' AND study_name NOT LIKE '%\\\\_QA\\\\_%' OR study_name IS NULL)\n", "#         AND (NOT COALESCE(is_control , FALSE)) AND (dash_group_ids LIKE '%ab/core\\\\_%')\n", "#         AND (avg_per_user > 0.005 OR avg_per_user < -0.005) \n", "#         AND (bh_p_value <= 0.05)\n", "#         AND REPLACE(LOWER(metric_name), ' ', '_') = '{metric_name}' \n", "#         AND mis_date = TIMESTAMP(PARSE_DATE('%Y%m%d', '{ds}')) \n", "#         AND CONCAT(info_study_name, treatment_name) IN (  \n", "#             SELECT CONCAT(study_name, treatment_name)\n", "#             FROM study_name_treatment_name_scenario_3_step1\n", "#             WHERE treatment_traffic_pct !=0\n", "#         )\n", "#         AND CONCAT(info_study_name, treatment_name) NOT IN (  \n", "#             SELECT CONCAT(study_name, treatment_name)\n", "#             FROM `sc-portal.ab_metric_impact.study_treatment_info_latest` \n", "#             WHERE launch_decision = 'Launch'\n", "#                 AND is_launched_treatment is True\n", "#                 AND launch_start_date BETWEEN '{ds_6d_ago_hyphen}' AND '{ds_hyphen}'   \n", "#         ) \n", "#         AND user_count > 10000 AND ABS(realized_impact_per_day_pct) > 0.0001\n", "    \n", "# )\n", "# ORDER BY ABS(realized_impact_per_day_pct_diff_tmp) DESC\n", "# LIMIT 20\n", "# \"\"\".format(\n", "#         metric_name=metric_name,\n", "#         ds=ds,\n", "#         ds_7d_ago=ds_7d_ago,\n", "#         ds_hyphen=ds_hyphen,\n", "#         ds_1d_after_hyphen=ds_1d_after_hyphen,\n", "#         ds_6d_ago_hyphen=ds_6d_ago_hyphen,\n", "#         ds_7d_ago_hyphen=ds_7d_ago_hyphen\n", "#     ).replace('\\\\', '\\\\\\\\')\n", "\n", "# df_ab_impacted = read_gbq(sql_ab_impacted)\n", "\n", "# if len(df_ab_impacted)==0:\n", "#     print(\"No AB traffic change is found that coincides with the metric regression.\")\n", "# else:\n", "#     df_ab_impacted"]}, {"cell_type": "code", "execution_count": null, "id": "2c4d870d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2b401bc7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"celltoolbar": "Tags", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "toc-autonumbering": false, "toc-showcode": false, "toc-showmarkdowntxt": false, "toc-showtags": false}, "nbformat": 4, "nbformat_minor": 5}