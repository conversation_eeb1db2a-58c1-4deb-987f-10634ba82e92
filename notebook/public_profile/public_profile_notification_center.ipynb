{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {"ExecuteTime": {"end_time": "2025-04-22T20:36:56.445163Z", "start_time": "2025-04-22T20:36:56.433756Z"}}, "outputs": [{"data": {"text/html": ["<style>.output_html.rendered_html table { font-size:8pt;}</style>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# import files\n", "from __future__ import division, unicode_literals, print_function\n", "import collections\n", "import os\n", "import functools\n", "if any(['VELLUM' in k for k in os.environ.keys()]):\n", "    pip_output = !pip install banjo\n", "\n", "import seaborn as sns\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from math import pi\n", "import logging\n", "logging.basicConfig()\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "from google.cloud import bigquery\n", "\n", "%matplotlib inline\n", "%config InlineBackend.figure_format = 'retina'\n", "\n", "from banjo import abtest, utils # see: go/pya\n", "from banjo.abtest.cohort_report import CohortReport\n", "\n", "# will need project for general data science\n", "billing_project = 'sc-bq-gcs-billingonly'\n", "PROJECT = 'sc-bq-gcs-billingonly'\n", "sns.set(style='whitegrid', font_scale=1.5)\n", "\n", "from IPython.core.display import display, HTML\n", "display(HTML(\"<style>.output_html.rendered_html table { font-size:8pt;}</style>\"))\n", "\n", "client = bigquery.Client(project=billing_project)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["### User NC Engagement ####\n", "from banjo.teams.product.notification_center_metrics import (\n", "    notification_center_metrics, public_profile_source_of_view_metrics\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["##################\n", "## Notebook Config\n", "##################\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# ===================================================================\n", "# This cell is tagged as 'parameters' to accept inputs from the UI.\n", "# ===================================================================\n", "\n", "# 1. Core Study Configuration\n", "# These values are expected to be populated by the form's study/date inputs.\n", "STUDY_NAME = 'NOTIFICATION_CENTER_ON_CAMERA_AB__144041'\n", "STUDY_START_DATE = '20250603'\n", "STUDY_END_DATE = '20250604'\n", "COHORT_DATE = '20250604' # Usually defaults to STUDY_END_DATE\n", "CONTROL_ID = '2'\n", "TREATMENT_IDS = ['3']\n", "\n", "# 2. Metric <PERSON>\n", "# Populated from the multi-select dropdown in the UI.\n", "SELECTED_METRIC_LIST = ['notification_center_metrics','public_profile_source_of_view_metrics']\n", "\n", "# 3. A/A Test Configuration\n", "# Populated from the \"Compute Retro AA\" checkbox and date pickers in the UI.\n", "COMPUTE_RETRO_AA = False\n", "RETRO_START_DATE = '20250527'\n", "RETRO_END_DATE = '20250602'\n", "\n", "# 4. Breakdown & Execution Flags\n", "# These can be controlled by UI elements like dropdowns or checkboxes.\n", "USER_BREAKDOWN_LIST = ['gender']\n", "OVERWRITE = False\n", "PIVOT_RESULTS = True\n", "DAILY_TREND = True\n", "USE_BATCH_BQ_PRIORITY = False\n", "\n", "# -------------------------------------------------------------------\n", "# Static Configurations (These are not meant to be changed by the UI)\n", "# It's good practice to keep these separate from the dynamic parameters above.\n", "# -------------------------------------------------------------------\n", "EXP_NAMES = {}\n", "ANALYSIS_START_DATE = STUDY_START_DATE\n", "ab_console_metrics = []  # A/B Quest V2\n", "\n", "AB_CONSOLE_DEFAULT_METRICS = [\n", "     \"app_active_day_user\",\n", "     \"snap_direct_snap_create_user\",\n", "     \"app_app_session_time_user\",\n", "     \"snap_filter_lens_swipe_user\",\n", "     \"chat_chat_send_user\",\n", "     \"chat_chat_view_user\",\n", "     \"snap_direct_snap_send_user\",\n", "     \"snap_direct_snap_view_user\",\n", "     \"story_friend_story_snap_post\",\n", "     \"story_friend_story_snap_view\",\n", "     \"story_friend_story_story_view\",\n", "     \"maps_map_open_active_day_user\",\n", "     \"maps_map_open_user\"\n", "]\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["#################\n", "## Metrics Config\n", "#################"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# create list\n", "def ensure_list(parameter):\n", "    if isinstance(parameter, list):\n", "        return parameter\n", "    else:\n", "        return [parameter]\n", "\n", "TREATMENT_IDS = ensure_list(TREATMENT_IDS)\n", "\n", "ab_console_metrics = ensure_list(ab_console_metrics)   \n", "ab_console_metrics = [\n", "    metric for metric in AB_CONSOLE_DEFAULT_METRICS if metric not in ab_console_metrics\n", "] + ab_console_metrics\n", "\n", "ab_console_metrics = ensure_list(ab_console_metrics)\n", "USER_BREAKDOWN_LIST = ensure_list(USER_BREAKDOWN_LIST)\n", "SELECTED_METRIC_LIST = ensure_list(SELECTED_METRIC_LIST)\n", "\n", "if not COHORT_DATE:\n", "    COHORT_DATE = STUDY_START_DATE\n", "PIVOT_RESULTS = bool(PIVOT_RESULTS)\n", "BQ_PRIORITY = \"BATCH\" if USE_BATCH_BQ_PRIORITY else \"INTERACTIVE\"\n", "\n", "# decide if print daily trend\n", "daily_display = []\n", "if DAILY_TREND:\n", "    daily_display = ['trend']"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"ExecuteTime": {"end_time": "2025-04-22T20:36:57.469613Z", "start_time": "2025-04-22T20:36:57.466399Z"}}, "outputs": [], "source": ["# Other Metrics\n", "nc_engagement_metrics = (\n", "    [f(STUDY_START_DATE, STUDY_END_DATE)\n", "     for f in [notification_center_metrics\n", "              ]]\n", ")\n", "\n", "pp_source_metrics = (\n", "    [f(STUDY_START_DATE, STUDY_END_DATE)\n", "     for f in [public_profile_source_of_view_metrics\n", "              ]]\n", ")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"ExecuteTime": {"end_time": "2025-04-22T20:36:58.756349Z", "start_time": "2025-04-22T20:36:57.853656Z"}}, "outputs": [], "source": ["if 'snapchat_core' in SELECTED_METRIC_LIST:\n", "    # AB metrics section\n", "    ab_console_metrics_dict = abtest.ABQuestMetricCatalog(\n", "        STUDY_START_DATE, STUDY_END_DATE, bq_project=billing_project).get_catalog_dict()\n", "    # Keep only the metrics passed from the Husky UI\n", "    ab_console_metric_tables = [\n", "        metric_tables\n", "        for metric_name, metric_tables in ab_console_metrics_dict.items()\n", "        if metric_name in ab_console_metrics\n", "    ]\n", "else:\n", "    ab_console_metric_tables = []"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["##########\n", "## Add metrics\n", "#########"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["metric_tables = ab_console_metric_tables\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["# Only run selected metrics based on UI input\n", "\n", "metric_list_nc_engagement = [\n", "    nc_engagement_metrics\n", "]\n", "\n", "metric_list_pp_source = [\n", "    pp_source_metrics\n", "]\n", "\n", "selected_metric_dic = {\n", "    'notification_center_metrics': metric_list_nc_engagement,\n", "    'public_profile_source_of_view_metrics': metric_list_pp_source\n", "}\n", "\n", "metric_tables_list = [metric_tables]\n", "for selected_metric in selected_metric_dic:\n", "    if selected_metric in SELECTED_METRIC_LIST:\n", "        metric_tables_list.extend(selected_metric_dic[selected_metric])\n", "\n", "metric_tables = functools.reduce(lambda a, b: a+b, metric_tables_list)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["# set up A/A config\n", "if COMPUTE_RETRO_AA:\n", "    for mt in metric_tables:\n", "        mt.aa = True"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["###################\n", "## Configure Report\n", "###################"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# Construct the report object\n", "COHORT_DATE = STUDY_END_DATE\n", "report = CohortReport(\n", "    study_name=STUDY_NAME,\n", "    study_start_date=STUDY_START_DATE,\n", "    study_end_date=STUDY_END_DATE,\n", "    metric_tables=metric_tables,\n", "    quantiles=['50', '90'],    \n", "    control_id=CONTROL_ID,\n", "    treatment_ids=TREATMENT_IDS,\n", "    user_group_bys=USER_BREAKDOWN_LIST,\n", "    bq_client=bigquery.Client(billing_project),\n", "    bq_project='sc-bq-gcs-billingonly',\n", "    dest_dataset='temp_abtest',\n", "    overwrite_mapping_table=OVERWRITE,\n", "    exp_id_to_name=EXP_NAMES,    \n", "    bq_priority=BQ_PRIORITY,\n", "    aa_start_date=RETRO_START_DATE,\n", "    aa_end_date=RETRO_END_DATE,\n", "    bq_dialect='standard',\n", "    cohort_definition_date=COHORT_DATE\n", "\n", ")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n", "WARNING:banjo.abtest.analysis:Some rows are skipped due to small sample sizes\n"]}], "source": ["# Run the joins and calculate the results\n", "report.execute(\n", "    overwrite=OVERWRITE,\n", "    skip_export=True,\n", ")"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["report.configurations['pivot_table'] = PIVOT_RESULTS\n", "report.configurations['stat_fmtr'] = (\n", "    \"{pct_diff:,.2f}% ({avg_control:,.3}→{avg_treatment:,.3}, {p_value:.4})\"\n", ")"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["#################\n", "## Execute Report\n", "#################"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/html": ["<h3 >Study Name: NOTIFICATION_CENTER_ON_CAMERA_AB__144041</h3>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<strong >Control ID: 2</strong>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<strong >Treatment ID(s): 3</strong>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<strong >Analysis period: 20250603 - 20250604</strong>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<strong >Breakdowns: <br> gender</strong>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Report print headers\n", "abtest.report.notebook_print(\"Study Name: {}\".format(report.study_name), \"h3\")\n", "abtest.report.notebook_print(\"Control ID: {}\".format(report.control_id), \"strong\")\n", "abtest.report.notebook_print(\"Treatment ID(s): {}\".format(\", \".join(report.treatment_ids)), \"strong\")\n", "abtest.report.notebook_print(\n", "    \"Analysis period: {} - {}\".format(STUDY_START_DATE, STUDY_END_DATE), \n", "    \"strong\"\n", ")\n", "\n", "abtest.report.notebook_print(\n", "    \"Breakdowns: <br> {}\".format(\n", "        \"<br>\".join(\", \".join(breakdown) for breakdown in report.user_group_by_list)\n", "    ), \n", "    \"strong\"\n", ")"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["def get_metric_names(metric_tables, quantiles=False):\n", "    return [\n", "        metric.col\n", "        for metric_table in metric_tables\n", "        for metric in metric_table.metrics\n", "        if metric_table.quantile_metrics == quantiles\n", "    ]"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["# AB console metrics\n", "ab_quantile_metrics_name = get_metric_names(ab_console_metric_tables, quantiles=True)\n", "ab_regular_metrics_name = get_metric_names(ab_console_metric_tables, quantiles=False)\n", "\n", "#customer_report_quantile_metrics_name = get_metric_names(custom_report_ab_metric_tables, quantiles=True)\n", "#customer_report_regular_metrics_name = get_metric_names(custom_report_ab_metric_tables, quantiles=False)\n", "\n", "#Regular Metrics\n", "nc_engagement_metrics_list = [m for mt in nc_engagement_metrics for m in mt.cumulative_metrics]\n", "pp_source_metrics_list = [m for mt in pp_source_metrics for m in mt.cumulative_metrics]"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["if 'snapchat_core' in SELECTED_METRIC_LIST:\n", "    if ab_regular_metrics_name:\n", "        report.ab_printer.print_text(\"A/B Console Metrics - Counts, Ratios and Timespent\", \"h2\")\n", "        report.generate_report(\n", "            format_pvalue=True,\n", "            extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "            display_config={'cumulative': ['table'], 'daily': daily_display},\n", "            metric_filters = {\n", "                'metrics': ab_regular_metrics_name\n", "            },\n", "        );"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/html": ["<h2 >User Visit by Tab</h2>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Overall</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Comparison of user overall (cumulative) metric values during the study period</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p ><style type=\"text/css\">\n", "#T_fdfd3_row0_col0 {\n", "  color: white;\n", "  font-weight: bold;\n", "  background-color: #B10101;\n", "}\n", "#T_fdfd3_row1_col0, #T_fdfd3_row2_col0, #T_fdfd3_row3_col0, #T_fdfd3_row4_col0, #T_fdfd3_row5_col0, #T_fdfd3_row6_col0, #T_fdfd3_row7_col0, #T_fdfd3_row8_col0, #T_fdfd3_row9_col0, #T_fdfd3_row10_col0, #T_fdfd3_row11_col0 {\n", "  color: white;\n", "  font-weight: bold;\n", "  background-color: #006400;\n", "}\n", "</style>\n", "<table id=\"T_fdfd3\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level0\" >control_id</th>\n", "      <th id=\"T_fdfd3_level0_col0\" class=\"col_heading level0 col0\" >2 (Control)</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level1\" >treatment_id</th>\n", "      <th id=\"T_fdfd3_level1_col0\" class=\"col_heading level1 col0\" >3 (Public + Public Official tier - 3 Tabs No Profile No Badging )</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level2\" >Sample Sizes</th>\n", "      <th id=\"T_fdfd3_level2_col0\" class=\"col_heading level2 col0\" >Treatment=83,849<br>\n", "Control=83,653<br>\n", "No SSPM (p=0.6320)</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Date</th>\n", "      <th class=\"index_name level1\" >Metric</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_fdfd3_level0_row0\" class=\"row_heading level0 row0\" rowspan=\"12\">06/03 to 06/04</th>\n", "      <th id=\"T_fdfd3_level1_row0\" class=\"row_heading level1 row0\" >all_tab_open_count</th>\n", "      <td id=\"T_fdfd3_row0_col0\" class=\"data row0 col0\" >-15.27% (3.0→2.54, 7.182e-22)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fdfd3_level1_row1\" class=\"row_heading level1 row1\" >achievements_tab_open_count</th>\n", "      <td id=\"T_fdfd3_row1_col0\" class=\"data row1 col0\" >115.94% (0.0184→0.0397, 5.412e-67)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fdfd3_level1_row2\" class=\"row_heading level1 row2\" >comments_tab_open_count</th>\n", "      <td id=\"T_fdfd3_row2_col0\" class=\"data row2 col0\" >104.49% (0.0277→0.0566, 4.166e-86)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fdfd3_level1_row3\" class=\"row_heading level1 row3\" >replies_tab_open_count</th>\n", "      <td id=\"T_fdfd3_row3_col0\" class=\"data row3 col0\" >35.67% (0.0789→0.107, 1.343e-06)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fdfd3_level1_row4\" class=\"row_heading level1 row4\" >professional_tab_open_count</th>\n", "      <td id=\"T_fdfd3_row4_col0\" class=\"data row4 col0\" >139.19% (0.0117→0.0281, 9.441e-45)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fdfd3_level1_row5\" class=\"row_heading level1 row5\" >mentions_tab_open_count</th>\n", "      <td id=\"T_fdfd3_row5_col0\" class=\"data row5 col0\" >114.94% (0.0159→0.0343, 4.977e-66)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fdfd3_level1_row6\" class=\"row_heading level1 row6\" >all_tab_open_uu</th>\n", "      <td id=\"T_fdfd3_row6_col0\" class=\"data row6 col0\" >20.85% (0.459→0.555, 4.494e-165)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fdfd3_level1_row7\" class=\"row_heading level1 row7\" >achievements_tab_open_uu</th>\n", "      <td id=\"T_fdfd3_row7_col0\" class=\"data row7 col0\" >128.04% (0.0136→0.0311, 3.403e-119)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fdfd3_level1_row8\" class=\"row_heading level1 row8\" >comments_tab_open_uu</th>\n", "      <td id=\"T_fdfd3_row8_col0\" class=\"data row8 col0\" >117.60% (0.0202→0.044, 4.18e-152)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fdfd3_level1_row9\" class=\"row_heading level1 row9\" >replies_tab_open_uu</th>\n", "      <td id=\"T_fdfd3_row9_col0\" class=\"data row9 col0\" >79.49% (0.034→0.061, 1.379e-124)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fdfd3_level1_row10\" class=\"row_heading level1 row10\" >professional_tab_open_uu</th>\n", "      <td id=\"T_fdfd3_row10_col0\" class=\"data row10 col0\" >144.25% (0.00981→0.024, 7.904e-106)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_fdfd3_level1_row11\" class=\"row_heading level1 row11\" >mentions_tab_open_uu</th>\n", "      <td id=\"T_fdfd3_row11_col0\" class=\"data row11 col0\" >127.14% (0.0123→0.028, 7.567e-107)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Breakdown by user groups: gender</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Comparison of user overall (cumulative) metric values during the study period</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p ><style type=\"text/css\">\n", "#T_e273f_row0_col0, #T_e273f_row0_col1 {\n", "  color: white;\n", "  font-weight: bold;\n", "  background-color: #B10101;\n", "}\n", "#T_e273f_row1_col0, #T_e273f_row1_col1, #T_e273f_row2_col0, #T_e273f_row2_col1, #T_e273f_row3_col1, #T_e273f_row4_col0, #T_e273f_row4_col1, #T_e273f_row5_col0, #T_e273f_row5_col1, #T_e273f_row6_col0, #T_e273f_row6_col1, #T_e273f_row7_col0, #T_e273f_row7_col1, #T_e273f_row8_col0, #T_e273f_row8_col1, #T_e273f_row9_col0, #T_e273f_row9_col1, #T_e273f_row10_col0, #T_e273f_row10_col1, #T_e273f_row11_col0, #T_e273f_row11_col1 {\n", "  color: white;\n", "  font-weight: bold;\n", "  background-color: #006400;\n", "}\n", "#T_e273f_row3_col0 {\n", "  color: white;\n", "  font-weight: bold;\n", "  background-color: #99CC99;\n", "}\n", "</style>\n", "<table id=\"T_e273f\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level0\" >control_id</th>\n", "      <th id=\"T_e273f_level0_col0\" class=\"col_heading level0 col0\" colspan=\"2\">2 (Control)</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level1\" >treatment_id</th>\n", "      <th id=\"T_e273f_level1_col0\" class=\"col_heading level1 col0\" colspan=\"2\">3 (Public + Public Official tier - 3 Tabs No Profile No Badging )</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level2\" >gender</th>\n", "      <th id=\"T_e273f_level2_col0\" class=\"col_heading level2 col0\" >female</th>\n", "      <th id=\"T_e273f_level2_col1\" class=\"col_heading level2 col1\" >male</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level3\" >Sample Sizes</th>\n", "      <th id=\"T_e273f_level3_col0\" class=\"col_heading level3 col0\" >Treatment=34,028<br>\n", "Control=34,130<br>\n", "No SSPM (p=0.6960)</th>\n", "      <th id=\"T_e273f_level3_col1\" class=\"col_heading level3 col1\" >Treatment=49,642<br>\n", "Control=49,348<br>\n", "No SSPM (p=0.3501)</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Date</th>\n", "      <th class=\"index_name level1\" >Metric</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "      <th class=\"blank col1\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_e273f_level0_row0\" class=\"row_heading level0 row0\" rowspan=\"12\">06/03 to 06/04</th>\n", "      <th id=\"T_e273f_level1_row0\" class=\"row_heading level1 row0\" >all_tab_open_count</th>\n", "      <td id=\"T_e273f_row0_col0\" class=\"data row0 col0\" >-20.82% (3.72→2.95, 6.258e-20)</td>\n", "      <td id=\"T_e273f_row0_col1\" class=\"data row0 col1\" >-9.47% (2.49→2.26, 1.793e-05)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e273f_level1_row1\" class=\"row_heading level1 row1\" >achievements_tab_open_count</th>\n", "      <td id=\"T_e273f_row1_col0\" class=\"data row1 col0\" >113.55% (0.0179→0.0383, 1.349e-27)</td>\n", "      <td id=\"T_e273f_row1_col1\" class=\"data row1 col1\" >119.00% (0.0186→0.0407, 1.897e-41)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e273f_level1_row2\" class=\"row_heading level1 row2\" >comments_tab_open_count</th>\n", "      <td id=\"T_e273f_row2_col0\" class=\"data row2 col0\" >99.35% (0.0282→0.0561, 3.533e-34)</td>\n", "      <td id=\"T_e273f_row2_col1\" class=\"data row2 col1\" >109.20% (0.0272→0.0568, 5.342e-54)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e273f_level1_row3\" class=\"row_heading level1 row3\" >replies_tab_open_count</th>\n", "      <td id=\"T_e273f_row3_col0\" class=\"data row3 col0\" >27.48% (0.0776→0.099, 0.02176)</td>\n", "      <td id=\"T_e273f_row3_col1\" class=\"data row3 col1\" >42.55% (0.0791→0.113, 6.275e-06)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e273f_level1_row4\" class=\"row_heading level1 row4\" >professional_tab_open_count</th>\n", "      <td id=\"T_e273f_row4_col0\" class=\"data row4 col0\" >138.09% (0.0116→0.0276, 8.483e-12)</td>\n", "      <td id=\"T_e273f_row4_col1\" class=\"data row4 col1\" >140.49% (0.0118→0.0284, 6.51e-49)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e273f_level1_row5\" class=\"row_heading level1 row5\" >mentions_tab_open_count</th>\n", "      <td id=\"T_e273f_row5_col0\" class=\"data row5 col0\" >98.85% (0.0168→0.0334, 7.392e-23)</td>\n", "      <td id=\"T_e273f_row5_col1\" class=\"data row5 col1\" >128.78% (0.0153→0.0349, 5.825e-46)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e273f_level1_row6\" class=\"row_heading level1 row6\" >all_tab_open_uu</th>\n", "      <td id=\"T_e273f_row6_col0\" class=\"data row6 col0\" >10.84% (0.51→0.565, 4.907e-23)</td>\n", "      <td id=\"T_e273f_row6_col1\" class=\"data row6 col1\" >29.21% (0.425→0.549, 8.565e-169)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e273f_level1_row7\" class=\"row_heading level1 row7\" >achievements_tab_open_uu</th>\n", "      <td id=\"T_e273f_row7_col0\" class=\"data row7 col0\" >123.18% (0.0135→0.0302, 6.716e-47)</td>\n", "      <td id=\"T_e273f_row7_col1\" class=\"data row7 col1\" >133.53% (0.0136→0.0317, 4.075e-75)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e273f_level1_row8\" class=\"row_heading level1 row8\" >comments_tab_open_uu</th>\n", "      <td id=\"T_e273f_row8_col0\" class=\"data row8 col0\" >110.36% (0.0208→0.0437, 1.711e-58)</td>\n", "      <td id=\"T_e273f_row8_col1\" class=\"data row8 col1\" >124.00% (0.0197→0.0441, 3.786e-96)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e273f_level1_row9\" class=\"row_heading level1 row9\" >replies_tab_open_uu</th>\n", "      <td id=\"T_e273f_row9_col0\" class=\"data row9 col0\" >72.51% (0.035→0.0605, 6.771e-46)</td>\n", "      <td id=\"T_e273f_row9_col1\" class=\"data row9 col1\" >85.30% (0.0331→0.0613, 1.951e-81)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e273f_level1_row10\" class=\"row_heading level1 row10\" >professional_tab_open_uu</th>\n", "      <td id=\"T_e273f_row10_col0\" class=\"data row10 col0\" >130.24% (0.00982→0.0226, 5.13e-38)</td>\n", "      <td id=\"T_e273f_row10_col1\" class=\"data row10 col1\" >154.59% (0.00979→0.0249, 6.586e-70)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_e273f_level1_row11\" class=\"row_heading level1 row11\" >mentions_tab_open_uu</th>\n", "      <td id=\"T_e273f_row11_col0\" class=\"data row11 col0\" >107.09% (0.0131→0.0272, 1.664e-36)</td>\n", "      <td id=\"T_e273f_row11_col1\" class=\"data row11 col1\" >144.13% (0.0117→0.0285, 1.765e-73)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["report.configurations['pivot_table'] = PIVOT_RESULTS\n", "report.configurations['stat_fmtr'] = (\n", "    \"{pct_diff:,.2f}% ({avg_control:,.3}→{avg_treatment:,.3}, {p_value:.4})\"\n", ")\n", "\n", "if 'notification_center_metrics' in SELECTED_METRIC_LIST:\n", "    report.ab_printer.print_text(\"User Visit by Tab\", \"h2\")\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': []},\n", "        metric_filters = {\n", "            'metrics': nc_engagement_metrics_list\n", "        },\n", "    )"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/html": ["<h2 >Public Profile Visit by Source</h2>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Overall</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Comparison of user overall (cumulative) metric values during the study period</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p ><style type=\"text/css\">\n", "#T_6cce6_row3_col0, #T_6cce6_row13_col0 {\n", "  color: white;\n", "  font-weight: bold;\n", "  background-color: #99CC99;\n", "}\n", "#T_6cce6_row4_col0 {\n", "  color: white;\n", "  font-weight: bold;\n", "  background-color: #66B366;\n", "}\n", "#T_6cce6_row21_col0 {\n", "  color: white;\n", "  font-weight: bold;\n", "  background-color: #1D831D;\n", "}\n", "</style>\n", "<table id=\"T_6cce6\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level0\" >control_id</th>\n", "      <th id=\"T_6cce6_level0_col0\" class=\"col_heading level0 col0\" >2 (Control)</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level1\" >treatment_id</th>\n", "      <th id=\"T_6cce6_level1_col0\" class=\"col_heading level1 col0\" >3 (Public + Public Official tier - 3 Tabs No Profile No Badging )</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level2\" >Sample Sizes</th>\n", "      <th id=\"T_6cce6_level2_col0\" class=\"col_heading level2 col0\" >Treatment=83,849<br>\n", "Control=83,653<br>\n", "No SSPM (p=0.6320)</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Date</th>\n", "      <th class=\"index_name level1\" >Metric</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_6cce6_level0_row0\" class=\"row_heading level0 row0\" rowspan=\"24\">06/03 to 06/04</th>\n", "      <th id=\"T_6cce6_level1_row0\" class=\"row_heading level1 row0\" >public_profile_view</th>\n", "      <td id=\"T_6cce6_row0_col0\" class=\"data row0 col0\" >2.74% (10.8→11.1, 0.08464)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6cce6_level1_row1\" class=\"row_heading level1 row1\" >public_profile_view_from_add_friends</th>\n", "      <td id=\"T_6cce6_row1_col0\" class=\"data row1 col0\" >2.74% (3.48→3.57, 0.3456)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6cce6_level1_row2\" class=\"row_heading level1 row2\" >public_profile_view_from_profile</th>\n", "      <td id=\"T_6cce6_row2_col0\" class=\"data row2 col0\" >1.88% (1.55→1.58, 0.3629)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6cce6_level1_row3\" class=\"row_heading level1 row3\" >public_profile_view_from_context_menu</th>\n", "      <td id=\"T_6cce6_row3_col0\" class=\"data row3 col0\" >4.22% (1.75→1.82, 0.0291)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6cce6_level1_row4\" class=\"row_heading level1 row4\" >public_profile_view_from_spotlight_feed</th>\n", "      <td id=\"T_6cce6_row4_col0\" class=\"data row4 col0\" >6.04% (0.927→0.983, 0.006726)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6cce6_level1_row5\" class=\"row_heading level1 row5\" >public_profile_view_from_search</th>\n", "      <td id=\"T_6cce6_row5_col0\" class=\"data row5 col0\" >-3.30% (0.498→0.482, 0.3133)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6cce6_level1_row6\" class=\"row_heading level1 row6\" >public_profile_view_from_unknown_source</th>\n", "      <td id=\"T_6cce6_row6_col0\" class=\"data row6 col0\" >7.29% (0.349→0.375, 0.3314)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6cce6_level1_row7\" class=\"row_heading level1 row7\" >public_profile_view_from_chat</th>\n", "      <td id=\"T_6cce6_row7_col0\" class=\"data row7 col0\" >0.63% (0.577→0.58, 0.8745)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6cce6_level1_row8\" class=\"row_heading level1 row8\" >public_profile_view_from_discover_feed</th>\n", "      <td id=\"T_6cce6_row8_col0\" class=\"data row8 col0\" >-1.23% (0.386→0.381, 0.8505)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6cce6_level1_row9\" class=\"row_heading level1 row9\" >public_profile_view_from_public_profile_management</th>\n", "      <td id=\"T_6cce6_row9_col0\" class=\"data row9 col0\" >2.75% (1.15→1.18, 0.4807)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6cce6_level1_row10\" class=\"row_heading level1 row10\" >public_profile_view_from_external_source</th>\n", "      <td id=\"T_6cce6_row10_col0\" class=\"data row10 col0\" >0.30% (0.0732→0.0735, 0.919)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6cce6_level1_row11\" class=\"row_heading level1 row11\" >public_profile_view_from_other_source</th>\n", "      <td id=\"T_6cce6_row11_col0\" class=\"data row11 col0\" >2.79% (0.0915→0.094, 0.668)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6cce6_level1_row12\" class=\"row_heading level1 row12\" >public_profile_view_uu</th>\n", "      <td id=\"T_6cce6_row12_col0\" class=\"data row12 col0\" >0.37% (0.962→0.966, 0.364)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6cce6_level1_row13\" class=\"row_heading level1 row13\" >public_profile_view_from_add_friends_uu</th>\n", "      <td id=\"T_6cce6_row13_col0\" class=\"data row13 col0\" >1.69% (0.414→0.421, 0.03004)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6cce6_level1_row14\" class=\"row_heading level1 row14\" >public_profile_view_from_profile_uu</th>\n", "      <td id=\"T_6cce6_row14_col0\" class=\"data row14 col0\" >-0.03% (0.406→0.406, 0.9716)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6cce6_level1_row15\" class=\"row_heading level1 row15\" >public_profile_view_from_context_menu_uu</th>\n", "      <td id=\"T_6cce6_row15_col0\" class=\"data row15 col0\" >0.22% (0.512→0.513, 0.7465)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6cce6_level1_row16\" class=\"row_heading level1 row16\" >public_profile_view_from_spotlight_feed_uu</th>\n", "      <td id=\"T_6cce6_row16_col0\" class=\"data row16 col0\" >1.92% (0.245→0.25, 0.0729)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6cce6_level1_row17\" class=\"row_heading level1 row17\" >public_profile_view_from_search_uu</th>\n", "      <td id=\"T_6cce6_row17_col0\" class=\"data row17 col0\" >-0.13% (0.168→0.168, 0.919)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6cce6_level1_row18\" class=\"row_heading level1 row18\" >public_profile_view_from_unknown_source_uu</th>\n", "      <td id=\"T_6cce6_row18_col0\" class=\"data row18 col0\" >1.63% (0.133→0.136, 0.2536)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6cce6_level1_row19\" class=\"row_heading level1 row19\" >public_profile_view_from_chat_uu</th>\n", "      <td id=\"T_6cce6_row19_col0\" class=\"data row19 col0\" >-0.88% (0.2→0.199, 0.4505)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6cce6_level1_row20\" class=\"row_heading level1 row20\" >public_profile_view_from_discover_feed_uu</th>\n", "      <td id=\"T_6cce6_row20_col0\" class=\"data row20 col0\" >0.26% (0.106→0.106, 0.873)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6cce6_level1_row21\" class=\"row_heading level1 row21\" >public_profile_view_from_public_profile_management_uu</th>\n", "      <td id=\"T_6cce6_row21_col0\" class=\"data row21 col0\" >4.98% (0.179→0.188, 0.0001334)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6cce6_level1_row22\" class=\"row_heading level1 row22\" >public_profile_view_from_external_source_uu</th>\n", "      <td id=\"T_6cce6_row22_col0\" class=\"data row22 col0\" >-1.21% (0.05→0.0494, 0.5897)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6cce6_level1_row23\" class=\"row_heading level1 row23\" >public_profile_view_from_other_source_uu</th>\n", "      <td id=\"T_6cce6_row23_col0\" class=\"data row23 col0\" >1.50% (0.037→0.0376, 0.5801)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Breakdown by user groups: gender</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Comparison of user overall (cumulative) metric values during the study period</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p ><style type=\"text/css\">\n", "#T_1a07f_row0_col0, #T_1a07f_row3_col0, #T_1a07f_row4_col1 {\n", "  color: white;\n", "  font-weight: bold;\n", "  background-color: #99CC99;\n", "}\n", "#T_1a07f_row21_col0 {\n", "  color: white;\n", "  font-weight: bold;\n", "  background-color: #006400;\n", "}\n", "</style>\n", "<table id=\"T_1a07f\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level0\" >control_id</th>\n", "      <th id=\"T_1a07f_level0_col0\" class=\"col_heading level0 col0\" colspan=\"2\">2 (Control)</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level1\" >treatment_id</th>\n", "      <th id=\"T_1a07f_level1_col0\" class=\"col_heading level1 col0\" colspan=\"2\">3 (Public + Public Official tier - 3 Tabs No Profile No Badging )</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level2\" >gender</th>\n", "      <th id=\"T_1a07f_level2_col0\" class=\"col_heading level2 col0\" >female</th>\n", "      <th id=\"T_1a07f_level2_col1\" class=\"col_heading level2 col1\" >male</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level3\" >Sample Sizes</th>\n", "      <th id=\"T_1a07f_level3_col0\" class=\"col_heading level3 col0\" >Treatment=34,028<br>\n", "Control=34,130<br>\n", "No SSPM (p=0.6960)</th>\n", "      <th id=\"T_1a07f_level3_col1\" class=\"col_heading level3 col1\" >Treatment=49,642<br>\n", "Control=49,348<br>\n", "No SSPM (p=0.3501)</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Date</th>\n", "      <th class=\"index_name level1\" >Metric</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "      <th class=\"blank col1\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_1a07f_level0_row0\" class=\"row_heading level0 row0\" rowspan=\"24\">06/03 to 06/04</th>\n", "      <th id=\"T_1a07f_level1_row0\" class=\"row_heading level1 row0\" >public_profile_view</th>\n", "      <td id=\"T_1a07f_row0_col0\" class=\"data row0 col0\" >5.23% (6.98→7.35, 0.02677)</td>\n", "      <td id=\"T_1a07f_row0_col1\" class=\"data row0 col1\" >1.87% (13.5→13.7, 0.344)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1a07f_level1_row1\" class=\"row_heading level1 row1\" >public_profile_view_from_add_friends</th>\n", "      <td id=\"T_1a07f_row1_col0\" class=\"data row1 col0\" >5.82% (1.87→1.97, 0.1978)</td>\n", "      <td id=\"T_1a07f_row1_col1\" class=\"data row1 col1\" >2.17% (4.57→4.67, 0.535)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1a07f_level1_row2\" class=\"row_heading level1 row2\" >public_profile_view_from_profile</th>\n", "      <td id=\"T_1a07f_row2_col0\" class=\"data row2 col0\" >3.63% (1.06→1.1, 0.3348)</td>\n", "      <td id=\"T_1a07f_row2_col1\" class=\"data row2 col1\" >1.12% (1.89→1.91, 0.6514)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1a07f_level1_row3\" class=\"row_heading level1 row3\" >public_profile_view_from_context_menu</th>\n", "      <td id=\"T_1a07f_row3_col0\" class=\"data row3 col0\" >5.60% (1.28→1.36, 0.03048)</td>\n", "      <td id=\"T_1a07f_row3_col1\" class=\"data row3 col1\" >3.59% (2.07→2.14, 0.1542)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1a07f_level1_row4\" class=\"row_heading level1 row4\" >public_profile_view_from_spotlight_feed</th>\n", "      <td id=\"T_1a07f_row4_col0\" class=\"data row4 col0\" >5.06% (0.537→0.565, 0.1972)</td>\n", "      <td id=\"T_1a07f_row4_col1\" class=\"data row4 col1\" >6.01% (1.2→1.27, 0.02291)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1a07f_level1_row5\" class=\"row_heading level1 row5\" >public_profile_view_from_search</th>\n", "      <td id=\"T_1a07f_row5_col0\" class=\"data row5 col0\" >3.26% (0.417→0.431, 0.5732)</td>\n", "      <td id=\"T_1a07f_row5_col1\" class=\"data row5 col1\" >-6.82% (0.553→0.516, 0.08686)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1a07f_level1_row6\" class=\"row_heading level1 row6\" >public_profile_view_from_unknown_source</th>\n", "      <td id=\"T_1a07f_row6_col0\" class=\"data row6 col0\" >19.81% (0.196→0.234, 0.2438)</td>\n", "      <td id=\"T_1a07f_row6_col1\" class=\"data row6 col1\" >3.35% (0.454→0.47, 0.689)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1a07f_level1_row7\" class=\"row_heading level1 row7\" >public_profile_view_from_chat</th>\n", "      <td id=\"T_1a07f_row7_col0\" class=\"data row7 col0\" >-4.27% (0.546→0.523, 0.543)</td>\n", "      <td id=\"T_1a07f_row7_col1\" class=\"data row7 col1\" >3.98% (0.595→0.619, 0.4016)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1a07f_level1_row8\" class=\"row_heading level1 row8\" >public_profile_view_from_discover_feed</th>\n", "      <td id=\"T_1a07f_row8_col0\" class=\"data row8 col0\" >12.03% (0.23→0.258, 0.2038)</td>\n", "      <td id=\"T_1a07f_row8_col1\" class=\"data row8 col1\" >-5.77% (0.494→0.465, 0.4763)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1a07f_level1_row9\" class=\"row_heading level1 row9\" >public_profile_view_from_public_profile_management</th>\n", "      <td id=\"T_1a07f_row9_col0\" class=\"data row9 col0\" >7.35% (0.723→0.777, 0.1354)</td>\n", "      <td id=\"T_1a07f_row9_col1\" class=\"data row9 col1\" >0.98% (1.44→1.45, 0.8437)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1a07f_level1_row10\" class=\"row_heading level1 row10\" >public_profile_view_from_external_source</th>\n", "      <td id=\"T_1a07f_row10_col0\" class=\"data row10 col0\" >-1.24% (0.0555→0.0548, 0.8175)</td>\n", "      <td id=\"T_1a07f_row10_col1\" class=\"data row10 col1\" >0.58% (0.0856→0.0861, 0.8707)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1a07f_level1_row11\" class=\"row_heading level1 row11\" >public_profile_view_from_other_source</th>\n", "      <td id=\"T_1a07f_row11_col0\" class=\"data row11 col0\" >15.66% (0.062→0.0717, 0.08832)</td>\n", "      <td id=\"T_1a07f_row11_col1\" class=\"data row11 col1\" >-2.13% (0.112→0.109, 0.7979)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1a07f_level1_row12\" class=\"row_heading level1 row12\" >public_profile_view_uu</th>\n", "      <td id=\"T_1a07f_row12_col0\" class=\"data row12 col0\" >0.46% (0.871→0.875, 0.5057)</td>\n", "      <td id=\"T_1a07f_row12_col1\" class=\"data row12 col1\" >0.28% (1.03→1.03, 0.5767)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1a07f_level1_row13\" class=\"row_heading level1 row13\" >public_profile_view_from_add_friends_uu</th>\n", "      <td id=\"T_1a07f_row13_col0\" class=\"data row13 col0\" >1.80% (0.324→0.33, 0.2035)</td>\n", "      <td id=\"T_1a07f_row13_col1\" class=\"data row13 col1\" >1.52% (0.476→0.483, 0.09886)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1a07f_level1_row14\" class=\"row_heading level1 row14\" >public_profile_view_from_profile_uu</th>\n", "      <td id=\"T_1a07f_row14_col0\" class=\"data row14 col0\" >-0.73% (0.326→0.323, 0.6061)</td>\n", "      <td id=\"T_1a07f_row14_col1\" class=\"data row14 col1\" >0.22% (0.462→0.463, 0.8191)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1a07f_level1_row15\" class=\"row_heading level1 row15\" >public_profile_view_from_context_menu_uu</th>\n", "      <td id=\"T_1a07f_row15_col0\" class=\"data row15 col0\" >0.41% (0.448→0.45, 0.7167)</td>\n", "      <td id=\"T_1a07f_row15_col1\" class=\"data row15 col1\" >0.06% (0.556→0.556, 0.9386)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1a07f_level1_row16\" class=\"row_heading level1 row16\" >public_profile_view_from_spotlight_feed_uu</th>\n", "      <td id=\"T_1a07f_row16_col0\" class=\"data row16 col0\" >1.76% (0.173→0.176, 0.3802)</td>\n", "      <td id=\"T_1a07f_row16_col1\" class=\"data row16 col1\" >1.87% (0.295→0.301, 0.1379)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1a07f_level1_row17\" class=\"row_heading level1 row17\" >public_profile_view_from_search_uu</th>\n", "      <td id=\"T_1a07f_row17_col0\" class=\"data row17 col0\" >1.50% (0.155→0.157, 0.4693)</td>\n", "      <td id=\"T_1a07f_row17_col1\" class=\"data row17 col1\" >-1.06% (0.177→0.175, 0.5022)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1a07f_level1_row18\" class=\"row_heading level1 row18\" >public_profile_view_from_unknown_source_uu</th>\n", "      <td id=\"T_1a07f_row18_col0\" class=\"data row18 col0\" >2.97% (0.0979→0.101, 0.2501)</td>\n", "      <td id=\"T_1a07f_row18_col1\" class=\"data row18 col1\" >0.89% (0.158→0.159, 0.6014)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1a07f_level1_row19\" class=\"row_heading level1 row19\" >public_profile_view_from_chat_uu</th>\n", "      <td id=\"T_1a07f_row19_col0\" class=\"data row19 col0\" >-3.15% (0.187→0.181, 0.09319)</td>\n", "      <td id=\"T_1a07f_row19_col1\" class=\"data row19 col1\" >0.43% (0.21→0.211, 0.7739)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1a07f_level1_row20\" class=\"row_heading level1 row20\" >public_profile_view_from_discover_feed_uu</th>\n", "      <td id=\"T_1a07f_row20_col0\" class=\"data row20 col0\" >1.60% (0.0813→0.0826, 0.583)</td>\n", "      <td id=\"T_1a07f_row20_col1\" class=\"data row20 col1\" >-0.40% (0.123→0.122, 0.8416)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1a07f_level1_row21\" class=\"row_heading level1 row21\" >public_profile_view_from_public_profile_management_uu</th>\n", "      <td id=\"T_1a07f_row21_col0\" class=\"data row21 col0\" >9.52% (0.153→0.168, 1.781e-05)</td>\n", "      <td id=\"T_1a07f_row21_col1\" class=\"data row21 col1\" >2.56% (0.197→0.202, 0.1124)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1a07f_level1_row22\" class=\"row_heading level1 row22\" >public_profile_view_from_external_source_uu</th>\n", "      <td id=\"T_1a07f_row22_col0\" class=\"data row22 col0\" >-1.62% (0.0367→0.0361, 0.6941)</td>\n", "      <td id=\"T_1a07f_row22_col1\" class=\"data row22 col1\" >-1.31% (0.0592→0.0585, 0.6253)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_1a07f_level1_row23\" class=\"row_heading level1 row23\" >public_profile_view_from_other_source_uu</th>\n", "      <td id=\"T_1a07f_row23_col0\" class=\"data row23 col0\" >0.00% (0.0296→0.0296, 0.9997)</td>\n", "      <td id=\"T_1a07f_row23_col1\" class=\"data row23 col1\" >2.47% (0.0421→0.0431, 0.4571)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["report.configurations['pivot_table'] = PIVOT_RESULTS\n", "report.configurations['stat_fmtr'] = (\n", "    \"{pct_diff:,.2f}% ({avg_control:,.3}→{avg_treatment:,.3}, {p_value:.4})\"\n", ")\n", "\n", "if 'notification_center_metrics' in SELECTED_METRIC_LIST:\n", "    report.ab_printer.print_text(\"Public Profile Visit by Source\", \"h2\")\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': []},\n", "        metric_filters = {\n", "            'metrics': pp_source_metrics_list\n", "        },\n", "    )"]}], "metadata": {"anaconda-cloud": {}, "celltoolbar": "Tags", "hide_input": false, "kernelspec": {"display_name": "py38", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}, "require": {"paths": {"buttons.colvis": "https://cdn.datatables.net/buttons/1.5.6/js/buttons.colVis.min", "buttons.flash": "https://cdn.datatables.net/buttons/1.5.6/js/buttons.flash.min", "buttons.html5": "https://cdn.datatables.net/buttons/1.5.6/js/buttons.html5.min", "buttons.print": "https://cdn.datatables.net/buttons/1.5.6/js/buttons.print.min", "d3": "https://cdnjs.cloudflare.com/ajax/libs/d3/5.9.2/d3.min", "datatables.net": "https://cdn.datatables.net/1.10.18/js/jquery.dataTables", "datatables.net-buttons": "https://cdn.datatables.net/buttons/1.5.6/js/dataTables.buttons.min", "datatables.responsive": "https://cdn.datatables.net/responsive/2.2.2/js/dataTables.responsive.min", "datatables.scroller": "https://cdn.datatables.net/scroller/2.0.0/js/dataTables.scroller.min", "datatables.select": "https://cdn.datatables.net/select/1.3.0/js/dataTables.select.min", "jszip": "https://cdnjs.cloudflare.com/ajax/libs/jszip/2.5.0/jszip.min", "pdfmake": "https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/pdfmake.min", "vfsfonts": "https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.36/vfs_fonts"}, "shim": {"buttons.colvis": {"deps": ["j<PERSON><PERSON>", "datatables.net-buttons"]}, "buttons.flash": {"deps": ["j<PERSON><PERSON>", "datatables.net-buttons"]}, "buttons.html5": {"deps": ["j<PERSON><PERSON>", "datatables.net-buttons"]}, "buttons.print": {"deps": ["j<PERSON><PERSON>", "datatables.net-buttons"]}, "datatables.net": {"exports": "$.fn.dataTable"}, "datatables.net-buttons": {"deps": ["datatables.net"]}, "pdfmake": {"deps": ["datatables.net"]}, "vfsfonts": {"deps": ["datatables.net"]}}}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": true, "toc_position": {"height": "calc(100% - 180px)", "left": "10px", "top": "150px", "width": "165px"}, "toc_section_display": true, "toc_window_display": true}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 4}