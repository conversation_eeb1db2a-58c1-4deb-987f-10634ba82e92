{"cells": [{"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["import pandas as pd\n", "import time\n", "from banjo.utils import gbq\n", "from IPython.core.display import display, HTML\n", "import json\n", "from itertools import combinations\n", "import matplotlib.pyplot as plt\n", "from matplotlib_venn import venn2, venn3\n", "\n"], "id": "5fda65bc5446456e"}, {"metadata": {"tags": ["parameters"]}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["#Parameters\n", "\n", "group_type = \"\"\n", "group_id = \"\"\n", "start_date = \"\"\n", "end_date = \"\"\n", "\n", "OVERWRITE = True"], "id": "d12e758009057c0"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["#for unique submission id\n", "\n", "query_ts = time.time_ns()\n"], "id": "11a20d9e07ad90c1"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["query = f\"\"\"\n", "WITH product_overlap AS (WITH example_data AS (\n", "         SELECT DISTINCT\n", "               CASE\n", "                 WHEN cross_product.sub_format IN (\"COMMERCIAL\", \"COMMERCIAL_EXTENDED_PLAY\") THEN \"COMMERCIAL\"\n", "                 WHEN cross_product.sub_format = \"COLLECTION\" THEN \"COLLECTION\"\n", "                 ELSE cross_product.format\n", "\n", "           END AS element_raw,\n", "           hll_count.merge_partial(paid_reach_hll) AS paid_reach_hll\n", "         FROM `sc-targeting-measurement.prod_campaign_reports_billable.pcr_campaign_performance_event_time` pcr\n", "         LEFT JOIN `sc-targeting-measurement.prod_snapads_metadata.metadata_cp` AS cross_product\n", "           ON pcr.ad_id = cross_product.ad_id\n", "         WHERE\n", "            DATE(pcr.date_local) BETWEEN PARSE_DATE('%b %d, %Y', '{start_date}') AND PARSE_DATE('%b %d, %Y', '{end_date}')\n", "\n", "\n", "           AND CASE\n", "             WHEN '{group_type}' = \"ad_account_id\" THEN pcr.ad_account_id\n", "             WHEN '{group_type}' = \"campaign_id\" THEN pcr.campaign_id\n", "             WHEN '{group_type}' = \"ad_squad_id\" THEN pcr.ad_squad_id\n", "             ELSE \"return error\"\n", "           END IN (SELECT TRIM(id) FROM UNNEST(SPLIT('{group_id}' , \",\")) AS id)\n", "           -- Ensure we only keep rows that map to one of the 6 allowed formats:\n", "           AND (\n", "             (cross_product.sub_format IN ('COMMERCIAL', 'COMMERCIAL_EXTENDED_PLAY', 'COLLECTION'))\n", "             OR cross_product.format IN ('STORY', 'SNAP_AD', 'LENS', 'FILTER','COMMERCIAL','COLLECTION')\n", "           )\n", "         GROUP BY 1\n", "      ),\n", "\n", "      subsets AS (\n", "      SELECT\n", "      subset,\n", "      hll_count.merge_partial(paid_reach_hll) AS unique_reach_hll,\n", "      hll_count.merge(paid_reach_hll) AS unique_reach,\n", "      COUNT(DISTINCT element_raw) AS total_ad_sets,\n", "      MAX(hll_count.merge(paid_reach_hll)) OVER () AS total_reach_group,\n", "      MAX(COUNT(DISTINCT element_raw)) OVER () AS total_campaigns\n", "      FROM (\n", "      SELECT\n", "      subset\n", "      FROM (\n", "      SELECT `sc-bq-sales-insights.connect_looker.get_subsets`(ARRAY_AGG(element_raw)) AS ads_array\n", "      FROM example_data\n", "      ), UNNEST(ads_array) AS subset\n", "      )\n", "      CROSS JOIN example_data\n", "      WHERE subset LIKE CONCAT('%', element_raw, '%')\n", "      GROUP BY 1\n", "      )\n", "\n", "      SELECT\n", "      subset_main AS subset,\n", "      unique_reach_main AS unique_reach,\n", "      (total_reach_group - unique_reach_secondary) AS incremental_reach\n", "      FROM (\n", "      SELECT\n", "      a.subset AS subset_main,\n", "      a.unique_reach AS unique_reach_main,\n", "      b.subset AS subset_secondary,\n", "      b.unique_reach AS unique_reach_secondary,\n", "      (\n", "      SELECT HLL_COUNT.MERGE(sketch)\n", "      FROM UNNEST([a.unique_reach_hll, b.unique_reach_hll]) sketch\n", "      ) AS a_union_b_reach,\n", "      a.total_reach_group,\n", "      a.total_ad_sets,\n", "      b.total_ad_sets\n", "      FROM subsets a\n", "      CROSS JOIN (\n", "      SELECT * FROM subsets\n", "      UNION ALL SELECT \"\" AS subset, NULL AS unique_reach_hll, 0 AS unique_reach, 0 AS total_ad_sets, 0 AS total_reach_group, 0 AS total_campaigns\n", "      ) b\n", "      WHERE a.unique_reach <> b.unique_reach\n", "      AND a.total_campaigns = a.total_ad_sets + b.total_ad_sets\n", "      )\n", "      WHERE a_union_b_reach = total_reach_group\n", "      )\n", "SELECT\n", "    \"{query_ts}\" AS submission_id,\n", "    CASE WHEN product_overlap.subset LIKE '%COLLECTION%' THEN 'Yes' ELSE 'No' END  AS Collection,\n", "    CASE WHEN product_overlap.subset LIKE '%COMMERCIAL%' THEN 'Yes' ELSE 'No' END  AS Commercial,\n", "    CASE WHEN product_overlap.subset LIKE '%FILTER%' THEN 'Yes' ELSE 'No' END  AS Filter,\n", "    CASE WHEN product_overlap.subset LIKE '%LENS%' THEN 'Yes' ELSE 'No' END  AS Lens,\n", "    CASE WHEN product_overlap.subset LIKE '%SNAP_AD%' THEN 'Yes' ELSE 'No' END  AS Snap_Ad,\n", "    CASE WHEN product_overlap.subset LIKE '%STORY%' THEN 'Yes' ELSE 'No' END  AS Story,\n", "    product_overlap.subset  AS Subset,\n", "    sum(product_overlap.incremental_reach)  AS Incremental_Reach,\n", "    sum(product_overlap.unique_reach)  AS Unique_Reach\n", "FROM product_overlap\n", "GROUP BY\n", "    1,\n", "    2,\n", "    3,\n", "    4,\n", "    5,\n", "    6,\n", "    7,\n", "    8\n", "ORDER BY\n", "    8 DESC\n", "LIMIT 500\n", "\n", ";\n", "\"\"\""], "id": "372035a221f60283"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["df_with_results = gbq.read_gbq(\n", "        query,\n", "        project_id=\"sc-bq-sales-insights\",\n", "        dialect='standard',\n", "        use_bqstorage_api=False\n", "    )"], "id": "5150b140eb526513"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["gbq.to_gbq(df_with_results,\n", "            project_id=\"sc-bq-sales-insights\",\n", "            dest_dataset_id=\"temp_output_tables\",\n", "            dest_table_name=\"product_overlap_template\",\n", "            dest_project_id=None,\n", "            write_disposition=\"WRITE_APPEND\"\n", "            );"], "id": "91c24ea3784d3240"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["import json\n", "import matplotlib.pyplot as plt\n", "from matplotlib_venn import venn2, venn3\n", "from itertools import combinations\n", "\n", "# ── Build frozenset keys ──\n", "df = df_with_results.copy()\n", "df[\"fkey\"] = df[\"Subset\"].apply(lambda s: frozenset(json.loads(s)))\n", "\n", "# ── Inclusion–Exclusion logic ──\n", "region_data = {}\n", "for fset, incr in sorted(zip(df[\"fkey\"], df[\"Incremental_Reach\"]), key=lambda x: len(x[0])):\n", "    deduction = sum(region_data.get(frozenset(sub), 0)\n", "                    for r in range(1, len(fset))\n", "                    for sub in combinations(fset, r))\n", "    region_data[fset] = incr - deduction\n", "\n", "df_with_results[\"Data\"] = df_with_results[\"Subset\"].apply(\n", "    lambda s: region_data[frozenset(json.loads(s))]\n", ")\n", "\n", "\n", "# ── <PERSON><PERSON><PERSON> ──\n", "def fmt_pct(val):\n", "    pct = (val / total_data_reach) * 100 if total_data_reach else 0\n", "    return f\"{pct:.1f}%\"\n", "\n", "\n", "# ── Handle 4-format case by merging bottom 2 ──\n", "raw_cats = sorted({e for s in df_with_results[\"Subset\"] for e in json.loads(s)})\n", "\n", "if len(raw_cats) == 4:\n", "    # Compute individual reach\n", "    format_reach = {\n", "        cat: region_data.get(frozenset([cat]), 0)\n", "        for cat in raw_cats\n", "    }\n", "\n", "    # Pick 2 with lowest reach\n", "    bottom_two = sorted(format_reach, key=format_reach.get)[:2]\n", "    top_two = [cat for cat in raw_cats if cat not in bottom_two]\n", "    merged_label = f\"{bottom_two[0]} + {bottom_two[1]}\"\n", "\n", "    # New list of 3 formats\n", "    raw_cats = sorted(top_two + [merged_label])\n", "\n", "    # Merge subsets\n", "    def merge_subset(s):\n", "        items = set(json.loads(s))\n", "        result = set()\n", "        found = False\n", "\n", "        for item in items:\n", "            if item in bottom_two:\n", "                found = True\n", "            elif item in top_two:\n", "                result.add(item)\n", "\n", "        if found:\n", "            result.add(merged_label)\n", "\n", "        return json.dumps(sorted(result))  # return as JSON string\n", "\n", "    # Apply merged label\n", "    df_with_results[\"Subset\"] = df_with_results[\"Subset\"].apply(merge_subset)\n", "\n", "    # Group by merged subset and recalculate\n", "    grouped = (\n", "        df_with_results\n", "        .groupby(\"Subset\", as_index=False)\n", "        .agg({\"Incremental_Reach\": \"sum\"})\n", "    )\n", "\n", "    grouped[\"fkey\"] = grouped[\"Subset\"].apply(lambda s: frozenset(json.loads(s)))\n", "    region_data = {}\n", "    for fset, incr in sorted(zip(grouped[\"fkey\"], grouped[\"Incremental_Reach\"]), key=lambda x: len(x[0])):\n", "        deduction = sum(region_data.get(frozenset(sub), 0)\n", "                        for r in range(1, len(fset))\n", "                        for sub in combinations(fset, r))\n", "        region_data[fset] = incr - deduction\n", "\n", "    grouped[\"Data\"] = grouped[\"fkey\"].apply(lambda f: region_data[f])\n", "    df_with_results = grouped\n", "    raw_cats = sorted({e for s in df_with_results[\"Subset\"] for e in json.loads(s)})\n", "\n", "# Recalculate total reach after final merge & data assignment\n", "total_data_reach = df_with_results[\"Data\"].sum()\n", "\n", "\n", "# ── <PERSON><PERSON><PERSON> ──\n", "if len(raw_cats) == 2:\n", "    A, B = raw_cats\n", "    a = region_data[frozenset([A])]\n", "    b = region_data[frozenset([B])]\n", "    ab = region_data.get(frozenset([A, B]), 0)\n", "\n", "    plt.figure(figsize=(6, 8.5))\n", "    plt.figtext(0.5, 0.95, \"Product Overlap Insights\", ha='center', va='top',\n", "                fontsize=22, fontweight='bold', color='#333333')\n", "    plt.figtext(0.5, 0.91, \"Designed by: •  pghorawat@  •  datascience-tools@\",\n", "                ha='center', va='top', fontsize=10, color='#666666')\n", "\n", "    v = venn2((a, b, ab), set_labels=[A.title(), B.title()])\n", "    for id_, val in zip((\"10\", \"01\", \"11\"), (a, b, ab)):\n", "        lbl = v.get_label_by_id(id_)\n", "        if lbl:\n", "            lbl.set_text(fmt_pct(val))\n", "    plt.title(f\"Overlap: {A.title()} vs {B.title()}\")\n", "\n", "elif len(raw_cats) == 3:\n", "    A, B, C = raw_cats\n", "    a   = region_data[frozenset([A])]\n", "    b   = region_data[frozenset([B])]\n", "    c   = region_data[frozenset([C])]\n", "    ab  = region_data.get(frozenset([A, B]), 0)\n", "    ac  = region_data.get(frozenset([A, C]), 0)\n", "    bc  = region_data.get(frozenset([B, C]), 0)\n", "    abc = region_data.get(frozenset([A, B, C]), 0)\n", "\n", "    subsets = (a, b, ab, c, ac, bc, abc)\n", "\n", "    plt.figure(figsize=(8, 10))\n", "    plt.figtext(0.5, 0.95, \"Product Overlap Insights\", ha='center', va='top',\n", "                fontsize=22, fontweight='bold', color='#333333')\n", "    plt.figtext(0.5, 0.91, \"Designed by: •  pghorawat@  •  datascience-tools@\",\n", "                ha='center', va='top', fontsize=10, color='#666666')\n", "\n", "    v = venn3(subsets=subsets, set_labels=[A.title(), B.title(), C.title()])\n", "    ids = [\"100\", \"010\", \"110\", \"001\", \"101\", \"011\", \"111\"]\n", "    for id_, val in zip(ids, subsets):\n", "        lbl = v.get_label_by_id(id_)\n", "        if lbl:\n", "            lbl.set_text(fmt_pct(val))\n", "    plt.title(f\"Overlap: {A.title()} vs {B.title()} vs {C.title()}\")\n", "\n", "elif len(raw_cats) == 1:\n", "    A = raw_cats[0]\n", "    a = region_data[frozenset([A])]\n", "\n", "    plt.figure(figsize=(6, 6))\n", "    plt.title(f\"Reach: {A.title()}\", fontsize=16, weight='bold')\n", "    circle = plt.Circle((0.5, 0.5), 0.3, color='#1f77b4', alpha=0.5)\n", "    plt.gca().add_patch(circle)\n", "    plt.text(0.5, 0.5, fmt_pct(a), ha='center', va='center', fontsize=18, weight='bold', color='white')\n", "    plt.text(0.5, 0.15, A.title(), ha='center', fontsize=12, color='#333333')\n", "    plt.axis('off')\n", "\n", "\n", "else:\n", "    plt.text(0.5, 0.5, f\"Upto 4 categories supported.\\nFound {len(raw_cats)} categories: {raw_cats}\",\n", "             ha='center', va='center', fontsize=12)\n", "    plt.title(\"Unsupported Category Count\")\n", "\n", "plt.show()\n", "\n", "# ── Print Table ──\n", "print(f\"{'Subset':<45} {'Incremental_Reach':>20}  {'Reach(Only This Group)':>25}  {'%Share':>20}\")\n", "print(\"-\" * 115)\n", "\n", "for _, r in df_with_results.iterrows():\n", "    pct = (r['Data'] / total_data_reach) * 100 if total_data_reach else 0\n", "    print(f\"{r['Subset']:<45} {r['Incremental_Reach']:>20,}  {r['Data']:>25,}  {pct:>20.1f}%\")\n", "\n", "\n"], "id": "d3c6932b74ecdcaa"}], "metadata": {"kernelspec": {"display_name": "pgbanjo", "language": "python", "name": "pgbanjo"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}