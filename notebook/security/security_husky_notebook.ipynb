{"cells": [{"cell_type": "markdown", "id": "f748d9f2-b1c6-47c2-bf5c-b1724db4cc73", "metadata": {}, "source": ["# Security AB Husky Notebook"]}, {"cell_type": "code", "execution_count": 7, "id": "fe05ffad-1b5d-4523-a352-47775e709b76", "metadata": {"ExecuteTime": {"end_time": "2024-10-03T02:47:28.829130Z", "start_time": "2024-10-03T02:47:28.819718Z"}}, "outputs": [], "source": ["from __future__ import division, unicode_literals, print_function\n", "import os\n", "from datetime import datetime, timedelta\n", "import pandas as pd\n", "import pandas.io.gbq as gbq\n", "import seaborn as sns\n", "import collections\n", "import logging\n", "import importlib\n", "import sys\n", "logging.basicConfig()\n", "logging.getLogger().setLevel(logging.WARNING)"]}, {"cell_type": "code", "execution_count": 8, "id": "f2f0c1f0-c534-47c5-a186-baf551bc2ea2", "metadata": {"ExecuteTime": {"end_time": "2024-10-03T02:47:38.422301Z", "start_time": "2024-10-03T02:47:37.147402Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:banjo.teams.product.security:Please check the metric file for definition and calculation of each metrics.\n"]}], "source": ["if any(['VELLUM' in k for k in os.environ.keys()]):\n", "    pip_output = !pip install banjo\n", "\n", "# importlib.reload(sys.modules['banjo'])\n", "from banjo import utils\n", "from banjo import abtest # see: go/pya\n", "from banjo.abtest.report import (\n", "    Metric, MetricTable, Report, get_quest_metric_table, CustomReport, \n", "    get_abtest_console_metric_table\n", ")\n", "from banjo.abtest.cohort_report import CohortReport\n", "from banjo.abtest.quest import TIER_ONE\n", "\n", "# importlib.reload(sys.modules['banjo.teams.product'])\n", "try:\n", "    from banjo.teams.product import security\n", "    #import local_banjo.authentication as authentication\n", "except ImportError:\n", "    GIT_DIR = os.path.join(os.environ['HOME'])\n", "    os.chdir(GIT_DIR)\n", "    print(\"Upgrade banjo to 0.14.0+, which packages all these scripts\")\n", "    from banjo.teams.product import security"]}, {"cell_type": "code", "execution_count": 9, "id": "ec1fda01-4025-4494-b24b-65eac5e8974e", "metadata": {"ExecuteTime": {"end_time": "2024-10-03T02:47:39.906604Z", "start_time": "2024-10-03T02:47:39.875060Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:banjo.teams.product.security:Please check the metric file for definition and calculation of each metrics.\n"]}, {"data": {"text/plain": ["<module 'banjo.teams.product.security' from '/Users/<USER>/repos/pyanalytics/banjo/teams/product/security.py'>"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["importlib.reload(security)"]}, {"cell_type": "code", "execution_count": 10, "id": "f0af539d-1ae8-4d35-8e43-0c22a37707d3", "metadata": {"ExecuteTime": {"end_time": "2024-10-03T02:47:40.309538Z", "start_time": "2024-10-03T02:47:40.279475Z"}}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/40/dhx2d3rx4351k1y2d1mzxr6c0000gn/T/ipykernel_92246/1549426864.py:1: DeprecationWarning: Importing display from IPython.core.display is deprecated since IPython 7.14, please import from IPython display\n", "  from IPython.core.display import display, HTML\n"]}, {"data": {"text/html": ["<style>.output_html.rendered_html table { font-size:8pt;}</style>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.core.display import display, HTML\n", "display(HTML(\"<style>.output_html.rendered_html table { font-size:8pt;}</style>\"))"]}, {"cell_type": "code", "execution_count": 11, "id": "b4ee2084-8fae-44bb-a33e-db9467c49f7f", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["STUDY_NAME = 'AUTH_APP_TO_WEB_TOKEN__108730'\n", "STUDY_START_DATE = '20240830' # the start date must match the study (current version) start date in the study config.\n", "STUDY_END_DATE = '20240926'\n", "CONTROL_ID = '1'\n", "TREATMENT_IDS = ['2']\n", "STUDY_TYPE = 'user_level_study' # user_level_study, mobile_device_level_study, web_device_level_study\n", "USER_BREAKDOWN_LIST = [] # select from BREAKDOWN_TABLE defined below\n", "MOBILE_DEVICE_BREAKDOWN_LIST = []\n", "WEB_DEVICE_BREAKDOWN_LIST = []\n", "# CUSTOM_BREAKDOWNS = ['app_engagement_bucket', 'inferred_age_bucket', 'l_90_country', 'device_cluster']\n", "OVERWRITE = False\n", "INCLUDE_DAILY_METRICS = True\n", "MATERIALIZE_MAPPING_TABLE = True\n", "PIVOT_RESULTS = True\n", "DAILY_TREND = False\n", "#BREAKDOWN_TABLE_JOIN_KEY = \"ghost_user_id\"\n", "BQ_PRIORITY = 'BATCH'\n", "STUDY_COHORT_DATE = None\n", "QUANTILES = ['50','90','99']\n", "\n", "# Default is for user level study\n", "METRICS_GROUP_CONFIG = [\n", "    #'sign_in_server_metrics',\n", "    'security_lock_metrics',\n", "    # 'account_takeover_metrics',\n", "    # 'engagement_metrics',\n", "]\n", "\n", "# Mobile device level metrics\n", "MOBILE_DEVICE_METRICS_GROUP_CONFIG = [\n", "    'sign_up_server_metrics',\n", "    'sign_up_server_rub_metrics',\n", "    'sign_up_network_response_metrics',\n", "    'security_ads_metrics',\n", "    'engagement_metrics',\n", "    'friending_metrics'\n", "\n", "]\n", "\n", "# Web device level metrics\n", "WEB_DEVICE_METRICS_GROUP_CONFIG = [\n", "    'sign_in_server_metrics',\n", "]\n", "\n", "# For filtering country breakdown\n", "COUNTRY_LIST = []\n", "\n", "# For app version breakdown\n", "APP_VERSION_LIST = ''\n"]}, {"cell_type": "code", "execution_count": 12, "id": "c3b181d7-581f-4813-b305-ad4b7928d20d", "metadata": {}, "outputs": [], "source": ["if STUDY_TYPE == 'mobile_device_level_study':\n", "    USER_BREAKDOWN_LIST = MOBILE_DEVICE_BREAKDOWN_LIST\n", "    METRICS_GROUP_CONFIG = MOBILE_DEVICE_METRICS_GROUP_CONFIG\n", "elif <PERSON>_TYPE == 'web_device_level_study':\n", "    USER_BREAKDOWN_LIST = WEB_DEVICE_BREAKDOWN_LIST\n", "    METRICS_GROUP_CONFIG = WEB_DEVICE_METRICS_GROUP_CONFIG"]}, {"cell_type": "code", "execution_count": 13, "id": "b96459b2-2d61-4609-b6b6-9beff1e5832e", "metadata": {}, "outputs": [], "source": ["def ensure_list(parameter):\n", "    if isinstance(parameter, list):\n", "        return parameter\n", "    else:\n", "        return [parameter]\n", "\n", "USER_BREAKDOWN_LIST = ensure_list(USER_BREAKDOWN_LIST)   \n", "METRICS_GROUP_CONFIG = ensure_list(METRICS_GROUP_CONFIG)\n", "TREATMENT_IDS = ensure_list(TREATMENT_IDS)\n", "COUNTRY_LIST = ensure_list(COUNTRY_LIST)\n", "\n", "# APP_VERSION_LIST is a string\n", "APP_VERSION_LIST = [item.strip() for item in APP_VERSION_LIST.strip().split(',') if item.strip()]\n"]}, {"cell_type": "code", "execution_count": 8, "id": "652c5fd7-10c3-453f-b339-82eedba92eb6", "metadata": {}, "outputs": [], "source": ["if DAILY_TREND:\n", "    trend = ['trend']\n", "else:\n", "    trend = []"]}, {"cell_type": "code", "execution_count": 33, "id": "bb59b0a6-b7ee-4530-a449-f5f3a925a835", "metadata": {}, "outputs": [], "source": ["# This cell determines the usermap table based on the selected metrics\n", "if STUDY_TYPE == 'mobile_device_level_study':\n", "    ab_dataset=\"abtest_device_usermap_cumulative\"\n", "    unit_id = \"config_device_id\"\n", "    custom_mapping_sql = \"\"\"\n", "    WITH ab_mapping_table AS (\n", "      SELECT\n", "        {unit_id},\n", "        exp_id,\n", "        TIMESTAMP_TRUNC(\n", "          TIMESTAMP_SUB(\n", "            MIN(TIMESTAMP_MILLIS(CAST(initial_timestamp AS INT64))),\n", "            INTERVAL 8 HOUR\n", "          ),\n", "          DAY\n", "        ) AS exposure_ds,\n", "        MIN(TIMESTAMP_MILLIS(CAST(initial_timestamp AS INT64))) AS exposure_ts,\n", "        MIN(app_version) AS app_version,\n", "        MIN(country) AS country ,\n", "        MIN(device_name) AS device_name,\n", "        MIN(device_connectivity) AS device_connectivity,\n", "        MIN(locale) AS locale,\n", "        MIN(os) AS os,\n", "        MIN(os_version) AS os_version,\n", "        MIN(device_cluster) AS device_cluster,\n", "        MIN(country_bucket) AS country_bucket,\n", "        MIN(business_report_region) AS business_report_region,\n", "        MIN(inclusive_region) AS inclusive_region,\n", "        MIN(device_cluster_bucket) AS device_cluster_bucket\n", "      FROM\n", "        `sc-portal.{ab_dataset}.{study_name}__{study_end_date}`\n", "      GROUP BY\n", "        {unit_id}, exp_id\n", "    )\n", "\n", "    SELECT\n", "      ab_mapping_table.*,\n", "    FROM\n", "      ab_mapping_table\n", "    \"\"\"\n", "elif <PERSON>_TYPE == 'web_device_level_study':\n", "    ab_dataset=\"abtest_web_usermap_cumulative\"\n", "    unit_id = \"web_client_id\"\n", "    \n", "    custom_mapping_sql = \"\"\"\n", "    WITH ab_mapping_table AS (\n", "      SELECT\n", "        {unit_id},\n", "        exp_id,\n", "        TIMESTAMP_TRUNC(\n", "          TIMESTAMP_SUB(\n", "            MIN(TIMESTAMP_MILLIS(CAST(initial_timestamp AS INT64))),\n", "            INTERVAL 8 HOUR\n", "          ),\n", "          DAY\n", "        ) AS exposure_ds,\n", "      FROM\n", "        `sc-portal.{ab_dataset}.{study_name}__{study_end_date}`\n", "      GROUP BY\n", "        {unit_id}, exp_id\n", "    )\n", "\n", "    SELECT\n", "      ab_mapping_table.*,\n", "    FROM\n", "      ab_mapping_table\n", "    \"\"\"\n", "    \n", "else:\n", "    # Keep this section for running custom breakdowns locally\n", "    # In Prod, CohortReport is used, custom_mapping_sql is not needed\n", "    ab_dataset=\"usermap_cumulative\"\n", "    unit_id = \"ghost_user_id\"\n", "    custom_mapping_sql = \"\"\"\n", "    WITH ab_mapping_table AS (\n", "      SELECT\n", "        {unit_id},\n", "        exp_id,\n", "        TIMESTAMP_TRUNC(\n", "          TIMESTAMP_SUB(\n", "            MIN(TIMESTAMP_MILLIS(CAST(initial_timestamp AS INT64))),\n", "            INTERVAL 8 HOUR\n", "          ),\n", "          DAY\n", "        ) AS exposure_ds,    \n", "        MIN(TIMESTAMP_MILLIS(CAST(initial_timestamp AS INT64))) AS exposure_ts\n", "        -- Optionally keey any dimensions in the AB mapping table that you want to use.\n", "      FROM\n", "        `sc-portal.{ab_dataset}.{study_name}__{study_end_date}`\n", "      GROUP BY\n", "        {unit_id}, exp_id\n", "    ),\n", "    -- change here\n", "    user_cohort_breakdown_dimensions AS (\n", "      SELECT\n", "        {unit_id},\n", "        device_model\n", "      FROM\n", "        -- For non-engagement breakdowns, you might want join with the dimension table on the end date\n", "        `sc-analytics.report_search.user_cohorts_{study_cohort_date}`\n", "    )\n", "\n", "    SELECT\n", "      ab_mapping_table.*,\n", "      user_cohort_breakdown_dimensions.* except({unit_id})\n", "    FROM\n", "      ab_mapping_table\n", "    LEFT JOIN\n", "      user_cohort_breakdown_dimensions\n", "    USING ({unit_id})\n", "    \n", "    \"\"\""]}, {"cell_type": "code", "execution_count": 34, "id": "cc2399de-bb5d-435f-88d8-1a3d9d726d97", "metadata": {}, "outputs": [], "source": ["# custom_mapping_sql = \"\"\"\n", "# WITH ab_mapping_table AS (\n", "#   SELECT\n", "#     {randomization_id},\n", "#     exp_id,\n", "#     TIMESTAMP_TRUNC(\n", "#       TIMESTAMP_SUB(\n", "#         MIN(TIMESTAMP_MILLIS(CAST(initial_timestamp AS INT64))),\n", "#         INTERVAL 8 HOUR\n", "#       ),\n", "#       DAY\n", "#     ) AS exposure_ds,    \n", "#     MIN(TIMESTAMP_MILLIS(CAST(initial_timestamp AS INT64))) AS exposure_ts\n", "#     -- Optionally keey any dimensions in the AB mapping table that you want to use.\n", "#   FROM\n", "#     `sc-portal.{ab_dataset}.{study_name}__{study_end_date}`\n", "#   GROUP BY\n", "#     {randomization_id}, exp_id\n", "# ),\n", "# -- change here\n", "# my_breakdown_dimensions AS (\n", "#   SELECT\n", "#     ghost_user_id,\n", "#     os_type,\n", "#     LEFT(device_model, 5) AS my_device_model\n", "#   FROM\n", "#     -- For non-engagement breakdowns, you might want join with the dimension table on the end date\n", "#     `sc-analytics.report_search.user_cohorts_{study_start_date}`\n", "# )\n", "\n", "# SELECT\n", "#   ab_mapping_table.*,\n", "#   my_breakdown_dimensions.* EXCEPT (ghost_user_id)\n", "# FROM\n", "#   ab_mapping_table\n", "# LEFT JOIN\n", "#   my_breakdown_dimensions\n", "# USING (ghost_user_id)\n", "# \"\"\""]}, {"cell_type": "code", "execution_count": 11, "id": "a2ea92bf-6aad-4dcb-b348-a8e6fa73bdf0", "metadata": {}, "outputs": [], "source": ["custom_mapping_sql=custom_mapping_sql.format(\n", "    unit_id=unit_id,\n", "    ab_dataset=ab_dataset,\n", "    study_name=STUDY_NAME,\n", "    study_start_date=STUDY_START_DATE,\n", "    study_end_date=STUDY_END_DATE,\n", "    study_cohort_date=STUDY_COHORT_DATE,\n", ")"]}, {"cell_type": "code", "execution_count": 12, "id": "e0d61ec3-58c9-4e39-966d-59537980c7de", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:google.auth._default:No project ID could be determined. Consider running `gcloud config set project` or setting the GOOGLE_CLOUD_PROJECT environment variable\n"]}], "source": ["study_name_sql = \"\"\"\n", "SELECT\n", "    name,\n", "    exp_id\n", "FROM\n", "    [sc-analytics:report_search.ab_console_study_config]\n", "WHERE\n", "    study_name = '{study_name}'\n", "    AND exp_id IN ('{exp_ids}')\n", "\"\"\"\n", "\n", "PROJECT = 'sc-bq-gcs-billingonly'\n", "df_exp_names = utils.gbq.read_gbq(study_name_sql.format(study_name=STUDY_NAME, exp_ids=\"', '\".join([CONTROL_ID] + TREATMENT_IDS)), \n", "                                  project_id=PROJECT,\n", "                                  priority=\"INTERACTIVE\")\n", "\n", "EXP_NAMES = None\n", "if not EXP_NAMES and not df_exp_names.empty:\n", "    EXP_NAMES = dict(zip(df_exp_names.exp_id, df_exp_names.name))"]}, {"cell_type": "code", "execution_count": 13, "id": "692bf4ec-4e80-4dcd-bbae-3cd8006610b1", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:banjo.teams.product.security:Sign-Up Server Metrics are not available for this study type.\n", "WARNING:banjo.teams.product.security:Sign-Up Server Metrics are not available for this study type.\n", "WARNING:banjo.teams.product.security:Sign-Up Network Response Metrics are not available for this study type.\n", "WARNING:google.auth._default:No project ID could be determined. Consider running `gcloud config set project` or setting the GOOGLE_CLOUD_PROJECT environment variable\n", "WARNING:banjo.teams.product.security:Security ADS Metrics are not available for this study type.\n"]}], "source": ["ALL_METRICS_GROUPS = {}\n", "\n", "ALL_METRICS_GROUPS['sign_up_server_metrics'] = [security.sign_up_server(STUDY_TYPE, STUDY_NAME, STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['sign_up_server_rub_metrics'] = [security.sign_up_server_rub(STUDY_TYPE, STUDY_NAME, STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['sign_up_network_response_metrics'] = [security.sign_up_network_response(STUDY_TYPE, STUDY_NAME, STUDY_START_DATE, STUDY_END_DATE)]\n", "# ALL_METRICS_GROUPS['account_recovery_client_metrics'] = [security.account_recovery_client(STUDY_TYPE, STUDY_NAME, STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['security_lock_metrics'] = [security.security_lock(STUDY_TYPE, STUDY_NAME, STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['security_ads_metrics'] = [security.security_ads(STUDY_TYPE, STUDY_NAME, STUDY_START_DATE, STUDY_END_DATE)]\n", "# ALL_METRICS_GROUPS['telephony_cost_metrics'] = [security.telephony_cost(STUDY_TYPE, STUDY_NAME, STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['account_takeover_metrics'] = [security.account_takeover(STUDY_TYPE, STUDY_NAME, STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['sign_in_server_metrics'] = [security.sign_in_server(STUDY_TYPE, STUDY_NAME, STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['engagement_metrics'] = [security.engagement(STUDY_TYPE, STUDY_NAME, STUDY_START_DATE, STUDY_END_DATE)]\n", "ALL_METRICS_GROUPS['friending_metrics'] = [security.friending(STUDY_TYPE, STUDY_NAME, STUDY_START_DATE, STUDY_END_DATE)]\n"]}, {"cell_type": "code", "execution_count": 14, "id": "688eaea9-170d-40aa-bf02-8a617e18f91b", "metadata": {}, "outputs": [], "source": ["metric_tables = [\n", "    metric_table \n", "    for sublist in [ALL_METRICS_GROUPS[mt] for mt in ALL_METRICS_GROUPS if mt in METRICS_GROUP_CONFIG and ALL_METRICS_GROUPS[mt][0] is not None]\n", "    for metric_table in sublist \n", "]"]}, {"cell_type": "code", "execution_count": 35, "id": "ee5e46d0-16eb-4b92-9b88-40f365dbaa16", "metadata": {}, "outputs": [], "source": ["if STUDY_TYPE in [\"mobile_device_level_study\", \"web_device_level_study\"]:\n", "\n", "    report = CustomReport(\n", "        study_name=STUDY_NAME,\n", "        study_start_date=STUDY_START_DATE,\n", "        study_end_date=STUDY_END_DATE,\n", "        metric_tables=metric_tables,\n", "        control_id=CONTROL_ID,\n", "        treatment_ids=TREATMENT_IDS,\n", "        user_group_bys=USER_BREAKDOWN_LIST,\n", "        bq_project='sc-bq-gcs-billingonly',  \n", "        dest_dataset='temp_abtest',\n", "        #quantiles=['90'],   \n", "        custom_mapping_sql=custom_mapping_sql,\n", "        old_mapping_format=False,\n", "        mapping_user_col=unit_id, # config_device_id or web_client_id\n", "        # mapping_for_daily_results='cumulative',\n", "        exp_id_to_name=EXP_NAMES,\n", "        bq_dialect='standard',\n", "        overwrite_mapping_table=True,\n", "        materializing_mapping_table=True\n", "    )\n", "else:\n", "    report = CohortReport(\n", "        study_name=STUDY_NAME,\n", "        study_start_date=STUDY_START_DATE,\n", "        study_end_date=STUDY_END_DATE,\n", "        metric_tables=metric_tables,\n", "        control_id=CONTROL_ID,\n", "        treatment_ids=TREATMENT_IDS,\n", "        user_group_bys=USER_BREAKDOWN_LIST,\n", "        bq_project='sc-bq-gcs-billingonly',  \n", "        dest_dataset='temp_abtest',\n", "        quantiles=QUANTILES,\n", "        materializing_mapping_table=True,\n", "        overwrite_mapping_table=True,  \n", "        exp_id_to_name=EXP_NAMES,\n", "        bq_priority=BQ_PRIORITY,\n", "        cohort_definition_date=STUDY_COHORT_DATE\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "8f75f2c7-1cbf-458a-8493-e97b4fe3f0fc", "metadata": {}, "outputs": [], "source": ["if STUDY_TYPE in [\"mobile_device_level_study\", \"user_level_study\"]:\n", "\n", "    group_filters = {}\n", "    \n", "    country_selected = any([b in ['country', 'study_country', 'l_90_country'] for b in USER_BREAKDOWN_LIST])\n", "    app_version_selected = any([b in ['study_app_version'] for b in USER_BREAKDOWN_LIST])\n", "    if country_selected:\n", "        if len(COUNTRY_LIST) == 0:\n", "            query=\"\"\"\n", "            SELECT \n", "              country,\n", "              COUNT(DISTINCT {unit_id}) AS cnt\n", "            FROM\n", "              `sc-portal.{ab_dataset}.{study_name}__{study_end_date}`\n", "            GROUP BY \n", "              ALL\n", "            ORDER BY \n", "              cnt DESC\n", "            LIMIT 20\n", "            \"\"\".format(\n", "                unit_id=unit_id,\n", "                ab_dataset=ab_dataset,\n", "                study_name=STUDY_NAME,\n", "                study_end_date=STUDY_END_DATE\n", "            )\n", "        \n", "            df_top_country=utils.gbq.read_gbq(query, project_id=PROJECT, priority=\"INTERACTIVE\", dialect='standard')\n", "    \n", "            COUNTRY_LIST = df_top_country.country.tolist()\n", "    \n", "        if len(COUNTRY_LIST) > 0:\n", "            group_filters['country'] = COUNTRY_LIST\n", "            group_filters['study_country'] = COUNTRY_LIST\n", "            group_filters['l_90_country'] = COUNTRY_LIST\n", "    \n", "    if app_version_selected:\n", "        if len(APP_VERSION_LIST) == 0:\n", "            query=\"\"\"\n", "            SELECT \n", "              app_version,\n", "              COUNT(DISTINCT {unit_id}) AS cnt\n", "            FROM\n", "              `sc-portal.{ab_dataset}.{study_name}__{study_end_date}`\n", "            GROUP BY \n", "              ALL\n", "            ORDER BY \n", "              cnt DESC\n", "            LIMIT 20\n", "            \"\"\".format(\n", "                unit_id=unit_id,\n", "                ab_dataset=ab_dataset,\n", "                study_name=STUDY_NAME,\n", "                study_end_date=STUDY_END_DATE\n", "            )\n", "    \n", "            df_top_app_version=utils.gbq.read_gbq(query, project_id=PROJECT, priority=\"INTERACTIVE\", dialect='standard')\n", "        \n", "            APP_VERSION_LIST = df_top_app_version.app_version.tolist()\n", "        \n", "        if len(APP_VERSION_LIST) > 0:\n", "            group_filters['app_version'] = APP_VERSION_LIST\n", "            group_filters['study_app_version'] = APP_VERSION_LIST"]}, {"cell_type": "code", "execution_count": 36, "id": "eb16a658-99d9-449f-b359-240784395aab", "metadata": {}, "outputs": [], "source": ["report.execute(\n", "    overwrite=True,\n", "    group_filters=group_filters\n", ")"]}, {"cell_type": "code", "execution_count": 17, "id": "3cb417e0-ac6d-4725-a1a4-4c7afa78ade9", "metadata": {}, "outputs": [], "source": ["report.configurations['pivot_table'] = PIVOT_RESULTS\n", "report.configurations['stat_fmtr'] = (\"{pct_diff:,.2f}% ({avg_control:,.4f}→{avg_treatment:,.4f}, {p_value_formatted})\")"]}, {"cell_type": "code", "execution_count": 18, "id": "923f3d78-206d-4e24-b510-cd279833b10b", "metadata": {}, "outputs": [], "source": ["# Sign-Up Metrics\n", "sign_up_server_metrics_report = [m for mt in ALL_METRICS_GROUPS['sign_up_server_metrics'] if mt is not None for m in mt.cumulative_metrics ] \n", "sign_up_server_rub_metrics_report = [m for mt in ALL_METRICS_GROUPS['sign_up_server_rub_metrics'] if mt is not None for m in mt.cumulative_metrics if 'sign_up_result' in m ]\n", "sign_up_ads_defense_metrics_report = [m for mt in ALL_METRICS_GROUPS['sign_up_server_rub_metrics'] if mt is not None for m in mt.cumulative_metrics if 'sign_up_blocked_by_ads_defense' in m]\n", "sign_up_safetynet_metrics_report = [m for mt in ALL_METRICS_GROUPS['sign_up_server_rub_metrics'] if mt is not None for m in mt.cumulative_metrics if 'sign_up_safetynet' in m]\n", "sign_up_key_attestation_metrics_report = [m for mt in ALL_METRICS_GROUPS['sign_up_server_rub_metrics'] if mt is not None for m in mt.cumulative_metrics if 'sign_up_key_attestation' in m]\n", "sign_up_play_integrity_metrics_report = [m for mt in ALL_METRICS_GROUPS['sign_up_server_rub_metrics'] if mt is not None for m in mt.cumulative_metrics if 'sign_up_play_integrity' in m]\n", "sign_up_app_attest_metrics_report = [m for mt in ALL_METRICS_GROUPS['sign_up_server_rub_metrics'] if mt is not None for m in mt.cumulative_metrics if 'sign_up_app_attest' in m]\n", "sign_up_network_response_metrics_report = [m for mt in ALL_METRICS_GROUPS['sign_up_network_response_metrics'] if mt is not None for m in mt.cumulative_metrics ]\n", "\n", "# Sign-In Metrics\n", "sign_in_server_metrics_report = [m for mt in ALL_METRICS_GROUPS['sign_in_server_metrics'] if mt is not None for m in mt.cumulative_metrics ]\n", "\n", "# Account Recovery Metrics\n", "# account_recovery_client_metrics_report = [m for mt in ALL_METRICS_GROUPS['account_recovery_client_metrics'] if mt is not None for m in mt.cumulative_metrics ]\n", "\n", "# Security Metrics\n", "security_lock_metrics_report = [m for mt in ALL_METRICS_GROUPS['security_lock_metrics']  if mt is not None for m in mt.cumulative_metrics]\n", "security_ads_metrics_report = [m for mt in ALL_METRICS_GROUPS['security_ads_metrics']  if mt is not None for m in mt.cumulative_metrics]\n", "\n", "# ATO Metrics\n", "account_takeover_metrics_report = [m for mt in ALL_METRICS_GROUPS['account_takeover_metrics'] if mt is not None for m in mt.cumulative_metrics ]\n", "\n", "# telephony_cost_metrics_report = [m for mt in ALL_METRICS_GROUPS['telephony_cost_metrics']  if mt is not None for m in mt.cumulative_metrics]\n", "\n", "# Engagement Metrics\n", "engagement_metrics_report = [m for mt in ALL_METRICS_GROUPS['engagement_metrics'] if mt is not None for m in mt.cumulative_metrics ]\n", "\n", "# Friending Metrics\n", "friending_metrics_report = [m for mt in ALL_METRICS_GROUPS['friending_metrics'] if mt is not None for m in mt.cumulative_metrics ]\n"]}, {"cell_type": "code", "execution_count": 19, "id": "f07289d2-4c9f-4aab-bbb7-18859dbad74a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:google.auth._default:No project ID could be determined. Consider running `gcloud config set project` or setting the GOOGLE_CLOUD_PROJECT environment variable\n"]}], "source": ["# This code cell is for getting necessary data to compute rollout impact.\n", "\n", "    \n", "def get_study_info(study_name, study_end_date):\n", "    sql = f\"\"\"\n", "    with study_config as \n", "    (\n", "    SELECT  \n", "    *\n", "    FROM `sc-analytics.report_search.ab_console_study_config` \n", "    where study_name = '{study_name}'\n", "    ), \n", "\n", "    study_info as \n", "    (\n", "    SELECT  \n", "    *\n", "    FROM `sc-portal.ab_metric_impact.study_info_{study_end_date}`\n", "    where study_name = '{study_name}'\n", "    )\n", "\n", "    select \n", "    a.*, \n", "    b.user_slice_range_start, \n", "    b.user_slice_range_end,\n", "    b.user_slice_range_end - b.user_slice_range_start as user_slice_range,\n", "    (a.traffic / 100) * ((b.user_slice_range_end - b.user_slice_range_start) / 100) as pct_population\n", "    from study_config a\n", "    left join study_info b\n", "    on CAST(a.study_version AS STRING) = CAST(b.study_version AS STRING)\n", "    \"\"\"\n", "    df = utils.gbq.read_gbq(sql, \n", "                            project_id=PROJECT,\n", "                            dialect='standard',\n", "                            priority=\"INTERACTIVE\")\n", "    \n", "    return df\n", "\n", "\n", "def get_rollout_impact(df_metric, treatment_target_ratio, control_target_ratio, n_days):\n", "    assert df_metric.shape[0] == 1\n", "    \n", "    df = df_metric.copy()\n", "    \n", "    avg_c = df['avg_control'].values[0]\n", "    avg_t = df['avg_treatment'].values[0]\n", "    n_c = df['count_control'].values[0]\n", "    n_t = df['count_treatment'].values[0]\n", "    \n", "    rollout_impact = (avg_t - avg_c) * (n_c + n_t) / (treatment_target_ratio + control_target_ratio)\n", "    rollout_impact_daily = rollout_impact / n_days\n", "    \n", "    print(f\"(Absolute) Rollout Impact (Experiment Period): {round(rollout_impact, 2)}\")\n", "    print(f\"(Absolute) Rollout Impact (Daily): {round(rollout_impact_daily, 2)}\")\n", "    print(f\"(Absolute) Rollout Impact (Annually): {round(rollout_impact_daily * 365, 2)}\")\n", "    \n", "\n", "def display_rollout_impact(df_study_info, df_results, metric_list):\n", "    n_days = (datetime.strptime(STUDY_END_DATE, '%Y%m%d') - datetime.strptime(STUDY_START_DATE, '%Y%m%d')).days + 1\n", "    \n", "    for metric in metrics_rollout_impact:\n", "        for treatment_id in TREATMENT_IDS:\n", "            print(\"=\" * 50)\n", "            print(metric)\n", "            print(f\"Treatment {treatment_id}\")\n", "            try:\n", "                select_rows = (df_results['metric'] == metric) & (df_results['treatment_id'] == treatment_id)\n", "                df_results_selected = df_results.loc[select_rows][['count_control', 'sum_control', 'count_treatment', 'sum_treatment', 'avg_control', 'avg_treatment']]\n", "\n", "                treatment_target_ratio = df_study_info.loc[df_study_info['exp_id'] == treatment_id]['pct_population'].values[0]\n", "                control_target_ratio = df_study_info.loc[df_study_info['exp_id'] == CONTROL_ID]['pct_population'].values[0]\n", "\n", "                get_rollout_impact(df_results_selected, treatment_target_ratio, control_target_ratio, n_days)\n", "            except:\n", "                print(f\"Rollout impact estimation failed for {metric}\")\n", "                \n", "\n", "df_study_info = get_study_info(STUDY_NAME, STUDY_END_DATE)\n"]}, {"cell_type": "code", "execution_count": 20, "id": "6c9a1969-2e3d-4f38-99be-ac65c5662e65", "metadata": {}, "outputs": [{"data": {"text/html": ["<h3 >Study Name: AUTH_APP_TO_WEB_TOKEN__108730</h3>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<strong >Study Type: user_level_study</strong>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<strong >Control ID: 1</strong>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<strong >Treatment ID(s): 2</strong>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<strong >Analysis period: 20240830 - 20240926</strong>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<strong >Breakdowns: <br> app_l7</strong>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["abtest.report.notebook_print(\"Study Name: {}\".format(report.study_name), \"h3\")\n", "abtest.report.notebook_print(\"Study Type: {}\".format(STUDY_TYPE), \"strong\")\n", "abtest.report.notebook_print(\"Control ID: {}\".format(report.control_id), \"strong\")\n", "abtest.report.notebook_print(\"Treatment ID(s): {}\".format(\", \".join(report.treatment_ids)), \"strong\")\n", "abtest.report.notebook_print(\n", "    \"Analysis period: {} - {}\".format(report.analysis_start_date, report.study_end_date), \n", "    \"strong\"\n", ")\n", "abtest.report.notebook_print(\n", "    \"Breakdowns: <br> {}\".format(\"<br>\".join(\", \".join(breakdown) for breakdown in report.user_group_by_list)), \n", "    \"strong\"\n", ")"]}, {"cell_type": "code", "execution_count": 21, "id": "60b183e9-38f0-4164-b8e7-5dc300461297", "metadata": {}, "outputs": [{"data": {"text/html": ["<h2 >Sample Sizes</h2>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Overall</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Comparison of user overall (cumulative) metric values during the study period</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p ><style type=\"text/css\">\n", "#T_ad1dc_row0_col0 {\n", "  color: white;\n", "  font-weight: bold;\n", "  background-color: #99CC99;\n", "}\n", "</style>\n", "<table id=\"T_ad1dc\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level0\" >control_id</th>\n", "      <th id=\"T_ad1dc_level0_col0\" class=\"col_heading level0 col0\" >1 (Control)</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level1\" >treatment_id</th>\n", "      <th id=\"T_ad1dc_level1_col0\" class=\"col_heading level1 col0\" >2 (Enabled)</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level2\" >Sample Sizes</th>\n", "      <th id=\"T_ad1dc_level2_col0\" class=\"col_heading level2 col0\" >Treatment=246,642<br>\n", "Control=247,240<br>\n", "No SSPM (p=0.3948)</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Date</th>\n", "      <th class=\"index_name level1\" >Metric</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_ad1dc_level0_row0\" class=\"row_heading level0 row0\" >08/30 to 09/26</th>\n", "      <th id=\"T_ad1dc_level1_row0\" class=\"row_heading level1 row0\" >security_lock_account_count</th>\n", "      <td id=\"T_ad1dc_row0_col0\" class=\"data row0 col0\" >Users in control: 247,240 <br>Users in treatment: 246,642</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Breakdown by user groups: app_l7</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Comparison of user overall (cumulative) metric values during the study period</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p ><style type=\"text/css\">\n", "#T_e5d9b_row0_col7 {\n", "  color: white;\n", "  font-weight: bold;\n", "  background-color: #99CC99;\n", "}\n", "</style>\n", "<table id=\"T_e5d9b\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level0\" >control_id</th>\n", "      <th id=\"T_e5d9b_level0_col0\" class=\"col_heading level0 col0\" colspan=\"9\">1 (Control)</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level1\" >treatment_id</th>\n", "      <th id=\"T_e5d9b_level1_col0\" class=\"col_heading level1 col0\" colspan=\"9\">2 (Enabled)</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level2\" >app_l7</th>\n", "      <th id=\"T_e5d9b_level2_col0\" class=\"col_heading level2 col0\" >0</th>\n", "      <th id=\"T_e5d9b_level2_col1\" class=\"col_heading level2 col1\" >1</th>\n", "      <th id=\"T_e5d9b_level2_col2\" class=\"col_heading level2 col2\" >2</th>\n", "      <th id=\"T_e5d9b_level2_col3\" class=\"col_heading level2 col3\" >3</th>\n", "      <th id=\"T_e5d9b_level2_col4\" class=\"col_heading level2 col4\" >4</th>\n", "      <th id=\"T_e5d9b_level2_col5\" class=\"col_heading level2 col5\" >5</th>\n", "      <th id=\"T_e5d9b_level2_col6\" class=\"col_heading level2 col6\" >6</th>\n", "      <th id=\"T_e5d9b_level2_col7\" class=\"col_heading level2 col7\" >7</th>\n", "      <th id=\"T_e5d9b_level2_col8\" class=\"col_heading level2 col8\" >UNKNOWN</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level3\" >Sample Sizes</th>\n", "      <th id=\"T_e5d9b_level3_col0\" class=\"col_heading level3 col0\" >Treatment=388<br>\n", "Control=352<br>\n", "No SSPM (p=0.1852)</th>\n", "      <th id=\"T_e5d9b_level3_col1\" class=\"col_heading level3 col1\" >Treatment=12,836<br>\n", "Control=13,195<br>\n", "No SSPM (p=0.0261)</th>\n", "      <th id=\"T_e5d9b_level3_col2\" class=\"col_heading level3 col2\" >Treatment=8,771<br>\n", "Control=8,877<br>\n", "No SSPM (p=0.4249)</th>\n", "      <th id=\"T_e5d9b_level3_col3\" class=\"col_heading level3 col3\" >Treatment=7,758<br>\n", "Control=7,893<br>\n", "No SSPM (p=0.2805)</th>\n", "      <th id=\"T_e5d9b_level3_col4\" class=\"col_heading level3 col4\" >Treatment=7,870<br>\n", "Control=7,708<br>\n", "No SSPM (p=0.1943)</th>\n", "      <th id=\"T_e5d9b_level3_col5\" class=\"col_heading level3 col5\" >Treatment=8,808<br>\n", "Control=8,742<br>\n", "No SSPM (p=0.6183)</th>\n", "      <th id=\"T_e5d9b_level3_col6\" class=\"col_heading level3 col6\" >Treatment=12,625<br>\n", "Control=12,596<br>\n", "No SSPM (p=0.8551)</th>\n", "      <th id=\"T_e5d9b_level3_col7\" class=\"col_heading level3 col7\" >Treatment=150,713<br>\n", "Control=151,086<br>\n", "No SSPM (p=0.4972)</th>\n", "      <th id=\"T_e5d9b_level3_col8\" class=\"col_heading level3 col8\" >Treatment=36,873<br>\n", "Control=36,791<br>\n", "No SSPM (p=0.7626)</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Date</th>\n", "      <th class=\"index_name level1\" >Metric</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "      <th class=\"blank col1\" >&nbsp;</th>\n", "      <th class=\"blank col2\" >&nbsp;</th>\n", "      <th class=\"blank col3\" >&nbsp;</th>\n", "      <th class=\"blank col4\" >&nbsp;</th>\n", "      <th class=\"blank col5\" >&nbsp;</th>\n", "      <th class=\"blank col6\" >&nbsp;</th>\n", "      <th class=\"blank col7\" >&nbsp;</th>\n", "      <th class=\"blank col8\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_e5d9b_level0_row0\" class=\"row_heading level0 row0\" >08/30 to 09/26</th>\n", "      <th id=\"T_e5d9b_level1_row0\" class=\"row_heading level1 row0\" >security_lock_account_count</th>\n", "      <td id=\"T_e5d9b_row0_col0\" class=\"data row0 col0\" >Users in control: 352 <br>Users in treatment: 388</td>\n", "      <td id=\"T_e5d9b_row0_col1\" class=\"data row0 col1\" >Users in control: 13,195 <br>Users in treatment: 12,836</td>\n", "      <td id=\"T_e5d9b_row0_col2\" class=\"data row0 col2\" >Users in control: 8,877 <br>Users in treatment: 8,771</td>\n", "      <td id=\"T_e5d9b_row0_col3\" class=\"data row0 col3\" >Users in control: 7,893 <br>Users in treatment: 7,758</td>\n", "      <td id=\"T_e5d9b_row0_col4\" class=\"data row0 col4\" >Users in control: 7,708 <br>Users in treatment: 7,870</td>\n", "      <td id=\"T_e5d9b_row0_col5\" class=\"data row0 col5\" >Users in control: 8,742 <br>Users in treatment: 8,808</td>\n", "      <td id=\"T_e5d9b_row0_col6\" class=\"data row0 col6\" >Users in control: 12,596 <br>Users in treatment: 12,625</td>\n", "      <td id=\"T_e5d9b_row0_col7\" class=\"data row0 col7\" >Users in control: 151,086 <br>Users in treatment: 150,713</td>\n", "      <td id=\"T_e5d9b_row0_col8\" class=\"data row0 col8\" >Users in control: 36,791 <br>Users in treatment: 36,873</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["[[{'table': <pandas.io.formats.style.Styler at 0x7f8b491f9400>}, None],\n", " [{'table': <pandas.io.formats.style.Styler at 0x7f8b4924ba90>}, None]]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["report.ab_printer.print_text(\"Sample Sizes\", 'h2')\n", "report.generate_report(\n", "    format_pvalue=True,\n", "    display_config={'cumulative': ['table'], 'daily': trend},\n", "    metric_filters={'metrics': [metric_tables[0].metrics[0].col], 'regex': False},\n", "    stat_fmtr=\"Users in control: {count_control:,.0f} <br>Users in treatment: {count_treatment:,.0f}\", \n", ")\n", "\n"]}, {"cell_type": "code", "execution_count": 22, "id": "09746a6d-4dea-4a58-84d5-62e8c88e7eb8", "metadata": {}, "outputs": [], "source": ["if 'sign_up_server_metrics' in METRICS_GROUP_CONFIG: \n", "    report.ab_printer.print_text(\"Sign-Up Server Metrics\", 'h2')\n", "    report.ab_printer.print_text(\"Sign-Up Password Step Server Metrics\", 'h3')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters={'metrics': sign_up_server_metrics_report}\n", "    )\n", "    report.ab_printer.print_text(\"\"\"Note: The metrics above are based on REGISTER_WITH_USERNAME_PASSWORD_ATTEMPT event and the registration_result field\n", "    \"\"\", 'h4')\n", "else: \n", "    pass\n", "    \n"]}, {"cell_type": "code", "execution_count": 23, "id": "23e8ad64-a604-4f30-83fd-e02915e1600c", "metadata": {}, "outputs": [], "source": ["if 'sign_up_server_rub_metrics' in METRICS_GROUP_CONFIG:\n", "    report.ab_printer.print_text(\"Sign-Up Password Step Server Metrics (register_user_basic)\", 'h3')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters={'metrics': sign_up_server_rub_metrics_report}\n", "    )\n", "    report.ab_printer.print_text(\"\"\"Note: The metrics above are based on event in register_user_basic (rub) table\n", "    \"\"\", 'h4')\n", "    \n", "    \n", "    report.ab_printer.print_text(\"Sign-Up ADS Defense Metrics\", 'h3')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters={'metrics': sign_up_ads_defense_metrics_report}\n", "    )\n", "    report.ab_printer.print_text(\"\"\"Note: The metrics above are based on event in register_user_basic (rub) table\n", "    \"\"\", 'h4')\n", "    \n", "    \n", "    report.ab_printer.print_text(\"Sign-Up SafetyNet Metrics\", 'h3')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters={'metrics': sign_up_safetynet_metrics_report}\n", "    )\n", "    report.ab_printer.print_text(\"\"\"Note: The metrics above are based on event in register_user_basic (rub) table\n", "    \"\"\", 'h4')\n", "    \n", "    report.ab_printer.print_text(\"Sign-Up Key Attestation Metrics\", 'h3')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters={'metrics': sign_up_key_attestation_metrics_report}\n", "    )\n", "    report.ab_printer.print_text(\"\"\"Note: The metrics above are based on event in register_user_basic (rub) table\n", "    \"\"\", 'h4')\n", "    \n", "    \n", "    report.ab_printer.print_text(\"Sign-Up Play Integrity Metrics\", 'h3')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters={'metrics': sign_up_play_integrity_metrics_report}\n", "    )\n", "    report.ab_printer.print_text(\"\"\"Note: The metrics above are based on event in register_user_basic (rub) table\n", "    \"\"\", 'h4')\n", "\n", "    report.ab_printer.print_text(\"Sign-Up App Attestation Metrics\", 'h3')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters={'metrics': sign_up_app_attest_metrics_report}\n", "    )\n", "    report.ab_printer.print_text(\"\"\"Note: The metrics above are based on event in register_user_basic (rub) table\n", "    \"\"\", 'h4')\n", "else: \n", "    pass"]}, {"cell_type": "code", "execution_count": 24, "id": "5c804036-f44a-4303-b0f1-90536c7db910", "metadata": {}, "outputs": [], "source": ["if 'sign_up_network_response_metrics' in METRICS_GROUP_CONFIG: \n", "    report.ab_printer.print_text(\"Sign-Up Network Response Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters={'metrics': sign_up_network_response_metrics_report}\n", "    )\n", "else: \n", "    pass"]}, {"cell_type": "code", "execution_count": 25, "id": "bc91a884-0a88-4caa-83ca-087701cd44bf", "metadata": {}, "outputs": [], "source": ["if 'sign_in_server_metrics' in METRICS_GROUP_CONFIG: \n", "    report.ab_printer.print_text(\"Sign-In Server Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters={'metrics': sign_in_server_metrics_report}\n", "    )\n", "else: \n", "    pass"]}, {"cell_type": "code", "execution_count": 26, "id": "375f7d7b-38ec-4517-b27f-2da42dd21e65", "metadata": {}, "outputs": [], "source": ["if 'account_takeover_metrics' in METRICS_GROUP_CONFIG: \n", "    report.ab_printer.print_text(\"Account Takeover Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters={'metrics': account_takeover_metrics_report}\n", "    )\n", "else: \n", "    pass"]}, {"cell_type": "code", "execution_count": 27, "id": "4085261b-942d-4d4f-8617-35a26aaed527", "metadata": {}, "outputs": [], "source": ["# if 'account_recovery_client_metrics' in METRICS_GROUP_CONFIG: \n", "#     report.ab_printer.print_text(\"Account Recovery Client Metrics\", 'h2')\n", "#     report.generate_report(\n", "#         format_pvalue=True,\n", "#         extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "#         display_config={'cumulative': ['table'], 'daily': trend},\n", "#         metric_filters={'metrics': account_recovery_client_metrics_report}\n", "#     )\n", "# else: \n", "#     pass"]}, {"cell_type": "code", "execution_count": 28, "id": "5e3dc58f-3a0e-4249-b179-f633bf817243", "metadata": {}, "outputs": [{"data": {"text/html": ["<h2 >Security Lock Metrics</h2>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<b >For a mobile device level study, we use the client_id <-> ghost_user_id mapping table to compute the metrics. security_lock_account_count is number of associated ghost_user_id that were locked. security_lock_uu is number of devices that have 1+ locked ghost_user_id's\n", "    <br>\n", "    <br>\n", "    For a user level study, account count and UU metric should be the same.\n", "    </b>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Overall</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Comparison of user overall (cumulative) metric values during the study period</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p ><style type=\"text/css\">\n", "#T_de8b4_row0_col0, #T_de8b4_row1_col0, #T_de8b4_row2_col0, #T_de8b4_row4_col0 {\n", "  color: white;\n", "  font-weight: bold;\n", "  background-color: #99CC99;\n", "}\n", "#T_de8b4_row15_col0 {\n", "  color: white;\n", "  font-weight: bold;\n", "  background-color: #66B366;\n", "}\n", "</style>\n", "<table id=\"T_de8b4\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level0\" >control_id</th>\n", "      <th id=\"T_de8b4_level0_col0\" class=\"col_heading level0 col0\" >1 (Control)</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level1\" >treatment_id</th>\n", "      <th id=\"T_de8b4_level1_col0\" class=\"col_heading level1 col0\" >2 (Enabled)</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level2\" >Sample Sizes</th>\n", "      <th id=\"T_de8b4_level2_col0\" class=\"col_heading level2 col0\" >Treatment=246,642<br>\n", "Control=247,240<br>\n", "No SSPM (p=0.3948)</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Date</th>\n", "      <th class=\"index_name level1\" >Metric</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_de8b4_level0_row0\" class=\"row_heading level0 row0\" rowspan=\"16\">08/30 to 09/26</th>\n", "      <th id=\"T_de8b4_level1_row0\" class=\"row_heading level1 row0\" >security_lock_account_count</th>\n", "      <td id=\"T_de8b4_row0_col0\" class=\"data row0 col0\" >-8.64% (0.0058→0.0053, < 0.05)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_de8b4_level1_row1\" class=\"row_heading level1 row1\" >security_lock_uu</th>\n", "      <td id=\"T_de8b4_row1_col0\" class=\"data row1 col0\" >-8.79% (0.0057→0.0052, < 0.05)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_de8b4_level1_row2\" class=\"row_heading level1 row2\" >security_perm_lock_uu</th>\n", "      <td id=\"T_de8b4_row2_col0\" class=\"data row2 col0\" >-7.72% (0.0054→0.0050, < 0.05)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_de8b4_level1_row3\" class=\"row_heading level1 row3\" >security_temp_lock_uu</th>\n", "      <td id=\"T_de8b4_row3_col0\" class=\"data row3 col0\" >-25.96% (0.0004→0.0003, < 0.1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_de8b4_level1_row4\" class=\"row_heading level1 row4\" >security_l_locked_user_reputation_high_bad_actor_score_perm_lock_uu</th>\n", "      <td id=\"T_de8b4_row4_col0\" class=\"data row4 col0\" >-9.41% (0.0035→0.0032, < 0.05)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_de8b4_level1_row5\" class=\"row_heading level1 row5\" >security_l_lock_excessive_registration_perm_lock_uu</th>\n", "      <td id=\"T_de8b4_row5_col0\" class=\"data row5 col0\" >-6.02% (0.0003→0.0002, ≥ 0.1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_de8b4_level1_row6\" class=\"row_heading level1 row6\" >security_l_1d_lock_3pa_lock_uu</th>\n", "      <td id=\"T_de8b4_row6_col0\" class=\"data row6 col0\" >-34.22% (0.0001→0.0001, ≥ 0.1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_de8b4_level1_row7\" class=\"row_heading level1 row7\" >security_l_locked_TS_DRUGS_perm_lock_uu</th>\n", "      <td id=\"T_de8b4_row7_col0\" class=\"data row7 col0\" >-7.28% (0.0002→0.0002, ≥ 0.1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_de8b4_level1_row8\" class=\"row_heading level1 row8\" >security_l_locked_account_sanitization_p_credential_stuffing_lock_uu</th>\n", "      <td id=\"T_de8b4_row8_col0\" class=\"data row8 col0\" >0.24% (0.0001→0.0001, ≥ 0.1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_de8b4_level1_row9\" class=\"row_heading level1 row9\" >security_l_locked_TS_NCMEC_perm_lock_uu</th>\n", "      <td id=\"T_de8b4_row9_col0\" class=\"data row9 col0\" >46.78% (0.0001→0.0002, ≥ 0.1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_de8b4_level1_row10\" class=\"row_heading level1 row10\" >security_l_locked_TS_SEXUAL_NUDITY_PORN_perm_lock_uu</th>\n", "      <td id=\"T_de8b4_row10_col0\" class=\"data row10 col0\" >2.75% (0.0002→0.0002, ≥ 0.1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_de8b4_level1_row11\" class=\"row_heading level1 row11\" >security_l_locked_TS_COMMERCIAL_SEX_TRANSACTIONS_perm_lock_uu</th>\n", "      <td id=\"T_de8b4_row11_col0\" class=\"data row11 col0\" >-36.80% (0.0002→0.0001, < 0.1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_de8b4_level1_row12\" class=\"row_heading level1 row12\" >security_l_lock_high_block_10_days_60_ng_bd_ru_perm_lock_uu</th>\n", "      <td id=\"T_de8b4_row12_col0\" class=\"data row12 col0\" >-14.08% (0.0001→0.0000, ≥ 0.1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_de8b4_level1_row13\" class=\"row_heading level1 row13\" >security_l_locked_striker_TS_SEXUAL_NUDITY_PORN_perm_lock_uu</th>\n", "      <td id=\"T_de8b4_row13_col0\" class=\"data row13 col0\" >-2.80% (0.0001→0.0001, ≥ 0.1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_de8b4_level1_row14\" class=\"row_heading level1 row14\" >security_l_7d_lock_3pa_lock_uu</th>\n", "      <td id=\"T_de8b4_row14_col0\" class=\"data row14 col0\" >-8.11% (0.0001→0.0001, ≥ 0.1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_de8b4_level1_row15\" class=\"row_heading level1 row15\" >security_l_locked_striker_TS_SEXUAL_HARASSMENT_perm_lock_uu</th>\n", "      <td id=\"T_de8b4_row15_col0\" class=\"data row15 col0\" >-48.76% (0.0002→0.0001, < 0.01)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Breakdown by user groups: app_l7</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h5 >Comparison of user overall (cumulative) metric values during the study period</h5>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<p ><style type=\"text/css\">\n", "#T_6d92d_row0_col7, #T_6d92d_row3_col7, #T_6d92d_row6_col7, #T_6d92d_row11_col7 {\n", "  color: white;\n", "  font-weight: bold;\n", "  background-color: #99CC99;\n", "}\n", "</style>\n", "<table id=\"T_6d92d\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level0\" >control_id</th>\n", "      <th id=\"T_6d92d_level0_col0\" class=\"col_heading level0 col0\" colspan=\"9\">1 (Control)</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level1\" >treatment_id</th>\n", "      <th id=\"T_6d92d_level1_col0\" class=\"col_heading level1 col0\" colspan=\"9\">2 (Enabled)</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level2\" >app_l7</th>\n", "      <th id=\"T_6d92d_level2_col0\" class=\"col_heading level2 col0\" >0</th>\n", "      <th id=\"T_6d92d_level2_col1\" class=\"col_heading level2 col1\" >1</th>\n", "      <th id=\"T_6d92d_level2_col2\" class=\"col_heading level2 col2\" >2</th>\n", "      <th id=\"T_6d92d_level2_col3\" class=\"col_heading level2 col3\" >3</th>\n", "      <th id=\"T_6d92d_level2_col4\" class=\"col_heading level2 col4\" >4</th>\n", "      <th id=\"T_6d92d_level2_col5\" class=\"col_heading level2 col5\" >5</th>\n", "      <th id=\"T_6d92d_level2_col6\" class=\"col_heading level2 col6\" >6</th>\n", "      <th id=\"T_6d92d_level2_col7\" class=\"col_heading level2 col7\" >7</th>\n", "      <th id=\"T_6d92d_level2_col8\" class=\"col_heading level2 col8\" >UNKNOWN</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"blank\" >&nbsp;</th>\n", "      <th class=\"index_name level3\" >Sample Sizes</th>\n", "      <th id=\"T_6d92d_level3_col0\" class=\"col_heading level3 col0\" >Treatment=388<br>\n", "Control=352<br>\n", "No SSPM (p=0.1852)</th>\n", "      <th id=\"T_6d92d_level3_col1\" class=\"col_heading level3 col1\" >Treatment=12,836<br>\n", "Control=13,195<br>\n", "No SSPM (p=0.0261)</th>\n", "      <th id=\"T_6d92d_level3_col2\" class=\"col_heading level3 col2\" >Treatment=8,771<br>\n", "Control=8,877<br>\n", "No SSPM (p=0.4249)</th>\n", "      <th id=\"T_6d92d_level3_col3\" class=\"col_heading level3 col3\" >Treatment=7,758<br>\n", "Control=7,893<br>\n", "No SSPM (p=0.2805)</th>\n", "      <th id=\"T_6d92d_level3_col4\" class=\"col_heading level3 col4\" >Treatment=7,870<br>\n", "Control=7,708<br>\n", "No SSPM (p=0.1943)</th>\n", "      <th id=\"T_6d92d_level3_col5\" class=\"col_heading level3 col5\" >Treatment=8,808<br>\n", "Control=8,742<br>\n", "No SSPM (p=0.6183)</th>\n", "      <th id=\"T_6d92d_level3_col6\" class=\"col_heading level3 col6\" >Treatment=12,625<br>\n", "Control=12,596<br>\n", "No SSPM (p=0.8551)</th>\n", "      <th id=\"T_6d92d_level3_col7\" class=\"col_heading level3 col7\" >Treatment=150,713<br>\n", "Control=151,086<br>\n", "No SSPM (p=0.4972)</th>\n", "      <th id=\"T_6d92d_level3_col8\" class=\"col_heading level3 col8\" >Treatment=36,873<br>\n", "Control=36,791<br>\n", "No SSPM (p=0.7626)</th>\n", "    </tr>\n", "    <tr>\n", "      <th class=\"index_name level0\" >Date</th>\n", "      <th class=\"index_name level1\" >Metric</th>\n", "      <th class=\"blank col0\" >&nbsp;</th>\n", "      <th class=\"blank col1\" >&nbsp;</th>\n", "      <th class=\"blank col2\" >&nbsp;</th>\n", "      <th class=\"blank col3\" >&nbsp;</th>\n", "      <th class=\"blank col4\" >&nbsp;</th>\n", "      <th class=\"blank col5\" >&nbsp;</th>\n", "      <th class=\"blank col6\" >&nbsp;</th>\n", "      <th class=\"blank col7\" >&nbsp;</th>\n", "      <th class=\"blank col8\" >&nbsp;</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_6d92d_level0_row0\" class=\"row_heading level0 row0\" rowspan=\"12\">08/30 to 09/26</th>\n", "      <th id=\"T_6d92d_level1_row0\" class=\"row_heading level1 row0\" >security_lock_account_count</th>\n", "      <td id=\"T_6d92d_row0_col0\" class=\"data row0 col0\" >-25.77% (0.0312→0.0232, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row0_col1\" class=\"data row0 col1\" >2.10% (0.0111→0.0114, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row0_col2\" class=\"data row0 col2\" >-7.31% (0.0107→0.0099, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row0_col3\" class=\"data row0 col3\" >0.27% (0.0087→0.0088, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row0_col4\" class=\"data row0 col4\" >-10.52% (0.0105→0.0094, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row0_col5\" class=\"data row0 col5\" >-4.67% (0.0087→0.0083, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row0_col6\" class=\"data row0 col6\" >-12.86% (0.0063→0.0055, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row0_col7\" class=\"data row0 col7\" >-12.89% (0.0030→0.0026, < 0.05)</td>\n", "      <td id=\"T_6d92d_row0_col8\" class=\"data row0 col8\" >-8.58% (0.0114→0.0104, ≥ 0.1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6d92d_level1_row1\" class=\"row_heading level1 row1\" >security_lock_uu</th>\n", "      <td id=\"T_6d92d_row1_col0\" class=\"data row1 col0\" ></td>\n", "      <td id=\"T_6d92d_row1_col1\" class=\"data row1 col1\" >0.66% (0.0109→0.0110, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row1_col2\" class=\"data row1 col2\" >-7.31% (0.0107→0.0099, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row1_col3\" class=\"data row1 col3\" >-1.21% (0.0087→0.0086, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row1_col4\" class=\"data row1 col4\" >-11.73% (0.0105→0.0093, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row1_col5\" class=\"data row1 col5\" >-4.67% (0.0087→0.0083, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row1_col6\" class=\"data row1 col6\" >-12.86% (0.0063→0.0055, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row1_col7\" class=\"data row1 col7\" >-12.03% (0.0030→0.0026, < 0.1)</td>\n", "      <td id=\"T_6d92d_row1_col8\" class=\"data row1 col8\" >-9.10% (0.0113→0.0103, ≥ 0.1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6d92d_level1_row2\" class=\"row_heading level1 row2\" >security_perm_lock_uu</th>\n", "      <td id=\"T_6d92d_row2_col0\" class=\"data row2 col0\" ></td>\n", "      <td id=\"T_6d92d_row2_col1\" class=\"data row2 col1\" >2.07% (0.0107→0.0109, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row2_col2\" class=\"data row2 col2\" >-8.59% (0.0105→0.0096, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row2_col3\" class=\"data row2 col3\" >0.17% (0.0082→0.0082, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row2_col4\" class=\"data row2 col4\" >-14.46% (0.0102→0.0088, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row2_col5\" class=\"data row2 col5\" >-4.83% (0.0084→0.0079, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row2_col6\" class=\"data row2 col6\" >-15.78% (0.0061→0.0051, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row2_col7\" class=\"data row2 col7\" >-8.45% (0.0026→0.0024, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row2_col8\" class=\"data row2 col8\" >-8.03% (0.0111→0.0102, ≥ 0.1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6d92d_level1_row3\" class=\"row_heading level1 row3\" >security_temp_lock_uu</th>\n", "      <td id=\"T_6d92d_row3_col0\" class=\"data row3 col0\" ></td>\n", "      <td id=\"T_6d92d_row3_col1\" class=\"data row3 col1\" ></td>\n", "      <td id=\"T_6d92d_row3_col2\" class=\"data row3 col2\" ></td>\n", "      <td id=\"T_6d92d_row3_col3\" class=\"data row3 col3\" ></td>\n", "      <td id=\"T_6d92d_row3_col4\" class=\"data row3 col4\" ></td>\n", "      <td id=\"T_6d92d_row3_col5\" class=\"data row3 col5\" ></td>\n", "      <td id=\"T_6d92d_row3_col6\" class=\"data row3 col6\" ></td>\n", "      <td id=\"T_6d92d_row3_col7\" class=\"data row3 col7\" >-37.78% (0.0004→0.0002, < 0.05)</td>\n", "      <td id=\"T_6d92d_row3_col8\" class=\"data row3 col8\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6d92d_level1_row4\" class=\"row_heading level1 row4\" >security_l_locked_user_reputation_high_bad_actor_score_perm_lock_uu</th>\n", "      <td id=\"T_6d92d_row4_col0\" class=\"data row4 col0\" ></td>\n", "      <td id=\"T_6d92d_row4_col1\" class=\"data row4 col1\" >4.62% (0.0086→0.0090, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row4_col2\" class=\"data row4 col2\" >-16.14% (0.0079→0.0066, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row4_col3\" class=\"data row4 col3\" >-5.94% (0.0067→0.0063, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row4_col4\" class=\"data row4 col4\" >-19.55% (0.0073→0.0058, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row4_col5\" class=\"data row4 col5\" >5.21% (0.0057→0.0060, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row4_col6\" class=\"data row4 col6\" >-7.36% (0.0033→0.0031, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row4_col7\" class=\"data row4 col7\" >-7.22% (0.0012→0.0012, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row4_col8\" class=\"data row4 col8\" >-14.09% (0.0080→0.0069, < 0.1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6d92d_level1_row5\" class=\"row_heading level1 row5\" >security_l_lock_excessive_registration_perm_lock_uu</th>\n", "      <td id=\"T_6d92d_row5_col0\" class=\"data row5 col0\" ></td>\n", "      <td id=\"T_6d92d_row5_col1\" class=\"data row5 col1\" ></td>\n", "      <td id=\"T_6d92d_row5_col2\" class=\"data row5 col2\" ></td>\n", "      <td id=\"T_6d92d_row5_col3\" class=\"data row5 col3\" ></td>\n", "      <td id=\"T_6d92d_row5_col4\" class=\"data row5 col4\" ></td>\n", "      <td id=\"T_6d92d_row5_col5\" class=\"data row5 col5\" ></td>\n", "      <td id=\"T_6d92d_row5_col6\" class=\"data row5 col6\" ></td>\n", "      <td id=\"T_6d92d_row5_col7\" class=\"data row5 col7\" ></td>\n", "      <td id=\"T_6d92d_row5_col8\" class=\"data row5 col8\" >-6.46% (0.0017→0.0016, ≥ 0.1)</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6d92d_level1_row6\" class=\"row_heading level1 row6\" >security_l_1d_lock_3pa_lock_uu</th>\n", "      <td id=\"T_6d92d_row6_col0\" class=\"data row6 col0\" ></td>\n", "      <td id=\"T_6d92d_row6_col1\" class=\"data row6 col1\" ></td>\n", "      <td id=\"T_6d92d_row6_col2\" class=\"data row6 col2\" ></td>\n", "      <td id=\"T_6d92d_row6_col3\" class=\"data row6 col3\" ></td>\n", "      <td id=\"T_6d92d_row6_col4\" class=\"data row6 col4\" ></td>\n", "      <td id=\"T_6d92d_row6_col5\" class=\"data row6 col5\" ></td>\n", "      <td id=\"T_6d92d_row6_col6\" class=\"data row6 col6\" ></td>\n", "      <td id=\"T_6d92d_row6_col7\" class=\"data row6 col7\" >-49.88% (0.0002→0.0001, < 0.05)</td>\n", "      <td id=\"T_6d92d_row6_col8\" class=\"data row6 col8\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6d92d_level1_row7\" class=\"row_heading level1 row7\" >security_l_locked_TS_DRUGS_perm_lock_uu</th>\n", "      <td id=\"T_6d92d_row7_col0\" class=\"data row7 col0\" ></td>\n", "      <td id=\"T_6d92d_row7_col1\" class=\"data row7 col1\" ></td>\n", "      <td id=\"T_6d92d_row7_col2\" class=\"data row7 col2\" ></td>\n", "      <td id=\"T_6d92d_row7_col3\" class=\"data row7 col3\" ></td>\n", "      <td id=\"T_6d92d_row7_col4\" class=\"data row7 col4\" ></td>\n", "      <td id=\"T_6d92d_row7_col5\" class=\"data row7 col5\" ></td>\n", "      <td id=\"T_6d92d_row7_col6\" class=\"data row7 col6\" ></td>\n", "      <td id=\"T_6d92d_row7_col7\" class=\"data row7 col7\" >-14.07% (0.0002→0.0002, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row7_col8\" class=\"data row7 col8\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6d92d_level1_row8\" class=\"row_heading level1 row8\" >security_l_locked_TS_NCMEC_perm_lock_uu</th>\n", "      <td id=\"T_6d92d_row8_col0\" class=\"data row8 col0\" ></td>\n", "      <td id=\"T_6d92d_row8_col1\" class=\"data row8 col1\" ></td>\n", "      <td id=\"T_6d92d_row8_col2\" class=\"data row8 col2\" ></td>\n", "      <td id=\"T_6d92d_row8_col3\" class=\"data row8 col3\" ></td>\n", "      <td id=\"T_6d92d_row8_col4\" class=\"data row8 col4\" ></td>\n", "      <td id=\"T_6d92d_row8_col5\" class=\"data row8 col5\" ></td>\n", "      <td id=\"T_6d92d_row8_col6\" class=\"data row8 col6\" ></td>\n", "      <td id=\"T_6d92d_row8_col7\" class=\"data row8 col7\" >50.37% (0.0001→0.0002, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row8_col8\" class=\"data row8 col8\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6d92d_level1_row9\" class=\"row_heading level1 row9\" >security_l_locked_TS_SEXUAL_NUDITY_PORN_perm_lock_uu</th>\n", "      <td id=\"T_6d92d_row9_col0\" class=\"data row9 col0\" ></td>\n", "      <td id=\"T_6d92d_row9_col1\" class=\"data row9 col1\" ></td>\n", "      <td id=\"T_6d92d_row9_col2\" class=\"data row9 col2\" ></td>\n", "      <td id=\"T_6d92d_row9_col3\" class=\"data row9 col3\" ></td>\n", "      <td id=\"T_6d92d_row9_col4\" class=\"data row9 col4\" ></td>\n", "      <td id=\"T_6d92d_row9_col5\" class=\"data row9 col5\" ></td>\n", "      <td id=\"T_6d92d_row9_col6\" class=\"data row9 col6\" ></td>\n", "      <td id=\"T_6d92d_row9_col7\" class=\"data row9 col7\" >-14.07% (0.0001→0.0001, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row9_col8\" class=\"data row9 col8\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6d92d_level1_row10\" class=\"row_heading level1 row10\" >security_l_locked_striker_TS_SEXUAL_NUDITY_PORN_perm_lock_uu</th>\n", "      <td id=\"T_6d92d_row10_col0\" class=\"data row10 col0\" ></td>\n", "      <td id=\"T_6d92d_row10_col1\" class=\"data row10 col1\" ></td>\n", "      <td id=\"T_6d92d_row10_col2\" class=\"data row10 col2\" ></td>\n", "      <td id=\"T_6d92d_row10_col3\" class=\"data row10 col3\" ></td>\n", "      <td id=\"T_6d92d_row10_col4\" class=\"data row10 col4\" ></td>\n", "      <td id=\"T_6d92d_row10_col5\" class=\"data row10 col5\" ></td>\n", "      <td id=\"T_6d92d_row10_col6\" class=\"data row10 col6\" ></td>\n", "      <td id=\"T_6d92d_row10_col7\" class=\"data row10 col7\" >-9.78% (0.0001→0.0001, ≥ 0.1)</td>\n", "      <td id=\"T_6d92d_row10_col8\" class=\"data row10 col8\" ></td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_6d92d_level1_row11\" class=\"row_heading level1 row11\" >security_l_locked_striker_TS_SEXUAL_HARASSMENT_perm_lock_uu</th>\n", "      <td id=\"T_6d92d_row11_col0\" class=\"data row11 col0\" ></td>\n", "      <td id=\"T_6d92d_row11_col1\" class=\"data row11 col1\" ></td>\n", "      <td id=\"T_6d92d_row11_col2\" class=\"data row11 col2\" ></td>\n", "      <td id=\"T_6d92d_row11_col3\" class=\"data row11 col3\" ></td>\n", "      <td id=\"T_6d92d_row11_col4\" class=\"data row11 col4\" ></td>\n", "      <td id=\"T_6d92d_row11_col5\" class=\"data row11 col5\" ></td>\n", "      <td id=\"T_6d92d_row11_col6\" class=\"data row11 col6\" ></td>\n", "      <td id=\"T_6d92d_row11_col7\" class=\"data row11 col7\" >-53.46% (0.0002→0.0001, < 0.05)</td>\n", "      <td id=\"T_6d92d_row11_col8\" class=\"data row11 col8\" ></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<h3 >Rollout Impact Estimation</h3>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["==================================================\n", "security_lock_account_count\n", "Treatment 2\n", "(Absolute) Rollout Impact (Experiment Period): -4947.25\n", "(Absolute) Rollout Impact (Daily): -176.69\n", "(Absolute) Rollout Impact (Annually): -64490.98\n"]}], "source": ["if 'security_lock_metrics' in METRICS_GROUP_CONFIG: \n", "    report.ab_printer.print_text(\"Security Lock Metrics\", 'h2')\n", "    report.ab_printer.print_text(\"\"\"For a mobile device level study, we use the client_id <-> ghost_user_id mapping table to compute the metrics. security_lock_account_count is number of associated ghost_user_id that were locked. security_lock_uu is number of devices that have 1+ locked ghost_user_id's\n", "    <br>\n", "    <br>\n", "    For a user level study, account count and UU metric should be the same.\n", "    \"\"\",\"b\")\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters={'metrics': security_lock_metrics_report}\n", "    )\n", "    \n", "    report.ab_printer.print_text(\"Rollout Impact Estimation\", 'h3')\n", "    metrics_rollout_impact = ['security_lock_account_count']\n", "    df_results = report.results['cumulative']\n", "\n", "    \n", "    display_rollout_impact(df_study_info=df_study_info, df_results=df_results, metric_list=metrics_rollout_impact)\n", "else: \n", "    pass"]}, {"cell_type": "code", "execution_count": 29, "id": "3e1ee1e8-7b57-40a7-b4a4-************", "metadata": {}, "outputs": [], "source": ["if 'security_ads_metrics' in METRICS_GROUP_CONFIG: \n", "    report.ab_printer.print_text(\"Security ADS Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters={'metrics': security_ads_metrics_report}\n", "    )\n", "else: \n", "    pass"]}, {"cell_type": "code", "execution_count": 30, "id": "1f3bcbc5-ea67-4616-9575-fd218332858c", "metadata": {}, "outputs": [], "source": ["if 'account_takeover_metrics' in METRICS_GROUP_CONFIG: \n", "    report.ab_printer.print_text(\"Account Takeover Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters={'metrics': account_takeover_metrics_report}\n", "    )\n", "else: \n", "    pass"]}, {"cell_type": "code", "execution_count": 31, "id": "aa785678-0d41-4e39-b6f1-6e47dc7f3383", "metadata": {}, "outputs": [], "source": ["# if 'telephony_cost_metrics' in METRICS_GROUP_CONFIG: \n", "#     report.ab_printer.print_text(\"Telephony Cost Metrics\", 'h2')\n", "#     report.generate_report(\n", "#         format_pvalue=True,\n", "#         extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "#         display_config={'cumulative': ['table'], 'daily': trend},\n", "#         metric_filters={'metrics': telephony_cost_metrics_report}\n", "#     )\n", "# else: \n", "#     pass"]}, {"cell_type": "code", "execution_count": 32, "id": "2e6f3a4e-c172-45af-8360-99eb6eff8419", "metadata": {}, "outputs": [], "source": ["if 'engagement_metrics' in METRICS_GROUP_CONFIG: \n", "    report.ab_printer.print_text(\"Engagement Metrics\", 'h2')\n", "    report.ab_printer.print_text(\"\"\"Engagement data source table is sc-analytics.report_app.dau_user_country_*. For mobile device level AB, the sc-analytics.report_growth.client_ghost_user_mapping_* table is used to get client_id <-> ghost_user_id mapping.\n", "    <br>\n", "    <br>\n", "    If an account is not locked and not reported, we consider it as a good actor account, otherwise it's a bad actor account. \n", "    The time window for lock event and in-app report event is from the study start date to study end date + 6 days, which makes \n", "    sure that each account has at least 7-day lookback window.\n", "    \"\"\", 'b')\n", "\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters={'metrics': engagement_metrics_report}\n", "    )\n", "    \n", "    report.ab_printer.print_text(\"Rollout Impact Estimation\", 'h3')\n", "    metrics_rollout_impact = engagement_metrics_report\n", "    df_results = report.results['cumulative']\n", "    \n", "    display_rollout_impact(df_study_info=df_study_info, df_results=df_results, metric_list=metrics_rollout_impact)\n", "else: \n", "    pass"]}, {"cell_type": "code", "execution_count": null, "id": "7d96c750-9741-461a-8196-a8694a8d20ec", "metadata": {}, "outputs": [], "source": ["if 'friending_metrics' in METRICS_GROUP_CONFIG: \n", "    report.ab_printer.print_text(\"Friending Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters={'metrics': friending_metrics_report}\n", "    )\n", "    report.ab_printer.print_text(\"Rollout Impact Estimation\", 'h3')\n", "    metrics_rollout_impact = friending_metrics_report\n", "    df_results = report.results['cumulative']\n", "    \n", "    display_rollout_impact(df_study_info=df_study_info, df_results=df_results, metric_list=metrics_rollout_impact)\n", "else: \n", "    pass"]}, {"cell_type": "code", "execution_count": null, "id": "3572b900-2a58-4e69-a4b0-9b8c15c0eaee", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "1849df04-5c0c-45d2-914b-c038682cd98b", "metadata": {}, "source": ["# The End"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.20"}}, "nbformat": 4, "nbformat_minor": 5}