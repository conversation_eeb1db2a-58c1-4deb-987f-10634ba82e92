{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# SendTo AB Analysis Notebook"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from __future__ import division, unicode_literals, print_function\n", "\n", "import os\n", "if any(['VELLUM' in k for k in os.environ.keys()]):\n", "    pip_output = !pip install banjo\n", "\n", "import logging\n", "logging.basicConfig()\n", "logging.getLogger().setLevel(logging.WARNING)\n", "\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "from datetime import datetime, timedelta\n", "import pandas as pd\n", "import collections\n", "\n", "import seaborn as sns\n", "sns.set(style='whitegrid', font_scale=1.5)\n", "\n", "from IPython.core.display import display, HTML\n", "display(HTML(\"<style>.output_html.rendered_html table { font-size:8pt;}</style>\"))\n", "\n", "from banjo import utils\n", "from banjo import abtest\n", "from banjo.abtest.report import Metric, MetricTable, Report, get_quest_metric_table, CustomReport, get_abtest_console_metric_table\n", "from banjo.abtest.cohort_report import CohortReport\n", "from banjo.abtest.quest import TIER_ONE\n", "\n", "try:\n", "    from banjo.teams.product import sendto_metrics as sendto_metrics\n", "    from banjo.teams.product import snapstreaks as snapstreaks\n", "    from banjo.teams.product import friending as friending\n", "    from banjo.teams.product import messaging as messaging\n", "except ImportError:\n", "    GIT_DIR = os.path.join(os.environ['HOME'])\n", "    os.chdir(GIT_DIR)\n", "    print(\"Upgrade banjo to 0.14.0+, which packages all these scripts\")\n", "    import sendto_metrics as sendto_metrics"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["PROJECT = 'sc-bq-gcs-billingonly'\n", "\n", "# Specify study details\n", "STUDY_NAME = 'SEARCH_SEND_TO_POSTTYPE_IOS__106982'\n", "# The start date must match the study(current version) start date in the study config.\n", "STUDY_START_DATE = '20241110'\n", "STUDY_END_DATE = '20241117'\n", "STUDY_COHORT_DATE = STUDY_START_DATE\n", "CONTROL_ID = '16'\n", "TREATMENT_IDS = ['17', '18']\n", "\n", "EXP_NAMES = {}\n", "USER_BREAKDOWN_LIST = []\n", "COUNTRIES_SELECTED = []\n", "    \n", "OVERWRITE = False\n", "INCLUDE_DAILY_METRICS = True\n", "MATERIALIZE_MAPPING_TABLE = True\n", "PIVOT_RESULTS = True\n", "DAILY_TREND = False\n", "\n", "USE_BATCH_BQ_PRIORITY = \"False\"\n", "if USE_BATCH_BQ_PRIORITY == \"True\":\n", "    BQ_PRIORITY = 'BATCH'\n", "else:\n", "    BQ_PRIORITY = 'INTERACTIVE'\n", "\n", "METRICS_GROUP_CONFIG = [\n", "'engagement_metrics',\n", "'sendto_available_metrics',\n", "'sendto_seen_metrics',\n", "'sendto_select_metrics',\n", "'sendto_session_time_metrics',\n", "'snap_viewer_metrics',\n", "'bidirectional_communication_breakdown_metrics',\n", "\"snapstreak_metrics\",\n", "'sendto_snap_send_recipient_breakdown_metrics',\n", "'sendto_snap_send_relationship_breakdown_metrics',\n", "'sendto_candidate_side_dau_delta_metrics',\n", "'sendto_recents_ranking_evaluation_metrics',\n", "'sendto_search_ranking_evaluation_metrics',\n", "'sendto_performance_metrics',\n", "'sendto_performance_quantile_metrics',\n", "'off_platform_share_metrics',\n", "'off_platform_share_session_time_quantile_metrics',\n", "'off_platform_share_operation_quantile_metrics',\n", "'invite_metrics',\n", "'invite_cost_metrics',\n", "'ops_invite_receiver_side_metrics'\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if isinstance(COUNTRIES_SELECTED, str):\n", "    COUNTRIES_SELECTED = COUNTRIES_SELECTED.split(', ')\n", "\n", "if len(COUNTRIES_SELECTED) > 0:\n", "    countries_selected_sql = ', '.join([f\"'{c}'\" for c in COUNTRIES_SELECTED])\n", "else:\n", "    countries_selected_sql = \"'NA'\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def ensure_list(parameter):\n", "    if isinstance(parameter, list):\n", "        return parameter\n", "    else:\n", "        return [parameter]\n", "\n", "USER_BREAKDOWN_LIST = ensure_list(USER_BREAKDOWN_LIST)   \n", "METRICS_GROUP_CONFIG = ensure_list(METRICS_GROUP_CONFIG)\n", "TREATMENT_IDS = ensure_list(TREATMENT_IDS)\n", "\n", "# decide if print daily trend\n", "if DAILY_TREND:\n", "    trend = ['trend']\n", "else: \n", "    trend = []"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get study name\n", "study_name_sql = \"\"\"\n", "    SELECT\n", "      name,\n", "      exp_id\n", "    FROM\n", "      [sc-analytics:report_search.ab_console_study_config]\n", "    WHERE\n", "      study_name = '{study_name}'\n", "      AND exp_id IN ('{exp_ids}')\n", "\"\"\"\n", "\n", "df_exp_names = utils.gbq.read_gbq(\n", "    study_name_sql.format(study_name=STUDY_NAME, exp_ids=\"', '\".join([CONTROL_ID] + TREATMENT_IDS)),\n", "    project_id=PROJECT, \n", "    priority=BQ_PRIORITY)\n", "\n", "if not EXP_NAMES and not df_exp_names.empty:\n", "    EXP_NAMES = dict(zip(df_exp_names.exp_id, df_exp_names.name))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# add send to metrics\n", "# break down metrics into different sections\n", "ALL_METRICS_GROUPS = {}\n", "\n", "ALL_METRICS_GROUPS['sendto_available_metrics'] = [\n", "    sendto_metrics.sendto_sections_available(STUDY_START_DATE, STUDY_END_DATE), \n", "]\n", "\n", "ALL_METRICS_GROUPS['sendto_seen_metrics'] = [\n", "    sendto_metrics.sendto_sections_seen(STUDY_START_DATE, STUDY_END_DATE), \n", "    sendto_metrics.sendto_object_types_seen(STUDY_START_DATE, STUDY_END_DATE), \n", "]\n", "\n", "ALL_METRICS_GROUPS['sendto_select_metrics'] = [\n", "    sendto_metrics.sendto_camera_snap_send(STUDY_START_DATE, STUDY_END_DATE), \n", "    sendto_metrics.sendto_recipients_by_section(STUDY_START_DATE, STUDY_END_DATE), \n", "    sendto_metrics.sendto_recipients_by_section_cancelled(STUDY_START_DATE, STUDY_END_DATE), \n", "    sendto_metrics.snap_recipient_by_source(STUDY_START_DATE, STUDY_END_DATE), \n", "    sendto_metrics.sendto_session_by_source(STUDY_START_DATE, STUDY_END_DATE), \n", "    sendto_metrics.sendto_select_all_usage(STUDY_START_DATE, STUDY_END_DATE),\n", "]\n", "\n", "ALL_METRICS_GROUPS['sendto_session_time_metrics'] = [\n", "    sendto_metrics.sendto_session_time(STUDY_START_DATE, STUDY_END_DATE),\n", "    sendto_metrics.sendto_time_per_cell_selected(STUDY_START_DATE, STUDY_END_DATE),\n", "]\n", "\n", "ALL_METRICS_GROUPS['snap_viewer_metrics'] = [\n", "    sendto_metrics.snap_viewer(STUDY_START_DATE, STUDY_END_DATE),\n", "    sendto_metrics.snap_viewer_engagement_change(STUDY_START_DATE, STUDY_END_DATE),\n", "]\n", "\n", "ALL_METRICS_GROUPS['bidirectional_communication_breakdown_metrics'] = [\n", "    sendto_metrics.bidirectional_communication_breakdown(STUDY_START_DATE, STUDY_END_DATE),\n", "]\n", "\n", "ALL_METRICS_GROUPS['snapstreak_metrics'] = [\n", "    snapstreaks.snapstreak(STUDY_START_DATE, STUDY_END_DATE),\n", "    snapstreaks.snapstreak_only_longest(STUDY_START_DATE, STUDY_END_DATE),\n", "]\n", "\n", "ALL_METRICS_GROUPS['sendto_snap_send_recipient_breakdown_metrics'] = [\n", "    sendto_metrics.sendto_snap_send_recipient_breakdown(STUDY_START_DATE, STUDY_END_DATE),\n", "]\n", "\n", "ALL_METRICS_GROUPS['sendto_snap_send_relationship_breakdown_metrics'] = [\n", "    sendto_metrics.sendto_snap_send_relationship_breakdown(STUDY_START_DATE, STUDY_END_DATE),\n", "]\n", "\n", "ALL_METRICS_GROUPS['sendto_candidate_side_dau_delta_metrics'] = [\n", "    sendto_metrics.sendto_candidate_side_dau_delta(STUDY_START_DATE, STUDY_END_DATE),\n", "] \n", "\n", "ALL_METRICS_GROUPS['sendto_recents_ranking_evaluation_metrics'] = [\n", "    sendto_metrics.recents_ranking_snapchatter_session_lvl_metrics(STUDY_START_DATE, STUDY_END_DATE),\n", "    sendto_metrics.recents_ranking_snapchatter_item_lvl_metrics(STUDY_START_DATE, STUDY_END_DATE),\n", "    sendto_metrics.recents_ranking_group_session_lvl_metrics(STUDY_START_DATE, STUDY_END_DATE),\n", "    sendto_metrics.recents_ranking_group_item_lvl_metrics(STUDY_START_DATE, STUDY_END_DATE),\n", "]\n", "\n", "ALL_METRICS_GROUPS['sendto_search_ranking_evaluation_metrics'] = [\n", "    sendto_metrics.search_ranking_item_lvl_metrics(STUDY_START_DATE, STUDY_END_DATE),\n", "]\n", "\n", "ALL_METRICS_GROUPS['sendto_performance_metrics'] = [\n", "    # sendto_metrics.sendto_slow_scroll(STUDY_START_DATE, STUDY_END_DATE),\n", "    sendto_metrics.sendto_render_latency(STUDY_START_DATE, STUDY_END_DATE),\n", "] \n", "\n", "ALL_METRICS_GROUPS['sendto_performance_quantile_metrics'] = [\n", "    sendto_metrics.sendto_render_latency_percentile(STUDY_START_DATE, STUDY_END_DATE),\n", "    sendto_metrics.sendto_data_ready_view_model_ready(STUDY_START_DATE, STUDY_END_DATE),\n", "] \n", "\n", "ALL_METRICS_GROUPS['off_platform_share_metrics'] = [\n", "    sendto_metrics.off_platform_share(STUDY_START_DATE, STUDY_END_DATE), \n", "    sendto_metrics.context_menu_action(STUDY_START_DATE, STUDY_END_DATE), \n", "]\n", "\n", "ALL_METRICS_GROUPS['off_platform_share_session_time_quantile_metrics'] = [\n", "    sendto_metrics.off_platform_share_session_time_quantile(STUDY_START_DATE, STUDY_END_DATE), \n", "]\n", "\n", "ALL_METRICS_GROUPS['off_platform_share_operation_quantile_metrics'] = [\n", "    sendto_metrics.off_platform_share_operation_quantile(STUDY_START_DATE, STUDY_END_DATE), \n", "]\n", "\n", "ALL_METRICS_GROUPS['invite_metrics'] = [\n", "    sendto_metrics.social_sms_action(STUDY_START_DATE, STUDY_END_DATE), \n", "    sendto_metrics.social_sms_invite_funnel_daily(STUDY_START_DATE, STUDY_END_DATE), \n", "    sendto_metrics.invite_contact_impression_action(STUDY_START_DATE, STUDY_END_DATE), \n", "]\n", "\n", "ALL_METRICS_GROUPS['invite_cost_metrics'] = [\n", "    sendto_metrics.twilio_total_cost(STUDY_START_DATE, STUDY_END_DATE), \n", "    sendto_metrics.twilio_total_cost_per_dnu(STUDY_START_DATE, STUDY_END_DATE), \n", "]\n", "\n", "ALL_METRICS_GROUPS['ops_invite_receiver_side_metrics'] = [\n", "    sendto_metrics.link_receiver_link_app_open(STUDY_START_DATE, STUDY_END_DATE), \n", "    sendto_metrics.link_receiver_deep_link_lifecycle(STUDY_START_DATE, STUDY_END_DATE),\n", "    sendto_metrics.link_receiver_link_web_page_view(STUDY_START_DATE, STUDY_END_DATE), \n", "    sendto_metrics.dnu_from_ops_ip(STUDY_START_DATE, STUDY_END_DATE), \n", "    sendto_metrics.indirect_org_dnu_attrib_1d(STUDY_START_DATE, STUDY_END_DATE), \n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# add quest metrics\n", "if 'engagement_metrics' in METRICS_GROUP_CONFIG or 'friend_requests_metrics' in METRICS_GROUP_CONFIG:\n", "    ab_metric_catalog = abtest.ABQuestMetricCatalog(STUDY_START_DATE, STUDY_END_DATE, bq_priority=BQ_PRIORITY).get_catalog()\n", "\n", "    if 'engagement_metrics' in METRICS_GROUP_CONFIG:\n", "        engagement_metrics_names = [\n", "            'app_app_open_user',\n", "            'app_active_day_user',\n", "            'snap_direct_snap_create_user',\n", "            'snap_direct_snap_create_active_day_user',\n", "\n", "            'app_app_session_time_user',\n", "            'app_app_close_session_time_user',\n", "\n", "            'snap_filter_lens_swipe_user',\n", "            'snap_filter_lens_swipe_active_day_user',\n", "            'snap_lens_direct_snap_create_active_day_user',\n", "\n", "            #'chat_chat_create_user',\n", "            'chat_chat_send_user',\n", "            'chat_chat_send_active_day_user',\n", "\n", "            'chat_chat_view_user',\n", "            'chat_chat_view_active_day_user',\n", "\n", "            'snap_direct_snap_save_user',\n", "            'snap_direct_snap_save_active_day_user',\n", "\n", "            'snap_direct_snap_send_user',\n", "            'snap_direct_snap_send_active_day_user',\n", "\n", "            'snap_direct_snap_view_user',\n", "            'snap_direct_snap_view_active_day_user',\n", "\n", "            'story_story_snap_post_user',\n", "            'story_story_snap_post_active_day_user',\n", "\n", "            'story_story_snap_view_user',\n", "            'story_story_snap_view_active_day_user',\n", "\n", "\n", "            'story_friend_story_snap_view',\n", "            'story_friend_story_snap_active_day_view',\n", "\n", "            'story_friend_story_snap_post',\n", "            'story_friend_story_snap_active_day_post',\n", "            'story_friend_story_snap_post_active_day_user',\n", "\n", "            'story_friend_story_story_view',\n", "            'story_friend_story_story_active_day_view',\n", "            'story_friend_story_snap_view_active_day_user',\n", "            'story_friend_story_story_view_active_day_user',\n", "\n", "            'story_user_story_snap_view_user',\n", "            'story_user_story_snap_view_active_day_user',\n", "\n", "            'discover_discover_edition_view_user',\n", "            'discover_discover_edition_view_active_day_user',\n", "\n", "            'discover_discover_snap_view_user',\n", "            'discover_discover_snap_view_active_day_user',\n", "            'app_communication_friendship_user'\n", "        ]\n", "\n", "        engagement_metrics_list = []\n", "        for attr in engagement_metrics_names:\n", "            try:\n", "                engagement_metrics_list.append(getattr(ab_metric_catalog, attr))\n", "            except AttributeError:\n", "                print('Warning: metric ' + attr + ' is not available. Skipping')            \n", "        \n", "        ALL_METRICS_GROUPS['engagement_metrics'] = engagement_metrics_list\n", "\n", "    if 'friend_requests_metrics' in METRICS_GROUP_CONFIG:\n", "        friend_requests_metrics_names = [\n", "            'friending_new_friendship_with_bidirectional_communication',\n", "            'friending_friend_request_accept_user'            \n", "        ]\n", "        \n", "        friend_requests_metrics_list = []\n", "        for attr in friend_requests_metrics_names:\n", "            try:\n", "                friend_requests_metrics_list.append(getattr(ab_metric_catalog, attr))\n", "            except AttributeError:\n", "                print('Warning: metric ' + attr + ' is not available. Skipping')        \n", "\n", "        ALL_METRICS_GROUPS['friend_requests_metrics'] = ([friending.friending_actions(STUDY_START_DATE, STUDY_END_DATE)] + [friending.incoming_friending(STUDY_START_DATE, STUDY_END_DATE)] + friend_requests_metrics_list)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql_query_template=\"\"\"\n", "SELECT\n", "  a.ts AS ts,\n", "  exposure_ts AS exposure_ts,\n", "  a.ghost_user_id AS ghost_user_id,\n", "  a.exp_id AS exp_id,\n", "  {breakdowns}\n", "FROM (\n", "  SELECT\n", "    PARSE_TIMESTAMP(\"%Y%m%d\", CONCAT(\"20\", _TABLE_SUFFIX)) AS ts,\n", "    ghost_user_id,\n", "    exp_id,\n", "    os os_type,\n", "    country,\n", "    age_group\n", "  FROM\n", "    `{project}.{dataset}.{name}__20*`\n", "  WHERE\n", "    CONCAT(\"20\", _TABLE_SUFFIX) BETWEEN \"{start}\"\n", "    AND \"{end}\"\n", "    AND LENGTH(_TABLE_SUFFIX) = 6 ) a\n", "LEFT JOIN (\n", "  SELECT\n", "    ghost_user_id,\n", "    exp_id,\n", "    MIN(TIMESTAMP_MILLIS(CAST(initial_timestamp AS INT64))) AS exposure_ts,\n", "  FROM\n", "    `{project}.{dataset}.{name}__{end}`\n", "  GROUP BY\n", "    ghost_user_id,\n", "    exp_id) AS mask\n", "ON\n", "  a.ghost_user_id = mask.ghost_user_id\n", "  AND a.exp_id = mask.exp_id\n", "LEFT JOIN ( {breakdown_table} ) b\n", "ON\n", "  a.ghost_user_id = b.ghost_user_id       \n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Breakdown table\n", "BREAKDOWN_TABLE_JOIN_KEY = \"ghost_user_id\"\n", "\n", "BREAKDOWN_TABLE = \"\"\"\n", "SELECT\n", "user_segment.ghost_user_id,\n", "\n", "CASE WHEN bidirectional_friend_count + unidirectional_friend_count < 7 THEN \"<7\"\n", "    WHEN bidirectional_friend_count + unidirectional_friend_count >= 7 THEN \"7+\"\n", "    ELSE \"unknown\" END AS outgoing_friend_count,\n", "\n", "case when n_recent_result_selected_per_session_p50 is null or n_recent_result_selected_per_session_p50 = 0 then '0'\n", "    when n_recent_result_selected_per_session_p50 <= 1 then '1'\n", "    when n_recent_result_selected_per_session_p50 <= 10 then '2 - 10'\n", "    when n_recent_result_selected_per_session_p50 <= 30 then '11 - 30'\n", "    when n_recent_result_selected_per_session_p50 <= 100 then '31 - 100'\n", "    else '100+'\n", "    end as n_recent_result_selected_per_session_p50_bucket,\n", "\n", "case when n_recent_result_selected_per_session_p90 is null or n_recent_result_selected_per_session_p90 = 0 then '0'\n", "    when n_recent_result_selected_per_session_p90 <= 1 then '1'\n", "    when n_recent_result_selected_per_session_p90 <= 10 then '2 - 10'\n", "    when n_recent_result_selected_per_session_p90 <= 30 then '11 - 30'\n", "    when n_recent_result_selected_per_session_p90 <= 100 then '31 - 100'\n", "    else '100+'\n", "    end as n_recent_result_selected_per_session_p90_bucket,\n", "\n", "case when n_recent_result_selected_per_session_excl_zero_p50 is null or n_recent_result_selected_per_session_excl_zero_p50 = 0 then 'NULL'\n", "    when n_recent_result_selected_per_session_excl_zero_p50 <= 1 then '1'\n", "    when n_recent_result_selected_per_session_excl_zero_p50 <= 10 then '2 - 10'\n", "    when n_recent_result_selected_per_session_excl_zero_p50 <= 30 then '11 - 30'\n", "    when n_recent_result_selected_per_session_excl_zero_p50 <= 100 then '31 - 100'\n", "    else '100+'\n", "    end as n_recent_result_selected_per_session_excl_zero_p50_bucket,\n", "\n", "case when n_recent_result_selected_per_session_excl_zero_p90 is null or n_recent_result_selected_per_session_excl_zero_p90 = 0 then 'NULL'\n", "    when n_recent_result_selected_per_session_excl_zero_p90 <= 1 then '1'\n", "    when n_recent_result_selected_per_session_excl_zero_p90 <= 10 then '2 - 10'\n", "    when n_recent_result_selected_per_session_excl_zero_p90 <= 30 then '11 - 30'\n", "    when n_recent_result_selected_per_session_excl_zero_p90 <= 100 then '31 - 100'\n", "    else '100+'\n", "    end as n_recent_result_selected_per_session_excl_zero_p90_bucket,\n", "\n", "app_engagement_status AS app_engagement_bucket,\n", "\n", "if(bidirectional_friend_count = 0, '0', if(bidirectional_friend_count <= 5, '1-5', bidirectional_friend_status)) as bidirectional_friend_status, \n", "\n", "inferred_age_bucket,\n", "age_group,\n", "device_cluster,\n", "app_version,\n", "country,\n", "if(country in ({countries_selected_sql}), country, 'OTHERS') as country_selected,\n", "os_type,\n", "\n", "case\n", "    when country in (\"US\",\"CA\") then \"US, CA\"\n", "    when country in (\"SA\",\"IN\",\"AU\") then country\n", "    when country in (\"FR\",\"GB\",\"DE\") then \"FR, GB, DE\"\n", "    when country is not null then \"OTHERS\"\n", "    end as country_major,\n", "\n", "concat(case\n", "    when country in  (\"US\",\"CA\") then \"US, CA\"\n", "    when country in  (\"SA\",\"IN\",\"AU\") then country\n", "    when country in  (\"FR\",\"GB\",\"DE\") then \"FR, GB, DE\"\n", "    when country is not null then \"OTHERS\"\n", "    end, \n", "    ' | ', \n", "    case when n_recent_result_selected_per_session_excl_zero_p50 is null or n_recent_result_selected_per_session_excl_zero_p50 = 0 then 'NULL'\n", "    when n_recent_result_selected_per_session_excl_zero_p50 <= 1 then '1'\n", "    when n_recent_result_selected_per_session_excl_zero_p50 <= 10 then '2 - 10'\n", "    when n_recent_result_selected_per_session_excl_zero_p50 <= 30 then '11 - 30'\n", "    when n_recent_result_selected_per_session_excl_zero_p50 <= 100 then '31 - 100'\n", "    else '100+'\n", "    end)  as  country_major_n_recent_result_selected_per_session_excl_zero_p50_bucket,\n", "    \n", "country_bucket,\n", "user_persona_v2,\n", "is_phone_verified,\n", "is_email_verified,\n", "user_persona_v3,\n", "public_profile_tier,\n", "creator_tier,\n", "story_post_L7,\n", "country_x_reported_age_bucket,\n", "\n", "CASE\n", "    WHEN is_dnu =  1 THEN cast(NULL AS string)\n", "    WHEN inactive_day BETWEEN 0 AND 0 THEN 'inactive  0d'\n", "    WHEN inactive_day BETWEEN 0 AND 3 THEN 'inactive  1-3d'\n", "    WHEN inactive_day BETWEEN 0 AND 6 THEN 'inactive  4-6d'\n", "    WHEN inactive_day BETWEEN 0 AND 13 THEN 'inactive  7-13d'\n", "    WHEN inactive_day BETWEEN 0 AND 29 THEN 'inactive 14-29d'\n", "    ELSE 'inactive 30d+'\n", "    END AS inactive_day_bucket,\n", "\n", "stories_available_7d_max,\n", "case when stories_available_7d_max is NULL then 'NULL' \n", "    when stories_available_7d_max <= 4 then cast(stories_available_7d_max as string)\n", "    else '5+'\n", "    end as stories_available_7d_max_group,\n", "    \n", "FROM\n", "(\n", "    SELECT\n", "    ghost_user_id,\n", "    (unidirectional_friend_count) AS  unidirectional_friend_count,\n", "    (bidirectional_friend_count) AS bidirectional_friend_count,\n", "    (bidirectional_friend_status) AS bidirectional_friend_status,\n", "    (app_L7) AS app_L7,\n", "    (app_engagement_status) AS app_engagement_status,\n", "    (inferred_age_bucket) AS inferred_age_bucket, \n", "    user_persona_v2,\n", "    is_phone_verified,\n", "    is_email_verified,\n", "    is_dnu,\n", "    user_persona_v3,\n", "    public_profile_tier,\n", "    creator_tier,\n", "    story_post_L7,\n", "    country_x_reported_age_bucket,\n", "    FROM `sc-analytics.report_search.user_cohorts_{cohort_date}`\n", ") user_segment\n", "LEFT JOIN\n", "(\n", "SELECT\n", "ghost_user_id,\n", "date_diff(PARSE_DATE('%Y%m%d','{cohort_date}'),date(last_active_date), day)-1 AS inactive_day,\n", "FROM\n", "`sc-analytics.report_app.last_active_day_20*`\n", "WHERE PARSE_DATE('%Y%m%d',concat('20',_TABLE_SUFFIX))\n", "=DATE_SUB(PARSE_DATE('%Y%m%d','{cohort_date}'), INTERVAL 1 day)\n", ")last_active\n", "ON user_segment.ghost_user_id = last_active.ghost_user_id\n", "LEFT JOIN (\n", "    SELECT\n", "    ghost_user_id,\n", "    ANY_VALUE(country_bucket) AS country_bucket,\n", "    ANY_VALUE(os) AS os_type,\n", "    ANY_VALUE(country) AS country,\n", "    ANY_VALUE(device_cluster) AS device_cluster,\n", "    ANY_VALUE(age_group) AS age_group,\n", "    max(app_version) AS app_version,\n", "    FROM `{project}.{dataset}.{name}__{end_date}`\n", "    GROUP BY ghost_user_id\n", ") AS mask\n", "ON user_segment.ghost_user_id = mask.ghost_user_id\n", "LEFT JOIN (\n", "    select distinct\n", "    ghost_user_id,\n", "    approx_quantiles(n_recent_with_contacts_result_selected + n_recent_result_selected, 1000)[offset(500)] as n_recent_result_selected_per_session_p50, -- P50 Recent recipient selection per session over the previous 7 days\n", "    approx_quantiles(n_recent_with_contacts_result_selected + n_recent_result_selected, 1000)[offset(900)] as n_recent_result_selected_per_session_p90, -- P90 Recent recipient selection per session over the previous 7 days\n", "    approx_quantiles(if(n_recent_with_contacts_result_selected + n_recent_result_selected > 0, n_recent_with_contacts_result_selected + n_recent_result_selected, NULL), 1000)[offset(500)] as n_recent_result_selected_per_session_excl_zero_p50, -- P50 Recent recipient selection per session (excl. 0) over the previous 7 days\n", "    approx_quantiles(if(n_recent_with_contacts_result_selected + n_recent_result_selected > 0, n_recent_with_contacts_result_selected + n_recent_result_selected, NULL), 1000)[offset(900)] as n_recent_result_selected_per_session_excl_zero_p90, -- P50 Recent recipient selection per session (excl. 0) over the previous 7 days\n", "    FROM `sc-analytics.report_growth.sendto_impression_action_session_level_2*` \n", "    WHERE PARSE_DATE('%Y%m%d',concat('2',_TABLE_SUFFIX)) BETWEEN DATE_ADD((PARSE_DATE('%Y%m%d','{cohort_date}')), INTERVAL -7 day) and DATE_ADD((PARSE_DATE('%Y%m%d','{cohort_date}')), INTERVAL -1 day)\n", "    group by 1\n", ") as recent_selection\n", "on user_segment.ghost_user_id = recent_selection.ghost_user_id\n", "LEFT JOIN (\n", "    select distinct\n", "    ghost_user_id,\n", "    max(stories_available) as stories_available_7d_max,\n", "    from `sc-analytics.report_growth.stg_usr_lvl_page_sendto_session_end_2*`\n", "    WHERE PARSE_DATE('%Y%m%d',concat('2',_TABLE_SUFFIX)) BETWEEN DATE_ADD((PARSE_DATE('%Y%m%d','{cohort_date}')), INTERVAL -7 day) and DATE_ADD((PARSE_DATE('%Y%m%d','{cohort_date}')), INTERVAL -1 day)\n", "    group by 1\n", ") as stories_available\n", "on user_segment.ghost_user_id = stories_available.ghost_user_id\n", "\n", "\"\"\".format(\n", "    date=STUDY_START_DATE,\n", "    end_date=STUDY_END_DATE,\n", "    cohort_date=STUDY_COHORT_DATE,\n", "    project='sc-portal',\n", "    dataset='usermap_cumulative',\n", "    name=STUDY_NAME,\n", "    countries_selected_sql=countries_selected_sql\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create the mapping table for the retrospective period\n", "def create_mapping_with_extra_breakdown(\n", "        study_name, \n", "        study_start_date, \n", "        study_end_date,\n", "        breakdown_table,\n", "        breakdown_table_join_key,\n", "        breakdowns,\n", "        ab_project='sc-portal',\n", "        ab_dataset='usermap_cumulative'):\n", "    \n", "    sql_template = sql_query_template\n", "    \n", "    sql = sql_template.format(\n", "        project=ab_project,\n", "        dataset=ab_dataset,\n", "        name=study_name,\n", "        start=study_start_date,\n", "        end=study_end_date,\n", "        breakdown_table=breakdown_table,\n", "        join_key=breakdown_table_join_key,\n", "        breakdowns=',\\n'.join(\n", "            ['COALESCE(b.{c},NULL) AS {c}'.format(c=c) for c in breakdowns]\n", "        )\n", "    )\n", "    \n", "    return sql\n", "    \n", "custom_mapping_table = create_mapping_with_extra_breakdown(\n", "    STUDY_NAME,\n", "    STUDY_START_DATE,\n", "    STUDY_END_DATE,\n", "    breakdown_table=BREAKDOWN_TABLE,\n", "    breakdown_table_join_key=BREAKDOWN_TABLE_JOIN_KEY,\n", "    breakdowns=USER_BREAKDOWN_LIST\n", ")\n", "\n", "# print(custom_mapping_table)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Construct the report object\n", "#metric_tables = ab_console_metrics + sendto_recipients + sendto_sections + sendto_viewership + sendto_time + sendto_recipients_by_source + sendto_snap_sources + sendto_render_latency\n", "metric_tables = [\n", "    metric_table \n", "    for sublist in [ALL_METRICS_GROUPS[mt] for mt in ALL_METRICS_GROUPS if mt in METRICS_GROUP_CONFIG]\n", "    for metric_table in sublist\n", "]\n", "\n", "report = CustomReport(\n", "    study_name=STUDY_NAME,\n", "    study_start_date=STUDY_START_DATE,\n", "    study_end_date=STUDY_END_DATE,\n", "    metric_tables=metric_tables,\n", "    control_id=CONTROL_ID,\n", "    treatment_ids=TREATMENT_IDS,\n", "    user_group_bys=USER_BREAKDOWN_LIST,\n", "    bq_project='sc-bq-gcs-billingonly',  \n", "    dest_dataset='temp_abtest',\n", "    quantiles=['90'],   \n", "    custom_mapping_sql=custom_mapping_table,  \n", "    # mapping_for_daily_results='cumulative,\n", "    exp_id_to_name=EXP_NAMES,\n", "    bq_dialect='standard',\n", "    overwrite_mapping_table=OVERWRITE\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run the joins and calculate the results\n", "report.execute(\n", "    overwrite=OVERWRITE\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["METRICS_NAME_MAPPING = collections.OrderedDict([\n", "    [\"tier1_active_days_active_days\", \"Active Days\"],\n", "    [\"tier1_session_time_session_time\", \"Session Time\"],\n", "    # [\"tier1_direct_snap_create_created_snaps_active_days\",\"Active Days for Snap Create\"],\n", "    [\"tier1_direct_snap_create_created_snaps\", \"Snap Create\"],\n", "    [\"tier1_direct_snap_create_snap_creator\",\"Snap Creator\"],\n", "    [\"chat_chat_create_created_chat\",\"Chat Page Enter\"],\n", "    [\"chat_chat_create_chat_creator\", \"Chat Page Enter User\"],\n", "    [\"chat_chat_send_sent_chat\",\"Chat Send\"],\n", "    [\"chat_chat_send_chat_sender\", \"Chat Sender\"],\n", "    [\"chat_chat_view_views\",\"Chat Views\"],\n", "    [\"chat_chat_view_chat_viewer\",\"Chat Viewer\"],\n", "    [\"direct_snap_send_sent_snap\", \"Snap Send\"],\n", "    [\"direct_snap_send_snap_sender\",\"Snap Sender\"],\n", "    [\"direct_snap_view_views\",\"Direct Snap Views\"],\n", "    [\"direct_snap_view_snap_viewer\",\"Direct Snap Viewer\"],\n", "    [\"user_story_snap_view_views\", \"Story Snap Views (Friends and Subscription)\"],\n", "    [\"user_story_snap_view_viewer\",\"Story Snap Viewer (Friends and Subscription)\"],\n", "    [\"user_story_story_view_views\", \"Story Views (Friends and Subscription)\"],\n", "    [\"user_story_story_view_viewer\",\"Story Viewer (Friends and Subscription)\"],\n", "    [\"discover_snap_view_viewer\", \"Publisher Story Snap Viewer\"],\n", "    [\"discover_snap_view_views\", \"Publisher Story Snap Views\"],\n", "    [\"discover_edition_view_viewer\", \"Publisher Story Viewer\"],\n", "    [\"discover_edition_view_views\", \"Publisher Story Views\"],    \n", "    [\"my_story_snap_post_posts\", \"My Story Snap Post\"],\n", "    [\"my_story_snap_post_poster\",\"My Story Snap Poster\"],\n", "    [\"our_story_snap_post_posts\", \"Our Story Snap Post\"],\n", "    [\"DIRECT_SNAP_CREATE_CAMERA_UU\", \"Snap Creator from Camera\"],\n", "    [\"DIRECT_SNAP_CREATE_CAMERA\",\"Snap Create from Camera\"],\n", "    [\"DIRECT_SNAP_CREATE_FEED_UU\", \"Snap Creator from DTTR\"],\n", "    [\"DIRECT_SNAP_CREATE_FEED\",\"Snap Create from DTTR\"],   \n", "    [\"DIRECT_SNAP_CREATE_FEED_SNAP_REPLY_UU\", \"Snap Creator from Feed Reply Button\"],\n", "    [\"DIRECT_SNAP_CREATE_FEED_SNAP_REPLY\",\"Snap Create from Feed Reply Button\"],     \n", "    [\"DIRECT_SNAP_CREATE_IN_CHAT_UU\", \"Snap Creator from Chat Page\"],\n", "    [\"DIRECT_SNAP_CREATE_IN_CHAT\",\"Snap Create from Chat Page\"],       \n", "    [\"DIRECT_SNAP_SEND_CAMERA_UU\", \"Snap Sender from Camera\"],\n", "    [\"DIRECT_SNAP_SEND_CAMERA\",\"Snap Send from Camera\"],\n", "    [\"DIRECT_SNAP_SEND_FEED_UU\", \"Snap Sender from DTTR\"],\n", "    [\"DIRECT_SNAP_SEND_FEED\",\"Snap Send from DTTR\"],   \n", "    [\"DIRECT_SNAP_SEND_FEED_SNAP_REPLY_UU\", \"Snap Sender from Feed Reply Button\"],\n", "    [\"DIRECT_SNAP_SEND_FEED_SNAP_REPLY\",\"Snap Send from Feed Reply Button\"],     \n", "    [\"DIRECT_SNAP_SEND_IN_CHAT_UU\", \"Snap Sender from Chat Page\"],\n", "    [\"DIRECT_SNAP_SEND_IN_CHAT\",\"Snap Send from Chat Page\"],   \n", "])\n", "\n", "for mt in report.metric_tables:\n", "    for metric in mt.metrics:\n", "        if metric.col in METRICS_NAME_MAPPING:\n", "            metric.name = METRICS_NAME_MAPPING[metric.col]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report.configurations['pivot_table'] = PIVOT_RESULTS\n", "report.configurations['stat_fmtr'] = (\n", "      \"{pct_diff:,.2f}% ({avg_control:,.4f}→{avg_treatment:,.4f}, {p_value_formatted})\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 'engagement_metrics' in METRICS_GROUP_CONFIG: \n", "    t1_metrics_report = [m for mt in engagement_metrics_list[:3] for m in mt.cumulative_metrics if  'blizzard' not in m]\n", "    general_eng_metrics_report = [m for mt in engagement_metrics_list[3:] for m in mt.cumulative_metrics if 'time_viewed' not in m and 'lens' not in m ]\n", "\n", "if 'friend_requests_metrics' in METRICS_GROUP_CONFIG:\n", "    friend_requests_metrics_report = [m for mt in ALL_METRICS_GROUPS['friend_requests_metrics'] for m in mt.cumulative_metrics]\n", "    \n", "sendto_available_metrics_report = [m for mt in ALL_METRICS_GROUPS['sendto_available_metrics'] for m in mt.cumulative_metrics]\n", "sendto_seen_metrics_report = [m for mt in ALL_METRICS_GROUPS['sendto_seen_metrics'] for m in mt.cumulative_metrics]\n", "sendto_select_metrics_report = [m for mt in ALL_METRICS_GROUPS['sendto_select_metrics'] for m in mt.cumulative_metrics]\n", "sendto_session_time_metrics_report = [m for mt in ALL_METRICS_GROUPS['sendto_session_time_metrics'] for m in mt.cumulative_metrics]\n", "snap_viewer_metrics_report = [m for mt in ALL_METRICS_GROUPS['snap_viewer_metrics'] for m in mt.cumulative_metrics]\n", "bidirectional_communication_breakdown_metrics_report = [m for mt in ALL_METRICS_GROUPS['bidirectional_communication_breakdown_metrics'] for m in mt.cumulative_metrics]\n", "snapstreak_metrics_report = [m for mt in ALL_METRICS_GROUPS['snapstreak_metrics'] for m in mt.cumulative_metrics]\n", "sendto_snap_send_recipient_breakdown_metrics_report = [m for mt in ALL_METRICS_GROUPS['sendto_snap_send_recipient_breakdown_metrics'] for m in mt.cumulative_metrics]\n", "sendto_snap_send_relationship_breakdown_metrics_report = [m for mt in ALL_METRICS_GROUPS['sendto_snap_send_relationship_breakdown_metrics'] for m in mt.cumulative_metrics]\n", "sendto_candidate_side_dau_delta_metrics_report = [m for mt in ALL_METRICS_GROUPS['sendto_candidate_side_dau_delta_metrics'] for m in mt.cumulative_metrics]\n", "sendto_recents_ranking_evaluation_metrics_report = [m for mt in ALL_METRICS_GROUPS['sendto_recents_ranking_evaluation_metrics'] for m in mt.cumulative_metrics]\n", "sendto_search_ranking_evaluation_metrics_report = [m for mt in ALL_METRICS_GROUPS['sendto_search_ranking_evaluation_metrics'] for m in mt.cumulative_metrics]\n", "sendto_performance_metrics_report = [m for mt in ALL_METRICS_GROUPS['sendto_performance_metrics'] for m in mt.cumulative_metrics]\n", "sendto_performance_quantile_metrics_report = [m for mt in ALL_METRICS_GROUPS['sendto_performance_quantile_metrics'] for m in mt.cumulative_metrics]\n", "off_platform_share_metrics_report = [m for mt in ALL_METRICS_GROUPS['off_platform_share_metrics'] for m in mt.cumulative_metrics]\n", "off_platform_share_session_time_quantile_metrics_report = [m for mt in ALL_METRICS_GROUPS['off_platform_share_session_time_quantile_metrics'] for m in mt.cumulative_metrics]\n", "off_platform_share_operation_quantile_metrics_report = [m for mt in ALL_METRICS_GROUPS['off_platform_share_operation_quantile_metrics'] for m in mt.cumulative_metrics]\n", "invite_metrics_report = [m for mt in ALL_METRICS_GROUPS['invite_metrics'] for m in mt.cumulative_metrics]\n", "invite_cost_metrics_report = [m for mt in ALL_METRICS_GROUPS['invite_cost_metrics'] for m in mt.cumulative_metrics]\n", "ops_invite_receiver_side_metrics_report = [m for mt in ALL_METRICS_GROUPS['ops_invite_receiver_side_metrics'] for m in mt.cumulative_metrics]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["abtest.report.notebook_print(\"Study Name: {}\".format(report.study_name), \"h3\")\n", "abtest.report.notebook_print(\"Control ID: {}\".format(report.control_id), \"strong\")\n", "abtest.report.notebook_print(\"Treatment ID(s): {}\".format(\", \".join(report.treatment_ids)), \"strong\")\n", "abtest.report.notebook_print(\n", "    \"Analysis period: {} - {}\".format(report.analysis_start_date, report.study_end_date), \n", "    \"strong\"\n", ")\n", "abtest.report.notebook_print(\n", "    \"Breakdowns: <br> {}\".format(\n", "        \"<br>\".join(\", \".join(breakdown) for breakdown in report.user_group_by_list)\n", "    ), \n", "    \"strong\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report.ab_printer.print_text(\"Sample Sizes\", 'h2')\n", "report.generate_report(\n", "    format_pvalue=True,\n", "    display_config={'cumulative': ['table'], 'daily': trend},\n", "    metric_filters={'metrics': ['tier1_active_days', 'ops_uu', 'app_app_open_user'], 'regex': True},\n", "    stat_fmtr=\"Users in control: {count_control:,.0f} <br>Users in treatment: {count_treatment:,.0f}\", \n", ");"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# SendTo Core Evaluation Metrics\n", "# Always show this group of metrics on top of the report\n", "sendto_eval_metrics_report = ['total_snap_send', 'snaps_sent_to_recents_section', 'receiver_side_inactive_1_day_snap_viewer', 'bidirectional_communication_friend', 'bidirectional_communication_from_sendto']\n", "\n", "report.ab_printer.print_text(\"SendTo Core Evaluation Metrics\", 'h2')\n", "report.generate_report(\n", "    format_pvalue=True,\n", "    extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "    display_config={'cumulative': ['table'], 'daily': trend},\n", "    metric_filters = {\n", "        'metrics': sendto_eval_metrics_report\n", "    },\n", ");"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 'engagement_metrics' in METRICS_GROUP_CONFIG: \n", "    # Tier1 Metrics\n", "    report.ab_printer.print_text(\"Tier1 Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters = {\n", "            'metrics': t1_metrics_report\n", "        },\n", "    );\n", "else: \n", "    pass    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 'engagement_metrics' in METRICS_GROUP_CONFIG: \n", "    # General Engagement Metrics\n", "    report.ab_printer.print_text(\"Key Engagement Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters = {\n", "            'metrics': general_eng_metrics_report\n", "        },\n", "    );\n", "else: \n", "    pass    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 'sendto_available_metrics' in METRICS_GROUP_CONFIG:\n", "    report.ab_printer.print_text(\"SendTo Available Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters={\n", "            'metrics': sendto_available_metrics_report\n", "        },\n", "    );\n", "else:\n", "    pass\n", "\n", "if 'sendto_seen_metrics' in METRICS_GROUP_CONFIG:\n", "    report.ab_printer.print_text(\"SendTo Seen Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters={\n", "            'metrics': sendto_seen_metrics_report\n", "        },\n", "    );\n", "else:\n", "    pass\n", "\n", "if 'sendto_select_metrics' in METRICS_GROUP_CONFIG:\n", "    report.ab_printer.print_text(\"SendTo Select Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters={\n", "            'metrics': sendto_select_metrics_report\n", "        },\n", "    );\n", "else:\n", "    pass\n", "\n", "if 'sendto_session_time' in METRICS_GROUP_CONFIG: \n", "    report.ab_printer.print_text(\"SendTo Session & Time Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters = {\n", "            'metrics': sendto_session_time_metrics_report\n", "        },\n", "    )\n", "else: \n", "    pass   \n", "\n", "if 'snap_viewer_metrics' in METRICS_GROUP_CONFIG:\n", "    report.ab_printer.print_text(\"Snap Viewer Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters={\n", "            'metrics': snap_viewer_metrics_report\n", "        },\n", "    );\n", "else:\n", "    pass\n", "\n", "if 'bidirectional_communication_breakdown_metrics' in METRICS_GROUP_CONFIG:\n", "    report.ab_printer.print_text(\"Bidirectional Communication Breakdown Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters={\n", "            'metrics': bidirectional_communication_breakdown_metrics_report\n", "        },\n", "    );\n", "else:\n", "    pass\n", "\n", "if 'snapstreak_metrics' in METRICS_GROUP_CONFIG:\n", "    report.ab_printer.print_text(\"Snap Streak Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters={\n", "            'metrics': snapstreak_metrics_report\n", "        },\n", "    );\n", "else:\n", "    pass   \n", "\n", "if 'sendto_snap_send_recipient_breakdown_metrics' in METRICS_GROUP_CONFIG:\n", "    report.ab_printer.print_text(\"SendTo Receiver-Side Recipient Breakdown Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters={\n", "            'metrics': sendto_snap_send_recipient_breakdown_metrics_report\n", "        },\n", "    );\n", "else:\n", "    pass   \n", "\n", "if 'sendto_snap_send_relationship_breakdown_metrics' in METRICS_GROUP_CONFIG:\n", "    report.ab_printer.print_text(\"SendTo Receiver-Side Relationship Breakdown Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters={\n", "            'metrics': sendto_snap_send_relationship_breakdown_metrics_report\n", "        },\n", "    );\n", "else:\n", "    pass   \n", "\n", "if 'sendto_candidate_side_dau_delta_metrics' in METRICS_GROUP_CONFIG: \n", "    report.ab_printer.print_text(\"SendTo Candidate Side DAU Delta Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters = {\n", "            'metrics': sendto_candidate_side_dau_delta_metrics_report\n", "        },\n", "    )\n", "else: \n", "    pass   \n", "\n", "if 'sendto_recents_ranking_evaluation_metrics' in METRICS_GROUP_CONFIG: \n", "    report.ab_printer.print_text(\"SendTo Recents Ranking Evaluation Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters = {\n", "            'metrics': sendto_recents_ranking_evaluation_metrics_report\n", "        },\n", "    )\n", "else: \n", "    pass   \n", "\n", "if 'sendto_search_ranking_evaluation_metrics' in METRICS_GROUP_CONFIG: \n", "    report.ab_printer.print_text(\"SendTo Search Ranking Evaluation Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters = {\n", "            'metrics': sendto_search_ranking_evaluation_metrics_report\n", "        },\n", "    )\n", "else: \n", "    pass   \n", "\n", "if 'sendto_performance_metrics' in METRICS_GROUP_CONFIG: \n", "    report.ab_printer.print_text(\"SendTo Performance Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters = {\n", "            'metrics': sendto_performance_metrics_report\n", "        },\n", "    )\n", "else: \n", "    pass   \n", "\n", "if 'off_platform_share_metrics' in METRICS_GROUP_CONFIG: \n", "    report.ab_printer.print_text(\"Off-Platform Share Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters = {\n", "            'metrics': off_platform_share_metrics_report\n", "        },\n", "    )\n", "else: \n", "    pass   \n", "\n", "if 'invite_metrics' in METRICS_GROUP_CONFIG: \n", "    report.ab_printer.print_text(\"Invite Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters = {\n", "            'metrics': invite_metrics_report\n", "        },\n", "    )\n", "else: \n", "    pass   \n", "\n", "if 'invite_cost_metrics' in METRICS_GROUP_CONFIG: \n", "    report.ab_printer.print_text(\"Invite Cost Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters = {\n", "            'metrics': invite_cost_metrics_report\n", "        },\n", "    )\n", "else: \n", "    pass   \n", "\n", "if 'ops_invite_receiver_side_metrics' in METRICS_GROUP_CONFIG: \n", "    report.ab_printer.print_text(\"Off-Platform Share and Invite Receiver-Side Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        extra_table_cols=['count_control', 'count_treatment', 'avg_control', 'avg_treatment'],\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters = {\n", "            'metrics': ops_invite_receiver_side_metrics_report\n", "        },\n", "    )\n", "else: \n", "    pass   "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report.configurations['pivot_table'] = True\n", "report.configurations['stat_fmtr'] = (\n", "    \"{pct_diff:,.2f}% ({quantile_control:,.3}→{quantile_treatment:,.3}, {p_value_formatted})\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if 'sendto_performance_quantile_metrics' in METRICS_GROUP_CONFIG: \n", "    report.ab_printer.print_text(\"SendTo Performance Quantile Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters = {\n", "            'metrics': sendto_performance_quantile_metrics_report\n", "        },\n", "    )\n", "else: \n", "    pass   \n", "\n", "if 'off_platform_share_session_time_quantile_metrics' in METRICS_GROUP_CONFIG: \n", "    report.ab_printer.print_text(\"Off-Platform Share Session Time Quantile Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters = {\n", "            'metrics': off_platform_share_session_time_quantile_metrics_report\n", "        },\n", "    )\n", "else: \n", "    pass   \n", "\n", "if 'off_platform_share_operation_quantile_metrics' in METRICS_GROUP_CONFIG: \n", "    report.ab_printer.print_text(\"Off-Platform Share Operation Quantile Metrics\", 'h2')\n", "    report.generate_report(\n", "        format_pvalue=True,\n", "        display_config={'cumulative': ['table'], 'daily': trend},\n", "        metric_filters = {\n", "            'metrics': off_platform_share_operation_quantile_metrics_report\n", "        },\n", "    )\n", "else: \n", "    pass   "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["report.ab_printer.print_text(\"Rollout Impact Estimation\", 'h2')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_study_info(study_name, study_end_date):\n", "    sql = f\"\"\"\n", "    with study_config as \n", "    (\n", "    SELECT  \n", "    *\n", "    FROM `sc-analytics.report_search.ab_console_study_config` \n", "    where study_name = '{study_name}'\n", "    ), \n", "\n", "    study_info as \n", "    (\n", "    SELECT  \n", "    *\n", "    FROM `sc-portal.ab_metric_impact.study_info_{study_end_date}`\n", "    where study_name = '{study_name}'\n", "    )\n", "\n", "    select \n", "    a.*, \n", "    b.user_slice_range_start, \n", "    b.user_slice_range_end,\n", "    b.user_slice_range_end - b.user_slice_range_start as user_slice_range,\n", "    (a.traffic / 100) * ((b.user_slice_range_end - b.user_slice_range_start) / 100) as pct_population\n", "    from study_config a\n", "    left join study_info b\n", "    on CAST(a.study_version AS STRING) = CAST(b.study_version AS STRING)\n", "    \"\"\"\n", "    \n", "    df = utils.gbq.read_gbq(sql, project_id=PROJECT, dialect='standard', priority=BQ_PRIORITY)\n", "    \n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_study_info = get_study_info(STUDY_NAME, STUDY_END_DATE)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["n_days = (datetime.strptime(STUDY_END_DATE, '%Y%m%d') - datetime.strptime(STUDY_START_DATE, '%Y%m%d')).days + 1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["result_types = report.results.keys()\n", "result_types = [r for r in result_types if 'cumulative' in r] # cumulative results only"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_rollout_impact = None\n", "\n", "for result_type in result_types:\n", "    df_result = report.results[result_type].copy()\n", "    \n", "    # Append control target ratio\n", "    df_result['control_target_ratio'] = df_study_info.loc[df_study_info['exp_id'] == CONTROL_ID]['pct_population'].values[0]\n", "    \n", "    # Join treatment target ratios\n", "    df_result = pd.merge(df_result, df_study_info[['exp_id', 'pct_population']], left_on='treatment_id', right_on='exp_id')\n", "    df_result.rename(columns={'pct_population': 'treatment_target_ratio'}, inplace=True)\n", "\n", "    df_result['rollout_impact'] = (df_result['avg_treatment'] - df_result['avg_control']) * (df_result['count_treatment'] + df_result['count_control']) / (df_result['treatment_target_ratio'] + df_result['control_target_ratio'])\n", "\n", "    df_result['rollout_impact_daily'] = df_result['rollout_impact'] / n_days\n", "\n", "    df_result['rollout_impact_annual'] = df_result['rollout_impact_daily'] * 365\n", "\n", "    if df_rollout_impact is None:\n", "        df_rollout_impact = df_result.copy()\n", "    else:\n", "        df_rollout_impact = pd.concat([df_rollout_impact, df_result], axis=0, ignore_index=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["metrics_rollout_impact = ['link_receiver_new_user_app_open_phone_hash_attrib', 'social_sms_invite_cost', 'social_sms_invite_cost_using_segments', \n", "                          'dnu_from_ops_ip', 'dnu_from_ops_ip_det',\n", "                          'send_communication_correspondent_inactive_1_plus_day', 'receiver_side_inactive_1_day_snap_viewer']\n", "\n", "df_rollout_impact_selected = df_rollout_impact.loc[df_rollout_impact['metric'].isin(metrics_rollout_impact)]\n", "df_rollout_impact_selected = df_rollout_impact_selected[['breakdown_dimensions', 'breakdown_values', 'metric',\n", "                                                         'control_id', 'control_target_ratio', 'count_control', 'sum_control', 'avg_control', \n", "                                                         'treatment_id', 'treatment_target_ratio', 'count_treatment', 'sum_treatment', 'avg_treatment', \n", "                                                         'sspm_pval', 'p_value',  'rollout_impact', 'rollout_impact_daily', 'rollout_impact_annual']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pd.options.display.float_format = '{:.3f}'.format\n", "display(HTML(df_rollout_impact_selected.to_html(\n", "        header=True,\n", "        index=True,\n", "        escape=False)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## The End."]}], "metadata": {"anaconda-cloud": {}, "celltoolbar": "Tags", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}