{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# SendTo Receiver-Side DAU Impact Estimation Notebook"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from banjo import utils\n", "from banjo import abtest\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import numpy as np\n", "import pandas as pd\n", "from datetime import datetime, timedelta\n", "from IPython.core.display import display, HTML\n", "import scipy.stats as stats"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Where to save temporary tables\n", "WRITE_PROJECT = \"sc-analytics\"\n", "WRITE_DATASET = 'report_growth'\n", "BQ_PRIORITY = 'INTERACTIVE'\n", "TEMP_TABLE_RETENTION = 30\n", "\n", "# Whether to force recreate the tables\n", "FORCE_RECREATE = False\n", "\n", "if FORCE_RECREATE:\n", "    QUERY_CREATE_TABLE = \"CREATE OR REPLACE TABLE\"\n", "else:\n", "    QUERY_CREATE_TABLE = \"CREATE TABLE IF NOT EXISTS\"\n", "\n", "# Whether to dry run the queries\n", "DRY_RUN = False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Where to run query\n", "QUERY_PROJECT = 'sc-bq-gcs-billingonly'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Where to read user mapping\n", "USER_MAPPING_PROJECT = 'sc-portal'\n", "USER_MAPPING_DATASET = 'usermap_cumulative'"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["STUDY_NAME = 'SEND_TO_RANKING_RECENTS_RANKING_SERVICE_HOLDOUT__146990'\n", "STUDY_START_DATE = '20250626'\n", "STUDY_END_DATE = '20250629'\n", "CONTROL_ID = '4'\n", "TREATMENT_ID = '3'\n", "LOOK_THROUGH_GROUPS = 'False'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["study_config = {\n", "    \"STUDY_NAME\": STUDY_NAME,\n", "    \"START_DATE\": STUDY_START_DATE,\n", "    \"END_DATE\": STUDY_END_DATE,\n", "    \"CONTROL_ID\": CONTROL_ID,\n", "    \"TREATMENT_ID\": TREATMENT_ID,\n", "    \"LOOK_THROUGH_GROUPS\": LOOK_THROUGH_GROUPS == 'True'\n", "}    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Table naming convensions\n", "def get_calc_prep_table_name(study_name, start_date, end_date):\n", "    table_name = f\"{WRITE_PROJECT}.{WRITE_DATASET}.calc_prep_with_group_{study_name}_{start_date}_{end_date}\"\n", "    \n", "    return table_name\n", "\n", "def get_candidate_agg_table_name(study_name, control_id, treatment_id, start_date, end_date):\n", "    table_name = f\"{WRITE_PROJECT}.{WRITE_DATASET}.candidate_agg_with_group_{study_name}_{control_id}_vs_{treatment_id}_{start_date}_{end_date}\"\n", "    \n", "    return table_name\n", "\n", "def get_candidate_incr_dau_result_table_name(study_name, control_id, treatment_id, start_date, end_date, look_through_groups):\n", "    if look_through_groups:\n", "        group_look_through = 'with_group'\n", "    else:\n", "        group_look_through = 'without_group'\n", "\n", "    table_name = f\"{WRITE_PROJECT}.{WRITE_DATASET}.candidate_incr_dau_result_{group_look_through}_{study_name}_{control_id}_vs_{treatment_id}_{start_date}_{end_date}\"\n", "    \n", "    return table_name"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def string_to_date(ds):\n", "    \"\"\"\n", "    ds: str, yyyymmdd or yyyy-mm-mdd\n", "    returns: date\n", "    \"\"\"\n", "    try:\n", "        if '-' in ds:\n", "            return datetime.strptime(ds, '%Y-%m-%d').date()\n", "        else:\n", "            return datetime.strptime(ds, '%Y%m%d').date()\n", "    except:\n", "        print(\"Failed to convert string to date\")\n", "        return None\n", "    \n", "def date_to_string(dt, return_format='yyyymmdd'):\n", "    \"\"\"\n", "    dt: date\n", "    return_format: str, {'yyyymmdd', 'yyyy-mm-dd'}\n", "    returns: str, yyyymmdd or yyyy-mm-mdd\n", "    \"\"\"\n", "    try:\n", "        if return_format == 'yyyy-mm-dd':\n", "            return datetime.strftime(dt, '%Y-%m-%d')\n", "        else:\n", "            return datetime.strftime(dt, '%Y%m%d')\n", "    except:\n", "        print(\"Failed to convert date to string\")\n", "        return None\n", "\n", "def get_date_list(start_dt, end_dt, return_type='yyyymmdd'):\n", "    \"\"\"\n", "    start_dt: date or str\n", "    end_dt: date or str\n", "    return_type: str, {'yyyymmdd', 'yyyy-mm-dd', 'date'}\n", "    returns: list, list of yyyymmdd/yyyy-mm-dd strings or dates\n", "    \"\"\"\n", "    \n", "    # convert start_dt and end_dt to dates if needed\n", "    if isinstance(start_dt, str):\n", "        start_dt = string_to_date(start_dt)\n", "    \n", "    if isinstance(end_dt, str):\n", "        end_dt = string_to_date(end_dt)\n", "        \n", "    assert start_dt != None and end_dt != None\n", "            \n", "    \n", "    # difference between current and previous date\n", "    delta = <PERSON>elta(days=1)\n", "\n", "    # store the dates between two dates in a list\n", "    dates = []\n", "\n", "    while start_dt <= end_dt:\n", "        # add current date to list by converting it to iso format\n", "        dates.append(start_dt.isoformat().replace('-', ''))\n", "        # increment start date by <PERSON><PERSON><PERSON>\n", "        start_dt += delta\n", "    \n", "    # convert list to selected format and return\n", "    if return_type == 'yyyymmdd':\n", "        return dates\n", "    elif return_type == 'yyyy-mm-dd':\n", "        return [datetime.strftime(datetime.strptime(d, '%Y%m%d'), '%Y-%m-%d') for d in dates]\n", "    elif return_type == 'date':\n", "        return [datetime.strptime(d, '%Y%m%d').date() for d in dates]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_calc_prep_table(study_config):\n", "    study_name = study_config[\"STUDY_NAME\"]\n", "    start_date = study_config[\"START_DATE\"]\n", "    end_date = study_config[\"END_DATE\"]\n", "    control_id = study_config[\"CONTROL_ID\"]\n", "    treatment_id = study_config[\"TREATMENT_ID\"]\n", "    \n", "    table_name = get_calc_prep_table_name(study_name, start_date, end_date)\n", "\n", "    sql = f\"\"\"{QUERY_CREATE_TABLE} `{table_name}`\n", "options (\n", "  expiration_timestamp=timestamp(date_add(CURRENT_DATE(), INTERVAL {TEMP_TABLE_RETENTION} DAY))\n", ") as\n", "with sender_exp as (\n", "SELECT \n", "ghost_user_id,\n", "min(date(timestamp_millis(cast(initial_timestamp as int)))) as exposure_date,\n", "max(exp_id) as exp_id\n", "from `{USER_MAPPING_PROJECT}.{USER_MAPPING_DATASET}.{study_name}__2*`\n", "where _table_suffix = (select max(_table_suffix) from `{USER_MAPPING_PROJECT}.{USER_MAPPING_DATASET}.{study_name}__2*` where exp_id in ('{control_id}', '{treatment_id}'))\n", "group by all\n", "),\n", "\n", "sendto_impression_action as (\n", "select *\n", "from `sc-analytics.report_growth.sendto_impression_action_candidate_dau_delta_2*`\n", "WHERE concat('2', _table_suffix) between '{start_date}' and '{end_date}'\n", ")\n", "\n", "SELECT\n", "s.*,\n", "e.exp_id,\n", "from sendto_impression_action s\n", "left join sender_exp e\n", "on s.ghost_user_id = e.ghost_user_id\n", "and s.partition_date >= e.exposure_date;\n", "\"\"\"\n", "    \n", "    if DRY_RUN:\n", "        print(sql)\n", "    else:\n", "        utils.gbq.read_gbq(sql, project_id=QUERY_PROJECT, dialect='standard', priority=BQ_PRIORITY)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_candidate_agg_table(study_config):\n", "    study_name = study_config[\"STUDY_NAME\"]\n", "    start_date = study_config[\"START_DATE\"]\n", "    end_date = study_config[\"END_DATE\"]\n", "    control_id = study_config[\"CONTROL_ID\"]\n", "    treatment_id = study_config[\"TREATMENT_ID\"]\n", "    \n", "    table_name = get_candidate_agg_table_name(study_name, control_id, treatment_id, start_date, end_date)\n", "    upstream_name = get_calc_prep_table_name(study_name, start_date, end_date)\n", "\n", "    # Compose query_impression_selections\n", "    metrics = [\n", "        'n_impressions_individual',\n", "        'n_selections_individual', \n", "        'n_impressions_via_group',\n", "        'n_selections_via_group',\n", "        'n_impressions',\n", "        'n_selections'\n", "    ]\n", "\n", "    query_parts = []\n", "    for exp_id in [control_id, treatment_id]:\n", "        for metric in metrics:\n", "            query_parts.append(f\"SUM(if(exp_id = '{exp_id}', {metric}, 0)) as {metric}_exp{exp_id}\")\n", "\n", "    query_impression_selections = ',  \\n'.join(query_parts) + ','\n", "    \n", "    # Compose query_lost_streaks\n", "    query_lost_streaks_parts = []\n", "    for exp_id in [control_id, treatment_id]:\n", "        query_lost_streaks_parts.append(\n", "            f\"count(distinct if(exp_id = '{exp_id}' and is_streak_friend_1md and not is_streak_friend, ghost_user_id, NULL)) as n_correspondent_lost_streak_exp{exp_id}\"\n", "        )\n", "\n", "    query_lost_streaks = ',\\n'.join(query_lost_streaks_parts) + ','\n", "\n", "    sql = f\"\"\"{QUERY_CREATE_TABLE} `{table_name}`\n", "options (\n", "  expiration_timestamp=timestamp(date_add(CURRENT_DATE(), INTERVAL {TEMP_TABLE_RETENTION} DAY))\n", ") as\n", "with \n", "base_with_all_exp_id as (\n", "SELECT distinct\n", "*\n", "from `{upstream_name}`\n", "), \n", "\n", "daily_user_agg as (\n", "-- A candidate contributes positive incremental DAU if is DAU and being selected by at least one Treatment sender\n", "-- A candidate contributes negative incremental DAU if not DAU and lost a streak with a Treatment sender\n", "select\n", "partition_date,\n", "candidate_ghost_user_id,\n", "ANY_VALUE(candidate_is_dau_delta) as candidate_is_dau_delta,\n", "{query_impression_selections}\n", "{query_lost_streaks}\n", "from base_with_all_exp_id\n", "group by all\n", "),\n", "\n", "user_cohorts as (\n", "select distinct\n", "parse_date('%Y%m%d', concat('2', _table_suffix)) as partition_date,\n", "ghost_user_id,\n", "l_90_country,\n", "days_since_last_active,\n", "app_l7,\n", "from `sc-analytics.report_search.user_cohorts_2*`\n", "where concat('2', _table_suffix) between '{start_date}' and '{end_date}'\n", "),\n", "\n", "user_identity as (\n", "select distinct\n", "ghost_user_id,\n", "lastCountry,\n", "creationCountry,\n", "from `sc-analytics.report_user.identity_{end_date}`\n", ")\n", "\n", "select \n", "a.*,\n", "coalesce(u.l_90_country, 'UNKOWN') as candidate_l_90_country,\n", "coalesce(u.days_since_last_active, 'UNKOWN') as candidate_days_since_last_active,\n", "coalesce(u.app_l7, 0) as candidate_app_l7,\n", "coalesce(i.lastCountry, 'UNKOWN') as candidate_last_country,\n", "coalesce(i.creationCountry, 'UNKOWN') as candidate_creation_country\n", "from daily_user_agg a\n", "left join user_cohorts u\n", "on a.partition_date = u.partition_date\n", "and a.candidate_ghost_user_id = u.ghost_user_id\n", "left join user_identity i\n", "on a.candidate_ghost_user_id = i.ghost_user_id;\n", "    \"\"\"    \n", "        \n", "    if DRY_RUN:\n", "        print(sql)\n", "    else:\n", "        utils.gbq.read_gbq(sql, project_id=QUERY_PROJECT, dialect='standard', priority=BQ_PRIORITY)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_candidate_incr_dau_result_table(study_config):\n", "    study_name = study_config[\"STUDY_NAME\"]\n", "    start_date = study_config[\"START_DATE\"]\n", "    end_date = study_config[\"END_DATE\"]\n", "    control_id = study_config[\"CONTROL_ID\"]\n", "    treatment_id = study_config[\"TREATMENT_ID\"]\n", "    look_through_groups = study_config[\"LOOK_THROUGH_GROUPS\"]\n", "    \n", "    table_name = get_candidate_incr_dau_result_table_name(study_name, control_id, treatment_id, start_date, end_date, look_through_groups)\n", "    upstream_name = get_candidate_agg_table_name(study_name, control_id, treatment_id, start_date, end_date)\n", "\n", "    if look_through_groups:\n", "        positive_delta_metric = 'n_selections'\n", "    else:\n", "        positive_delta_metric = 'n_selections_individual'\n", "\n", "    sql = f\"\"\"{QUERY_CREATE_TABLE} `{table_name}`\n", "options (\n", "  expiration_timestamp=timestamp(date_add(CURRENT_DATE(), INTERVAL {TEMP_TABLE_RETENTION} DAY))\n", ") as\n", "with\n", "calc as (\n", "select\n", "partition_date,\n", "candidate_l_90_country,\n", "candidate_days_since_last_active,\n", "candidate_app_l7,\n", "candidate_last_country,\n", "candidate_creation_country,\n", "\n", "count(distinct candidate_ghost_user_id) as n_candidate,\n", "\n", "-- Control positive delta metrics\n", "sum(n_selections_individual_exp{control_id}) as n_selections_individual_exp{control_id},\n", "sum(n_selections_via_group_exp{control_id}) as n_selections_via_group_exp{control_id},\n", "sum(n_selections_exp{control_id}) as n_selections_exp{control_id},\n", "count(distinct if({positive_delta_metric}_exp{control_id} > 0 and candidate_is_dau_delta > 0, candidate_ghost_user_id, NULL)) as n_candidate_positive_delta_exp{control_id},\n", "SUM(if({positive_delta_metric}_exp{control_id} > 0 and candidate_is_dau_delta > 0, candidate_is_dau_delta, 0)) as positive_delta_exp{control_id},\n", "\n", "-- Control negative delta metrics\n", "sum(n_correspondent_lost_streak_exp{control_id}) as n_correspondent_lost_streak_exp{control_id},\n", "count(distinct if(n_correspondent_lost_streak_exp{control_id} > 0 and candidate_is_dau_delta < 0, candidate_ghost_user_id, NULL)) as n_candidate_negative_delta_exp{control_id},\n", "SUM(if(n_correspondent_lost_streak_exp{control_id} > 0 and candidate_is_dau_delta < 0, candidate_is_dau_delta, 0)) as negative_delta_exp{control_id},\n", "\n", "-- Treatment positive delta metrics\n", "sum(n_selections_individual_exp{treatment_id}) as n_selections_individual_exp{treatment_id},\n", "sum(n_selections_via_group_exp{treatment_id}) as n_selections_via_group_exp{treatment_id},\n", "sum(n_selections_exp{treatment_id}) as n_selections_exp{treatment_id},\n", "count(distinct if({positive_delta_metric}_exp{treatment_id} > 0 and candidate_is_dau_delta > 0, candidate_ghost_user_id, NULL)) as n_candidate_positive_delta_exp{treatment_id},\n", "SUM(if({positive_delta_metric}_exp{treatment_id} > 0 and candidate_is_dau_delta > 0, candidate_is_dau_delta, 0)) as positive_delta_exp{treatment_id},\n", "\n", "-- Treatment negative delta metrics\n", "sum(n_correspondent_lost_streak_exp{treatment_id}) as n_correspondent_lost_streak_exp{treatment_id},\n", "count(distinct if(n_correspondent_lost_streak_exp{treatment_id} > 0 and candidate_is_dau_delta < 0, candidate_ghost_user_id, NULL)) as n_candidate_negative_delta_exp{treatment_id},\n", "SUM(if(n_correspondent_lost_streak_exp{treatment_id} > 0 and candidate_is_dau_delta < 0, candidate_is_dau_delta, 0)) as negative_delta_exp{treatment_id},\n", "\n", "from `{upstream_name}`\n", "group by all\n", ")\n", "\n", "select\n", "*,\n", "positive_delta_exp{control_id} + negative_delta_exp{control_id} as net_delta_exp{control_id},\n", "positive_delta_exp{treatment_id} + negative_delta_exp{treatment_id} as net_delta_exp{treatment_id},\n", "(positive_delta_exp{treatment_id} + negative_delta_exp{treatment_id}) - (positive_delta_exp{control_id} + negative_delta_exp{control_id}) as incremental_dau_exp{treatment_id},\n", "from calc;\n", "\"\"\"    \n", "    if DRY_RUN:\n", "        print(sql)\n", "        return 0, 0\n", "    else:    \n", "        utils.gbq.read_gbq(sql, project_id=QUERY_PROJECT, dialect='standard', priority=BQ_PRIORITY)\n", "        \n", "        sql = f\"\"\"\n", "        select \n", "        sum(incremental_dau_exp{treatment_id}) as incremental_dau_exp{treatment_id}_total,\n", "        sum(incremental_dau_exp{treatment_id}) / count(distinct partition_date) as incremental_dau_exp{treatment_id}_daily_avg,\n", "        from {table_name};\n", "        \"\"\"\n", "\n", "        df = utils.gbq.read_gbq(sql, project_id=QUERY_PROJECT, dialect='standard', priority=BQ_PRIORITY)\n", "        incr_dau_total = df[f\"incremental_dau_exp{treatment_id}_total\"][0]\n", "        incr_dau_daily = df[f\"incremental_dau_exp{treatment_id}_daily_avg\"][0]\n", "        \n", "        return round(incr_dau_total, 4), round(incr_dau_daily, 4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_candidate_incr_dau_result_table_sampled(study_config, n_samples=30):\n", "    if DRY_RUN:\n", "        print(\"Dry run mode is enabled. Skipping the creation of sampled incremental DAU result table.\")\n", "        return 0, 0\n", "\n", "    study_name = study_config[\"STUDY_NAME\"]\n", "    start_date = study_config[\"START_DATE\"]\n", "    end_date = study_config[\"END_DATE\"]\n", "    control_id = study_config[\"CONTROL_ID\"]\n", "    treatment_id = study_config[\"TREATMENT_ID\"]\n", "    look_through_groups = study_config[\"LOOK_THROUGH_GROUPS\"]\n", "    \n", "    table_name = get_candidate_incr_dau_result_table_name(study_name, control_id, treatment_id, start_date, end_date, look_through_groups)\n", "    upstream_name = get_candidate_agg_table_name(study_name, control_id, treatment_id, start_date, end_date)\n", "\n", "    if look_through_groups:\n", "        positive_delta_metric = 'n_selections'\n", "    else:\n", "        positive_delta_metric = 'n_selections_individual'\n", "\n", "    incr_dau_totals = []\n", "    incr_dau_dailys = []\n", "    \n", "    for sample_idx in range(n_samples):\n", "        table_name_sample = '_'.join(table_name.split('_')[:(len(table_name.split('_')) - 2)]) + f\"_{n_samples}_{sample_idx}\" + '_' + '_'.join(table_name.split('_')[(len(table_name.split('_')) - 2):])\n", "        sql = f\"\"\"{QUERY_CREATE_TABLE} `{table_name_sample}`\n", "options (\n", "  expiration_timestamp=timestamp(date_add(CURRENT_DATE(), INTERVAL {TEMP_TABLE_RETENTION} DAY))\n", ") as\n", "with\n", "calc as (\n", "select\n", "partition_date,\n", "candidate_l_90_country,\n", "candidate_days_since_last_active,\n", "candidate_app_l7,\n", "candidate_last_country,\n", "candidate_creation_country,\n", "\n", "count(distinct candidate_ghost_user_id) as n_candidate,\n", "\n", "-- Control positive delta metrics\n", "sum(n_selections_individual_exp{control_id}) as n_selections_individual_exp{control_id},\n", "sum(n_selections_via_group_exp{control_id}) as n_selections_via_group_exp{control_id},\n", "sum(n_selections_exp{control_id}) as n_selections_exp{control_id},\n", "count(distinct if({positive_delta_metric}_exp{control_id} > 0 and candidate_is_dau_delta > 0, candidate_ghost_user_id, NULL)) as n_candidate_positive_delta_exp{control_id},\n", "SUM(if({positive_delta_metric}_exp{control_id} > 0 and candidate_is_dau_delta > 0, candidate_is_dau_delta, 0)) as positive_delta_exp{control_id},\n", "\n", "-- Control negative delta metrics\n", "sum(n_correspondent_lost_streak_exp{control_id}) as n_correspondent_lost_streak_exp{control_id},\n", "count(distinct if(n_correspondent_lost_streak_exp{control_id} > 0 and candidate_is_dau_delta < 0, candidate_ghost_user_id, NULL)) as n_candidate_negative_delta_exp{control_id},\n", "SUM(if(n_correspondent_lost_streak_exp{control_id} > 0 and candidate_is_dau_delta < 0, candidate_is_dau_delta, 0)) as negative_delta_exp{control_id},\n", "\n", "-- Treatment positive delta metrics\n", "sum(n_selections_individual_exp{treatment_id}) as n_selections_individual_exp{treatment_id},\n", "sum(n_selections_via_group_exp{treatment_id}) as n_selections_via_group_exp{treatment_id},\n", "sum(n_selections_exp{treatment_id}) as n_selections_exp{treatment_id},\n", "count(distinct if({positive_delta_metric}_exp{treatment_id} > 0 and candidate_is_dau_delta > 0, candidate_ghost_user_id, NULL)) as n_candidate_positive_delta_exp{treatment_id},\n", "SUM(if({positive_delta_metric}_exp{treatment_id} > 0 and candidate_is_dau_delta > 0, candidate_is_dau_delta, 0)) as positive_delta_exp{treatment_id},\n", "\n", "-- Treatment negative delta metrics\n", "sum(n_correspondent_lost_streak_exp{treatment_id}) as n_correspondent_lost_streak_exp{treatment_id},\n", "count(distinct if(n_correspondent_lost_streak_exp{treatment_id} > 0 and candidate_is_dau_delta < 0, candidate_ghost_user_id, NULL)) as n_candidate_negative_delta_exp{treatment_id},\n", "SUM(if(n_correspondent_lost_streak_exp{treatment_id} > 0 and candidate_is_dau_delta < 0, candidate_is_dau_delta, 0)) as negative_delta_exp{treatment_id},\n", "\n", "from `{upstream_name}`\n", "where mod(abs(farm_fingerprint(candidate_ghost_user_id)), {n_samples}) = {sample_idx}\n", "group by all\n", ")\n", "\n", "select\n", "*,\n", "positive_delta_exp{control_id} + negative_delta_exp{control_id} as net_delta_exp{control_id},\n", "positive_delta_exp{treatment_id} + negative_delta_exp{treatment_id} as net_delta_exp{treatment_id},\n", "(positive_delta_exp{treatment_id} + negative_delta_exp{treatment_id}) - (positive_delta_exp{control_id} + negative_delta_exp{control_id}) as incremental_dau_exp{treatment_id},\n", "from calc;\n", "        \"\"\"\n", "\n", "        utils.gbq.read_gbq(sql, project_id=QUERY_PROJECT, dialect='standard', priority=BQ_PRIORITY)\n", "\n", "        sql = f\"\"\"\n", "        select \n", "        sum(incremental_dau_exp{treatment_id}) * {n_samples} as incremental_dau_exp{treatment_id}_total,\n", "        sum(incremental_dau_exp{treatment_id}) / count(distinct partition_date) * {n_samples} as incremental_dau_exp{treatment_id}_daily_avg,\n", "        from {table_name_sample};\n", "        \"\"\"\n", "\n", "        df = utils.gbq.read_gbq(sql, project_id=QUERY_PROJECT, dialect='standard', priority=BQ_PRIORITY)\n", "\n", "        incr_dau_totals.append(df[f\"incremental_dau_exp{treatment_id}_total\"][0])\n", "        incr_dau_dailys.append(df[f\"incremental_dau_exp{treatment_id}_daily_avg\"][0])\n", "        \n", "        # print(f\"Finished sample {sample_idx}...\")\n", "\n", "    mean_incr_dau_totals = np.mean(incr_dau_totals)\n", "    std_incr_dau_totals = np.std(incr_dau_totals)\n", "    se_incr_dau_totals = 1.96 * std_incr_dau_totals / np.sqrt(n_samples)\n", "    ci_incr_dau_total = [round(mean_incr_dau_totals - se_incr_dau_totals, 2), round(mean_incr_dau_totals + se_incr_dau_totals, 2)]\n", "\n", "    \n", "    mean_incr_dau_dailys = np.mean(incr_dau_dailys)\n", "    std_incr_dau_dailys = np.std(incr_dau_dailys)\n", "    se_incr_dau_dailys = 1.96 * std_incr_dau_dailys / np.sqrt(n_samples)\n", "    ci_incr_dau_daily = [round(mean_incr_dau_dailys - se_incr_dau_dailys, 2), round(mean_incr_dau_dailys + se_incr_dau_dailys, 2)]\n", "    \n", "    return ci_incr_dau_total, ci_incr_dau_daily"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_candidate_incr_dau_result_breakdown(study_config, dimension):\n", "    study_name = study_config[\"STUDY_NAME\"]\n", "    start_date = study_config[\"START_DATE\"]\n", "    end_date = study_config[\"END_DATE\"]\n", "    control_id = study_config[\"CONTROL_ID\"]\n", "    treatment_id = study_config[\"TREATMENT_ID\"]\n", "    look_through_groups = study_config[\"LOOK_THROUGH_GROUPS\"]\n", "    \n", "    upstream_name = get_candidate_incr_dau_result_table_name(study_name, control_id, treatment_id, start_date, end_date, look_through_groups)\n", "\n", "    sql = f\"\"\"\n", "SELECT  \n", "{dimension},\n", "\n", "sum(n_candidate) as n_candidate,\n", "\n", "sum(incremental_dau_exp{treatment_id}) as incremental_dau_exp{treatment_id},\n", "sum(incremental_dau_exp{treatment_id}) / count(distinct partition_date) as incremental_dau_exp{treatment_id}_daily_avg,\n", "-- sum(incremental_dau_exp{treatment_id}) / sum(n_candidate) as incremental_dau_exp{treatment_id}_per_candidate,\n", "-- sum(incremental_dau_exp{treatment_id}) / sum(n_candidate) / count(distinct partition_date) as incremental_dau_exp{treatment_id}_per_candidate_daily_avg,\n", "\n", "sum(positive_delta_exp{control_id}) as positive_delta_exp{control_id},\n", "sum(positive_delta_exp{treatment_id}) as positive_delta_exp{treatment_id},\n", "sum(negative_delta_exp{control_id}) as negative_delta_exp{control_id},\n", "sum(negative_delta_exp{treatment_id}) as negative_delta_exp{treatment_id},\n", "sum(net_delta_exp{control_id}) as net_delta_exp{control_id},\n", "sum(net_delta_exp{treatment_id}) as net_delta_exp{treatment_id},\n", "sum(n_selections_individual_exp{control_id}) as n_selections_individual_exp{control_id},\n", "sum(n_selections_individual_exp{treatment_id}) as n_selections_individual_exp{treatment_id},\n", "sum(n_selections_via_group_exp{control_id}) as n_selections_via_group_exp{control_id},\n", "sum(n_selections_via_group_exp{treatment_id}) as n_selections_via_group_exp{treatment_id},\n", "sum(n_correspondent_lost_streak_exp{control_id}) as n_correspondent_lost_streak_exp{control_id},\n", "sum(n_correspondent_lost_streak_exp{treatment_id}) as n_correspondent_lost_streak_exp{treatment_id},\n", "\n", "FROM `{upstream_name}`\n", "group by all\n", "order by {dimension};\n", "    \"\"\"\n", "    \n", "    if DRY_RUN:\n", "        print(sql)\n", "        return\n", "    else:    \n", "        df = utils.gbq.read_gbq(sql, project_id=QUERY_PROJECT, dialect='standard', priority=BQ_PRIORITY)\n", "        return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["create_calc_prep_table(study_config)\n", "create_candidate_agg_table(study_config)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["abtest.report.notebook_print(\"Study Name: {}\".format(STUDY_NAME), \"h3\")\n", "abtest.report.notebook_print(\"Control ID: {}\".format(CONTROL_ID), \"strong\")\n", "abtest.report.notebook_print(\"Treatment ID: {}\".format(TREATMENT_ID), \"strong\")\n", "abtest.report.notebook_print(\n", "    \"Analysis period: {} - {}\".format(STUDY_START_DATE, STUDY_END_DATE), \n", "    \"strong\"\n", ")\n", "abtest.report.notebook_print(\"Group chat look through: {}\".format(LOOK_THROUGH_GROUPS), \"strong\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["incr_dau_total, incr_dau_daily = create_candidate_incr_dau_result_table(study_config)\n", "\n", "ci_incr_dau_total, ci_incr_dau_daily = create_candidate_incr_dau_result_table_sampled(study_config, n_samples=30)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_result = pd.DataFrame({\n", "    'Metric': ['Total Incremental DAU', 'Daily Average Incremental DAU'],\n", "    'Value': [incr_dau_total, incr_dau_daily],\n", "    '95% Considence Interval': [ci_incr_dau_total, ci_incr_dau_daily]\n", "})"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def compute_p_values(df):\n", "#     df[\"SE\"] = (df[\"95% Considence Interval\"][1] - df[\"95% Considence Interval\"][0]) / (2 * 1.96)\n", "    df[\"SE\"] = df.apply(lambda x: (x[\"95% Considence Interval\"][1] - x[\"95% Considence Interval\"][0]) / (2 * 1.96), axis=1)\n", "    df[\"Z\"] = df[\"Value\"] / df[\"SE\"]\n", "    df[\"P-Value\"] = 2 * (1 - stats.norm.cdf(abs(df[\"Z\"])))\n", "    return df\n", "\n", "df_result = compute_p_values(df_result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def color_code_col(x):\n", "    c1 = 'background-color: #F89999; color: white'\n", "    c2 = 'background-color: #F56666; color: white'\n", "    c3 = 'background-color: #D01212; color: white'\n", "    c4 = 'background-color: #B10101; color: white'\n", "    c5 = 'background-color: #99CC99; color: white'\n", "    c6 = 'background-color: #66B366; color: white'\n", "    c7 = 'background-color: #1D831D; color: white'\n", "    c8 = 'background-color: #006400; color: white'\n", "    c0 = '' \n", "    #compare columns\n", "    mask =  (x['P-Value'] < 0.05) & (x['Value'] < 0)\n", "    mask2 = (x['P-Value'] < 0.01) & (x['Value'] < 0)\n", "    mask3 = (x['P-Value'] < 0.001) & (x['Value'] < 0)\n", "    mask4 = (x['P-Value'] < 0.0001) & (x['Value'] < 0)\n", "    mask5 =  (x['P-Value'] < 0.05) & (x['Value'] > 0)\n", "    mask6 = (x['P-Value'] < 0.01) & (x['Value'] > 0)\n", "    mask7 = (x['P-Value'] < 0.001) & (x['Value'] > 0)\n", "    mask8 = (x['P-Value'] < 0.0001) & (x['Value'] > 0)\n", "    #DataFrame with same index and columns names as original filled empty strings\n", "    df1 =  pd.DataFrame(c0, index=x.index, columns=x.columns)\n", "    #modify values of df1 column by boolean mask\n", "    df1.loc[mask, 'Value'] = c1\n", "    df1.loc[mask2, 'Value'] = c2\n", "    df1.loc[mask3, 'Value'] = c3\n", "    df1.loc[mask4, 'Value'] = c4\n", "    df1.loc[mask5, 'Value'] = c5\n", "    df1.loc[mask6, 'Value'] = c6\n", "    df1.loc[mask7, 'Value'] = c7\n", "    df1.loc[mask8, 'Value'] = c8\n", "    return df1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_result.style.apply(color_code_col, axis=None).format(precision=2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["abtest.report.notebook_print(\"Breakdowns\", \"h3\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for dimension in ['partition_date', 'candidate_days_since_last_active', 'candidate_app_l7', 'candidate_l_90_country']:\n", "    abtest.report.notebook_print(\"{}\".format(dimension), \"h4\")\n", "    df_breakdown = get_candidate_incr_dau_result_breakdown(study_config, dimension)\n", "    \n", "    styled_df = df_breakdown.style.set_properties(\n", "        subset=[f\"positive_delta_exp{CONTROL_ID}\", f\"positive_delta_exp{TREATMENT_ID}\"], \n", "        **{'border': '2px solid #99CC99'}\n", "    ).set_properties(\n", "        subset=[f\"negative_delta_exp{CONTROL_ID}\", f\"negative_delta_exp{TREATMENT_ID}\"], \n", "        **{'border': '2px solid #F89999'}\n", "    ).set_properties(\n", "        subset=[\"n_candidate\", f\"incremental_dau_exp{TREATMENT_ID}\", f\"incremental_dau_exp{TREATMENT_ID}_daily_avg\", f\"net_delta_exp{CONTROL_ID}\", f\"net_delta_exp{TREATMENT_ID}\"], \n", "        **{'border': '2px solid #FFD700'}\n", "    ).format(precision=2)\n", "    \n", "    display(styled_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"anaconda-cloud": {}, "celltoolbar": "Tags", "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 4}