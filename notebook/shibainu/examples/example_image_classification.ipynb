{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c12173af-b403-4c4f-a837-461265a16e28", "metadata": {}, "outputs": [], "source": ["from banjo.utils.husky_api import HuskyAPIJobRequest, HuskyAPIJobRuntimeError, HUSKY_PROD_ENV, HUSKY_STG_ENV, ContainerResourceConfig\n", "import datetime\n"]}, {"cell_type": "code", "execution_count": 2, "id": "6f21d1a7-593e-4295-92eb-f0bf4c181507", "metadata": {}, "outputs": [], "source": ["packages = ['av', 'openai', 'banjo']"]}, {"cell_type": "code", "execution_count": 3, "id": "d0788afb-946e-41dd-b92c-9e109e4ac580", "metadata": {}, "outputs": [], "source": ["sql = \"\"\" \n", "        select \n", "        brand_content_id, \n", "        brand_name, \n", "        cms_id, \n", "        content_type,\n", "        garment_type, \n", "        option_id, \n", "        tags, \n", "        token_price, \n", "        token_catalog_id, \n", "        CONCAT(\"https://preview.bitmoji.com/avatar-builder-v3/preview/\", \n", "        case when garment_type in (\"ONE_PIECE\", \"BODYSUIT\") then concat(\"mannequin-center?scale=3&style=5&ua=2&clothing_type=1&version=0&gender=2&top=\", option_id, '&',\n", "        if(tones[SAFE_OFFSET(0)].color1 is not null, concat(lower(garment_type), \"_tone1=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \n", "        if(tones[SAFE_OFFSET(0)].color2 is not null, concat(lower(garment_type), \"_tone2=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \n", "        if(tones[SAFE_OFFSET(0)].color3 is not null, concat(lower(garment_type), \"_tone3=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \n", "        if(tones[SAFE_OFFSET(0)].color4 is not null, concat(lower(garment_type), \"_tone4=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \n", "        if(tones[SAFE_OFFSET(0)].color5 is not null, concat(lower(garment_type), \"_tone5=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \n", "        if(tones[SAFE_OFFSET(0)].color6 is not null, concat(lower(garment_type), \"_tone6=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \n", "        if(tones[SAFE_OFFSET(0)].color7 is not null, concat(lower(garment_type), \"_tone7=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \n", "        if(tones[SAFE_OFFSET(0)].color8 is not null, concat(lower(garment_type), \"_tone8=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"),  \n", "        if(tones[SAFE_OFFSET(0)].color9 is not null, concat(lower(garment_type), \"_tone9=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \n", "        if(tones[SAFE_OFFSET(0)].color10 is not null, concat(lower(garment_type), \"_tone10=\", tones[SAFE_OFFSET(0)].color1), \"\")) \n", "        \n", "        else concat(lower(garment_type), \"?scale=4&style=5&ua=2&clothing_type=1&version=0&gender=2&\",  lower(garment_type), \"=\", option_id, '&', \n", "        if(tones[SAFE_OFFSET(0)].color1 is not null, concat(lower(garment_type), \"_tone1=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \n", "        if(tones[SAFE_OFFSET(0)].color2 is not null, concat(lower(garment_type), \"_tone2=\", tones[SAFE_OFFSET(0)].color2, \"&\"), \"\"), \n", "        if(tones[SAFE_OFFSET(0)].color3 is not null, concat(lower(garment_type), \"_tone3=\", tones[SAFE_OFFSET(0)].color3, \"&\"), \"\"), \n", "        if(tones[SAFE_OFFSET(0)].color4 is not null, concat(lower(garment_type), \"_tone4=\", tones[SAFE_OFFSET(0)].color4, \"&\"), \"\"), \n", "        if(tones[SAFE_OFFSET(0)].color5 is not null, concat(lower(garment_type), \"_tone5=\", tones[SAFE_OFFSET(0)].color5, \"&\"), \"\"), \n", "        if(tones[SAFE_OFFSET(0)].color6 is not null, concat(lower(garment_type), \"_tone6=\", tones[SAFE_OFFSET(0)].color6, \"&\"), \"\"), \n", "        if(tones[SAFE_OFFSET(0)].color7 is not null, concat(lower(garment_type), \"_tone7=\", tones[SAFE_OFFSET(0)].color7, \"&\"), \"\"), \n", "        if(tones[SAFE_OFFSET(0)].color8 is not null, concat(lower(garment_type), \"_tone8=\", tones[SAFE_OFFSET(0)].color8, \"&\"), \"\"),  \n", "        if(tones[SAFE_OFFSET(0)].color9 is not null, concat(lower(garment_type), \"_tone9=\", tones[SAFE_OFFSET(0)].color9, \"&\"), \"\"), \n", "        if(tones[SAFE_OFFSET(0)].color10 is not null, concat(lower(garment_type), \"_tone10=\", tones[SAFE_OFFSET(0)].color10), \"\")) end) as image_url  \n", "        FROM `sc-analytics.report_bitmoji.cms_bitmoji_fashion_metadata_table_2*`\n", "        WHERE _TABLE_SUFFIX = (SELECT max(_TABLE_SUFFIX) FROM `sc-analytics.report_bitmoji.cms_bitmoji_fashion_metadata_table_2*`)\n", "        and content_type in ('GARMENT')\n", "        and garment_type = \"TOP\"\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 4, "id": "164b686f-054c-480c-9792-17c522a2f8cb", "metadata": {}, "outputs": [], "source": ["prompt = \"\"\"\n", "    What is the main color of the top garment? \n", "    The options are: 1) <PERSON>, 2) <PERSON>, 3) <PERSON>, 4) Orange, 5) <PERSON><PERSON>, 6) <PERSON>, 7) <PERSON>, 8) <PERSON>, 9) <PERSON>, 10) <PERSON>, 11) Pink, and 12) Red. \n", "    Can you reply in just one word. \n", "    Do not include any other words or periods\n", "    \"\"\""]}, {"cell_type": "code", "execution_count": 5, "id": "f6f88acf-eb7b-47c7-8193-39523a23a52e", "metadata": {}, "outputs": [], "source": ["parameters = {\n", "    'MODEL_NAME': \"gpt-4o-mini\",\n", "    \"PASS\": \"https://pass.mesh.sc-corp.net/privacyReviews/pass?id=EXAMPLE\",\n", "    \"USE_CUSTOM_PROMPT\": True,\n", "    \"USE_PROMPT_OPTIMIZATION\": <PERSON><PERSON><PERSON>,\n", "    \"DATA_TYPE\": \"image\" ,\n", "    \"DATA_SOURCE\": \"sql\",\n", "    \n", "    \n", "    \"CUSTOM_SQL\": sql ,\n", "    \"COLUMN_NAME\": \"image_url\",\n", "    \"SAMPLE_SIZE\": 20,\n", "    \n", "    \"CUSTOM_PROMPT\": prompt,\n", "    \n", "    \"DESTINATION_TABLE\": 'shibainu_image_test',\n", "    \"DESTINATION_DATASET\": 'dishchenko',\n", "    \"DESTINATION_PROJECT\": 'sc-bq-gcs-billingonly',\n", "    \"RETENTION_DAYS\": 30,\n", "\n", "    \"USE_VALIDATION\": True,\n", "    \"VALIDATION_SAMPLE_SIZE\":  10,\n", "    \"VALIDATION_MODEL_NAME\": 'gpt-4o'\n", "}"]}, {"cell_type": "code", "execution_count": 6, "id": "a0ef9578-8a89-4f9c-8791-9d3d2def4417", "metadata": {}, "outputs": [], "source": ["husky_request = HuskyAPIJobRequest(\n", "    requester_name=\"<PERSON><PERSON><PERSON>\",\n", "    requester_email=\"<EMAIL>\",\n", "    husky_notebook=\"text_classification/shibainu.ipynb\",\n", "    extra_packages=packages,\n", "    subscribers=[],\n", "    husky_service_account=\"<EMAIL>\",\n", "    parameters = parameters,\n", "    husky_env=HUSKY_STG_ENV,\n", "#    husky_env=HUSKY_PROD_ENV,\n", ")\n"]}, {"cell_type": "code", "execution_count": 7, "id": "08b84b4b-c667-4a8d-b5db-51ea5e0f91f5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-06-16 17:50:14.331847\n"]}, {"ename": "HuskyAPIJobRuntimeError", "evalue": "The request 9ff9e9a0-d708-4f04-851f-c7f655c340d5 with config\n{'status': 'failed', 'parameters': {'MODEL_NAME': 'gpt-4o-mini', 'PASS': 'https://pass.mesh.sc-corp.net/privacyReviews/pass?id=EXAMPLE', 'USE_CUSTOM_PROMPT': True, 'USE_PROMPT_OPTIMIZATION': False, 'DATA_TYPE': 'image', 'DATA_SOURCE': 'sql', 'CUSTOM_SQL': ' \\n        select \\n        brand_content_id, \\n        brand_name, \\n        cms_id, \\n        content_type,\\n        garment_type, \\n        option_id, \\n        tags, \\n        token_price, \\n        token_catalog_id, \\n        CONCAT(\"https://preview.bitmoji.com/avatar-builder-v3/preview/\", \\n        case when garment_type in (\"ONE_PIECE\", \"BODYSUIT\") then concat(\"mannequin-center?scale=3&style=5&ua=2&clothing_type=1&version=0&gender=2&top=\", option_id, \\'&\\',\\n        if(tones[SAFE_OFFSET(0)].color1 is not null, concat(lower(garment_type), \"_tone1=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color2 is not null, concat(lower(garment_type), \"_tone2=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color3 is not null, concat(lower(garment_type), \"_tone3=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color4 is not null, concat(lower(garment_type), \"_tone4=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color5 is not null, concat(lower(garment_type), \"_tone5=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color6 is not null, concat(lower(garment_type), \"_tone6=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color7 is not null, concat(lower(garment_type), \"_tone7=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color8 is not null, concat(lower(garment_type), \"_tone8=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"),  \\n        if(tones[SAFE_OFFSET(0)].color9 is not null, concat(lower(garment_type), \"_tone9=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color10 is not null, concat(lower(garment_type), \"_tone10=\", tones[SAFE_OFFSET(0)].color1), \"\")) \\n        \\n        else concat(lower(garment_type), \"?scale=4&style=5&ua=2&clothing_type=1&version=0&gender=2&\",  lower(garment_type), \"=\", option_id, \\'&\\', \\n        if(tones[SAFE_OFFSET(0)].color1 is not null, concat(lower(garment_type), \"_tone1=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color2 is not null, concat(lower(garment_type), \"_tone2=\", tones[SAFE_OFFSET(0)].color2, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color3 is not null, concat(lower(garment_type), \"_tone3=\", tones[SAFE_OFFSET(0)].color3, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color4 is not null, concat(lower(garment_type), \"_tone4=\", tones[SAFE_OFFSET(0)].color4, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color5 is not null, concat(lower(garment_type), \"_tone5=\", tones[SAFE_OFFSET(0)].color5, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color6 is not null, concat(lower(garment_type), \"_tone6=\", tones[SAFE_OFFSET(0)].color6, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color7 is not null, concat(lower(garment_type), \"_tone7=\", tones[SAFE_OFFSET(0)].color7, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color8 is not null, concat(lower(garment_type), \"_tone8=\", tones[SAFE_OFFSET(0)].color8, \"&\"), \"\"),  \\n        if(tones[SAFE_OFFSET(0)].color9 is not null, concat(lower(garment_type), \"_tone9=\", tones[SAFE_OFFSET(0)].color9, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color10 is not null, concat(lower(garment_type), \"_tone10=\", tones[SAFE_OFFSET(0)].color10), \"\")) end) as image_url  \\n        FROM `sc-analytics.report_bitmoji.cms_bitmoji_fashion_metadata_table_2*`\\n        WHERE _TABLE_SUFFIX = (SELECT max(_TABLE_SUFFIX) FROM `sc-analytics.report_bitmoji.cms_bitmoji_fashion_metadata_table_2*`)\\n        and content_type in (\\'GARMENT\\')\\n        and garment_type = \"TOP\"\\n', 'COLUMN_NAME': 'image_url', 'SAMPLE_SIZE': 20, 'CUSTOM_PROMPT': '\\n    What is the main color of the top garment? \\n    The options are: 1) Black, 2) Gray, 3) White, 4) Orange, 5) Beige, 6) Brown, 7) Yellow, 8) Green, 9) Blue, 10) Purple, 11) Pink, and 12) Red. \\n    Can you reply in just one word. \\n    Do not include any other words or periods\\n    ', 'DESTINATION_TABLE': 'shibainu_image_test', 'DESTINATION_DATASET': 'dishchenko', 'DESTINATION_PROJECT': 'sc-bq-gcs-billingonly', 'RETENTION_DAYS': 30, 'USE_VALIDATION': True, 'VALIDATION_SAMPLE_SIZE': 10, 'VALIDATION_MODEL_NAME': 'gpt-4o', 'report_key': '0a2b3d51a73d1747ea3add7ed7bc6b78'}, 'request_id': '9ff9e9a0-d708-4f04-851f-c7f655c340d5', 'job_name': 'vellum-exporter-20250617005022-e3411j'}\nfailed with the following error message:\nTraceback (most recent call last):\n  File \"/srv/venv/lib/python3.10/site-packages/vellum/converter.py\", line 40, in convert\n    pm.execute_notebook(notebook, output_temp.name, parameters=parameters,\n  File \"/srv/venv/lib/python3.10/site-packages/papermill/execute.py\", line 131, in execute_notebook\n    raise_for_execution_errors(nb, output_path)\n  File \"/srv/venv/lib/python3.10/site-packages/papermill/execute.py\", line 251, in raise_for_execution_errors\n    raise error\npapermill.exceptions.PapermillExecutionError: \n---------------------------------------------------------------------------\nException encountered at \"In [13]\":\n---------------------------------------------------------------------------\nTypeError                                 Traceback (most recent call last)\nCell In[13], line 1\n----> 1 classification_model = Classification(model = MODEL_NAME, service_account = SERVICE_ACCOUNT, prompt=PROMPT, \n      2                                       temperature = TEMPERATURE, max_token = MAX_TOKEN, validation_model = VALIDATION_MODEL_NAME)\n      3 classification_model.send_message(\"Say: 'The model {model_name} is alive' \". format(model_name = MODEL_NAME))\n\nTypeError: __init__() got an unexpected keyword argument 'model'\n\nerror_name:\n---------------------------------------------------------------------------\nException encountered at \"In [13]\":\n---------------------------------------------------------------------------\nTypeError                                 Traceback (most recent call last)\nCell In[13], line 1\n----> 1 classification_model = Classification(model = MODEL_NAME, service_account = SERVICE_ACCOUNT, prompt=PROMPT, \n      2                                       temperature = TEMPERATURE, max_token = MAX_TOKEN, validation_model = VALIDATION_MODEL_NAME)\n      3 classification_model.send_message(\"Say: 'The model {model_name} is alive' \". format(model_name = MODEL_NAME))\n\nTypeError: __init__() got an unexpected keyword argument 'model'\n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mHuskyAPIJobRuntimeError\u001b[0m                   <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[7], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28mprint\u001b[39m(datetime\u001b[38;5;241m.\u001b[39mdatetime\u001b[38;5;241m.\u001b[39mnow())\n\u001b[0;32m----> 2\u001b[0m \u001b[43mhusky_request\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mwait_for_completion\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28mprint\u001b[39m(husky_request\u001b[38;5;241m.\u001b[39mreport_url)\n\u001b[1;32m      4\u001b[0m \u001b[38;5;28mprint\u001b[39m(datetime\u001b[38;5;241m.\u001b[39mdatetime\u001b[38;5;241m.\u001b[39mnow())\n", "File \u001b[0;32m~/anaconda3/envs/py38/lib/python3.8/site-packages/banjo/utils/husky_api.py:218\u001b[0m, in \u001b[0;36mHuskyAPIJobRequest.execute\u001b[0;34m(self, wait_for_completion)\u001b[0m\n\u001b[1;32m    216\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpublish(message)\n\u001b[1;32m    217\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m wait_for_completion:\n\u001b[0;32m--> 218\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mwait_for_job_completion\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/anaconda3/envs/py38/lib/python3.8/site-packages/banjo/utils/husky_api.py:164\u001b[0m, in \u001b[0;36mHuskyAPIJobRequest.wait_for_job_completion\u001b[0;34m(self, request_id)\u001b[0m\n\u001b[1;32m    162\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    163\u001b[0m     error_message \u001b[38;5;241m=\u001b[39m log_data\u001b[38;5;241m.\u001b[39mpop(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124merror_message\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNo error message found in log\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m--> 164\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m HuskyAPIJobRuntimeError(\n\u001b[1;32m    165\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mThe request \u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m with config\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124mfailed with the following error message:\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mformat(\n\u001b[1;32m    166\u001b[0m             request_id,\n\u001b[1;32m    167\u001b[0m             log_data,\n\u001b[1;32m    168\u001b[0m             error_message\n\u001b[1;32m    169\u001b[0m         )\n\u001b[1;32m    170\u001b[0m     )\n", "\u001b[0;31mHuskyAPIJobRuntimeError\u001b[0m: The request 9ff9e9a0-d708-4f04-851f-c7f655c340d5 with config\n{'status': 'failed', 'parameters': {'MODEL_NAME': 'gpt-4o-mini', 'PASS': 'https://pass.mesh.sc-corp.net/privacyReviews/pass?id=EXAMPLE', 'USE_CUSTOM_PROMPT': True, 'USE_PROMPT_OPTIMIZATION': False, 'DATA_TYPE': 'image', 'DATA_SOURCE': 'sql', 'CUSTOM_SQL': ' \\n        select \\n        brand_content_id, \\n        brand_name, \\n        cms_id, \\n        content_type,\\n        garment_type, \\n        option_id, \\n        tags, \\n        token_price, \\n        token_catalog_id, \\n        CONCAT(\"https://preview.bitmoji.com/avatar-builder-v3/preview/\", \\n        case when garment_type in (\"ONE_PIECE\", \"BODYSUIT\") then concat(\"mannequin-center?scale=3&style=5&ua=2&clothing_type=1&version=0&gender=2&top=\", option_id, \\'&\\',\\n        if(tones[SAFE_OFFSET(0)].color1 is not null, concat(lower(garment_type), \"_tone1=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color2 is not null, concat(lower(garment_type), \"_tone2=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color3 is not null, concat(lower(garment_type), \"_tone3=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color4 is not null, concat(lower(garment_type), \"_tone4=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color5 is not null, concat(lower(garment_type), \"_tone5=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color6 is not null, concat(lower(garment_type), \"_tone6=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color7 is not null, concat(lower(garment_type), \"_tone7=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color8 is not null, concat(lower(garment_type), \"_tone8=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"),  \\n        if(tones[SAFE_OFFSET(0)].color9 is not null, concat(lower(garment_type), \"_tone9=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color10 is not null, concat(lower(garment_type), \"_tone10=\", tones[SAFE_OFFSET(0)].color1), \"\")) \\n        \\n        else concat(lower(garment_type), \"?scale=4&style=5&ua=2&clothing_type=1&version=0&gender=2&\",  lower(garment_type), \"=\", option_id, \\'&\\', \\n        if(tones[SAFE_OFFSET(0)].color1 is not null, concat(lower(garment_type), \"_tone1=\", tones[SAFE_OFFSET(0)].color1, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color2 is not null, concat(lower(garment_type), \"_tone2=\", tones[SAFE_OFFSET(0)].color2, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color3 is not null, concat(lower(garment_type), \"_tone3=\", tones[SAFE_OFFSET(0)].color3, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color4 is not null, concat(lower(garment_type), \"_tone4=\", tones[SAFE_OFFSET(0)].color4, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color5 is not null, concat(lower(garment_type), \"_tone5=\", tones[SAFE_OFFSET(0)].color5, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color6 is not null, concat(lower(garment_type), \"_tone6=\", tones[SAFE_OFFSET(0)].color6, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color7 is not null, concat(lower(garment_type), \"_tone7=\", tones[SAFE_OFFSET(0)].color7, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color8 is not null, concat(lower(garment_type), \"_tone8=\", tones[SAFE_OFFSET(0)].color8, \"&\"), \"\"),  \\n        if(tones[SAFE_OFFSET(0)].color9 is not null, concat(lower(garment_type), \"_tone9=\", tones[SAFE_OFFSET(0)].color9, \"&\"), \"\"), \\n        if(tones[SAFE_OFFSET(0)].color10 is not null, concat(lower(garment_type), \"_tone10=\", tones[SAFE_OFFSET(0)].color10), \"\")) end) as image_url  \\n        FROM `sc-analytics.report_bitmoji.cms_bitmoji_fashion_metadata_table_2*`\\n        WHERE _TABLE_SUFFIX = (SELECT max(_TABLE_SUFFIX) FROM `sc-analytics.report_bitmoji.cms_bitmoji_fashion_metadata_table_2*`)\\n        and content_type in (\\'GARMENT\\')\\n        and garment_type = \"TOP\"\\n', 'COLUMN_NAME': 'image_url', 'SAMPLE_SIZE': 20, 'CUSTOM_PROMPT': '\\n    What is the main color of the top garment? \\n    The options are: 1) Black, 2) Gray, 3) White, 4) Orange, 5) Beige, 6) Brown, 7) Yellow, 8) Green, 9) Blue, 10) Purple, 11) Pink, and 12) Red. \\n    Can you reply in just one word. \\n    Do not include any other words or periods\\n    ', 'DESTINATION_TABLE': 'shibainu_image_test', 'DESTINATION_DATASET': 'dishchenko', 'DESTINATION_PROJECT': 'sc-bq-gcs-billingonly', 'RETENTION_DAYS': 30, 'USE_VALIDATION': True, 'VALIDATION_SAMPLE_SIZE': 10, 'VALIDATION_MODEL_NAME': 'gpt-4o', 'report_key': '0a2b3d51a73d1747ea3add7ed7bc6b78'}, 'request_id': '9ff9e9a0-d708-4f04-851f-c7f655c340d5', 'job_name': 'vellum-exporter-20250617005022-e3411j'}\nfailed with the following error message:\nTraceback (most recent call last):\n  File \"/srv/venv/lib/python3.10/site-packages/vellum/converter.py\", line 40, in convert\n    pm.execute_notebook(notebook, output_temp.name, parameters=parameters,\n  File \"/srv/venv/lib/python3.10/site-packages/papermill/execute.py\", line 131, in execute_notebook\n    raise_for_execution_errors(nb, output_path)\n  File \"/srv/venv/lib/python3.10/site-packages/papermill/execute.py\", line 251, in raise_for_execution_errors\n    raise error\npapermill.exceptions.PapermillExecutionError: \n---------------------------------------------------------------------------\nException encountered at \"In [13]\":\n---------------------------------------------------------------------------\nTypeError                                 Traceback (most recent call last)\nCell In[13], line 1\n----> 1 classification_model = Classification(model = MODEL_NAME, service_account = SERVICE_ACCOUNT, prompt=PROMPT, \n      2                                       temperature = TEMPERATURE, max_token = MAX_TOKEN, validation_model = VALIDATION_MODEL_NAME)\n      3 classification_model.send_message(\"Say: 'The model {model_name} is alive' \". format(model_name = MODEL_NAME))\n\nTypeError: __init__() got an unexpected keyword argument 'model'\n\nerror_name:\n---------------------------------------------------------------------------\nException encountered at \"In [13]\":\n---------------------------------------------------------------------------\nTypeError                                 Traceback (most recent call last)\nCell In[13], line 1\n----> 1 classification_model = Classification(model = MODEL_NAME, service_account = SERVICE_ACCOUNT, prompt=PROMPT, \n      2                                       temperature = TEMPERATURE, max_token = MAX_TOKEN, validation_model = VALIDATION_MODEL_NAME)\n      3 classification_model.send_message(\"Say: 'The model {model_name} is alive' \". format(model_name = MODEL_NAME))\n\nTypeError: __init__() got an unexpected keyword argument 'model'\n"]}], "source": ["print(datetime.datetime.now())\n", "husky_request.execute(wait_for_completion=True)\n", "print(husky_request.report_url)\n", "print(datetime.datetime.now())"]}, {"cell_type": "code", "execution_count": null, "id": "e80f67c1-9711-4c32-8087-64ec2c7dffc2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py38", "language": "python", "name": "py38"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.19"}}, "nbformat": 4, "nbformat_minor": 5}