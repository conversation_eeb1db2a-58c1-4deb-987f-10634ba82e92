{"cells": [{"cell_type": "code", "execution_count": null, "id": "d3f7f78b-f019-4551-b901-d984b3077157", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py38", "language": "python", "name": "py38"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.19"}}, "nbformat": 4, "nbformat_minor": 5}