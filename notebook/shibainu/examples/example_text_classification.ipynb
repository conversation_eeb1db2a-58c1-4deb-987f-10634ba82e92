{"cells": [{"cell_type": "code", "execution_count": 6, "id": "c12173af-b403-4c4f-a837-461265a16e28", "metadata": {}, "outputs": [], "source": ["from banjo.utils.husky_api import HuskyAPIJobRequest, HuskyAPIJobRuntimeError, HUSKY_PROD_ENV, HUSKY_STG_ENV, ContainerResourceConfig\n", "import datetime\n"]}, {"cell_type": "code", "execution_count": null, "id": "4b6aacc4-dd13-4578-949c-fc67d46ea68f", "metadata": {}, "outputs": [], "source": ["# OpenAI"]}, {"cell_type": "code", "execution_count": 7, "id": "f6f88acf-eb7b-47c7-8193-39523a23a52e", "metadata": {}, "outputs": [], "source": ["parameters = {\n", "    'MODEL_NAME': \"gpt-4o-mini\",\n", "    \"PASS\": \"https://pass.mesh.sc-corp.net/privacyReviews/pass?id=EXAMPLE\",\n", "    \"USE_CUSTOM_PROMPT\": False,\n", "    \"USE_PROMPT_OPTIMIZATION\": <PERSON><PERSON><PERSON>,\n", "    \"DATA_TYPE\": \"text\" ,\n", "    \"DATA_SOURCE\": \"table\",\n", "    \n", "    \"TABLE_NAME\": \"sc-bq-gcs-billingonly.dishchenko.wiki_characters\" ,\n", "    \"COLUMN_NAME\": \"name\",\n", "    \"SAMPLE_SIZE\": 100,\n", "    \n", "    \"CLASSES\": \"real character, fictional character\",\n", "    \n", "    \"DESTINATION_TABLE\": 'shibainu_text_test',\n", "    \"DESTINATION_DATASET\": 'dishchenko',\n", "    \"DESTINATION_PROJECT\": 'sc-bq-gcs-billingonly',\n", "    \"RETENTION_DAYS\": None,\n", "\n", "    \"USE_VALIDATION\": True,\n", "    \"VALIDATION_SAMPLE_SIZE\": 10,\n", "    \"VALIDATION_MODEL_NAME\": 'o1-mini'\n", "}"]}, {"cell_type": "code", "execution_count": 8, "id": "a0ef9578-8a89-4f9c-8791-9d3d2def4417", "metadata": {}, "outputs": [], "source": ["husky_request = HuskyAPIJobRequest(\n", "    requester_name=\"<PERSON><PERSON><PERSON>\",\n", "    requester_email=\"<EMAIL>\",\n", "    husky_notebook=\"text_classification/shibainu.ipynb\",\n", "    extra_packages=packages,\n", "    subscribers=[],\n", "    husky_service_account=\"<EMAIL>\",\n", "    parameters = parameters,\n", "    husky_env=HUSKY_STG_ENV,\n", "#    husky_env=HUSKY_PROD_ENV,\n", ")\n"]}, {"cell_type": "code", "execution_count": 9, "id": "08b84b4b-c667-4a8d-b5db-51ea5e0f91f5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-06-17 19:25:34.783199\n"]}, {"ename": "HuskyAPIJobRuntimeError", "evalue": "The request 5094fc23-cff7-48ac-a156-6e603fea850c with config\n{'status': 'failed', 'parameters': {'MODEL_NAME': 'gpt-4o-mini', 'PASS': 'https://pass.mesh.sc-corp.net/privacyReviews/pass?id=EXAMPLE', 'USE_CUSTOM_PROMPT': False, 'USE_PROMPT_OPTIMIZATION': False, 'DATA_TYPE': 'text', 'DATA_SOURCE': 'table', 'TABLE_NAME': 'sc-bq-gcs-billingonly.dishchenko.wiki_characters', 'COLUMN_NAME': 'name', 'SAMPLE_SIZE': 100, 'CLASSES': 'real character, fictional character', 'DESTINATION_TABLE': 'shibainu_text_test', 'DESTINATION_DATASET': 'dishchenko', 'DESTINATION_PROJECT': 'sc-bq-gcs-billingonly', 'RETENTION_DAYS': None, 'USE_VALIDATION': True, 'VALIDATION_SAMPLE_SIZE': 10, 'VALIDATION_MODEL_NAME': 'o1-mini', 'report_key': 'e0140219e3719030743993cacb73fb41'}, 'request_id': '5094fc23-cff7-48ac-a156-6e603fea850c', 'job_name': 'vellum-exporter-20250618022542-dex7st'}\nfailed with the following error message:\nTraceback (most recent call last):\n  File \"/srv/venv/lib/python3.10/site-packages/vellum/converter.py\", line 40, in convert\n    pm.execute_notebook(notebook, output_temp.name, parameters=parameters,\n  File \"/srv/venv/lib/python3.10/site-packages/papermill/execute.py\", line 131, in execute_notebook\n    raise_for_execution_errors(nb, output_path)\n  File \"/srv/venv/lib/python3.10/site-packages/papermill/execute.py\", line 251, in raise_for_execution_errors\n    raise error\npapermill.exceptions.PapermillExecutionError: \n---------------------------------------------------------------------------\nException encountered at \"In [1]\":\n---------------------------------------------------------------------------\nModuleNotFoundError                       Traceback (most recent call last)\nCell In[1], line 3\n      1 from datetime import date, datetime, timedelta, timezone\n      2 from banjo import utils\n----> 3 from banjo.utils.shibainu_v2 import Classification, Validation, Visualization, estimate_run_cost, configure_logger\n      4 import logging\n      5 configure_logger(level=logging.ERROR)\n\nModuleNotFoundError: No module named 'banjo.utils.shibainu_v2'\n\nerror_name:\n---------------------------------------------------------------------------\nException encountered at \"In [1]\":\n---------------------------------------------------------------------------\nModuleNotFoundError                       Traceback (most recent call last)\nCell In[1], line 3\n      1 from datetime import date, datetime, timedelta, timezone\n      2 from banjo import utils\n----> 3 from banjo.utils.shibainu_v2 import Classification, Validation, Visualization, estimate_run_cost, configure_logger\n      4 import logging\n      5 configure_logger(level=logging.ERROR)\n\nModuleNotFoundError: No module named 'banjo.utils.shibainu_v2'\n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mHuskyAPIJobRuntimeError\u001b[0m                   <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[9], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28mprint\u001b[39m(datetime\u001b[38;5;241m.\u001b[39mdatetime\u001b[38;5;241m.\u001b[39mnow())\n\u001b[0;32m----> 2\u001b[0m \u001b[43mhusky_request\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mwait_for_completion\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28mprint\u001b[39m(husky_request\u001b[38;5;241m.\u001b[39mreport_url)\n\u001b[1;32m      4\u001b[0m \u001b[38;5;28mprint\u001b[39m(datetime\u001b[38;5;241m.\u001b[39mdatetime\u001b[38;5;241m.\u001b[39mnow())\n", "File \u001b[0;32m~/anaconda3/envs/py38/lib/python3.8/site-packages/banjo/utils/husky_api.py:218\u001b[0m, in \u001b[0;36mHuskyAPIJobRequest.execute\u001b[0;34m(self, wait_for_completion)\u001b[0m\n\u001b[1;32m    216\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpublish(message)\n\u001b[1;32m    217\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m wait_for_completion:\n\u001b[0;32m--> 218\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mwait_for_job_completion\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/anaconda3/envs/py38/lib/python3.8/site-packages/banjo/utils/husky_api.py:164\u001b[0m, in \u001b[0;36mHuskyAPIJobRequest.wait_for_job_completion\u001b[0;34m(self, request_id)\u001b[0m\n\u001b[1;32m    162\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    163\u001b[0m     error_message \u001b[38;5;241m=\u001b[39m log_data\u001b[38;5;241m.\u001b[39mpop(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124merror_message\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNo error message found in log\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m--> 164\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m HuskyAPIJobRuntimeError(\n\u001b[1;32m    165\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mThe request \u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m with config\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124mfailed with the following error message:\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mformat(\n\u001b[1;32m    166\u001b[0m             request_id,\n\u001b[1;32m    167\u001b[0m             log_data,\n\u001b[1;32m    168\u001b[0m             error_message\n\u001b[1;32m    169\u001b[0m         )\n\u001b[1;32m    170\u001b[0m     )\n", "\u001b[0;31mHusk<PERSON>APIJobRuntimeError\u001b[0m: The request 5094fc23-cff7-48ac-a156-6e603fea850c with config\n{'status': 'failed', 'parameters': {'MODEL_NAME': 'gpt-4o-mini', 'PASS': 'https://pass.mesh.sc-corp.net/privacyReviews/pass?id=EXAMPLE', 'USE_CUSTOM_PROMPT': False, 'USE_PROMPT_OPTIMIZATION': False, 'DATA_TYPE': 'text', 'DATA_SOURCE': 'table', 'TABLE_NAME': 'sc-bq-gcs-billingonly.dishchenko.wiki_characters', 'COLUMN_NAME': 'name', 'SAMPLE_SIZE': 100, 'CLASSES': 'real character, fictional character', 'DESTINATION_TABLE': 'shibainu_text_test', 'DESTINATION_DATASET': 'dishchenko', 'DESTINATION_PROJECT': 'sc-bq-gcs-billingonly', 'RETENTION_DAYS': None, 'USE_VALIDATION': True, 'VALIDATION_SAMPLE_SIZE': 10, 'VALIDATION_MODEL_NAME': 'o1-mini', 'report_key': 'e0140219e3719030743993cacb73fb41'}, 'request_id': '5094fc23-cff7-48ac-a156-6e603fea850c', 'job_name': 'vellum-exporter-20250618022542-dex7st'}\nfailed with the following error message:\nTraceback (most recent call last):\n  File \"/srv/venv/lib/python3.10/site-packages/vellum/converter.py\", line 40, in convert\n    pm.execute_notebook(notebook, output_temp.name, parameters=parameters,\n  File \"/srv/venv/lib/python3.10/site-packages/papermill/execute.py\", line 131, in execute_notebook\n    raise_for_execution_errors(nb, output_path)\n  File \"/srv/venv/lib/python3.10/site-packages/papermill/execute.py\", line 251, in raise_for_execution_errors\n    raise error\npapermill.exceptions.PapermillExecutionError: \n---------------------------------------------------------------------------\nException encountered at \"In [1]\":\n---------------------------------------------------------------------------\nModuleNotFoundError                       Traceback (most recent call last)\nCell In[1], line 3\n      1 from datetime import date, datetime, timedelta, timezone\n      2 from banjo import utils\n----> 3 from banjo.utils.shibainu_v2 import Classification, Validation, Visualization, estimate_run_cost, configure_logger\n      4 import logging\n      5 configure_logger(level=logging.ERROR)\n\nModuleNotFoundError: No module named 'banjo.utils.shibainu_v2'\n\nerror_name:\n---------------------------------------------------------------------------\nException encountered at \"In [1]\":\n---------------------------------------------------------------------------\nModuleNotFoundError                       Traceback (most recent call last)\nCell In[1], line 3\n      1 from datetime import date, datetime, timedelta, timezone\n      2 from banjo import utils\n----> 3 from banjo.utils.shibainu_v2 import Classification, Validation, Visualization, estimate_run_cost, configure_logger\n      4 import logging\n      5 configure_logger(level=logging.ERROR)\n\nModuleNotFoundError: No module named 'banjo.utils.shibainu_v2'\n"]}], "source": ["print(datetime.datetime.now())\n", "husky_request.execute(wait_for_completion=True)\n", "print(husky_request.report_url)\n", "print(datetime.datetime.now())"]}, {"cell_type": "code", "execution_count": null, "id": "c519e853-a68a-49ae-8f2b-4893e98afb58", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py38", "language": "python", "name": "py38"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.19"}}, "nbformat": 4, "nbformat_minor": 5}