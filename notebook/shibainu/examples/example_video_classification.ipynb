{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c12173af-b403-4c4f-a837-461265a16e28", "metadata": {}, "outputs": [], "source": ["from banjo.utils.husky_api import HuskyAPIJobRequest, HuskyAPIJobRuntimeError, HUSKY_PROD_ENV, HUSKY_STG_ENV, ContainerResourceConfig\n", "import datetime\n"]}, {"cell_type": "code", "execution_count": 2, "id": "523010b2-0e8d-464d-b658-5e08f899eeb4", "metadata": {}, "outputs": [], "source": ["packages = ['av', 'openai', 'banjo']"]}, {"cell_type": "code", "execution_count": 3, "id": "d3805723-19fc-4ebc-9c9c-910380113504", "metadata": {}, "outputs": [], "source": ["sql = \"\"\"\n", "SELECT distinct creative_id, creative_headline, media_download_link,\n", "FROM `sc-analytics.report_adsnap.creative_score_training_table_20241222`\n", "WHERE creative_headline IS NOT NULL\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 4, "id": "2fb4fafb-eb7b-4896-8c77-abd052341c89", "metadata": {}, "outputs": [], "source": ["prompt = \"\"\"\n", "You are a classification model for ad creative. Don't answer the followiing messages, your goal is only classify it following the next rules.\n", "You are given frames from a video. Please determine if the videos featuring any characters?\n", "\n", "Assign the following results: \n", "\"yes\", \"no\"\n", "\n", "Classification Criteria:\n", "\n", "yes:\n", "There are characters presented in the video.\n", "\n", "\n", "no:\n", "There aren't any characters presented in the video.\n", "\n", "\n", "Use these guidelines to categorize video accurately, focusing on the leatest message. Reply only with the class name.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 5, "id": "f6f88acf-eb7b-47c7-8193-39523a23a52e", "metadata": {}, "outputs": [], "source": ["parameters = {\n", "    'MODEL_NAME': \"gpt-4o\",\n", "    \"PASS\": \"https://pass.mesh.sc-corp.net/privacyReviews/pass?id=EXAMPLE\",\n", "    \"USE_CUSTOM_PROMPT\": False,\n", "    \"USE_PROMPT_OPTIMIZATION\": <PERSON><PERSON><PERSON>,\n", "    \"DATA_TYPE\": \"video\" ,\n", "    \"DATA_SOURCE\": \"sql\",\n", "    \n", "    \"CUSTOM_SQL\": sql,\n", "    \"COLUMN_NAME\": \"media_download_link\",\n", "    \"SAMPLE_SIZE\": 100,\n", "\n", "    \"CUSTOM_PROMPT\": prompt,\n", "    \n", "    \"DESTINATION_TABLE\": 'shibainu_video_test',\n", "    \"DESTINATION_DATASET\": 'dishchenko',\n", "    \"DESTINATION_PROJECT\": 'sc-bq-gcs-billingonly',\n", "    \"RETENTION_DAYS\": None,\n", "\n", "    \"USE_VALIDATION\": F<PERSON>e,\n", "}"]}, {"cell_type": "code", "execution_count": 6, "id": "a0ef9578-8a89-4f9c-8791-9d3d2def4417", "metadata": {}, "outputs": [], "source": ["husky_request = HuskyAPIJobRequest(\n", "    requester_name=\"<PERSON><PERSON><PERSON>\",\n", "    requester_email=\"<EMAIL>\",\n", "    husky_notebook=\"text_classification/shibainu.ipynb\",\n", "    extra_packages=packages,\n", "    subscribers=[],\n", "    husky_service_account=\"<EMAIL>\",\n", "    parameters = parameters,\n", "    husky_env=HUSKY_STG_ENV,\n", "#    husky_env=HUSKY_PROD_ENV,\n", ")\n"]}, {"cell_type": "code", "execution_count": 7, "id": "08b84b4b-c667-4a8d-b5db-51ea5e0f91f5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-02-20 18:16:29.093277\n"]}, {"ename": "HuskyAPIJobRuntimeError", "evalue": "The request 3b3ece13-7b21-48e5-ad50-a905e81d4e18 with config\n{'status': 'failed', 'parameters': {'MODEL_NAME': 'gpt-4o', 'PASS': 'https://pass.mesh.sc-corp.net/privacyReviews/pass?id=EXAMPLE', 'USE_CUSTOM_PROMPT': False, 'USE_PROMPT_OPTIMIZATION': False, 'DATA_TYPE': 'video', 'DATA_SOURCE': 'sql', 'CUSTOM_SQL': '\\nSELECT distinct creative_id, creative_headline, media_download_link,\\nFROM `sc-analytics.report_adsnap.creative_score_training_table_20241222`\\nWHERE creative_headline IS NOT NULL\\n', 'COLUMN_NAME': 'media_download_link', 'SAMPLE_SIZE': 100, 'CUSTOM_PROMPT': '\\nYou are a classification model for ad creative. Don\\'t answer the followiing messages, your goal is only classify it following the next rules.\\nYou are given frames from a video. Please determine if the videos featuring any characters?\\n\\nAssign the following results: \\n\"yes\", \"no\"\\n\\nClassification Criteria:\\n\\nyes:\\nThere are characters presented in the video.\\n\\n\\nno:\\nThere aren\\'t any characters presented in the video.\\n\\n\\nUse these guidelines to categorize video accurately, focusing on the leatest message. Reply only with the class name.\\n', 'DESTINATION_TABLE': 'shibainu_video_test', 'DESTINATION_DATASET': 'dishchenko', 'DESTINATION_PROJECT': 'sc-bq-gcs-billingonly', 'RETENTION_DAYS': None, 'USE_VALIDATION': False, 'report_key': 'f5d5ec59ff2de806fce6fd747f2db678'}, 'request_id': '3b3ece13-7b21-48e5-ad50-a905e81d4e18', 'job_name': 'vellum-exporter-20250221021630-ax8258'}\nfailed with the following error message:\nTraceback (most recent call last):\n  File \"/srv/venv/lib/python3.10/site-packages/vellum/converter.py\", line 40, in convert\n    pm.execute_notebook(notebook, output_temp.name, parameters=parameters,\n  File \"/srv/venv/lib/python3.10/site-packages/papermill/execute.py\", line 131, in execute_notebook\n    raise_for_execution_errors(nb, output_path)\n  File \"/srv/venv/lib/python3.10/site-packages/papermill/execute.py\", line 251, in raise_for_execution_errors\n    raise error\npapermill.exceptions.PapermillExecutionError: \n---------------------------------------------------------------------------\nException encountered at \"In [14]\":\n---------------------------------------------------------------------------\nForbidden                                 Traceback (most recent call last)\nCell In[14], line 3\n      1 ## Download data\n----> 3 data = utils.gbq.read_gbq(SQL, \n      4                           project_id=PROJECT,\n      5                           dialect=\"standard\",\n      6                           priority = PRIORITY)\n      7 dataset = data[COLUMN_NAME].values\n\nFile /srv/venv38/lib/python3.8/site-packages/banjo/utils/gbq.py:653, in read_gbq(query, project_id, client, index_col, col_order, dialect, location, priority, maximum_billing_tier, job_config, use_bqstorage_api, bq_retry)\n    601 def read_gbq(query, project_id=None, client=None, index_col=None, col_order=None,\n    602              dialect='legacy', location=None, priority=\"BATCH\",\n    603              maximum_billing_tier=None, job_config=None,\n    604              use_bqstorage_api=False,\n    605              bq_retry=None,\n    606              ):\n    607     \"\"\"Read query results as pandas.DataFrame using google.cloud.bigquery\n    608 \n    609     Parameters\n   (...)\n    650     0  164656\n    651     \"\"\"\n--> 653     return batch_read_gbq(\n    654         [query],\n    655         project_id=project_id, client=client, index_col=index_col, col_order=col_order,\n    656         dialect=dialect, location=location, priority=priority,\n    657         maximum_billing_tier=maximum_billing_tier, job_config=job_config,\n    658         use_bqstorage_api=use_bqstorage_api,\n    659         bq_retry=bq_retry,\n    660     )[0]\n\nFile /srv/venv38/lib/python3.8/site-packages/banjo/utils/gbq.py:782, in batch_read_gbq(queries, project_id, client, index_col, col_order, dialect, location, priority, maximum_billing_tier, job_config, use_bqstorage_api, bq_retry, parallel, parallel_concurrency)\n    780             raise e\n    781 else:\n--> 782     final_dfs = [_fetch_data(_submit_query_job(query, job_dialect)) for query, job_dialect in queries_and_dialects]\n    784 return [_reindex_df(df, index_col=index_col, col_order=col_order) for df in final_dfs]\n\nFile /srv/venv38/lib/python3.8/site-packages/banjo/utils/gbq.py:782, in <listcomp>(.0)\n    780             raise e\n    781 else:\n--> 782     final_dfs = [_fetch_data(_submit_query_job(query, job_dialect)) for query, job_dialect in queries_and_dialects]\n    784 return [_reindex_df(df, index_col=index_col, col_order=col_order) for df in final_dfs]\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_unary.py:293, in Retry.__call__.<locals>.retry_wrapped_func(*args, **kwargs)\n    289 target = functools.partial(func, *args, **kwargs)\n    290 sleep_generator = exponential_sleep_generator(\n    291     self._initial, self._maximum, multiplier=self._multiplier\n    292 )\n--> 293 return retry_target(\n    294     target,\n    295     self._predicate,\n    296     sleep_generator,\n    297     timeout=self._timeout,\n    298     on_error=on_error,\n    299 )\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_unary.py:153, in retry_target(target, predicate, sleep_generator, timeout, on_error, exception_factory, **kwargs)\n    149 # pylint: disable=broad-except\n    150 # This function explicitly must deal with broad exceptions.\n    151 except Exception as exc:\n    152     # defer to shared logic for handling errors\n--> 153     _retry_error_helper(\n    154         exc,\n    155         deadline,\n    156         sleep,\n    157         error_list,\n    158         predicate,\n    159         on_error,\n    160         exception_factory,\n    161         timeout,\n    162     )\n    163     # if exception not raised, sleep before next attempt\n    164     time.sleep(sleep)\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_base.py:212, in _retry_error_helper(exc, deadline, next_sleep, error_list, predicate_fn, on_error_fn, exc_factory_fn, original_timeout)\n    206 if not predicate_fn(exc):\n    207     final_exc, source_exc = exc_factory_fn(\n    208         error_list,\n    209         RetryFailureReason.NON_RETRYABLE_ERROR,\n    210         original_timeout,\n    211     )\n--> 212     raise final_exc from source_exc\n    213 if on_error_fn is not None:\n    214     on_error_fn(exc)\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_unary.py:144, in retry_target(target, predicate, sleep_generator, timeout, on_error, exception_factory, **kwargs)\n    142 for sleep in sleep_generator:\n    143     try:\n--> 144         result = target()\n    145         if inspect.isawaitable(result):\n    146             warnings.warn(_ASYNC_RETRY_WARNING)\n\nFile /srv/venv38/lib/python3.8/site-packages/banjo/utils/gbq.py:756, in batch_read_gbq.<locals>._fetch_data(job)\n    753 @bq_retry\n    754 def _fetch_data(job):\n    755     try:\n--> 756         return job.result().to_arrow(bqstorage_client=bqstorage_client).to_pandas()\n    757     except (NotFound, BadRequest) as e:\n    758         raise_with_traceback(BigQueryRuntimeError(\n    759             '{0}\\nQuery submitted:\\n{1}'.format(e, sqlparse.format(job.query, reindent=True))\n    760         ))\n\nFile /srv/venv38/lib/python3.8/site-packages/google/cloud/bigquery/job/query.py:1681, in QueryJob.result(self, page_size, max_results, retry, timeout, start_index, job_retry)\n   1676     remaining_timeout = None\n   1678 if remaining_timeout is None:\n   1679     # Since is_job_done() calls jobs.getQueryResults, which is a\n   1680     # long-running API, don't delay the next request at all.\n-> 1681     while not is_job_done():\n   1682         pass\n   1683 else:\n   1684     # Use a monotonic clock since we don't actually care about\n   1685     # daylight savings or similar, just the elapsed time.\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_unary.py:293, in Retry.__call__.<locals>.retry_wrapped_func(*args, **kwargs)\n    289 target = functools.partial(func, *args, **kwargs)\n    290 sleep_generator = exponential_sleep_generator(\n    291     self._initial, self._maximum, multiplier=self._multiplier\n    292 )\n--> 293 return retry_target(\n    294     target,\n    295     self._predicate,\n    296     sleep_generator,\n    297     timeout=self._timeout,\n    298     on_error=on_error,\n    299 )\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_unary.py:153, in retry_target(target, predicate, sleep_generator, timeout, on_error, exception_factory, **kwargs)\n    149 # pylint: disable=broad-except\n    150 # This function explicitly must deal with broad exceptions.\n    151 except Exception as exc:\n    152     # defer to shared logic for handling errors\n--> 153     _retry_error_helper(\n    154         exc,\n    155         deadline,\n    156         sleep,\n    157         error_list,\n    158         predicate,\n    159         on_error,\n    160         exception_factory,\n    161         timeout,\n    162     )\n    163     # if exception not raised, sleep before next attempt\n    164     time.sleep(sleep)\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_base.py:212, in _retry_error_helper(exc, deadline, next_sleep, error_list, predicate_fn, on_error_fn, exc_factory_fn, original_timeout)\n    206 if not predicate_fn(exc):\n    207     final_exc, source_exc = exc_factory_fn(\n    208         error_list,\n    209         RetryFailureReason.NON_RETRYABLE_ERROR,\n    210         original_timeout,\n    211     )\n--> 212     raise final_exc from source_exc\n    213 if on_error_fn is not None:\n    214     on_error_fn(exc)\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_unary.py:144, in retry_target(target, predicate, sleep_generator, timeout, on_error, exception_factory, **kwargs)\n    142 for sleep in sleep_generator:\n    143     try:\n--> 144         result = target()\n    145         if inspect.isawaitable(result):\n    146             warnings.warn(_ASYNC_RETRY_WARNING)\n\nFile /srv/venv38/lib/python3.8/site-packages/google/cloud/bigquery/job/query.py:1630, in QueryJob.result.<locals>.is_job_done()\n   1607 if job_failed_exception is not None:\n   1608     # Only try to restart the query job if the job failed for\n   1609     # a retriable reason. For example, don't restart the query\n   (...)\n   1627     # into an exception that can be processed by the\n   1628     # `job_retry` predicate.\n   1629     restart_query_job = True\n-> 1630     raise job_failed_exception\n   1631 else:\n   1632     # Make sure that the _query_results are cached so we\n   1633     # can return a complete RowIterator.\n   (...)\n   1639     # making any extra API calls if the previous loop\n   1640     # iteration fetched the finished job.\n   1641     self._reload_query_results(\n   1642         retry=retry, **reload_query_results_kwargs\n   1643     )\n\nForbidden: 403 Access Denied: Table sc-analytics:report_adsnap.creative_score_training_table_20241222: User does not have permission to query table sc-analytics:report_adsnap.creative_score_training_table_20241222, or perhaps it does not exist.; reason: accessDenied, message: Access Denied: Table sc-analytics:report_adsnap.creative_score_training_table_20241222: User does not have permission to query table sc-analytics:report_adsnap.creative_score_training_table_20241222, or perhaps it does not exist.\n\nLocation: US\nJob ID: 7e70ca16-837f-4e73-b20d-cce231f635f1\n\n\nerror_name:\n---------------------------------------------------------------------------\nException encountered at \"In [14]\":\n---------------------------------------------------------------------------\nForbidden                                 Traceback (most recent call last)\nCell In[14], line 3\n      1 ## Download data\n----> 3 data = utils.gbq.read_gbq(SQL, \n      4                           project_id=PROJECT,\n      5                           dialect=\"standard\",\n      6                           priority = PRIORITY)\n      7 dataset = data[COLUMN_NAME].values\n\nFile /srv/venv38/lib/python3.8/site-packages/banjo/utils/gbq.py:653, in read_gbq(query, project_id, client, index_col, col_order, dialect, location, priority, maximum_billing_tier, job_config, use_bqstorage_api, bq_retry)\n    601 def read_gbq(query, project_id=None, client=None, index_col=None, col_order=None,\n    602              dialect='legacy', location=None, priority=\"BATCH\",\n    603              maximum_billing_tier=None, job_config=None,\n    604              use_bqstorage_api=False,\n    605              bq_retry=None,\n    606              ):\n    607     \"\"\"Read query results as pandas.DataFrame using google.cloud.bigquery\n    608 \n    609     Parameters\n   (...)\n    650     0  164656\n    651     \"\"\"\n--> 653     return batch_read_gbq(\n    654         [query],\n    655         project_id=project_id, client=client, index_col=index_col, col_order=col_order,\n    656         dialect=dialect, location=location, priority=priority,\n    657         maximum_billing_tier=maximum_billing_tier, job_config=job_config,\n    658         use_bqstorage_api=use_bqstorage_api,\n    659         bq_retry=bq_retry,\n    660     )[0]\n\nFile /srv/venv38/lib/python3.8/site-packages/banjo/utils/gbq.py:782, in batch_read_gbq(queries, project_id, client, index_col, col_order, dialect, location, priority, maximum_billing_tier, job_config, use_bqstorage_api, bq_retry, parallel, parallel_concurrency)\n    780             raise e\n    781 else:\n--> 782     final_dfs = [_fetch_data(_submit_query_job(query, job_dialect)) for query, job_dialect in queries_and_dialects]\n    784 return [_reindex_df(df, index_col=index_col, col_order=col_order) for df in final_dfs]\n\nFile /srv/venv38/lib/python3.8/site-packages/banjo/utils/gbq.py:782, in <listcomp>(.0)\n    780             raise e\n    781 else:\n--> 782     final_dfs = [_fetch_data(_submit_query_job(query, job_dialect)) for query, job_dialect in queries_and_dialects]\n    784 return [_reindex_df(df, index_col=index_col, col_order=col_order) for df in final_dfs]\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_unary.py:293, in Retry.__call__.<locals>.retry_wrapped_func(*args, **kwargs)\n    289 target = functools.partial(func, *args, **kwargs)\n    290 sleep_generator = exponential_sleep_generator(\n    291     self._initial, self._maximum, multiplier=self._multiplier\n    292 )\n--> 293 return retry_target(\n    294     target,\n    295     self._predicate,\n    296     sleep_generator,\n    297     timeout=self._timeout,\n    298     on_error=on_error,\n    299 )\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_unary.py:153, in retry_target(target, predicate, sleep_generator, timeout, on_error, exception_factory, **kwargs)\n    149 # pylint: disable=broad-except\n    150 # This function explicitly must deal with broad exceptions.\n    151 except Exception as exc:\n    152     # defer to shared logic for handling errors\n--> 153     _retry_error_helper(\n    154         exc,\n    155         deadline,\n    156         sleep,\n    157         error_list,\n    158         predicate,\n    159         on_error,\n    160         exception_factory,\n    161         timeout,\n    162     )\n    163     # if exception not raised, sleep before next attempt\n    164     time.sleep(sleep)\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_base.py:212, in _retry_error_helper(exc, deadline, next_sleep, error_list, predicate_fn, on_error_fn, exc_factory_fn, original_timeout)\n    206 if not predicate_fn(exc):\n    207     final_exc, source_exc = exc_factory_fn(\n    208         error_list,\n    209         RetryFailureReason.NON_RETRYABLE_ERROR,\n    210         original_timeout,\n    211     )\n--> 212     raise final_exc from source_exc\n    213 if on_error_fn is not None:\n    214     on_error_fn(exc)\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_unary.py:144, in retry_target(target, predicate, sleep_generator, timeout, on_error, exception_factory, **kwargs)\n    142 for sleep in sleep_generator:\n    143     try:\n--> 144         result = target()\n    145         if inspect.isawaitable(result):\n    146             warnings.warn(_ASYNC_RETRY_WARNING)\n\nFile /srv/venv38/lib/python3.8/site-packages/banjo/utils/gbq.py:756, in batch_read_gbq.<locals>._fetch_data(job)\n    753 @bq_retry\n    754 def _fetch_data(job):\n    755     try:\n--> 756         return job.result().to_arrow(bqstorage_client=bqstorage_client).to_pandas()\n    757     except (NotFound, BadRequest) as e:\n    758         raise_with_traceback(BigQueryRuntimeError(\n    759             '{0}\\nQuery submitted:\\n{1}'.format(e, sqlparse.format(job.query, reindent=True))\n    760         ))\n\nFile /srv/venv38/lib/python3.8/site-packages/google/cloud/bigquery/job/query.py:1681, in QueryJob.result(self, page_size, max_results, retry, timeout, start_index, job_retry)\n   1676     remaining_timeout = None\n   1678 if remaining_timeout is None:\n   1679     # Since is_job_done() calls jobs.getQueryResults, which is a\n   1680     # long-running API, don't delay the next request at all.\n-> 1681     while not is_job_done():\n   1682         pass\n   1683 else:\n   1684     # Use a monotonic clock since we don't actually care about\n   1685     # daylight savings or similar, just the elapsed time.\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_unary.py:293, in Retry.__call__.<locals>.retry_wrapped_func(*args, **kwargs)\n    289 target = functools.partial(func, *args, **kwargs)\n    290 sleep_generator = exponential_sleep_generator(\n    291     self._initial, self._maximum, multiplier=self._multiplier\n    292 )\n--> 293 return retry_target(\n    294     target,\n    295     self._predicate,\n    296     sleep_generator,\n    297     timeout=self._timeout,\n    298     on_error=on_error,\n    299 )\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_unary.py:153, in retry_target(target, predicate, sleep_generator, timeout, on_error, exception_factory, **kwargs)\n    149 # pylint: disable=broad-except\n    150 # This function explicitly must deal with broad exceptions.\n    151 except Exception as exc:\n    152     # defer to shared logic for handling errors\n--> 153     _retry_error_helper(\n    154         exc,\n    155         deadline,\n    156         sleep,\n    157         error_list,\n    158         predicate,\n    159         on_error,\n    160         exception_factory,\n    161         timeout,\n    162     )\n    163     # if exception not raised, sleep before next attempt\n    164     time.sleep(sleep)\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_base.py:212, in _retry_error_helper(exc, deadline, next_sleep, error_list, predicate_fn, on_error_fn, exc_factory_fn, original_timeout)\n    206 if not predicate_fn(exc):\n    207     final_exc, source_exc = exc_factory_fn(\n    208         error_list,\n    209         RetryFailureReason.NON_RETRYABLE_ERROR,\n    210         original_timeout,\n    211     )\n--> 212     raise final_exc from source_exc\n    213 if on_error_fn is not None:\n    214     on_error_fn(exc)\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_unary.py:144, in retry_target(target, predicate, sleep_generator, timeout, on_error, exception_factory, **kwargs)\n    142 for sleep in sleep_generator:\n    143     try:\n--> 144         result = target()\n    145         if inspect.isawaitable(result):\n    146             warnings.warn(_ASYNC_RETRY_WARNING)\n\nFile /srv/venv38/lib/python3.8/site-packages/google/cloud/bigquery/job/query.py:1630, in QueryJob.result.<locals>.is_job_done()\n   1607 if job_failed_exception is not None:\n   1608     # Only try to restart the query job if the job failed for\n   1609     # a retriable reason. For example, don't restart the query\n   (...)\n   1627     # into an exception that can be processed by the\n   1628     # `job_retry` predicate.\n   1629     restart_query_job = True\n-> 1630     raise job_failed_exception\n   1631 else:\n   1632     # Make sure that the _query_results are cached so we\n   1633     # can return a complete RowIterator.\n   (...)\n   1639     # making any extra API calls if the previous loop\n   1640     # iteration fetched the finished job.\n   1641     self._reload_query_results(\n   1642         retry=retry, **reload_query_results_kwargs\n   1643     )\n\nForbidden: 403 Access Denied: Table sc-analytics:report_adsnap.creative_score_training_table_20241222: User does not have permission to query table sc-analytics:report_adsnap.creative_score_training_table_20241222, or perhaps it does not exist.; reason: accessDenied, message: Access Denied: Table sc-analytics:report_adsnap.creative_score_training_table_20241222: User does not have permission to query table sc-analytics:report_adsnap.creative_score_training_table_20241222, or perhaps it does not exist.\n\nLocation: US\nJob ID: 7e70ca16-837f-4e73-b20d-cce231f635f1\n\n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mHuskyAPIJobRuntimeError\u001b[0m                   <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[7], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28mprint\u001b[39m(datetime\u001b[38;5;241m.\u001b[39mdatetime\u001b[38;5;241m.\u001b[39mnow())\n\u001b[0;32m----> 2\u001b[0m \u001b[43mhusky_request\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexecute\u001b[49m\u001b[43m(\u001b[49m\u001b[43mwait_for_completion\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28mprint\u001b[39m(husky_request\u001b[38;5;241m.\u001b[39mreport_url)\n\u001b[1;32m      4\u001b[0m \u001b[38;5;28mprint\u001b[39m(datetime\u001b[38;5;241m.\u001b[39mdatetime\u001b[38;5;241m.\u001b[39mnow())\n", "File \u001b[0;32m~/projects/pyanalytics/banjo/utils/husky_api.py:218\u001b[0m, in \u001b[0;36mHuskyAPIJobRequest.execute\u001b[0;34m(self, wait_for_completion)\u001b[0m\n\u001b[1;32m    216\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpublish(message)\n\u001b[1;32m    217\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m wait_for_completion:\n\u001b[0;32m--> 218\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mwait_for_job_completion\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/projects/pyanalytics/banjo/utils/husky_api.py:164\u001b[0m, in \u001b[0;36mHuskyAPIJobRequest.wait_for_job_completion\u001b[0;34m(self, request_id)\u001b[0m\n\u001b[1;32m    162\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    163\u001b[0m     error_message \u001b[38;5;241m=\u001b[39m log_data\u001b[38;5;241m.\u001b[39mpop(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124merror_message\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mNo error message found in log\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m--> 164\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m HuskyAPIJobRuntimeError(\n\u001b[1;32m    165\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mThe request \u001b[39m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m with config\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124mfailed with the following error message:\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;241m.\u001b[39mformat(\n\u001b[1;32m    166\u001b[0m             request_id,\n\u001b[1;32m    167\u001b[0m             log_data,\n\u001b[1;32m    168\u001b[0m             error_message\n\u001b[1;32m    169\u001b[0m         )\n\u001b[1;32m    170\u001b[0m     )\n", "\u001b[0;31mHuskyAPIJobRuntimeError\u001b[0m: The request 3b3ece13-7b21-48e5-ad50-a905e81d4e18 with config\n{'status': 'failed', 'parameters': {'MODEL_NAME': 'gpt-4o', 'PASS': 'https://pass.mesh.sc-corp.net/privacyReviews/pass?id=EXAMPLE', 'USE_CUSTOM_PROMPT': False, 'USE_PROMPT_OPTIMIZATION': False, 'DATA_TYPE': 'video', 'DATA_SOURCE': 'sql', 'CUSTOM_SQL': '\\nSELECT distinct creative_id, creative_headline, media_download_link,\\nFROM `sc-analytics.report_adsnap.creative_score_training_table_20241222`\\nWHERE creative_headline IS NOT NULL\\n', 'COLUMN_NAME': 'media_download_link', 'SAMPLE_SIZE': 100, 'CUSTOM_PROMPT': '\\nYou are a classification model for ad creative. Don\\'t answer the followiing messages, your goal is only classify it following the next rules.\\nYou are given frames from a video. Please determine if the videos featuring any characters?\\n\\nAssign the following results: \\n\"yes\", \"no\"\\n\\nClassification Criteria:\\n\\nyes:\\nThere are characters presented in the video.\\n\\n\\nno:\\nThere aren\\'t any characters presented in the video.\\n\\n\\nUse these guidelines to categorize video accurately, focusing on the leatest message. Reply only with the class name.\\n', 'DESTINATION_TABLE': 'shibainu_video_test', 'DESTINATION_DATASET': 'dishchenko', 'DESTINATION_PROJECT': 'sc-bq-gcs-billingonly', 'RETENTION_DAYS': None, 'USE_VALIDATION': False, 'report_key': 'f5d5ec59ff2de806fce6fd747f2db678'}, 'request_id': '3b3ece13-7b21-48e5-ad50-a905e81d4e18', 'job_name': 'vellum-exporter-20250221021630-ax8258'}\nfailed with the following error message:\nTraceback (most recent call last):\n  File \"/srv/venv/lib/python3.10/site-packages/vellum/converter.py\", line 40, in convert\n    pm.execute_notebook(notebook, output_temp.name, parameters=parameters,\n  File \"/srv/venv/lib/python3.10/site-packages/papermill/execute.py\", line 131, in execute_notebook\n    raise_for_execution_errors(nb, output_path)\n  File \"/srv/venv/lib/python3.10/site-packages/papermill/execute.py\", line 251, in raise_for_execution_errors\n    raise error\npapermill.exceptions.PapermillExecutionError: \n---------------------------------------------------------------------------\nException encountered at \"In [14]\":\n---------------------------------------------------------------------------\nForbidden                                 Traceback (most recent call last)\nCell In[14], line 3\n      1 ## Download data\n----> 3 data = utils.gbq.read_gbq(SQL, \n      4                           project_id=PROJECT,\n      5                           dialect=\"standard\",\n      6                           priority = PRIORITY)\n      7 dataset = data[COLUMN_NAME].values\n\nFile /srv/venv38/lib/python3.8/site-packages/banjo/utils/gbq.py:653, in read_gbq(query, project_id, client, index_col, col_order, dialect, location, priority, maximum_billing_tier, job_config, use_bqstorage_api, bq_retry)\n    601 def read_gbq(query, project_id=None, client=None, index_col=None, col_order=None,\n    602              dialect='legacy', location=None, priority=\"BATCH\",\n    603              maximum_billing_tier=None, job_config=None,\n    604              use_bqstorage_api=False,\n    605              bq_retry=None,\n    606              ):\n    607     \"\"\"Read query results as pandas.DataFrame using google.cloud.bigquery\n    608 \n    609     Parameters\n   (...)\n    650     0  164656\n    651     \"\"\"\n--> 653     return batch_read_gbq(\n    654         [query],\n    655         project_id=project_id, client=client, index_col=index_col, col_order=col_order,\n    656         dialect=dialect, location=location, priority=priority,\n    657         maximum_billing_tier=maximum_billing_tier, job_config=job_config,\n    658         use_bqstorage_api=use_bqstorage_api,\n    659         bq_retry=bq_retry,\n    660     )[0]\n\nFile /srv/venv38/lib/python3.8/site-packages/banjo/utils/gbq.py:782, in batch_read_gbq(queries, project_id, client, index_col, col_order, dialect, location, priority, maximum_billing_tier, job_config, use_bqstorage_api, bq_retry, parallel, parallel_concurrency)\n    780             raise e\n    781 else:\n--> 782     final_dfs = [_fetch_data(_submit_query_job(query, job_dialect)) for query, job_dialect in queries_and_dialects]\n    784 return [_reindex_df(df, index_col=index_col, col_order=col_order) for df in final_dfs]\n\nFile /srv/venv38/lib/python3.8/site-packages/banjo/utils/gbq.py:782, in <listcomp>(.0)\n    780             raise e\n    781 else:\n--> 782     final_dfs = [_fetch_data(_submit_query_job(query, job_dialect)) for query, job_dialect in queries_and_dialects]\n    784 return [_reindex_df(df, index_col=index_col, col_order=col_order) for df in final_dfs]\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_unary.py:293, in Retry.__call__.<locals>.retry_wrapped_func(*args, **kwargs)\n    289 target = functools.partial(func, *args, **kwargs)\n    290 sleep_generator = exponential_sleep_generator(\n    291     self._initial, self._maximum, multiplier=self._multiplier\n    292 )\n--> 293 return retry_target(\n    294     target,\n    295     self._predicate,\n    296     sleep_generator,\n    297     timeout=self._timeout,\n    298     on_error=on_error,\n    299 )\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_unary.py:153, in retry_target(target, predicate, sleep_generator, timeout, on_error, exception_factory, **kwargs)\n    149 # pylint: disable=broad-except\n    150 # This function explicitly must deal with broad exceptions.\n    151 except Exception as exc:\n    152     # defer to shared logic for handling errors\n--> 153     _retry_error_helper(\n    154         exc,\n    155         deadline,\n    156         sleep,\n    157         error_list,\n    158         predicate,\n    159         on_error,\n    160         exception_factory,\n    161         timeout,\n    162     )\n    163     # if exception not raised, sleep before next attempt\n    164     time.sleep(sleep)\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_base.py:212, in _retry_error_helper(exc, deadline, next_sleep, error_list, predicate_fn, on_error_fn, exc_factory_fn, original_timeout)\n    206 if not predicate_fn(exc):\n    207     final_exc, source_exc = exc_factory_fn(\n    208         error_list,\n    209         RetryFailureReason.NON_RETRYABLE_ERROR,\n    210         original_timeout,\n    211     )\n--> 212     raise final_exc from source_exc\n    213 if on_error_fn is not None:\n    214     on_error_fn(exc)\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_unary.py:144, in retry_target(target, predicate, sleep_generator, timeout, on_error, exception_factory, **kwargs)\n    142 for sleep in sleep_generator:\n    143     try:\n--> 144         result = target()\n    145         if inspect.isawaitable(result):\n    146             warnings.warn(_ASYNC_RETRY_WARNING)\n\nFile /srv/venv38/lib/python3.8/site-packages/banjo/utils/gbq.py:756, in batch_read_gbq.<locals>._fetch_data(job)\n    753 @bq_retry\n    754 def _fetch_data(job):\n    755     try:\n--> 756         return job.result().to_arrow(bqstorage_client=bqstorage_client).to_pandas()\n    757     except (NotFound, BadRequest) as e:\n    758         raise_with_traceback(BigQueryRuntimeError(\n    759             '{0}\\nQuery submitted:\\n{1}'.format(e, sqlparse.format(job.query, reindent=True))\n    760         ))\n\nFile /srv/venv38/lib/python3.8/site-packages/google/cloud/bigquery/job/query.py:1681, in QueryJob.result(self, page_size, max_results, retry, timeout, start_index, job_retry)\n   1676     remaining_timeout = None\n   1678 if remaining_timeout is None:\n   1679     # Since is_job_done() calls jobs.getQueryResults, which is a\n   1680     # long-running API, don't delay the next request at all.\n-> 1681     while not is_job_done():\n   1682         pass\n   1683 else:\n   1684     # Use a monotonic clock since we don't actually care about\n   1685     # daylight savings or similar, just the elapsed time.\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_unary.py:293, in Retry.__call__.<locals>.retry_wrapped_func(*args, **kwargs)\n    289 target = functools.partial(func, *args, **kwargs)\n    290 sleep_generator = exponential_sleep_generator(\n    291     self._initial, self._maximum, multiplier=self._multiplier\n    292 )\n--> 293 return retry_target(\n    294     target,\n    295     self._predicate,\n    296     sleep_generator,\n    297     timeout=self._timeout,\n    298     on_error=on_error,\n    299 )\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_unary.py:153, in retry_target(target, predicate, sleep_generator, timeout, on_error, exception_factory, **kwargs)\n    149 # pylint: disable=broad-except\n    150 # This function explicitly must deal with broad exceptions.\n    151 except Exception as exc:\n    152     # defer to shared logic for handling errors\n--> 153     _retry_error_helper(\n    154         exc,\n    155         deadline,\n    156         sleep,\n    157         error_list,\n    158         predicate,\n    159         on_error,\n    160         exception_factory,\n    161         timeout,\n    162     )\n    163     # if exception not raised, sleep before next attempt\n    164     time.sleep(sleep)\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_base.py:212, in _retry_error_helper(exc, deadline, next_sleep, error_list, predicate_fn, on_error_fn, exc_factory_fn, original_timeout)\n    206 if not predicate_fn(exc):\n    207     final_exc, source_exc = exc_factory_fn(\n    208         error_list,\n    209         RetryFailureReason.NON_RETRYABLE_ERROR,\n    210         original_timeout,\n    211     )\n--> 212     raise final_exc from source_exc\n    213 if on_error_fn is not None:\n    214     on_error_fn(exc)\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_unary.py:144, in retry_target(target, predicate, sleep_generator, timeout, on_error, exception_factory, **kwargs)\n    142 for sleep in sleep_generator:\n    143     try:\n--> 144         result = target()\n    145         if inspect.isawaitable(result):\n    146             warnings.warn(_ASYNC_RETRY_WARNING)\n\nFile /srv/venv38/lib/python3.8/site-packages/google/cloud/bigquery/job/query.py:1630, in QueryJob.result.<locals>.is_job_done()\n   1607 if job_failed_exception is not None:\n   1608     # Only try to restart the query job if the job failed for\n   1609     # a retriable reason. For example, don't restart the query\n   (...)\n   1627     # into an exception that can be processed by the\n   1628     # `job_retry` predicate.\n   1629     restart_query_job = True\n-> 1630     raise job_failed_exception\n   1631 else:\n   1632     # Make sure that the _query_results are cached so we\n   1633     # can return a complete RowIterator.\n   (...)\n   1639     # making any extra API calls if the previous loop\n   1640     # iteration fetched the finished job.\n   1641     self._reload_query_results(\n   1642         retry=retry, **reload_query_results_kwargs\n   1643     )\n\nForbidden: 403 Access Denied: Table sc-analytics:report_adsnap.creative_score_training_table_20241222: User does not have permission to query table sc-analytics:report_adsnap.creative_score_training_table_20241222, or perhaps it does not exist.; reason: accessDenied, message: Access Denied: Table sc-analytics:report_adsnap.creative_score_training_table_20241222: User does not have permission to query table sc-analytics:report_adsnap.creative_score_training_table_20241222, or perhaps it does not exist.\n\nLocation: US\nJob ID: 7e70ca16-837f-4e73-b20d-cce231f635f1\n\n\nerror_name:\n---------------------------------------------------------------------------\nException encountered at \"In [14]\":\n---------------------------------------------------------------------------\nForbidden                                 Traceback (most recent call last)\nCell In[14], line 3\n      1 ## Download data\n----> 3 data = utils.gbq.read_gbq(SQL, \n      4                           project_id=PROJECT,\n      5                           dialect=\"standard\",\n      6                           priority = PRIORITY)\n      7 dataset = data[COLUMN_NAME].values\n\nFile /srv/venv38/lib/python3.8/site-packages/banjo/utils/gbq.py:653, in read_gbq(query, project_id, client, index_col, col_order, dialect, location, priority, maximum_billing_tier, job_config, use_bqstorage_api, bq_retry)\n    601 def read_gbq(query, project_id=None, client=None, index_col=None, col_order=None,\n    602              dialect='legacy', location=None, priority=\"BATCH\",\n    603              maximum_billing_tier=None, job_config=None,\n    604              use_bqstorage_api=False,\n    605              bq_retry=None,\n    606              ):\n    607     \"\"\"Read query results as pandas.DataFrame using google.cloud.bigquery\n    608 \n    609     Parameters\n   (...)\n    650     0  164656\n    651     \"\"\"\n--> 653     return batch_read_gbq(\n    654         [query],\n    655         project_id=project_id, client=client, index_col=index_col, col_order=col_order,\n    656         dialect=dialect, location=location, priority=priority,\n    657         maximum_billing_tier=maximum_billing_tier, job_config=job_config,\n    658         use_bqstorage_api=use_bqstorage_api,\n    659         bq_retry=bq_retry,\n    660     )[0]\n\nFile /srv/venv38/lib/python3.8/site-packages/banjo/utils/gbq.py:782, in batch_read_gbq(queries, project_id, client, index_col, col_order, dialect, location, priority, maximum_billing_tier, job_config, use_bqstorage_api, bq_retry, parallel, parallel_concurrency)\n    780             raise e\n    781 else:\n--> 782     final_dfs = [_fetch_data(_submit_query_job(query, job_dialect)) for query, job_dialect in queries_and_dialects]\n    784 return [_reindex_df(df, index_col=index_col, col_order=col_order) for df in final_dfs]\n\nFile /srv/venv38/lib/python3.8/site-packages/banjo/utils/gbq.py:782, in <listcomp>(.0)\n    780             raise e\n    781 else:\n--> 782     final_dfs = [_fetch_data(_submit_query_job(query, job_dialect)) for query, job_dialect in queries_and_dialects]\n    784 return [_reindex_df(df, index_col=index_col, col_order=col_order) for df in final_dfs]\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_unary.py:293, in Retry.__call__.<locals>.retry_wrapped_func(*args, **kwargs)\n    289 target = functools.partial(func, *args, **kwargs)\n    290 sleep_generator = exponential_sleep_generator(\n    291     self._initial, self._maximum, multiplier=self._multiplier\n    292 )\n--> 293 return retry_target(\n    294     target,\n    295     self._predicate,\n    296     sleep_generator,\n    297     timeout=self._timeout,\n    298     on_error=on_error,\n    299 )\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_unary.py:153, in retry_target(target, predicate, sleep_generator, timeout, on_error, exception_factory, **kwargs)\n    149 # pylint: disable=broad-except\n    150 # This function explicitly must deal with broad exceptions.\n    151 except Exception as exc:\n    152     # defer to shared logic for handling errors\n--> 153     _retry_error_helper(\n    154         exc,\n    155         deadline,\n    156         sleep,\n    157         error_list,\n    158         predicate,\n    159         on_error,\n    160         exception_factory,\n    161         timeout,\n    162     )\n    163     # if exception not raised, sleep before next attempt\n    164     time.sleep(sleep)\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_base.py:212, in _retry_error_helper(exc, deadline, next_sleep, error_list, predicate_fn, on_error_fn, exc_factory_fn, original_timeout)\n    206 if not predicate_fn(exc):\n    207     final_exc, source_exc = exc_factory_fn(\n    208         error_list,\n    209         RetryFailureReason.NON_RETRYABLE_ERROR,\n    210         original_timeout,\n    211     )\n--> 212     raise final_exc from source_exc\n    213 if on_error_fn is not None:\n    214     on_error_fn(exc)\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_unary.py:144, in retry_target(target, predicate, sleep_generator, timeout, on_error, exception_factory, **kwargs)\n    142 for sleep in sleep_generator:\n    143     try:\n--> 144         result = target()\n    145         if inspect.isawaitable(result):\n    146             warnings.warn(_ASYNC_RETRY_WARNING)\n\nFile /srv/venv38/lib/python3.8/site-packages/banjo/utils/gbq.py:756, in batch_read_gbq.<locals>._fetch_data(job)\n    753 @bq_retry\n    754 def _fetch_data(job):\n    755     try:\n--> 756         return job.result().to_arrow(bqstorage_client=bqstorage_client).to_pandas()\n    757     except (NotFound, BadRequest) as e:\n    758         raise_with_traceback(BigQueryRuntimeError(\n    759             '{0}\\nQuery submitted:\\n{1}'.format(e, sqlparse.format(job.query, reindent=True))\n    760         ))\n\nFile /srv/venv38/lib/python3.8/site-packages/google/cloud/bigquery/job/query.py:1681, in QueryJob.result(self, page_size, max_results, retry, timeout, start_index, job_retry)\n   1676     remaining_timeout = None\n   1678 if remaining_timeout is None:\n   1679     # Since is_job_done() calls jobs.getQueryResults, which is a\n   1680     # long-running API, don't delay the next request at all.\n-> 1681     while not is_job_done():\n   1682         pass\n   1683 else:\n   1684     # Use a monotonic clock since we don't actually care about\n   1685     # daylight savings or similar, just the elapsed time.\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_unary.py:293, in Retry.__call__.<locals>.retry_wrapped_func(*args, **kwargs)\n    289 target = functools.partial(func, *args, **kwargs)\n    290 sleep_generator = exponential_sleep_generator(\n    291     self._initial, self._maximum, multiplier=self._multiplier\n    292 )\n--> 293 return retry_target(\n    294     target,\n    295     self._predicate,\n    296     sleep_generator,\n    297     timeout=self._timeout,\n    298     on_error=on_error,\n    299 )\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_unary.py:153, in retry_target(target, predicate, sleep_generator, timeout, on_error, exception_factory, **kwargs)\n    149 # pylint: disable=broad-except\n    150 # This function explicitly must deal with broad exceptions.\n    151 except Exception as exc:\n    152     # defer to shared logic for handling errors\n--> 153     _retry_error_helper(\n    154         exc,\n    155         deadline,\n    156         sleep,\n    157         error_list,\n    158         predicate,\n    159         on_error,\n    160         exception_factory,\n    161         timeout,\n    162     )\n    163     # if exception not raised, sleep before next attempt\n    164     time.sleep(sleep)\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_base.py:212, in _retry_error_helper(exc, deadline, next_sleep, error_list, predicate_fn, on_error_fn, exc_factory_fn, original_timeout)\n    206 if not predicate_fn(exc):\n    207     final_exc, source_exc = exc_factory_fn(\n    208         error_list,\n    209         RetryFailureReason.NON_RETRYABLE_ERROR,\n    210         original_timeout,\n    211     )\n--> 212     raise final_exc from source_exc\n    213 if on_error_fn is not None:\n    214     on_error_fn(exc)\n\nFile /srv/venv38/lib/python3.8/site-packages/google/api_core/retry/retry_unary.py:144, in retry_target(target, predicate, sleep_generator, timeout, on_error, exception_factory, **kwargs)\n    142 for sleep in sleep_generator:\n    143     try:\n--> 144         result = target()\n    145         if inspect.isawaitable(result):\n    146             warnings.warn(_ASYNC_RETRY_WARNING)\n\nFile /srv/venv38/lib/python3.8/site-packages/google/cloud/bigquery/job/query.py:1630, in QueryJob.result.<locals>.is_job_done()\n   1607 if job_failed_exception is not None:\n   1608     # Only try to restart the query job if the job failed for\n   1609     # a retriable reason. For example, don't restart the query\n   (...)\n   1627     # into an exception that can be processed by the\n   1628     # `job_retry` predicate.\n   1629     restart_query_job = True\n-> 1630     raise job_failed_exception\n   1631 else:\n   1632     # Make sure that the _query_results are cached so we\n   1633     # can return a complete RowIterator.\n   (...)\n   1639     # making any extra API calls if the previous loop\n   1640     # iteration fetched the finished job.\n   1641     self._reload_query_results(\n   1642         retry=retry, **reload_query_results_kwargs\n   1643     )\n\nForbidden: 403 Access Denied: Table sc-analytics:report_adsnap.creative_score_training_table_20241222: User does not have permission to query table sc-analytics:report_adsnap.creative_score_training_table_20241222, or perhaps it does not exist.; reason: accessDenied, message: Access Denied: Table sc-analytics:report_adsnap.creative_score_training_table_20241222: User does not have permission to query table sc-analytics:report_adsnap.creative_score_training_table_20241222, or perhaps it does not exist.\n\nLocation: US\nJob ID: 7e70ca16-837f-4e73-b20d-cce231f635f1\n\n"]}], "source": ["print(datetime.datetime.now())\n", "husky_request.execute(wait_for_completion=True)\n", "print(husky_request.report_url)\n", "print(datetime.datetime.now())"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.19"}}, "nbformat": 4, "nbformat_minor": 5}