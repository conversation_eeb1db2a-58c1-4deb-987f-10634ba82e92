{"cells": [{"cell_type": "code", "execution_count": 2, "id": "3285cee1", "metadata": {}, "outputs": [], "source": ["from banjo import utils\n", "from banjo.utils.shibainu import Classification, PromptLibrary, estimate_run_cost, configure_logger\n"]}, {"cell_type": "code", "execution_count": null, "id": "75801b76", "metadata": {}, "outputs": [], "source": ["PROJECT = 'myaigcp'\n", "\n", "SQL = \"\"\"\n", "WITH urls as (SELECT story_snap_id, MAX(IF(unencrypted_flat_video_result_url != '', unencrypted_flat_video_result_url, media_url)) AS media_url\n", "FROM `context-pii.snapjoin.our_story_snap_2025*`\n", "WHERE _TABLE_SUFFIX BETWEEN RIGHT('20250715', 4) AND RIGHT('20250721', 4)\n", "GROUP BY 1)\n", "\n", "\n", "a as (SELECT continent, inclusive_region, event_time, place_id, place_country_code, place_name, place_region, place_country,modified_z_score, coords, story_snap_id, story_link, time_viewed_total_day heatmap_story_views_total_day\n", "FROM `sc-analytics.report_maps.maps_ttp_demand_detection_result_locality_stories_20250720`)\n", "\n", "SELECT  a.*, media_url\n", "FROM urls\n", "LEFT JOIN a USING(story_snap_id)\n", "\n", "\n", "\"\"\"\n"]}, {"cell_type": "code", "execution_count": 18, "id": "f9bb778e", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'media_url'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m~/anaconda3/envs/py310/lib/python3.10/site-packages/pandas/core/indexes/base.py:3802\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[0;34m(self, key, method, tolerance)\u001b[0m\n\u001b[1;32m   3801\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 3802\u001b[0m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_engine\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcasted_key\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   3803\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[0;32m~/anaconda3/envs/py310/lib/python3.10/site-packages/pandas/_libs/index.pyx:138\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32m~/anaconda3/envs/py310/lib/python3.10/site-packages/pandas/_libs/index.pyx:165\u001b[0m, in \u001b[0;36mpandas._libs.index.IndexEngine.get_loc\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32mpandas/_libs/hashtable_class_helper.pxi:5745\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[0;34m()\u001b[0m\n", "File \u001b[0;32mpandas/_libs/hashtable_class_helper.pxi:5753\u001b[0m, in \u001b[0;36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[0;34m()\u001b[0m\n", "\u001b[0;31m<PERSON><PERSON>Error\u001b[0m: 'media_url'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[18], line 5\u001b[0m\n\u001b[1;32m      1\u001b[0m data \u001b[38;5;241m=\u001b[39m utils\u001b[38;5;241m.\u001b[39mgbq\u001b[38;5;241m.\u001b[39mread_gbq(SQL, \n\u001b[1;32m      2\u001b[0m                           project_id\u001b[38;5;241m=\u001b[39mPROJECT,\n\u001b[1;32m      3\u001b[0m                           dialect\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstandard\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m      4\u001b[0m                           priority \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mINTERACTIVE\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m----> 5\u001b[0m dataset \u001b[38;5;241m=\u001b[39m \u001b[43mdata\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mmedia_url\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[38;5;241m.\u001b[39mvalues\n", "File \u001b[0;32m~/anaconda3/envs/py310/lib/python3.10/site-packages/pandas/core/frame.py:3807\u001b[0m, in \u001b[0;36mDataFrame.__getitem__\u001b[0;34m(self, key)\u001b[0m\n\u001b[1;32m   3805\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcolumns\u001b[38;5;241m.\u001b[39mnlevels \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[1;32m   3806\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_getitem_multilevel(key)\n\u001b[0;32m-> 3807\u001b[0m indexer \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   3808\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m is_integer(indexer):\n\u001b[1;32m   3809\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m [indexer]\n", "File \u001b[0;32m~/anaconda3/envs/py310/lib/python3.10/site-packages/pandas/core/indexes/base.py:3804\u001b[0m, in \u001b[0;36mIndex.get_loc\u001b[0;34m(self, key, method, tolerance)\u001b[0m\n\u001b[1;32m   3802\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_engine\u001b[38;5;241m.\u001b[39mget_loc(casted_key)\n\u001b[1;32m   3803\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n\u001b[0;32m-> 3804\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m   3805\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mType<PERSON><PERSON>r\u001b[39;00m:\n\u001b[1;32m   3806\u001b[0m     \u001b[38;5;66;03m# If we have a listlike key, _check_indexing_error will raise\u001b[39;00m\n\u001b[1;32m   3807\u001b[0m     \u001b[38;5;66;03m#  InvalidIndexError. Otherwise we fall through and re-raise\u001b[39;00m\n\u001b[1;32m   3808\u001b[0m     \u001b[38;5;66;03m#  the TypeError.\u001b[39;00m\n\u001b[1;32m   3809\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_check_indexing_error(key)\n", "\u001b[0;31m<PERSON><PERSON>Error\u001b[0m: 'media_url'"]}], "source": ["data = utils.gbq.read_gbq(SQL, \n", "                          project_id=PROJECT,\n", "                          dialect=\"standard\",\n", "                          priority = \"INTERACTIVE\")\n", "dataset = data['media_url'].values"]}, {"cell_type": "code", "execution_count": null, "id": "daabc9c1", "metadata": {}, "outputs": [], "source": ["import requests\n", "import tempfile\n", "import os\n", "from google.cloud import storage\n", "import pandas as pd\n", "from concurrent.futures import ThreadPoolExecutor\n", "from tqdm.notebook import tqdm\n", "\n", "# Initialize GCS client\n", "storage_client = storage.Client()\n", "bucket_name = f\"shiba-inu-temp\"\n", "bucket_folder = f\"maps_events_20250720\"\n", "\n", "# Create bucket if it doesn't exist (usually not needed if bucket already exists)\n", "try:\n", "    bucket = storage_client.get_bucket(bucket_name)\n", "except Exception as e:\n", "    print(f\"Error accessing bucket: {e}\")\n", "    # You might want to create the bucket if it doesn't exist\n", "    # bucket = storage_client.create_bucket(bucket_name)\n", "\n", "# Function to download and upload a single file\n", "def download_and_upload(row):\n", "    url = row['media_url']\n", "    story_id = row['story_snap_id']\n", "    \n", "    try:\n", "        # Download the file\n", "        response = requests.get(url, stream=True, timeout=30)\n", "        response.raise_for_status()\n", "        \n", "        # Determine content type and file extension\n", "        content_type = response.headers.get('content-type', 'application/octet-stream')\n", "        ext = \"\"\n", "        if 'video' in content_type:\n", "            ext = '.mp4'\n", "        elif 'image' in content_type:\n", "            ext = '.jpg' if 'jpeg' in content_type else '.png'\n", "        \n", "        # Create a temporary file to store the download\n", "        with tempfile.NamedTemporaryFile(delete=False) as temp_file:\n", "            for chunk in response.iter_content(chunk_size=8192):\n", "                temp_file.write(chunk)\n", "        \n", "        # Upload to GCS\n", "        blob_name = f\"{bucket_folder}/{story_id}{ext}\"\n", "        blob = bucket.blob(blob_name)\n", "        blob.upload_from_filename(temp_file.name, content_type=content_type)\n", "        \n", "        # Clean up temporary file\n", "        os.unlink(temp_file.name)\n", "        \n", "        # Return the GCS URL\n", "        return f\"gs://{bucket_name}/{blob_name}\"\n", "    \n", "    except Exception as e:\n", "        print(f\"Error processing {url} for story_id {story_id}: {e}\")\n", "        return None\n", "\n", "# Process all URLs with progress bar\n", "print(f\"Processing {len(data)} files...\")\n", "results = []\n", "\n", "# Using ThreadPoolExecutor for parallel processing\n", "with ThreadPoolExecutor(max_workers=10) as executor:\n", "    # Create a list to hold the futures\n", "    futures = [executor.submit(download_and_upload, {'media_url': url, 'story_snap_id': data['story_snap_id'].iloc[i]}) \n", "               for i, url in enumerate(original_links)]\n", "    \n", "    # Process as they complete with a progress bar\n", "    for future in tqdm(futures, total=len(futures)):\n", "        results.append(future.result())\n", "\n", "# Add the GCS URLs to the dataframe\n", "data['gcs_url'] = results\n"]}], "metadata": {"kernelspec": {"display_name": "py310", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}