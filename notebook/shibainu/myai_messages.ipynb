{"cells": [{"cell_type": "code", "execution_count": null, "id": "c320c927-de5b-4c54-afc3-be3ff2c2a689", "metadata": {}, "outputs": [], "source": ["#from banjo.utils.husky_api import HuskyAPIJobRequest, HuskyAPIJobRuntimeError, HUSKY_PROD_ENV, HUSKY_STG_ENV, ContainerResourceConfig\n", "from datetime import datetime, timedelta, timezone\n", "from banjo import utils\n", "from tqdm.contrib.concurrent import thread_map\n", "import pandas as pd\n", "from google.cloud import bigquery\n", "from banjo.utils.shibainu import Classification, estimate_run_cost, configure_logger\n", "import logging\n", "configure_logger(level=logging.ERROR)"]}, {"cell_type": "code", "execution_count": null, "id": "bd63778f", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["DATE = '20250603'\n", "SAMPLE_SIZE = 1000\n", "PROVIDER_NAME = 'gemini'\n", "MODEL_NAME = 'gemini-2.0-flash'\n", "MAX_TOKEN = None\n", "MAX_WORKERS = 25\n", "RETENTION_DAYS = 30\n", "\n", "PROMPT = \"\"\"You are an expert in content tagging and classification are tasked with labeling text with the correct keywords.\n", "This is part of users conversations with a chatbot. You have to extract keywords and classify these keywords into following predefined cartegories.\n", "Output is only keywords and categories for the following text. If the text or keywords are not in English, translate the outputs into English.\n", "\n", "The criteria of correct keywords are:\n", "- Brands and Products mentioned or appeared in the conversation\n", "- Keywords which describe what happening in the conversation\n", "- All extracted keywords have to with 1 or 2 words\n", "- Be as specific as possible, e.g., if the concept is \"latin dance\", provide the full phrase, not \"latin\" and \"dance\"\n", "- Do not include general words like \"hello\", \"thanks\", \"okay\"\n", "- The number of keywords is up to 20\n", "- The list of keywords have to capture the main idea of the conversation\n", "- If you don't have any keywords - reply \"unknown\"\n", "- DO NOT REPEAT\n", "\n", "Output format: \\{\"keywords\": (keyword1, keyword2, keyword3, ...],\n", "    \"categories\": [category1, category2, ...]\\}\n", "\n", "Your goal categorization is to pick from the list bellow which are directly related to one or a few provided keywords from the next message.\n", "Select only categories names from the list, with the same spelling, no other text in output.\n", "DONT CREATE NEW CATEGORIES!\n", "\n", "The list of possible categories:\n", "\"\"\"\n", "\n", "SQL = \"\"\"\n", "SELECT ghost_user_id, hashed_unique_message_id, user_message, assistant_message, user_message_intent, context_messages as sample, locale, country, gender, inferred_age_bucket, with_snapchat_plus, DATE(server_ts) as date, \n", "FROM `sc-bot-privacy.context_analysis.snapbot_messages_replies_completed_20250701`\n", "WHERE\n", "  user_message is not null\n", "  AND LENGTH(user_message) > 4\n", "  and country = \"US\"\n", "ORDER BY RAND()\n", "\"\"\"\n", "\n", "DESTINATION_TABLE = \"sc-bot-privacy.context_analysis.insights_brand_ads_test_20250701\""]}, {"cell_type": "code", "execution_count": null, "id": "5805e27e", "metadata": {}, "outputs": [], "source": ["scc_df = utils.gbq.read_gbq(\"\"\"SELECT * FROM `sc-analytics.cameos_ai.insights_snapchat_content_categories`\"\"\", \n", "                          project_id='myaigcp',\n", "                          dialect=\"standard\",\n", "                          priority = \"INTERACTIVE\")\n", "\n", "categories_list = scc_df[\"category\"].tolist()\n", "categories_set = set(categories_list) \n", "categories_string = \"\\n\".join(categories_set)"]}, {"cell_type": "code", "execution_count": null, "id": "5b4be89e", "metadata": {}, "outputs": [], "source": ["prompt = PROMPT +  categories_string"]}, {"cell_type": "code", "execution_count": null, "id": "e11195af-7607-4b43-ba6d-e841e0cf7716", "metadata": {"tags": []}, "outputs": [], "source": ["sql = SQL + \" LIMIT {sample_size}\".format(sample_size=SAMPLE_SIZE)"]}, {"cell_type": "code", "execution_count": null, "id": "3045d2a4", "metadata": {}, "outputs": [], "source": ["data = utils.gbq.read_gbq(sql, \n", "                          project_id='myaigcp',\n", "                          dialect=\"standard\",\n", "                          priority = \"INTERACTIVE\")\n", "dataset = data['sample'].values"]}, {"cell_type": "code", "execution_count": null, "id": "cb5c379f", "metadata": {}, "outputs": [], "source": ["if PROVIDER_NAME == \"gemini\":\n", "    provider_config = { \"project_id\": \"myaigcp\",\n", "                        \"location\": \"us-central1\"}\n", "else: \n", "    provider_config = {\"service_account\": \"<EMAIL>\"}\n", "\n", "model_parameters = {\"max_tokens\": MAX_TOKEN, 'temperature': 0}\n", "processor_config = None"]}, {"cell_type": "code", "execution_count": null, "id": "01b5c3c3", "metadata": {}, "outputs": [], "source": ["extraction_classification_model = Classification(provider_name = PROVIDER_NAME,\n", "                                     model_name = MODEL_NAME,\n", "                                     prompt = PROMPT,\n", "                                     provider_config=provider_config,\n", "                                     processor_config = processor_config,\n", "                                     model_parameters=model_parameters,\n", "                                     input_type = 'text',\n", "                                     )"]}, {"cell_type": "code", "execution_count": null, "id": "ef980f57", "metadata": {}, "outputs": [], "source": ["r = extraction_classification_model.classify(dataset[0])"]}, {"cell_type": "code", "execution_count": null, "id": "dd5845b8", "metadata": {}, "outputs": [], "source": ["start_time = datetime.now()\n", "results = thread_map(extraction_classification_model.classify, dataset, max_workers=MAX_WORKERS)\n", "print(\"Classification time: \", datetime.now()-start_time)"]}, {"cell_type": "code", "execution_count": null, "id": "c15bc946", "metadata": {}, "outputs": [], "source": ["data['label'] = [extraction_classification_model.get_result(i).lower() for i in results]\n", "data['completion_tokens'] = [extraction_classification_model.get_token_usage(i)['completion_tokens'] for i in results]\n", "data['prompt_tokens'] = [extraction_classification_model.get_token_usage(i)['prompt_tokens'] for i in results]"]}, {"cell_type": "code", "execution_count": null, "id": "ea4d31eb", "metadata": {}, "outputs": [], "source": ["print(estimate_run_cost(data['prompt_tokens'], data['completion_tokens'], MODEL_NAME))"]}, {"cell_type": "code", "execution_count": null, "id": "228b3ab8", "metadata": {}, "outputs": [], "source": ["data['with_snapchat_plus'] = data['with_snapchat_plus'].astype(bool)\n"]}, {"cell_type": "code", "execution_count": null, "id": "3b6cef3f-9e7a-43e4-9251-f836951718ec", "metadata": {"tags": []}, "outputs": [], "source": ["import json\n", "\n", "\n", "def parse_json_from_str(text):\n", "    \"\"\"Removes markdown formatting and loads the JSON string.\"\"\"\n", "    try:\n", "        # Remove the starting '```json\\n' and the ending '\\n```'\n", "        clean_text = text.strip().lstrip('```json\\n').rstrip('\\n```')\n", "        return json.loads(clean_text)\n", "    except (<PERSON>tri<PERSON><PERSON><PERSON><PERSON><PERSON>, json.JSONDecodeError):\n", "        # Return a default dict if the input is not a string or is bad JSON\n", "        return {'keywords': [], 'categories': []}\n", "\n", "# 3. Apply the function and create new columns\n", "# First, create a temporary column with the parsed dictionaries\n", "temp_col = data['label'].apply(parse_json_from_str)"]}, {"cell_type": "code", "execution_count": null, "id": "197c6f83", "metadata": {}, "outputs": [], "source": ["keywords = []\n", "categories = []\n", "\n", "for i in temp_col:\n", "    try:\n", "        keywords.append(i.get('keywords', []))\n", "        categories.append(i.get('categories', []))\n", "    except:\n", "        keywords.append(['unknown'])\n", "        categories.append(['unknown'])\n", "\n", "data['keywords'] = keywords\n", "data['categories'] = categories"]}, {"cell_type": "code", "execution_count": null, "id": "a3cca7d3", "metadata": {}, "outputs": [], "source": ["categories_set = {category.lower() for category in categories_list}\n", "\n", "def filter_categories_from_list(category_list):\n", "    \"\"\"\n", "    Filters a list of category strings against a predefined set.\n", "    \"\"\"\n", "    if not isinstance(category_list, list):\n", "        return '' # Return empty string if data is not a list\n", "\n", "    # Filter the list directly and join the result into a string\n", "    filtered = [cat for cat in category_list if cat.lower() in categories_set]\n", "    return ', '.join(filtered)\n", "\n", "\n", "# Apply the function to the label_cls column\n", "data['label_cls_filtered'] = data['categories'].apply(filter_categories_from_list)"]}, {"cell_type": "code", "execution_count": null, "id": "5941ff81", "metadata": {}, "outputs": [], "source": ["data['model_name'] = MODEL_NAME\n", "data['sample_size'] = SAMPLE_SIZE"]}, {"cell_type": "code", "execution_count": null, "id": "d20d6499-107a-4865-9339-75979fc8362a", "metadata": {"tags": []}, "outputs": [], "source": ["PROJECT ='myaigcp'\n", "\n", "DESTINATION_PROJECT, DESTINATION_DATASET, DESTINATION_TABLE_NAME = DESTINATION_TABLE.split('.')"]}, {"cell_type": "code", "execution_count": null, "id": "05704e73", "metadata": {}, "outputs": [], "source": ["## Upload results\n", "utils.gbq.to_gbq(\n", "                 data,\n", "                 project_id = PROJECT,\n", "                 dest_table_name = DESTINATION_TABLE_NAME,\n", "                 dest_dataset_id = DESTINATION_DATASET,\n", "                 dest_project_id = DESTINATION_PROJECT)\n", "\n", "print(f\"Results saved to {DESTINATION_PROJECT}.{DESTINATION_DATASET}.{DESTINATION_TABLE_NAME} table\")"]}, {"cell_type": "code", "execution_count": null, "id": "88442844", "metadata": {}, "outputs": [], "source": ["if RETENTION_DAYS:\n", "    client = bigquery.Client(project=DESTINATION_PROJECT)\n", "    table = client.get_table(f\"{DESTINATION_PROJECT}.{DESTINATION_DATASET}.{DESTINATION_TABLE_NAME}\")  \n", "    # Set the expiration time (e.g., 7 days from now)\n", "    \n", "    expiration_time = datetime.now(timezone.utc) + timedelta(days=RETENTION_DAYS)\n", "    table.expires = expiration_time\n", "    \n", "    # Update the table with new expiration time\n", "    client.update_table(table, [\"expires\"])  # Make an API request.\n", "    \n", "    print(f\"Table {table} expiration set to {expiration_time}\")"]}], "metadata": {"environment": {"kernel": "python3", "name": "tf2-cpu.2-11.m125", "type": "gcloud", "uri": "us-docker.pkg.dev/deeplearning-platform-release/gcr.io/tf2-cpu.2-11:m125"}, "kernelspec": {"display_name": "py310", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}