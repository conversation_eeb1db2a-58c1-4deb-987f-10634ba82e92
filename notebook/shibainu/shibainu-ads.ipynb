{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1bc64d20-4c5e-43c5-a833-06895a5e2ed9", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["from datetime import date, datetime, timedelta, timezone\n", "\n", "from banjo.utils.shibainu import Classification, Validation, Visualization, estimate_run_cost, configure_logger, PromptLibrary\n", "import logging\n", "configure_logger(level=logging.ERROR)\n", "\n", "import numpy as np\n", "import pandas as pd\n", "from tqdm.contrib.concurrent import thread_map\n", "from google.cloud import bigquery\n", "\n", "import lca\n", "from banjo import utils\n", "\n", "pd.set_option('display.max_colwidth', None)\n"]}, {"cell_type": "code", "execution_count": 2, "id": "f7abb262-df7d-48c7-bbcc-bb99e6788b60", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["parameters"]}, "outputs": [], "source": ["## Constant\n", "\n", "### Init parameters\n", "SERVICE_ACCOUNT = \"<EMAIL>\"\n", "PROJECT = 'myaigcp' \n", "PRIORITY = 'INTERACTIVE' #Should be \"BATCH' in prod\n", "TEMPERATURE = 0\n", "MAX_TOKEN = 1280\n", "MAX_WORKER = 5\n", "RESULT_VISUALIZATION_SAMPLE = 10\n", "RESULT_CATEGORIES_SIZE_THRESHOLD = 5\n", "\n", "PROVIDER_NAME = 'openai'\n", "MODEL_NAME = \"gpt-4o-mini\"\n", "#MODEL_NAME = \"o1-preview\"\n", "PASS = \"https://pass.mesh.sc-corp.net/privacyReviews/pass?id=EXAMPLE\"\n", "\n", "#DATA_TYPE = \"video\" #Values: ['text', 'image','video']\n", "DATA_TYPE = 'video'\n", "#DATA_SOURCE = \"sql\" #Values: ['table', 'sql']. Add parameters required for image processing\n", "\n", "\n", "### Data parameters\n", "#TABLE_NAME = \"sc-bq-gcs-billingonly.dishchenko.wiki_characters\" #if user picked \"table\" data source \n", "# CUSTOM_SQL = \"SELECT hashtag FROM sc-targeting-measurement.topic_engagement.hashtag_metadata\" #if user picked custom_sql \"sql\" data source\n", "SQL =  \"\"\"\n", "SELECT distinct creative_id, creative_headline, media_download_link,\n", "FROM `sc-product-datascience.cyang.snap_promote_AI_classifier`\n", "WHERE creative_headline IS NOT NULL\n", "AND media_download_link != 'unknown'\n", "ORDER BY RAND()\n", "LIMIT 100\n", "\"\"\"\n", "COLUMN_NAME = 'media_download_link' \n", "\n", "TAXONOMY = 'SCC'\n", "#GCC - 1030 items - Google content categories https://cloud.google.com/natural-language/docs/categories\n", "#SCC - 383 items - Snapchat content categoeries https://wiki.sc-corp.net/display/PT/Snapchat+Content+Category+%28SCC%29+101\n", "#S2I - 22,185 items - Snap 2 interest tags https://docs.google.com/document/d/1PZZ3zFSeR4_DPZGe7rqv3iWMwDYTKPYAWgocbpGaFDQ/edit?tab=t.0#heading=h.684wtn93gyyl\n", "#LTC - 373 items- Lens topic clusters https://docs.google.com/document/d/1ZQM5_XSu_6tzacTPZSwePEdianAEZVTs2J70N66YluI/edit?tab=t.0#heading=h.c3lu4nluiojx\n", "\n", "\n", "### Output parameters\n", "DESTINATION_TABLE = 'xfun_text_ai_20241227'\n", "DESTINATION_DATASET = 'dishchenko'\n", "DESTINATION_PROJECT = 'sc-bq-gcs-billingonly'\n", "RETENTION_DAYS = 30\n", "\n", "\n", "##Processing parameters\n", "VIDEO_SAMPLING_MODE = 'fps'\n", "VIDEO_SAMPLING_VALUE = 1.0\n", "VIDEO_MAX_FRAMES = 20\n", "DOWNLOAD_VIDEO = True\n", "PROCESSING_MODE = None"]}, {"cell_type": "code", "execution_count": 3, "id": "166adfae-60d4-4b49-89dd-3bbba916e4a4", "metadata": {}, "outputs": [], "source": ["if not PROCESSING_MODE:\n", "    if DATA_TYPE == 'image':\n", "        PROCESSING_MODE = 'image_url'\n", "    elif <PERSON>_TYPE == 'video' or DATA_TYPE == 'mixed':\n", "        PROCESSING_MODE = 'bytes'\n", "\n", "processor_config = {\n", "    'sampling_mode': VIDEO_SAMPLING_MODE,\n", "    'sampling_value': VIDEO_SAMPLING_VALUE,\n", "    'max_frames': VIDEO_MAX_FRAMES,\n", "    'download': DOWNLOAD_VIDEO,\n", "    'processing_mode': PROCESSING_MODE\n", "}"]}, {"cell_type": "code", "execution_count": 4, "id": "63ae1c97-6fc3-4e30-b0a8-bddca72e024c", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["### Parameters processing"]}, {"cell_type": "code", "execution_count": 5, "id": "228e14a7-275d-46df-912d-6360e49f9d37", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["# handle the checkbox\n", "def handle_husky_checkbox(parameter, checked_value, unchecked_value):\n", "    \"\"\"husky always returns the checkbox values as strings\"\"\"\n", "    if isinstance(parameter, str):\n", "        return checked_value\n", "    elif parameter in [checked_value, unchecked_value]:\n", "        return parameter\n", "    else:\n", "        return unchecked_value\n"]}, {"cell_type": "code", "execution_count": 6, "id": "86055ba3-8210-4117-8e4e-d03d184f404b", "metadata": {}, "outputs": [], "source": ["class PrivacyException(Exception):\n", "    def __init__(self, msg=\"\"\"\n", "        Please, attach the link to approved PASS. \n", "        Note: It is your responsibility to ensure that it is filled out correctly and approved.\"\"\", *args, **kwargs):\n", "        super().__init__(msg, *args, **kwargs)\n", "    pass\n", "\n", "#check if PASS is attached\n", "if (\"https://pass.mesh.sc-corp.net/privacyReviews/pass\" in PASS):\n", "    pass\n", "else:\n", "    raise PrivacyException\n", "    "]}, {"cell_type": "markdown", "id": "92f92fbd-81e3-4c0a-8c7c-d4d31b9cd20b", "metadata": {}, "source": ["# Prompt generation"]}, {"cell_type": "code", "execution_count": 13, "id": "108259ee-3f69-4107-8355-f40b7e5989ea", "metadata": {}, "outputs": [], "source": ["PROMPT = PromptLibrary().get_taxonomy_prompt('SCC', project_id='myaigcp', priority=\"INTERACTIVE\")"]}, {"cell_type": "markdown", "id": "9e58cd8e-3d6e-4b5b-8c92-601af2aa9cbc", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["## Test"]}, {"cell_type": "code", "execution_count": 17, "id": "927d72cf-16b3-4278-ac56-a2c440a6fb02", "metadata": {}, "outputs": [], "source": ["classification_model = Classification(provider_name = PROVIDER_NAME,\n", "                                     model_name = MODEL_NAME,\n", "                                     prompt = PROMPT,\n", "                                     provider_config={\n", "                                        \"service_account\": \"<EMAIL>\"},\n", "                                     processor_config = processor_config,\n", "                                     model_parameters={'temperature': TEMPERATURE, 'max_token': MAX_TOKEN},\n", "                                     input_type = DATA_TYPE,\n", "                                     )"]}, {"cell_type": "code", "execution_count": 18, "id": "487ba01c-29c9-41b6-8aba-bc940fad2d89", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The model gpt-4o-mini is alive.\n"]}], "source": ["result = classification_model.send_message(\"Say: 'The model {model_name} is alive' \". format(model_name = MODEL_NAME))\n", "print(classification_model.get_result(result))"]}, {"cell_type": "code", "execution_count": 19, "id": "6fc51192-b7fc-4b41-b213-3d31ec5884f1", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"ename": "Forbidden", "evalue": "403 Access Denied: Table sc-product-datascience:cyang.snap_promote_AI_classifier: User does not have permission to query table sc-product-datascience:cyang.snap_promote_AI_classifier, or perhaps it does not exist.; reason: accessDenied, message: Access Denied: Table sc-product-datascience:cyang.snap_promote_AI_classifier: User does not have permission to query table sc-product-datascience:cyang.snap_promote_AI_classifier, or perhaps it does not exist.\n\nLocation: US\nJob ID: f380b928-ae25-4da9-b6b5-558ba07d2baa\n", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON>orbidden\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[19], line 3\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m## Download data\u001b[39;00m\n\u001b[0;32m----> 3\u001b[0m data \u001b[38;5;241m=\u001b[39m \u001b[43mutils\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgbq\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread_gbq\u001b[49m\u001b[43m(\u001b[49m\u001b[43mSQL\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[1;32m      4\u001b[0m \u001b[43m                          \u001b[49m\u001b[43mproject_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mPROJECT\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m      5\u001b[0m \u001b[43m                          \u001b[49m\u001b[43mdialect\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mstandard\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m      6\u001b[0m \u001b[43m                          \u001b[49m\u001b[43mpriority\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43m \u001b[49m\u001b[43mPRIORITY\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m      7\u001b[0m dataset \u001b[38;5;241m=\u001b[39m data[COLUMN_NAME]\u001b[38;5;241m.\u001b[39mvalues\n", "File \u001b[0;32m~/anaconda3/envs/py38/lib/python3.8/site-packages/banjo/utils/gbq.py:653\u001b[0m, in \u001b[0;36mread_gbq\u001b[0;34m(query, project_id, client, index_col, col_order, dialect, location, priority, maximum_billing_tier, job_config, use_bqstorage_api, bq_retry)\u001b[0m\n\u001b[1;32m    601\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mread_gbq\u001b[39m(query, project_id\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, client\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, index_col\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, col_order\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m    602\u001b[0m              dialect\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mlegacy\u001b[39m\u001b[38;5;124m'\u001b[39m, location\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mN<PERSON>\u001b[39;00m, priority\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mBATCH\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m    603\u001b[0m              maximum_billing_tier\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, job_config\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m    604\u001b[0m              use_bqstorage_api\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[1;32m    605\u001b[0m              bq_retry\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m    606\u001b[0m              ):\n\u001b[1;32m    607\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Read query results as pandas.DataFrame using google.cloud.bigquery\u001b[39;00m\n\u001b[1;32m    608\u001b[0m \n\u001b[1;32m    609\u001b[0m \u001b[38;5;124;03m    Parameters\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    650\u001b[0m \u001b[38;5;124;03m    0  164656\u001b[39;00m\n\u001b[1;32m    651\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 653\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mbatch_read_gbq\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    654\u001b[0m \u001b[43m        \u001b[49m\u001b[43m[\u001b[49m\u001b[43mquery\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    655\u001b[0m \u001b[43m        \u001b[49m\u001b[43mproject_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mproject_id\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mclient\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mclient\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mindex_col\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mindex_col\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcol_order\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcol_order\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    656\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdialect\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdialect\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlocation\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlocation\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpriority\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mpriority\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    657\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmaximum_billing_tier\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmaximum_billing_tier\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mjob_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mjob_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    658\u001b[0m \u001b[43m        \u001b[49m\u001b[43muse_bqstorage_api\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43muse_bqstorage_api\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    659\u001b[0m \u001b[43m        \u001b[49m\u001b[43mbq_retry\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbq_retry\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    660\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m[\u001b[38;5;241m0\u001b[39m]\n", "File \u001b[0;32m~/anaconda3/envs/py38/lib/python3.8/site-packages/banjo/utils/gbq.py:782\u001b[0m, in \u001b[0;36mbatch_read_gbq\u001b[0;34m(queries, project_id, client, index_col, col_order, dialect, location, priority, maximum_billing_tier, job_config, use_bqstorage_api, bq_retry, parallel, parallel_concurrency)\u001b[0m\n\u001b[1;32m    780\u001b[0m             \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[1;32m    781\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 782\u001b[0m     final_dfs \u001b[38;5;241m=\u001b[39m [_fetch_data(_submit_query_job(query, job_dialect)) \u001b[38;5;28;01mfor\u001b[39;00m query, job_dialect \u001b[38;5;129;01min\u001b[39;00m queries_and_dialects]\n\u001b[1;32m    784\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m [_reindex_df(df, index_col\u001b[38;5;241m=\u001b[39mindex_col, col_order\u001b[38;5;241m=\u001b[39mcol_order) \u001b[38;5;28;01mfor\u001b[39;00m df \u001b[38;5;129;01min\u001b[39;00m final_dfs]\n", "File \u001b[0;32m~/anaconda3/envs/py38/lib/python3.8/site-packages/banjo/utils/gbq.py:782\u001b[0m, in \u001b[0;36m<listcomp>\u001b[0;34m(.0)\u001b[0m\n\u001b[1;32m    780\u001b[0m             \u001b[38;5;28;01mraise\u001b[39;00m e\n\u001b[1;32m    781\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 782\u001b[0m     final_dfs \u001b[38;5;241m=\u001b[39m [\u001b[43m_fetch_data\u001b[49m\u001b[43m(\u001b[49m\u001b[43m_submit_query_job\u001b[49m\u001b[43m(\u001b[49m\u001b[43mquery\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mjob_dialect\u001b[49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m query, job_dialect \u001b[38;5;129;01min\u001b[39;00m queries_and_dialects]\n\u001b[1;32m    784\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m [_reindex_df(df, index_col\u001b[38;5;241m=\u001b[39mindex_col, col_order\u001b[38;5;241m=\u001b[39mcol_order) \u001b[38;5;28;01mfor\u001b[39;00m df \u001b[38;5;129;01min\u001b[39;00m final_dfs]\n", "File \u001b[0;32m~/anaconda3/envs/py38/lib/python3.8/site-packages/google/api_core/retry.py:372\u001b[0m, in \u001b[0;36mRetry.__call__.<locals>.retry_wrapped_func\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    368\u001b[0m target \u001b[38;5;241m=\u001b[39m functools\u001b[38;5;241m.\u001b[39mpartial(func, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m    369\u001b[0m sleep_generator \u001b[38;5;241m=\u001b[39m exponential_sleep_generator(\n\u001b[1;32m    370\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_initial, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_maximum, multiplier\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_multiplier\n\u001b[1;32m    371\u001b[0m )\n\u001b[0;32m--> 372\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mretry_target\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    373\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtarget\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    374\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_predicate\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    375\u001b[0m \u001b[43m    \u001b[49m\u001b[43msleep_generator\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    376\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_timeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    377\u001b[0m \u001b[43m    \u001b[49m\u001b[43mon_error\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mon_error\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    378\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/anaconda3/envs/py38/lib/python3.8/site-packages/google/api_core/retry.py:207\u001b[0m, in \u001b[0;36mretry_target\u001b[0;34m(target, predicate, sleep_generator, timeout, on_error, **kwargs)\u001b[0m\n\u001b[1;32m    205\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m sleep \u001b[38;5;129;01min\u001b[39;00m sleep_generator:\n\u001b[1;32m    206\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 207\u001b[0m         result \u001b[38;5;241m=\u001b[39m \u001b[43mtarget\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    208\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m inspect\u001b[38;5;241m.\u001b[39misawaitable(result):\n\u001b[1;32m    209\u001b[0m             warnings\u001b[38;5;241m.\u001b[39mwarn(_ASYNC_RETRY_WARNING)\n", "File \u001b[0;32m~/anaconda3/envs/py38/lib/python3.8/site-packages/banjo/utils/gbq.py:756\u001b[0m, in \u001b[0;36mbatch_read_gbq.<locals>._fetch_data\u001b[0;34m(job)\u001b[0m\n\u001b[1;32m    753\u001b[0m \u001b[38;5;129m@bq_retry\u001b[39m\n\u001b[1;32m    754\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_fetch_data\u001b[39m(job):\n\u001b[1;32m    755\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 756\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mjob\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mresult\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39mto_arrow(bqstorage_client\u001b[38;5;241m=\u001b[39mbqstorage_client)\u001b[38;5;241m.\u001b[39mto_pandas()\n\u001b[1;32m    757\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m (NotFound, BadRequest) \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m    758\u001b[0m         raise_with_traceback(BigQueryRuntimeError(\n\u001b[1;32m    759\u001b[0m             \u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{0}\u001b[39;00m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124mQuery submitted:\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{1}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;241m.\u001b[39mformat(e, sqlparse\u001b[38;5;241m.\u001b[39mformat(job\u001b[38;5;241m.\u001b[39mquery, reindent\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m))\n\u001b[1;32m    760\u001b[0m         ))\n", "File \u001b[0;32m~/anaconda3/envs/py38/lib/python3.8/site-packages/google/cloud/bigquery/job/query.py:1681\u001b[0m, in \u001b[0;36mQueryJob.result\u001b[0;34m(self, page_size, max_results, retry, timeout, start_index, job_retry)\u001b[0m\n\u001b[1;32m   1676\u001b[0m     remaining_timeout \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m   1678\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m remaining_timeout \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m   1679\u001b[0m     \u001b[38;5;66;03m# Since is_job_done() calls jobs.getQueryResults, which is a\u001b[39;00m\n\u001b[1;32m   1680\u001b[0m     \u001b[38;5;66;03m# long-running API, don't delay the next request at all.\u001b[39;00m\n\u001b[0;32m-> 1681\u001b[0m     \u001b[38;5;28;01mwhile\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[43mis_job_done\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m:\n\u001b[1;32m   1682\u001b[0m         \u001b[38;5;28;01mpass\u001b[39;00m\n\u001b[1;32m   1683\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1684\u001b[0m     \u001b[38;5;66;03m# Use a monotonic clock since we don't actually care about\u001b[39;00m\n\u001b[1;32m   1685\u001b[0m     \u001b[38;5;66;03m# daylight savings or similar, just the elapsed time.\u001b[39;00m\n", "File \u001b[0;32m~/anaconda3/envs/py38/lib/python3.8/site-packages/google/api_core/retry.py:372\u001b[0m, in \u001b[0;36mRetry.__call__.<locals>.retry_wrapped_func\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    368\u001b[0m target \u001b[38;5;241m=\u001b[39m functools\u001b[38;5;241m.\u001b[39mpartial(func, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[1;32m    369\u001b[0m sleep_generator \u001b[38;5;241m=\u001b[39m exponential_sleep_generator(\n\u001b[1;32m    370\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_initial, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_maximum, multiplier\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_multiplier\n\u001b[1;32m    371\u001b[0m )\n\u001b[0;32m--> 372\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mretry_target\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    373\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtarget\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    374\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_predicate\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    375\u001b[0m \u001b[43m    \u001b[49m\u001b[43msleep_generator\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    376\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_timeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    377\u001b[0m \u001b[43m    \u001b[49m\u001b[43mon_error\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mon_error\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    378\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/anaconda3/envs/py38/lib/python3.8/site-packages/google/api_core/retry.py:207\u001b[0m, in \u001b[0;36mretry_target\u001b[0;34m(target, predicate, sleep_generator, timeout, on_error, **kwargs)\u001b[0m\n\u001b[1;32m    205\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m sleep \u001b[38;5;129;01min\u001b[39;00m sleep_generator:\n\u001b[1;32m    206\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 207\u001b[0m         result \u001b[38;5;241m=\u001b[39m \u001b[43mtarget\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    208\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m inspect\u001b[38;5;241m.\u001b[39misawaitable(result):\n\u001b[1;32m    209\u001b[0m             warnings\u001b[38;5;241m.\u001b[39mwarn(_ASYNC_RETRY_WARNING)\n", "File \u001b[0;32m~/anaconda3/envs/py38/lib/python3.8/site-packages/google/cloud/bigquery/job/query.py:1630\u001b[0m, in \u001b[0;36mQueryJob.result.<locals>.is_job_done\u001b[0;34m()\u001b[0m\n\u001b[1;32m   1607\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m job_failed_exception \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m   1608\u001b[0m     \u001b[38;5;66;03m# Only try to restart the query job if the job failed for\u001b[39;00m\n\u001b[1;32m   1609\u001b[0m     \u001b[38;5;66;03m# a retriable reason. For example, don't restart the query\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1627\u001b[0m     \u001b[38;5;66;03m# into an exception that can be processed by the\u001b[39;00m\n\u001b[1;32m   1628\u001b[0m     \u001b[38;5;66;03m# `job_retry` predicate.\u001b[39;00m\n\u001b[1;32m   1629\u001b[0m     restart_query_job \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[0;32m-> 1630\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m job_failed_exception\n\u001b[1;32m   1631\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m   1632\u001b[0m     \u001b[38;5;66;03m# Make sure that the _query_results are cached so we\u001b[39;00m\n\u001b[1;32m   1633\u001b[0m     \u001b[38;5;66;03m# can return a complete RowIterator.\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1639\u001b[0m     \u001b[38;5;66;03m# making any extra API calls if the previous loop\u001b[39;00m\n\u001b[1;32m   1640\u001b[0m     \u001b[38;5;66;03m# iteration fetched the finished job.\u001b[39;00m\n\u001b[1;32m   1641\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_reload_query_results(\n\u001b[1;32m   1642\u001b[0m         retry\u001b[38;5;241m=\u001b[39mretry, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mreload_query_results_kwargs\n\u001b[1;32m   1643\u001b[0m     )\n", "\u001b[0;31mForbidden\u001b[0m: 403 Access Denied: Table sc-product-datascience:cyang.snap_promote_AI_classifier: User does not have permission to query table sc-product-datascience:cyang.snap_promote_AI_classifier, or perhaps it does not exist.; reason: accessDenied, message: Access Denied: Table sc-product-datascience:cyang.snap_promote_AI_classifier: User does not have permission to query table sc-product-datascience:cyang.snap_promote_AI_classifier, or perhaps it does not exist.\n\nLocation: US\nJob ID: f380b928-ae25-4da9-b6b5-558ba07d2baa\n"]}], "source": ["## Download data\n", "\n", "data = utils.gbq.read_gbq(SQL, \n", "                          project_id=PROJECT,\n", "                          dialect=\"standard\",\n", "                          priority = PRIORITY)\n", "dataset = data[COLUMN_NAME].values"]}, {"cell_type": "code", "execution_count": null, "id": "c710c1ad-b467-48b2-9588-31f4587d8772", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "638ec566-176a-48f0-bba7-b65bb35b73eb", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["## Classifcation processing"]}, {"cell_type": "code", "execution_count": null, "id": "52dfc928-9dda-40cf-890f-3c78a8f4eb9c", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["start_time = datetime.now()\n", "results = thread_map(classification_model.classify, dataset, max_workers=MAX_WORKER)\n", "print(\"Classification time: \", datetime.now()-start_time)"]}, {"cell_type": "code", "execution_count": null, "id": "f7f97177-b964-472e-9606-3dd18755f8d4", "metadata": {}, "outputs": [], "source": ["data['label'] = [i[0].lower() for i in results]\n", "data['completion_tokens'] = [i[1] for i in results]\n", "data['prompt_tokens'] = [i[2] for i in results]"]}, {"cell_type": "markdown", "id": "fd18ae2a-afad-4ff7-9ffb-10875519696a", "metadata": {}, "source": ["## Cost estimation"]}, {"cell_type": "code", "execution_count": null, "id": "ba28f6e5-5e52-4d89-bb64-ee4d0edde63b", "metadata": {}, "outputs": [], "source": ["est_cost = estimate_run_cost(data['completion_tokens'], data[\"prompt_tokens\"], MODEL_NAME)\n", "\n", "print(f\"Estimated cost for prediction is {est_cost} $\")"]}, {"cell_type": "code", "execution_count": null, "id": "579234f1-6770-4883-a5c6-63188b9e2706", "metadata": {}, "outputs": [], "source": ["data.to_csv('SCC_100.csv')"]}, {"cell_type": "code", "execution_count": null, "id": "bd23aa31-13bb-4f93-8f35-6395f2d7705a", "metadata": {}, "outputs": [], "source": ["data"]}, {"cell_type": "code", "execution_count": null, "id": "d0e9af8a-21ce-4e24-99ba-9c790cc47dc7", "metadata": {}, "outputs": [], "source": ["t = []\n", "for index, row in data.iterrows():\n", "    labels = row['label']\n", "    if labels != 'n/a':\n", "        for label in labels[1:-1].split(','):\n", "            t.append({'label': label, 'creative_id': row['creative_id']})\n", "    else:\n", "        t.append({'label': 'n/a', 'creative_id': row['creative_id']})"]}, {"cell_type": "code", "execution_count": null, "id": "706555b1-9358-4f8a-ac44-0cbf4adbdebd", "metadata": {}, "outputs": [], "source": ["t_df = pd.DataFrame(t)"]}, {"cell_type": "code", "execution_count": null, "id": "77ec42fa-3ee4-4fbb-b2e5-6df59b3f28ce", "metadata": {}, "outputs": [], "source": ["t_df.groupby('label').count().reset_index().sort_values(by='creative_id', ascending=False)"]}, {"cell_type": "code", "execution_count": null, "id": "bee88025-c7b1-4cf0-948d-c9d861efeedf", "metadata": {}, "outputs": [], "source": ["data"]}, {"cell_type": "code", "execution_count": null, "id": "48e151a5-76c9-4ab5-8112-0fee1b8fdc93", "metadata": {}, "outputs": [], "source": ["t = []\n", "for index, row in data.iterrows():\n", "    labels = row['label']\n", "    if labels != 'n/a':\n", "        for label in labels[1:-1].split(','):\n", "            t.append({'label': label, 'creative_id': row['creative_id']})\n", "    else:\n", "        t.append({'label': 'n/a', 'creative_id': row['creative_id']})"]}, {"cell_type": "code", "execution_count": null, "id": "be04a6f5-b4e7-477c-88ab-960c6af63686", "metadata": {}, "outputs": [], "source": ["t_df = pd.DataFrame(t)"]}, {"cell_type": "code", "execution_count": null, "id": "880ee77c-992c-4ff4-9d55-6ef9a83b587c", "metadata": {}, "outputs": [], "source": ["t_df.groupby('label').count().reset_index().sort_values(by='creative_id', ascending=False)"]}, {"cell_type": "markdown", "id": "29f5012e-28c8-4785-b1c4-3f6eb6bbd7c2", "metadata": {}, "source": ["## Save Results"]}, {"cell_type": "code", "execution_count": null, "id": "5be32142-0b1d-4b50-ab55-395c4b1199f6", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["## Upload results\n", "utils.gbq.to_gbq(\n", "                 data,\n", "                 project_id = PROJECT,\n", "                 dest_table_name = DESTINATION_TABLE,\n", "                 dest_dataset_id = DESTINATION_DATASET,\n", "                 dest_project_id = DESTINATION_PROJECT)\n", "\n", "print(f\"Results saved to {DESTINATION_PROJECT}.{DESTINATION_DATASET}.{DESTINATION_TABLE} table\")"]}, {"cell_type": "code", "execution_count": null, "id": "ce41e6c6-d758-4276-9af2-75617ef85925", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["if RETENTION_DAYS:\n", "    client = bigquery.Client(project=DESTINATION_PROJECT)\n", "    table = client.get_table(f\"{DESTINATION_PROJECT}.{DESTINATION_DATASET}.{DESTINATION_TABLE}\")  \n", "    # Set the expiration time (e.g., 7 days from now)\n", "    \n", "    expiration_time = datetime.now(timezone.utc) + timedelta(days=RETENTION_DAYS)\n", "    table.expires = expiration_time\n", "    \n", "    # Update the table with new expiration time\n", "    client.update_table(table, [\"expires\"])  # Make an API request.\n", "    \n", "    print(f\"Table {table} expiration set to {expiration_time}\")"]}, {"cell_type": "code", "execution_count": null, "id": "75bca6b6-ec36-429d-958f-75a1a87c70ba", "metadata": {}, "outputs": [], "source": ["## Upload results\n", "utils.gbq.to_gbq(\n", "                 metadata,\n", "                 project_id = PROJECT,\n", "                 dest_table_name = \"shiba_inu_logs\",\n", "                 dest_dataset_id = \"cameos_ai\",\n", "                 dest_project_id = \"sc-analytics\",\n", "                 write_disposition = 'WRITE_APPEND')\n", "\n", "print(\"Logs saved to sc-analytics.cameos_ai.shiba_inu_logs table\")"]}, {"cell_type": "markdown", "id": "1532bdd6", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["## Result Visualization"]}, {"cell_type": "code", "execution_count": null, "id": "9baac27c", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["# import packages and settings\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import matplotlib\n", "from IPython.display import display, HTML\n", "\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "sns.set_palette(palette='Set2')\n", "sns.set_theme(style=\"white\")\n", "font = {'family' : 'DejaVu Sans',\n", "       'weight' : 'normal',\n", "       'size'   : 12}\n", "matplotlib.rc('font', **font)\n", "\n", "# parameters\n", "# PROJECT_ALIAS = 'Ad Creative Classification'\n", "# RESULT_VISUALIZATION_SAMPLE = 5"]}, {"cell_type": "code", "execution_count": null, "id": "fd28babd", "metadata": {"editable": true, "scrolled": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["# ################## SECTION. RESULT VISUALIZATION #######################\n", "\n", "# instantiate the visualization class\n", "DATA_TYPE = \"video\"\n", "visualization = Visualization(data = data, data_type=DATA_TYPE)\n", "print(\"==== Result Visualization ====\")\n", "\n", "# figure1. plot the distribution of classification labels\n", "visualization.plot_label_counts(figsize=(10,5), column_name='label', title=\"Displaying distribution of classification labels\", threshold=RESULT_CATEGORIES_SIZE_THRESHOLD)\n", "print(\"\\n\")\n", "\n", "# figure2. plot the sample of the data\n", "visualization.display_samples(column_name=COLUMN_NAME, n_sample=RESULT_VISUALIZATION_SAMPLE)\n", "print(\"\\n\")\n", "\n", "# ################## END OF RESULT VISUALIZATION #######################"]}, {"cell_type": "code", "execution_count": null, "id": "4acd727f-b694-4593-9211-25014bf115f4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "15c8d8d9-3b09-4ae6-8b1f-d3cb2223f840", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ec8d645e-5945-40f6-91bd-0ba5db9dbe82", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py38", "language": "python", "name": "py38"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.19"}}, "nbformat": 4, "nbformat_minor": 5}