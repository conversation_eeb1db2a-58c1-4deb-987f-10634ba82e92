{"cells": [{"cell_type": "code", "execution_count": null, "id": "b5d6f99f", "metadata": {}, "outputs": [], "source": ["from datetime import date, datetime, timedelta, timezone\n", "from banjo import utils\n", "from banjo.utils.shibainu import Classification, PromptLibrary, estimate_run_cost, configure_logger\n", "import logging\n", "configure_logger(level=logging.ERROR)\n", "\n", "import numpy as np\n", "import pandas as pd\n", "from tqdm.contrib.concurrent import thread_map\n", "\n", "# # Google Cloud and authentication libraries\n", "from google.cloud import bigquery\n", "\n", "\n", "# Local application/library-specific imports\n", "import lca\n", "\n", "pd.set_option('display.max_colwidth', None)"]}, {"cell_type": "code", "execution_count": null, "id": "3136f465", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["#Model parameters\n", "\n", "\n", "PROVIDER_NAME = 'gemini'\n", "MODEL_NAME = \"gemini-2.0-flash\"\n", "INPUT_TYPE = 'text'\n", "\n", "MAX_WORKER = 5\n", "\n", "PASS = \"https://pass.mesh.sc-corp.net/privacyReviews/pass?id=EXAMPLE\"\n", "#PASS = None\n", "\n", "# Source\n", "SQL = \"\"\"\n", "    WITH urls as (SELECT story_snap_id, MAX(IF(unencrypted_flat_video_result_url != '', unencrypted_flat_video_result_url, media_url)) AS media_url\n", "FROM `context-pii.snapjoin.our_story_snap_2025*`\n", "WHERE _TABLE_SUFFIX BETWEEN RIGHT(FORMAT_DATE('%Y%m%d', DATE_SUB(PARSE_DATE('%Y%m%d','20250701'), INTERVAL 7 DAY)), 4) AND RIGHT('20250701', 4)\n", "GROUP BY 1)\n", "\n", "SELECT story_snap_id as sample_id, media_url as sample, IF(media_type = 'IMAGE', 'image', 'video') as media_type, count(*) as views\n", "FROM `sc-analytics.report_maps.daily_maps_story_events_20250701`\n", "LEFT JOIN urls USING (story_snap_id)\n", "WHERE \n", "    event_name = 'STORY_SNAP_VIEW'\n", "    AND map_story_type = 'TAP_ANYWHERE'\n", "    AND view_source = 'NYC'\n", "    AND story_id LIKE 'ttp-%'\n", "    AND country = 'US'\n", "    AND media_type = 'IMAGE'\n", "    AND media_url != '' AND media_url is not NULL\n", "GROUP BY 1,2,3\n", "    \"\"\"\n", "SAMPLE_SIZE = 1000\n", "\n", "#Prompt\n", "PROMPT = \"\"\"\n", "    Identify categories from the following list:\n", "    {categories}\n", "    \"\"\"\n", "TAXONOMY = None\n", "\n", "# Destination\n", "DESTINATION_TABLE = \"sc-bot-privacy.context_analysis.insights_brand_ads_20250710\"\n", "RETENTION_DAYS = 30\n", "\n", "#Optional\n", "MODEL_PARAMETERS = None\n", "PROCESSOR_CONFIG= None\n", "COLUMN_NAME = None\n", "PROJECT = 'myaigcp'\n", "SERVICE_ACCOUNT = \"<EMAIL>\"\n"]}, {"cell_type": "code", "execution_count": null, "id": "e0bc06e6", "metadata": {}, "outputs": [], "source": ["#Check if pass is provided\n", "if PASS is None:\n", "    raise ValueError(\"PASS is not provided\")"]}, {"cell_type": "code", "execution_count": null, "id": "06fb97e9", "metadata": {}, "outputs": [], "source": ["#Constuct prompt\n", "if TAXONOMY:\n", "    prompt = PromptLibrary.get_taxonomy_prompt(\n", "        taxonomy_name=TAXONOMY,\n", "        project_id=PROJECT,\n", "        custom_prompt = PROMPT if PROMPT else None\n", "    )\n", "else:\n", "    prompt = PROMPT"]}, {"cell_type": "code", "execution_count": null, "id": "b9524aa2", "metadata": {}, "outputs": [], "source": ["#Construct classification parameters\n", "model_parameters = {\"max_tokens\": 1000, 'temperature': 0} if MODEL_PARAMETERS is None else MODEL_PARAMETERS\n", "\n", "if PROCESSOR_CONFIG is None and INPUT_TYPE in ['image', 'video']:\n", "    processor_config = {'processing_mode': 'image_url',\n", "                        'return_direct_url': True}\n", "else:\n", "    processor_config = PROCESSOR_CONFIG\n", "\n", "\n", "if PROVIDER_NAME == \"gemini\":\n", "    provider_config = { \"project_id\": \"myaigcp\",\n", "                        \"location\": \"us-central1\"}\n", "elif PROVIDER_NAME == \"openai\": \n", "    provider_config = {\"service_account\": \n", "                        SERVICE_ACCOUNT}\n", "elif PROVIDER_NAME == \"agi\":\n", "    provider_config = {\"service_account\": \n", "                        SERVICE_ACCOUNT}\n", "\n", "    model_parameters['disable_thinking'] = True\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "53069f8b", "metadata": {}, "outputs": [], "source": ["classification_model = Classification(provider_name = PROVIDER_NAME,\n", "                                     model_name = MODEL_NAME,\n", "                                     prompt = prompt,\n", "                                     provider_config=provider_config,\n", "                                     processor_config = processor_config,\n", "                                     model_parameters=model_parameters,\n", "                                     input_type = INPUT_TYPE,\n", "                                     )"]}, {"cell_type": "code", "execution_count": null, "id": "04fd45ab", "metadata": {}, "outputs": [], "source": ["result = classification_model.send_message(\"Say: 'The model {model_name} is alive' \". format(model_name = MODEL_NAME))\n", "print(classification_model.get_result(result))"]}, {"cell_type": "code", "execution_count": null, "id": "39287fdb", "metadata": {}, "outputs": [], "source": ["## Download data\n", "column_name = 'sample'if COLUMN_NAME is None else COLUMN_NAME\n", "sql = SQL + f\" LIMIT {SAMPLE_SIZE}\" if SAMPLE_SIZE else SQL\n", "\n", "data = utils.gbq.read_gbq(sql, \n", "                          project_id=PROJECT,\n", "                          dialect=\"standard\",\n", "                          priority = \"INTERACTIVE\")\n", "dataset = data[column_name].values"]}, {"cell_type": "code", "execution_count": null, "id": "88c8c459", "metadata": {}, "outputs": [], "source": ["start_time = datetime.now()\n", "results = thread_map(classification_model.classify, dataset, max_workers=MAX_WORKER)\n", "print(\"Classification time: \", datetime.now()-start_time)"]}, {"cell_type": "code", "execution_count": null, "id": "53fcf172", "metadata": {}, "outputs": [], "source": ["data['label'] = [classification_model.get_result(i).lower() for i in results]\n", "data['completion_tokens'] = [classification_model.get_token_usage(i)['completion_tokens'] for i in results]\n", "data['prompt_tokens'] = [classification_model.get_token_usage(i)['prompt_tokens'] for i in results]\n", "data['model_name'] = MODEL_NAME"]}, {"cell_type": "code", "execution_count": null, "id": "0943c12a", "metadata": {}, "outputs": [], "source": ["total_cost = estimate_run_cost(data['completion_tokens'], data['prompt_tokens'], MODEL_NAME)\n", "print(total_cost)"]}, {"cell_type": "code", "execution_count": null, "id": "b39ab243", "metadata": {}, "outputs": [], "source": ["DESTINATION_PROJECT, DESTINATION_DATASET, DESTINATION_TABLE_NAME = DESTINATION_TABLE.split('.')"]}, {"cell_type": "code", "execution_count": null, "id": "655f5a86", "metadata": {}, "outputs": [], "source": ["## Upload results\n", "utils.gbq.to_gbq(\n", "                 data,\n", "                 project_id = PROJECT,\n", "                 dest_table_name = DESTINATION_TABLE_NAME,\n", "                 dest_dataset_id = DESTINATION_DATASET,\n", "                 dest_project_id = DESTINATION_PROJECT)\n", "\n", "print(f\"Results saved to {DESTINATION_PROJECT}.{DESTINATION_DATASET}.{DESTINATION_TABLE_NAME} table\")"]}, {"cell_type": "code", "execution_count": null, "id": "3f21db78", "metadata": {}, "outputs": [], "source": ["if RETENTION_DAYS:\n", "    client = bigquery.Client(project=DESTINATION_PROJECT)\n", "    table = client.get_table(f\"{DESTINATION_PROJECT}.{DESTINATION_DATASET}.{DESTINATION_TABLE_NAME}\")  \n", "    # Set the expiration time (e.g., 7 days from now)\n", "    \n", "    expiration_time = datetime.now(timezone.utc) + timedelta(days=RETENTION_DAYS)\n", "    table.expires = expiration_time\n", "    \n", "    # Update the table with new expiration time\n", "    client.update_table(table, [\"expires\"])  # Make an API request.\n", "    \n", "    print(f\"Table {table} expiration set to {expiration_time}\")"]}, {"cell_type": "code", "execution_count": null, "id": "31efb5f7", "metadata": {}, "outputs": [], "source": ["metadata = pd.DataFrame([{\n", "    \"provider_name\": PROVIDER_NAME,\n", "    \"model_name\": MODEL_NAME,\n", "    \"pass\": PASS,\n", "    \"input_type\": INPUT_TYPE,\n", " \n", "    \"model_parameters\": model_parameters,\n", "    \"processor_config\": processor_config,\n", "    \"provider_config\": provider_config,\n", "\n", "    \"sql\": SQL,\n", "    \"sample_size\": SAMPLE_SIZE,\n", "    \"prompt\": PROMPT,\n", " \n", "    \"destination_table\": DESTINATION_TABLE,\n", "    \"retention_days\": RETENTION_DAYS,\n", "\n", "    \"completion_tokens\": data['completion_tokens'].sum(),\n", "    \"prompt_tokens\": data['prompt_tokens'].sum(),\n", "    \"estimated_cost\": total_cost,\n", "\n", "    \n", "}])"]}, {"cell_type": "code", "execution_count": null, "id": "08bc25b1", "metadata": {}, "outputs": [], "source": ["## Upload results\n", "utils.gbq.to_gbq(\n", "                 metadata,\n", "                 project_id = PROJECT,\n", "                 dest_table_name = \"shiba_inu_logs\",\n", "                 dest_dataset_id = \"cameos_ai\",\n", "                 dest_project_id = \"sc-analytics\",\n", "                 write_disposition = 'WRITE_APPEND')\n", "\n", "print(\"Logs saved to sc-analytics.cameos_ai.shiba_inu_logs table\")"]}, {"cell_type": "code", "execution_count": null, "id": "f9dceb23", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "45495dd2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py310", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}