{"cells": [{"cell_type": "code", "execution_count": null, "id": "1bc64d20-4c5e-43c5-a833-06895a5e2ed9", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["from datetime import date, datetime, timedelta, timezone\n", "from banjo import utils\n", "from banjo.utils.shibainu import Classification, Validation, Visualization, estimate_run_cost, configure_logger\n", "import logging\n", "configure_logger(level=logging.ERROR)\n", "\n", "\n", "import numpy as np\n", "import pandas as pd\n", "from tqdm.contrib.concurrent import thread_map\n", "\n", "# # Google Cloud and authentication libraries\n", "from google.cloud import bigquery\n", "\n", "\n", "# Local application/library-specific imports\n", "import lca\n", "\n", "pd.set_option('display.max_colwidth', None)\n"]}, {"cell_type": "code", "execution_count": null, "id": "f7abb262-df7d-48c7-bbcc-bb99e6788b60", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": ["parameters"]}, "outputs": [], "source": ["## Constant\n", "\n", "### Init parameters\n", "SERVICE_ACCOUNT = \"<EMAIL>\"\n", "PROJECT = 'myaigcp' \n", "PRIORITY = 'INTERACTIVE'\n", "MAX_WORKER = 5\n", "\n", "\n", "\n", "RESULT_VISUALIZATION_SAMPLE = 10\n", "RESULT_CATEGORIES_SIZE_THRESHOLD = 5\n", "\n", "#Model parameters\n", "PROVIDER_NAME = 'gemini'\n", "MODEL_NAME = \"gemini-2.0-flash\"\n", "USE_MODEL_PARAMETERS = False\n", "MODEL_PARAMETERS = None\n", "TEMPERATURE = 0\n", "MAX_TOKEN = 1280\n", "\n", "\n", "\n", "### Data parameters\n", "DATA_TYPE = \"text\"\n", "DATA_SOURCE = \"table\" \n", "\n", "TABLE_NAME = \"sc-bq-gcs-billingonly.dishchenko.wiki_characters\" #if user picked \"table\" data source \n", "# CUSTOM_SQL = \"SELECT hashtag FROM sc-targeting-measurement.topic_engagement.hashtag_metadata\" #if user picked custom_sql \"sql\" data source\n", "CUSTOM_SQL =  \"\"\"\n", "SELECT distinct creative_id, creative_headline, media_download_link,\n", "FROM `sc-product-datascience.cyang.snap_promote_AI_classifier`\n", "WHERE creative_headline IS NOT NULL\n", "\"\"\"\n", "COLUMN_NAME = 'name' \n", "SAMPLE_SIZE = 100\n", "\n", "USE_CUSTOM_PROCESSOR_CONFIG = False\n", "PROCESSOR_CONFIG = None\n", "\n", "#Privacy and Legal review\n", "PASS = \"https://pass.mesh.sc-corp.net/privacyReviews/pass?id=EXAMPLE\"\n", "\n", "\n", "\n", "### Classificaiton parameters\n", "USE_CUSTOM_PROMPT = False\n", "CUSTOM_PROMPT = \"\"\"\n", "You are a classification model for ad creative. Now you are analyzing Snapchat Snap Promote videos, which are short-form sponsored video ads created by businesses, influencers, or creators.\n", "These videos may promote goals like gaining followers, website visits, chats, or profile views.\n", "Your task is to create a classification system of 5 to 8 categories that:\n", "•Capture the intent or theme of the video content (e.g., fashion, music, sports, etc)\n", "•Are mutually exclusive — each video should fit clearly into only one category\n", "•Are roughly balanced — the categories should ideally end up with similar amounts of content\n", "Use these guidelines to categorize video accurately, focusing on the leatest message. Reply only with the categories name.\n", "\"\"\"\n", "CLASSES = \"real character, fictional character\" #no need if using custom prompt\n", "USE_PREDEFINED_TAXONOMY = False\n", "TAXONOMY = \"SCC\"\n", "\n", "### Output parameters\n", "DESTINATION_TABLE = 'sc-bq-gcs-billingonly.dishchenko.xfun_text_ai_20241227'\n", "RETENTION_DAYS = 30\n", "\n", "\n", "## Validation parameter\n", "USE_VALIDATION = True\n", "VALIDATION_SAMPLE_SIZE = 10\n", "VALIDATION_PROVIDER_NAME = 'openai'\n", "VALIDATION_MODEL_NAME = 'gpt-4o'\n", "\n", "##Processing parameters\n", "VIDEO_SAMPLING_MODE = 'fps'\n", "VIDEO_SAMPLING_VALUE = 1.0\n", "VIDEO_MAX_FRAMES = 20\n", "DOWNLOAD_VIDEO = True\n", "PROCESSING_MODE = None\n", "RETURN_DIRECT_URL = False"]}, {"cell_type": "code", "execution_count": null, "id": "0efb87ec-dfdd-4f43-b304-9dfb3e79654c", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["# handle the checkbox\n", "def handle_husky_checkbox(parameter, checked_value, unchecked_value):\n", "    \"\"\"husky always returns the checkbox values as strings\"\"\"\n", "    if isinstance(parameter, str):\n", "        return checked_value\n", "    elif parameter in [checked_value, unchecked_value]:\n", "        return parameter\n", "    else:\n", "        return unchecked_value\n"]}, {"cell_type": "code", "execution_count": null, "id": "32f34564-911e-4c8e-b34c-7a4720cdeef8", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["### Parameters processing"]}, {"cell_type": "code", "execution_count": null, "id": "83913f53-0ed6-4927-b07e-aebc74495c57", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["USE_CUSTOM_PROCESSOR_CONFIG = handle_husky_checkbox(USE_CUSTOM_PROCESSOR_CONFIG, True, False)  \n", "\n", "\n", "if USE_CUSTOM_PROCESSOR_CONFIG:\n", "    processor_config = PROCESSOR_CONFIG\n", "\n", "else:  \n", "    if not PROCESSING_MODE:\n", "        if FIRST_DATA_TYPE == 'image' or SECOND_DATA_TYPE == 'image':\n", "            PROCESSING_MODE = 'image_url'\n", "        elif FIRST_DATA_TYPE == 'video' or SECOND_DATA_TYPE == 'video':\n", "            if PROVIDER_NAME == 'openai':\n", "                PROCESSING_MODE = 'bytes'\n", "            else:\n", "                PROCESSING_MODE = 'image_url'\n", "                DOWNLOAD_VIDEO = False\n", "                RETURN_DIRECT_URL = True\n", "    \n", "    processor_config = {\n", "        'sampling_mode': VIDEO_SAMPLING_MODE,\n", "        'sampling_value': VIDEO_SAMPLING_VALUE,\n", "        'max_frames': VIDEO_MAX_FRAMES,\n", "        'download': DOWNLOAD_VIDEO,\n", "        'processing_mode': PROCESSING_MODE,\n", "        'return_direct_url': RETURN_DIRECT_URL\n", "    }"]}, {"cell_type": "code", "execution_count": null, "id": "e4c17804-2da2-4df7-85f9-367e486315a4", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["USE_MODEL_PARAMETERS = handle_husky_checkbox(USE_MODEL_PARAMETERS, True, False)  \n", "\n", "if USE_MODEL_PARAMETERS:\n", "    model_parameters = MODEL_PARAMETERS\n", "else:\n", "    model_parameters = {'temperature': TEMPERATURE, 'max_token': MAX_TOKEN}"]}, {"cell_type": "code", "execution_count": null, "id": "5c3e8990-523e-4d5d-8cfb-f4c33b49d5c9", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["if PROVIDER_NAME == 'openai':\n", "    provider_config={\n", "        \"service_account\": \"<EMAIL>\"}\n", "elif PROVIDER_NAME == 'gemini':\n", "    provider_config = {\n", "                        \"project_id\": \"myaigcp\",\n", "                         \"location\": \"us-central1\"}"]}, {"cell_type": "code", "execution_count": null, "id": "17787ac2-5986-42e3-a7a3-b50005372df9", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["USE_VALIDATION = handle_husky_checkbox(USE_VALIDATION, True, False)  \n", "if USE_VALIDATION == True and (DATA_TYPE == \"image\" or DATA_TYPE == \"video\") and VALIDATION_MODEL_NAME == 'o1-mini':\n", "    print(\"O1 model currently unable to process images. Please, use another model for validation\")\n", "    USE_VALIDATION = False"]}, {"cell_type": "code", "execution_count": null, "id": "81e9698a-f772-4197-b960-e4a331abff90", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["DEFAULT_PROMT  = \"\"\"\n", "You are a classification model. \n", "Assign the following message to one of the predefined classes: \n", "{CLASS_LIST}.\n", "\n", "Provide only the class name as your response.\n", "If the class is unclear or the context is not understood, choose 'unknown'.\n", "\"\"\"\n", "\n", "USE_CUSTOM_PROMPT = handle_husky_checkbox(USE_CUSTOM_PROMPT, True, False)  \n", "USE_PREDEFINED_TAXONOMY = handle_husky_checkbox(USE_PREDEFINED_TAXONOMY, True, False)  \n", "\n", "if USE_CUSTOM_PROMPT:\n", "    PROMPT = CUSTOM_PROMPT\n", "elif <PERSON>_PREDEFINED_TAXONOMY:\n", "    PROMPT = PromptLibrary().get_taxonomy_prompt(TAXONOMY, project_id='myaigcp', priority=\"INTERACTIVE\")\n", "else:\n", "    PROMPT = DEFAULT_PROMT.format(CLASS_LIST = CLASSES) "]}, {"cell_type": "code", "execution_count": null, "id": "acb8e7cc-eb7d-43fb-850f-a19e0f8721a4", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["class PrivacyException(Exception):\n", "    def __init__(self, msg=\"\"\"\n", "        Please, attach the link to approved PASS. \n", "        Note: It is your responsibility to ensure that it is filled out correctly and approved.\"\"\", *args, **kwargs):\n", "        super().__init__(msg, *args, **kwargs)\n", "    pass\n", "\n", "#check if PASS is attached\n", "if (\"https://pass.mesh.sc-corp.net/privacyReviews/pass\" in PASS):\n", "    pass\n", "else:\n", "    raise PrivacyException\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "228dde3c-ee20-43f0-98a4-cc94bb0427f8", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["if DATA_SOURCE == 'table':\n", "    SQL = \"\"\"\n", "    SELECT {column_name}\n", "    FROM `{table}`\n", "    ORDER BY RAND()\n", "    LIMIT {sample_size}\n", "    \"\"\".format(column_name = COLUMN_NAME, table = TABLE_NAME, sample_size = SAMPLE_SIZE)\n", "else:\n", "    SQL = CUSTOM_SQL + ' LIMIT {sample_size}'.format(sample_size = SAMPLE_SIZE)\n", "    "]}, {"cell_type": "markdown", "id": "9e58cd8e-3d6e-4b5b-8c92-601af2aa9cbc", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["## Test"]}, {"cell_type": "code", "execution_count": null, "id": "3b29857c-b07d-453c-9cc5-aa2225cef342", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["classification_model = Classification(provider_name = PROVIDER_NAME,\n", "                                     model_name = MODEL_NAME,\n", "                                     prompt = PROMPT,\n", "                                     provider_config=provider_config,\n", "                                     processor_config = processor_config,\n", "                                     model_parameters=model_parameters,\n", "                                     input_type = DATA_TYPE,\n", "                                     )"]}, {"cell_type": "code", "execution_count": null, "id": "9c3cde3d-327d-498a-9b10-0b30f9754133", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["result = classification_model.send_message(\"Say: 'The model {model_name} is alive' \". format(model_name = MODEL_NAME))\n", "print(classification_model.get_result(result))"]}, {"cell_type": "code", "execution_count": null, "id": "6fc51192-b7fc-4b41-b213-3d31ec5884f1", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["## Download data\n", "data = utils.gbq.read_gbq(SQL, \n", "                          project_id=PROJECT,\n", "                          dialect=\"standard\",\n", "                          priority = PRIORITY)\n", "dataset = data[COLUMN_NAME].values"]}, {"cell_type": "markdown", "id": "638ec566-176a-48f0-bba7-b65bb35b73eb", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["## Classifcation processing"]}, {"cell_type": "code", "execution_count": null, "id": "f664a64e-1f66-4d6d-b482-28bd34a126f2", "metadata": {}, "outputs": [], "source": ["start_time = datetime.now()\n", "results = thread_map(classification_model.classify, dataset, max_workers=MAX_WORKER)\n", "print(\"Classification time: \", datetime.now()-start_time)"]}, {"cell_type": "code", "execution_count": null, "id": "f7f97177-b964-472e-9606-3dd18755f8d4", "metadata": {}, "outputs": [], "source": ["data['label'] = [classification_model.get_result(i).lower() for i in results]\n", "data['completion_tokens'] = [classification_model.get_token_usage(i)['completion_tokens'] for i in results]\n", "data['prompt_tokens'] = [classification_model.get_token_usage(i)['prompt_tokens'] for i in results]"]}, {"cell_type": "code", "execution_count": null, "id": "4c854413-05fd-4dff-8377-afaa4707653d", "metadata": {}, "outputs": [], "source": ["## Validation processing\n", "\n", "if USE_VALIDATION:\n", "    classification_model = Classification(provider_name = VALIDATION_PROVIDER_NAME,\n", "                                     model_name = VALIDATION_MODEL_NAME,\n", "                                     prompt = PROMPT,\n", "                                     provider_config={\n", "                                        \"service_account\": \"<EMAIL>\"},\n", "                                     processor_config = processor_config,\n", "                                     model_parameters={'temperature': TEMPERATURE, 'max_token': MAX_TOKEN},\n", "                                     input_type = DATA_TYPE,\n", "                                     )\n", "    \n", "    validation_subset = data.sample(n=VALIDATION_SAMPLE_SIZE, random_state=42)\n", "    \n", "    val_dataset = validation_subset[COLUMN_NAME].to_list()\n", "    start_time = datetime.now()\n", "    val_results = thread_map(classification_model.classify, val_dataset, max_workers=MAX_WORKER)\n", "\n", "\n", "    print(\"Validation GPT inference time: \", datetime.now()-start_time)\n", "    data[\"validation_label\"] = np.nan \n", "    data['validation_completion_tokens'] = np.nan \n", "    data['validation_prompt_tokens'] = np.nan \n", "    \n", "    data.loc[validation_subset.index, \"validation_label\"] = [classification_model.get_result(i).lower() for i in val_results]\n", "    data.loc[validation_subset.index, \"validation_completion_tokens\"] = [classification_model.get_token_usage(i)['completion_tokens'] for i in val_results]\n", "    data.loc[validation_subset.index, \"validation_prompt_tokens\"] = [classification_model.get_token_usage(i)['prompt_tokens'] for i in val_results]\n"]}, {"cell_type": "markdown", "id": "ad31d404-0f3b-4710-a8c8-a3fefff0ace3", "metadata": {}, "source": ["## Validation"]}, {"cell_type": "code", "execution_count": null, "id": "b74a7e0d-7e68-40f0-a43a-f5b161b9e71e", "metadata": {}, "outputs": [], "source": ["if USE_VALIDATION:\n", "    counts = data['label'].value_counts()\n", "    defined_category = set(counts[counts > RESULT_CATEGORIES_SIZE_THRESHOLD].index)\n", "    \n", "    metrics = Validation(data[~data[\"validation_label\"].isna()], defined_category)\n", "\n", "    # Calculate and print metrics\n", "    print(\"==== Result Validation ==== \")\n", "    print(\"Validation is based on \", VALIDATION_MODEL_NAME, \" output.\")\n", "    print(\"Validation is conducted on \", VALIDATION_SAMPLE_SIZE, \" samples.\\n\")\n", "    print(\"Key Validation Metrics:\")\n", "    \n", "    # Get the metrics as dictionaries rather than trying to access by index\n", "    within_category_metrics = metrics.calculate_within_defined_category()\n", "    \n", "    validation_metrics = {\n", "        \"% of Labels within defined category\": np.round(within_category_metrics[\"predicted_within_category\"],0),\n", "        \"% of Labels within defined category for validation model\": np.round(within_category_metrics[\"validated_within_category\"],0),\n", "        \"Accuracy\": np.round(metrics.calculate_accuracy(),2),\n", "        \"Precision\": np.round(metrics.calculate_precision(),2),\n", "        \"Recall\": np.round(metrics.calculate_recall(),2),\n", "        \"f1_score\": np.round(metrics.calculate_f1_score(),2),\n", "    }\n", "    \n", "    df_validation_metrics = pd.DataFrame.from_dict(validation_metrics, orient=\"index\", columns=[\"value\"])\n", "    display(df_validation_metrics)\n", "    \n", "    # Plot the confusion matrix\n", "    print(\"Plot Validation Confusion Matrix:\")\n", "    metrics.plot_confusion_matrix()\n", "\n", "else:\n", "    print(\"Validation of results was not conducted.\")"]}, {"cell_type": "markdown", "id": "fd18ae2a-afad-4ff7-9ffb-10875519696a", "metadata": {}, "source": ["## Cost estimation"]}, {"cell_type": "code", "execution_count": null, "id": "ba28f6e5-5e52-4d89-bb64-ee4d0edde63b", "metadata": {}, "outputs": [], "source": ["est_cost = estimate_run_cost(data['completion_tokens'], data[\"prompt_tokens\"], MODEL_NAME)\n", "\n", "print(f\"Estimated cost for prediction is {est_cost} $\")"]}, {"cell_type": "code", "execution_count": null, "id": "eec7cd24-1264-4be4-88d9-be016a20b4e9", "metadata": {}, "outputs": [], "source": ["if USE_VALIDATION:\n", "    est_cost = estimate_run_cost(data['validation_completion_tokens'], data[\"validation_prompt_tokens\"], VALIDATION_MODEL_NAME)\n", "        \n", "    print(f\"Estimated cost for validation is {est_cost} $\")"]}, {"cell_type": "markdown", "id": "29f5012e-28c8-4785-b1c4-3f6eb6bbd7c2", "metadata": {}, "source": ["## Save Results"]}, {"cell_type": "code", "execution_count": null, "id": "510c8e25-e336-4276-954e-79a5cec2fa7a", "metadata": {}, "outputs": [], "source": ["DESTINATION_TABLE_NAME, DESTINATION_DATASET, DESTINATION_PROJECT = 'sc-bq-gcs-billingonly.dishchenko.xfun_text_ai_20241227'.split('.')"]}, {"cell_type": "code", "execution_count": null, "id": "5be32142-0b1d-4b50-ab55-395c4b1199f6", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["## Upload results\n", "utils.gbq.to_gbq(\n", "                 data,\n", "                 project_id = PROJECT,\n", "                 dest_table_name = DESTINATION_TABLE_NAME,\n", "                 dest_dataset_id = DESTINATION_DATASET,\n", "                 dest_project_id = DESTINATION_PROJECT)\n", "\n", "print(f\"Results saved to {DESTINATION_PROJECT}.{DESTINATION_DATASET}.{DESTINATION_TABLE_NAME} table\")"]}, {"cell_type": "code", "execution_count": null, "id": "ce41e6c6-d758-4276-9af2-75617ef85925", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["if RETENTION_DAYS:\n", "    client = bigquery.Client(project=DESTINATION_PROJECT)\n", "    table = client.get_table(f\"{DESTINATION_PROJECT}.{DESTINATION_DATASET}.{DESTINATION_TABLE_NAME}\")  \n", "    # Set the expiration time (e.g., 7 days from now)\n", "    \n", "    expiration_time = datetime.now(timezone.utc) + timedelta(days=RETENTION_DAYS)\n", "    table.expires = expiration_time\n", "    \n", "    # Update the table with new expiration time\n", "    client.update_table(table, [\"expires\"])  # Make an API request.\n", "    \n", "    print(f\"Table {table} expiration set to {expiration_time}\")"]}, {"cell_type": "code", "execution_count": null, "id": "f90544dc-1721-41bf-8e26-5cf520ecd3b0", "metadata": {}, "outputs": [], "source": ["metadata = pd.DataFrame([{\n", "    \"service_account\": PROVIDER_NAME,\n", "    \"project\": PROJECT,\n", "    \"temperature\": TEMPERATURE,\n", "    \"max_token\": MAX_TOKEN,\n", "    \"max_worker\": MAX_WORKER,\n", "    \"model_name\": MODEL_NAME,\n", "    \"pass\": PASS,\n", "    \"use_custom_prompt\": USE_CUSTOM_PROMPT,\n", "    \"use_prompt_optimization\": USE_PROMPT_OPTIMIZATION,\n", "    \"data_type\": DATA_TYPE,\n", "    \"data_source\": DATA_SOURCE,\n", "    \"sql\": SQL,\n", "    \"column_name\": COLUMN_NAME,\n", "    \"sample_size\": SAMPLE_SIZE,\n", "    \"prompt\": PROMPT,\n", "    \"destination_table\": DESTINATION_TABLE,\n", "    \"destination_dataset\": DESTINATION_DATASET,\n", "    \"destination_project\": DESTINATION_PROJECT,\n", "    \"retention_days\": RETENTION_DAYS,\n", "    \"use_validation\": USE_VALIDATION,\n", "    \"validation_sample_size\": VALIDATION_SAMPLE_SIZE,\n", "    \"validation_model_name\": VALIDATION_MODEL_NAME\n", "}])"]}, {"cell_type": "code", "execution_count": null, "id": "75bca6b6-ec36-429d-958f-75a1a87c70ba", "metadata": {}, "outputs": [], "source": ["## Upload results\n", "utils.gbq.to_gbq(\n", "                 metadata,\n", "                 project_id = PROJECT,\n", "                 dest_table_name = \"shiba_inu_logs\",\n", "                 dest_dataset_id = \"cameos_ai\",\n", "                 dest_project_id = \"sc-analytics\",\n", "                 write_disposition = 'WRITE_APPEND')\n", "\n", "print(\"Logs saved to sc-analytics.cameos_ai.shiba_inu_logs table\")"]}, {"cell_type": "markdown", "id": "1532bdd6", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["## Result Visualization"]}, {"cell_type": "code", "execution_count": null, "id": "9baac27c", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["# import packages and settings\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import matplotlib\n", "from IPython.display import display, HTML\n", "\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "sns.set_palette(palette='Set2')\n", "sns.set_theme(style=\"white\")\n", "font = {'family' : 'DejaVu Sans',\n", "       'weight' : 'normal',\n", "       'size'   : 12}\n", "matplotlib.rc('font', **font)\n", "\n", "# parameters\n", "# PROJECT_ALIAS = 'Ad Creative Classification'\n", "# RESULT_VISUALIZATION_SAMPLE = 5"]}, {"cell_type": "code", "execution_count": null, "id": "fd28babd", "metadata": {"editable": true, "scrolled": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["# ################## SECTION. RESULT VISUALIZATION #######################\n", "\n", "# instantiate the visualization class\n", "visualization = Visualization(data = data, data_type=DATA_TYPE)\n", "print(\"==== Result Visualization ====\")\n", "\n", "# figure1. plot the distribution of classification labels\n", "visualization.plot_label_counts(figsize=(10,5), column_name='label', title=\"Displaying distribution of classification labels\", threshold=RESULT_CATEGORIES_SIZE_THRESHOLD)\n", "print(\"\\n\")\n", "\n", "# figure2. plot the sample of the data\n", "visualization.display_samples(column_name=COLUMN_NAME, n_sample=RESULT_VISUALIZATION_SAMPLE)\n", "print(\"\\n\")\n", "\n", "if USE_VALIDATION:\n", "    # figure3. plot the result validation precision/recall\n", "    visualization.plot_label_counts(figsize=(10,5), column_name='validation_label', title=\"Displaying distribution of validation labels\")\n", "    print(\"\\n\")\n", "    \n", "    # figure4: plot the mismatched samples from validations\n", "    visualization.display_mismatches(column_name=COLUMN_NAME, n_sample=RESULT_VISUALIZATION_SAMPLE)\n", "    print(\"\\n\")\n", "\n", "else:\n", "    print(\"Validation of results was not conducted.\")\n", "\n", "# ################## END OF RESULT VISUALIZATION #######################"]}, {"cell_type": "code", "execution_count": null, "id": "15c8d8d9-3b09-4ae6-8b1f-d3cb2223f840", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ec8d645e-5945-40f6-91bd-0ba5db9dbe82", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2fd71385-a2c0-4ec2-bd21-fea65e81c9c4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "52e2f664-fb07-4156-9ce7-6a147c805a0a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "py38", "language": "python", "name": "py38"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.19"}}, "nbformat": 4, "nbformat_minor": 5}