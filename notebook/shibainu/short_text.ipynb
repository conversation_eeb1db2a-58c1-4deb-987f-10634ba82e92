{"cells": [{"cell_type": "code", "execution_count": null, "id": "c320c927-de5b-4c54-afc3-be3ff2c2a689", "metadata": {}, "outputs": [], "source": ["#from banjo.utils.husky_api import HuskyAPIJobRequest, HuskyAPIJobRuntimeError, HUSKY_PROD_ENV, HUSKY_STG_ENV, ContainerResourceConfig\n", "from datetime import datetime, timedelta, timezone\n", "from banjo import utils\n", "from tqdm.contrib.concurrent import thread_map\n", "import pandas as pd\n", "import json\n", "from google.cloud import bigquery\n", "from banjo.utils.shibainu import Classification, estimate_run_cost, configure_logger\n", "import logging\n", "configure_logger(level=logging.ERROR)"]}, {"cell_type": "code", "execution_count": null, "id": "bd63778f", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["DATE = '20250603'\n", "SAMPLE_SIZE = 1000\n", "PROVIDER_NAME = 'agi'\n", "MODEL_NAME = 'Qwen3-30B-A3B-FP8'\n", "MAX_TOKEN = 1000\n", "MAX_WORKERS = 250\n", "RETENTION_DAYS = 30\n", "\n", "PROMPT = \"\"\"You are an expert in content tagging and classification are tasked with labeling text with the correct keywords.\n", "This is users comments under the public post. You have to extract keywords and classify these keywords into following predefined cartegories.\n", "Output is only keywords and categories for the following text. If the text or keywords are not in English, translate the outputs into English.\n", "\n", "The criteria of correct keywords are:\n", "- Brands and Products mentioned or appeared in the comment\n", "- Keywords which describe what happening in the comment\n", "- All extracted keywords have to with 1 or 2 words\n", "- Be as specific as possible, e.g., if the concept is \\\"latin dance\\\", provide the full phrase, not \\\"latin\\\" and \\\"dance\\\"\n", "- Do not include general words like \"hello\", \"thanks\", \"okay\"\n", "- The number of keywords is up to 20\n", "- The list of keywords have to capture the main idea of the comment\n", "- If you don't have any keywords - reply \"unknown\"\n", "- DO NOT REPEAT\n", "\n", "Output format is json: \\{\"keywords\": [\"keyword1\", \"keyword2\", \"keyword3\"],\n", "    \"categories\": [\"category1\", \"category2\"]\\}\n", "\n", "Your goal categorization is to pick from the list bellow which are directly related to one or a few provided keywords from the next message.\n", "Select only categories names from the list, with the same spelling, no other text in output.\n", "DONT CREATE NEW CATEGORIES!\n", "\n", "The list of possible categories:\n", "\"\"\"\n", "\n", "SQL = \"\"\"\n", "SELECT snap_poster_id, snap_id, reply_id, reply_text as sample\n", "FROM `context-pii.spotlight_replies.daily_export_20250702`\n", "WHERE LENGTH(reply_text) > 4\n", "AND approval_state = 'APPROVED'\n", "\n", "ORDER BY react_total_count DESC\n", "\"\"\"\n", "\n", "DESTINATION_TABLE = \"sc-trends-prod.labeled_content.comments_20250702\""]}, {"cell_type": "code", "execution_count": null, "id": "5805e27e", "metadata": {}, "outputs": [], "source": ["scc_df = utils.gbq.read_gbq(\"\"\"SELECT * FROM `sc-analytics.cameos_ai.insights_snapchat_content_categories`\"\"\", \n", "                          project_id='myaigcp',\n", "                          dialect=\"standard\",\n", "                          priority = \"INTERACTIVE\")\n", "\n", "categories_list = scc_df[\"category\"].tolist()\n", "categories_set = set(categories_list) \n", "categories_string = \"\\n\".join(categories_set)\n", "categories_set_lower = {category.lower() for category in categories_list}\n"]}, {"cell_type": "code", "execution_count": null, "id": "5b4be89e", "metadata": {}, "outputs": [], "source": ["prompt = PROMPT +  categories_string"]}, {"cell_type": "code", "execution_count": null, "id": "e11195af-7607-4b43-ba6d-e841e0cf7716", "metadata": {"tags": []}, "outputs": [], "source": ["sql = SQL + \" LIMIT {sample_size}\".format(sample_size=SAMPLE_SIZE)"]}, {"cell_type": "code", "execution_count": null, "id": "3045d2a4", "metadata": {}, "outputs": [], "source": ["data = utils.gbq.read_gbq(sql, \n", "                          project_id='myaigcp',\n", "                          dialect=\"standard\",\n", "                          priority = \"INTERACTIVE\")\n", "dataset = data['sample'].values"]}, {"cell_type": "code", "execution_count": null, "id": "18b4b989", "metadata": {}, "outputs": [], "source": ["data['model_name'] = MODEL_NAME"]}, {"cell_type": "code", "execution_count": null, "id": "cb5c379f", "metadata": {}, "outputs": [], "source": ["model_parameters = {\"max_tokens\": MAX_TOKEN, 'temperature': 0}\n", "processor_config = {'processing_mode': 'image_url',\n", "                    'return_direct_url': True}\n", "\n", "if PROVIDER_NAME == \"gemini\":\n", "    provider_config = { \"project_id\": \"myaigcp\",\n", "                        \"location\": \"us-central1\"}\n", "else: \n", "    provider_config = {\"service_account\": \"<EMAIL>\"}\n", "\n", "if PROVIDER_NAME == \"agi\":\n", "    model_parameters['disable_thinking'] = True\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "01b5c3c3", "metadata": {}, "outputs": [], "source": ["classification_model = Classification(provider_name = PROVIDER_NAME,\n", "                                     model_name = MODEL_NAME,\n", "                                     prompt = PROMPT,\n", "                                     provider_config=provider_config,\n", "                                     processor_config = processor_config,\n", "                                     model_parameters=model_parameters,\n", "                                     input_type = 'text',\n", "                                     )"]}, {"cell_type": "code", "execution_count": null, "id": "ef980f57", "metadata": {}, "outputs": [], "source": ["r = classification_model.classify(dataset[0])"]}, {"cell_type": "code", "execution_count": null, "id": "dd5845b8", "metadata": {}, "outputs": [], "source": ["start_time = datetime.now()\n", "results = thread_map(classification_model.classify, dataset, max_workers=MAX_WORKERS)\n", "print(\"Classification time: \", datetime.now()-start_time)"]}, {"cell_type": "code", "execution_count": null, "id": "c15bc946", "metadata": {}, "outputs": [], "source": ["data['label'] = [classification_model.get_result(i).lower() for i in results]\n", "data['completion_tokens'] = [classification_model.get_token_usage(i)['completion_tokens'] for i in results]\n", "data['prompt_tokens'] = [classification_model.get_token_usage(i)['prompt_tokens'] for i in results]"]}, {"cell_type": "code", "execution_count": null, "id": "ea4d31eb", "metadata": {}, "outputs": [], "source": ["print(estimate_run_cost(data['prompt_tokens'], data['completion_tokens'], MODEL_NAME))"]}, {"cell_type": "code", "execution_count": null, "id": "3c637728", "metadata": {}, "outputs": [], "source": ["def filter_categories_from_list(column):\n", "    \"\"\"\n", "    Filters a list of category strings against a predefined set.\n", "    \"\"\"\n", "    # if not isinstance(column, list):\n", "    #     return '' # Return empty string if data is not a list\n", "\n", "    # Filter the list directly and join the result into a string\n", "    category_list = column.split(\",\")\n", "    filtered = [cat for cat in category_list if cat.lower() in categories_set_lower]\n", "    return ', '.join(filtered)"]}, {"cell_type": "code", "execution_count": null, "id": "228b3ab8", "metadata": {}, "outputs": [], "source": ["l = []\n", "f = 0\n", "for index, row in data.iterrows():\n", "    try:\n", "        if row['label'][-1] == '}':\n", "            l.append(json.loads(row['label']))\n", "        elif row['label'][-1] == ']':\n", "            l.append(json.loads(row['label'] + '}'))\n", "        elif row['label'][-1] == '\"':\n", "            l.append(json.loads(row['label'] + ']}'))\n", "        elif row['label'][-1] == ',':\n", "            l.append(json.loads(row['label'][:-1] + ']}'))\n", "        else:\n", "            try:\n", "                l.append(json.loads(row['label'] + '\"]}'))\n", "            except:\n", "                l.append(json.loads('{\"keywords\": [\"unknown\"], \"categories\": [\"unknown\"]}'))\n", "                f+=1\n", "    except:\n", "        l.append(json.loads('{\"keywords\": [\"unknown\"], \"categories\": [\"unknown\"]}'))\n", "        f+=1\n", "\n", "print('Failed parsing: ', f)\n", "\n", "keywords = []\n", "categories = []\n", "\n", "for i in l:\n", "    keywords.append(i.get('keywords'))\n", "    categories.append(i.get('categories'))\n", "\n", "data['keywords'] = keywords\n", "data['categories'] = categories\n", "\n", "data['keywords'] = data['keywords'].apply(lambda x: str(x).replace(\"[\",\"\").replace(\"]\",\"\").replace(\"'\",\"\").replace(\"'\",\"\").replace(\"  \",\" \"))\n", "data['categories'] = data['categories'].apply(lambda x: str(x).replace(\"[\",\"\").replace(\"]\",\"\").replace(\"'\",\"\").replace(\"  \",\" \"))\n", "\n", "\n", "# Apply the function to the categories column\n", "data['label_cls_filtered'] = data['categories'].apply(filter_categories_from_list)"]}, {"cell_type": "code", "execution_count": null, "id": "fa706a30", "metadata": {}, "outputs": [], "source": ["data[data.keywords == 'unknown']['label'].to_list()[4]\n", "data['model_name'] = MODEL_NAME\n", "data['sample_size'] = SAMPLE_SIZE"]}, {"cell_type": "code", "execution_count": null, "id": "d20d6499-107a-4865-9339-75979fc8362a", "metadata": {"tags": []}, "outputs": [], "source": ["PROJECT ='myaigcp'\n", "\n", "DESTINATION_PROJECT, DESTINATION_DATASET, DESTINATION_TABLE_NAME = DESTINATION_TABLE.split('.')"]}, {"cell_type": "code", "execution_count": null, "id": "05704e73", "metadata": {}, "outputs": [], "source": ["## Upload results\n", "utils.gbq.to_gbq(\n", "                 data,\n", "                 project_id = PROJECT,\n", "                 dest_table_name = DESTINATION_TABLE_NAME,\n", "                 dest_dataset_id = DESTINATION_DATASET,\n", "                 dest_project_id = DESTINATION_PROJECT)\n", "\n", "print(f\"Results saved to {DESTINATION_PROJECT}.{DESTINATION_DATASET}.{DESTINATION_TABLE_NAME} table\")"]}, {"cell_type": "code", "execution_count": null, "id": "88442844", "metadata": {}, "outputs": [], "source": ["if RETENTION_DAYS:\n", "    client = bigquery.Client(project=DESTINATION_PROJECT)\n", "    table = client.get_table(f\"{DESTINATION_PROJECT}.{DESTINATION_DATASET}.{DESTINATION_TABLE_NAME}\")  \n", "    # Set the expiration time (e.g., 7 days from now)\n", "    \n", "    expiration_time = datetime.now(timezone.utc) + timedelta(days=RETENTION_DAYS)\n", "    table.expires = expiration_time\n", "    \n", "    # Update the table with new expiration time\n", "    client.update_table(table, [\"expires\"])  # Make an API request.\n", "    \n", "    print(f\"Table {table} expiration set to {expiration_time}\")"]}, {"cell_type": "code", "execution_count": null, "id": "b423b2b3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"environment": {"kernel": "python3", "name": "tf2-cpu.2-11.m125", "type": "gcloud", "uri": "us-docker.pkg.dev/deeplearning-platform-release/gcr.io/tf2-cpu.2-11:m125"}, "kernelspec": {"display_name": "py310", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}