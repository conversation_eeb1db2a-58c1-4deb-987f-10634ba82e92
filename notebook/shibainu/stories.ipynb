{"cells": [{"cell_type": "code", "execution_count": null, "id": "c320c927-de5b-4c54-afc3-be3ff2c2a689", "metadata": {}, "outputs": [], "source": ["#from banjo.utils.husky_api import HuskyAPIJobRequest, HuskyAPIJobRuntimeError, HUSKY_PROD_ENV, HUSKY_STG_ENV, ContainerResourceConfig\n", "from datetime import datetime, timedelta, timezone\n", "from banjo import utils\n", "from tqdm.contrib.concurrent import thread_map\n", "from google.cloud import bigquery, storage\n", "from banjo.utils.shibainu import Classification, estimate_run_cost, configure_logger\n", "import logging\n", "configure_logger(level=logging.ERROR)\n", "\n", "import requests\n", "import tempfile\n", "import os\n", "import pandas as pd\n", "\n", "from concurrent.futures import ThreadPoolExecutor\n", "from tqdm.notebook import tqdm\n"]}, {"cell_type": "code", "execution_count": null, "id": "bd63778f", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["MAX_WORKERS = 25\n", "\n", "\n", "EXTRACTION_PROVIDER_NAME = 'gemini'\n", "EXTRACTION_MODEL_NAME = 'gemini-2.0-flash'\n", "\n", "CLASSIFICATION_PROVIDER_NAME = 'agi'\n", "CLASSIFICATION_MODEL_NAME = 'Qwen3-30B-A3B-FP8'\n", "\n", "MAX_TOKEN = 1000\n", "\n", "BUCKET_NAME = 'shiba-inu-temp'\n", "BUCKET_FOLDER = 'maps_stories_20250702'\n", "\n", "EXTRACTION_PROMPT = \"\"\"You are an expert in content tagging and are tasked with labeling videos with the correct keywords.\n", "This is users posted stories.\n", "Output is ONLY lowercase keywords for the following video, no other text in reply.\n", "If the video or keywords are not in English, translate the outputs into English.\n", "\n", "The criteries of correct keywords are:\n", "- Brands and Products mentioned or appeared in the video\n", "- Keywords which describe what happening in the video\n", "- Text on video\n", "- Names of objects apperaed in the video\n", "- All extracted keywords have to with 1 or 2 words\n", "- Be as specific as possible, e.g., if the concept is \\\"latin dance\\\", provide the full phrase, not \\\"latin\\\" and \\\"dance\\\"\n", "- Do not provide generic keywords like \\\"video\\\"\n", "- The number of keywords is up to 30\n", "- The list of keywrods have to capturing the main idea of the video\n", "- If you don't have any keywords - reply \"unknown\"\n", "- DO NOT REPEAT\n", "\n", "Output format is list separated by commas: keyword1, keyword2, keyword3, etc.\n", "\"\"\"\n", "\n", "CLASSIFICATION_PROMPT = \"\"\"\n", "You are an expert in content classification. \n", "Your goal is to pick one or a few categories from the list bellow which are directly related to one or a few provided keywords from the next message.\n", "Output is the only categories names from the list, with the same spelling, no other text in output. Format is the low case list separated by comma: category1, category2, category3\n", "DONT CREATE NEW CATEGORIES!\n", "\n", "The list of possible categories:\n", "\"\"\"\n", "\n", "\n", "SQL = \"\"\"\n", "WITH urls as (SELECT story_snap_id, MAX(IF(unencrypted_flat_video_result_url != '', unencrypted_flat_video_result_url, media_url)) AS media_url\n", "FROM `context-pii.snapjoin.our_story_snap_2025*`\n", "WHERE _TABLE_SUFFIX BETWEEN RIGHT(FORMAT_DATE('%Y%m%d', DATE_SUB(PARSE_DATE('%Y%m%d','20250702'), INTERVAL 7 DAY)), 4) AND RIGHT('20250702', 4)\n", "GROUP BY 1)\n", "\n", "SELECT story_snap_id as sample_id, media_url as sample, IF(media_type = 'IMAGE', 'image', 'video') as media_type, count(*) as views\n", "FROM `sc-analytics.report_maps.daily_maps_story_events_20250702`\n", "LEFT JOIN urls USING (story_snap_id)\n", "WHERE \n", "     event_name = 'STORY_SNAP_VIEW'\n", "    AND map_story_type = 'TAP_ANYWHERE'\n", "    AND view_source = 'NYC'\n", "    AND story_id LIKE 'ttp-%'\n", "    AND country = 'US'\n", "    AND media_url != '' AND media_url is not NULL\n", "GROUP BY 1,2,3\n", "ORDER BY 4 DESC\n", "\"\"\"\n", "SAMPLE_SIZE = 1000\n", "\n", "\n", "DESTINATION_TABLE = \"sc-trends-prod.labeled_content.maps_stories_20250702\"\n", "RETENTION_DAYS = 30\n"]}, {"cell_type": "code", "execution_count": null, "id": "e11195af-7607-4b43-ba6d-e841e0cf7716", "metadata": {"tags": []}, "outputs": [], "source": ["sql = SQL + \" LIMIT {sample_size}\".format(sample_size=SAMPLE_SIZE)"]}, {"cell_type": "code", "execution_count": null, "id": "3045d2a4", "metadata": {}, "outputs": [], "source": ["data = utils.gbq.read_gbq(sql, \n", "                          project_id='myaigcp',\n", "                          dialect=\"standard\",\n", "                          priority = \"INTERACTIVE\")\n", "dataset = data['sample'].values"]}, {"cell_type": "code", "execution_count": null, "id": "bca1e3f0", "metadata": {}, "outputs": [], "source": ["# Initialize GCS client\n", "storage_client = storage.Client()\n", "bucket_name = BUCKET_NAME\n", "bucket_folder = BUCKET_FOLDER\n", "\n", "# Create bucket if it doesn't exist (usually not needed if bucket already exists)\n", "try:\n", "    bucket = storage_client.get_bucket(bucket_name)\n", "except Exception as e:\n", "    print(f\"Error accessing bucket: {e}\")\n", "    # You might want to create the bucket if it doesn't exist\n", "    # bucket = storage_client.create_bucket(bucket_name)\n", "\n", "# Function to download and upload a single file\n", "def download_and_upload(row):\n", "    url = row['sample']\n", "    story_id = row['sample_id']\n", "    \n", "    try:\n", "        # Download the file\n", "        response = requests.get(url, stream=True, timeout=30)\n", "        response.raise_for_status()\n", "        \n", "        # Determine content type and file extension\n", "        content_type = response.headers.get('content-type', 'application/octet-stream')\n", "        ext = \"\"\n", "        if 'video' in content_type:\n", "            ext = '.mp4'\n", "        elif 'image' in content_type:\n", "            ext = '.jpg' if 'jpeg' in content_type else '.png'\n", "        \n", "        # Create a temporary file to store the download\n", "        with tempfile.NamedTemporaryFile(delete=False) as temp_file:\n", "            for chunk in response.iter_content(chunk_size=8192):\n", "                temp_file.write(chunk)\n", "        \n", "        # Upload to GCS\n", "        blob_name = f\"{bucket_folder}/{story_id}{ext}\"\n", "        blob = bucket.blob(blob_name)\n", "        blob.upload_from_filename(temp_file.name, content_type=content_type)\n", "        \n", "        # Clean up temporary file\n", "        os.unlink(temp_file.name)\n", "        \n", "        # Return the GCS URL\n", "        return f\"gs://{bucket_name}/{blob_name}\"\n", "    \n", "    except Exception as e:\n", "        print(f\"Error processing {url} for story_id {story_id}: {e}\")\n", "        return None\n", "\n", "# Process all URLs with progress bar\n", "print(f\"Processing {len(data)} files...\")\n", "results = []\n", "\n", "# Using ThreadPoolExecutor for parallel processing\n", "with ThreadPoolExecutor(max_workers=10) as executor:\n", "    # Create a list to hold the futures\n", "    futures = [executor.submit(download_and_upload, {'sample': url, 'sample_id': data['sample_id'].iloc[i]}) \n", "               for i, url in enumerate(dataset)]\n", "    \n", "    # Process as they complete with a progress bar\n", "    for future in tqdm(futures, total=len(futures)):\n", "        results.append(future.result())\n", "\n", "# Add the GCS URLs to the dataframe\n", "data['gcs_url'] = results\n"]}, {"cell_type": "code", "execution_count": null, "id": "13c2f952", "metadata": {}, "outputs": [], "source": ["dataset_image = data[data['media_type'] == 'image']['gcs_url'].to_list()\n", "dataset_video = data[data['media_type'] == 'video']['gcs_url'].to_list()"]}, {"cell_type": "code", "execution_count": null, "id": "cb5c379f", "metadata": {}, "outputs": [], "source": ["model_parameters = {\"max_tokens\": MAX_TOKEN, 'temperature': 0}\n", "processor_config = {'processing_mode': 'image_url',\n", "                    'return_direct_url': True}\n", "\n", "if EXTRACTION_PROVIDER_NAME == \"gemini\" :\n", "    provider_config = { \"project_id\": \"myaigcp\",\n", "                        \"location\": \"us-central1\"}\n", "else: \n", "    provider_config = {\"service_account\": \"<EMAIL>\"}\n", "\n", "if EXTRACTION_PROVIDER_NAME == \"agi\":\n", "    model_parameters['disable_thinking'] = True\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "01b5c3c3", "metadata": {}, "outputs": [], "source": ["video_classification_model = Classification(provider_name = EXTRACTION_PROVIDER_NAME,\n", "                                     model_name = EXTRACTION_MODEL_NAME,\n", "                                     prompt = EXTRACTION_PROMPT,\n", "                                     provider_config= provider_config,\n", "                                     processor_config= processor_config,\n", "                                     model_parameters= model_parameters,\n", "                                     input_type = 'video',\n", "                                     )"]}, {"cell_type": "code", "execution_count": null, "id": "ef980f57", "metadata": {}, "outputs": [], "source": ["start_time = datetime.now()\n", "results_videos = thread_map(video_classification_model.classify, dataset_video, max_workers=MAX_WORKERS)\n", "print(\"Classification time: \", datetime.now()-start_time)"]}, {"cell_type": "code", "execution_count": null, "id": "dd5845b8", "metadata": {}, "outputs": [], "source": ["image_classification_model = Classification(provider_name = EXTRACTION_PROVIDER_NAME,\n", "                                     model_name = EXTRACTION_MODEL_NAME,\n", "                                     prompt = EXTRACTION_PROMPT,\n", "                                     provider_config=provider_config,\n", "                                     processor_config = processor_config,\n", "                                     model_parameters=model_parameters,\n", "                                     input_type = 'image',\n", "                                     )"]}, {"cell_type": "code", "execution_count": null, "id": "d674fd50", "metadata": {}, "outputs": [], "source": ["start_time = datetime.now()\n", "results_image = thread_map(image_classification_model.classify, dataset_image, max_workers=25)\n", "print(\"Classification time: \", datetime.now()-start_time)"]}, {"cell_type": "code", "execution_count": null, "id": "ea4d31eb", "metadata": {}, "outputs": [], "source": ["video_indices = data[data['media_type'] == 'video'].index\n", "for i, label in enumerate(results_videos):\n", "    if i < len(video_indices):\n", "        data.loc[video_indices[i], 'label'] = image_classification_model.get_result(label).lower()\n", "        data.loc[video_indices[i], 'completion_tokens'] = image_classification_model.get_token_usage(label)['completion_tokens'] \n", "        data.loc[video_indices[i], 'prompt_tokens'] = image_classification_model.get_token_usage(label)['prompt_tokens'] \n", "\n", "image_indices = data[data['media_type'] == 'image'].index\n", "for i, label in enumerate(results_image):\n", "    if i < len(image_indices):\n", "        data.loc[image_indices[i], 'label'] = image_classification_model.get_result(label).lower()\n", "        data.loc[image_indices[i], 'completion_tokens'] = image_classification_model.get_token_usage(label)['completion_tokens'] \n", "        data.loc[image_indices[i], 'prompt_tokens'] = image_classification_model.get_token_usage(label)['prompt_tokens'] "]}, {"cell_type": "code", "execution_count": null, "id": "228b3ab8", "metadata": {}, "outputs": [], "source": ["print(estimate_run_cost(data['prompt_tokens'], data['completion_tokens'], EXTRACTION_MODEL_NAME))"]}, {"cell_type": "code", "execution_count": null, "id": "f96777c2", "metadata": {}, "outputs": [], "source": ["scc_df = utils.gbq.read_gbq(\"\"\"SELECT * FROM `sc-analytics.cameos_ai.insights_snapchat_content_categories`\"\"\", \n", "                          project_id='myaigcp',\n", "                          dialect=\"standard\",\n", "                          priority = \"INTERACTIVE\")\n", "\n", "categories_list = scc_df[\"category\"].tolist()\n", "categories_set = set(categories_list) \n", "categories_string = \"\\n\".join(categories_set)"]}, {"cell_type": "code", "execution_count": null, "id": "f8b1a272", "metadata": {}, "outputs": [], "source": ["CLASSIFICATION_PROMPT = CLASSIFICATION_PROMPT + categories_string"]}, {"cell_type": "code", "execution_count": null, "id": "cf552552", "metadata": {}, "outputs": [], "source": ["model_parameters = {\"max_tokens\": MAX_TOKEN, 'temperature': 0}\n", "processor_config = {'processing_mode': 'image_url',\n", "                    'return_direct_url': True}\n", "\n", "if CLASSIFICATION_PROVIDER_NAME == \"gemini\":\n", "    provider_config = { \"project_id\": \"myaigcp\",\n", "                        \"location\": \"us-central1\"}\n", "    if CLASSIFICATION_PROVIDER_NAME == \"agi\":\n", "        model_parameters['disable_thinking'] = True\n", "else: \n", "    provider_config = {\"service_account\": \"<EMAIL>\"}\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "3b6cef3f-9e7a-43e4-9251-f836951718ec", "metadata": {"tags": []}, "outputs": [], "source": ["classification_model = Classification(provider_name = CLASSIFICATION_PROVIDER_NAME,\n", "                                     model_name = CLASSIFICATION_MODEL_NAME,\n", "                                     prompt = CLASSIFICATION_PROMPT,\n", "                                     provider_config=provider_config,\n", "                                     processor_config = processor_config,\n", "                                     model_parameters=model_parameters,\n", "                                     input_type = \"text\",\n", "                                     )"]}, {"cell_type": "code", "execution_count": null, "id": "9ecbbf00", "metadata": {}, "outputs": [], "source": ["dataset_labels = data['label'].to_list()"]}, {"cell_type": "code", "execution_count": null, "id": "d40658d2", "metadata": {}, "outputs": [], "source": ["start_time = datetime.now()\n", "cls_results = thread_map(classification_model.classify, dataset_labels, max_workers=MAX_WORKERS*10)\n", "print(\"Classification time: \", datetime.now()-start_time)"]}, {"cell_type": "code", "execution_count": null, "id": "197c6f83", "metadata": {}, "outputs": [], "source": ["data['label_cls'] = [classification_model.get_result(i).lower() for i in cls_results]\n", "data['completion_tokens_cls'] = [classification_model.get_token_usage(i)['completion_tokens'] for i in cls_results]\n", "data['prompt_tokens_cls'] = [classification_model.get_token_usage(i)['prompt_tokens'] for i in cls_results]\n", "\n", "data['model_name'] = MODEL_NAME\n", "data['sample_size'] = SAMPLE_SIZE"]}, {"cell_type": "code", "execution_count": null, "id": "a3cca7d3", "metadata": {}, "outputs": [], "source": ["categories_set = {category.lower() for category in categories_list}\n", "\n", "def filter_labels(labels_str):\n", "    if not isinstance(labels_str, str):\n", "        return ''\n", "    # Split the string, strip whitespace, and filter\n", "    filtered = set([label.strip() for label in labels_str.split(',') \n", "               if label.strip() in categories_set])\n", "    return ', '.join(filtered)\n", "\n", "# Apply the function to the label_cls column\n", "data['label_cls_filtered'] = data['label_cls'].apply(filter_labels)"]}, {"cell_type": "code", "execution_count": null, "id": "d20d6499-107a-4865-9339-75979fc8362a", "metadata": {"tags": []}, "outputs": [], "source": ["PROJECT ='myaigcp'\n", "\n", "DESTINATION_PROJECT, DESTINATION_DATASET, DESTINATION_TABLE_NAME = DESTINATION_TABLE.split('.')"]}, {"cell_type": "code", "execution_count": null, "id": "05704e73", "metadata": {}, "outputs": [], "source": ["## Upload results\n", "utils.gbq.to_gbq(\n", "                 data,\n", "                 project_id = PROJECT,\n", "                 dest_table_name = DESTINATION_TABLE_NAME,\n", "                 dest_dataset_id = DESTINATION_DATASET,\n", "                 dest_project_id = DESTINATION_PROJECT)\n", "\n", "print(f\"Results saved to {DESTINATION_PROJECT}.{DESTINATION_DATASET}.{DESTINATION_TABLE_NAME} table\")"]}, {"cell_type": "code", "execution_count": null, "id": "88442844", "metadata": {}, "outputs": [], "source": ["if RETENTION_DAYS:\n", "    client = bigquery.Client(project=DESTINATION_PROJECT)\n", "    table = client.get_table(f\"{DESTINATION_PROJECT}.{DESTINATION_DATASET}.{DESTINATION_TABLE_NAME}\")  \n", "    # Set the expiration time (e.g., 7 days from now)\n", "    \n", "    expiration_time = datetime.now(timezone.utc) + timedelta(days=RETENTION_DAYS)\n", "    table.expires = expiration_time\n", "    \n", "    # Update the table with new expiration time\n", "    client.update_table(table, [\"expires\"])  # Make an API request.\n", "    \n", "    print(f\"Table {table} expiration set to {expiration_time}\")"]}, {"cell_type": "code", "execution_count": null, "id": "1737bce3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"environment": {"kernel": "python3", "name": "tf2-cpu.2-11.m125", "type": "gcloud", "uri": "us-docker.pkg.dev/deeplearning-platform-release/gcr.io/tf2-cpu.2-11:m125"}, "kernelspec": {"display_name": "py310", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}