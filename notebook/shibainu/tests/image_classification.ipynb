{"cells": [{"cell_type": "code", "execution_count": 37, "id": "3c729f14-0ca7-497f-97c5-71c66b12de0e", "metadata": {}, "outputs": [], "source": ["from banjo.utils.shibainu import Classification, estimate_run_cost, configure_logger\n", "import logging\n", "configure_logger(level=logging.DEBUG)\n", "\n", "from banjo import utils\n", "from datetime import date, datetime, timedelta, timezone\n", "from tqdm.contrib.concurrent import thread_map\n"]}, {"cell_type": "code", "execution_count": 38, "id": "6239e389-fff9-4041-b716-38c656ac4c09", "metadata": {}, "outputs": [], "source": ["SERVICE_ACCOUNT = \"<EMAIL>\"\n", "PROJECT = 'myaigcp' \n", "PRIORITY = 'INTERACTIVE'\n", "LOCATION = \"us-west1\"\n", "\n", "SQL = \"SELECT image_url as samples FROM sc-bq-gcs-billingonly.dishchenko.bitmoji_images LIMIT 10\"\n", "PROMPT = \"\"\"You are an expert garment classifier. \n", "Analyze the provided 3D rendered image and determine the typical weather suitability of the garment based on its design, materials, and intended use.  \n", "Categories: All Year, Warm Weather, Cold Weather  \n", "\n", "Instructions: \n", "- Return only the applicable categories as a comma-separated list (e.g., Warm Weather, All Year). \n", "- Include multiple categories if appropriate. \n", "- Return an empty response if the category is unclear or if no garment is shown. \n", "- Return \"image_failure\" only if you are unable to download or view the image. \n", "- Do not include any explanations or extra text.\"\"\"\n", "\n", "INPUT_TYPE = \"image\""]}, {"cell_type": "markdown", "id": "343e54a7-ee65-4348-8f77-83b54c917553", "metadata": {}, "source": ["# Pull data"]}, {"cell_type": "code", "execution_count": 39, "id": "aadd517c-ecb0-4b5c-a605-56afe7e68f0b", "metadata": {}, "outputs": [], "source": ["data = utils.gbq.read_gbq(SQL, \n", "                          project_id=PROJECT,\n", "                          dialect=\"standard\",\n", "                          priority = PRIORITY)"]}, {"cell_type": "code", "execution_count": 40, "id": "d82852be-3a61-4aac-81cb-46552f8074f5", "metadata": {}, "outputs": [], "source": ["dataset = data.samples.values"]}, {"cell_type": "code", "execution_count": 41, "id": "d246daf2-61a2-4353-88dd-75d3dc99304a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['https://preview.bitmoji.com/avatar-builder-v3/preview/hat?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&hat=1375&'\n", " 'https://preview.bitmoji.com/avatar-builder-v3/preview/hat?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&hat=1376&'\n", " 'https://preview.bitmoji.com/avatar-builder-v3/preview/hat?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&hat=1377&'\n", " 'https://preview.bitmoji.com/avatar-builder-v3/preview/hat?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&hat=1378&'\n", " 'https://preview.bitmoji.com/avatar-builder-v3/preview/hat?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&hat=1379&'\n", " 'https://preview.bitmoji.com/avatar-builder-v3/preview/hat?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&hat=1380&'\n", " 'https://preview.bitmoji.com/avatar-builder-v3/preview/hat?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&hat=1381&'\n", " 'https://preview.bitmoji.com/avatar-builder-v3/preview/hat?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&hat=1382&'\n", " 'https://preview.bitmoji.com/avatar-builder-v3/preview/hat?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&hat=1383&'\n", " 'https://preview.bitmoji.com/avatar-builder-v3/preview/hat?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&hat=1384&']\n"]}], "source": ["print(dataset)"]}, {"cell_type": "markdown", "id": "30e6b1fd-7119-4e20-93c4-856b67384fff", "metadata": {}, "source": ["# OpenAI"]}, {"cell_type": "code", "execution_count": null, "id": "86b2d9ce-544a-42bf-a90e-75408869c4e4", "metadata": {}, "outputs": [], "source": ["PROVIDER_NAME = 'openai'"]}, {"cell_type": "markdown", "id": "c8b74af8-03b9-40a7-89ff-8e37e4514d13", "metadata": {}, "source": ["## GPT model"]}, {"cell_type": "code", "execution_count": null, "id": "6e26ed3e-fdf3-4038-80c4-49032afb7fec", "metadata": {}, "outputs": [], "source": ["classification_model = Classification(provider_name = PROVIDER_NAME,\n", "                                     model_name = 'gpt-4o-mini',\n", "                                     prompt = PROMPT,\n", "                                     provider_config={\n", "                                        \"service_account\": SERVICE_ACCOUNT},\n", "                                     processor_config = {'processing_mode': 'image_url'},\n", "                                     model_parameters={'temperature': 0, 'max_token': 10},\n", "                                     input_type = INPUT_TYPE,\n", "                                     )"]}, {"cell_type": "code", "execution_count": null, "id": "e44113ff-62f9-4cde-86ce-67ced3919867", "metadata": {}, "outputs": [], "source": ["result = classification_model.send_message(\"Say: 'The model {model_name} is alive' \". format(model_name = 'gpt-4o-mini'))\n", "print(classification_model.get_result(result))"]}, {"cell_type": "code", "execution_count": null, "id": "3dfc342b-727c-40a8-b0d4-6fe12ae98af8", "metadata": {}, "outputs": [], "source": ["start_time = datetime.now()\n", "results = thread_map(classification_model.classify, dataset, max_workers=5)\n", "print(\"Classification time: \", datetime.now()-start_time)"]}, {"cell_type": "code", "execution_count": null, "id": "f91fad47-29fc-4283-8b8e-d1b7375b97c2", "metadata": {}, "outputs": [], "source": ["data['label'] = [classification_model.get_result(i).lower() for i in results]\n", "data['completion_tokens'] = [classification_model.get_token_usage(i)['completion_tokens'] for i in results]\n", "data['prompt_tokens'] = [classification_model.get_token_usage(i)['prompt_tokens'] for i in results]"]}, {"cell_type": "code", "execution_count": null, "id": "987a554d-8052-4a2b-9f6d-d6b5969fa93a", "metadata": {}, "outputs": [], "source": ["data.groupby('label').count()"]}, {"cell_type": "code", "execution_count": null, "id": "647da738-91a2-4567-b083-8a4050cf0f5d", "metadata": {}, "outputs": [], "source": ["estimate_run_cost(data['completion_tokens'], data['prompt_tokens'], 'gpt-4o-mini')"]}, {"cell_type": "markdown", "id": "c55e267b-b84d-4def-82ac-78306c40c68e", "metadata": {}, "source": ["## O thinking model\n", "## <span style=\"color:red\">It has to fail for images</span>.\n"]}, {"cell_type": "code", "execution_count": null, "id": "a397f71d-cfb1-4c25-a2c5-875c26b3c2be", "metadata": {}, "outputs": [], "source": ["classification_model = Classification(provider_name = PROVIDER_NAME,\n", "                                     model_name = 'o1-mini',\n", "                                     prompt = PROMPT,\n", "                                     provider_config={\n", "                                        \"service_account\": SERVICE_ACCOUNT},\n", "                                     processor_config = None,\n", "                                     model_parameters={'temperature': 0, 'max_token': 10},\n", "                                     input_type = INPUT_TYPE,\n", "                                     )"]}, {"cell_type": "code", "execution_count": null, "id": "85ee112d-3135-40b7-9188-df2e3c3aa453", "metadata": {}, "outputs": [], "source": ["result = classification_model.send_message(\"Say: 'The model {model_name} is alive' \". format(model_name = 'o1-mini'))\n", "print(classification_model.get_result(result))"]}, {"cell_type": "code", "execution_count": null, "id": "bd01179b-3b30-44c4-a4d3-64396deb32a1", "metadata": {}, "outputs": [], "source": ["start_time = datetime.now()\n", "results = thread_map(classification_model.classify, dataset, max_workers=5)\n", "print(\"Classification time: \", datetime.now()-start_time)"]}, {"cell_type": "code", "execution_count": null, "id": "798b2001-d4ec-46e0-a8f9-ff21ed0c73f8", "metadata": {}, "outputs": [], "source": ["data['label_o1'] = [classification_model.get_result(i).lower() for i in results]\n", "data['completion_tokens_o1'] = [classification_model.get_token_usage(i)['completion_tokens'] for i in results]\n", "data['prompt_tokens_o1'] = [classification_model.get_token_usage(i)['prompt_tokens'] for i in results]"]}, {"cell_type": "code", "execution_count": null, "id": "1e1dee5b-7cc7-4fb1-a1dd-64817fa5892c", "metadata": {}, "outputs": [], "source": ["data.groupby('label_o1').count()"]}, {"cell_type": "code", "execution_count": null, "id": "006f189e-dd5b-4bf2-b468-ab3d69244bb7", "metadata": {}, "outputs": [], "source": ["estimate_run_cost(data['completion_tokens_o1'], data['prompt_tokens_o1'], 'o1-mini')"]}, {"cell_type": "markdown", "id": "b5902d2b-3e0b-4b38-8dc4-6edec1acf6ea", "metadata": {}, "source": ["# Gemini"]}, {"cell_type": "code", "execution_count": 42, "id": "6b3f5d84-2ea3-44a9-bb22-4e916e5307b5", "metadata": {}, "outputs": [], "source": ["PROVIDER_NAME = 'gemini'"]}, {"cell_type": "markdown", "id": "59cc1db9-f233-42ae-bd77-1b27042c19b5", "metadata": {}, "source": ["## Flash lite not-thinking"]}, {"cell_type": "code", "execution_count": 43, "id": "5a6372f4-a229-4ca3-9015-4606e83eb10c", "metadata": {}, "outputs": [], "source": ["MODEL_NAME = 'gemini-2.0-flash'"]}, {"cell_type": "code", "execution_count": 44, "id": "90811795-1437-413c-a62f-cedebffdc772", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-06-20 14:27:53,397 - shibainu_v2 - DEBUG - Creating processor for input type: image\n", "2025-06-20 14:27:53,401 - shibainu_v2 - DEBUG - Initialized ImageProcessor with processing_mode=image_url\n"]}], "source": ["classification_model = Classification(provider_name = PROVIDER_NAME,\n", "                                     model_name = MODEL_NAME,\n", "                                     prompt = PROMPT,\n", "                                     provider_config={\n", "                                        \"project_id\": PROJECT,\n", "                                        \"location\": LOCATION\n", "                                    },\n", "                                     processor_config = None,\n", "                                     model_parameters={'temperature': 0, 'max_token': 100},\n", "                                     input_type = INPUT_TYPE,\n", "                                     )"]}, {"cell_type": "code", "execution_count": 45, "id": "5f8ddf8b-4683-46c7-8596-adde1734aae3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-06-20 14:27:53,998 - shiba<PERSON>u_v2 - DEBUG - Sending text message to <PERSON>: Say: 'The model gemini-2.0-flash is alive' \n", "2025-06-20 14:27:54,007 - shibainu_v2 - DEBUG - Using generation config: {\"max_output_tokens\": 100, \"temperature\": 0}\n", "2025-06-20 14:27:54,807 - shibainu_v2 - DEBUG - Extracted result from Gemini response: The model gemini-2.0-flash is alive.\n", "The model gemini-2.0-flash is alive.\n"]}], "source": ["result = classification_model.send_message(\"Say: 'The model {model_name} is alive' \". format(model_name = 'gemini-2.0-flash'))\n", "print(classification_model.get_result(result))"]}, {"cell_type": "code", "execution_count": 46, "id": "8be771fa-0494-4463-a5f0-60b8ccbd89e3", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-06-20 14:27:55,281 - shibainu_v2 - INFO - Preparing image data with processing_mode=image_url\n", "2025-06-20 14:27:55,283 - shibainu_v2 - INFO - Preparing image data with processing_mode=image_url\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "30163877de454850bd6b106afa75d94b", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["2025-06-20 14:27:55,285 - shibainu_v2 - INFO - Preparing image data with processing_mode=image_url\n", "2025-06-20 14:27:55,287 - shibainu_v2 - INFO - Preparing image data with processing_mode=image_url\n", "2025-06-20 14:27:55,287 - shibainu_v2 - INFO - Preparing image data with processing_mode=image_url\n", "2025-06-20 14:27:55,300 - shibainu_v2 - DEBUG - Processing image data with GeminiProvider\n", "2025-06-20 14:27:55,303 - shibainu_v2 - DEBUG - Processing image data with GeminiProvider\n", "2025-06-20 14:27:55,305 - shibainu_v2 - DEBUG - Processing image data with GeminiProvider\n", "2025-06-20 14:27:55,308 - shibainu_v2 - DEBUG - Re-initializing Gemini model with system prompt: You are an expert garment classifier. \n", "Analyze the provided 3D rendered image and determine the typi...\n", "2025-06-20 14:27:55,306 - shibainu_v2 - DEBUG - Processing image data with GeminiProvider\n", "2025-06-20 14:27:55,309 - shibainu_v2 - DEBUG - Re-initializing Gemini model with system prompt: You are an expert garment classifier. \n", "Analyze the provided 3D rendered image and determine the typi...\n", "2025-06-20 14:27:55,307 - shibainu_v2 - DEBUG - Re-initializing Gemini model with system prompt: You are an expert garment classifier. \n", "Analyze the provided 3D rendered image and determine the typi...\n", "2025-06-20 14:27:55,306 - shibainu_v2 - DEBUG - Processing image data with GeminiProvider\n", "2025-06-20 14:27:55,309 - shibainu_v2 - DEBUG - Processing ['https://preview.bitmoji.com/avatar-builder-v3/preview/hat?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&hat=1377&'] with GeminiProvider\n", "2025-06-20 14:27:55,307 - shibainu_v2 - DEBUG - Re-initializing Gemini model with system prompt: You are an expert garment classifier. \n", "Analyze the provided 3D rendered image and determine the typi...\n", "2025-06-20 14:27:55,309 - shibainu_v2 - DEBUG - Processing ['https://preview.bitmoji.com/avatar-builder-v3/preview/hat?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&hat=1379&'] with GeminiProvider\n", "2025-06-20 14:27:55,310 - shibainu_v2 - DEBUG - Processing ['https://preview.bitmoji.com/avatar-builder-v3/preview/hat?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&hat=1376&'] with GeminiProvider\n", "2025-06-20 14:27:55,310 - shibainu_v2 - DEBUG - Re-initializing Gemini model with system prompt: You are an expert garment classifier. \n", "Analyze the provided 3D rendered image and determine the typi...\n", "2025-06-20 14:27:55,310 - shibainu_v2 - DEBUG - Sending content list to Gemini with 1 parts\n", "2025-06-20 14:27:55,311 - shiba<PERSON>u_v2 - DEBUG - Processing ['https://preview.bitmoji.com/avatar-builder-v3/preview/hat?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&hat=1375&'] with GeminiProvider\n", "2025-06-20 14:27:55,311 - shibainu_v2 - DEBUG - Sending content list to Gemini with 1 parts\n", "2025-06-20 14:27:55,312 - shibainu_v2 - DEBUG - Sending content list to Gemini with 1 parts\n", "2025-06-20 14:27:55,312 - shibainu_v2 - DEBUG - Processing ['https://preview.bitmoji.com/avatar-builder-v3/preview/hat?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&hat=1378&'] with GeminiProvider\n", "2025-06-20 14:27:55,313 - shiba<PERSON>u_v2 - DEBUG -   Part 0: https://preview.bitmoji.com/avatar-builder-v3/prev...\n", "2025-06-20 14:27:55,313 - shibainu_v2 - DEBUG - Sending content list to Gemini with 1 parts\n", "2025-06-20 14:27:55,314 - shibainu_v2 - DEBUG -   Part 0: https://preview.bitmoji.com/avatar-builder-v3/prev...\n", "2025-06-20 14:27:55,314 - shibainu_v2 - DEBUG -   Part 0: https://preview.bitmoji.com/avatar-builder-v3/prev...\n", "2025-06-20 14:27:55,314 - shibainu_v2 - DEBUG - Sending content list to Gemini with 1 parts\n", "2025-06-20 14:27:55,314 - shibainu_v2 - DEBUG - Using generation config: {\"max_output_tokens\": 100, \"temperature\": 0}\n", "2025-06-20 14:27:55,315 - shiba<PERSON>u_v2 - DEBUG -   Part 0: https://preview.bitmoji.com/avatar-builder-v3/prev...\n", "2025-06-20 14:27:55,315 - shibainu_v2 - DEBUG - Using generation config: {\"max_output_tokens\": 100, \"temperature\": 0}\n", "2025-06-20 14:27:55,316 - shibainu_v2 - DEBUG - Using generation config: {\"max_output_tokens\": 100, \"temperature\": 0}\n", "2025-06-20 14:27:55,316 - shibainu_v2 - DEBUG -   Part 0: https://preview.bitmoji.com/avatar-builder-v3/prev...\n", "2025-06-20 14:27:55,318 - shibainu_v2 - DEBUG - Using generation config: {\"max_output_tokens\": 100, \"temperature\": 0}\n", "2025-06-20 14:27:55,321 - shibainu_v2 - DEBUG - Using generation config: {\"max_output_tokens\": 100, \"temperature\": 0}\n", "2025-06-20 14:27:55,734 - shibainu_v2 - INFO - Preparing image data with processing_mode=image_url\n", "2025-06-20 14:27:55,739 - shibainu_v2 - INFO - Preparing image data with processing_mode=image_url\n", "2025-06-20 14:27:55,745 - shibainu_v2 - DEBUG - Processing image data with GeminiProvider\n", "2025-06-20 14:27:55,746 - shibainu_v2 - DEBUG - Re-initializing Gemini model with system prompt: You are an expert garment classifier. \n", "Analyze the provided 3D rendered image and determine the typi...\n", "2025-06-20 14:27:55,749 - shibainu_v2 - DEBUG - Processing ['https://preview.bitmoji.com/avatar-builder-v3/preview/hat?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&hat=1381&'] with GeminiProvider\n", "2025-06-20 14:27:55,753 - shibainu_v2 - DEBUG - Sending content list to Gemini with 1 parts\n", "2025-06-20 14:27:55,741 - shibainu_v2 - DEBUG - Processing image data with GeminiProvider\n", "2025-06-20 14:27:55,742 - shibainu_v2 - INFO - Preparing image data with processing_mode=image_url\n", "2025-06-20 14:27:55,751 - shibainu_v2 - INFO - Preparing image data with processing_mode=image_url\n", "2025-06-20 14:27:55,740 - shibainu_v2 - INFO - Preparing image data with processing_mode=image_url\n", "2025-06-20 14:27:55,754 - shibainu_v2 - DEBUG -   Part 0: https://preview.bitmoji.com/avatar-builder-v3/prev...\n", "2025-06-20 14:27:55,756 - shibainu_v2 - DEBUG - Re-initializing Gemini model with system prompt: You are an expert garment classifier. \n", "Analyze the provided 3D rendered image and determine the typi...\n", "2025-06-20 14:27:55,756 - shibainu_v2 - DEBUG - Processing image data with GeminiProvider\n", "2025-06-20 14:27:55,757 - shibainu_v2 - DEBUG - Processing image data with GeminiProvider\n", "2025-06-20 14:27:55,758 - shibainu_v2 - DEBUG - Processing image data with GeminiProvider\n", "2025-06-20 14:27:55,759 - shibainu_v2 - DEBUG - Using generation config: {\"max_output_tokens\": 100, \"temperature\": 0}\n", "2025-06-20 14:27:55,759 - shibainu_v2 - DEBUG - Processing ['https://preview.bitmoji.com/avatar-builder-v3/preview/hat?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&hat=1380&'] with GeminiProvider\n", "2025-06-20 14:27:55,760 - shibainu_v2 - DEBUG - Re-initializing Gemini model with system prompt: You are an expert garment classifier. \n", "Analyze the provided 3D rendered image and determine the typi...\n", "2025-06-20 14:27:55,761 - shibainu_v2 - DEBUG - Re-initializing Gemini model with system prompt: You are an expert garment classifier. \n", "Analyze the provided 3D rendered image and determine the typi...\n", "2025-06-20 14:27:55,761 - shibainu_v2 - DEBUG - Re-initializing Gemini model with system prompt: You are an expert garment classifier. \n", "Analyze the provided 3D rendered image and determine the typi...\n", "2025-06-20 14:27:55,764 - shibainu_v2 - DEBUG - Sending content list to Gemini with 1 parts\n", "2025-06-20 14:27:55,764 - shibainu_v2 - DEBUG - Processing ['https://preview.bitmoji.com/avatar-builder-v3/preview/hat?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&hat=1383&'] with GeminiProvider\n", "2025-06-20 14:27:55,765 - shibainu_v2 - DEBUG - Processing ['https://preview.bitmoji.com/avatar-builder-v3/preview/hat?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&hat=1384&'] with GeminiProvider\n", "2025-06-20 14:27:55,766 - shibainu_v2 - DEBUG - Processing ['https://preview.bitmoji.com/avatar-builder-v3/preview/hat?scale=2&style=5&ua=2&clothing_type=1&version=0&gender=2&hat=1382&'] with GeminiProvider\n", "2025-06-20 14:27:55,767 - shibainu_v2 - DEBUG -   Part 0: https://preview.bitmoji.com/avatar-builder-v3/prev...\n", "2025-06-20 14:27:55,768 - shibainu_v2 - DEBUG - Sending content list to Gemini with 1 parts\n", "2025-06-20 14:27:55,768 - shibainu_v2 - DEBUG - Sending content list to Gemini with 1 parts\n", "2025-06-20 14:27:55,769 - shibainu_v2 - DEBUG - Sending content list to Gemini with 1 parts\n", "2025-06-20 14:27:55,770 - shibainu_v2 - DEBUG - Using generation config: {\"max_output_tokens\": 100, \"temperature\": 0}\n", "2025-06-20 14:27:55,770 - shibainu_v2 - DEBUG -   Part 0: https://preview.bitmoji.com/avatar-builder-v3/prev...\n", "2025-06-20 14:27:55,771 - shibainu_v2 - DEBUG -   Part 0: https://preview.bitmoji.com/avatar-builder-v3/prev...\n", "2025-06-20 14:27:55,771 - shibainu_v2 - DEBUG -   Part 0: https://preview.bitmoji.com/avatar-builder-v3/prev...\n", "2025-06-20 14:27:55,774 - shibainu_v2 - DEBUG - Using generation config: {\"max_output_tokens\": 100, \"temperature\": 0}\n", "2025-06-20 14:27:55,774 - shibainu_v2 - DEBUG - Using generation config: {\"max_output_tokens\": 100, \"temperature\": 0}\n", "2025-06-20 14:27:55,775 - shibainu_v2 - DEBUG - Using generation config: {\"max_output_tokens\": 100, \"temperature\": 0}\n", "Classification time:  0:00:00.988610\n"]}], "source": ["start_time = datetime.now()\n", "results = thread_map(classification_model.classify, dataset, max_workers=5)\n", "print(\"Classification time: \", datetime.now()-start_time)"]}, {"cell_type": "code", "execution_count": 47, "id": "1e99b03c-8c83-4a45-a728-7b90eeccf94c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-06-20 14:27:56,277 - shibainu_v2 - DEBUG - Extracted result from Gemini response: Cold Weather\n", "2025-06-20 14:27:56,278 - shi<PERSON><PERSON>u_v2 - DEBUG - Extracted result from Gemini response: Cold Weather\n", "2025-06-20 14:27:56,278 - shi<PERSON><PERSON>u_v2 - DEBUG - Extracted result from Gemini response: Cold Weather\n", "2025-06-20 14:27:56,278 - shi<PERSON><PERSON>u_v2 - DEBUG - Extracted result from Gemini response: Cold Weather\n", "2025-06-20 14:27:56,279 - shibainu_v2 - DEBUG - Extracted result from Gemini response: Cold Weather\n", "2025-06-20 14:27:56,279 - shibainu_v2 - DEBUG - Extracted result from Gemini response: Cold Weather\n", "2025-06-20 14:27:56,280 - shibainu_v2 - DEBUG - Extracted result from Gemini response: Cold Weather\n", "2025-06-20 14:27:56,280 - shibainu_v2 - DEBUG - Extracted result from Gemini response: Cold Weather\n", "2025-06-20 14:27:56,280 - shibainu_v2 - DEBUG - Extracted result from Gemini response: Cold Weather\n", "2025-06-20 14:27:56,281 - s<PERSON><PERSON><PERSON>u_v2 - DEBUG - Extracted result from Gemini response: Cold Weather\n"]}], "source": ["data['label'] = [classification_model.get_result(i).lower() for i in results]\n", "data['completion_tokens'] = [classification_model.get_token_usage(i)['completion_tokens'] for i in results]\n", "data['prompt_tokens'] = [classification_model.get_token_usage(i)['prompt_tokens'] for i in results]"]}, {"cell_type": "code", "execution_count": null, "id": "28079d32-9d46-4f58-b86b-61f615d42a90", "metadata": {}, "outputs": [], "source": ["data.groupby('label').count()"]}, {"cell_type": "code", "execution_count": null, "id": "ea51997d-0a52-45d6-9048-5d128ad158c0", "metadata": {}, "outputs": [], "source": ["estimate_run_cost(data['completion_tokens'], data['prompt_tokens'], MODEL_NAME)"]}, {"cell_type": "markdown", "id": "837bdb53-f239-4d01-b89e-2fa02f874116", "metadata": {}, "source": ["## Pro thinking"]}, {"cell_type": "code", "execution_count": null, "id": "0b90710b-7493-46cf-b1f6-257db26545a4", "metadata": {}, "outputs": [], "source": ["MODEL_NAME = 'gemini-2.5-pro'"]}, {"cell_type": "code", "execution_count": null, "id": "46c8a8f5-1736-41dd-9616-76f6518b971d", "metadata": {}, "outputs": [], "source": ["classification_model = Classification(provider_name = PROVIDER_NAME,\n", "                                     model_name = MODEL_NAME,\n", "                                     prompt = PROMPT,\n", "                                     provider_config={\n", "                                        \"project_id\": PROJECT,\n", "                                        \"location\": LOCATION\n", "                                    },\n", "                                     processor_config = None,\n", "                                     model_parameters={'temperature': 0, 'max_token': 5000},\n", "                                     input_type = INPUT_TYPE,\n", "                                     )"]}, {"cell_type": "code", "execution_count": null, "id": "37964bf7-43ba-4ea2-8975-c75b9cb833a9", "metadata": {}, "outputs": [], "source": ["result = classification_model.send_message(\"Say: 'The model {model_name} is alive' \". format(model_name = MODEL_NAME))\n", "print(classification_model.get_result(result))"]}, {"cell_type": "code", "execution_count": null, "id": "bb81fc70-817c-46a1-bdf1-c542dd1c25fc", "metadata": {"scrolled": true}, "outputs": [], "source": ["start_time = datetime.now()\n", "results = thread_map(classification_model.classify, dataset, max_workers=5)\n", "print(\"Classification time: \", datetime.now()-start_time)"]}, {"cell_type": "code", "execution_count": null, "id": "7a0449b2-f32b-499f-b718-9ea09569b0f5", "metadata": {}, "outputs": [], "source": ["data.groupby('label').count()"]}, {"cell_type": "code", "execution_count": null, "id": "97ac34a4-59a5-4848-95f3-53b90e25cfab", "metadata": {}, "outputs": [], "source": ["estimate_run_cost(data['completion_tokens'], data['prompt_tokens'], MODEL_NAME)"]}, {"cell_type": "code", "execution_count": null, "id": "1b23701d-9d05-4825-8e17-4df3c19abe24", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.19"}}, "nbformat": 4, "nbformat_minor": 5}