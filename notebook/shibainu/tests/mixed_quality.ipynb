{"cells": [{"cell_type": "code", "execution_count": null, "id": "3c729f14-0ca7-497f-97c5-71c66b12de0e", "metadata": {}, "outputs": [], "source": ["from banjo.utils.shibainu import Classification, estimate_run_cost, configure_logger\n", "import logging\n", "configure_logger(level=logging.DEBUG)\n", "\n", "from banjo import utils\n", "from datetime import date, datetime, timedelta, timezone\n", "from tqdm.contrib.concurrent import thread_map\n"]}, {"cell_type": "code", "execution_count": null, "id": "6239e389-fff9-4041-b716-38c656ac4c09", "metadata": {}, "outputs": [], "source": ["SERVICE_ACCOUNT = \"<EMAIL>\"\n", "PROJECT = 'myaigcp' \n", "PRIORITY = 'INTERACTIVE'\n", "LOCATION = \"us-west1\"\n", "\n", "SQL = \"\"\"SELECT \n", "  acumen_text as text,\n", "  acumen_video_url as url,\n", "FROM `snap-search-v2.content_eval.CONTENT_SEARCH_RANKING__136494_32_20240417` \n", "WHERE acumen_video_url <> ''\n", "limit 10\n", "\"\"\"\n", "PROMPT = \"\"\"You are quality evaluation model. Your goal for provided text and following images, identify if text relevant to these images\n", "When evaluating videos, imagine you are searching on a major social media platform with a specific goal in mind—whether to be entertained, to learn, to find timely news, or find something practical. Consider both how well the video matches your query and how engaging or useful it is. The evaluation label should be one of the 5 categories: Irrelevant, Somewhat Relevant,  Relevant, Relevant and Engaging, Relevant and Delightful.\n", "See the detailed descriptions of each category below.\n", "Irrelevant: The video is unrelated to the query or has only a very loose connection to the query. For example, a query about \"tennis\" returns a highly entertaining video of someone juggling a tennis ball, but nothing else about the sport tennis. \n", "Somewhat Relevant: The video has some relevance to the query but isn't the primary content you’d expect. For instance, searching for \"basketball\" and the video is an interview with <PERSON>. \n", "Relevant: The video aligns with the query but is neither engaging nor timely. It may be low quality or even a deep fake. While the content matches the query very well, it’s not something you’d find useful or exciting. You feel disappointed with the result and continue searching, as it doesn’t fully meet your expectations.\n", "Relevant and Engaging: The video aligns with the query and provides additional utility to the viewer, such as entertainment, timely news, or practical advice. However, it is not a video worth rewatching or sharing with friends. \n", "Relevant and Delightful: The video aligns with the query and is engaging, entertaining, or useful enough that a viewer would likely recommend or rewatch it. It’s the kind of content people would enjoy, share with friends, or find especially satisfying when searching for this topic. For example, a user searching for \"cooking tips\" finds a video with tips they’re excited to try.\n", "\n", "Answer only with a category with the same spelling as provided.\n", "\"\"\"\n", "\n", "INPUT_TYPE = \"mixed\"\n", "\n"]}, {"cell_type": "markdown", "id": "343e54a7-ee65-4348-8f77-83b54c917553", "metadata": {}, "source": ["# Pull data"]}, {"cell_type": "code", "execution_count": null, "id": "aadd517c-ecb0-4b5c-a605-56afe7e68f0b", "metadata": {}, "outputs": [], "source": ["data = utils.gbq.read_gbq(SQL, \n", "                          project_id=PROJECT,\n", "                          dialect=\"standard\",\n", "                          priority = PRIORITY)"]}, {"cell_type": "code", "execution_count": null, "id": "d82852be-3a61-4aac-81cb-46552f8074f5", "metadata": {}, "outputs": [], "source": ["dataset = []\n", "for index, row in data.iterrows():\n", "    sample = [{'type': \"text\", \"data\": row['text']}, {'type': \"video\", \"data\": row['url']}]\n", "    dataset.append(sample)"]}, {"cell_type": "code", "execution_count": null, "id": "d246daf2-61a2-4353-88dd-75d3dc99304a", "metadata": {}, "outputs": [], "source": ["print(dataset)"]}, {"cell_type": "markdown", "id": "30e6b1fd-7119-4e20-93c4-856b67384fff", "metadata": {}, "source": ["# OpenAI"]}, {"cell_type": "code", "execution_count": null, "id": "86b2d9ce-544a-42bf-a90e-75408869c4e4", "metadata": {}, "outputs": [], "source": ["PROVIDER_NAME = 'openai'\n", "\n", "processor_config = {\n", "    'sampling_mode': 'fps',\n", "    'sampling_value': 1.0,\n", "    'max_frames': 20,\n", "    'download': True,\n", "    'processing_mode': 'bytes'\n", "}"]}, {"cell_type": "markdown", "id": "c8b74af8-03b9-40a7-89ff-8e37e4514d13", "metadata": {}, "source": ["## GPT model"]}, {"cell_type": "code", "execution_count": null, "id": "6e26ed3e-fdf3-4038-80c4-49032afb7fec", "metadata": {}, "outputs": [], "source": ["classification_model = Classification(provider_name = PROVIDER_NAME,\n", "                                     model_name = 'gpt-4o-mini',\n", "                                     prompt = PROMPT,\n", "                                     provider_config={\n", "                                        \"service_account\": SERVICE_ACCOUNT},\n", "                                     processor_config = processor_config,\n", "                                     model_parameters={'temperature': 0, 'max_token': 10},\n", "                                     input_type = INPUT_TYPE,\n", "                                     )"]}, {"cell_type": "code", "execution_count": null, "id": "e44113ff-62f9-4cde-86ce-67ced3919867", "metadata": {}, "outputs": [], "source": ["result = classification_model.send_message(\"Say: 'The model {model_name} is alive' \". format(model_name = 'gpt-4o-mini'))\n", "print(classification_model.get_result(result))"]}, {"cell_type": "code", "execution_count": null, "id": "3dfc342b-727c-40a8-b0d4-6fe12ae98af8", "metadata": {"scrolled": true}, "outputs": [], "source": ["start_time = datetime.now()\n", "results = thread_map(classification_model.classify, dataset, max_workers=5)\n", "print(\"Classification time: \", datetime.now()-start_time)"]}, {"cell_type": "code", "execution_count": null, "id": "f91fad47-29fc-4283-8b8e-d1b7375b97c2", "metadata": {}, "outputs": [], "source": ["data['label'] = [classification_model.get_result(i).lower() for i in results]\n", "data['completion_tokens'] = [classification_model.get_token_usage(i)['completion_tokens'] for i in results]\n", "data['prompt_tokens'] = [classification_model.get_token_usage(i)['prompt_tokens'] for i in results]"]}, {"cell_type": "code", "execution_count": null, "id": "987a554d-8052-4a2b-9f6d-d6b5969fa93a", "metadata": {}, "outputs": [], "source": ["data.groupby('label').count()"]}, {"cell_type": "code", "execution_count": null, "id": "647da738-91a2-4567-b083-8a4050cf0f5d", "metadata": {}, "outputs": [], "source": ["estimate_run_cost(data['completion_tokens'], data['prompt_tokens'], 'gpt-4o-mini')"]}, {"cell_type": "markdown", "id": "c55e267b-b84d-4def-82ac-78306c40c68e", "metadata": {}, "source": ["## O thinking model\n", "## <span style=\"color:red\">It has to fail for videos</span>.\n"]}, {"cell_type": "code", "execution_count": null, "id": "a397f71d-cfb1-4c25-a2c5-875c26b3c2be", "metadata": {}, "outputs": [], "source": ["classification_model = Classification(provider_name = PROVIDER_NAME,\n", "                                     model_name = 'o1-mini',\n", "                                     prompt = PROMPT,\n", "                                     provider_config={\n", "                                        \"service_account\": SERVICE_ACCOUNT},\n", "                                     processor_config = processor_config,\n", "                                     model_parameters={'temperature': 0, 'max_token': 10},\n", "                                     input_type = INPUT_TYPE,\n", "                                     )"]}, {"cell_type": "code", "execution_count": null, "id": "85ee112d-3135-40b7-9188-df2e3c3aa453", "metadata": {}, "outputs": [], "source": ["result = classification_model.send_message(\"Say: 'The model {model_name} is alive' \". format(model_name = 'o1-mini'))\n", "print(classification_model.get_result(result))"]}, {"cell_type": "code", "execution_count": null, "id": "bd01179b-3b30-44c4-a4d3-64396deb32a1", "metadata": {"scrolled": true}, "outputs": [], "source": ["start_time = datetime.now()\n", "results = thread_map(classification_model.classify, dataset, max_workers=5)\n", "print(\"Classification time: \", datetime.now()-start_time)"]}, {"cell_type": "code", "execution_count": null, "id": "798b2001-d4ec-46e0-a8f9-ff21ed0c73f8", "metadata": {}, "outputs": [], "source": ["data['label_o1'] = [classification_model.get_result(i).lower() for i in results]\n", "data['completion_tokens_o1'] = [classification_model.get_token_usage(i)['completion_tokens'] for i in results]\n", "data['prompt_tokens_o1'] = [classification_model.get_token_usage(i)['prompt_tokens'] for i in results]"]}, {"cell_type": "code", "execution_count": null, "id": "1e1dee5b-7cc7-4fb1-a1dd-64817fa5892c", "metadata": {}, "outputs": [], "source": ["data.groupby('label_o1').count()"]}, {"cell_type": "code", "execution_count": null, "id": "006f189e-dd5b-4bf2-b468-ab3d69244bb7", "metadata": {}, "outputs": [], "source": ["estimate_run_cost(data['completion_tokens_o1'], data['prompt_tokens_o1'], 'o1-mini')"]}, {"cell_type": "markdown", "id": "b5902d2b-3e0b-4b38-8dc4-6edec1acf6ea", "metadata": {}, "source": ["# Gemini"]}, {"cell_type": "code", "execution_count": null, "id": "6b3f5d84-2ea3-44a9-bb22-4e916e5307b5", "metadata": {}, "outputs": [], "source": ["PROVIDER_NAME = 'gemini'\n", "\n", "processor_config = {\n", "    \"processing_mode\": \"image_url\",\n", "    \"return_direct_url\": True,\n", "}"]}, {"cell_type": "markdown", "id": "59cc1db9-f233-42ae-bd77-1b27042c19b5", "metadata": {}, "source": ["## Flash lite not-thinking"]}, {"cell_type": "code", "execution_count": null, "id": "5a6372f4-a229-4ca3-9015-4606e83eb10c", "metadata": {}, "outputs": [], "source": ["MODEL_NAME = 'gemini-2.0-flash'"]}, {"cell_type": "code", "execution_count": null, "id": "90811795-1437-413c-a62f-cedebffdc772", "metadata": {}, "outputs": [], "source": ["classification_model = Classification(provider_name = PROVIDER_NAME,\n", "                                     model_name = MODEL_NAME,\n", "                                     prompt = PROMPT,\n", "                                     provider_config={\n", "                                        \"project_id\": PROJECT,\n", "                                        \"location\": LOCATION\n", "                                    },\n", "                                     processor_config = processor_config,\n", "                                     model_parameters={'temperature': 0, 'max_token': 100},\n", "                                     input_type = INPUT_TYPE,\n", "                                     )"]}, {"cell_type": "code", "execution_count": null, "id": "5f8ddf8b-4683-46c7-8596-adde1734aae3", "metadata": {}, "outputs": [], "source": ["result = classification_model.send_message(\"Say: 'The model {model_name} is alive' \". format(model_name = 'gemini-2.0-flash'))\n", "print(classification_model.get_result(result))"]}, {"cell_type": "code", "execution_count": null, "id": "494c7733-1af9-440a-8181-6901eb0ab44e", "metadata": {}, "outputs": [], "source": ["classification_model.classify(dataset[0])"]}, {"cell_type": "code", "execution_count": null, "id": "8be771fa-0494-4463-a5f0-60b8ccbd89e3", "metadata": {"scrolled": true}, "outputs": [], "source": ["start_time = datetime.now()\n", "results = thread_map(classification_model.classify, dataset, max_workers=5)\n", "print(\"Classification time: \", datetime.now()-start_time)"]}, {"cell_type": "code", "execution_count": null, "id": "1e99b03c-8c83-4a45-a728-7b90eeccf94c", "metadata": {"scrolled": true}, "outputs": [], "source": ["data['label'] = [classification_model.get_result(i).lower() for i in results]\n", "data['completion_tokens'] = [classification_model.get_token_usage(i)['completion_tokens'] for i in results]\n", "data['prompt_tokens'] = [classification_model.get_token_usage(i)['prompt_tokens'] for i in results]"]}, {"cell_type": "code", "execution_count": null, "id": "28079d32-9d46-4f58-b86b-61f615d42a90", "metadata": {}, "outputs": [], "source": ["data.groupby('label').count()"]}, {"cell_type": "code", "execution_count": null, "id": "ea51997d-0a52-45d6-9048-5d128ad158c0", "metadata": {}, "outputs": [], "source": ["estimate_run_cost(data['completion_tokens'], data['prompt_tokens'], MODEL_NAME)"]}, {"cell_type": "markdown", "id": "837bdb53-f239-4d01-b89e-2fa02f874116", "metadata": {}, "source": ["## Pro thinking"]}, {"cell_type": "code", "execution_count": null, "id": "0b90710b-7493-46cf-b1f6-257db26545a4", "metadata": {}, "outputs": [], "source": ["MODEL_NAME = 'gemini-2.5-pro'"]}, {"cell_type": "code", "execution_count": null, "id": "46c8a8f5-1736-41dd-9616-76f6518b971d", "metadata": {}, "outputs": [], "source": ["classification_model = Classification(provider_name = PROVIDER_NAME,\n", "                                     model_name = MODEL_NAME,\n", "                                     prompt = PROMPT,\n", "                                     provider_config={\n", "                                        \"project_id\": PROJECT,\n", "                                        \"location\": LOCATION\n", "                                    },\n", "                                     processor_config = processor_config,\n", "                                     model_parameters={'temperature': 0, 'max_token': 5000},\n", "                                     input_type = INPUT_TYPE,\n", "                                     )"]}, {"cell_type": "code", "execution_count": null, "id": "37964bf7-43ba-4ea2-8975-c75b9cb833a9", "metadata": {}, "outputs": [], "source": ["result = classification_model.send_message(\"Say: 'The model {model_name} is alive' \". format(model_name = MODEL_NAME))\n", "print(classification_model.get_result(result))"]}, {"cell_type": "code", "execution_count": null, "id": "bb81fc70-817c-46a1-bdf1-c542dd1c25fc", "metadata": {"scrolled": true}, "outputs": [], "source": ["start_time = datetime.now()\n", "results = thread_map(classification_model.classify, dataset, max_workers=5)\n", "print(\"Classification time: \", datetime.now()-start_time)"]}, {"cell_type": "code", "execution_count": null, "id": "7a0449b2-f32b-499f-b718-9ea09569b0f5", "metadata": {}, "outputs": [], "source": ["data.groupby('label').count()"]}, {"cell_type": "code", "execution_count": null, "id": "97ac34a4-59a5-4848-95f3-53b90e25cfab", "metadata": {}, "outputs": [], "source": ["estimate_run_cost(data['completion_tokens'], data['prompt_tokens'], MODEL_NAME)"]}, {"cell_type": "code", "execution_count": null, "id": "1b23701d-9d05-4825-8e17-4df3c19abe24", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e7ef1b4c-6f5f-40e5-92a9-feacbd4d14d7", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "af9c9466-6fcd-4451-a829-6920970ad459", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "fe343774-8b93-4be8-add8-7723eaad0f28", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.19"}}, "nbformat": 4, "nbformat_minor": 5}