{"cells": [{"cell_type": "code", "execution_count": null, "id": "3c729f14-0ca7-497f-97c5-71c66b12de0e", "metadata": {}, "outputs": [], "source": ["from banjo.utils.shibainu import Classification, estimate_run_cost, configure_logger\n", "import logging\n", "configure_logger(level=logging.DEBUG)\n", "\n", "from banjo import utils\n", "from datetime import date, datetime, timedelta, timezone\n", "from tqdm.contrib.concurrent import thread_map\n"]}, {"cell_type": "code", "execution_count": null, "id": "6239e389-fff9-4041-b716-38c656ac4c09", "metadata": {}, "outputs": [], "source": ["SERVICE_ACCOUNT = \"<EMAIL>\"\n", "PROJECT = 'myaigcp' \n", "PRIORITY = 'INTERACTIVE'\n", "LOCATION = \"us-west1\"\n", "\n", "\n", "SQL = \"SELECT name as samples FROM sc-bq-gcs-billingonly.dishchenko.wiki_characters ORDER BY RAND() LIMIT 10\"\n", "PROMPT = \"\"\"\n", "You are a classification model. \n", "Assign the following message to one of the predefined classes: \n", "\"real character\", \"fictional character\"\n", "\n", "Provide only the class name as your response.\n", "If the class is unclear or the context is not understood, choose 'unknown'.\n", "\"\"\"\n", "INPUT_TYPE = \"text\""]}, {"cell_type": "markdown", "id": "343e54a7-ee65-4348-8f77-83b54c917553", "metadata": {}, "source": ["# Pull data"]}, {"cell_type": "code", "execution_count": null, "id": "aadd517c-ecb0-4b5c-a605-56afe7e68f0b", "metadata": {}, "outputs": [], "source": ["data = utils.gbq.read_gbq(SQL, \n", "                          project_id=PROJECT,\n", "                          dialect=\"standard\",\n", "                          priority = PRIORITY)\n", "dataset = data.samples.values"]}, {"cell_type": "code", "execution_count": null, "id": "d246daf2-61a2-4353-88dd-75d3dc99304a", "metadata": {}, "outputs": [], "source": ["print(dataset)"]}, {"cell_type": "markdown", "id": "30e6b1fd-7119-4e20-93c4-856b67384fff", "metadata": {}, "source": ["# OpenAI"]}, {"cell_type": "code", "execution_count": null, "id": "86b2d9ce-544a-42bf-a90e-75408869c4e4", "metadata": {}, "outputs": [], "source": ["PROVIDER_NAME = 'openai'"]}, {"cell_type": "markdown", "id": "c8b74af8-03b9-40a7-89ff-8e37e4514d13", "metadata": {}, "source": ["## GPT model"]}, {"cell_type": "code", "execution_count": null, "id": "6e26ed3e-fdf3-4038-80c4-49032afb7fec", "metadata": {}, "outputs": [], "source": ["classification_model = Classification(provider_name = PROVIDER_NAME,\n", "                                     model_name = 'gpt-4o-mini',\n", "                                     prompt = PROMPT,\n", "                                     provider_config={\n", "                                        \"service_account\": SERVICE_ACCOUNT},\n", "                                     processor_config = None,\n", "                                     model_parameters={'temperature': 0, 'max_token': 10},\n", "                                     input_type = INPUT_TYPE,\n", "                                     )"]}, {"cell_type": "code", "execution_count": null, "id": "e44113ff-62f9-4cde-86ce-67ced3919867", "metadata": {}, "outputs": [], "source": ["result = classification_model.send_message(\"Say: 'The model {model_name} is alive' \". format(model_name = 'gpt-4o-mini'))\n", "print(classification_model.get_result(result))"]}, {"cell_type": "code", "execution_count": null, "id": "3dfc342b-727c-40a8-b0d4-6fe12ae98af8", "metadata": {}, "outputs": [], "source": ["start_time = datetime.now()\n", "results = thread_map(classification_model.classify, dataset, max_workers=5)\n", "print(\"Classification time: \", datetime.now()-start_time)"]}, {"cell_type": "code", "execution_count": null, "id": "f91fad47-29fc-4283-8b8e-d1b7375b97c2", "metadata": {}, "outputs": [], "source": ["data['label'] = [classification_model.get_result(i).lower() for i in results]\n", "data['completion_tokens'] = [classification_model.get_token_usage(i)['completion_tokens'] for i in results]\n", "data['prompt_tokens'] = [classification_model.get_token_usage(i)['prompt_tokens'] for i in results]"]}, {"cell_type": "code", "execution_count": null, "id": "987a554d-8052-4a2b-9f6d-d6b5969fa93a", "metadata": {}, "outputs": [], "source": ["data.groupby('label').count()"]}, {"cell_type": "code", "execution_count": null, "id": "647da738-91a2-4567-b083-8a4050cf0f5d", "metadata": {}, "outputs": [], "source": ["estimate_run_cost(data['completion_tokens'], data['prompt_tokens'], 'gpt-4o-mini')"]}, {"cell_type": "markdown", "id": "c55e267b-b84d-4def-82ac-78306c40c68e", "metadata": {}, "source": ["## O thinking model"]}, {"cell_type": "code", "execution_count": null, "id": "a397f71d-cfb1-4c25-a2c5-875c26b3c2be", "metadata": {}, "outputs": [], "source": ["classification_model = Classification(provider_name = PROVIDER_NAME,\n", "                                     model_name = 'o1-mini',\n", "                                     prompt = PROMPT,\n", "                                     provider_config={\n", "                                        \"service_account\": SERVICE_ACCOUNT},\n", "                                     processor_config = None,\n", "                                     model_parameters={'temperature': 0, 'max_token': 10},\n", "                                     input_type = INPUT_TYPE,\n", "                                     )"]}, {"cell_type": "code", "execution_count": null, "id": "85ee112d-3135-40b7-9188-df2e3c3aa453", "metadata": {}, "outputs": [], "source": ["result = classification_model.send_message(\"Say: 'The model {model_name} is alive' \". format(model_name = 'o1-mini'))\n", "print(classification_model.get_result(result))"]}, {"cell_type": "code", "execution_count": null, "id": "bd01179b-3b30-44c4-a4d3-64396deb32a1", "metadata": {}, "outputs": [], "source": ["start_time = datetime.now()\n", "results = thread_map(classification_model.classify, dataset, max_workers=5)\n", "print(\"Classification time: \", datetime.now()-start_time)"]}, {"cell_type": "code", "execution_count": null, "id": "798b2001-d4ec-46e0-a8f9-ff21ed0c73f8", "metadata": {}, "outputs": [], "source": ["data['label'] = [classification_model.get_result(i).lower() for i in results]\n", "data['completion_tokens'] = [classification_model.get_token_usage(i)['completion_tokens'] for i in results]\n", "data['prompt_tokens'] = [classification_model.get_token_usage(i)['prompt_tokens'] for i in results]"]}, {"cell_type": "code", "execution_count": null, "id": "1e1dee5b-7cc7-4fb1-a1dd-64817fa5892c", "metadata": {}, "outputs": [], "source": ["data.groupby('label').count()"]}, {"cell_type": "code", "execution_count": null, "id": "006f189e-dd5b-4bf2-b468-ab3d69244bb7", "metadata": {}, "outputs": [], "source": ["estimate_run_cost(data['completion_tokens'], data['prompt_tokens'], 'o1-mini')"]}, {"cell_type": "markdown", "id": "b5902d2b-3e0b-4b38-8dc4-6edec1acf6ea", "metadata": {}, "source": ["# Gemini"]}, {"cell_type": "code", "execution_count": null, "id": "6b3f5d84-2ea3-44a9-bb22-4e916e5307b5", "metadata": {}, "outputs": [], "source": ["PROVIDER_NAME = 'gemini'"]}, {"cell_type": "markdown", "id": "59cc1db9-f233-42ae-bd77-1b27042c19b5", "metadata": {}, "source": ["## Flash lite not-thinking"]}, {"cell_type": "code", "execution_count": null, "id": "197e19ed-c22d-4963-892b-38264b29c88f", "metadata": {}, "outputs": [], "source": ["MODEL_NAME = 'gemini-2.0-flash'"]}, {"cell_type": "code", "execution_count": null, "id": "265ef386-b8bf-4896-9ff1-e5670ba10ff9", "metadata": {}, "outputs": [], "source": ["classification_model = Classification(provider_name = PROVIDER_NAME,\n", "                                     model_name = MODEL_NAME,\n", "                                     prompt = PROMPT,\n", "                                     provider_config={\n", "                                        \"project_id\": PROJECT,\n", "                                        \"location\": LOCATION\n", "                                    },\n", "                                     processor_config = None,\n", "                                     model_parameters={'temperature': 0, 'max_token': 10},\n", "                                     input_type = INPUT_TYPE,\n", "                                     )"]}, {"cell_type": "code", "execution_count": null, "id": "5f8ddf8b-4683-46c7-8596-adde1734aae3", "metadata": {}, "outputs": [], "source": ["result = classification_model.send_message(\"Say: 'The model {model_name} is alive' \". format(model_name = MODEL_NAME))\n", "print(classification_model.get_result(result))"]}, {"cell_type": "code", "execution_count": null, "id": "fadd65dc-7b58-44e2-a04f-4b680906eb70", "metadata": {"scrolled": true}, "outputs": [], "source": ["start_time = datetime.now()\n", "results = thread_map(classification_model.classify, dataset, max_workers=5)\n", "print(\"Classification time: \", datetime.now()-start_time)"]}, {"cell_type": "code", "execution_count": null, "id": "f6ef4a44-be5f-47d3-9779-c07803f74b68", "metadata": {}, "outputs": [], "source": ["data['label'] = [classification_model.get_result(i).lower() for i in results]\n", "data['completion_tokens'] = [classification_model.get_token_usage(i)['completion_tokens'] for i in results]\n", "data['prompt_tokens'] = [classification_model.get_token_usage(i)['prompt_tokens'] for i in results]"]}, {"cell_type": "code", "execution_count": null, "id": "0c5edd5a-ef16-49f5-91ab-13a7c80f702c", "metadata": {}, "outputs": [], "source": ["data.groupby('label').count()"]}, {"cell_type": "code", "execution_count": null, "id": "3c7f77cc-dafd-4da6-b884-fd53176e0972", "metadata": {}, "outputs": [], "source": ["estimate_run_cost(data['completion_tokens'], data['prompt_tokens'], MODEL_NAME)"]}, {"cell_type": "markdown", "id": "837bdb53-f239-4d01-b89e-2fa02f874116", "metadata": {}, "source": ["## Pro thinking"]}, {"cell_type": "code", "execution_count": null, "id": "5ed0fd26-790a-4f30-b48b-08d5e4d4ed7a", "metadata": {}, "outputs": [], "source": ["MODEL_NAME = 'gemini-2.5-pro'"]}, {"cell_type": "code", "execution_count": null, "id": "11de4bb5-6bb3-4670-909b-eee18d6828f7", "metadata": {}, "outputs": [], "source": ["classification_model = Classification(provider_name = PROVIDER_NAME,\n", "                                     model_name = MODEL_NAME,\n", "                                     prompt = PROMPT,\n", "                                     provider_config={\n", "                                        \"project_id\": PROJECT,\n", "                                        \"location\": LOCATION\n", "                                    },\n", "                                     processor_config = None,\n", "                                     model_parameters={'temperature': 0, 'max_token': 5000, 'disable_thinking': False},\n", "                                     input_type = INPUT_TYPE,\n", "                                     )"]}, {"cell_type": "code", "execution_count": null, "id": "23338e0b-c5b0-4fb1-8f19-12ff4d2e493f", "metadata": {}, "outputs": [], "source": ["result = classification_model.send_message(\"Say: 'The model {model_name} is alive' \". format(model_name = MODEL_NAME))\n", "print(classification_model.get_result(result))"]}, {"cell_type": "code", "execution_count": null, "id": "ae7acab9-bbd6-4172-8ad6-1c75b447a4c2", "metadata": {}, "outputs": [], "source": ["result"]}, {"cell_type": "code", "execution_count": null, "id": "eaccd21c-80cd-4fec-b798-5b311782c7ef", "metadata": {"scrolled": true}, "outputs": [], "source": ["start_time = datetime.now()\n", "results = thread_map(classification_model.classify, dataset, max_workers=5)\n", "print(\"Classification time: \", datetime.now()-start_time)"]}, {"cell_type": "code", "execution_count": null, "id": "1d6710cd-83d9-411d-a237-e4fdb0d8b69c", "metadata": {}, "outputs": [], "source": ["data['label'] = [classification_model.get_result(i).lower() for i in results]\n", "data['completion_tokens'] = [classification_model.get_token_usage(i)['completion_tokens'] for i in results]\n", "data['prompt_tokens'] = [classification_model.get_token_usage(i)['prompt_tokens'] for i in results]"]}, {"cell_type": "code", "execution_count": null, "id": "84e0c4d0-b619-4b7b-9a29-38fe0cb0d198", "metadata": {}, "outputs": [], "source": ["data.groupby('label').count()"]}, {"cell_type": "code", "execution_count": null, "id": "bda718fe-c1f2-49e4-b86c-33330f709504", "metadata": {}, "outputs": [], "source": ["estimate_run_cost(data['completion_tokens'], data['prompt_tokens'], MODEL_NAME)"]}, {"cell_type": "markdown", "id": "b4a66444-ac06-4414-8edc-95bbbd59b9ec", "metadata": {}, "source": ["# AGI model"]}, {"cell_type": "code", "execution_count": null, "id": "688520fe-598c-4fbf-b8cb-2bb2597a3794", "metadata": {}, "outputs": [], "source": ["MODEL_NAME = \"Qwen3-30B-A3B-FP8\"\n", "PROVIDER_NAME = \"agi\""]}, {"cell_type": "code", "execution_count": null, "id": "6e4654c5-1780-4389-9edc-e1c40c986186", "metadata": {}, "outputs": [], "source": ["classification_model = Classification(\n", "    provider_name=PROVIDER_NAME,\n", "    model_name=MODEL_NAME,\n", "    prompt=PROMPT,\n", "    provider_config={\"service_account\": \"<EMAIL>\"},\n", "    model_parameters={'temperature': 0, 'max_token': 1280, 'disable_thinking': True},\n", "    input_type=\"text\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "efbdeef3-4a9c-4c1d-88d3-ee6305e2053d", "metadata": {}, "outputs": [], "source": ["result = classification_model.send_message(\"Say: 'The model {model_name} is alive' \". format(model_name = MODEL_NAME))\n", "print(classification_model.get_result(result))"]}, {"cell_type": "code", "execution_count": null, "id": "93b2d155-d050-4c28-92bf-2854c4f8d5bd", "metadata": {}, "outputs": [], "source": ["print(classification_model.get_result(result, include_thinking=True))"]}, {"cell_type": "code", "execution_count": null, "id": "cf2a755a-f205-42f0-8b13-a7514c8ee5f1", "metadata": {}, "outputs": [], "source": ["start_time = datetime.now()\n", "results = thread_map(classification_model.classify, dataset, max_workers=5)\n", "print(\"Classification time: \", datetime.now()-start_time)"]}, {"cell_type": "code", "execution_count": null, "id": "dc3b5ed4-f3aa-407e-bf09-cbcd4abf3469", "metadata": {}, "outputs": [], "source": ["data['label'] = [classification_model.get_result(i).lower() for i in results]\n", "data['completion_tokens'] = [classification_model.get_token_usage(i)['completion_tokens'] for i in results]\n", "data['prompt_tokens'] = [classification_model.get_token_usage(i)['prompt_tokens'] for i in results]"]}, {"cell_type": "code", "execution_count": null, "id": "d18754b8-4efb-4a91-abcf-d29efb208029", "metadata": {}, "outputs": [], "source": ["data.groupby('label').count()"]}, {"cell_type": "code", "execution_count": null, "id": "429599ba-0d59-48ed-a773-f18ccd72fdf6", "metadata": {}, "outputs": [], "source": ["estimate_run_cost(data['completion_tokens'], data['prompt_tokens'], MODEL_NAME)"]}, {"cell_type": "code", "execution_count": null, "id": "c8826a10-7016-4077-a3df-2e112c70bd6d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.19"}}, "nbformat": 4, "nbformat_minor": 5}