{"cells": [{"cell_type": "code", "execution_count": null, "id": "3e954b2c", "metadata": {"slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["import json\n", "from google.cloud import storage, bigquery\n", "from google.oauth2 import service_account\n", "import time\n", "from functools import wraps\n", "import pandas as pd\n", "import pandas_gbq\n", "import os\n", "from sklearn.preprocessing import OneHotEncoder\n", "from sklearn.base import clone\n", "import xgboost as xgb\n", "import pickle\n", "import datetime\n", "\n", "project_id = 'sc-bq-gcs-billingonly'\n", "client = bigquery.Client(project=project_id)"]}, {"cell_type": "code", "execution_count": null, "id": "ccde366f-3161-46c8-917b-73034507b9b9", "metadata": {"slideshow": {"slide_type": ""}, "tags": ["parameters"]}, "outputs": [], "source": ["# Husky Notebook Parameters cell\n", "DATE = '********'\n", "CONDITIONS = \"\"\n", "LOCAL_TESTING = \"TRUE\"  # use a string instead of bool because husky passes strings"]}, {"cell_type": "code", "execution_count": null, "id": "0bee14f5", "metadata": {"slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["# define the function to download the model file from GCS bucket\n", "def download_all_blobs(bucket_name, source_folder, destination_folder):\n", "    storage_client = storage.Client(project = \"sc-bq-gcs-billingonly\")\n", "\n", "    bucket = storage_client.bucket(bucket_name)\n", "    blobs = bucket.list_blobs(prefix=source_folder)\n", "\n", "    for blob in blobs:\n", "        if blob.name.endswith('/'):\n", "            continue\n", "            3\n", "        destination_file_name = destination_folder + blob.name\n", "        # Create the destination directory if it doesn't exist\n", "        os.makedirs(os.path.dirname(destination_file_name), exist_ok=True)\n", "        blob.download_to_filename(destination_file_name)\n", "\n", "# Use the function to download the model file\n", "download_all_blobs(\"prod-models\", \"snapchat_plus/\",\"./\")"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["query = '''\n", "SELECT\n", "    a.*,\n", "    b.signup_propensity_score\n", "FROM\n", "    (select * from `sc-analytics.report_snapchat_plus.splus_propensity_scoring_data_{ds}`) a\n", "INNER JOIN\n", "    `sc-analytics.report_snapchat_plus.snapchat_plus_signup_propensity_score_{ds}` b\n", "ON\n", "    a.ghost_user_id=b.ghost_user_id\n", "WHERE\n", "    b.signup_propensity_score>0.5\n", "'''.format(ds=DATE)"], "id": "40fe5a5057a14201"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "df = client.query(query).to_dataframe()", "id": "216043d858941a31"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "df.replace({False: 0, True: 1}, inplace=True)", "id": "32dfa79e4810465f"}, {"cell_type": "code", "execution_count": null, "id": "247ef13f", "metadata": {"slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["cate_var = [\n", " 'inferred_age_bucket',\n", " 'gender',\n", " 'app_engagement_status',\n", " 'communication_engagement_status',\n", " 'calling_engagement_status',\n", " 'snap_create_engagement_status',\n", " 'publisher_engagement_status',\n", " 'bidirectional_friend_status',\n", " 'friend_story_engagement_status',\n", " 'public_user_story_engagement_status',\n", " 'lens_engagement_status',\n", " 'user_persona_v2',\n", " 'country_bucket',\n", " 'network_quality',\n", " 'device_cluster',  # add in device cluster\n", " 'weekly_active_friend_status',\n", " 'weekly_post_active_friend_status',\n", " 'weekly_communication_active_friend_status',\n", " 'weekly_snap_create_active_friend_status',\n", " 'communication_power_friend_status',\n", " 'snap_create_power_friend_status',\n", " 'spotlight_story_engagement_status',\n", " 'spotlight_story_post_status',\n", " 'days_since_last_active',\n", " 'map_L30_status',\n", " 'map_persona_v1',\n", " 'bitmoji_fashion_persona',\n", " 'bitmoji_fashion_builder_L28_engagement_status']"]}, {"cell_type": "code", "execution_count": null, "id": "e9246f72", "metadata": {}, "outputs": [], "source": ["pretrained_xgboost_model_annual = \"snapchat_plus/xgboost_propensity_wau_annual.json\"\n", "pretrained_xgboost_model_platinum = \"snapchat_plus/xgboost_propensity_wau_platinum.json\""]}, {"cell_type": "code", "execution_count": null, "id": "4974bbe4", "metadata": {}, "outputs": [], "source": ["xbg_annual = xgb.XGBClassifier()\n", "xbg_annual.load_model(pretrained_xgboost_model_annual)\n", "\n", "xbg_platinum = xgb.XGBClassifier()\n", "xbg_platinum.load_model(pretrained_xgboost_model_platinum)"]}, {"cell_type": "code", "execution_count": null, "id": "1cdfc778", "metadata": {}, "outputs": [], "source": ["with open('snapchat_plus/encoder_w_device.pickle', 'rb') as f:\n", "    enc = pickle.load(f)"]}, {"cell_type": "code", "execution_count": 1, "id": "47d54372", "metadata": {"slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["plan_propensity_predictors = [\n", "\n", " #new predictors for plan-type model\n", " 'platinum_plan_tap_l30_uu',\n", " 'annual_plan_tap_l30_uu',\n", " 'family_plan_tap_l30_uu',\n", " 'monthly_plan_tap_l30_uu',\n", " 'used_to_annual',\n", " 'vol_churn_uu',\n", " 'invol_churn_uu',\n", " 'plus_tenure',\n", "\n", " 'exclusive_bitmoji_background_upsell',\n", " 'chat_ct_drawer_open_count',\n", " 'MAP_MAP_OPEN_COUNT_last_7days',\n", " 'my_profile_upsell',\n", " 'friend_badge_upsell',\n", " 'app_open_count',\n", " 'active_splus_friend_counts',\n", " 'friend_story_view_count',\n", " 'is_engaged_in_last_30_days',\n", " 'story_post_L7',\n", " 'CHAT_BITMOJI_STICKER_COUNT_last_7days',\n", " 'friend_counts',\n", " 'MAP_STORY_SNAP_VIEW_COUNT_last_7days',\n", " 'is_story_public_user',\n", " 'publisher_subscription_count',\n", " 'chat_wallpapers_upsell',\n", " 'LENS_DIRECT_SNAP_SEND_COUNT_last_7days',\n", " 'lens_swipe_count',\n", " 'subscription_landing_page_visits',\n", " 'ghost_trail_upsell',\n", " 'communication_L7',\n", " 'used_to_signedup_index',\n", " 'is_snap_public_user',\n", " 'notification_sounds_upsell',\n", " 'pin_bff_upsell',\n", " 'splus_exposure',\n", " 'non_friend_user_story_view_count',\n", " 'communication_count',\n", " 'snap_create_L7',\n", " 'has_bitmoji',\n", " 'has_public_profile',\n", " 'DIRECT_SNAP_CREATE_COUNT_last_7days',\n", " 'camera_takeover_upsell',\n", " 'df_L7',\n", " 'is_popular_user',\n", " 'publisher_story_view_count',\n", " 'FRIEND_STORY_SNAP_POST_COUNT_last_7days',\n", " 'CHAT_GROUP_SEND_COUNT_last_7days',\n", " 'settings_upsell',\n", " 'publisher_L7',\n", " 'DIRECT_SNAP_VIEW_COUNT_last_7days',\n", " 'active_splus_close_friend_counts', # add in close+ active splus friends\n", " 'app_L7',  # newly added for app_l7 > 0 users\n", " 'inferred_age_bucket_13-17', 'inferred_age_bucket_18-24',\n", " 'inferred_age_bucket_25-34', 'inferred_age_bucket_35-plus',\n", " 'inferred_age_bucket_unknown', 'inferred_age_bucket_None',\n", " 'gender_female', 'gender_male', 'gender_None',\n", " 'app_engagement_status_0 - Idle',\n", " 'app_engagement_status_1 - Casual',\n", " 'app_engagement_status_2 - Regular',\n", " 'app_engagement_status_3 - Power',\n", " 'app_engagement_status_4 - Outlier',\n", " 'communication_engagement_status_0 - Idle',\n", " 'communication_engagement_status_1 - Casual',\n", " 'communication_engagement_status_2 - Regular',\n", " 'communication_engagement_status_3 - Power',\n", " 'communication_engagement_status_4 - Outlier',\n", " 'calling_engagement_status_0 - Idle',\n", " 'calling_engagement_status_1 - Casual',\n", " 'calling_engagement_status_2 - Regular',\n", " 'calling_engagement_status_3 - Power',\n", " 'calling_engagement_status_4 - Outlier',\n", " 'snap_create_engagement_status_0 - Idle',\n", " 'snap_create_engagement_status_1 - Casual',\n", " 'snap_create_engagement_status_2 - Regular',\n", " 'snap_create_engagement_status_3 - Power',\n", " 'snap_create_engagement_status_4 - Outlier',\n", " 'publisher_engagement_status_0 - Idle',\n", " 'publisher_engagement_status_1 - Casual',\n", " 'publisher_engagement_status_2 - Regular',\n", " 'publisher_engagement_status_3 - Power',\n", " 'publisher_engagement_status_4 - Outlier',\n", " 'bidirectional_friend_status_0-5',\n", " 'bidirectional_friend_status_101-200',\n", " 'bidirectional_friend_status_201+',\n", " 'bidirectional_friend_status_31-100',\n", " 'bidirectional_friend_status_6-30',\n", " 'friend_story_engagement_status_0 - Idle',\n", " 'friend_story_engagement_status_1 - Casual',\n", " 'friend_story_engagement_status_2 - Regular',\n", " 'friend_story_engagement_status_3 - Power',\n", " 'friend_story_engagement_status_4 - Outlier',\n", " 'friend_story_engagement_status_Unknown',\n", " 'public_user_story_engagement_status_0 - Idle',\n", " 'public_user_story_engagement_status_1 - Casual',\n", " 'public_user_story_engagement_status_2 - Regular',\n", " 'public_user_story_engagement_status_3 - Power',\n", " 'public_user_story_engagement_status_4 - Outlier',\n", " 'public_user_story_engagement_status_Unknown',\n", " 'lens_engagement_status_0 - Idle',\n", " 'lens_engagement_status_1 - Casual',\n", " 'lens_engagement_status_2 - Regular',\n", " 'lens_engagement_status_3 - Power',\n", " 'lens_engagement_status_4 - Outlier',\n", " 'lens_engagement_status_Unknown',\n", "    'user_persona_v2_Chatter',\n", " 'user_persona_v2_Do Nothing',\n", "    'user_persona_v2_NA',\n", " 'user_persona_v2_Poster',\n", "    'user_persona_v2_Snap as a Camera',\n", " 'user_persona_v2_Snap/Chat Viewer',\n", "    'user_persona_v2_Snapper',\n", " 'user_persona_v2_Story/Discover Watcher',\n", "    'country_bucket_01: US',\n", " 'country_bucket_02: Balanced',\n", "    'country_bucket_03: Emerging',\n", " 'country_bucket_04: Community',\n", "    'country_bucket_None',\n", " 'network_quality_1-bad', 'network_quality_2-okay',\n", " 'network_quality_3-good', 'network_quality_4-excellent',\n", " 'network_quality_NA', 'weekly_active_friend_status_Bucket 1: 0',\n", " 'weekly_active_friend_status_Bucket 2: 1-9',\n", " 'weekly_active_friend_status_Bucket 3: 10-30',\n", " 'weekly_active_friend_status_Bucket 4: 31-100',\n", " 'weekly_active_friend_status_Bucket 5: 101+',\n", " 'weekly_active_friend_status_NA',\n", " 'weekly_post_active_friend_status_Bucket 1: 0',\n", " 'weekly_post_active_friend_status_Bucket 2: 1-4',\n", " 'weekly_post_active_friend_status_Bucket 3: 5-15',\n", " 'weekly_post_active_friend_status_Bucket 4: 16-50',\n", " 'weekly_post_active_friend_status_Bucket 5: 51+',\n", " 'weekly_post_active_friend_status_NA',\n", " 'weekly_communication_active_friend_status_Bucket 1: 0',\n", " 'weekly_communication_active_friend_status_Bucket 2: 1-9',\n", " 'weekly_communication_active_friend_status_Bucket 3: 10-30',\n", " 'weekly_communication_active_friend_status_Bucket 4: 31-100',\n", " 'weekly_communication_active_friend_status_Bucket 5: 101+',\n", " 'weekly_communication_active_friend_status_NA',\n", " 'weekly_snap_create_active_friend_status_Bucket 1: 0',\n", " 'weekly_snap_create_active_friend_status_Bucket 2: 1-9',\n", " 'weekly_snap_create_active_friend_status_Bucket 3: 10-30',\n", " 'weekly_snap_create_active_friend_status_Bucket 4: 31-100',\n", " 'weekly_snap_create_active_friend_status_Bucket 5: 101+',\n", " 'weekly_snap_create_active_friend_status_NA',\n", " 'communication_power_friend_status_Bucket 1: 0',\n", " 'communication_power_friend_status_Bucket 2: 1-9',\n", " 'communication_power_friend_status_Bucket 3: 10-30',\n", " 'communication_power_friend_status_Bucket 4: 31-100',\n", " 'communication_power_friend_status_Bucket 5: 101+',\n", " 'communication_power_friend_status_NA',\n", " 'snap_create_power_friend_status_Bucket 1: 0',\n", " 'snap_create_power_friend_status_Bucket 2: 1-9',\n", " 'snap_create_power_friend_status_Bucket 3: 10-30',\n", " 'snap_create_power_friend_status_Bucket 4: 31-100',\n", " 'snap_create_power_friend_status_Bucket 5: 101+',\n", " 'snap_create_power_friend_status_NA',\n", " 'spotlight_story_engagement_status_0: Non User',\n", " 'spotlight_story_engagement_status_1: Accidental User',\n", " 'spotlight_story_engagement_status_2: Casual User',\n", " 'spotlight_story_engagement_status_3: Regular User',\n", " 'spotlight_story_engagement_status_4: Power User',\n", " 'spotlight_story_engagement_status_5: Outlier User',\n", " 'spotlight_story_post_status_0: Non-Creators (No L30 Submissions)',\n", " 'spotlight_story_post_status_1: Casual (0 Quality Submissions)',\n", " 'spotlight_story_post_status_2: Regular (1 Quality Submission)',\n", " 'spotlight_story_post_status_3: Frequent (2-5 Quality Submissions)',\n", " 'spotlight_story_post_status_4: Power (6+ Quality Submissions)',\n", " 'days_since_last_active_0-2', 'days_since_last_active_15-30',\n", " 'days_since_last_active_181-365', 'days_since_last_active_3-7',\n", " 'days_since_last_active_31-60', 'days_since_last_active_366+',\n", " 'days_since_last_active_61-90', 'days_since_last_active_8-14',\n", " 'days_since_last_active_91-180', 'map_L30_status_Casual',\n", " 'map_L30_status_NA', 'map_L30_status_New User',\n", " 'map_L30_status_Power', 'map_L30_status_Regular',\n", " 'map_persona_v1_Actionmoji User', 'map_persona_v1_Browser',\n", " 'map_persona_v1_Do Nothing', 'map_persona_v1_Friend Viewer',\n", " 'map_persona_v1_Layers User', 'map_persona_v1_NA',\n", " 'map_persona_v1_Places User', 'map_persona_v1_Story Viewer',\n", " 'bitmoji_fashion_persona_1: Outfit Changer',\n", " 'bitmoji_fashion_persona_2: Outfit Trier',\n", " 'bitmoji_fashion_persona_3: Outfit Browser',\n", " 'bitmoji_fashion_persona_4: <PERSON><PERSON>',\n", " 'bitmoji_fashion_persona_5: Do Nothing',\n", " 'bitmoji_fashion_persona_<PERSON>A',\n", " 'bitmoji_fashion_builder_L28_engagement_status_1: Idle',\n", " 'bitmoji_fashion_builder_L28_engagement_status_2: Regular',\n", " 'bitmoji_fashion_builder_L28_engagement_status_3: Power',\n", " 'bitmoji_fashion_builder_L28_engagement_status_4: New Bitmoji User',\n", " 'bitmoji_fashion_builder_L28_engagement_status_NA',\n", " 'device_cluster_0', # add decive_cluster\n", " 'device_cluster_1',\n", " 'device_cluster_10',\n", " 'device_cluster_11',\n", " 'device_cluster_12',\n", " 'device_cluster_13',\n", " 'device_cluster_2',\n", " 'device_cluster_3',\n", " 'device_cluster_4',\n", " 'device_cluster_5',\n", " 'device_cluster_6',\n", " 'device_cluster_7',\n", " 'device_cluster_8',\n", " 'device_cluster_9',\n", " 'device_cluster_NA'\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "15afe242", "metadata": {"slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["encoded_cate_var = enc.transform(df[cate_var])\n", "encoded_cat_vars_feature_names = enc.get_feature_names_out(cate_var)\n", "df_encoded_cat_vars = pd.DataFrame(encoded_cate_var, columns = encoded_cat_vars_feature_names)\n", "df_new = pd.concat([df, df_encoded_cat_vars], axis=1)"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# perform the propensity model prediction and append to the score table\n", "y_pred_ann = xbg_annual.predict_proba(df_new[plan_propensity_predictors])\n", "y_pred_platinum = xbg_platinum.predict_proba(df_new[plan_propensity_predictors])"], "id": "eea1cc371f5da3df"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["df_scores=df_new[['ghost_user_id','signup_propensity_score']].copy()\n", "df_scores['propensity_annual'] = y_pred_ann[:, 1]\n", "df_scores['propensity_platinum'] = y_pred_platinum[:, 1]"], "id": "7ee6db817c8b73c1"}, {"cell_type": "code", "execution_count": null, "id": "07c24e1d", "metadata": {"slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["job_config = bigquery.LoadJobConfig(\n", "         # below is equivalent to if_exists=\"replace\"\n", "         write_disposition=\"WRITE_TRUNCATE\"\n", "     )\n", "job = client.load_table_from_dataframe(\n", "         df_scores,\n", "         f'sc-analytics.report_snapchat_plus.snapchat_plus_plan_propensity_score_{DATE}',\n", "         job_config=job_config\n", "     )\n", "\n", "print('Table written')"]}], "metadata": {"kernelspec": {"display_name": "py38", "language": "python", "name": "py38"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.12"}}, "nbformat": 4, "nbformat_minor": 5}