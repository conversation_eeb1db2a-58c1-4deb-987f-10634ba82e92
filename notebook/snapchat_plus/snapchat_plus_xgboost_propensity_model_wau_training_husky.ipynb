{"cells": [{"cell_type": "code", "execution_count": null, "id": "b90f4b5e", "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas as pd\n", "from google.cloud import bigquery, storage\n", "from google.oauth2 import service_account\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import matplotlib.ticker as mtick\n", "import numpy as np\n", "import os\n", "import xgboost as xgb\n", "import pickle\n", "\n", "project_id = 'sc-bq-gcs-billingonly'\n", "client = bigquery.Client(project=project_id)"]}, {"cell_type": "code", "execution_count": null, "id": "4358678f", "metadata": {}, "outputs": [], "source": ["from sklearn.ensemble import GradientBoostingClassifier\n", "from sklearn.preprocessing import OneHotEncoder\n", "from sklearn import model_selection, preprocessing \n", "from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc, roc_auc_score,average_precision_score,accuracy_score"]}, {"cell_type": "code", "execution_count": null, "id": "8ad897f4", "metadata": {"slideshow": {"slide_type": ""}, "tags": ["parameters"]}, "outputs": [], "source": ["# Husky Notebook Parameters cell\n", "DATE = '********'\n", "CONDITIONS = \"\"\n", "LOCAL_TESTING = \"TRUE\"  # use a string instead of bool because husky passes strings"]}, {"cell_type": "code", "execution_count": null, "id": "30067be0", "metadata": {"slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["query = '''\n", "SELECT * \n", "FROM `sc-analytics.report_snapchat_plus.splus_propensity_model_training_data_{ds}`\n", "WHERE (new_sign_up=1 OR MOD(ABS(FARM_FINGERPRINT(CONCAT('splus_propensity_model_{ds}_active_subscriber_with_downsampling', ghost_user_id))),120) IN (10)) \n", "AND app_L7 > 0\n", "AND map_persona_v1 !='Other'\n", "AND coalesce(active_today_index, 0) <> 1\n", "'''.format(ds=DATE)"]}, {"cell_type": "code", "execution_count": null, "id": "66a4c4a3", "metadata": {}, "outputs": [], "source": ["df = client.query(query).to_dataframe()"]}, {"cell_type": "code", "execution_count": null, "id": "228a192d", "metadata": {}, "outputs": [], "source": ["df.replace({False: 0, True: 1}, inplace=True)"]}, {"cell_type": "code", "execution_count": null, "id": "3de0181d", "metadata": {}, "outputs": [], "source": ["remove_highly_correalted_features = [\n", "'num_weekly_active_friends',\n", "'num_weekly_active_friends_with_friend_story_post',\n", "'num_weekly_active_friends_with_direct_snap_or_chat_send',\n", "'num_weekly_active_friends_with_snap_create',\n", "'num_app_open_power_friends',\n", "'num_snap_create_power_friends',\n", "'num_communication_power_friends',\n", "'num_friend_story_post_power_friends',\n", "'friend_story_view_time',\n", "'FRIEND_STORY_SNAP_VIEW_COUNT_last_7days',\n", "'non_friend_user_story_view_time',\n", "'STORY_SNAP_VIEW_COUNT_last_7days',\n", "'story_L7',\n", "'DISCOVER_SNAP_VIEW_COUNT_last_7days',\n", "'LENS_DIRECT_SNAP_CREATE_COUNT_last_7days',\n", "'FILTER_LENS_SWIPE_COUNT_last_7days',\n", "'friend_story_post_L7',\n", "'CHAT_CHAT_SEND_COUNT_last_7days',\n", "'DIRECT_SNAP_SEND_COUNT_last_7days',\n", "'MAP_USER_VIEW_COUNT_last_7days',\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "8d8cf0b9", "metadata": {}, "outputs": [], "source": ["X = df.drop(remove_highly_correalted_features+['ghost_user_id', 'user_id', 'country', 'user_persona_v3', 'active_today_index', 'new_sign_up', 'new_sign_up_date', 'new_purchased_at', 'new_subscription_type', 'new_subscription_source', 'new_subscription_category'], axis=1)\n", "y = df['new_sign_up']"]}, {"cell_type": "markdown", "id": "19df0de4", "metadata": {}, "source": ["## generate trainging and testing dataset"]}, {"cell_type": "code", "execution_count": null, "id": "00e7976f", "metadata": {}, "outputs": [], "source": ["predictors = ['friend_counts',\n", " 'active_splus_friend_counts',\n", " 'splus_exposure',\n", " 'inferred_age_bucket',\n", " 'gender',\n", " 'is_engaged_in_last_30_days',\n", " 'has_bitmoji',\n", " 'friend_story_view_count',\n", " 'non_friend_user_story_view_count',\n", " 'df_L7',\n", " 'publisher_story_view_count',\n", " 'chat_ct_drawer_open_count',\n", " 'lens_swipe_count',\n", " 'publisher_subscription_count',\n", " 'communication_L7',\n", " 'publisher_L7',\n", " 'snap_create_L7',\n", " 'story_post_L7',\n", " 'active_splus_close_friend_counts', # add in close+ active splus friends\n", " 'device_cluster', # add in device cluster\n", " 'app_open_count',\n", " 'communication_count',\n", " 'is_popular_user',\n", " 'has_public_profile',\n", " 'is_story_public_user',\n", " 'is_snap_public_user',\n", " 'app_engagement_status',\n", " 'communication_engagement_status',\n", " 'calling_engagement_status',\n", " 'snap_create_engagement_status',\n", " 'publisher_engagement_status',\n", " 'bidirectional_friend_status',\n", " 'friend_story_engagement_status',\n", " 'public_user_story_engagement_status',\n", " 'lens_engagement_status',\n", " 'user_persona_v2',\n", " 'country_bucket',\n", " 'network_quality',\n", " 'weekly_active_friend_status',\n", " 'weekly_post_active_friend_status',\n", " 'weekly_communication_active_friend_status',\n", " 'weekly_snap_create_active_friend_status',\n", " 'communication_power_friend_status',\n", " 'snap_create_power_friend_status',\n", " 'spotlight_story_engagement_status',\n", " 'spotlight_story_post_status',\n", " 'days_since_last_active',\n", " 'map_L30_status',\n", " 'map_persona_v1',\n", " 'bitmoji_fashion_persona',\n", " 'bitmoji_fashion_builder_L28_engagement_status',\n", " 'used_to_signedup_index',\n", " 'my_profile_upsell',\n", " 'pin_bff_upsell',\n", " 'settings_upsell',\n", " 'friend_badge_upsell',\n", " 'chat_wallpapers_upsell',\n", " 'exclusive_bitmoji_background_upsell',\n", " 'notification_sounds_upsell',\n", " 'ghost_trail_upsell',\n", " 'camera_takeover_upsell',\n", " 'subscription_landing_page_visits',\n", " 'CHAT_GROUP_SEND_COUNT_last_7days',\n", " 'CHAT_BITMOJI_STICKER_COUNT_last_7days',\n", " 'DIRECT_SNAP_CREATE_COUNT_last_7days',\n", " 'DIRECT_SNAP_VIEW_COUNT_last_7days',\n", " 'FRIEND_STORY_SNAP_POST_COUNT_last_7days',\n", " 'LENS_DIRECT_SNAP_SEND_COUNT_last_7days',\n", " 'MAP_MAP_OPEN_COUNT_last_7days',\n", " 'MAP_STORY_SNAP_VIEW_COUNT_last_7days']"]}, {"cell_type": "code", "execution_count": null, "id": "cb41bdfa", "metadata": {}, "outputs": [], "source": ["cate_var = [\n", " 'inferred_age_bucket',\n", " 'gender',\n", " 'app_engagement_status',\n", " 'communication_engagement_status',\n", " 'calling_engagement_status',\n", " 'snap_create_engagement_status',\n", " 'publisher_engagement_status',\n", " 'bidirectional_friend_status',\n", " 'friend_story_engagement_status',\n", " 'public_user_story_engagement_status',\n", " 'lens_engagement_status',\n", " 'user_persona_v2',\n", " 'country_bucket',\n", " 'network_quality',\n", " 'device_cluster',  # add in device cluster\n", " 'weekly_active_friend_status',\n", " 'weekly_post_active_friend_status',\n", " 'weekly_communication_active_friend_status',\n", " 'weekly_snap_create_active_friend_status',\n", " 'communication_power_friend_status',\n", " 'snap_create_power_friend_status',\n", " 'spotlight_story_engagement_status',\n", " 'spotlight_story_post_status',\n", " 'days_since_last_active',\n", " 'map_L30_status',\n", " 'map_persona_v1',\n", " 'bitmoji_fashion_persona',\n", " 'bitmoji_fashion_builder_L28_engagement_status']"]}, {"cell_type": "code", "execution_count": null, "id": "1160ea61", "metadata": {}, "outputs": [], "source": ["# define the function to download the model file from GCS bucket\n", "def download_all_blobs(bucket_name, source_folder, destination_folder):\n", "    storage_client = storage.Client(project = \"sc-bq-gcs-billingonly\")\n", "\n", "    bucket = storage_client.bucket(bucket_name)\n", "    blobs = bucket.list_blobs(prefix=source_folder)\n", "\n", "    for blob in blobs:\n", "        if blob.name.endswith('/'):\n", "            continue\n", "            3\n", "        destination_file_name = destination_folder + blob.name\n", "        # Create the destination directory if it doesn't exist\n", "        os.makedirs(os.path.dirname(destination_file_name), exist_ok=True)\n", "        blob.download_to_filename(destination_file_name)\n", "\n", "# Use the function to download the model file\n", "download_all_blobs(\"prod-models\", \"snapchat_plus/\", \"./\")"]}, {"cell_type": "code", "execution_count": null, "id": "f80b04f4", "metadata": {}, "outputs": [], "source": ["with open('snapchat_plus/encoder_w_device.pickle', 'rb') as f:\n", "    enc = pickle.load(f)"]}, {"cell_type": "code", "execution_count": null, "id": "548f2c25", "metadata": {}, "outputs": [], "source": ["encoded_cate_var = enc.transform(df[cate_var])\n", "encoded_cat_vars_feature_names = enc.get_feature_names_out(cate_var)\n", "df_encoded_cat_vars = pd.DataFrame(encoded_cate_var, columns = encoded_cat_vars_feature_names)"]}, {"cell_type": "code", "execution_count": null, "id": "82a02fbd", "metadata": {}, "outputs": [], "source": ["df_encoded_cat_vars.shape"]}, {"cell_type": "code", "execution_count": null, "id": "988a9301", "metadata": {}, "outputs": [], "source": ["df_new = pd.concat([df, df_encoded_cat_vars], axis=1)"]}, {"cell_type": "code", "execution_count": null, "id": "fb5a1b51", "metadata": {}, "outputs": [], "source": ["propensity_predictors = ['exclusive_bitmoji_background_upsell',\n", " 'chat_ct_drawer_open_count',\n", " 'MAP_MAP_OPEN_COUNT_last_7days',\n", " 'my_profile_upsell',\n", " 'friend_badge_upsell',\n", " 'app_open_count',\n", " 'active_splus_friend_counts',\n", " 'friend_story_view_count',\n", " 'is_engaged_in_last_30_days',\n", " 'story_post_L7',\n", " 'CHAT_BITMOJI_STICKER_COUNT_last_7days',\n", " 'friend_counts',\n", " 'MAP_STORY_SNAP_VIEW_COUNT_last_7days',\n", " 'is_story_public_user',\n", " 'publisher_subscription_count',\n", " 'chat_wallpapers_upsell',\n", " 'LENS_DIRECT_SNAP_SEND_COUNT_last_7days',\n", " 'lens_swipe_count',\n", " 'subscription_landing_page_visits',\n", " 'ghost_trail_upsell',\n", " 'communication_L7',\n", " 'used_to_signedup_index',\n", " 'is_snap_public_user',\n", " 'notification_sounds_upsell',\n", " 'pin_bff_upsell',\n", " 'splus_exposure',\n", " 'non_friend_user_story_view_count',\n", " 'communication_count',\n", " 'snap_create_L7',\n", " 'has_bitmoji',\n", " 'has_public_profile',\n", " 'DIRECT_SNAP_CREATE_COUNT_last_7days',\n", " 'camera_takeover_upsell',\n", " 'df_L7',\n", " 'is_popular_user',\n", " 'publisher_story_view_count',\n", " 'FRIEND_STORY_SNAP_POST_COUNT_last_7days',\n", " 'CHAT_GROUP_SEND_COUNT_last_7days',\n", " 'settings_upsell',\n", " 'publisher_L7',\n", " 'DIRECT_SNAP_VIEW_COUNT_last_7days',\n", " 'active_splus_close_friend_counts', # add in close+ active splus friends\n", " 'app_L7',  # newly added for app_l7 > 0 users                         \n", " 'inferred_age_bucket_13-17', 'inferred_age_bucket_18-24',\n", " 'inferred_age_bucket_25-34', 'inferred_age_bucket_35-plus',\n", " 'inferred_age_bucket_unknown', 'inferred_age_bucket_None',\n", " 'gender_female', 'gender_male', 'gender_None',\n", " 'app_engagement_status_0 - Idle',\n", " 'app_engagement_status_1 - Casual',\n", " 'app_engagement_status_2 - Regular',\n", " 'app_engagement_status_3 - Power',\n", " 'app_engagement_status_4 - Outlier',\n", " 'communication_engagement_status_0 - Idle',\n", " 'communication_engagement_status_1 - Casual',\n", " 'communication_engagement_status_2 - Regular',\n", " 'communication_engagement_status_3 - Power',\n", " 'communication_engagement_status_4 - Outlier',\n", " 'calling_engagement_status_0 - Idle',\n", " 'calling_engagement_status_1 - Casual',\n", " 'calling_engagement_status_2 - Regular',\n", " 'calling_engagement_status_3 - Power',\n", " 'calling_engagement_status_4 - Outlier',\n", " 'snap_create_engagement_status_0 - Idle',\n", " 'snap_create_engagement_status_1 - Casual',\n", " 'snap_create_engagement_status_2 - Regular',\n", " 'snap_create_engagement_status_3 - Power',\n", " 'snap_create_engagement_status_4 - Outlier',\n", " 'publisher_engagement_status_0 - Idle',\n", " 'publisher_engagement_status_1 - Casual',\n", " 'publisher_engagement_status_2 - Regular',\n", " 'publisher_engagement_status_3 - Power',\n", " 'publisher_engagement_status_4 - Outlier',\n", " 'bidirectional_friend_status_0-5',\n", " 'bidirectional_friend_status_101-200',\n", " 'bidirectional_friend_status_201+',\n", " 'bidirectional_friend_status_31-100',\n", " 'bidirectional_friend_status_6-30',\n", " 'friend_story_engagement_status_0 - Idle',\n", " 'friend_story_engagement_status_1 - Casual',\n", " 'friend_story_engagement_status_2 - Regular',\n", " 'friend_story_engagement_status_3 - Power',\n", " 'friend_story_engagement_status_4 - Outlier',\n", " 'friend_story_engagement_status_Unknown',\n", " 'public_user_story_engagement_status_0 - Idle',\n", " 'public_user_story_engagement_status_1 - Casual',\n", " 'public_user_story_engagement_status_2 - Regular',\n", " 'public_user_story_engagement_status_3 - Power',\n", " 'public_user_story_engagement_status_4 - Outlier',\n", " 'public_user_story_engagement_status_Unknown',\n", " 'lens_engagement_status_0 - Idle',\n", " 'lens_engagement_status_1 - Casual',\n", " 'lens_engagement_status_2 - Regular',\n", " 'lens_engagement_status_3 - Power',\n", " 'lens_engagement_status_4 - Outlier',\n", " 'lens_engagement_status_Unknown', 'user_persona_v2_Chatter',\n", " 'user_persona_v2_Do Nothing', 'user_persona_v2_NA',\n", " 'user_persona_v2_Poster', 'user_persona_v2_Snap as a Camera',\n", " 'user_persona_v2_Snap/Chat Viewer', 'user_persona_v2_Snapper',\n", " 'user_persona_v2_Story/Discover Watcher', 'country_bucket_01: US',\n", " 'country_bucket_02: Balanced', 'country_bucket_03: Emerging',\n", " 'country_bucket_04: Community', 'country_bucket_None',\n", " 'network_quality_1-bad', 'network_quality_2-okay',\n", " 'network_quality_3-good', 'network_quality_4-excellent',\n", " 'network_quality_NA', 'weekly_active_friend_status_Bucket 1: 0',\n", " 'weekly_active_friend_status_Bucket 2: 1-9',\n", " 'weekly_active_friend_status_Bucket 3: 10-30',\n", " 'weekly_active_friend_status_Bucket 4: 31-100',\n", " 'weekly_active_friend_status_Bucket 5: 101+',\n", " 'weekly_active_friend_status_NA',\n", " 'weekly_post_active_friend_status_Bucket 1: 0',\n", " 'weekly_post_active_friend_status_Bucket 2: 1-4',\n", " 'weekly_post_active_friend_status_Bucket 3: 5-15',\n", " 'weekly_post_active_friend_status_Bucket 4: 16-50',\n", " 'weekly_post_active_friend_status_Bucket 5: 51+',\n", " 'weekly_post_active_friend_status_NA',\n", " 'weekly_communication_active_friend_status_Bucket 1: 0',\n", " 'weekly_communication_active_friend_status_Bucket 2: 1-9',\n", " 'weekly_communication_active_friend_status_Bucket 3: 10-30',\n", " 'weekly_communication_active_friend_status_Bucket 4: 31-100',\n", " 'weekly_communication_active_friend_status_Bucket 5: 101+',\n", " 'weekly_communication_active_friend_status_NA',\n", " 'weekly_snap_create_active_friend_status_Bucket 1: 0',\n", " 'weekly_snap_create_active_friend_status_Bucket 2: 1-9',\n", " 'weekly_snap_create_active_friend_status_Bucket 3: 10-30',\n", " 'weekly_snap_create_active_friend_status_Bucket 4: 31-100',\n", " 'weekly_snap_create_active_friend_status_Bucket 5: 101+',\n", " 'weekly_snap_create_active_friend_status_NA',\n", " 'communication_power_friend_status_Bucket 1: 0',\n", " 'communication_power_friend_status_Bucket 2: 1-9',\n", " 'communication_power_friend_status_Bucket 3: 10-30',\n", " 'communication_power_friend_status_Bucket 4: 31-100',\n", " 'communication_power_friend_status_Bucket 5: 101+',\n", " 'communication_power_friend_status_NA',\n", " 'snap_create_power_friend_status_Bucket 1: 0',\n", " 'snap_create_power_friend_status_Bucket 2: 1-9',\n", " 'snap_create_power_friend_status_Bucket 3: 10-30',\n", " 'snap_create_power_friend_status_Bucket 4: 31-100',\n", " 'snap_create_power_friend_status_Bucket 5: 101+',\n", " 'snap_create_power_friend_status_NA',\n", " 'spotlight_story_engagement_status_0: Non User',\n", " 'spotlight_story_engagement_status_1: Accidental User',\n", " 'spotlight_story_engagement_status_2: Casual User',\n", " 'spotlight_story_engagement_status_3: Regular User',\n", " 'spotlight_story_engagement_status_4: Power User',\n", " 'spotlight_story_engagement_status_5: Outlier User',\n", " 'spotlight_story_post_status_0: Non-Creators (No L30 Submissions)',\n", " 'spotlight_story_post_status_1: Casual (0 Quality Submissions)',\n", " 'spotlight_story_post_status_2: Regular (1 Quality Submission)',\n", " 'spotlight_story_post_status_3: Frequent (2-5 Quality Submissions)',\n", " 'spotlight_story_post_status_4: Power (6+ Quality Submissions)',\n", " 'days_since_last_active_0-2', 'days_since_last_active_15-30',\n", " 'days_since_last_active_181-365', 'days_since_last_active_3-7',\n", " 'days_since_last_active_31-60', 'days_since_last_active_366+',\n", " 'days_since_last_active_61-90', 'days_since_last_active_8-14',\n", " 'days_since_last_active_91-180', 'map_L30_status_Casual',\n", " 'map_L30_status_NA', 'map_L30_status_New User',\n", " 'map_L30_status_Power', 'map_L30_status_Regular',\n", " 'map_persona_v1_Actionmoji User', 'map_persona_v1_Browser',\n", " 'map_persona_v1_Do Nothing', 'map_persona_v1_Friend Viewer',\n", " 'map_persona_v1_Layers User', 'map_persona_v1_NA',\n", " 'map_persona_v1_Places User', 'map_persona_v1_Story Viewer',\n", " 'bitmoji_fashion_persona_1: Outfit Changer',\n", " 'bitmoji_fashion_persona_2: Outfit Trier',\n", " 'bitmoji_fashion_persona_3: Outfit Browser',\n", " 'bitmoji_fashion_persona_4: <PERSON><PERSON>',\n", " 'bitmoji_fashion_persona_5: Do Nothing',\n", " 'bitmoji_fashion_persona_<PERSON>A',\n", " 'bitmoji_fashion_builder_L28_engagement_status_1: Idle',\n", " 'bitmoji_fashion_builder_L28_engagement_status_2: Regular',\n", " 'bitmoji_fashion_builder_L28_engagement_status_3: Power',\n", " 'bitmoji_fashion_builder_L28_engagement_status_4: New Bitmoji User',\n", " 'bitmoji_fashion_builder_L28_engagement_status_NA',\n", " 'device_cluster_0', # add decive_cluster\n", " 'device_cluster_1', \n", " 'device_cluster_10', \n", " 'device_cluster_11', \n", " 'device_cluster_12', \n", " 'device_cluster_13',\n", " 'device_cluster_2', \n", " 'device_cluster_3', \n", " 'device_cluster_4',\n", " 'device_cluster_5', \n", " 'device_cluster_6', \n", " 'device_cluster_7', \n", " 'device_cluster_8', \n", " 'device_cluster_9', \n", " 'device_cluster_NA']"]}, {"cell_type": "code", "execution_count": null, "id": "c9035966", "metadata": {}, "outputs": [], "source": ["one_hot_X = df_new[propensity_predictors]"]}, {"cell_type": "code", "execution_count": null, "id": "27ac53c6", "metadata": {}, "outputs": [], "source": ["X_train, X_test, y_train, y_test = model_selection.train_test_split(one_hot_X, y, random_state=42, test_size=0.25)"]}, {"cell_type": "code", "execution_count": null, "id": "4b4844db", "metadata": {}, "outputs": [], "source": ["xgb_classifier = xgb.XGBClassifier()"]}, {"cell_type": "code", "execution_count": null, "id": "963b6b5b", "metadata": {}, "outputs": [], "source": ["xgb_classifier.fit(X_train, y_train)"]}, {"cell_type": "code", "execution_count": null, "id": "3c558714", "metadata": {}, "outputs": [], "source": ["predictions = xgb_classifier.predict(X_test)"]}, {"cell_type": "code", "execution_count": null, "id": "f2710989", "metadata": {}, "outputs": [], "source": ["print(\"Accuracy of Model:\", accuracy_score(y_test,predictions))"]}, {"cell_type": "code", "execution_count": null, "id": "a7548eac", "metadata": {}, "outputs": [], "source": ["y_pred = xgb_classifier.predict_proba(X_test)[:, 1]\n", "fpr, tpr, _ = roc_curve(y_test, y_pred)\n", "auc = round(roc_auc_score(y_test, y_pred), 4)\n", "print(auc)"]}, {"cell_type": "code", "execution_count": null, "id": "030b3da1", "metadata": {}, "outputs": [], "source": ["# plt.figure(figsize=(18, 10))\n", "# plt.plot(fpr, tpr, label=\"XGBoost, AUC=\"+str(auc), lw=4)\n", "# #plt.plot(fpr[3],tpr[0], color='darkorange', lw=lw, label='ROC curve (area=%0.2f)' %auc)\n", "# plt.plot([0,1],[0,1], color='grey', linestyle='--', lw=4)\n", "# plt.yticks(fontsize=25)\n", "# plt.xticks(fontsize=25)\n", "# plt.xlabel('False positive rate', size=25)\n", "# plt.ylabel('True positive rate', size=25)\n", "# plt.legend(prop={'size': 25})\n", "# plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "5a7762b9", "metadata": {}, "outputs": [], "source": ["def mv_blob(bucket_name, blob_name, new_bucket_name, new_blob_name):\n", "    \"\"\"\n", "    Function for moving files between directories or buckets. it will use GCP's copy \n", "    function then delete the blob from the old location.\n", "    \n", "    inputs\n", "    -----\n", "    bucket_name: name of bucket\n", "    blob_name: str, name of file \n", "        ex. 'data/some_location/file_name'\n", "    new_bucket_name: name of bucket (can be same as original if we're just moving around directories)\n", "    new_blob_name: str, name of file in new directory in target bucket \n", "        ex. 'data/destination/file_name'\n", "    \"\"\"\n", "    storage_client = storage.Client(project = \"sc-bq-gcs-billingonly\")\n", "    source_bucket = storage_client.bucket(bucket_name)\n", "    \n", "    if storage.Blob(bucket=source_bucket, name=blob_name).exists(storage_client):\n", "        source_blob = source_bucket.blob(blob_name)\n", "        destination_bucket = storage_client.bucket(new_bucket_name)\n", "\n", "        # copy to new destination\n", "        new_blob = source_bucket.copy_blob(\n", "            source_blob, destination_bucket, new_blob_name)\n", "        # delete in old destination\n", "        source_blob.delete()"]}, {"cell_type": "code", "execution_count": null, "id": "c5274968", "metadata": {}, "outputs": [], "source": ["def upload_to_bucket(blob_name, path_to_file, bucket_name):\n", "    \"\"\" Upload data to a bucket\"\"\"\n", "    storage_client = storage.Client(project = \"sc-bq-gcs-billingonly\")\n", "\n", "    #print(buckets = list(storage_client.list_buckets())\n", "\n", "    bucket = storage_client.bucket(bucket_name)\n", "    blob = bucket.blob(blob_name)\n", "    blob.upload_from_filename(path_to_file)"]}, {"cell_type": "code", "execution_count": null, "id": "a9d06d96", "metadata": {}, "outputs": [], "source": ["if auc>0.85:\n", "    xgb_classifier.save_model(\"./xgboost_propensity_wau.json\")\n", "    move_to_bucket_name = \"snapchat_plus/archived_models/xgboost_propensity_wau_{ds}.json\".format(ds=DATE)\n", "    mv_blob(\"prod-models\", \"snapchat_plus/xgboost_propensity_wau.json\",  \"prod-models\", move_to_bucket_name)\n", "    upload_to_bucket(\"snapchat_plus/xgboost_propensity_wau.json\", \"xgboost_propensity_wau.json\", \"prod-models\")\n", "else:\n", "    print('model is not updated, low AUC')"]}, {"metadata": {}, "cell_type": "markdown", "source": "# Plan Type Propensity Modeling", "id": "6739d58d7e992227"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["y_pred = xgb_classifier.predict_proba(df_new[propensity_predictors])\n", "df_high_propensity=df_new.copy()\n", "df_high_propensity['propensity_any_signup'] = y_pred[:, 1]\n", "df_high_propensity = df_high_propensity[df_high_propensity['propensity_any_signup']>0.5]"], "id": "99ceafae6ed787b8"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["plan_propensity_predictors = [\n", "\n", " #new predictors for plan-type model\n", " 'platinum_plan_tap_l30_uu',\n", " 'annual_plan_tap_l30_uu',\n", " 'family_plan_tap_l30_uu',\n", " 'monthly_plan_tap_l30_uu',\n", " 'used_to_annual',\n", " 'vol_churn_uu',\n", " 'invol_churn_uu',\n", " 'plus_tenure',\n", "\n", " 'exclusive_bitmoji_background_upsell',\n", " 'chat_ct_drawer_open_count',\n", " 'MAP_MAP_OPEN_COUNT_last_7days',\n", " 'my_profile_upsell',\n", " 'friend_badge_upsell',\n", " 'app_open_count',\n", " 'active_splus_friend_counts',\n", " 'friend_story_view_count',\n", " 'is_engaged_in_last_30_days',\n", " 'story_post_L7',\n", " 'CHAT_BITMOJI_STICKER_COUNT_last_7days',\n", " 'friend_counts',\n", " 'MAP_STORY_SNAP_VIEW_COUNT_last_7days',\n", " 'is_story_public_user',\n", " 'publisher_subscription_count',\n", " 'chat_wallpapers_upsell',\n", " 'LENS_DIRECT_SNAP_SEND_COUNT_last_7days',\n", " 'lens_swipe_count',\n", " 'subscription_landing_page_visits',\n", " 'ghost_trail_upsell',\n", " 'communication_L7',\n", " 'used_to_signedup_index',\n", " 'is_snap_public_user',\n", " 'notification_sounds_upsell',\n", " 'pin_bff_upsell',\n", " 'splus_exposure',\n", " 'non_friend_user_story_view_count',\n", " 'communication_count',\n", " 'snap_create_L7',\n", " 'has_bitmoji',\n", " 'has_public_profile',\n", " 'DIRECT_SNAP_CREATE_COUNT_last_7days',\n", " 'camera_takeover_upsell',\n", " 'df_L7',\n", " 'is_popular_user',\n", " 'publisher_story_view_count',\n", " 'FRIEND_STORY_SNAP_POST_COUNT_last_7days',\n", " 'CHAT_GROUP_SEND_COUNT_last_7days',\n", " 'settings_upsell',\n", " 'publisher_L7',\n", " 'DIRECT_SNAP_VIEW_COUNT_last_7days',\n", " 'active_splus_close_friend_counts', # add in close+ active splus friends\n", " 'app_L7',  # newly added for app_l7 > 0 users\n", " 'inferred_age_bucket_13-17', 'inferred_age_bucket_18-24',\n", " 'inferred_age_bucket_25-34', 'inferred_age_bucket_35-plus',\n", " 'inferred_age_bucket_unknown', 'inferred_age_bucket_None',\n", " 'gender_female', 'gender_male', 'gender_None',\n", " 'app_engagement_status_0 - Idle',\n", " 'app_engagement_status_1 - Casual',\n", " 'app_engagement_status_2 - Regular',\n", " 'app_engagement_status_3 - Power',\n", " 'app_engagement_status_4 - Outlier',\n", " 'communication_engagement_status_0 - Idle',\n", " 'communication_engagement_status_1 - Casual',\n", " 'communication_engagement_status_2 - Regular',\n", " 'communication_engagement_status_3 - Power',\n", " 'communication_engagement_status_4 - Outlier',\n", " 'calling_engagement_status_0 - Idle',\n", " 'calling_engagement_status_1 - Casual',\n", " 'calling_engagement_status_2 - Regular',\n", " 'calling_engagement_status_3 - Power',\n", " 'calling_engagement_status_4 - Outlier',\n", " 'snap_create_engagement_status_0 - Idle',\n", " 'snap_create_engagement_status_1 - Casual',\n", " 'snap_create_engagement_status_2 - Regular',\n", " 'snap_create_engagement_status_3 - Power',\n", " 'snap_create_engagement_status_4 - Outlier',\n", " 'publisher_engagement_status_0 - Idle',\n", " 'publisher_engagement_status_1 - Casual',\n", " 'publisher_engagement_status_2 - Regular',\n", " 'publisher_engagement_status_3 - Power',\n", " 'publisher_engagement_status_4 - Outlier',\n", " 'bidirectional_friend_status_0-5',\n", " 'bidirectional_friend_status_101-200',\n", " 'bidirectional_friend_status_201+',\n", " 'bidirectional_friend_status_31-100',\n", " 'bidirectional_friend_status_6-30',\n", " 'friend_story_engagement_status_0 - Idle',\n", " 'friend_story_engagement_status_1 - Casual',\n", " 'friend_story_engagement_status_2 - Regular',\n", " 'friend_story_engagement_status_3 - Power',\n", " 'friend_story_engagement_status_4 - Outlier',\n", " 'friend_story_engagement_status_Unknown',\n", " 'public_user_story_engagement_status_0 - Idle',\n", " 'public_user_story_engagement_status_1 - Casual',\n", " 'public_user_story_engagement_status_2 - Regular',\n", " 'public_user_story_engagement_status_3 - Power',\n", " 'public_user_story_engagement_status_4 - Outlier',\n", " 'public_user_story_engagement_status_Unknown',\n", " 'lens_engagement_status_0 - Idle',\n", " 'lens_engagement_status_1 - Casual',\n", " 'lens_engagement_status_2 - Regular',\n", " 'lens_engagement_status_3 - Power',\n", " 'lens_engagement_status_4 - Outlier',\n", " 'lens_engagement_status_Unknown',\n", "    'user_persona_v2_Chatter',\n", " 'user_persona_v2_Do Nothing',\n", "    'user_persona_v2_NA',\n", " 'user_persona_v2_Poster',\n", "    'user_persona_v2_Snap as a Camera',\n", " 'user_persona_v2_Snap/Chat Viewer',\n", "    'user_persona_v2_Snapper',\n", " 'user_persona_v2_Story/Discover Watcher',\n", "    'country_bucket_01: US',\n", " 'country_bucket_02: Balanced',\n", "    'country_bucket_03: Emerging',\n", " 'country_bucket_04: Community',\n", "    'country_bucket_None',\n", " 'network_quality_1-bad', 'network_quality_2-okay',\n", " 'network_quality_3-good', 'network_quality_4-excellent',\n", " 'network_quality_NA', 'weekly_active_friend_status_Bucket 1: 0',\n", " 'weekly_active_friend_status_Bucket 2: 1-9',\n", " 'weekly_active_friend_status_Bucket 3: 10-30',\n", " 'weekly_active_friend_status_Bucket 4: 31-100',\n", " 'weekly_active_friend_status_Bucket 5: 101+',\n", " 'weekly_active_friend_status_NA',\n", " 'weekly_post_active_friend_status_Bucket 1: 0',\n", " 'weekly_post_active_friend_status_Bucket 2: 1-4',\n", " 'weekly_post_active_friend_status_Bucket 3: 5-15',\n", " 'weekly_post_active_friend_status_Bucket 4: 16-50',\n", " 'weekly_post_active_friend_status_Bucket 5: 51+',\n", " 'weekly_post_active_friend_status_NA',\n", " 'weekly_communication_active_friend_status_Bucket 1: 0',\n", " 'weekly_communication_active_friend_status_Bucket 2: 1-9',\n", " 'weekly_communication_active_friend_status_Bucket 3: 10-30',\n", " 'weekly_communication_active_friend_status_Bucket 4: 31-100',\n", " 'weekly_communication_active_friend_status_Bucket 5: 101+',\n", " 'weekly_communication_active_friend_status_NA',\n", " 'weekly_snap_create_active_friend_status_Bucket 1: 0',\n", " 'weekly_snap_create_active_friend_status_Bucket 2: 1-9',\n", " 'weekly_snap_create_active_friend_status_Bucket 3: 10-30',\n", " 'weekly_snap_create_active_friend_status_Bucket 4: 31-100',\n", " 'weekly_snap_create_active_friend_status_Bucket 5: 101+',\n", " 'weekly_snap_create_active_friend_status_NA',\n", " 'communication_power_friend_status_Bucket 1: 0',\n", " 'communication_power_friend_status_Bucket 2: 1-9',\n", " 'communication_power_friend_status_Bucket 3: 10-30',\n", " 'communication_power_friend_status_Bucket 4: 31-100',\n", " 'communication_power_friend_status_Bucket 5: 101+',\n", " 'communication_power_friend_status_NA',\n", " 'snap_create_power_friend_status_Bucket 1: 0',\n", " 'snap_create_power_friend_status_Bucket 2: 1-9',\n", " 'snap_create_power_friend_status_Bucket 3: 10-30',\n", " 'snap_create_power_friend_status_Bucket 4: 31-100',\n", " 'snap_create_power_friend_status_Bucket 5: 101+',\n", " 'snap_create_power_friend_status_NA',\n", " 'spotlight_story_engagement_status_0: Non User',\n", " 'spotlight_story_engagement_status_1: Accidental User',\n", " 'spotlight_story_engagement_status_2: Casual User',\n", " 'spotlight_story_engagement_status_3: Regular User',\n", " 'spotlight_story_engagement_status_4: Power User',\n", " 'spotlight_story_engagement_status_5: Outlier User',\n", " 'spotlight_story_post_status_0: Non-Creators (No L30 Submissions)',\n", " 'spotlight_story_post_status_1: Casual (0 Quality Submissions)',\n", " 'spotlight_story_post_status_2: Regular (1 Quality Submission)',\n", " 'spotlight_story_post_status_3: Frequent (2-5 Quality Submissions)',\n", " 'spotlight_story_post_status_4: Power (6+ Quality Submissions)',\n", " 'days_since_last_active_0-2', 'days_since_last_active_15-30',\n", " 'days_since_last_active_181-365', 'days_since_last_active_3-7',\n", " 'days_since_last_active_31-60', 'days_since_last_active_366+',\n", " 'days_since_last_active_61-90', 'days_since_last_active_8-14',\n", " 'days_since_last_active_91-180', 'map_L30_status_Casual',\n", " 'map_L30_status_NA', 'map_L30_status_New User',\n", " 'map_L30_status_Power', 'map_L30_status_Regular',\n", " 'map_persona_v1_Actionmoji User', 'map_persona_v1_Browser',\n", " 'map_persona_v1_Do Nothing', 'map_persona_v1_Friend Viewer',\n", " 'map_persona_v1_Layers User', 'map_persona_v1_NA',\n", " 'map_persona_v1_Places User', 'map_persona_v1_Story Viewer',\n", " 'bitmoji_fashion_persona_1: Outfit Changer',\n", " 'bitmoji_fashion_persona_2: Outfit Trier',\n", " 'bitmoji_fashion_persona_3: Outfit Browser',\n", " 'bitmoji_fashion_persona_4: <PERSON><PERSON>',\n", " 'bitmoji_fashion_persona_5: Do Nothing',\n", " 'bitmoji_fashion_persona_<PERSON>A',\n", " 'bitmoji_fashion_builder_L28_engagement_status_1: Idle',\n", " 'bitmoji_fashion_builder_L28_engagement_status_2: Regular',\n", " 'bitmoji_fashion_builder_L28_engagement_status_3: Power',\n", " 'bitmoji_fashion_builder_L28_engagement_status_4: New Bitmoji User',\n", " 'bitmoji_fashion_builder_L28_engagement_status_NA',\n", " 'device_cluster_0', # add decive_cluster\n", " 'device_cluster_1',\n", " 'device_cluster_10',\n", " 'device_cluster_11',\n", " 'device_cluster_12',\n", " 'device_cluster_13',\n", " 'device_cluster_2',\n", " 'device_cluster_3',\n", " 'device_cluster_4',\n", " 'device_cluster_5',\n", " 'device_cluster_6',\n", " 'device_cluster_7',\n", " 'device_cluster_8',\n", " 'device_cluster_9',\n", " 'device_cluster_NA'\n", "]"], "id": "9c854908b9834f12"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "one_hot_X = df_high_propensity[plan_propensity_predictors]", "id": "85dd9d4810175681"}, {"metadata": {}, "cell_type": "markdown", "source": "## Annual Plan", "id": "ff68676be550dbb0"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["df_high_propensity['new_sign_up_annual'] = ((df_high_propensity['new_sign_up'] == 1) & (df_high_propensity['new_subscription_type'] == '12 Month')).astype(int)\n", "y_ann = df_high_propensity['new_sign_up_annual']"], "id": "936332bcdd5cfbc5"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["X_train_ann, X_test_ann, y_train_ann, y_test_ann = model_selection.train_test_split(one_hot_X, y_ann, random_state=42, test_size=0.25)\n", "xgb_classifier_ann = xgb.XGBClassifier()\n", "xgb_classifier_ann.fit(X_train_ann, y_train_ann)"], "id": "e79ad4c6011d03b9"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["predictions_ann = xgb_classifier_ann.predict(X_test_ann)\n", "y_pred_ann = xgb_classifier_ann.predict_proba(X_test_ann)[:, 1]\n", "fpr, tpr, _ = roc_curve(y_test_ann, y_pred_ann)\n", "auc = round(roc_auc_score(y_test_ann, y_pred_ann), 4)\n", "print(\"Dep Variable: Annual\")\n", "print(\"Accuracy of Model: \", accuracy_score(y_test_ann,predictions_ann))\n", "print(\"AUC: \", auc)"], "id": "d504bd4b6a2d3f7e"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if auc>0.7:\n", "    xgb_classifier_ann.save_model(\"./xgboost_propensity_wau_annual.json\")\n", "    move_to_bucket_name = \"snapchat_plus/archived_models/xgboost_propensity_wau_annual_{ds}.json\".format(ds=DATE)\n", "    mv_blob(\"prod-models\", \"snapchat_plus/xgboost_propensity_wau_annual.json\",  \"prod-models\", move_to_bucket_name)\n", "    upload_to_bucket(\"snapchat_plus/xgboost_propensity_wau_annual.json\", \"xgboost_propensity_wau_annual.json\", \"prod-models\")\n", "else:\n", "    print('model is not updated, low AUC')"], "id": "929ec35cb7e26e21"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "y_pred_ann = xgb_classifier_ann.predict_proba(df_high_propensity[plan_propensity_predictors])", "id": "a14abb68019c3a07"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["#store scores for defining thresholds\n", "df_hp_plan_score = df_high_propensity[['ghost_user_id','new_sign_up','new_subscription_type','new_subscription_source','propensity_any_signup']].copy()\n", "df_hp_plan_score['propensity_annual'] = y_pred_ann[:, 1]"], "id": "5b59f130190eca04"}, {"metadata": {}, "cell_type": "markdown", "source": "## Platinum", "id": "a627f9fb96cf84f"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["df_high_propensity['new_sign_up_platinum'] = ((df_high_propensity['new_sign_up'] == 1) & (df_high_propensity['new_subscription_source'].str.contains('Ad Free'))).astype(int)\n", "y_plat = df_high_propensity['new_sign_up_platinum']"], "id": "d7db270752f52ff0"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["X_train_plat, X_test_plat, y_train_plat, y_test_plat = model_selection.train_test_split(one_hot_X, y_plat, random_state=42, test_size=0.25)\n", "xgb_classifier_plat = xgb.XGBClassifier()\n", "xgb_classifier_plat.fit(X_train_plat, y_train_plat)"], "id": "3aa840a54ed6a01b"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["predictions_plat = xgb_classifier_plat.predict(X_test_plat)\n", "y_pred_plat = xgb_classifier_plat.predict_proba(X_test_plat)[:, 1]\n", "fpr, tpr, _ = roc_curve(y_test_plat, y_pred_plat)\n", "auc = round(roc_auc_score(y_test_plat, y_pred_plat), 4)\n", "print(\"Dep Variable: Platinum\")\n", "print(\"Accuracy of Model: \", accuracy_score(y_test_plat,predictions_plat))\n", "print(\"AUC: \", auc)"], "id": "da9ffe55b821d3d0"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if auc>0.7:\n", "    xgb_classifier_plat.save_model(\"./xgboost_propensity_wau_platinum.json\")\n", "    move_to_bucket_name = \"snapchat_plus/archived_models/xgboost_propensity_wau_platinum_{ds}.json\".format(ds=DATE)\n", "    mv_blob(\"prod-models\", \"snapchat_plus/xgboost_propensity_wau_platinum.json\",  \"prod-models\", move_to_bucket_name)\n", "    upload_to_bucket(\"snapchat_plus/xgboost_propensity_wau_platinum.json\", \"xgboost_propensity_wau_platinum.json\", \"prod-models\")\n", "else:\n", "    print('model is not updated, low AUC')"], "id": "93489b48ee4ff377"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "y_pred_plat = xgb_classifier_plat.predict_proba(df_high_propensity[plan_propensity_predictors])", "id": "dc3701ff69a9df77"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "df_hp_plan_score['propensity_platinum'] = y_pred_plat[:, 1]", "id": "ec641d0b2b3c83d7"}, {"metadata": {}, "cell_type": "markdown", "source": "## Write scores to BQ", "id": "4fc5ba999a860661"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["filtered_df = df_hp_plan_score[df_hp_plan_score['new_sign_up'] == 1]\n", "summary_df = filtered_df[['propensity_any_signup', 'propensity_annual', 'propensity_platinum']].agg(['mean', 'median', lambda x: x.quantile(0.75)]).T\n", "summary_df = (\n", "    filtered_df[['propensity_any_signup', 'propensity_annual', 'propensity_platinum']]\n", "    .agg([\n", "        'mean',\n", "        lambda x: x.quantile(0.25),\n", "        'median',\n", "        lambda x: x.quantile(0.75),\n", "        lambda x: x.quantile(0.90)\n", "    ])\n", "    .T\n", ")\n", "summary_df.columns = ['avg', 'p25', 'p50', 'p75', 'p90']"], "id": "83da3c704ac41cbe"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["summary_df=summary_df.reset_index()\n", "summary_df=summary_df.rename(columns={'index': 'propensity_score_type'})"], "id": "6967ac375937074a"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["job_config = bigquery.LoadJobConfig(\n", "         # below is equivalent to if_exists=\"replace\"\n", "         write_disposition=\"WRITE_TRUNCATE\"\n", "     )\n", "job = client.load_table_from_dataframe(\n", "         summary_df,\n", "         f'sc-analytics.report_snapchat_plus.propensity_plan_type_thresholds_{DATE}',\n", "         job_config=job_config\n", "     )\n", "\n", "print('Table written')"], "id": "f57f846daa4e7378"}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}