{"cells": [{"cell_type": "markdown", "id": "daa025a2", "metadata": {}, "source": ["# Simple Data Processing Husky Notebook Template"]}, {"cell_type": "markdown", "id": "6fe038ad", "metadata": {}, "source": ["This notebook serves as a template for building a Husky notebook.  \n", "It demonstrates how to perform a simple data query using BigQuery and how to create plots from the resulting dataset.  \n", "Use this as a starting point for your own data exploration and visualization tasks.\n", "\n", "The example below retrieves Husky usage data, filters it by a specified time period and notebook name, and visualizes the number of runs for that time period/notebook using a line chart.\n", "\n", "The ui template code for this notebook: [Github Folder](https://github.sc-corp.net/Snapchat/vellum-image/tree/master/src/exporter/vellum/notebook/data_processing_template)\n", "\n", "To run and test the notebook on the Husky app: [Husky Notebook](https://github.sc-corp.net/Snapchat/vellum-image/tree/master/src/exporter/vellum/notebook/data_processing_template)\n", "\n", "For the Husky documentation / Guide document:  [Guide](https://docs.google.com/document/d/1zdASeSjEEBHCidwFNUVJZf-aj_Yvuc0CQ9Swls5htxI/edit?tab=t.0#heading=h.fsoytx8na4em)\n", "\n", "For questions / Support:  [Slack Channel](https://snap.enterprise.slack.com/archives/CG8R0834M) \n"]}, {"cell_type": "markdown", "id": "80f2b1c9", "metadata": {}, "source": ["## Parameters\n", "First, define your parameters. These parameters will be defined in the husky ui and will be used as input for our data analysis. Ensure that the cell is tagged with `parameters`."]}, {"cell_type": "code", "execution_count": 56, "id": "3e47907c", "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["## Parameters\n", "##Define your parameters here, these should correspond to the parameter names created in your husky notebook ui\n", "START_DATE = \"20250401\"\n", "END_DATE = \"20250501\"\n", "NOTEBOOK_ID = \"ab_generic/AB_CONSOLE_METRICS_DEEP_DIVE.ipynb\""]}, {"cell_type": "markdown", "id": "819f5cf2", "metadata": {}, "source": ["## Imports\n", "Import all of the necessary packages for data processing."]}, {"cell_type": "code", "execution_count": 57, "id": "83af1ca2", "metadata": {}, "outputs": [], "source": ["# --- Imports ---\n", "import pandas as pd\n", "from banjo.utils import gbq\n", "import matplotlib.pyplot as plt\n", "from IPython.display import display, HTML\n", "from io import BytesIO\n", "import base64"]}, {"cell_type": "markdown", "id": "33221341", "metadata": {}, "source": ["## Querying BigQuery to Create the Dataset\n", "\n", "In this step, we use the specified date range and notebook parameters to construct and execute a SQL query against BigQuery.  \n", "The resulting dataset will be loaded into a pandas DataFrame for further analysis and visualization.  \n", "This approach allows us to efficiently retrieve only the relevant data needed for our analysis."]}, {"cell_type": "code", "execution_count": null, "id": "7f32d105", "metadata": {}, "outputs": [], "source": ["# Construct the SQL query using the provided parameters.\n", "# - Filters data by creation_time within the specified date range (START_DATE, END_DATE)\n", "# - Filters by the selected notebook name (NOTEBOOK_ID)\n", "query = f\"\"\" \n", "    select * \n", "        from `sc-bq-sales-insights.schamp.husky_cost_analysis`\n", "        where date(creation_time)>= PARSE_DATE('%Y%m%d', '{START_DATE}')\n", "        and date(creation_time)<= PARSE_DATE('%Y%m%d', '{END_DATE}')\n", "        and notebook_name = '{NOTEBOOK_ID}'\n", "\"\"\"\n", "\n", "# Execute the query and load the results into a pandas DataFrame.\n", "# - Uses Google BigQuery as the data source.\n", "# - Replace 'project_id' with your own GCP project if needed.\n", "df_with_results = gbq.read_gbq(\n", "        query,\n", "        project_id=\"sc-bq-sales-insights\",\n", "        dialect='standard',\n", "        use_bqstorage_api=False\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "234efb25", "metadata": {}, "outputs": [], "source": ["\n", "# Limit column width for display\n", "pd.set_option('display.max_colwidth', 30)  # or another suitable value\n", "\n", "# OR: Truncate long strings in all columns\n", "def truncate(val, length=30):\n", "    if isinstance(val, str) and len(val) > length:\n", "        return val[:length] + \"...\"\n", "    return val\n", "\n", "df_display = df_with_results.head(10).applymap(truncate)\n", "\n", "display(df_display.style.set_caption(\"Summary Table\"))"]}, {"cell_type": "markdown", "id": "ee26ad83", "metadata": {}, "source": ["## Visualizing Runs Per Day\n", "\n", "In this step, we visualize the number of runs per day within the selected date range.  \n", "A run is defined as a distinct `report_key` on a given day.  \n", "This chart helps identify trends in notebook activity over time."]}, {"cell_type": "code", "execution_count": null, "id": "8e83d59b", "metadata": {}, "outputs": [], "source": ["\n", "\n", "# --- Display analysis period and notebook info as HTML ---\n", "display(HTML(f\"\"\"\n", "<h3>Runs Per Day</h3>\n", "<p>This chart shows the number of runs per day for the selected period and notebook.</p>\n", "<ul>\n", "  <li><b>Start Date:</b> {START_DATE}</li>\n", "  <li><b>End Date:</b> {END_DATE}</li>\n", "  <li><b>Notebook:</b> {NOTEBOOK_ID}</li>\n", "</ul>\n", "\"\"\"))\n", "\n", "# --- Group by date and count distinct report_key values per day ---\n", "runs_per_day = (\n", "    df_with_results\n", "    .groupby(df_with_results['creation_time'].dt.date)['report_key']\n", "    .nunique()\n", "    .reset_index(name='num_runs')\n", ")\n", "\n", "# --- Plotting ---\n", "fig, ax = plt.subplots(figsize=(10, 6))\n", "ax.plot(runs_per_day['creation_time'], runs_per_day['num_runs'], marker='o')\n", "ax.set_title(f'Number of Runs per Day for Notebook: {NOTEBOOK_ID}')  # Notebook name included here\n", "ax.set_xlabel('Date')\n", "ax.set_ylabel('Number of Runs (distinct report_key)')\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "\n", "# --- Convert plot to HTML image and display ---\n", "buf = BytesIO()\n", "plt.savefig(buf, format='png')\n", "plt.close(fig)\n", "buf.seek(0)\n", "img_base64 = base64.b64encode(buf.read()).decode('utf-8')\n", "display(HTML(f'<img src=\"data:image/png;base64,{img_base64}\" style=\"max-width:100%;\">'))"]}], "metadata": {"kernelspec": {"display_name": "husky_38", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.12"}}, "nbformat": 4, "nbformat_minor": 5}